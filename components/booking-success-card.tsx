"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowRight, CalendarCheck, Clock, MapPin, X } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { type CartItem } from "@/store/booking-cart"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"

interface BookingSuccessCardProps {
  bookings: CartItem[]
  eventName: string
  onClose: () => void
  onViewCalendar: () => void
}

export function BookingSuccessCard({ 
  bookings, 
  eventName, 
  onClose, 
  onViewCalendar 
}: BookingSuccessCardProps) {
  const [visible, setVisible] = useState(false)
  
  useEffect(() => {
    // Animate in when component mounts
    const timer = setTimeout(() => {
      setVisible(true)
    }, 100)
    
    return () => clearTimeout(timer)
  }, [])
  
  const handleClose = () => {
    setVisible(false)
    // Wait for animation to complete before calling onClose
    setTimeout(onClose, 500)
  }
  
  const totalBookings = bookings.length
  const totalHours = bookings.reduce((acc, item) => {
    const duration = new Date(item.booking.end).getTime() - new Date(item.booking.start).getTime()
    return acc + (duration / (1000 * 60 * 60))
  }, 0)
  
  return (
    <div 
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-black/40 transition-opacity duration-300",
        visible ? "opacity-100" : "opacity-0 pointer-events-none"
      )}
      onClick={(e) => e.target === e.currentTarget && handleClose()}
    >
      <Card 
        className={cn(
          "w-full max-w-lg shadow-xl transition-all duration-500 transform border-indigo-200",
          visible ? "scale-100 shadow-indigo-100/20" : "scale-95"
        )}
      >
        <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b flex flex-row items-start justify-between rounded-t-lg">
          <div>
            <div className="flex items-center gap-2">
              <div className="bg-green-100 p-1.5 rounded-full">
                <CalendarCheck className="h-5 w-5 text-green-600" />
              </div>
              <CardTitle className="text-green-800">Bookings Confirmed</CardTitle>
            </div>
            <CardDescription className="mt-1.5">
              Your bookings have been successfully confirmed and added to your calendar.
            </CardDescription>
          </div>
          <Button 
            size="icon" 
            variant="ghost" 
            className="h-8 w-8 rounded-full hover:bg-red-50 hover:text-red-500 transition-colors" 
            onClick={handleClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent className="p-6 space-y-4">
          {eventName && (
            <div className="bg-muted/50 rounded-lg p-3">
              <div className="text-sm font-medium text-muted-foreground">Associated Event</div>
              <div className="font-medium mt-1">{eventName}</div>
            </div>
          )}
          
          <div>
            <div className="text-sm font-medium text-muted-foreground mb-2">Booking Summary</div>
            <ScrollArea className="h-[160px]">
              <div className="space-y-3">
                {bookings.map((booking) => {
                  const start = new Date(booking.booking.start)
                  const end = new Date(booking.booking.end)
                  
                  return (
                    <div 
                      key={booking.id} 
                      className="border rounded-lg p-3 bg-card shadow-sm"
                    >
                      <div className="font-medium">{booking.booking.title}</div>
                      <div className="text-sm text-muted-foreground mt-1">
                        <div className="flex items-center gap-1.5">
                          <MapPin className="h-3.5 w-3.5" />
                          <span>{booking.room.name}</span>
                        </div>
                        <div className="flex items-center gap-1.5 mt-1">
                          <Clock className="h-3.5 w-3.5" />
                          <span>
                            {format(start, "MMM d, yyyy")} · {format(start, "h:mm a")} - {format(end, "h:mm a")}
                          </span>
                        </div>
                      </div>
                      <Badge variant="outline" className="mt-2 bg-green-50 text-green-700 border-green-200">
                        Confirmed
                      </Badge>
                    </div>
                  )
                })}
              </div>
            </ScrollArea>
            
            <div className="flex items-center justify-between text-sm mt-4 px-2">
              <span>Total Bookings: <span className="font-medium">{totalBookings}</span></span>
              <span>Total Hours: <span className="font-medium">{totalHours.toFixed(1)}h</span></span>
            </div>
          </div>
        </CardContent>
        
        <CardFooter className="bg-muted/20 flex justify-end gap-3 p-4 rounded-b-lg border-t">
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              onClick={handleClose}
              className="border-gray-200 hover:bg-gray-50"
            >
              Close
            </Button>
            <Button 
              className="gap-2 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 transition-all" 
              onClick={onViewCalendar}
            >
              View Calendar
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
