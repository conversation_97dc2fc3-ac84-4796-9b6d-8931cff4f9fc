import { create, combine } from 'zustand';
import type { RefObject } from 'react';
import type FullCalendar from '@fullcalendar/react';

interface CalendarRefState {
  calendarRef: RefObject<FullCalendar | null> | null;
  setCalendarRef: (ref: RefObject<FullCalendar | null>) => void;
}

export const useCalendarStore = create<CalendarRefState>((set) => ({
  calendarRef: null,
  setCalendarRef: (ref) => set({ calendarRef: ref }),
}));
