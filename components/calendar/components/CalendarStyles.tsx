"use client";

import React from "react";

/**
 * Custom CSS for the calendar
 * This includes all the specialized styling and animations needed for the calendar
 */
const CALENDAR_CSS = `
@keyframes pulse-subtle {
  0%, 100% {
    opacity: 0.9;
    box-shadow: var(--tw-shadow);
  }
  50% {
    opacity: 1;
    box-shadow: var(--tw-shadow), 0 0 15px var(--glow-color, rgba(156, 163, 175, 0.5));
  }
}

/* Month transition animations */
@keyframes slideInRight {
  from {
    transform: translateX(8px);
    opacity: 0.7;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-8px);
    opacity: 0.7;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0.7;
  }
  to {
    opacity: 1;
  }
}

/* Apply animations to month navigation */
.fc .month-transition-next {
  animation: slideInRight 0.3s ease-out forwards;
}

.fc .month-transition-prev {
  animation: slideInLeft 0.3s ease-out forwards;
}

.fc .month-transition-fade {
  animation: fadeIn 0.25s ease-out forwards;
}

/* Improve the focus style on navigation buttons for better accessibility */
.calendar-nav-button:focus-visible {
  outline: 2px solid var(--focus-ring-color, rgba(59, 130, 246, 0.5));
  outline-offset: 2px;
}

.dark .dark\\:animate-pulse-subtle {
  animation: pulse-subtle 3s ease-in-out infinite;
}

.event-status-indicator {
  transition: all 300ms ease;
}

.dark .event-status-indicator {
  --glow-color: rgba(156, 163, 175, 0.3);
}

.dark .event-status-inquiry {
  --glow-color: rgba(192, 132, 252, 0.4);
}

.dark .event-status-prospect {
  --glow-color: rgba(244, 114, 182, 0.4);
}

.dark .event-status-tentative {
  --glow-color: rgba(251, 191, 36, 0.4);
}

.dark .event-status-definite {
  --glow-color: rgba(74, 222, 128, 0.4);
}

.dark .event-status-lost {
  --glow-color: rgba(248, 113, 113, 0.4);
}

.dark .event-status-canceled {
  --glow-color: rgba(156, 163, 175, 0.3);
}

.dark .event-status-hold {
  --glow-color: rgba(103, 232, 249, 0.4);
}

/* Ghost event styling */
.ghost-event {
  opacity: 0.7;
  border-style: dashed !important;
  background-color: rgba(240, 244, 248, 0.7) !important;
}

/* Cart ghost event styling */
.cart-ghost-event {
  opacity: 0.8 !important;
  border-style: dashed !important;
}

/* Cart badge styling */
.cart-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--color-primary-light, #f0f9ff);
  border: 1px solid var(--color-primary, #3b82f6);
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary, #3b82f6);
  font-size: 10px;
  z-index: 5;
  animation: badge-pulse 2s infinite;
}

@keyframes badge-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Hold badge styling */
.hold-badge {
  position: absolute;
  top: 2px;
  right: 20px;
  min-width: 16px;
  height: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  padding: 0 4px;
  color: #0891b2;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Animation for cart updates */
@keyframes cart-update-flash {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(79, 70, 229, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
  }
}

.cart-update-flash {
  animation: cart-update-flash 1s forwards;
}

/* Resource related styling */
.venue-group-label {
  font-weight: 600 !important;
  color: #4b5563 !important;
  background-color: rgba(243, 244, 246, 0.8) !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.dark .venue-group-label {
  color: #e5e7eb !important;
  background-color: rgba(31, 41, 55, 0.8) !important;
  border-bottom: 1px solid #374151 !important;
}

.venue-group-lane {
  border-top: 1px solid #e5e7eb !important;
}

.dark .venue-group-lane {
  border-top: 1px solid #374151 !important;
}

.resource-lane:hover {
  background-color: rgba(243, 244, 246, 0.5) !important;
}

.dark .resource-lane:hover {
  background-color: rgba(31, 41, 55, 0.5) !important;
}

.resource-area-header {
  font-weight: 600 !important;
}

/* Grid enhancements */
.enhanced-grid-line-hour {
  border-top: 1px solid rgba(209, 213, 219, 0.8) !important;
}

.dark .enhanced-grid-line-hour {
  border-top: 1px solid rgba(75, 85, 99, 0.8) !important;
}

.enhanced-grid-line-half-hour {
  border-top: 1px dashed rgba(209, 213, 219, 0.5) !important;
}

.dark .enhanced-grid-line-half-hour {
  border-top: 1px dashed rgba(75, 85, 99, 0.5) !important;
}

.enhanced-grid-line-quarter-hour {
  border-top: 1px dotted rgba(209, 213, 219, 0.3) !important;
}

.dark .enhanced-grid-line-quarter-hour {
  border-top: 1px dotted rgba(75, 85, 99, 0.3) !important;
}

/* Today highlight */
.fc-day-today,
.fc-timegrid-col.fc-day-today {
  background-color: rgba(59, 130, 246, 0.05) !important;
}

.dark .fc-day-today,
.dark .fc-timegrid-col.fc-day-today {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

/* Improved now indicator */
.fc-timegrid-now-indicator-line {
  border-color: #ef4444 !important;
  border-width: 2px !important;
}

.fc-timegrid-now-indicator-arrow {
  border-color: #ef4444 !important;
  border-width: 5px !important;
}

/* Calendar container with app borders */
.calendar-with-app-borders {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.dark .calendar-with-app-borders {
  border-color: #374151;
}

/* Cart events update animation */
.cart-update-highlight {
  animation: cart-highlight-pulse 2s ease-out;
}

@keyframes cart-highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    transform: scale(1);
  }
  30% {
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
    transform: scale(1.05);
  }
  60% {
    box-shadow: 0 0 0 5px rgba(34, 197, 94, 0.3);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    transform: scale(1);
  }
}
`;

/**
 * Component that injects the calendar-specific CSS into the document
 */
export function CalendarStyles() {
  return <style jsx global>{CALENDAR_CSS}</style>;
}

export default CalendarStyles; 