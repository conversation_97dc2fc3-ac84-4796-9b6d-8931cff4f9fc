"use client";

import React from "react";
import { CalendarIcon, Clock } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";

/**
 * Component props for EventContent
 */
interface EventContentProps {
  event: any;
  timeText?: string;
}

/**
 * Custom event content renderer for FullCalendar
 * 
 * Provides a consistent, styled representation of events with:
 * - Title
 * - Time information
 * - Status badge
 * - Optional descriptive elements
 * 
 * @param props Component props
 * @returns JSX for the event content
 */
export function EventContent({ event, timeText }: EventContentProps) {
  const { title, start, end, allDay, extendedProps } = event;
  const { status, holdRank, isGhost, isCartGhost } = extendedProps;

  // Format time range
  const formatTime = (date: Date) => {
    return date ? format(date, 'h:mm a') : '';
  };
  
  // Format date range
  const formatDate = (date: Date) => {
    return date ? format(date, 'MMM d') : '';
  };
  
  const timeRange = start && end ? `${formatTime(start)} - ${formatTime(end)}` : '';
  const dateRange = start && end && start.toDateString() !== end.toDateString() 
    ? `${formatDate(start)} - ${formatDate(end)}` 
    : formatDate(start);

  // Status colors mapping
  const statusColors: Record<string, { bg: string, text: string }> = {
    "Inquiry": { bg: "bg-purple-100", text: "text-purple-800" },
    "Prospect": { bg: "bg-pink-100", text: "text-pink-800" },
    "Tentative": { bg: "bg-amber-100", text: "text-amber-800" },
    "Definite": { bg: "bg-green-100", text: "text-green-800" },
    "Lost": { bg: "bg-red-100", text: "text-red-800" },
    "Canceled": { bg: "bg-gray-100", text: "text-gray-800" },
    "Hold": { bg: "bg-cyan-100", text: "text-cyan-800" },
  };

  const statusStyle = statusColors[status] || { bg: "bg-blue-100", text: "text-blue-800" };

  // Render status badge with appropriate styling
  const statusBadge = status ? (
    <Badge 
      variant={status === "Hold" ? "outline" : "default"} 
      className={`status-badge ${statusStyle.bg} ${statusStyle.text} truncate`}
    >
      {status}
      {status === "Hold" && holdRank && <> #{holdRank}</>}
    </Badge>
  ) : null;

  return (
    <div className="flex flex-col p-1 h-full overflow-hidden">
      {/* Header with title and status */}
      <div className="flex items-center justify-between mb-1">
        <div className="font-semibold text-sm truncate mr-1">
          {title}
          {status === "Hold" && holdRank && <span className="text-xs ml-1 font-normal">(#{holdRank})</span>}
        </div>
      </div>
      
      {/* Date and time information with icons */}
      <div className="flex flex-col text-xs text-muted-foreground">
        <div className="flex items-center gap-1 mb-0.5">
          <CalendarIcon className="h-3 w-3 text-primary" />
          <span className="truncate text-white">{dateRange}</span>
        </div>
        {!allDay && (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-primary" />
            <span className="truncate text-white">{timeRange}</span>
          </div>
        )}
      </div>
      {statusBadge}
    </div>
  );
}

export default EventContent; 