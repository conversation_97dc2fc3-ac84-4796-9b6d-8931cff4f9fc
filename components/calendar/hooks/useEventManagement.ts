"use client";

import { useState, useEffect, useMemo } from "react";
import { useCalendar } from "@/context/calendar-context";
import type { EventInput } from "@fullcalendar/core";
import { getContrastColor } from "@/utils/calendar-utils";

/**
 * A custom hook for managing and transforming event data for FullCalendar
 * 
 * Features:
 * - Transforms bookings and ghost bookings into FullCalendar-compatible event objects
 * - Applies appropriate styling based on event type, status, and ownership
 * - Handles special cases like ghost events and cart events
 * 
 * @returns Object containing the processed events
 */
export const useEventManagement = () => {
  const { filteredBookings, ghostBookings, rooms, venues } = useCalendar();
  const [allEvents, setAllEvents] = useState<EventInput[]>([]);

  /**
   * Helper function to convert hex color to RGB
   */
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: Number.parseInt(result[1], 16),
          g: Number.parseInt(result[2], 16),
          b: Number.parseInt(result[3], 16),
        }
      : null;
  };

  // Process bookings and ghost bookings whenever they change
  useEffect(() => {
    /**
     * Transforms regular bookings to FullCalendar events
     */
    const processedEvents: EventInput[] = filteredBookings.map((booking) => {
      // Get room and venue for color
      const room = rooms.find((r) => r.id === booking.roomId);
      const venue = venues.find((v) => v.id === booking.venueId);
      const color = room?.color || venue?.color || "#4285F4";

      return {
        id: booking.id,
        title: booking.title,
        start: booking.start,
        end: booking.end,
        resourceId: booking.roomId,
        allDay: booking.isAllDay,
        extendedProps: {
          description: booking.description,
          venueId: booking.venueId,
          roomId: booking.roomId,
          isGhost: false, // Mark as a real event
          status: booking.status || "Tentative", // Default to Tentative if no status is set
          holdRank: booking.holdRank,
          lostReason: booking.lostReason
        },
        backgroundColor: color,
        borderColor: color,
        textColor: getContrastColor(color),
        classNames: ["google-calendar-event"],
      };
    });

    /**
     * Transforms ghost bookings to FullCalendar events
     */
    const ghostEvents: EventInput[] = ghostBookings.map((booking) => {
      // Get room and venue for color
      const room = rooms.find((r) => r.id === booking.roomId);
      const venue = venues.find((v) => v.id === booking.venueId);
      const baseColor = room?.color || venue?.color || "#4285F4";

      // Create a semi-transparent version of the color
      const rgbColor = hexToRgb(baseColor);
      const ghostColor = rgbColor ? `rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, 0.5)` : baseColor;

      // Determine if this is a cart-related ghost event
      const isCartGhost = booking.id.startsWith("ghost-cart-");
      const isModalGhost = booking.id.startsWith("ghost-modal-");

      // Set title based on cart mode and ghost type
      let title = booking.title || "New Booking";
      if (!title.includes("for") && room) {
        title = `${title} [${room.name}]`;
      }

      // Add visual indicator for cart items
      if (isCartGhost) {
        title = title.startsWith("Cart:") ? title : `Cart: ${title}`;
      }

      return {
        id: booking.id,
        title: title,
        start: booking.start,
        end: booking.end,
        resourceId: booking.roomId,
        allDay: booking.isAllDay,
        extendedProps: {
          description: booking.description,
          venueId: booking.venueId,
          roomId: booking.roomId,
          isGhost: true, // Mark as a ghost event
          status: booking.status,
          isCartGhost: isCartGhost,
          isModalGhost: isModalGhost,
          holdRank: booking.holdRank,
          lostReason: booking.lostReason
        },
        backgroundColor: ghostColor,
        borderColor: baseColor,
        textColor: getContrastColor(baseColor),
        classNames: [
          "ghost-event",
          `ghost-event-${booking.status}`,
          isCartGhost ? "cart-ghost-event" : "modal-ghost-event",
        ],
        durationEditable: true,
        startEditable: true,
      };
    });

    // Combine regular and ghost events
    setAllEvents([...processedEvents, ...ghostEvents]);
  }, [filteredBookings, ghostBookings, rooms, venues]);

  return {
    events: allEvents,
    regularEvents: useMemo(() => 
      allEvents.filter(event => !event.extendedProps?.isGhost),
      [allEvents]
    ),
    ghostEvents: useMemo(() => 
      allEvents.filter(event => event.extendedProps?.isGhost),
      [allEvents]
    ),
    cartEvents: useMemo(() => 
      allEvents.filter(event => event.extendedProps?.isCartGhost),
      [allEvents]
    )
  };
}; 