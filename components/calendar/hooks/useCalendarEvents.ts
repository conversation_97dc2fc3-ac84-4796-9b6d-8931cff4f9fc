"use client";

import { useCallback, useRef } from "react";
import type { 
  DateSelectArg, 
  EventClickArg, 
  EventChangeArg
} from "@fullcalendar/core";
import { useCalendar } from "@/context/calendar-context";
import { useBookingCart } from "@/store/booking-cart";
import { useCalendarPopover } from "../stores/popover-store";
import { v4 as uuidv4 } from "uuid";
import { showToast } from "@/components/toast-manager";

// Define interfaces for FullCalendar events with DOM elements
interface FullCalendarEventWithEl {
  el: HTMLElement;
  [key: string]: any;
}

// Helper function to ensure valid date objects
const ensureValidDate = (date: any): Date => {
  // Check if it's already a valid Date
  if (date instanceof Date && !isNaN(date.getTime())) {
    return date;
  }
  
  // Try to convert from string or number
  if (typeof date === 'string' || typeof date === 'number') {
    const newDate = new Date(date);
    if (!isNaN(newDate.getTime())) {
      return newDate;
    }
  }
  
  // Return current time as fallback
  console.warn('Invalid date provided, using current time as fallback:', date);
  return new Date();
};

/**
 * Custom hook that encapsulates all event handling logic for the calendar
 * 
 * @param setModalOpen - Function to open the booking modal
 * @param setSelectedBooking - Function to set the selected booking for the modal
 * @param setSelectedDates - Function to set the selected dates for the modal
 * @param setSelectedRoomId - Function to set the selected room for the modal
 * @param setSelectedVenueId - Function to set the selected venue for the modal
 * @param isCartMode - Whether cart mode is enabled
 * @returns Object containing all event handler functions
 */
export const useCalendarEvents = (
  setModalOpen: (open: boolean) => void,
  setSelectedBooking: (booking: any) => void,
  setSelectedDates: (dates: { start: Date; end: Date } | null) => void,
  setSelectedRoomId: (id: string | null) => void,
  setSelectedVenueId: (id: string | null) => void,
  isCartMode: boolean
) => {
  const {
    filteredBookings,
    ghostBookings,
    rooms,
    updateBooking,
    deleteBooking,
    updateGhostBooking,
    convertGhostBookingToReal,
    addGhostBooking,
  } = useCalendar();

  const { addItem, updateItem } = useBookingCart();
  
  // Get the popover control functions
  const { openPopover, closePopover } = useCalendarPopover();

  // Add a ref to prevent immediate updates when the modal opens
  const isUpdatingRef = useRef(false);

  /**
   * Handle date selection (for creating new events)
   */
  const handleDateSelect = useCallback((selectInfo: DateSelectArg) => {
    // Ensure we have valid Date objects
    if (
      !selectInfo.start ||
      !selectInfo.end ||
      !(selectInfo.start instanceof Date) ||
      !(selectInfo.end instanceof Date)
    ) {
      console.error("Invalid date objects in selection", selectInfo);
      return;
    }
    
    // Validate dates and create safe copies
    const safeStart = ensureValidDate(selectInfo.start);
    const safeEnd = ensureValidDate(selectInfo.end);

    // Check if the selection is on a resource
    const resource = selectInfo.resource;

    if (resource) {
      // Get the room ID and venue ID from the resource
      const roomId = resource.id;
      const venueId = resource.extendedProps?.venueId;
      const room = rooms.find((r) => r.id === roomId);

      if (isCartMode && room) {
        // In cart mode, create a temporary booking and add it to the cart
        const cartItemId = uuidv4();
        const newBooking = {
          id: cartItemId,
          title: `New Booking - ${room.name}`,
          start: safeStart,
          end: safeEnd,
          venueId: venueId || room.venueId,
          roomId: roomId,
          description: "",
          isAllDay: selectInfo.allDay,
        };

        // Add to cart
        addItem({
          id: cartItemId,
          booking: newBooking,
          room,
          addedAt: new Date(),
        });
        
        // IMPORTANT: Create a ghost booking for the calendar display
        const ghostId = `ghost-cart-${cartItemId}`;
        const ghostBooking = {
          id: ghostId,
          title: `Cart: ${newBooking.title}`,
          start: safeStart,
          end: safeEnd,
          venueId: venueId || room.venueId,
          roomId: roomId,
          description: "",
          isAllDay: selectInfo.allDay || false,
          status: "pending" as const
        };
        
        // Add the ghost booking to the calendar directly
        addGhostBooking(ghostBooking);

        // Show visual feedback
        showToast(`Added to cart: New booking for ${room.name}`);
        
        // Open the cart briefly to show the new item
        const { isOpen, setCartOpen } = useBookingCart.getState();
        if (!isOpen) {
          // Open the cart
          setCartOpen(true);
          
          // Auto-close after 3 seconds
          setTimeout(() => {
            setCartOpen(false);
          }, 3000);
        }
        
        // Add a visual highlight to the newly created ghost event
        setTimeout(() => {
          // Find the newly created ghost event
          const ghostEl = document.querySelector(`[data-event-id="${ghostId}"]`) as HTMLElement;
          
          if (ghostEl) {
            // Add highlight effect
            ghostEl.classList.add("cart-update-highlight");
            ghostEl.style.border = "2px solid #22c55e";
            
            // Create and add a "New" badge
            const newBadge = document.createElement("div");
            newBadge.textContent = "New";
            newBadge.style.position = "absolute";
            newBadge.style.top = "-8px";
            newBadge.style.right = "8px";
            newBadge.style.backgroundColor = "#22c55e";
            newBadge.style.color = "white";
            newBadge.style.padding = "2px 6px";
            newBadge.style.borderRadius = "9999px";
            newBadge.style.fontSize = "10px";
            newBadge.style.fontWeight = "bold";
            newBadge.style.zIndex = "10";
            newBadge.style.boxShadow = "0 2px 4px rgba(0, 0, 0, 0.1)";
            
            // Add the badge to the event
            ghostEl.appendChild(newBadge);
            
            // Remove the highlight after 2 seconds
            setTimeout(() => {
              ghostEl.classList.remove("cart-update-highlight");
              ghostEl.style.border = "";
              
              // Remove the badge with a fade out effect
              newBadge.style.transition = "opacity 0.5s ease-out";
              newBadge.style.opacity = "0";
              
              // Remove the badge from the DOM after the transition
              setTimeout(() => {
                if (ghostEl && ghostEl.contains(newBadge)) {
                  ghostEl.removeChild(newBadge);
                }
              }, 500);
            }, 2000);
          }
        }, 100); // Small delay to ensure the ghost event is rendered
      } else {
        // Regular mode - open modal with ghost event
        setSelectedRoomId(roomId);
        setSelectedVenueId(venueId);
        setSelectedDates({
          start: safeStart,
          end: safeEnd,
        });
        setSelectedBooking(null);
        setModalOpen(true);
      }
    } else {
      // For non-resource views (day, week, month), just open the modal
      // The user will need to select a room in the modal
      setSelectedRoomId(null);
      setSelectedVenueId(null);
      setSelectedDates({
        start: safeStart,
        end: safeEnd,
      });
      setSelectedBooking(null);
      setModalOpen(true);
    }

    // Clear selection
    if (selectInfo.view.calendar) {
      selectInfo.view.calendar.unselect();
    }
  }, [
    rooms,
    isCartMode,
    addItem,
    setModalOpen,
    setSelectedBooking,
    setSelectedDates,
    setSelectedRoomId,
    setSelectedVenueId,
    addGhostBooking,
  ]);

  /**
   * Handle event click
   */
  const handleEventClick = useCallback(
    (clickInfo: EventClickArg) => {
      // Check if this is a ghost event
      const isGhost = clickInfo.event.extendedProps.isGhost;
      const isCartGhost = clickInfo.event.extendedProps.isCartGhost;

      if (isGhost) {
        // For ghost events, we'll open the booking modal with the ghost event data
        const ghostId = clickInfo.event.id;
        const ghostBooking = ghostBookings.find((b) => b.id === ghostId);

        if (ghostBooking) {
          // Create a deep copy of the ghost booking to prevent reference issues
          const ghostBookingCopy = JSON.parse(JSON.stringify(ghostBooking));

          // Convert string dates back to Date objects
          ghostBookingCopy.start = new Date(ghostBookingCopy.start);
          ghostBookingCopy.end = new Date(ghostBookingCopy.end);

          // Temporarily disable ghost updates to prevent loops
          window.disableGhostUpdates = true;

          setSelectedBooking(ghostBookingCopy);
          setSelectedDates(null);
          setSelectedRoomId(null);
          setSelectedVenueId(null);
          setModalOpen(true);

          // Re-enable ghost updates after a delay
          setTimeout(() => {
            window.disableGhostUpdates = false;
          }, 1000);
        }
        return;
      }

      // For regular events, proceed as normal
      const booking = filteredBookings.find((b) => b.id === clickInfo.event.id);
      if (booking) {
        // Create a deep copy of the booking to prevent reference issues
        const bookingCopy = JSON.parse(JSON.stringify(booking));

        // Convert string dates back to Date objects
        bookingCopy.start = new Date(bookingCopy.start);
        bookingCopy.end = new Date(bookingCopy.end);

        setSelectedBooking(bookingCopy);
        setSelectedDates(null);
        setSelectedRoomId(null);
        setSelectedVenueId(null);
        setModalOpen(true);
      }
    },
    [
      ghostBookings,
      filteredBookings,
      setModalOpen,
      setSelectedBooking,
      setSelectedDates,
      setSelectedRoomId,
      setSelectedVenueId,
    ]
  );

  /**
   * Handle event changes (drag, resize)
   */
  const handleEventChange = useCallback(
    (changeInfo: EventChangeArg) => {
      // Check if this is a ghost event
      const isGhost = changeInfo.event.extendedProps.isGhost;
      const isCartGhost = changeInfo.event.extendedProps.isCartGhost;

      if (isGhost) {
        // For ghost events, update the ghost booking
        const ghostId = changeInfo.event.id;
        const ghostBooking = ghostBookings.find((b) => b.id === ghostId);

        if (ghostBooking) {
          const resources = changeInfo.event.getResources();
          const resourceId = resources.length > 0 ? resources[0].id : null;

          // Ensure we have valid start and end dates
          const eventStart = changeInfo.event.start;
          const eventEnd = changeInfo.event.end;

          if (!eventStart || !(eventStart instanceof Date)) {
            console.error("Invalid start date in event change", eventStart);
            return;
          }

          if (resourceId) {
            // Get venue ID from the room's extended props
            const resource = resources[0];
            const venueId = resource.extendedProps?.venueId;
            const room = rooms.find((r) => r.id === resourceId);

            const updatedGhostBooking = {
              ...ghostBooking,
              start: new Date(eventStart),
              end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
              roomId: resourceId,
              venueId: venueId || ghostBooking.venueId,
            };

            // Update the ghost booking
            updateGhostBooking(updatedGhostBooking);

            // If this is a cart ghost, we need to update the cart item too
            if (isCartGhost && room) {
              // Extract the cart item ID from the ghost ID
              const cartItemId = ghostId.replace("ghost-cart-", "");

              // Get the current cart items
              const { items, updateItem: updateCartItem } = useBookingCart.getState();

              // Find the cart item
              const cartItem = items.find((item) => item.id === cartItemId);

              if (cartItem) {
                // Create updated cart item
                const updatedCartItem = {
                  ...cartItem,
                  booking: {
                    ...cartItem.booking,
                    start: new Date(eventStart),
                    end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
                    roomId: resourceId,
                    venueId: venueId || cartItem.booking.venueId,
                  },
                  room: room,
                };

                // Update the cart item
                updateCartItem(cartItemId, updatedCartItem);

                // Add visual feedback directly on the event element
                const fcEvent = changeInfo.event as unknown as FullCalendarEventWithEl;
                if (fcEvent && fcEvent.el) {
                  const eventEl = fcEvent.el;
                  
                  // Add a highlight effect to the event
                  eventEl.style.transition = "box-shadow 0.3s ease-out, transform 0.3s ease-out";
                  eventEl.style.boxShadow = "0 0 0 3px rgba(34, 197, 94, 0.7)";
                  eventEl.style.transform = "scale(1.02)";
                  
                  // Create and add a "Updated" badge to the event
                  const updateBadge = document.createElement("div");
                  updateBadge.textContent = "Updated";
                  updateBadge.style.position = "absolute";
                  updateBadge.style.top = "-8px";
                  updateBadge.style.right = "8px";
                  updateBadge.style.backgroundColor = "#22c55e";
                  updateBadge.style.color = "white";
                  updateBadge.style.padding = "2px 6px";
                  updateBadge.style.borderRadius = "9999px";
                  updateBadge.style.fontSize = "10px";
                  updateBadge.style.fontWeight = "bold";
                  updateBadge.style.zIndex = "10";
                  updateBadge.style.boxShadow = "0 2px 4px rgba(0, 0, 0, 0.1)";
                  
                  // Add the badge to the event
                  eventEl.appendChild(updateBadge);
                  
                  // Remove the highlight effect and badge after 2 seconds
                  setTimeout(() => {
                    eventEl.style.boxShadow = "";
                    eventEl.style.transform = "";
                    
                    // Remove the badge with a fade out effect
                    updateBadge.style.transition = "opacity 0.5s ease-out";
                    updateBadge.style.opacity = "0";
                    
                    // Remove the badge from the DOM after the transition
                    setTimeout(() => {
                      if (eventEl.contains(updateBadge)) {
                        eventEl.removeChild(updateBadge);
                      }
                    }, 500);
                  }, 2000);
                }

                // Show feedback toast or notification
                showToast(`Booking updated: ${updatedCartItem.booking.title || "Untitled Booking"}`);
              }
            }
          } else {
            // If no resource (e.g., in day/week view), just update times
            const updatedGhostBooking = {
              ...ghostBooking,
              start: new Date(eventStart),
              end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
            };

            // Update the ghost booking
            updateGhostBooking(updatedGhostBooking);

            // If this is a cart ghost, update the cart item too
            if (isCartGhost) {
              // Extract the cart item ID from the ghost ID
              const cartItemId = ghostId.replace("ghost-cart-", "");

              // Get the current cart items
              const { items, updateItem: updateCartItem } = useBookingCart.getState();

              // Find the cart item
              const cartItem = items.find((item) => item.id === cartItemId);

              if (cartItem) {
                // Create updated cart item
                const updatedCartItem = {
                  ...cartItem,
                  booking: {
                    ...cartItem.booking,
                    start: new Date(eventStart),
                    end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
                  },
                };

                // Update the cart item
                updateCartItem(cartItemId, updatedCartItem);

                // Add visual feedback directly on the event element
                const fcEvent = changeInfo.event as unknown as FullCalendarEventWithEl;
                if (fcEvent && fcEvent.el) {
                  const eventEl = fcEvent.el;
                  eventEl.classList.add("cart-update-highlight");
                  
                  // Remove the highlight class after 2 seconds
                  setTimeout(() => {
                    eventEl.classList.remove("cart-update-highlight");
                  }, 2000);
                }

                // Show feedback toast or notification
                showToast(`Booking updated: ${updatedCartItem.booking.title || "Untitled Booking"}`);
              }
            }
          }
        }
        return;
      }

      // For regular events, proceed as normal
      const booking = filteredBookings.find((b) => b.id === changeInfo.event.id);

      if (booking) {
        const resources = changeInfo.event.getResources();
        const resourceId = resources.length > 0 ? resources[0].id : null;

        // Ensure we have valid start and end dates
        const eventStart = changeInfo.event.start;
        const eventEnd = changeInfo.event.end;

        if (!eventStart || !(eventStart instanceof Date)) {
          console.error("Invalid start date in event change", eventStart);
          return;
        }

        if (resourceId) {
          // Get venue ID from the room's extended props
          const resource = resources[0];
          const venueId = resource.extendedProps?.venueId;

          const updatedBooking = {
            ...booking,
            start: new Date(eventStart),
            end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000), // Default to 1 hour if no end
            roomId: resourceId,
            venueId: venueId || booking.venueId,
          };

          updateBooking(updatedBooking);
        } else {
          // If no resource (e.g., in day/week view), just update times
          const updatedBooking = {
            ...booking,
            start: new Date(eventStart),
            end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000), // Default to 1 hour if no end
          };

          updateBooking(updatedBooking);
        }
      }
    },
    [rooms, ghostBookings, filteredBookings, updateGhostBooking, updateBooking, updateItem]
  );

  /**
   * Handle event hover to display popover
   */
  const handleEventMouseEnter = useCallback((info: any) => {
    // First, validate the event data to ensure it has valid dates
    if (info.event) {
      const eventStart = info.event.start;
      const eventEnd = info.event.end;
      
      // Skip opening popover if dates are invalid
      if (!eventStart || !(eventStart instanceof Date) || isNaN(eventStart.getTime())) {
        console.warn('Event has invalid start date, skipping popover:', info.event);
        return;
      }
      
      // Ensure end date is valid (use start date + 1 hour as fallback)
      if (!eventEnd || !(eventEnd instanceof Date) || isNaN(eventEnd.getTime())) {
        console.warn('Event has invalid end date, using fallback for popover:', info.event);
        info.event.end = new Date(eventStart.getTime() + 3600000); // Add 1 hour as fallback
      }
    }
    
    // Open the popover with validated event data
    openPopover({ 
      event: info.event, 
      el: info.el 
    });
  }, [openPopover]);

  /**
   * Handle mouse leave to close popover
   */
  const handleEventMouseLeave = useCallback(() => {
    // Small delay to check if cursor moved to popover
    setTimeout(() => {
      // Only close if mouse is not over the popover
      const state = useCalendarPopover.getState();
      if (!state.isMouseOverPopover) {
        closePopover();
      }
    }, 50);
  }, [closePopover]);

  /**
   * Clean up popover on various events
   */
  const clearPopover = useCallback(() => {
    // Only close if mouse is not over the popover
    const { isMouseOverPopover } = useCalendarPopover.getState();
    if (!isMouseOverPopover) {
      closePopover();
    }
  }, [closePopover]);

  /**
   * Handles events being mounted in the DOM
   */
  const eventDidMountHandler = useCallback((info: any) => {
    const { event, el } = info;
    const { roomId, venueId, isGhost, isCartGhost, status, holdRank } = event.extendedProps;
    
    // Add data-event-id attribute
    el.setAttribute("data-event-id", event.id);
    
    // Add status as a data attribute for styling purposes
    if (status) {
      el.setAttribute("data-status", status);
      el.classList.add(`event-status-${status.toLowerCase().replace(' ', '-')}`);
    }

    const room = rooms.find((r) => r.id === roomId);
    
    if (room) {
      el.style.borderLeft = `5px solid ${room.color}`;
    }
    
    // Apply styling for ghost events
    if (isGhost || isCartGhost) {
      el.style.opacity = "0.7";
      el.style.backgroundColor = "#f0f4f8";
      
      if (isCartGhost) {
        el.classList.add("cart-ghost-event");
        // Add visual indication that this is in the cart
        const cartBadge = document.createElement("div");
        cartBadge.className = "cart-badge";
        cartBadge.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="8" cy="21" r="1"/><circle cx="19" cy="21" r="1"/><path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/></svg>`;
        el.appendChild(cartBadge);
      }

      if (status === "Hold") {
        const holdBadge = document.createElement("div");
        holdBadge.className = "hold-badge";
        holdBadge.innerText = `${holdRank || ""}`;
        el.appendChild(holdBadge);
      }
    }
  }, [rooms]);

  /**
   * Manually synchronize a cart ghost item when eventDrop doesn't fire
   * Used to fix a bug in FullCalendar where eventDrop doesn't always trigger
   */
  const synchronizeCartItem = useCallback((eventInfo: any) => {
    console.log('Manually synchronizing cart ghost event:', eventInfo);

    // Check if this is a cart ghost event
    const isCartGhost = eventInfo.extendedProps?.isCartGhost;
    if (!isCartGhost) return;
    
    const ghostId = eventInfo.id;
    const ghostBooking = ghostBookings.find((b) => b.id === ghostId);
    
    if (ghostBooking) {
      // Collect resource data
      const resources = eventInfo.getResources();
      const resourceId = resources.length > 0 ? resources[0].id : null;
      
      // Ensure we have valid dates
      const eventStart = eventInfo.start;
      const eventEnd = eventInfo.end;

      if (!eventStart || !(eventStart instanceof Date)) {
        console.error("Invalid start date in manual sync", eventStart);
        return;
      }
      
      if (resourceId) {
        // Get venue ID from the resource
        const resource = resources[0];
        const venueId = resource.extendedProps?.venueId;
        const room = rooms.find((r) => r.id === resourceId);
        
        // Create updated ghost booking
        const updatedGhostBooking = {
          ...ghostBooking,
          start: new Date(eventStart),
          end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
          roomId: resourceId,
          venueId: venueId || ghostBooking.venueId,
        };
        
        // Update the ghost booking
        updateGhostBooking(updatedGhostBooking);
        
        // Update the cart item too
        if (room) {
          // Extract the cart item ID from the ghost ID
          const cartItemId = ghostId.replace("ghost-cart-", "");
          
          // Get the current cart items
          const { items, updateItem: updateCartItem } = useBookingCart.getState();
          
          // Find the cart item
          const cartItem = items.find((item) => item.id === cartItemId);
          
          if (cartItem) {
            // Create updated cart item
            const updatedCartItem = {
              ...cartItem,
              booking: {
                ...cartItem.booking,
                start: new Date(eventStart),
                end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
                roomId: resourceId,
                venueId: venueId || cartItem.booking.venueId,
              },
              room: room,
            };
            
            // Update the cart item
            updateCartItem(cartItemId, updatedCartItem);
            
            // Show a toast to indicate the manual sync happened
            showToast(`Cart item updated after drag: Fixed an issue where the booking wasn't updating properly.`);
          }
        }
      }
    }
  }, [ghostBookings, rooms, updateGhostBooking]);

  return {
    handleDateSelect,
    handleEventClick,
    handleEventChange,
    handleEventMouseEnter,
    handleEventMouseLeave,
    eventDidMountHandler,
    clearPopover,
    synchronizeCartItem,
  };
};

// Declare the global types
declare global {
  interface Window {
    disableGhostUpdates?: boolean;
    eventPopoverData?: {
      event: any;
      el: HTMLElement;
    } | null;
  }
}