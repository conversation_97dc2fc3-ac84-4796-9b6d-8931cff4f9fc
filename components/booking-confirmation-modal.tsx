"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useBookingCart, type CartItem } from "@/store/booking-cart"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { format } from "date-fns"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertCircle, CalendarPlus, Edit, Plus, RefreshCw, ShoppingBag, Trash2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON>ertDescription, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

interface BookingConfirmationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onComplete: (eventId?: string, eventName?: string) => void
  onCancel: () => void
}

export function BookingConfirmationModal({
  open,
  onOpenChange,
  onComplete,
  onCancel,
}: BookingConfirmationModalProps) {
  const { items, removeItem, updateItem } = useBookingCart()
  const [activeTab, setActiveTab] = useState<string>("review")
  const [processingCheckout, setProcessingCheckout] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState<string>("")
  const [showNewEventForm, setShowNewEventForm] = useState(false)
  const [newEventName, setNewEventName] = useState("")
  
  // Sample CS Events - in a real implementation, these would come from NetSuite
  const sampleEvents = [
    { id: "evt1", name: "Annual Conference 2025" },
    { id: "evt2", name: "Product Launch Q2" },
    { id: "evt3", name: "Executive Retreat" },
  ]

  // Function to handle editing a booking inline
  const handleEditBooking = (item: CartItem, field: string, value: any) => {
    if (field === "title") {
      updateItem(item.id, {
        ...item,
        booking: {
          ...item.booking,
          title: value,
        },
      })
    }
  }

  // Function to handle the checkout process
  const handleCheckout = async () => {
    setProcessingCheckout(true)
    
    // Simulate API call to process booking
    try {
      // In a real implementation, this would call the NetSuite API
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Pass the selected event information back to the parent component
      if (selectedEvent === "new-event" && newEventName.trim() !== "") {
        onComplete("new-event", newEventName.trim())
      } else if (selectedEvent) {
        const eventName = sampleEvents.find(e => e.id === selectedEvent)?.name || ""
        onComplete(selectedEvent, eventName)
      } else {
        onComplete()
      }
    } catch (error) {
      console.error("Error processing checkout:", error)
    } finally {
      setProcessingCheckout(false)
    }
  }

  const handleCreateNewEvent = () => {
    // In a real implementation, this would create a new event in NetSuite
    if (newEventName.trim() === "") return
    
    // For demo, we'll just update state
    setSelectedEvent("new-event")
    setShowNewEventForm(false)
  }

  const totalBookings = items.length
  const totalHours = items.reduce((acc, item) => {
    const duration = new Date(item.booking.end).getTime() - new Date(item.booking.start).getTime()
    return acc + (duration / (1000 * 60 * 60))
  }, 0)
  
  // Calculate booking total - in a real app this would include pricing logic
  const bookingTotal = items.reduce((acc, item) => {
    const duration = new Date(item.booking.end).getTime() - new Date(item.booking.start).getTime()
    const hours = duration / (1000 * 60 * 60)
    // Assume a rate of $100 per hour for this demo
    return acc + (hours * 100)
  }, 0)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Confirm Your Bookings</DialogTitle>
          <DialogDescription>
            Review and finalize your bookings before completing the checkout process.
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="review">Review Bookings</TabsTrigger>
            <TabsTrigger value="event">Event Assignment</TabsTrigger>
          </TabsList>
          
          <TabsContent value="review" className="space-y-4">
            {items.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No bookings added</AlertTitle>
                <AlertDescription>
                  You haven't added any bookings to your cart yet. Add bookings from the calendar to proceed.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-4">
                <ScrollArea className="h-[350px] rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Room</TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Time</TableHead>
                        <TableHead>Duration</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {items.map((item) => {
                        const start = new Date(item.booking.start)
                        const end = new Date(item.booking.end)
                        const durationMs = end.getTime() - start.getTime()
                        const durationHours = durationMs / (1000 * 60 * 60)
                        
                        return (
                          <TableRow key={item.id}>
                            <TableCell className="font-medium">{item.room.name}</TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Input 
                                  className="h-8 w-full"
                                  value={item.booking.title} 
                                  onChange={(e) => handleEditBooking(item, "title", e.target.value)}
                                />
                                <Button size="icon" variant="ghost" className="h-8 w-8">
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                            <TableCell>{format(start, "MMM d, yyyy")}</TableCell>
                            <TableCell>{format(start, "h:mm a")} - {format(end, "h:mm a")}</TableCell>
                            <TableCell>{durationHours.toFixed(1)} hours</TableCell>
                            <TableCell>
                              <Button 
                                size="icon" 
                                variant="ghost" 
                                onClick={() => removeItem(item.id)}
                                className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                </ScrollArea>
                
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="flex flex-col space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Total Bookings:</span>
                      <span className="font-medium">{totalBookings}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Total Hours:</span>
                      <span className="font-medium">{totalHours.toFixed(1)} hours</span>
                    </div>
                    <Separator className="my-2" />
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total Amount:</span>
                      <span>${bookingTotal.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="event" className="space-y-4">
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="event-select">Assign bookings to an existing CS Event</Label>
                <Select value={selectedEvent} onValueChange={setSelectedEvent}>
                  <SelectTrigger id="event-select" className="w-full">
                    <SelectValue placeholder="Select an event" />
                  </SelectTrigger>
                  <SelectContent>
                    {sampleEvents.map((event) => (
                      <SelectItem key={event.id} value={event.id}>
                        {event.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="h-px flex-1 bg-muted"></div>
                <span className="text-xs text-muted-foreground">OR</span>
                <div className="h-px flex-1 bg-muted"></div>
              </div>

              {showNewEventForm ? (
                <div className="space-y-3 p-4 border rounded-lg">
                  <Label htmlFor="new-event-name">New Event Name</Label>
                  <div className="flex space-x-2">
                    <Input 
                      id="new-event-name" 
                      value={newEventName} 
                      onChange={(e) => setNewEventName(e.target.value)} 
                      placeholder="Enter event name"
                    />
                    <Button onClick={handleCreateNewEvent} disabled={newEventName.trim() === ""}>
                      Create
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setShowNewEventForm(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => setShowNewEventForm(true)}
                >
                  <CalendarPlus className="mr-2 h-4 w-4" />
                  Create New Event
                </Button>
              )}
              
              {selectedEvent && (
                <Alert className="bg-green-50 text-green-800 border-green-200">
                  <AlertTitle className="text-green-800">Event Selected</AlertTitle>
                  <AlertDescription className="text-green-700">
                    Your bookings will be assigned to: {" "}
                    <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
                      {selectedEvent === "new-event" ? newEventName : sampleEvents.find(e => e.id === selectedEvent)?.name}
                    </Badge>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="sm:justify-between">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <div className="flex space-x-2">
            <Button
              disabled={items.length === 0 || processingCheckout}
              onClick={handleCheckout}
              className="gap-2"
            >
              {processingCheckout ? <RefreshCw className="h-4 w-4 animate-spin" /> : <ShoppingBag className="h-4 w-4" />}
              {processingCheckout ? "Processing..." : "Complete Checkout"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
