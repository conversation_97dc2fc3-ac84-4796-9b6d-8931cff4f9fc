"use client"

import type React from "react"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Clock, CalendarX, Calendar, AlertTriangle, HelpCircle, CheckCircle2, XCircle, PauseCircle } from "lucide-react"
import type { Venue, Room, BookingStatus } from "@/types/calendar"

interface BookingFormFieldsProps {
  title: string
  setTitle: (title: string) => void
  description: string
  setDescription: (description: string) => void
  venueId: string
  handleVenueChange: (venueId: string) => void
  roomId: string
  setRoomId: (roomId: string) => void
  start: Date
  end: Date
  handleDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleTimeChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  isAllDay: boolean
  setIsAllDay: (isAllDay: boolean) => void
  createGhost: boolean
  handleGhostToggle: (checked: boolean) => void
  venues: Venue[]
  venueRooms: Room[]
  showGhostToggle?: boolean
  isCompact?: boolean
  status?: BookingStatus
  setStatus?: (status: BookingStatus) => void
  holdRank?: number
  setHoldRank?: (rank: number) => void
  lostReason?: string
  setLostReason?: (reason: string) => void
  showStatusSelector?: boolean
}

export function BookingFormFields({
  title,
  setTitle,
  description,
  setDescription,
  venueId,
  handleVenueChange,
  roomId,
  setRoomId,
  start,
  end,
  handleDateChange,
  handleTimeChange,
  isAllDay,
  setIsAllDay,
  createGhost,
  handleGhostToggle,
  venues,
  venueRooms,
  showGhostToggle = true,
  isCompact = false,
  status = "Tentative",
  setStatus = () => {},
  holdRank = 1,
  setHoldRank = () => {},
  lostReason = "",
  setLostReason = () => {},
  showStatusSelector = false,
}: BookingFormFieldsProps) {
  const statusOptions: {value: BookingStatus; label: string; icon: React.ReactNode; description: string}[] = [
    { 
      value: "Inquiry", 
      label: "Inquiry", 
      icon: <HelpCircle className="h-4 w-4 text-purple-500" />,
      description: "Initial customer interest" 
    },
    { 
      value: "Prospect", 
      label: "Prospect", 
      icon: <Calendar className="h-4 w-4 text-pink-500" />,
      description: "Potential booking being discussed" 
    },
    { 
      value: "Tentative", 
      label: "Tentative", 
      icon: <AlertTriangle className="h-4 w-4 text-amber-500" />,
      description: "Booking not yet confirmed" 
    },
    { 
      value: "Definite", 
      label: "Definite", 
      icon: <CheckCircle2 className="h-4 w-4 text-green-500" />,
      description: "Confirmed booking" 
    },
    { 
      value: "Hold", 
      label: "Hold", 
      icon: <PauseCircle className="h-4 w-4 text-cyan-500" />,
      description: "Space being held temporarily" 
    },
    { 
      value: "Lost", 
      label: "Lost", 
      icon: <XCircle className="h-4 w-4 text-red-500" />,
      description: "Booking opportunity lost" 
    },
    { 
      value: "Canceled", 
      label: "Canceled", 
      icon: <CalendarX className="h-4 w-4 text-gray-500" />,
      description: "Booking was canceled" 
    },
  ];

  return (
    <div className="space-y-5 py-2">
      <div className="space-y-2">
        <Label htmlFor="title" className="text-sm font-medium">
          Title *
        </Label>
        <Input
          id="title"
          name="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter booking title"
          required
          autoFocus
          className="w-full"
        />
      </div>

      {showStatusSelector && (
        <div className="space-y-2">
          <Label htmlFor="status" className="text-sm font-medium">
            Status
          </Label>
          <Select value={status} onValueChange={(value) => setStatus(value as BookingStatus)}>
            <SelectTrigger id="status" className="w-full">
              <SelectValue placeholder="Select a status" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem 
                  key={option.value} 
                  value={option.value}
                  className="flex items-center gap-2"
                >
                  <div className="flex items-center gap-2">
                    {option.icon}
                    <span>{option.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            {statusOptions.find(option => option.value === status)?.description || "Set the current status of this booking"}
          </p>
        </div>
      )}

      {showStatusSelector && status === "Hold" && (
        <div className="space-y-2">
          <Label htmlFor="holdRank" className="text-sm font-medium">
            Hold Rank
          </Label>
          <Input
            id="holdRank"
            type="number"
            min="1"
            max="10"
            value={holdRank}
            onChange={(e) => setHoldRank(parseInt(e.target.value, 10))}
            className="w-full"
          />
          <p className="text-xs text-muted-foreground">
            Lower numbers have higher priority (1 is first hold)
          </p>
        </div>
      )}

      {showStatusSelector && status === "Lost" && (
        <div className="space-y-2">
          <Label htmlFor="lostReason" className="text-sm font-medium">
            Lost Reason
          </Label>
          <Input
            id="lostReason"
            value={lostReason}
            onChange={(e) => setLostReason(e.target.value)}
            placeholder="Reason the booking was lost"
            className="w-full"
          />
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="venue" className="text-sm font-medium">
          Venue *
        </Label>
        <Select value={venueId} onValueChange={handleVenueChange}>
          <SelectTrigger id="venue" className="w-full">
            <SelectValue placeholder="Select a venue" />
          </SelectTrigger>
          <SelectContent>
            {venues.map((venue) => (
              <SelectItem key={venue.id} value={venue.id}>
                {venue.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="room" className="text-sm font-medium">
          Room *
        </Label>
        <Select value={roomId} onValueChange={(value) => setRoomId(value)}>
          <SelectTrigger id="room" className="w-full">
            <SelectValue placeholder="Select a room" />
          </SelectTrigger>
          <SelectContent>
            {venueRooms.map((room) => (
              <SelectItem key={room.id} value={room.id}>
                {room.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {venueRooms.length === 0 && (
          <p className="text-xs text-destructive mt-1">
            No rooms available for this venue. Please select another venue or add rooms.
          </p>
        )}
      </div>

      <div className="flex items-center gap-2 py-1">
        <Checkbox id="isAllDay" checked={isAllDay} onCheckedChange={(checked) => setIsAllDay(!!checked)} />
        <Label htmlFor="isAllDay" className="text-sm font-medium cursor-pointer">
          All day event
        </Label>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="startDate" className="text-sm font-medium">
            Start Date
          </Label>
          <Input
            id="startDate"
            type="date"
            name="startDate"
            value={start.toISOString().split("T")[0]}
            onChange={handleDateChange}
            className="w-full"
          />
        </div>

        {!isAllDay && (
          <div className="space-y-2">
            <Label htmlFor="startTime" className="text-sm font-medium">
              Start Time
            </Label>
            <Input
              id="startTime"
              type="time"
              name="startTime"
              value={`${String(start.getHours()).padStart(2, "0")}:${String(start.getMinutes()).padStart(2, "0")}`}
              onChange={handleTimeChange}
              className="w-full"
            />
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="endDate" className="text-sm font-medium">
            End Date
          </Label>
          <Input
            id="endDate"
            type="date"
            name="endDate"
            value={end.toISOString().split("T")[0]}
            onChange={handleDateChange}
            className="w-full"
          />
        </div>

        {!isAllDay && (
          <div className="space-y-2">
            <Label htmlFor="endTime" className="text-sm font-medium">
              End Time
            </Label>
            <Input
              id="endTime"
              type="time"
              name="endTime"
              value={`${String(end.getHours()).padStart(2, "0")}:${String(end.getMinutes()).padStart(2, "0")}`}
              onChange={handleTimeChange}
              className="w-full"
            />
          </div>
        )}
      </div>

      {!isCompact && (
        <div className="space-y-2">
          <Label htmlFor="description" className="text-sm font-medium">
            Description
          </Label>
          <Textarea
            id="description"
            name="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter booking description"
            rows={3}
            className="w-full resize-none"
          />
        </div>
      )}

      {showGhostToggle && (
        <div className="flex items-center gap-2 pt-3 mt-2 border-t">
          <Checkbox
            id="createGhost"
            checked={createGhost}
            onCheckedChange={(checked) => handleGhostToggle(!!checked)}
          />
          <div>
            <Label htmlFor="createGhost" className="flex items-center gap-1 text-sm font-medium cursor-pointer">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>Show preview on calendar</span>
            </Label>
            <p className="text-xs text-muted-foreground">
              Display a preview of this booking on the calendar as you make changes
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

