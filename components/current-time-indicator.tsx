"use client"

import { useEffect, useState } from "react"

interface CurrentTimeIndicatorProps {
  view: "day" | "week" | "timeline"
}

export function CurrentTimeIndicator({ view }: CurrentTimeIndicatorProps) {
  const [currentTime, setCurrentTime] = useState(new Date())

  // Update current time every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [])

  // Calculate position based on current time
  const hours = currentTime.getHours()
  const minutes = currentTime.getMinutes()
  const totalMinutes = hours * 60 + minutes

  // Position from top (1 hour = 60px)
  const topPosition = (totalMinutes / 60) * 60

  // For timeline view, we need a vertical line
  if (view === "timeline") {
    // Find the current day column
    const today = new Date()
    const dayOfWeek = today.getDay()

    return (
      <div
        className="absolute top-0 bottom-0 z-30 pointer-events-none"
        style={{
          left: `calc(${dayOfWeek} * (100% / 7) + (100% / 14))`,
        }}
      >
        <div className="relative h-full flex items-center">
          {/* Vertical Line */}
          <div className="h-full w-[2px] bg-destructive"></div>

          {/* Circle indicator at the top (Google style) */}
          <div className="absolute top-0 -translate-x-1/2 w-3 h-3 rounded-full bg-destructive border-2 border-background"></div>
        </div>
      </div>
    )
  }

  if (view === "day" || view === "week") {
    return (
      <div className="absolute left-0 right-0 z-30 pointer-events-none" style={{ top: `${topPosition}px` }}>
        <div className="relative flex items-center">
          {/* Time label */}
          <div className="absolute -left-1 -translate-y-1/2 bg-destructive text-destructive-foreground text-xs px-1 py-0.5 rounded-sm z-10 font-medium">
            {hours.toString().padStart(2, "0")}:{minutes.toString().padStart(2, "0")}
          </div>

          {/* Line */}
          <div className="w-full h-[2px] bg-destructive"></div>

          {/* Circle indicator (Google style) */}
          <div className="absolute left-0 -translate-y-1/2 w-3 h-3 rounded-full bg-destructive border-2 border-background"></div>
        </div>
      </div>
    )
  }

  return null
}

