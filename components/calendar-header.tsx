"use client"

import { useState, useEffect } from "react"
import { useCalendar } from "@/context/calendar-context"
import { MONTHS } from "@/utils/date-utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Moon,
  Plus,
  Settings,
  Sun,
  ShoppingCart,
  Maximize,
  Minimize,
} from "lucide-react"
import { ViewSelector } from "./view-selector"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { AppearanceSettings } from "./appearance-settings"
import { ViewOptions } from "./view-options"
import { useBookingCart } from "@/store/booking-cart"
import { Badge } from "@/components/ui/badge"

interface CalendarHeaderProps {
  toggleTheme?: () => void
  currentTheme?: "light" | "dark"
}

export function CalendarHeader({ toggleTheme, currentTheme = "light" }: CalendarHeaderProps) {
  const { currentDate, nextPeriod, prevPeriod, goToToday, view } = useCalendar()
  const { isOpen, toggleCart, items } = useBookingCart()
  const [appearanceOpen, setAppearanceOpen] = useState(false)
  const [viewOptionsOpen, setViewOptionsOpen] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)

  const formatHeaderDate = () => {
    // Ensure currentDate is a valid Date object
    if (!(currentDate instanceof Date) || isNaN(currentDate.getTime())) {
      console.error("Invalid date object in formatHeaderDate", currentDate)
      return "Invalid date"
    }

    const month = MONTHS[currentDate.getMonth()]
    const year = currentDate.getFullYear()

    if (view === "month") {
      return `${month} ${year}`
    } else if (view === "week" || view === "timeline") {
      // For week view, show the month and year of the first day of the week
      const firstDay = new Date(currentDate)
      firstDay.setDate(currentDate.getDate() - currentDate.getDay())
      const lastDay = new Date(firstDay)
      lastDay.setDate(firstDay.getDate() + 6)

      const firstMonth = MONTHS[firstDay.getMonth()]
      const lastMonth = MONTHS[lastDay.getMonth()]
      const firstYear = firstDay.getFullYear()
      const lastYear = lastDay.getFullYear()

      if (firstMonth === lastMonth && firstYear === lastYear) {
        return `${firstMonth} ${firstYear}`
      } else if (firstYear === lastYear) {
        return `${firstMonth} - ${lastMonth} ${firstYear}`
      } else {
        return `${firstMonth} ${firstYear} - ${lastMonth} ${lastYear}`
      }
    } else {
      // Day view
      return `${month} ${currentDate.getDate()}, ${year}`
    }
  }

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement
        .requestFullscreen()
        .then(() => {
          setIsFullscreen(true)
        })
        .catch((err) => {
          console.error(`Error attempting to enable fullscreen: ${err.message}`)
        })
    } else {
      if (document.exitFullscreen) {
        document
          .exitFullscreen()
          .then(() => {
            setIsFullscreen(false)
          })
          .catch((err) => {
            console.error(`Error attempting to exit fullscreen: ${err.message}`)
          })
      }
    }
  }

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener("fullscreenchange", handleFullscreenChange)
    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange)
    }
  }, [])

  return (
    <header className="flex items-center h-16 px-4 border-b shrink-0 md:px-6 bg-background text-foreground">
      <div className="flex items-center gap-2 text-lg font-medium sm:text-base mr-4">
        <CalendarIcon className="w-6 h-6 text-primary" />
        <span>Calendar</span>
      </div>

      <div className="flex items-center gap-2 ml-auto">
        <Button variant="outline" size="sm" onClick={goToToday} className="rounded-full px-4 hover:bg-muted">
          Today
        </Button>

        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={prevPeriod} className="rounded-full">
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={nextPeriod} className="rounded-full">
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        <div className="text-lg font-medium mx-4 min-w-[180px] text-center">{formatHeaderDate()}</div>

        <ViewSelector />

        <Button size="sm" className="ml-4 gap-1 bg-primary text-primary-foreground hover:bg-primary/90 rounded-full">
          <Plus className="w-4 h-4" />
          Create
        </Button>

        {/* Settings Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="ml-2 rounded-full" aria-label="Calendar settings">
              <Settings className="w-5 h-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Calendar Settings</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                onSelect={(e) => {
                  e.preventDefault()
                  setAppearanceOpen(true)
                }}
              >
                Appearance
              </DropdownMenuItem>
              <DropdownMenuItem
                onSelect={(e) => {
                  e.preventDefault()
                  setViewOptionsOpen(true)
                }}
              >
                View options
              </DropdownMenuItem>
              <DropdownMenuItem>Working hours</DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>Print</DropdownMenuItem>
              <DropdownMenuItem>Export calendar</DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onSelect={(e) => {
                e.preventDefault()
                toggleFullscreen()
              }}
            >
              {isFullscreen ? (
                <>
                  <Minimize className="w-4 h-4 mr-2" />
                  Exit Fullscreen
                </>
              ) : (
                <>
                  <Maximize className="w-4 h-4 mr-2" />
                  Enter Fullscreen
                </>
              )}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Add the cart toggle button here, before the theme toggle */}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleCart}
          className="ml-2 rounded-full relative"
          aria-label="Toggle booking cart"
        >
          <ShoppingCart className="w-5 h-5" />
          {items.length > 0 && (
            <Badge className="absolute -right-1 -top-1 h-5 w-5 p-0 flex items-center justify-center">
              {items.length}
            </Badge>
          )}
        </Button>

        <Button
          variant="ghost"
          size="icon"
          onClick={toggleTheme}
          className="ml-2 rounded-full"
          aria-label={currentTheme === "light" ? "Switch to dark mode" : "Switch to light mode"}
        >
          {currentTheme === "light" ? <Moon className="w-5 h-5" /> : <Sun className="w-5 h-5" />}
        </Button>

        {/* Appearance Settings Dialog */}
        <AppearanceSettings open={appearanceOpen} onOpenChange={setAppearanceOpen} />

        {/* View Options Dialog */}
        <ViewOptions open={viewOptionsOpen} onOpenChange={setViewOptionsOpen} />
      </div>
    </header>
  )
}

