"use client"

import { useCalendar } from "@/context/calendar-context"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ChevronDown } from "lucide-react"
import { useCallback } from "react"

export function ViewSelector() {
  const { view, setView } = useCalendar()

  const viewLabels = {
    day: "Day",
    week: "Week",
    month: "Month",
    timeline: "Timeline",
  }

  // Use useCallback to ensure stable function references
  const handleViewChange = useCallback(
    (newView: "day" | "week" | "month" | "timeline") => {
      console.log(`ViewSelector: changing view to ${newView}`)
      setView(newView)
    },
    [setView],
  )

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-1">
          {viewLabels[view]}
          <ChevronDown className="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleViewChange("day")}>Day</DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleViewChange("week")}>Week</DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleViewChange("month")}>Month</DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleViewChange("timeline")}>Timeline</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

