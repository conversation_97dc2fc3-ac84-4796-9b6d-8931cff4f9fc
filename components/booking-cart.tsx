"use client"

import { useEffect, useState, useRef } from "react"
import { useBookingCart, type CartItem } from "@/store/booking-cart"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>rollA<PERSON> } from "@/components/ui/scroll-area"
import { ShoppingCart, X, Calendar, Clock, MapPin, Trash2, Edit } from "lucide-react"
import { useCalendar } from "@/context/calendar-context"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { format, isSameDay } from "date-fns"

export function BookingCart() {
  const { items, removeItem, clearCart, isOpen, toggleCart, setCartOpen } = useBookingCart()
  const { addBooking, addGhostBooking, deleteGhostBooking, clearGhostBookings, ghostBookings } = useCalendar()
  const [mounted, setMounted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Use a ref to track the previous items to avoid unnecessary updates
  const prevItemsRef = useRef<CartItem[]>([])
  const isInitialRender = useRef(true)
  const isSyncingRef = useRef(false)
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Hydrate the cart from localStorage on client side
  useEffect(() => {
    setMounted(true)
  }, [])

  // Synchronize cart items with ghost events - with optimizations to prevent infinite loops
  useEffect(() => {
    if (!mounted) return

    // Skip the first render since CalendarInitializer will handle it
    if (isInitialRender.current) {
      isInitialRender.current = false
      prevItemsRef.current = items
      return
    }

    // Prevent synchronization if already in progress
    if (isSyncingRef.current) return

    // Check if items have actually changed to avoid unnecessary updates
    const itemsChanged =
      items.length !== prevItemsRef.current.length ||
      items.some(
        (item, index) =>
          !prevItemsRef.current[index] ||
          prevItemsRef.current[index]?.id !== item.id ||
          prevItemsRef.current[index]?.booking.start.getTime() !== item.booking.start.getTime() ||
          prevItemsRef.current[index]?.booking.end.getTime() !== item.booking.end.getTime() ||
          prevItemsRef.current[index]?.booking.roomId !== item.booking.roomId,
      )

    if (!itemsChanged) return

    // Update the previous items ref
    prevItemsRef.current = [...items]

    // Set syncing flag to prevent loops
    isSyncingRef.current = true

    // Clear any existing timeout
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current)
    }

    // Use a debounced timeout to batch updates
    syncTimeoutRef.current = setTimeout(() => {
      // Get existing cart ghost IDs
      const existingCartGhostIds = ghostBookings
        .filter((ghost) => ghost.id.startsWith("ghost-cart-"))
        .map((ghost) => ghost.id)

      // Get current cart item IDs
      const currentCartItemIds = items.map((item) => `ghost-cart-${item.id}`)

      // Find ghost bookings to remove (exist in ghostBookings but not in items)
      const ghostsToRemove = existingCartGhostIds.filter((ghostId) => !currentCartItemIds.includes(ghostId))

      // Remove ghost bookings that don't have corresponding cart items
      ghostsToRemove.forEach((ghostId) => {
        deleteGhostBooking(ghostId)
      })

      // Add or update ghost bookings for each cart item
      items.forEach((item) => {
        const ghostId = `ghost-cart-${item.id}`
        const existingGhost = ghostBookings.find((ghost) => ghost.id === ghostId)

        // Ensure we have valid date objects
        const validStart =
          item.booking.start instanceof Date && !isNaN(item.booking.start.getTime()) ? item.booking.start : new Date()

        const validEnd =
          item.booking.end instanceof Date && !isNaN(item.booking.end.getTime())
            ? item.booking.end
            : new Date(validStart.getTime() + 3600000) // Default to 1 hour after start

        // Only add/update if the ghost doesn't exist or has changed
        if (
          !existingGhost ||
          existingGhost.start.getTime() !== validStart.getTime() ||
          existingGhost.end.getTime() !== validEnd.getTime() ||
          existingGhost.roomId !== item.booking.roomId
        ) {
          addGhostBooking({
            id: ghostId,
            title: `Cart: ${item.booking.title || `New Booking for ${item.room.name}`}`,
            start: validStart,
            end: validEnd,
            venueId: item.booking.venueId,
            roomId: item.booking.roomId,
            description: item.booking.description || "",
            isAllDay: item.booking.isAllDay || false,
            status: "pending",
          })
        }
      })

      // Reset syncing flag
      isSyncingRef.current = false
    }, 300) // Debounce for 300ms

    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current)
      }
    }
  }, [items, mounted, addGhostBooking, deleteGhostBooking, ghostBookings])

  // Handle booking confirmation
  const handleBookNow = async () => {
    if (items.length === 0) return

    setIsSubmitting(true)

    try {
      // In a real app, this would be an API call to create the bookings
      // For now, we'll just add them to the calendar context
      items.forEach((item) => {
        addBooking(item.booking)

        // Remove the ghost event for this cart item
        deleteGhostBooking(`ghost-cart-${item.id}`)
      })

      // Clear the cart after successful booking
      clearCart()

      // Show success message or notification
      alert("Bookings confirmed successfully!")
    } catch (error) {
      console.error("Error confirming bookings:", error)
      alert("Failed to confirm bookings. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle removing an item from the cart
  const handleRemoveItem = (itemId: string) => {
    // Remove the item from the cart
    removeItem(itemId)

    // Remove the ghost event for this cart item
    // This is now safe because we're not in the useEffect
    deleteGhostBooking(`ghost-cart-${itemId}`)
  }

  // Handle clearing the cart
  const handleClearCart = () => {
    // Get all ghost events associated with cart items
    const cartGhostIds = ghostBookings.filter((ghost) => ghost.id.startsWith("ghost-cart-")).map((ghost) => ghost.id)

    // Remove all ghost events associated with cart items
    cartGhostIds.forEach((ghostId) => {
      deleteGhostBooking(ghostId)
    })

    // Clear the cart
    clearCart()
  }

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current)
      }
    }
  }, [])

  // Don't render anything during SSR to prevent hydration errors
  if (!mounted) return null

  return (
    <div
      className={cn(
        "fixed right-0 top-0 z-40 h-screen w-80 transform bg-background shadow-xl transition-transform duration-300 ease-in-out",
        isOpen ? "translate-x-0" : "translate-x-full",
      )}
    >
      {/* Cart content */}
      <Card className="h-full border-l rounded-none">
        <CardHeader className="px-4 py-4 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Booking Cart
              {items.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {items.length} {items.length === 1 ? "item" : "items"}
                </Badge>
              )}
            </CardTitle>
            <Button variant="ghost" size="icon" onClick={() => setCartOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>
            {items.length > 0 ? "Review and confirm your bookings" : "Your booking cart is empty"}
          </CardDescription>
        </CardHeader>

        <ScrollArea className="h-[calc(100vh-13rem)]">
          <CardContent className="px-4 py-4">
            {items.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                <ShoppingCart className="h-12 w-12 mb-2 opacity-20" />
                <p>Drag on the calendar to add bookings</p>
              </div>
            ) : (
              <div className="space-y-4">
                {items.map((item) => (
                  <CartItemCard key={item.id} item={item} onRemove={() => handleRemoveItem(item.id)} />
                ))}
              </div>
            )}
          </CardContent>
        </ScrollArea>

        <CardFooter className="border-t p-4 flex flex-col gap-2">
          {items.length > 0 && (
            <>
              <div className="flex justify-between w-full text-sm mb-2">
                <span>Total bookings:</span>
                <span className="font-medium">{items.length}</span>
              </div>
              <Button className="w-full" onClick={handleBookNow} disabled={isSubmitting}>
                {isSubmitting ? "Processing..." : "Book Now"}
              </Button>
              <Button variant="outline" className="w-full" onClick={handleClearCart} disabled={isSubmitting}>
                Clear Cart
              </Button>
            </>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}

function CartItemCard({ item, onRemove }: { item: CartItem; onRemove: () => void }) {
  const [isHovered, setIsHovered] = useState(false)
  const [isRecentlyUpdated, setIsRecentlyUpdated] = useState(false)
  const { setCartOpen } = useBookingCart()
  const prevItemRef = useRef(item)

  // Add this useEffect to detect changes to the item
  useEffect(() => {
    // Check if this is an update (not the initial render)
    if (prevItemRef.current) {
      const prevStart = prevItemRef.current.booking.start.getTime()
      const prevEnd = prevItemRef.current.booking.end.getTime()
      const prevRoomId = prevItemRef.current.booking.roomId

      const currentStart = item.booking.start.getTime()
      const currentEnd = item.booking.end.getTime()
      const currentRoomId = item.booking.roomId

      // If any of these properties changed, mark as recently updated
      if (prevStart !== currentStart || prevEnd !== currentEnd || prevRoomId !== currentRoomId) {
        setIsRecentlyUpdated(true)

        // Reset the indicator after 3 seconds
        const timer = setTimeout(() => {
          setIsRecentlyUpdated(false)
        }, 3000)

        return () => clearTimeout(timer)
      }
    }

    // Update the ref
    prevItemRef.current = item
  }, [item])

  // Ensure we have valid date objects for formatting
  const start =
    item.booking.start instanceof Date && !isNaN(item.booking.start.getTime()) ? item.booking.start : new Date()

  const end =
    item.booking.end instanceof Date && !isNaN(item.booking.end.getTime())
      ? item.booking.end
      : new Date(start.getTime() + 3600000) // Default to 1 hour after start

  const addedAt = item.addedAt instanceof Date && !isNaN(item.addedAt.getTime()) ? item.addedAt : new Date()

  return (
    <div
      className={`border rounded-md p-3 relative transition-all duration-200 hover:shadow-md ${
        isRecentlyUpdated ? "border-green-500 bg-green-50 dark:bg-green-900/20" : ""
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {isRecentlyUpdated && (
        <div className="absolute -top-2 right-2 bg-green-500 text-white text-xs px-2 py-0.5 rounded-full">Updated</div>
      )}

      <div className="absolute top-2 right-2 flex gap-1">
        <Button
          variant="ghost"
          size="icon"
          className={`h-6 w-6 ${isHovered ? "opacity-100" : "opacity-0"} transition-opacity duration-200`}
          onClick={() => {
            // This would open the booking modal with the cart item
            // We'll use the ghost event click handler in the calendar
            const ghostId = `ghost-cart-${item.id}`

            // Find the ghost event in the calendar and simulate a click
            const ghostEvent = document.querySelector(`[data-event-id="${ghostId}"]`)
            if (ghostEvent) {
              ghostEvent.dispatchEvent(new MouseEvent("click", { bubbles: true }))
              // Close the cart
              setCartOpen(false)
            }
          }}
        >
          <Edit className="h-4 w-4 text-muted-foreground" />
        </Button>
        <Button variant="ghost" size="icon" className="h-6 w-6" onClick={onRemove}>
          <Trash2 className="h-4 w-4 text-muted-foreground" />
        </Button>
      </div>

      <h4 className="font-medium mb-2 pr-12">{item.booking.title || "Untitled Booking"}</h4>

      <div className="text-sm space-y-1 text-muted-foreground">
        <div className="flex items-start gap-2">
          <Calendar className="h-4 w-4 mt-0.5 flex-shrink-0" />
          <span>
            {format(start, "MMM d, yyyy")}
            {!isSameDay(start, end) ? ` - ${format(end, "MMM d, yyyy")}` : ""}
          </span>
        </div>

        <div className="flex items-start gap-2">
          <Clock className="h-4 w-4 mt-0.5 flex-shrink-0" />
          <span>
            {format(start, "h:mm a")} - {format(end, "h:mm a")}
          </span>
        </div>

        <div className="flex items-start gap-2">
          <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0" />
          <span>{item.room.name}</span>
        </div>
      </div>

      <div className="mt-2 text-xs text-muted-foreground">Added {format(addedAt, "MMM d, h:mm a")}</div>
    </div>
  )
}

