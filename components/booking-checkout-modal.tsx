"use client"

import { useState, ReactElement } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { DateTimePicker } from "@/components/ui/date-time-picker"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { format } from "date-fns"
import { useToast } from "@/components/ui/use-toast"
import { useBooking<PERSON>art, type CartItem } from "@/store/booking-cart"
import { useCalendar } from "@/context/calendar-context"
import {
  CalendarPlus,
  Edit,
  Check,
  Trash2,
  CalendarCheck,
  Clock,
  Building,
  RefreshCw,
  X,
  Info
} from "lucide-react"

interface BookingCheckoutModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProcessed?: () => void
}

export function BookingCheckoutModal({
  open,
  onOpenChange,
  onProcessed
}: BookingCheckoutModalProps): ReactElement {
  const router = useRouter()
  const { items, updateItem, removeItem, clearCart } = useBookingCart()
  const { clearGhostBookings, convertGhostBookingToReal } = useCalendar()
  const { toast } = useToast()
  
  const [processingCheckout, setProcessingCheckout] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState<string>("")
  const [editingItemId, setEditingItemId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string>("event")
  
  // Event details form state
  const [eventName, setEventName] = useState("New Event")
  const [eventType, setEventType] = useState<string[]>([])
  const [account, setAccount] = useState<string>("")
  const [contact, setContact] = useState<string>("")
  const [eventCategory, setEventCategory] = useState<string>("")
  const [businessClassification, setBusinessClassification] = useState<string>("")
  const [hideEventDetails, setHideEventDetails] = useState(false)
  const [showNameOnAvails, setShowNameOnAvails] = useState(false)

  // Sample CS Events data - would come from API in a real implementation
  const sampleEvents = [
    { id: "evt1", name: "Annual Conference 2025" },
    { id: "evt2", name: "Product Launch Q2" },
    { id: "evt3", name: "Executive Retreat" },
  ]
  
  // Sample account data
  const sampleAccounts = [
    { id: "acc1", name: "Acme Corporation" },
    { id: "acc2", name: "Globex Industries" },
    { id: "acc3", name: "Stark Enterprises" },
  ]
  
  // Sample contact data
  const sampleContacts = [
    { id: "con1", name: "John Smith" },
    { id: "con2", name: "Jane Doe" },
    { id: "con3", name: "Alex Johnson" },
  ]
  
  // Sample event types
  const sampleEventTypes = [
    { id: "et1", name: "Conference" },
    { id: "et2", name: "Meeting" },
    { id: "et3", name: "Workshop" },
  ]
  
  // Sample business classifications
  const sampleBusinessClassifications = [
    { id: "bc1", name: "Corporate" },
    { id: "bc2", name: "Non-profit" },
    { id: "bc3", name: "Government" },
  ]

  // Calculate booking summary data
  const totalBookings = items.length
  
  // Calculate booking total - in a real app this would have complex pricing logic
  const totalHours = items.reduce((acc, item) => {
    const duration = new Date(item.booking.end).getTime() - new Date(item.booking.start).getTime()
    return acc + (duration / (1000 * 60 * 60))
  }, 0)

  // Handle editing a booking
  const handleEditBooking = (item: CartItem, field: string, value: any) => {
    const updatedItem = { ...item }

    switch (field) {
      case "title":
        updatedItem.booking = { ...updatedItem.booking, title: value }
        break
      case "description":
        updatedItem.booking = { ...updatedItem.booking, description: value }
        break  
      case "start":
        updatedItem.booking = { ...updatedItem.booking, start: value }
        break
      case "end":
        updatedItem.booking = { ...updatedItem.booking, end: value }
        break
      case "status":
        updatedItem.booking = { ...updatedItem.booking, status: value }
        break
    }

    updateItem(item.id, updatedItem)
  }

  // Handle the checkout process
  const handleCheckout = async () => {
    if (items.length === 0) {
      toast({
        title: "Your cart is empty",
        description: "Add bookings to your cart before proceeding to checkout.",
        variant: "destructive",
      })
      return
    }

    setProcessingCheckout(true)
    
    try {
      // Simulate API call to process booking
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Prepare bookings with event assignment if selected
      const bookingsToProcess = items.map(item => {
        let updatedBooking = { ...item.booking }
        
        // If a valid event is selected, assign it to all bookings
        if (selectedEvent && selectedEvent !== "new-event") {
          updatedBooking.needsEventAssignment = false
          updatedBooking.csEventId = selectedEvent
        } else {
          // We're creating a new event
          updatedBooking.needsEventAssignment = false
          // In a real implementation, we would create a new event with the form details and get its ID
          updatedBooking.csEventId = "new-event-id"
          console.log("Creating new event:", {
            name: eventName,
            eventType: eventType,
            account: account,
            contact: contact,
            eventCategory: eventCategory,
            businessClassification: businessClassification,
            hideEventDetails: hideEventDetails,
            showNameOnAvails: showNameOnAvails
          })
        }
        
        return { ...item, booking: updatedBooking }
      })
      
      // Process bookings with the associated event
      console.log("Processing bookings with event details:", {
        bookings: bookingsToProcess,
        eventDetails: {
          name: eventName,
          eventType: eventType,
          account: account,
          contact: contact,
          eventCategory: eventCategory,
          businessClassification: businessClassification,
          hideEventDetails: hideEventDetails,
          showNameOnAvails: showNameOnAvails,
        },
      })

      // Convert all cart items to real bookings on the calendar
      items.forEach(item => {
        // If this booking has a source ghost booking ID (from when it was added to cart)
        if ('ghostBookingId' in item.booking && item.booking.ghostBookingId) {
          convertGhostBookingToReal(item.booking.ghostBookingId);
        }
      })

      // In a real implementation, this would call NetSuite APIs to create the bookings
      console.log("Processing bookings:", bookingsToProcess)
      
      // Clear the cart after successfully processing bookings
      clearCart()
      
      // Show success toast
      toast({
        title: "Bookings Confirmed",
        description: `Successfully processed ${items.length} bookings.`,
      })
      
      // Close the modal
      onOpenChange(false)
      
      // Call the onProcessed callback if provided
      if (onProcessed) {
        onProcessed()
      }
    } catch (error) {
      console.error("Error processing checkout:", error)
      toast({
        title: "Checkout Failed",
        description: "There was a problem processing your bookings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setProcessingCheckout(false)
    }
  }



  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-2xl">Confirm Your Bookings</DialogTitle>
          <DialogDescription>
            Review and finalize your bookings
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex flex-col h-full">
            <TabsList className="w-full grid grid-cols-2 mb-4">
              <TabsTrigger value="event" className="flex items-center gap-1">
                <CalendarCheck className="h-4 w-4" />
                Event Details
              </TabsTrigger>
              <TabsTrigger value="bookings" className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                Bookings
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="bookings" className="flex-1 overflow-hidden">
              <div className="space-y-4">
                <ScrollArea className="h-[350px] rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Room</TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Time</TableHead>
                        <TableHead>Duration</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {items.map((item) => {
                        const isEditing = editingItemId === item.id
                        const start = new Date(item.booking.start)
                        const end = new Date(item.booking.end)
                        const durationMs = end.getTime() - start.getTime()
                        const durationHours = durationMs / (1000 * 60 * 60)
                        
                        return (
                          <TableRow key={item.id} className={isEditing ? "bg-blue-50" : ""}>
                            <TableCell className="font-medium">{item.room.name}</TableCell>
                            <TableCell>
                              {isEditing ? (
                                <Input 
                                  className="h-8 w-full"
                                  value={item.booking.title} 
                                  onChange={(e) => handleEditBooking(item, "title", e.target.value)}
                                />
                              ) : (
                                <div className="flex items-center space-x-2">
                                  <span>{item.booking.title}</span>
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              {isEditing ? (
                                <DateTimePicker 
                                  value={new Date(item.booking.start)} 
                                  onChange={(date: Date) => handleEditBooking(item, "start", date)}
                                />
                              ) : (
                                format(start, "MMM d, yyyy")
                              )}
                            </TableCell>
                            <TableCell>
                              {isEditing ? (
                                <DateTimePicker 
                                  value={new Date(item.booking.end)} 
                                  onChange={(date: Date) => handleEditBooking(item, "end", date)}
                                />
                              ) : (
                                <>
                                  {format(start, "h:mm a")} - {format(end, "h:mm a")}
                                </>
                              )}
                            </TableCell>
                            <TableCell>{durationHours.toFixed(1)} hours</TableCell>
                            <TableCell>
                              <div className="flex space-x-1">
                                <Button 
                                  size="sm" 
                                  variant="ghost" 
                                  onClick={() => setEditingItemId(isEditing ? null : item.id)}
                                  className="h-8 w-8 p-0"
                                >
                                  {isEditing ? <Check className="h-4 w-4 text-green-600" /> : <Edit className="h-4 w-4" />}
                                </Button>
                                <Button 
                                  size="sm" 
                                  variant="ghost" 
                                  onClick={() => removeItem(item.id)}
                                  className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                </ScrollArea>
                
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="flex flex-col space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Total Bookings:</span>
                      <span className="font-medium">{totalBookings}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Total Hours:</span>
                      <span className="font-medium">{totalHours.toFixed(1)} hours</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="event" className="flex-1 overflow-hidden">
              <div className="space-y-4">
                <div className="rounded-lg border p-4 bg-blue-50/50 border-blue-200">
                  <div className="flex gap-2 text-blue-800">
                    <Info className="h-5 w-5 text-blue-500 shrink-0" />
                    <div className="text-sm">
                      <p>Create a new CS Event for your bookings. All selected bookings will be assigned to this event.</p>
                    </div>
                  </div>
                </div>
                
                <ScrollArea className="h-[350px] px-1">
                  <div className="space-y-5">
                    {/* Event Name */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Name</label>
                      <Input 
                        value={eventName} 
                        onChange={(e) => setEventName(e.target.value)} 
                        placeholder="Event name"
                      />
                    </div>
                    
                    {/* Event Type Checkboxes */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <input 
                          type="checkbox" 
                          id="event-type-inquiry"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={eventType.includes('inquiry')}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEventType([...eventType, 'inquiry'])
                            } else {
                              setEventType(eventType.filter(t => t !== 'inquiry'))
                            }
                          }}
                        />
                        <label htmlFor="event-type-inquiry" className="text-sm">
                          Inquiry (No spaces will be booked)
                        </label>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <input 
                          type="checkbox" 
                          id="event-type-prospect"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={eventType.includes('prospect')}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEventType([...eventType, 'prospect'])
                            } else {
                              setEventType(eventType.filter(t => t !== 'prospect'))
                            }
                          }}
                        />
                        <label htmlFor="event-type-prospect" className="text-sm">
                          Prospect
                        </label>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <input 
                          type="checkbox" 
                          id="event-type-internal"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={eventType.includes('internal')}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEventType([...eventType, 'internal'])
                            } else {
                              setEventType(eventType.filter(t => t !== 'internal'))
                            }
                          }}
                        />
                        <label htmlFor="event-type-internal" className="text-sm">
                          Internal Event
                        </label>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <input 
                          type="checkbox" 
                          id="event-type-blackout"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={eventType.includes('blackout')}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setEventType([...eventType, 'blackout'])
                            } else {
                              setEventType(eventType.filter(t => t !== 'blackout'))
                            }
                          }}
                        />
                        <label htmlFor="event-type-blackout" className="text-sm">
                          Blackout
                        </label>
                      </div>
                    </div>
                    
                    {/* Account Dropdown */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Account</label>
                      <Select value={account} onValueChange={setAccount}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an account" />
                        </SelectTrigger>
                        <SelectContent>
                          {sampleAccounts.map((acc) => (
                            <SelectItem key={acc.id} value={acc.id}>
                              {acc.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {/* Contact Dropdown */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Contact</label>
                      <Select value={contact} onValueChange={setContact}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a contact" />
                        </SelectTrigger>
                        <SelectContent>
                          {sampleContacts.map((con) => (
                            <SelectItem key={con.id} value={con.id}>
                              {con.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {/* Event Type Dropdown */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Event Type</label>
                      <Select value={eventCategory} onValueChange={setEventCategory}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an event type" />
                        </SelectTrigger>
                        <SelectContent>
                          {sampleEventTypes.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              {type.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {/* Business Classification */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Business Classification</label>
                      <Select value={businessClassification} onValueChange={setBusinessClassification}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a classification" />
                        </SelectTrigger>
                        <SelectContent>
                          {sampleBusinessClassifications.map((bc) => (
                            <SelectItem key={bc.id} value={bc.id}>
                              {bc.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {/* Visibility */}
                    <div className="space-y-3">
                      <label className="text-sm font-medium">Visibility</label>
                      <div className="flex items-center gap-2">
                        <input 
                          type="checkbox" 
                          id="hide-event-details"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={hideEventDetails}
                          onChange={(e) => setHideEventDetails(e.target.checked)}
                        />
                        <label htmlFor="hide-event-details" className="text-sm">
                          Hide event details
                        </label>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <input 
                          type="checkbox" 
                          id="show-name-on-avails"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={showNameOnAvails}
                          onChange={(e) => setShowNameOnAvails(e.target.checked)}
                        />
                        <label htmlFor="show-name-on-avails" className="text-sm">
                          Show name on avails (if confirmed)
                        </label>
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        
        <Separator className="my-4" />
        
        <DialogFooter className="sm:justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button 
            onClick={handleCheckout}
            className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700"
            disabled={processingCheckout || items.length === 0}
          >
            {processingCheckout ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                Complete Booking
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
