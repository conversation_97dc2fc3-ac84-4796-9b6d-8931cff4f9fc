"use client"

import type React from "react"

import { Calendar } from "@/components/ui/calendar"
import { cn } from "../lib/utils"

export type MiniCalendarProps = React.ComponentProps<typeof Calendar> & {
  className?: string
}

export function MiniCalendar({ className, classNames, ...props }: MiniCalendarProps) {
  return (
    <div className="w-full">
      <Calendar
        className={cn("w-full", className)}
        classNames={{
          month: "space-y-2 w-full",
          caption: "flex justify-center relative items-center h-9",
          caption_label: "text-sm font-medium",
          nav: "flex items-center gap-1",
          nav_button_previous: "absolute left-1",
          nav_button_next: "absolute right-1",
          table: "w-full border-collapse",
          head_row: "flex w-full",
          head_cell: "text-base-content/60 w-9 font-normal text-[0.8rem] flex items-center justify-center",
          row: "flex w-full mt-1",
          cell: cn(
            "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 w-9",
            "[&:has([aria-selected])]:bg-base-200",
            "[&:has([aria-selected].day-outside)]:bg-base-200/50",
            "[&:has([aria-selected].day-range-end)]:rounded-r-md",
            "[&:has([aria-selected].day-range-start)]:rounded-l-md",
          ),
          day: cn(
            "h-8 w-8 p-0 font-normal aria-selected:opacity-100 hover:bg-base-200 rounded-full flex items-center justify-center mx-auto",
          ),
          day_selected: "bg-primary text-primary-content hover:bg-primary hover:text-primary-content",
          day_today: "border border-primary/50 text-base-content",
          day_outside: "text-base-content/50 opacity-50",
          day_disabled: "text-base-content/30",
          day_hidden: "invisible",
          ...classNames,
        }}
        {...props}
      />
    </div>
  )
}

