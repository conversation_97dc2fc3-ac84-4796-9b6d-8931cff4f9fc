"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { useCalendar } from "@/context/calendar-context"
import FullCalendar from "@fullcalendar/react"
import dayGridPlugin from "@fullcalendar/daygrid"
import timeGridPlugin from "@fullcalendar/timegrid"
import interactionPlugin from "@fullcalendar/interaction"
import resourceTimelinePlugin from "@fullcalendar/resource-timeline"
import listPlugin from "@fullcalendar/list"
import multiMonthPlugin from "@fullcalendar/multimonth"
import type {
  EventInput,
  DateSelectArg,
  EventClickArg,
  EventChangeArg,
  ViewDidMountArg,
  DatesSetArg,
} from "@fullcalendar/core"
import type { Booking, ResourceItem, TimeSlotInterval } from "@/types/calendar"
import { BookingModal } from "./booking-modal"
import { useBookingCart } from "@/store/booking-cart"
import { useViewPreferences } from "@/store/view-preferences"
import { v4 as uuidv4 } from "uuid"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { CalendarIcon, ChevronLeft, ChevronRight, Filter, SlidersHorizontal, ShoppingCart } from "lucide-react"
import { TimelineToolbar, type TimelineDuration } from "./timeline-toolbar"
import { Button } from "@/components/ui/button"
import {
  getContrastColor,
  getFullCalendarViewType,
  getAppViewType,
  getViewSpecificSlotConfig,
  enhanceGridLines,
  formatDateRangeForTimeline,
  formatHeaderDate,
  getTimelineDurationFromViewType,
  createEventTooltip,
} from "@/utils/calendar-utils"

// Add the import for the toast manager at the top of the file
import { showToast } from "./toast-manager"

// Add this at the top of the file, after imports
declare global {
  interface Window {
    disableGhostUpdates?: boolean
  }
}

// Initialize the flag
if (typeof window !== "undefined") {
  window.disableGhostUpdates = false
}

export function FullCalendarWrapper() {
  const {
    currentDate,
    view,
    setCurrentDate,
    setView,
    filteredBookings,
    ghostBookings,
    rooms,
    addBooking,
    updateBooking,
    deleteBooking,
    venues,
    nextPeriod,
    prevPeriod,
    updateGhostBooking,
    addGhostBooking,
    deleteGhostBooking,
    convertGhostBookingToReal,
  } = useCalendar()

  const { addItem } = useBookingCart()

  // Get persisted view preferences
  const {
    timelineDuration: persistedTimelineDuration,
    setTimelineDuration: persistTimelineDuration,
    timeSlotInterval: persistedTimeSlotInterval,
    setTimeSlotInterval: persistTimelineSlotInterval,
  } = useViewPreferences()

  const calendarRef = useRef<FullCalendar>(null)
  const [resources, setResources] = useState<ResourceItem[]>([])

  // Use refs to track previous values and prevent infinite loops
  const prevViewRef = useRef(view)
  const prevDateRef = useRef(currentDate)
  const isInternalUpdateRef = useRef(false)
  const initialRenderRef = useRef(true)

  // Modal state
  const [modalOpen, setModalOpen] = useState(false)
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null)
  const [selectedDates, setSelectedDates] = useState<{ start: Date; end: Date } | null>(null)
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null)
  const [selectedVenueId, setSelectedVenueId] = useState<string | null>(null)

  // Cart mode state
  const [isCartMode, setIsCartMode] = useState(false)

  // Timeline duration state - initialize from persisted value
  const [timelineDuration, setTimelineDuration] = useState<TimelineDuration>(persistedTimelineDuration)

  // Time slot interval state - initialize from persisted value
  const [timeSlotInterval, setTimeSlotInterval] = useState<TimeSlotInterval>(persistedTimeSlotInterval)

  // Handle timeline duration change
  const handleTimelineDurationChange = (duration: TimelineDuration) => {
    setTimelineDuration(duration)

    // Persist the timeline duration preference
    persistTimelineDuration(duration)

    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi()

      // Map our duration values to FullCalendar view names
      const viewName = getFullCalendarViewType("timeline", duration)

      // Change the view
      calendarApi.changeView(viewName)
    }
  }

  // Handle time slot interval change
  const handleTimeSlotIntervalChange = (interval: TimeSlotInterval) => {
    setTimeSlotInterval(interval)

    // Persist the time slot interval preference
    persistTimelineSlotInterval(interval)

    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi()

      // Refresh the view to apply the new time slot interval
      const currentView = calendarApi.view.type
      calendarApi.changeView(currentView)

      // Apply grid lines after view change
      setTimeout(() => {
        const calendarElement = calendarRef.current?.elRef?.current as HTMLElement
        enhanceGridLines(calendarElement, view, interval)
      }, 100)
    }
  }

  // Convert bookings to FullCalendar events with Google Calendar-like styling
  const events: EventInput[] = filteredBookings.map((booking: Booking) => {
    // Get room and venue for color
    const room = rooms.find((r) => r.id === booking.roomId)
    const venue = venues.find((v) => v.id === booking.venueId)
    const color = room?.color || venue?.color || "#4285F4"

    return {
      id: booking.id,
      title: booking.title,
      start: booking.start,
      end: booking.end,
      resourceId: booking.roomId,
      allDay: booking.isAllDay,
      extendedProps: {
        description: booking.description,
        venueId: booking.venueId,
        roomId: booking.roomId,
        isGhost: false, // Mark as a real event
      },
      backgroundColor: color,
      borderColor: color,
      textColor: getContrastColor(color),
      classNames: ["google-calendar-event"],
    }
  })

  // Convert ghost bookings to FullCalendar events with distinct styling
  const ghostEvents: EventInput[] = ghostBookings.map((booking) => {
    // Get room and venue for color
    const room = rooms.find((r) => r.id === booking.roomId)
    const venue = venues.find((v) => v.id === booking.venueId)
    const baseColor = room?.color || venue?.color || "#4285F4"

    // Create a semi-transparent version of the color
    const rgbColor = hexToRgb(baseColor)
    const ghostColor = rgbColor ? `rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, 0.5)` : baseColor

    // Determine if this is a cart-related ghost event
    const isCartGhost = booking.id.startsWith("ghost-cart-")
    const isModalGhost = booking.id.startsWith("ghost-modal-")

    // Set title based on cart mode and ghost type
    let title = booking.title || "New Booking"
    if (isCartMode && !title.includes("for") && room) {
      title = `New Booking for ${room.name}`
    }

    // Add visual indicator for cart items
    if (isCartGhost) {
      title = title.startsWith("Cart:") ? title : `Cart: ${title}`
    }

    return {
      id: booking.id,
      title: title,
      start: booking.start,
      end: booking.end,
      resourceId: booking.roomId,
      allDay: booking.isAllDay,
      extendedProps: {
        description: booking.description,
        venueId: booking.venueId,
        roomId: booking.roomId,
        isGhost: true, // Mark as a ghost event
        status: booking.status,
        isCartGhost: isCartGhost,
        isModalGhost: isModalGhost,
      },
      backgroundColor: ghostColor,
      borderColor: baseColor,
      textColor: getContrastColor(baseColor),
      classNames: [
        "ghost-event",
        `ghost-event-${booking.status}`,
        isCartGhost ? "cart-ghost-event" : "modal-ghost-event",
      ],
      durationEditable: true,
      startEditable: true,
    }
  })

  // Combine real and ghost events
  const allEvents = [...events, ...ghostEvents]

  // Helper function to convert hex color to RGB
  function hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result
      ? {
          r: Number.parseInt(result[1], 16),
          g: Number.parseInt(result[2], 16),
          b: Number.parseInt(result[3], 16),
        }
      : null
  }

  // Create flat list of room resources with venue information for grouping
  useEffect(() => {
    // Create a flat list of room resources with venue information
    const roomResources: ResourceItem[] = []

    venues
      .filter((venue) => venue.isSelected)
      .forEach((venue) => {
        // Get all rooms for this venue
        const venueRooms = rooms.filter((room) => room.venueId === venue.id && room.isSelected)

        // Add each room as a resource with venue information
        venueRooms.forEach((room) => {
          roomResources.push({
            id: room.id,
            title: room.name,
            venueName: venue.name, // This will be used for grouping
            venueId: venue.id,
            eventColor: room.color,
            businessHours: {
              startTime: "08:00",
              endTime: "20:00",
              daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
            },
          })
        })
      })

    setResources(roomResources)
  }, [venues, rooms])

  // Sync FullCalendar view with our app state
  useEffect(() => {
    // Skip if this is an internal update triggered by FullCalendar
    if (isInternalUpdateRef.current) {
      isInternalUpdateRef.current = false
      return
    }

    if (!calendarRef.current) return

    const calendarApi = calendarRef.current.getApi()
    const currentFcView = calendarApi.view.type
    const targetFcView = getFullCalendarViewType(view, timelineDuration)

    // Only change the view if it's actually different
    if (currentFcView !== targetFcView) {
      console.log(`Changing view from ${currentFcView} to ${targetFcView}`)
      calendarApi.changeView(targetFcView)
    }

    // Always update the date to ensure consistency
    calendarApi.gotoDate(currentDate)

    // Update our refs
    prevViewRef.current = view
    prevDateRef.current = currentDate

    // Clear the initial render flag
    initialRenderRef.current = false

    // Apply grid lines after view change
    setTimeout(() => {
      const calendarElement = calendarRef.current?.elRef?.current as HTMLElement
      enhanceGridLines(calendarElement, view, timeSlotInterval)
    }, 100)
  }, [view, currentDate, timelineDuration])

  // Handle date selection (for creating new events)
  const handleDateSelect = (selectInfo: DateSelectArg) => {
    // Ensure we have valid Date objects
    if (
      !selectInfo.start ||
      !selectInfo.end ||
      !(selectInfo.start instanceof Date) ||
      !(selectInfo.end instanceof Date)
    ) {
      console.error("Invalid date objects in selection", selectInfo)
      return
    }

    // Check if the selection is on a resource
    const resource = selectInfo.resource

    if (resource) {
      // Get the room ID and venue ID from the resource
      const roomId = resource.id
      const venueId = resource.extendedProps?.venueId
      const room = rooms.find((r) => r.id === roomId)

      if (isCartMode && room) {
        // In cart mode, create a temporary booking and add it to the cart
        const cartItemId = uuidv4()
        const newBooking: Booking = {
          id: cartItemId,
          title: `New Booking - ${room.name}`,
          start: new Date(selectInfo.start),
          end: new Date(selectInfo.end),
          venueId: venueId || room.venueId,
          roomId: roomId,
          description: "",
          isAllDay: selectInfo.allDay,
        }

        // Add to cart
        addItem({
          id: cartItemId,
          booking: newBooking,
          room,
          addedAt: new Date(),
        })

        // The cart component will handle creating the ghost event
      } else {
        // Regular mode - open modal with ghost event
        setSelectedRoomId(roomId)
        setSelectedVenueId(venueId)
        setSelectedDates({
          start: new Date(selectInfo.start),
          end: new Date(selectInfo.end),
        })
        setSelectedBooking(null)
        setModalOpen(true)
      }
    } else {
      // For non-resource views (day, week, month), just open the modal
      // The user will need to select a room in the modal
      setSelectedRoomId(null)
      setSelectedVenueId(null)
      setSelectedDates({
        start: new Date(selectInfo.start),
        end: new Date(selectInfo.end),
      })
      setSelectedBooking(null)
      setModalOpen(true)
    }

    // Clear selection
    if (calendarRef.current) {
      calendarRef.current.getApi().unselect()
    }
  }

  // Add a ref to prevent immediate updates when the modal opens
  const isUpdatingRef = useRef(false)

  // Handle event click
  const handleEventClick = (clickInfo: EventClickArg) => {
    // Check if this is a ghost event
    const isGhost = clickInfo.event.extendedProps.isGhost
    const isCartGhost = clickInfo.event.extendedProps.isCartGhost
    const isModalGhost = clickInfo.event.extendedProps.isModalGhost

    if (isGhost) {
      // For ghost events, we'll open the booking modal with the ghost event data
      const ghostId = clickInfo.event.id
      const ghostBooking = ghostBookings.find((b) => b.id === ghostId)

      if (ghostBooking) {
        // Create a deep copy of the ghost booking to prevent reference issues
        const ghostBookingCopy = JSON.parse(JSON.stringify(ghostBooking))

        // Convert string dates back to Date objects
        ghostBookingCopy.start = new Date(ghostBookingCopy.start)
        ghostBookingCopy.end = new Date(ghostBookingCopy.end)

        // If it's a cart ghost, open the cart first
        if (isCartGhost) {
          // Open the cart if it's not already open
          if (!isCartMode) {
            setIsCartMode(true)
          }
        }

        // Temporarily disable ghost updates to prevent loops
        window.disableGhostUpdates = true

        setSelectedBooking(ghostBookingCopy)
        setSelectedDates(null)
        setSelectedRoomId(null)
        setSelectedVenueId(null)
        setModalOpen(true)

        // Re-enable ghost updates after a delay
        setTimeout(() => {
          window.disableGhostUpdates = false
        }, 1000)
      }
      return
    }

    // For regular events, proceed as normal
    const booking = filteredBookings.find((b) => b.id === clickInfo.event.id)
    if (booking) {
      // Create a deep copy of the booking to prevent reference issues
      const bookingCopy = JSON.parse(JSON.stringify(booking))

      // Convert string dates back to Date objects
      bookingCopy.start = new Date(bookingCopy.start)
      bookingCopy.end = new Date(bookingCopy.end)

      setSelectedBooking(bookingCopy)
      setSelectedDates(null)
      setSelectedRoomId(null)
      setSelectedVenueId(null)
      setModalOpen(true)
    }
  }

  // Handle event changes (drag, resize)
  const handleEventChange = (changeInfo: EventChangeArg) => {
    // Check if this is a ghost event
    const isGhost = changeInfo.event.extendedProps.isGhost
    const isCartGhost = changeInfo.event.extendedProps.isCartGhost
    const isModalGhost = changeInfo.event.extendedProps.isModalGhost

    if (isGhost) {
      // For ghost events, update the ghost booking
      const ghostId = changeInfo.event.id
      const ghostBooking = ghostBookings.find((b) => b.id === ghostId)

      if (ghostBooking) {
        const resources = changeInfo.event.getResources()
        const resourceId = resources.length > 0 ? resources[0].id : null

        // Ensure we have valid start and end dates
        const eventStart = changeInfo.event.start
        const eventEnd = changeInfo.event.end

        if (!eventStart || !(eventStart instanceof Date)) {
          console.error("Invalid start date in event change", eventStart)
          return
        }

        if (resourceId) {
          // Get venue ID from the room's extended props
          const resource = resources[0]
          const venueId = resource.extendedProps?.venueId
          const room = rooms.find((r) => r.id === resourceId)

          const updatedGhostBooking = {
            ...ghostBooking,
            start: new Date(eventStart),
            end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
            roomId: resourceId,
            venueId: venueId || ghostBooking.venueId,
          }

          // Update the ghost booking
          updateGhostBooking(updatedGhostBooking)

          // If this is a cart ghost, we need to update the cart item too
          if (isCartGhost && room) {
            // Extract the cart item ID from the ghost ID
            const cartItemId = ghostId.replace("ghost-cart-", "")

            // Import the updateCartItem function from the store
            const { updateItem } = useBookingCart.getState()

            // Get the current cart items
            const { items } = useBookingCart.getState()

            // Find the cart item
            const cartItem = items.find((item) => item.id === cartItemId)

            if (cartItem) {
              // Create updated cart item
              const updatedCartItem = {
                ...cartItem,
                booking: {
                  ...cartItem.booking,
                  start: new Date(eventStart),
                  end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
                  roomId: resourceId,
                  venueId: venueId || cartItem.booking.venueId,
                },
                room: room,
              }

              // Update the cart item
              updateItem(cartItemId, updatedCartItem)

              // Show feedback toast or notification
              showToast(`Booking updated: ${updatedCartItem.booking.title || "Untitled Booking"}`)

              // Add visual feedback
              const eventEl = changeInfo.event.el
              if (eventEl) {
                eventEl.classList.add("cart-update-flash")
                setTimeout(() => {
                  eventEl.classList.remove("cart-update-flash")
                }, 1000)
              }
            }
          }
        } else {
          // If no resource (e.g., in day/week view), just update times
          const updatedGhostBooking = {
            ...ghostBooking,
            start: new Date(eventStart),
            end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
          }

          // Update the ghost booking
          updateGhostBooking(updatedGhostBooking)

          // If this is a cart ghost, update the cart item too
          if (isCartGhost) {
            // Extract the cart item ID from the ghost ID
            const cartItemId = ghostId.replace("ghost-cart-", "")

            // Import the updateCartItem function from the store
            const { updateItem } = useBookingCart.getState()

            // Get the current cart items
            const { items } = useBookingCart.getState()

            // Find the cart item
            const cartItem = items.find((item) => item.id === cartItemId)

            if (cartItem) {
              // Create updated cart item
              const updatedCartItem = {
                ...cartItem,
                booking: {
                  ...cartItem.booking,
                  start: new Date(eventStart),
                  end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000),
                },
              }

              // Update the cart item
              updateItem(cartItemId, updatedCartItem)

              // Show feedback toast or notification
              showToast(`Booking updated: ${updatedCartItem.booking.title || "Untitled Booking"}`)

              // Add visual feedback
              const eventEl = changeInfo.event.el
              if (eventEl) {
                eventEl.classList.add("cart-update-flash")
                setTimeout(() => {
                  eventEl.classList.remove("cart-update-flash")
                }, 1000)
              }
            }
          }
        }
      }
      return
    }

    // For regular events, proceed as normal
    const booking = filteredBookings.find((b) => b.id === changeInfo.event.id)

    if (booking) {
      const resources = changeInfo.event.getResources()
      const resourceId = resources.length > 0 ? resources[0].id : null

      // Ensure we have valid start and end dates
      const eventStart = changeInfo.event.start
      const eventEnd = changeInfo.event.end

      if (!eventStart || !(eventStart instanceof Date)) {
        console.error("Invalid start date in event change", eventStart)
        return
      }

      if (resourceId) {
        // Get venue ID from the room's extended props
        const resource = resources[0]
        const venueId = resource.extendedProps?.venueId

        const updatedBooking: Booking = {
          ...booking,
          start: new Date(eventStart),
          end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000), // Default to 1 hour if no end
          roomId: resourceId,
          venueId: venueId || booking.venueId,
        }

        updateBooking(updatedBooking)
      } else {
        // If no resource (e.g., in day/week view), just update times
        const updatedBooking: Booking = {
          ...booking,
          start: new Date(eventStart),
          end: eventEnd ? new Date(eventEnd) : new Date(eventStart.getTime() + 3600000), // Default to 1 hour if no end
        }

        updateBooking(updatedBooking)
      }
    }
  }

  // Handle view change from FullCalendar
  const handleViewChange = useCallback(
    (info: ViewDidMountArg) => {
      const newAppView = getAppViewType(info.view.type)

      // Only update if the view has actually changed
      if (newAppView !== prevViewRef.current) {
        console.log(`View changed to ${newAppView} (from ${info.view.type})`)

        // Set the flag to indicate this is an internal update
        isInternalUpdateRef.current = true

        // Update the app state
        setView(newAppView)

        // Update our ref
        prevViewRef.current = newAppView

        // Apply grid lines after view change
        setTimeout(() => {
          const calendarElement = calendarRef.current?.elRef?.current as HTMLElement
          enhanceGridLines(calendarElement, newAppView, timeSlotInterval)
        }, 100)
      }

      // If we're in timeline view, also update the timeline duration based on the FullCalendar view
      if (newAppView === "timeline" && info.view.type.startsWith("resourceTimeline")) {
        const newDuration = getTimelineDurationFromViewType(info.view.type)

        if (newDuration !== timelineDuration) {
          setTimelineDuration(newDuration)
          persistTimelineDuration(newDuration)
        }
      }
    },
    [setView, timelineDuration, persistTimelineDuration, timeSlotInterval],
  )

  // Handle date change from FullCalendar
  const handleDatesSet = useCallback(
    (info: DatesSetArg) => {
      // Skip during initial render to avoid double updates
      if (initialRenderRef.current) return

      // Ensure we have a valid date object
      if (!info.start || !(info.start instanceof Date) || isNaN(info.start.getTime())) {
        console.error("Invalid date in datesSet", info.start)
        return
      }

      // Only update if the date has actually changed
      if (info.start.getTime() !== prevDateRef.current.getTime()) {
        // Set the flag to indicate this is an internal update
        isInternalUpdateRef.current = true

        // Update the app state with a new Date object
        setCurrentDate(new Date(info.start))

        // Update our ref
        prevDateRef.current = new Date(info.start)
      }
    },
    [setCurrentDate],
  )

  // Apply grid lines after initial render and whenever the view changes
  useEffect(() => {
    // Apply grid lines after initial render
    setTimeout(() => {
      const calendarElement = calendarRef.current?.elRef?.current as HTMLElement
      enhanceGridLines(calendarElement, view, timeSlotInterval)
    }, 100)

    // Apply grid lines whenever the window is resized
    const handleResize = () => {
      const calendarElement = calendarRef.current?.elRef?.current as HTMLElement
      enhanceGridLines(calendarElement, view, timeSlotInterval)
    }

    window.addEventListener("resize", handleResize)

    // Apply grid lines periodically to ensure they remain visible
    const intervalId = setInterval(() => {
      const calendarElement = calendarRef.current?.elRef?.current as HTMLElement
      enhanceGridLines(calendarElement, view, timeSlotInterval)
    }, 2000)

    return () => {
      window.removeEventListener("resize", handleResize)
      clearInterval(intervalId)
    }
  }, [view, resources, timeSlotInterval])

  // Add this after the other useEffect hooks
  useEffect(() => {
    // Function to clean up all tooltips
    const cleanupTooltips = () => {
      document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
    }

    // Clean up tooltips when component unmounts
    return () => {
      cleanupTooltips()
    }
  }, [view, resources, timeSlotInterval])

  // Add a MutationObserver to catch any tooltips that might be missed
  useEffect(() => {
    // Create a MutationObserver to watch for DOM changes
    const observer = new MutationObserver((mutations) => {
      // Check if we're currently dragging (look for fc-event-dragging class)
      const isDragging = document.querySelector(".fc-event-dragging")

      if (isDragging) {
        // If dragging, remove all tooltips
        document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
      }
    })

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ["class"],
    })

    // Clean up observer on component unmount
    return () => {
      observer.disconnect()
    }
  }, [])

  // Get the slot configuration based on current view
  const slotConfig = getViewSpecificSlotConfig(view, timeSlotInterval)

  return (
    <>
      <div className="h-full w-full card">
        {/* Show timeline toolbar only when in timeline view */}
        {view === "timeline" && (
          <TimelineToolbar
            dateRange={formatDateRangeForTimeline(currentDate, timelineDuration)}
            onPrevPeriod={prevPeriod}
            onNextPeriod={nextPeriod}
            isCartMode={isCartMode}
            onCartModeChange={setIsCartMode}
            timelineDuration={timelineDuration}
            onTimelineDurationChange={handleTimelineDurationChange}
            timeSlotInterval={timeSlotInterval}
            onTimeSlotIntervalChange={handleTimeSlotIntervalChange}
          />
        )}

        {/* Show regular view toolbar when not in timeline view */}
        {view !== "timeline" && (
          <div className="flex items-center justify-between p-2 bg-background border rounded-lg shadow-sm mb-4">
            {/* Left section - View type display */}
            <div className="flex items-center space-x-2">
              <div className="flex items-center mr-2">
                <CalendarIcon className="h-4 w-4 mr-1 text-muted-foreground" />
                <span className="text-sm font-medium">View: {view.charAt(0).toUpperCase() + view.slice(1)}</span>
              </div>
            </div>

            {/* Center section - Date navigation */}
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="icon" onClick={prevPeriod} className="h-8 w-8 rounded-full">
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <div className="text-base font-semibold min-w-[180px] text-center">
                {formatHeaderDate(currentDate, view)}
              </div>
              <Button variant="ghost" size="icon" onClick={nextPeriod} className="h-8 w-8 rounded-full">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Right section - Controls */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Label htmlFor="cart-mode" className="flex items-center gap-1 text-sm cursor-pointer">
                  <ShoppingCart className="h-3.5 w-3.5" />
                  <span className="hidden sm:inline">Cart Mode</span>
                </Label>
                <Switch
                  id="cart-mode"
                  checked={isCartMode}
                  onCheckedChange={setIsCartMode}
                  className="data-[state=checked]:bg-primary"
                />
              </div>

              <div className="flex sm:hidden items-center space-x-2">
                <Button variant="outline" size="sm" className="h-8 gap-1">
                  <Filter className="h-3.5 w-3.5" />
                  <span className="hidden sm:inline">Filter</span>
                </Button>

                <Button variant="outline" size="sm" className="h-8 gap-1">
                  <SlidersHorizontal className="h-3.5 w-3.5" />
                  <span className="hidden sm:inline">Options</span>
                </Button>
              </div>
            </div>
          </div>
        )}

        <FullCalendar
          schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
          ref={calendarRef}
          plugins={[
            dayGridPlugin,
            timeGridPlugin,
            resourceTimelinePlugin,
            interactionPlugin,
            listPlugin,
            multiMonthPlugin,
          ]}
          droppable
          editable
          allDayMaintainDuration
          selectable
          initialView={getFullCalendarViewType(view, timelineDuration)}
          rerenderDelay={10}
          initialDate={currentDate}
          headerToolbar={false} // We'll use our custom header
          selectable={true}
          selectMirror={true}
          dayMaxEvents={true}
          weekends={true}
          events={allEvents} // Use combined events
          resources={resources}
          resourceGroupField="venueName" // Group resources by venue name
          resourceAreaWidth="200px" // Increase width for hierarchical display
          resourceAreaHeaderContent="Venues & Rooms"
          resourcesInitiallyExpanded={true}
          // Apply view-specific slot configuration
          slotLabelFormat={slotConfig.slotLabelFormat}
          allDaySlot={true}
          allDayText="All Day"
          nowIndicator={true}
          height="100%"
          slotMinTime="00:00:00"
          slotMaxTime="24:00:00"
          scrollTime="08:00:00" // Scroll to 8am by default
          businessHours={{
            daysOfWeek: [1, 2, 3, 4, 5], // Monday - Friday
            startTime: "08:00",
            endTime: "18:00",
          }}
          eventTimeFormat={{
            hour: "numeric",
            minute: "2-digit",
            meridiem: "short",
          }}
          eventDisplay="block"
          slotEventOverlap={false}
          eventOverlap // Prevent events from overlapping
          eventResizableFromStart={true}
          eventDurationEditable={true}
          eventShortHeight={20}
          eventMinHeight={20}
          eventMinWidth={20}
          stickyHeaderDates={true}
          firstDay={1} // Start week on Monday
          locale="en"
          timeZone="local"
          resourceAreaColumns={[
            {
              field: "title",
              headerContent: "Rooms",
            },
          ]}
          resourceOrder="venueName,title" // Ensure resources are ordered by venue first, then by room name
          resourceGroupLabelClassNames="venue-group-label"
          resourceLaneClassNames="resource-lane"
          resourceGroupLaneClassNames="venue-group-lane"
          resourceTimeGridClassNames="resource-time-grid"
          resourceAreaHeaderClassNames="resource-area-header"
          // Configure time slots based on the view-specific configuration
          slotDuration={slotConfig.slotDuration}
          slotLabelInterval={slotConfig.slotLabelInterval}
          snapDuration={slotConfig.snapDuration}
          // Add a class to ensure our CSS overrides take effect
          className="calendar-with-app-borders"
          // Configure views with specific header formats and time slot settings
          views={{
            // Day and Week views with fixed 15-minute slots and proper time formatting
            timeGridDay: {
              slotDuration: "00:15:00",
              slotLabelInterval: "01:00:00",
              slotLabelFormat: {
                hour: "numeric",
                minute: "2-digit",
                omitZeroMinute: false,
                meridiem: "short",
              },
            },
            timeGridWeek: {
              slotDuration: "00:15:00",
              slotLabelInterval: "01:00:00",
              slotLabelFormat: {
                hour: "numeric",
                minute: "2-digit",
                omitZeroMinute: false,
                meridiem: "short",
              },
            },
            // Define custom timeline views with specific header formats
            resourceTimelineDay: {
              type: "resourceTimeline",
              duration: { days: 1 },
              slotLabelFormat: [
                { weekday: "long", day: "numeric", month: "short", year: "numeric" },
                { hour: "numeric", minute: "2-digit", hour12: true },
              ],
              slotDuration: slotConfig.slotDuration,
              slotLabelInterval: slotConfig.slotLabelInterval,
            },
            resourceTimelineThreeDays: {
              type: "resourceTimeline",
              duration: { days: 3 },
              slotLabelFormat: [
                { weekday: "short", day: "numeric", month: "short" },
                { hour: "numeric", minute: "2-digit", hour12: true },
              ],
              slotDuration: slotConfig.slotDuration,
              slotLabelInterval: slotConfig.slotLabelInterval,
            },
            resourceTimelineWeek: {
              type: "resourceTimeline",
              duration: { days: 7 },
              slotLabelFormat: [
                { weekday: "short", day: "numeric", month: "short" },
                { hour: "numeric", minute: "2-digit", hour12: true },
              ],
              slotDuration: slotConfig.slotDuration,
              slotLabelInterval: slotConfig.slotLabelInterval,
            },
            resourceTimelineTwoWeeks: {
              type: "resourceTimeline",
              duration: { days: 14 },
              slotLabelFormat: [
                { weekday: "short", day: "numeric", month: "short" },
                { hour: "numeric", minute: "2-digit", hour12: true },
              ],
              slotDuration: slotConfig.slotDuration,
              slotLabelInterval: slotConfig.slotLabelInterval,
            },
            resourceTimelineMonth: {
              type: "resourceTimeline",
              duration: { days: 30 },
              slotLabelFormat: [
                { weekday: "short", day: "numeric", month: "short" },
                { hour: "numeric", minute: "2-digit", hour12: true },
              ],
              slotDuration: slotConfig.slotDuration,
              slotLabelInterval: slotConfig.slotLabelInterval,
            },
          }}
          eventDidMount={(info) => {
            // Add tooltips or custom styling to events
            const roomId = info.event.extendedProps.roomId
            const venueId = info.event.extendedProps.venueId
            const isGhost = info.event.extendedProps.isGhost
            const isCartGhost = info.event.extendedProps.isCartGhost
            const isModalGhost = info.event.extendedProps.isModalGhost

            // Add this line to set the data-event-id attribute
            info.el.setAttribute("data-event-id", info.event.id)

            const room = rooms.find((r) => r.id === roomId)
            const venue = venues.find((v) => v.id === venueId)

            if (room) {
              info.el.style.borderLeft = `5px solid ${room.color}`
            }

            // Add ghost event styling
            if (isGhost) {
              // Add dashed border
              info.el.style.border = "2px dashed"

              // Add ghost event animation
              info.el.classList.add("ghost-event-pulse")

              // Add status indicator
              const status = info.event.extendedProps.status
              if (status) {
                info.el.classList.add(`ghost-event-${status}`)
              }

              // Add cart-specific styling if it's a cart ghost
              if (isCartGhost) {
                info.el.classList.add("cart-ghost-event")
                info.el.style.borderStyle = "dotted"
              } else if (isModalGhost) {
                info.el.classList.add("modal-ghost-event")
              }

              // Ensure ghost events are visible
              info.el.style.opacity = "0.7"
              info.el.style.pointerEvents = "all"
            }

            // Add tooltip with event details
            const tooltip = document.createElement("div")
            tooltip.className = "fc-tooltip"
            tooltip.innerHTML = createEventTooltip(
              info.event.title,
              info.event.start,
              info.event.end,
              room ? room.name : "",
              venue ? venue.name : "",
              info.event.extendedProps.description,
              isGhost, // Pass isGhost to tooltip creator
              isCartGhost, // Pass isCartGhost to tooltip creator
            )
            // Store the tooltip reference on the event element for later cleanup
            info.el.fcTooltip = tooltip

            // Show tooltip on mouseenter
            info.el.addEventListener("mouseenter", () => {
              // Remove any existing tooltips first
              document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())

              document.body.appendChild(tooltip)
              const rect = info.el.getBoundingClientRect()
              tooltip.style.position = "absolute"
              tooltip.style.zIndex = "1000"
              tooltip.style.left = `${rect.right + 10}px`
              tooltip.style.top = `${rect.top}px`
            })

            // Hide tooltip on mouseleave
            info.el.addEventListener("mouseleave", () => {
              if (document.body.contains(tooltip)) {
                document.body.removeChild(tooltip)
              }
            })
          }}
          eventWillUnmount={(info) => {
            // Clean up tooltip when event is unmounted
            if (info.el && info.el.fcTooltip && document.body.contains(info.el.fcTooltip)) {
              document.body.removeChild(info.el.fcTooltip)
            }

            // Remove any other tooltips that might be lingering
            document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
          }}
          eventDragStart={() => {
            // Remove all tooltips when drag starts
            document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
          }}
          eventDragStop={() => {
            // Ensure tooltips are removed when drag stops
            document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
          }}
          eventResize={() => {
            // Remove tooltips during resize operations
            document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
          }}
          select={handleDateSelect}
          eventClick={handleEventClick}
          eventChange={handleEventChange}
          datesSet={handleDatesSet}
          viewDidMount={handleViewChange}
          editable={true}
          droppable={true}
          slotMinWidth={70}
          resourceTimelineSlotMinWidth={70}
        />
      </div>

      <BookingModal
        isOpen={modalOpen}
        onClose={() => {
          setModalOpen(false)
          setSelectedVenueId(null)
          setSelectedRoomId(null)
        }}
        booking={selectedBooking}
        startDate={selectedDates?.start}
        endDate={selectedDates?.end}
        initialRoomId={selectedRoomId}
        initialVenueId={selectedVenueId}
      />
    </>
  )
}

// Add a default export to ensure compatibility
export default FullCalendarWrapper

