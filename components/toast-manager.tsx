"use client"

import { useState, useEffect } from "react"
import { createPortal } from "react-dom"
import { CartUpdateToast } from "./cart-update-toast"

export type ToastType = {
  id: string
  message: string
}

// Create a custom event for toast notifications
export const showToast = (message: string) => {
  const event = new CustomEvent("showToast", { detail: { message } })
  window.dispatchEvent(event)
}

export function ToastManager() {
  const [toasts, setToasts] = useState<ToastType[]>([])
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)

    const handleShowToast = (event: CustomEvent<{ message: string }>) => {
      const id = Date.now().toString()
      setToasts((prev) => [...prev, { id, message: event.detail.message }])
    }

    window.addEventListener("showToast", handleShowToast as EventListener)

    return () => {
      window.removeEventListener("showToast", handleShowToast as EventListener)
    }
  }, [])

  const removeToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }

  if (!isMounted) return null

  return createPortal(
    <div className="toast-container">
      {toasts.map((toast) => (
        <CartUpdateToast key={toast.id} message={toast.message} onClose={() => removeToast(toast.id)} />
      ))}
    </div>,
    document.body,
  )
}

