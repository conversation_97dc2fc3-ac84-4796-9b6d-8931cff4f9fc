"use client"

import { useState, useEffect } from "react"
import { Bug } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function DebugToggle() {
  const [isDebugMode, setIsDebugMode] = useState(false)

  useEffect(() => {
    if (isDebugMode) {
      document.body.classList.add("debug-layout")
    } else {
      document.body.classList.remove("debug-layout")
    }

    return () => {
      document.body.classList.remove("debug-layout")
    }
  }, [isDebugMode])

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={() => setIsDebugMode(!isDebugMode)}
      className="fixed bottom-4 right-4 z-50 bg-muted rounded-full"
      title={isDebugMode ? "Disable debug mode" : "Enable debug mode"}
    >
      <Bug className={`w-5 h-5 ${isDebugMode ? "text-destructive" : "text-muted-foreground"}`} />
    </Button>
  )
}

