lockfileVersion: 5.4

specifiers:
  '@fullcalendar/core': 6.1.17
  '@fullcalendar/daygrid': 6.1.17
  '@fullcalendar/interaction': 6.1.17
  '@fullcalendar/list': 6.1.17
  '@fullcalendar/multimonth': 6.1.17
  '@fullcalendar/react': 6.1.17
  '@fullcalendar/resource': 6.1.17
  '@fullcalendar/resource-timeline': ^6.1.17
  '@fullcalendar/timegrid': 6.1.17
  '@hookform/resolvers': ^3.9.1
  '@radix-ui/react-accordion': ^1.2.2
  '@radix-ui/react-alert-dialog': ^1.1.4
  '@radix-ui/react-aspect-ratio': ^1.1.1
  '@radix-ui/react-avatar': ^1.1.2
  '@radix-ui/react-checkbox': ^1.1.3
  '@radix-ui/react-collapsible': ^1.1.2
  '@radix-ui/react-context-menu': ^2.2.4
  '@radix-ui/react-dialog': latest
  '@radix-ui/react-dropdown-menu': ^2.1.4
  '@radix-ui/react-hover-card': ^1.1.4
  '@radix-ui/react-label': ^2.1.1
  '@radix-ui/react-menubar': ^1.1.4
  '@radix-ui/react-navigation-menu': ^1.2.3
  '@radix-ui/react-popover': ^1.1.4
  '@radix-ui/react-progress': ^1.1.1
  '@radix-ui/react-radio-group': ^1.2.2
  '@radix-ui/react-scroll-area': latest
  '@radix-ui/react-select': ^2.1.4
  '@radix-ui/react-separator': ^1.1.1
  '@radix-ui/react-slider': ^1.2.2
  '@radix-ui/react-slot': ^1.1.2
  '@radix-ui/react-switch': ^1.1.2
  '@radix-ui/react-tabs': ^1.1.2
  '@radix-ui/react-toast': ^1.2.4
  '@radix-ui/react-toggle': ^1.1.1
  '@radix-ui/react-toggle-group': ^1.1.1
  '@radix-ui/react-tooltip': latest
  '@types/node': ^22
  '@types/react': ^19
  '@types/react-dom': ^19
  autoprefixer: ^10.4.20
  bunx: ^0.1.0
  class-variance-authority: ^0.7.1
  clsx: ^2.1.1
  cmdk: 1.0.4
  date-fns: latest
  embla-carousel-react: 8.5.1
  immer: latest
  input-otp: 1.4.1
  lucide-react: ^0.454.0
  motion: ^12.6.3
  next: 15.2.4
  next-themes: latest
  postcss: ^8
  react: ^19
  react-day-picker: 8.10.1
  react-dom: ^19
  react-hook-form: ^7.54.1
  react-resizable-panels: ^2.1.7
  recharts: 2.15.0
  sonner: ^1.7.1
  tailwind-merge: ^2.5.5
  tailwindcss: ^3.4.17
  tailwindcss-animate: ^1.0.7
  typescript: ^5
  use-sync-external-store: latest
  uuid: latest
  vaul: ^0.9.6
  zod: ^3.24.1
  zustand: latest

dependencies:
  '@fullcalendar/core': 6.1.17
  '@fullcalendar/daygrid': 6.1.17_@fullcalendar+core@6.1.17
  '@fullcalendar/interaction': 6.1.17_@fullcalendar+core@6.1.17
  '@fullcalendar/list': 6.1.17_@fullcalendar+core@6.1.17
  '@fullcalendar/multimonth': 6.1.17_@fullcalendar+core@6.1.17
  '@fullcalendar/react': 6.1.17_kb5mc3ktnjoohuanl6xatr4qai
  '@fullcalendar/resource': 6.1.17_@fullcalendar+core@6.1.17
  '@fullcalendar/resource-timeline': 6.1.17_s5te7wkk7ljwqiwsw4f4js4ut4
  '@fullcalendar/timegrid': 6.1.17_@fullcalendar+core@6.1.17
  '@hookform/resolvers': 3.10.0_react-hook-form@7.55.0
  '@radix-ui/react-accordion': 1.2.6_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-alert-dialog': 1.1.9_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-aspect-ratio': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-avatar': 1.1.5_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-checkbox': 1.2.1_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-collapsible': 1.1.6_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-context-menu': 2.2.9_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-dialog': 1.1.14_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-dropdown-menu': 2.1.9_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-hover-card': 1.1.9_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-label': 2.1.4_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-menubar': 1.1.9_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-navigation-menu': 1.2.8_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-popover': 1.1.9_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-progress': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-radio-group': 1.3.1_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-scroll-area': 1.2.9_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-select': 2.2.1_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-separator': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-slider': 1.3.1_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-slot': 1.2.0_dl52hbs33murufky2ojig52vdq
  '@radix-ui/react-switch': 1.2.1_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-tabs': 1.1.6_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-toast': 1.2.9_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-toggle': 1.1.5_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-toggle-group': 1.1.5_5j5q5giq4a6q5rwe55huzqxn5e
  '@radix-ui/react-tooltip': 1.2.7_5j5q5giq4a6q5rwe55huzqxn5e
  autoprefixer: 10.4.21_postcss@8.5.3
  class-variance-authority: 0.7.1
  clsx: 2.1.1
  cmdk: 1.0.4_5j5q5giq4a6q5rwe55huzqxn5e
  date-fns: 4.1.0
  embla-carousel-react: 8.5.1_react@19.1.0
  immer: 10.1.1
  input-otp: 1.4.1_j6k6oay3ugsr56slyfvma2drry
  lucide-react: 0.454.0_react@19.1.0
  motion: 12.7.4_j6k6oay3ugsr56slyfvma2drry
  next: 15.2.4_j6k6oay3ugsr56slyfvma2drry
  next-themes: 0.4.6_j6k6oay3ugsr56slyfvma2drry
  react: 19.1.0
  react-day-picker: 8.10.1_azwu7r6w53kir3xxtk6jezvccy
  react-dom: 19.1.0_react@19.1.0
  react-hook-form: 7.55.0_react@19.1.0
  react-resizable-panels: 2.1.7_j6k6oay3ugsr56slyfvma2drry
  recharts: 2.15.0_j6k6oay3ugsr56slyfvma2drry
  sonner: 1.7.4_j6k6oay3ugsr56slyfvma2drry
  tailwind-merge: 2.6.0
  tailwindcss-animate: 1.0.7_tailwindcss@3.4.17
  use-sync-external-store: 1.5.0_react@19.1.0
  uuid: 11.1.0
  vaul: 0.9.9_5j5q5giq4a6q5rwe55huzqxn5e
  zod: 3.24.3
  zustand: 5.0.6_ymjxa3ljbsttey3xxcr7vsvgzu

devDependencies:
  '@types/node': 22.14.1
  '@types/react': 19.1.2
  '@types/react-dom': 19.1.2_@types+react@19.1.2
  bunx: 0.1.0
  postcss: 8.5.3
  tailwindcss: 3.4.17
  typescript: 5.8.3

packages:

  /@alloc/quick-lru/5.2.0:
    resolution: {integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==}
    engines: {node: '>=10'}

  /@babel/runtime/7.27.0:
    resolution: {integrity: sha512-VtPOkrdPHZsKc/clNqyi9WUA8TINkZ4cGk63UUE3u4pmB2k+ZMQRDuIOagv8UVd6j7k0T3+RRIb7beKTebNbcw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.14.1
    dev: false

  /@emnapi/runtime/1.4.3:
    resolution: {integrity: sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==}
    requiresBuild: true
    dependencies:
      tslib: 2.8.1
    dev: false
    optional: true

  /@floating-ui/core/1.6.9:
    resolution: {integrity: sha512-uMXCuQ3BItDUbAMhIXw7UPXRfAlOAvZzdK9BWpE60MCn+Svt3aLn9jsPTi/WNGlRUu2uI0v5S7JiIUsbsvh3fw==}
    dependencies:
      '@floating-ui/utils': 0.2.9
    dev: false

  /@floating-ui/dom/1.6.13:
    resolution: {integrity: sha512-umqzocjDgNRGTuO7Q8CU32dkHkECqI8ZdMZ5Swb6QAM0t5rnlrN3lGo1hdpscRd3WS8T6DKYK4ephgIH9iRh3w==}
    dependencies:
      '@floating-ui/core': 1.6.9
      '@floating-ui/utils': 0.2.9
    dev: false

  /@floating-ui/react-dom/2.1.2_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@floating-ui/dom': 1.6.13
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@floating-ui/utils/0.2.9:
    resolution: {integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==}
    dev: false

  /@fullcalendar/core/6.1.17:
    resolution: {integrity: sha512-0W7lnIrv18ruJ5zeWBeNZXO8qCWlzxDdp9COFEsZnyNjiEhUVnrW/dPbjRKYpL0edGG0/Lhs0ghp1z/5ekt8ZA==}
    dependencies:
      preact: 10.12.1
    dev: false

  /@fullcalendar/daygrid/6.1.17_@fullcalendar+core@6.1.17:
    resolution: {integrity: sha512-K7m+pd7oVJ9fW4h7CLDdDGJbc9szJ1xDU1DZ2ag+7oOo1aCNLv44CehzkkknM6r8EYlOOhgaelxQpKAI4glj7A==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
    dependencies:
      '@fullcalendar/core': 6.1.17
    dev: false

  /@fullcalendar/interaction/6.1.17_@fullcalendar+core@6.1.17:
    resolution: {integrity: sha512-AudvQvgmJP2FU89wpSulUUjeWv24SuyCx8FzH2WIPVaYg+vDGGYarI7K6PcM3TH7B/CyaBjm5Rqw9lXgnwt5YA==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
    dependencies:
      '@fullcalendar/core': 6.1.17
    dev: false

  /@fullcalendar/list/6.1.17_@fullcalendar+core@6.1.17:
    resolution: {integrity: sha512-fkyK49F9IxwlGUBVhJGsFpd/LTi/vRVERLIAe1HmBaGkjwpxnynm8TMLb9mZip97wvDk3CmZWduMe6PxscAlow==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
    dependencies:
      '@fullcalendar/core': 6.1.17
    dev: false

  /@fullcalendar/multimonth/6.1.17_@fullcalendar+core@6.1.17:
    resolution: {integrity: sha512-ZxA9mkTzKayCdxR5je9P9++qqhSeSbuvXmvZ6doZw6omv8K52cD7XJii+P7gvxATXxtI6hg4i+DuMyOHxP1E2g==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
    dependencies:
      '@fullcalendar/core': 6.1.17
      '@fullcalendar/daygrid': 6.1.17_@fullcalendar+core@6.1.17
    dev: false

  /@fullcalendar/premium-common/6.1.17_@fullcalendar+core@6.1.17:
    resolution: {integrity: sha512-zoN7fMwGMcP6Xu+2YudRAGfdwD2J+V+A/xAieXgYDSZT+5ekCsjZiwb2rmvthjt+HVnuZcqs6sGp7rnJ8Ie/mA==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
    dependencies:
      '@fullcalendar/core': 6.1.17
    dev: false

  /@fullcalendar/react/6.1.17_kb5mc3ktnjoohuanl6xatr4qai:
    resolution: {integrity: sha512-AA8soHhlfRH5dUeqHnfAtzDiXa2vrgWocJSK/F5qzw/pOxc9MqpuoS/nQBROWtHHg6yQUg3DoGqOOhi7dmylXQ==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
      react: ^16.7.0 || ^17 || ^18 || ^19
      react-dom: ^16.7.0 || ^17 || ^18 || ^19
    dependencies:
      '@fullcalendar/core': 6.1.17
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@fullcalendar/resource-timeline/6.1.17_s5te7wkk7ljwqiwsw4f4js4ut4:
    resolution: {integrity: sha512-QMrtc1mLs4c6DtlBNmWICef8Lr4CmzE47uWS/rcJBd9K2kBzvusTp7AQQ1qn3RX5UnjNHqT8pkKO/wE4yspJQw==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
      '@fullcalendar/resource': ~6.1.17
    dependencies:
      '@fullcalendar/core': 6.1.17
      '@fullcalendar/premium-common': 6.1.17_@fullcalendar+core@6.1.17
      '@fullcalendar/resource': 6.1.17_@fullcalendar+core@6.1.17
      '@fullcalendar/scrollgrid': 6.1.17_@fullcalendar+core@6.1.17
      '@fullcalendar/timeline': 6.1.17_@fullcalendar+core@6.1.17
    dev: false

  /@fullcalendar/resource/6.1.17_@fullcalendar+core@6.1.17:
    resolution: {integrity: sha512-hWnbOWlroIN5Wt4NJmHAJh/F7ge2cV6S0PdGSmLFoZJZJA0hJX9GeYRzyz4MlUoj7f4dGzBlesy2RdC+t5FEMw==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
    dependencies:
      '@fullcalendar/core': 6.1.17
      '@fullcalendar/premium-common': 6.1.17_@fullcalendar+core@6.1.17
    dev: false

  /@fullcalendar/scrollgrid/6.1.17_@fullcalendar+core@6.1.17:
    resolution: {integrity: sha512-lzphEKwxWMS4xQVEuimzZjKFLijlSn49ExvzkYZls0VLDwOa3BYHcRlDJBjQ0LP6kauz9aatg3MfRIde/LAazA==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
    dependencies:
      '@fullcalendar/core': 6.1.17
      '@fullcalendar/premium-common': 6.1.17_@fullcalendar+core@6.1.17
    dev: false

  /@fullcalendar/timegrid/6.1.17_@fullcalendar+core@6.1.17:
    resolution: {integrity: sha512-K4PlA3L3lclLOs3IX8cvddeiJI9ZVMD7RA9IqaWwbvac771971foc9tFze9YY+Pqesf6S+vhS2dWtEVlERaGlQ==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
    dependencies:
      '@fullcalendar/core': 6.1.17
      '@fullcalendar/daygrid': 6.1.17_@fullcalendar+core@6.1.17
    dev: false

  /@fullcalendar/timeline/6.1.17_@fullcalendar+core@6.1.17:
    resolution: {integrity: sha512-UhL2OOph/S0cEKs3lzbXjS2gTxmQwaNug2XFjdljvO/ERj10v7OBXj/zvJrPyhjvWR/CSgjNgBaUpngkCu4JtQ==}
    peerDependencies:
      '@fullcalendar/core': ~6.1.17
    dependencies:
      '@fullcalendar/core': 6.1.17
      '@fullcalendar/premium-common': 6.1.17_@fullcalendar+core@6.1.17
      '@fullcalendar/scrollgrid': 6.1.17_@fullcalendar+core@6.1.17
    dev: false

  /@hookform/resolvers/3.10.0_react-hook-form@7.55.0:
    resolution: {integrity: sha512-79Dv+3mDF7i+2ajj7SkypSKHhl1cbln1OGavqrsF7p6mbUv11xpqpacPsGDCTRvCSjEEIez2ef1NveSVL3b0Ag==}
    peerDependencies:
      react-hook-form: ^7.0.0
    dependencies:
      react-hook-form: 7.55.0_react@19.1.0
    dev: false

  /@img/sharp-darwin-arm64/0.33.5:
    resolution: {integrity: sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    optionalDependencies:
      '@img/sharp-libvips-darwin-arm64': 1.0.4
    dev: false
    optional: true

  /@img/sharp-darwin-x64/0.33.5:
    resolution: {integrity: sha512-fyHac4jIc1ANYGRDxtiqelIbdWkIuQaI84Mv45KvGRRxSAa7o7d1ZKAOBaYbnepLC1WqxfpimdeWfvqqSGwR2Q==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    optionalDependencies:
      '@img/sharp-libvips-darwin-x64': 1.0.4
    dev: false
    optional: true

  /@img/sharp-libvips-darwin-arm64/1.0.4:
    resolution: {integrity: sha512-XblONe153h0O2zuFfTAbQYAX2JhYmDHeWikp1LM9Hul9gVPjFY427k6dFEcOL72O01QxQsWi761svJ/ev9xEDg==}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /@img/sharp-libvips-darwin-x64/1.0.4:
    resolution: {integrity: sha512-xnGR8YuZYfJGmWPvmlunFaWJsb9T/AO2ykoP3Fz/0X5XV2aoYBPkX6xqCQvUTKKiLddarLaxpzNe+b1hjeWHAQ==}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /@img/sharp-libvips-linux-arm/1.0.5:
    resolution: {integrity: sha512-gvcC4ACAOPRNATg/ov8/MnbxFDJqf/pDePbBnuBDcjsI8PssmjoKMAz4LtLaVi+OnSb5FK/yIOamqDwGmXW32g==}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@img/sharp-libvips-linux-arm64/1.0.4:
    resolution: {integrity: sha512-9B+taZ8DlyyqzZQnoeIvDVR/2F4EbMepXMc/NdVbkzsJbzkUjhXv/70GQJ7tdLA4YJgNP25zukcxpX2/SueNrA==}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@img/sharp-libvips-linux-s390x/1.0.4:
    resolution: {integrity: sha512-u7Wz6ntiSSgGSGcjZ55im6uvTrOxSIS8/dgoVMoiGE9I6JAfU50yH5BoDlYA1tcuGS7g/QNtetJnxA6QEsCVTA==}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@img/sharp-libvips-linux-x64/1.0.4:
    resolution: {integrity: sha512-MmWmQ3iPFZr0Iev+BAgVMb3ZyC4KeFc3jFxnNbEPas60e1cIfevbtuyf9nDGIzOaW9PdnDciJm+wFFaTlj5xYw==}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@img/sharp-libvips-linuxmusl-arm64/1.0.4:
    resolution: {integrity: sha512-9Ti+BbTYDcsbp4wfYib8Ctm1ilkugkA/uscUn6UXK1ldpC1JjiXbLfFZtRlBhjPZ5o1NCLiDbg8fhUPKStHoTA==}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@img/sharp-libvips-linuxmusl-x64/1.0.4:
    resolution: {integrity: sha512-viYN1KX9m+/hGkJtvYYp+CCLgnJXwiQB39damAO7WMdKWlIhmYTfHjwSbQeUK/20vY154mwezd9HflVFM1wVSw==}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@img/sharp-linux-arm/0.33.5:
    resolution: {integrity: sha512-JTS1eldqZbJxjvKaAkxhZmBqPRGmxgu+qFKSInv8moZ2AmT5Yib3EQ1c6gp493HvrvV8QgdOXdyaIBrhvFhBMQ==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    optionalDependencies:
      '@img/sharp-libvips-linux-arm': 1.0.5
    dev: false
    optional: true

  /@img/sharp-linux-arm64/0.33.5:
    resolution: {integrity: sha512-JMVv+AMRyGOHtO1RFBiJy/MBsgz0x4AWrT6QoEVVTyh1E39TrCUpTRI7mx9VksGX4awWASxqCYLCV4wBZHAYxA==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    optionalDependencies:
      '@img/sharp-libvips-linux-arm64': 1.0.4
    dev: false
    optional: true

  /@img/sharp-linux-s390x/0.33.5:
    resolution: {integrity: sha512-y/5PCd+mP4CA/sPDKl2961b+C9d+vPAveS33s6Z3zfASk2j5upL6fXVPZi7ztePZ5CuH+1kW8JtvxgbuXHRa4Q==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    optionalDependencies:
      '@img/sharp-libvips-linux-s390x': 1.0.4
    dev: false
    optional: true

  /@img/sharp-linux-x64/0.33.5:
    resolution: {integrity: sha512-opC+Ok5pRNAzuvq1AG0ar+1owsu842/Ab+4qvU879ippJBHvyY5n2mxF1izXqkPYlGuP/M556uh53jRLJmzTWA==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    optionalDependencies:
      '@img/sharp-libvips-linux-x64': 1.0.4
    dev: false
    optional: true

  /@img/sharp-linuxmusl-arm64/0.33.5:
    resolution: {integrity: sha512-XrHMZwGQGvJg2V/oRSUfSAfjfPxO+4DkiRh6p2AFjLQztWUuY/o8Mq0eMQVIY7HJ1CDQUJlxGGZRw1a5bqmd1g==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    optionalDependencies:
      '@img/sharp-libvips-linuxmusl-arm64': 1.0.4
    dev: false
    optional: true

  /@img/sharp-linuxmusl-x64/0.33.5:
    resolution: {integrity: sha512-WT+d/cgqKkkKySYmqoZ8y3pxx7lx9vVejxW/W4DOFMYVSkErR+w7mf2u8m/y4+xHe7yY9DAXQMWQhpnMuFfScw==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    optionalDependencies:
      '@img/sharp-libvips-linuxmusl-x64': 1.0.4
    dev: false
    optional: true

  /@img/sharp-wasm32/0.33.5:
    resolution: {integrity: sha512-ykUW4LVGaMcU9lu9thv85CbRMAwfeadCJHRsg2GmeRa/cJxsVY9Rbd57JcMxBkKHag5U/x7TSBpScF4U8ElVzg==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [wasm32]
    requiresBuild: true
    dependencies:
      '@emnapi/runtime': 1.4.3
    dev: false
    optional: true

  /@img/sharp-win32-ia32/0.33.5:
    resolution: {integrity: sha512-T36PblLaTwuVJ/zw/LaH0PdZkRz5rd3SmMHX8GSmR7vtNSP5Z6bQkExdSK7xGWyxLw4sUknBuugTelgw2faBbQ==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@img/sharp-win32-x64/0.33.5:
    resolution: {integrity: sha512-MpY/o8/8kj+EcnxwvrP4aTJSWw/aZ7JIGR4aBeZkZw5B7/Jn+tY9/VNwtcoGmdT7GfggGIU4kygOMSbYnOrAbg==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@isaacs/cliui/8.0.2:
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}
    dependencies:
      string-width: 5.1.2
      string-width-cjs: /string-width/4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: /strip-ansi/6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: /wrap-ansi/7.0.0

  /@jridgewell/gen-mapping/0.3.8:
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  /@jridgewell/resolve-uri/3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/set-array/1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/sourcemap-codec/1.5.0:
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  /@jridgewell/trace-mapping/0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  /@next/env/15.2.4:
    resolution: {integrity: sha512-+SFtMgoiYP3WoSswuNmxJOCwi06TdWE733D+WPjpXIe4LXGULwEaofiiAy6kbS0+XjM5xF5n3lKuBwN2SnqD9g==}
    dev: false

  /@next/swc-darwin-arm64/15.2.4:
    resolution: {integrity: sha512-1AnMfs655ipJEDC/FHkSr0r3lXBgpqKo4K1kiwfUf3iE68rDFXZ1TtHdMvf7D0hMItgDZ7Vuq3JgNMbt/+3bYw==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-darwin-x64/15.2.4:
    resolution: {integrity: sha512-3qK2zb5EwCwxnO2HeO+TRqCubeI/NgCe+kL5dTJlPldV/uwCnUgC7VbEzgmxbfrkbjehL4H9BPztWOEtsoMwew==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-arm64-gnu/15.2.4:
    resolution: {integrity: sha512-HFN6GKUcrTWvem8AZN7tT95zPb0GUGv9v0d0iyuTb303vbXkkbHDp/DxufB04jNVD+IN9yHy7y/6Mqq0h0YVaQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-arm64-musl/15.2.4:
    resolution: {integrity: sha512-Oioa0SORWLwi35/kVB8aCk5Uq+5/ZIumMK1kJV+jSdazFm2NzPDztsefzdmzzpx5oGCJ6FkUC7vkaUseNTStNA==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-x64-gnu/15.2.4:
    resolution: {integrity: sha512-yb5WTRaHdkgOqFOZiu6rHV1fAEK0flVpaIN2HB6kxHVSy/dIajWbThS7qON3W9/SNOH2JWkVCyulgGYekMePuw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-x64-musl/15.2.4:
    resolution: {integrity: sha512-Dcdv/ix6srhkM25fgXiyOieFUkz+fOYkHlydWCtB0xMST6X9XYI3yPDKBZt1xuhOytONsIFJFB08xXYsxUwJLw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-win32-arm64-msvc/15.2.4:
    resolution: {integrity: sha512-dW0i7eukvDxtIhCYkMrZNQfNicPDExt2jPb9AZPpL7cfyUo7QSNl1DjsHjmmKp6qNAqUESyT8YFl/Aw91cNJJg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-win32-x64-msvc/15.2.4:
    resolution: {integrity: sha512-SbnWkJmkS7Xl3kre8SdMF6F/XDh1DTFEhp0jRTj/uB8iPKoU2bb2NDfcu+iifv1+mxQEd1g2vvSxcZbXSKyWiQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  /@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  /@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  /@pkgjs/parseargs/0.11.0:
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}
    requiresBuild: true
    optional: true

  /@radix-ui/number/1.1.1:
    resolution: {integrity: sha512-MkKCwxlXTgz6CFoJx3pCwn07GKp36+aZyu/u2Ln2VrA5DcdyCZkASEDBTd8x5whTQQL5CiYf4prXKLcgQdv29g==}
    dev: false

  /@radix-ui/primitive/1.1.2:
    resolution: {integrity: sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==}
    dev: false

  /@radix-ui/react-accordion/1.2.6_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-FMsLy77w3XH0349mOB6TP69QwqXQ5sii49ZF9qOUD+bqrAk3Os0whM/VayIDWMSK1PWUqItvlamFvnE/RW+wPw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collapsible': 1.1.6_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-collection': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-alert-dialog/1.1.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-TpLFYZ3LFLZ04CSODOq0x3CvreHMF//OMqBIgMaapWsLaTvXn8po9KU9PyZZpCN1MNQGz8LVIfIB51VMk5kqyg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-dialog': 1.1.9_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-slot': 1.2.0_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-arrow/1.1.4_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-qz+fxrqgNxG0dYew5l7qR3c7wdgRu1XVUHGnGYX7rg5HM4p9SWaRmJwfgR3J0SgyUKayLmzQIun+N6rWRgiRKw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-arrow/1.1.7_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-F+M1tLhO+mlQaOWspE8Wstg+z6PwxwRd8oQ8IXceWz92kfAmalTRf0EjrouQeo7QssEPfCn05B4Ihs1K9WQ/7w==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-aspect-ratio/1.1.4_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-ie2mUDtM38LBqVU+Xn+GIY44tWM5yVbT5uXO+th85WZxUUsgEdWNNZWecqqGzkQ4Af+Fq1mYT6TyQ/uUf5gfcw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-avatar/1.1.5_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-bC1RzfRN8jWayFTGirqgnlmjJ9IHl3K6GT4Z4vDFrU7DdPVc4P8XSxUe3sRicjL6423WIWY6p5ys5NndNoaeAw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-checkbox/1.2.1_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-6a+qvM6rNAvVJ82z10nQquthwu+gcjwX28vmIh1eshhBf5lFHw+XU87nh4K6AzbL5tS2IH7h4H0+CIUlVuIoog==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-presence': 1.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-previous': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-size': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-collapsible/1.1.6_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-L2K6a4DB0xg2gAhVqdyI7tDdjG995l7AgHVLgk2LB1nnmtJtxbg7pF6+LhMABmuahs3DvI4aq3QqBlhOA9e1gA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-presence': 1.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-collection/1.1.4_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-cv4vSf7HttqXilDnAnvINd53OTl1/bjUYVZrkFnA7nwmY9Ob2POUy0WY0sfqBAe1s5FyKsyceQlqiEGPYNTadg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-slot': 1.2.0_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-compose-refs/1.1.2_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-context-menu/2.2.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-0V/PId9MNLjx6VuLm55S4qEy3x0H0KmO3x3G884HHB5xlGAZu92MwF3oBEg7J8JsorGMbabt6k6XSOfLoB32Bw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-menu': 2.1.9_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-context/1.1.2_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-dialog/1.1.14_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-+CpweKjqpzTmwRwcYECQcNYbI8V9VSQt0SNFKeEBLgfucbsLssU6Ppq7wUdNXEGb573bMjFhVjKVll8rmV6zMw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-dismissable-layer': 1.1.10_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-focus-guards': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-focus-scope': 1.1.7_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-portal': 1.1.9_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-presence': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-slot': 1.2.3_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-controllable-state': 1.2.2_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      aria-hidden: 1.2.4
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      react-remove-scroll: 2.6.3_dl52hbs33murufky2ojig52vdq
    dev: false

  /@radix-ui/react-dialog/1.1.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-uQPEgnOOqrxj0oDfzTT9rhAflnSGGl2/Txto586pLl1XjnhaxfJ/w61v7bW+Ce7LWTKJUZhcnZqtEcME6DOseQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-dismissable-layer': 1.1.7_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-focus-guards': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-focus-scope': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-portal': 1.1.6_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-presence': 1.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-slot': 1.2.0_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      aria-hidden: 1.2.4
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      react-remove-scroll: 2.6.3_dl52hbs33murufky2ojig52vdq
    dev: false

  /@radix-ui/react-direction/1.1.1_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-dismissable-layer/1.1.10_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-IM1zzRV4W3HtVgftdQiiOmA0AdJlCtMLe00FXaHwgt3rAnNsIyDqshvkIW3hj/iu5hu8ERP7KIYki6NkqDxAwQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-escape-keydown': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-dismissable-layer/1.1.7_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-j5+WBUdhccJsmH5/H0K6RncjDtoALSEr6jbkaZu+bjw6hOPOhHycr6vEUujl+HBK8kjUfWcoCJXxP6e4lUlMZw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-escape-keydown': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-dropdown-menu/2.1.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-f8Qhc+gGQWSm/mOL4AazRvJ1DZaf6dR5Wz90PRuouNdPVj4DKA/zHI2nCbSZnYLwRnkRqlhzK16u8CzeUPa7LQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-menu': 2.1.9_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-focus-guards/1.1.2_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-focus-scope/1.1.4_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-r2annK27lIW5w9Ho5NyQgqs0MmgZSTIKXWpVCJaLC1q2kZrZkcqnmHkCHMEmv8XLvsLlurKMPT+kbKkRkm/xVA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-focus-scope/1.1.7_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-t2ODlkXBQyn7jkl6TNaw/MtVEVvIGelJDCG41Okq/KwUsJBwQ4XVZsHAVUkK4mBv3ewiAS3PGuUWuY2BoK4ZUw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-hover-card/1.1.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-qQynfnyz75k5Ls9g+8tlJ7h07TMUXT2yHnG1Ol1Cq37RI49Hy8GwM6q1FsQzIYi414aiNXcl1YkQkelj8ScekQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-dismissable-layer': 1.1.7_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-popper': 1.2.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-portal': 1.1.6_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-presence': 1.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-id/1.1.1_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-label/2.1.4_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-wy3dqizZnZVV4ja0FNnUhIWNwWdoldXrneEyUcVtLYDAt8ovGS4ridtMAOGgXBBIfggL4BOveVWsjXDORdGEQg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-menu/2.1.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-obBOYJGkrzIJDFkOmsdOe51a8d7kcY6ZUE+dW3BcWP67sZE1sdm72e+VHOjPbD/WdQ2BgUJI/u/eX2md2FXpug==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-dismissable-layer': 1.1.7_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-focus-guards': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-focus-scope': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-popper': 1.2.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-portal': 1.1.6_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-presence': 1.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-roving-focus': 1.1.5_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-slot': 1.2.0_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      aria-hidden: 1.2.4
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      react-remove-scroll: 2.6.3_dl52hbs33murufky2ojig52vdq
    dev: false

  /@radix-ui/react-menubar/1.1.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-tS2KoCplSqiQLJRj3gVq50pFMycLmiN8TCBVCNQug0TZciLTIXLCt2gjjy+3e5pnt9JafDmvrl61P3xlyauikA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-menu': 2.1.9_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-roving-focus': 1.1.5_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-navigation-menu/1.2.8_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-zZ/s2kmkvDB7X0N28WUwxLCv+U7TAMtqmgtZC9GkNxS4S2KijUZTA4tyiY3ApNgWdBKMAkphK0CU9L6Ivq9ITQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-dismissable-layer': 1.1.7_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-presence': 1.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-previous': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-visually-hidden': 1.2.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-popover/1.1.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-+HwMT3vivhqyNJGtWEOMcd5OiQuRoRzPdjQZ+I16V4mzIXfV8p+woi7qKVhNF/HzS+O4HGxlCAeKNu5AHuZWWQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-dismissable-layer': 1.1.7_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-focus-guards': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-focus-scope': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-popper': 1.2.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-portal': 1.1.6_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-presence': 1.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-slot': 1.2.0_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      aria-hidden: 1.2.4
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      react-remove-scroll: 2.6.3_dl52hbs33murufky2ojig52vdq
    dev: false

  /@radix-ui/react-popper/1.2.4_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-3p2Rgm/a1cK0r/UVkx5F/K9v/EplfjAeIFCGOPYPO4lZ0jtg4iSQXt/YGTSLWaf4x7NG6Z4+uKFcylcTZjeqDA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@floating-ui/react-dom': 2.1.2_j6k6oay3ugsr56slyfvma2drry
      '@radix-ui/react-arrow': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-rect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-size': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/rect': 1.1.1
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-popper/1.2.7_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-IUFAccz1JyKcf/RjB552PlWwxjeCJB8/4KxT7EhBHOJM+mN7LdW+B3kacJXILm32xawcMMjb2i0cIZpo+f9kiQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@floating-ui/react-dom': 2.1.2_j6k6oay3ugsr56slyfvma2drry
      '@radix-ui/react-arrow': 1.1.7_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-rect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-size': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/rect': 1.1.1
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-portal/1.1.6_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-XmsIl2z1n/TsYFLIdYam2rmFwf9OC/Sh2avkbmVMDuBZIe7hSpM0cYnWPAo7nHOVx8zTuwDZGByfcqLdnzp3Vw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-portal/1.1.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-bpIxvq03if6UNwXZ+HTK71JLh4APvnXntDc6XOX8UVq4XQOVl7lwok0AvIl+b8zgCw3fSaVTZMpAPPagXbKmHQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-presence/1.1.3_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-IrVLIhskYhH3nLvtcBLQFZr61tBG7wx7O3kEmdzcYwRGAEBmBicGGL7ATzNgruYJ3xBTbuzEEq9OXJM3PAX3tA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-presence/1.1.4_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-primitive/2.1.0_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-/J/FhLdK0zVcILOwt5g+dH4KnkonCtkVJsa2G6JmvbbtZfBEI1gMsO3QMjseL4F/SwfAMt1Vc/0XKYKq+xJ1sw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-slot': 1.2.0_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-primitive/2.1.3_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-slot': 1.2.3_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-progress/1.1.4_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-8rl9w7lJdcVPor47Dhws9mUHRHLE+8JEgyJRdNWCpGPa6HIlr3eh+Yn9gyx1CnCLbw5naHsI2gaO9dBWO50vzw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-radio-group/1.3.1_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-IDYsPZBjk0NxduStmADKPeS+HNHdXrDFiQaJkTLNfNedsHQbcnWmwQVgyIJG2YT25NLDs4YYYLnAtXf1sGQQ9g==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-presence': 1.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-roving-focus': 1.1.5_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-previous': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-size': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-roving-focus/1.1.5_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-KmUqtK/rU73ZyN29KdY7DLgI/GavtV+Xj8L0xKjMZizUlnBNQOlKI1WWJ7ANtvXJ/g9kG4zgD06S9d0wM6dEjw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-scroll-area/1.2.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-YSjEfBXnhUELsO2VzjdtYYD4CfQjvao+lhhrX5XsHD7/cyUNzljF1FHEbgTPN7LH2MClfwRMIsYlqTYpKTTe2A==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-presence': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-select/2.2.1_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-KVVf2ee/nTWIJRZLYvVlK23j555DuCCg4dPmRE4S5SqGEQf94qNH+X41pNz7LKnSEC7HbhBFyHYm2PpPfsOkrw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-dismissable-layer': 1.1.7_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-focus-guards': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-focus-scope': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-popper': 1.2.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-portal': 1.1.6_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-slot': 1.2.0_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-previous': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-visually-hidden': 1.2.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      aria-hidden: 1.2.4
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      react-remove-scroll: 2.6.3_dl52hbs33murufky2ojig52vdq
    dev: false

  /@radix-ui/react-separator/1.1.4_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-2fTm6PSiUm8YPq9W0E4reYuv01EE3aFSzt8edBiXqPHshF8N9+Kymt/k0/R+F3dkY5lQyB/zPtrP82phskLi7w==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-slider/1.3.1_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-emQJ7BC+V2OfygV53qlJ9O7fYND4oqKZ69qX/N8287lNUh+Ov8HrjLq0DpGq2odGOFkYZMfaDj529LCHQMvadA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-previous': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-size': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-slot/1.2.0_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-ujc+V6r0HNDviYqIK3rW4ffgYiZ8g5DEHrGJVk4x7kTlLXRDILnKX9vAUYeIsLOoDpDJ0ujpqMkjH4w2ofuo6w==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-slot/1.2.3_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-switch/1.2.1_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-AYGSGuEWLh4tylZ+7QexBI4XlkWvRjFiWRybkkSzOS7yvadaNp7vvI+oUXBPO9pspi/24CYEUml8HnKxMoGbuw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-previous': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-size': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-tabs/1.1.6_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-aglCVrMIYX8bLEobfep1Hv2sQ9rLzk9FF0pQh/cCSAzD+aaceFptVVEQNWkrchlBhqgXnhNhzFFJ2w4oB3XMBQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-presence': 1.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-roving-focus': 1.1.5_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-toast/1.2.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-gLFofr1xhNs/PQMdn2mAI5M+NzLm+y/9Qt6joABPG9zpAcraziC+X8t7DQsLF1y13S/oM9iBXD3Nc4iONiwSPQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-dismissable-layer': 1.1.7_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-portal': 1.1.6_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-presence': 1.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-visually-hidden': 1.2.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-toggle-group/1.1.5_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-Jks7EvKescOA3F59rCm3YpDdUMfQKVLWeYIjq3Blhmkex7OZIYcjVBV4+V13T/i04YdwTXeE5hGxyY9fxUKQNA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-direction': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-roving-focus': 1.1.5_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-toggle': 1.1.5_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-toggle/1.1.5_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-PvWWb4q5xRPcnaQowr+ux0Wq073P1FVaZeLWGpbjwFigOdPHpmP0VjvK8B+ZTomE3h28flRWjvODcQ6zUuSDqQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-use-controllable-state': 1.2.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-tooltip/1.2.7_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-Ap+fNYwKTYJ9pzqW+Xe2HtMRbQ/EeWkj2qykZ6SuEV4iS/o1bZI5ssJbk4D2r8XuDuOBVz/tIx2JObtuqU+5Zw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-context': 1.1.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-dismissable-layer': 1.1.10_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-popper': 1.2.7_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-portal': 1.1.9_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-presence': 1.1.4_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-primitive': 2.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-slot': 1.2.3_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-controllable-state': 1.2.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-visually-hidden': 1.2.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-use-callback-ref/1.1.1_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-use-controllable-state/1.2.1_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-Ef9+7lGO21GP9IV2Rk715KTBFrV1xfDrNSX1ix/rhvV8O3nAuXM3A1549dI8jr5zqS4xXX3vR+yp9+O37Gg23w==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-effect-event': 0.0.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-use-controllable-state/1.2.2_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-effect-event': 0.0.2_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-use-effect-event/0.0.1_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-GUYSVhFnN8ieFGIcckBHGV9OyOiIfK3Y7k/zNt1jM4vGlGNrvoyUnTwrbsZ+OP12Hzl5tEYzAOc98zF+TPRtFA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-use-effect-event/0.0.2_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-use-escape-keydown/1.1.1_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-use-layout-effect/1.1.1_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-use-previous/1.1.1_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-use-rect/1.1.1_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/rect': 1.1.1
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-use-size/1.1.1_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@types/react': 19.1.2
      react: 19.1.0
    dev: false

  /@radix-ui/react-visually-hidden/1.2.0_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-rQj0aAWOpCdCMRbI6pLQm8r7S2BM3YhTa0SzOYD55k+hJA8oo9J+H+9wLM9oMlZWOX/wJWPTzfDfmZkf7LvCfg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/react-visually-hidden/1.2.3_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-pzJq12tEaaIhqjbzpCuv/OypJY/BPavOofm+dbab+MHLajy277+1lLm6JFcGgF5eskJ6mquGirhXY2GD/8u8Ug==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.3_5j5q5giq4a6q5rwe55huzqxn5e
      '@types/react': 19.1.2
      '@types/react-dom': 19.1.2_@types+react@19.1.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /@radix-ui/rect/1.1.1:
    resolution: {integrity: sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw==}
    dev: false

  /@swc/counter/0.1.3:
    resolution: {integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==}
    dev: false

  /@swc/helpers/0.5.15:
    resolution: {integrity: sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==}
    dependencies:
      tslib: 2.8.1
    dev: false

  /@types/d3-array/3.2.1:
    resolution: {integrity: sha512-Y2Jn2idRrLzUfAKV2LyRImR+y4oa2AntrgID95SHJxuMUrkNXmanDSed71sRNZysveJVt1hLLemQZIady0FpEg==}
    dev: false

  /@types/d3-color/3.1.3:
    resolution: {integrity: sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==}
    dev: false

  /@types/d3-ease/3.0.2:
    resolution: {integrity: sha512-NcV1JjO5oDzoK26oMzbILE6HW7uVXOHLQvHshBUW4UMdZGfiY6v5BeQwh9a9tCzv+CeefZQHJt5SRgK154RtiA==}
    dev: false

  /@types/d3-interpolate/3.0.4:
    resolution: {integrity: sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==}
    dependencies:
      '@types/d3-color': 3.1.3
    dev: false

  /@types/d3-path/3.1.1:
    resolution: {integrity: sha512-VMZBYyQvbGmWyWVea0EHs/BwLgxc+MKi1zLDCONksozI4YJMcTt8ZEuIR4Sb1MMTE8MMW49v0IwI5+b7RmfWlg==}
    dev: false

  /@types/d3-scale/4.0.9:
    resolution: {integrity: sha512-dLmtwB8zkAeO/juAMfnV+sItKjlsw2lKdZVVy6LRr0cBmegxSABiLEpGVmSJJ8O08i4+sGR6qQtb6WtuwJdvVw==}
    dependencies:
      '@types/d3-time': 3.0.4
    dev: false

  /@types/d3-shape/3.1.7:
    resolution: {integrity: sha512-VLvUQ33C+3J+8p+Daf+nYSOsjB4GXp19/S/aGo60m9h1v6XaxjiT82lKVWJCfzhtuZ3yD7i/TPeC/fuKLLOSmg==}
    dependencies:
      '@types/d3-path': 3.1.1
    dev: false

  /@types/d3-time/3.0.4:
    resolution: {integrity: sha512-yuzZug1nkAAaBlBBikKZTgzCeA+k1uy4ZFwWANOfKw5z5LRhV0gNA7gNkKm7HoK+HRN0wX3EkxGk0fpbWhmB7g==}
    dev: false

  /@types/d3-timer/3.0.2:
    resolution: {integrity: sha512-Ps3T8E8dZDam6fUyNiMkekK3XUsaUEik+idO9/YjPtfj2qruF8tFBXS7XhtE4iIXBLxhmLjP3SXpLhVf21I9Lw==}
    dev: false

  /@types/node/22.14.1:
    resolution: {integrity: sha512-u0HuPQwe/dHrItgHHpmw3N2fYCR6x4ivMNbPHRkBVP4CvN+kiRrKHWk3i8tXiO/joPwXLMYvF9TTF0eqgHIuOw==}
    dependencies:
      undici-types: 6.21.0
    dev: true

  /@types/react-dom/19.1.2_@types+react@19.1.2:
    resolution: {integrity: sha512-XGJkWF41Qq305SKWEILa1O8vzhb3aOo3ogBlSmiqNko/WmRb6QIaweuZCXjKygVDXpzXb5wyxKTSOsmkuqj+Qw==}
    peerDependencies:
      '@types/react': ^19.0.0
    dependencies:
      '@types/react': 19.1.2

  /@types/react/19.1.2:
    resolution: {integrity: sha512-oxLPMytKchWGbnQM9O7D67uPa9paTNxO7jVoNMXgkkErULBPhPARCfkKL9ytcIJJRGjbsVwW4ugJzyFFvm/Tiw==}
    dependencies:
      csstype: 3.1.3

  /ansi-regex/5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  /ansi-regex/6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}

  /ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1

  /ansi-styles/6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  /any-promise/1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  /anymatch/3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  /arg/5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  /aria-hidden/1.2.4:
    resolution: {integrity: sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A==}
    engines: {node: '>=10'}
    dependencies:
      tslib: 2.8.1
    dev: false

  /autoprefixer/10.4.21_postcss@8.5.3:
    resolution: {integrity: sha512-O+A6LWV5LDHSJD3LjHYoNi4VLsj/Whi7k6zG12xTYaU4cQ8oxQGckXNX8cRHK5yOZ/ppVHe0ZBXGzSV9jXdVbQ==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      browserslist: 4.24.4
      caniuse-lite: 1.0.30001714
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
    dev: false

  /balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  /binary-extensions/2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  /brace-expansion/2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}
    dependencies:
      balanced-match: 1.0.2

  /braces/3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.1.1

  /browserslist/4.24.4:
    resolution: {integrity: sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001714
      electron-to-chromium: 1.5.138
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3_browserslist@4.24.4
    dev: false

  /bun-utilities/0.2.1:
    resolution: {integrity: sha512-2e4rMaA50zfUqvc3+Cu8I88nCzvF6WsOnxz4iZSSSQt/pBGgpiwgFCx2y5/s69C9iaCVfyrM6kHxYOgvVx1e8Q==}
    dev: true

  /bunx/0.1.0:
    resolution: {integrity: sha512-RKz/xk5NJ/oPlnafM9qjujUumy8I/yZL2w+LNu/JCJFty2WbeLhLlqWNnR7kEQFFYVxyZZGHT5c5U288fDM1Sg==}
    dependencies:
      bun-utilities: 0.2.1
    dev: true

  /busboy/1.6.0:
    resolution: {integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==}
    engines: {node: '>=10.16.0'}
    dependencies:
      streamsearch: 1.1.0
    dev: false

  /camelcase-css/2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}

  /caniuse-lite/1.0.30001714:
    resolution: {integrity: sha512-mtgapdwDLSSBnCI3JokHM7oEQBLxiJKVRtg10AxM1AyeiKcM96f0Mkbqeq+1AbiCtvMcHRulAAEMu693JrSWqg==}
    dev: false

  /chokidar/3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  /class-variance-authority/0.7.1:
    resolution: {integrity: sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==}
    dependencies:
      clsx: 2.1.1
    dev: false

  /client-only/0.0.1:
    resolution: {integrity: sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==}
    dev: false

  /clsx/2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}
    dev: false

  /cmdk/1.0.4_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-AnsjfHyHpQ/EFeAnG216WY7A5LiYCoZzCSygiLvfXC3H3LFGCprErteUcszaVluGOhuOTbJS3jWHrSDYPBBygg==}
    peerDependencies:
      react: ^18 || ^19 || ^19.0.0-rc
      react-dom: ^18 || ^19 || ^19.0.0-rc
    dependencies:
      '@radix-ui/react-dialog': 1.1.14_5j5q5giq4a6q5rwe55huzqxn5e
      '@radix-ui/react-id': 1.1.1_dl52hbs33murufky2ojig52vdq
      '@radix-ui/react-primitive': 2.1.0_5j5q5giq4a6q5rwe55huzqxn5e
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      use-sync-external-store: 1.5.0_react@19.1.0
    transitivePeerDependencies:
      - '@types/react'
      - '@types/react-dom'
    dev: false

  /color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4

  /color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  /color-string/1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    dev: false
    optional: true

  /color/4.2.3:
    resolution: {integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==}
    engines: {node: '>=12.5.0'}
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1
    dev: false
    optional: true

  /commander/4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}

  /cross-spawn/7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  /cssesc/3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  /csstype/3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  /d3-array/3.2.4:
    resolution: {integrity: sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==}
    engines: {node: '>=12'}
    dependencies:
      internmap: 2.0.3
    dev: false

  /d3-color/3.1.0:
    resolution: {integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==}
    engines: {node: '>=12'}
    dev: false

  /d3-ease/3.0.1:
    resolution: {integrity: sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==}
    engines: {node: '>=12'}
    dev: false

  /d3-format/3.1.0:
    resolution: {integrity: sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==}
    engines: {node: '>=12'}
    dev: false

  /d3-interpolate/3.0.1:
    resolution: {integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==}
    engines: {node: '>=12'}
    dependencies:
      d3-color: 3.1.0
    dev: false

  /d3-path/3.1.0:
    resolution: {integrity: sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==}
    engines: {node: '>=12'}
    dev: false

  /d3-scale/4.0.2:
    resolution: {integrity: sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==}
    engines: {node: '>=12'}
    dependencies:
      d3-array: 3.2.4
      d3-format: 3.1.0
      d3-interpolate: 3.0.1
      d3-time: 3.1.0
      d3-time-format: 4.1.0
    dev: false

  /d3-shape/3.2.0:
    resolution: {integrity: sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==}
    engines: {node: '>=12'}
    dependencies:
      d3-path: 3.1.0
    dev: false

  /d3-time-format/4.1.0:
    resolution: {integrity: sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==}
    engines: {node: '>=12'}
    dependencies:
      d3-time: 3.1.0
    dev: false

  /d3-time/3.1.0:
    resolution: {integrity: sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==}
    engines: {node: '>=12'}
    dependencies:
      d3-array: 3.2.4
    dev: false

  /d3-timer/3.0.1:
    resolution: {integrity: sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==}
    engines: {node: '>=12'}
    dev: false

  /date-fns/4.1.0:
    resolution: {integrity: sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==}
    dev: false

  /decimal.js-light/2.5.1:
    resolution: {integrity: sha512-qIMFpTMZmny+MMIitAB6D7iVPEorVw6YQRWkvarTkT4tBeSLLiHzcwj6q0MmYSFCiVpiqPJTJEYIrpcPzVEIvg==}
    dev: false

  /detect-libc/2.0.3:
    resolution: {integrity: sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==}
    engines: {node: '>=8'}
    dev: false
    optional: true

  /detect-node-es/1.1.0:
    resolution: {integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==}
    dev: false

  /didyoumean/1.2.2:
    resolution: {integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==}

  /dlv/1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}

  /dom-helpers/5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}
    dependencies:
      '@babel/runtime': 7.27.0
      csstype: 3.1.3
    dev: false

  /eastasianwidth/0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  /electron-to-chromium/1.5.138:
    resolution: {integrity: sha512-FWlQc52z1dXqm+9cCJ2uyFgJkESd+16j6dBEjsgDNuHjBpuIzL8/lRc0uvh1k8RNI6waGo6tcy2DvwkTBJOLDg==}
    dev: false

  /embla-carousel-react/8.5.1_react@19.1.0:
    resolution: {integrity: sha512-z9Y0K84BJvhChXgqn2CFYbfEi6AwEr+FFVVKm/MqbTQ2zIzO1VQri6w67LcfpVF0AjbhwVMywDZqY4alYkjW5w==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    dependencies:
      embla-carousel: 8.5.1
      embla-carousel-reactive-utils: 8.5.1_embla-carousel@8.5.1
      react: 19.1.0
    dev: false

  /embla-carousel-reactive-utils/8.5.1_embla-carousel@8.5.1:
    resolution: {integrity: sha512-n7VSoGIiiDIc4MfXF3ZRTO59KDp820QDuyBDGlt5/65+lumPHxX2JLz0EZ23hZ4eg4vZGUXwMkYv02fw2JVo/A==}
    peerDependencies:
      embla-carousel: 8.5.1
    dependencies:
      embla-carousel: 8.5.1
    dev: false

  /embla-carousel/8.5.1:
    resolution: {integrity: sha512-JUb5+FOHobSiWQ2EJNaueCNT/cQU9L6XWBbWmorWPQT9bkbk+fhsuLr8wWrzXKagO3oWszBO7MSx+GfaRk4E6A==}
    dev: false

  /emoji-regex/8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  /emoji-regex/9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  /escalade/3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}
    dev: false

  /eventemitter3/4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}
    dev: false

  /fast-equals/5.2.2:
    resolution: {integrity: sha512-V7/RktU11J3I36Nwq2JnZEM7tNm17eBJz+u25qdxBZeCKiX6BkVSZQjwWIr+IobgnZy+ag73tTZgZi7tr0LrBw==}
    engines: {node: '>=6.0.0'}
    dev: false

  /fast-glob/3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  /fastq/1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}
    dependencies:
      reusify: 1.1.0

  /fill-range/7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1

  /foreground-child/3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==}
    engines: {node: '>=14'}
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  /fraction.js/4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==}
    dev: false

  /framer-motion/12.7.4_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-jX0bPsTmU0oPZTYz/dVyD0dmOyEOEJvdn0TaZBE5I8g2GvVnnQnW9f65cJnoVfUkY3WZWNXGXnPbVA9YnaIfVA==}
    peerDependencies:
      '@emotion/is-prop-valid': '*'
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/is-prop-valid':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
    dependencies:
      motion-dom: 12.7.4
      motion-utils: 12.7.2
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      tslib: 2.8.1
    dev: false

  /fsevents/2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    optional: true

  /function-bind/1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  /get-nonce/1.0.1:
    resolution: {integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==}
    engines: {node: '>=6'}
    dev: false

  /glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3

  /glob-parent/6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3

  /glob/10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  /hasown/2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2

  /immer/10.1.1:
    resolution: {integrity: sha512-s2MPrmjovJcoMaHtx6K11Ra7oD05NT97w1IC5zpMkT6Atjr7H8LjaDd81iIxUYpMKSRRNMJE703M1Fhr/TctHw==}
    dev: false

  /input-otp/1.4.1_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-+yvpmKYKHi9jIGngxagY9oWiiblPB7+nEO75F2l2o4vs+6vpPZZmUl4tBNYuTCvQjhvEIbdNeJu70bhfYP2nbw==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /internmap/2.0.3:
    resolution: {integrity: sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==}
    engines: {node: '>=12'}
    dev: false

  /is-arrayish/0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}
    dev: false
    optional: true

  /is-binary-path/2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.3.0

  /is-core-module/2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2

  /is-extglob/2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  /is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  /is-glob/4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1

  /is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  /isexe/2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  /jackspeak/3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  /jiti/1.21.7:
    resolution: {integrity: sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==}
    hasBin: true

  /js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}
    dev: false

  /lilconfig/3.1.3:
    resolution: {integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==}
    engines: {node: '>=14'}

  /lines-and-columns/1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  /lodash/4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: false

  /loose-envify/1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0
    dev: false

  /lru-cache/10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}

  /lucide-react/0.454.0_react@19.1.0:
    resolution: {integrity: sha512-hw7zMDwykCLnEzgncEEjHeA6+45aeEzRYuKHuyRSOPkhko+J3ySGjGIzu+mmMfDFG1vazHepMaYFYHbTFAZAAQ==}
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0-rc
    dependencies:
      react: 19.1.0
    dev: false

  /merge2/1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  /micromatch/4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  /minimatch/9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.1

  /minipass/7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  /motion-dom/12.7.4:
    resolution: {integrity: sha512-1ZUHAoSUMMxP6jPqyxlk9XUfb6NxMsnWPnH2YGhrOhTURLcXWbETi6eemoKb60Pe32NVJYduL4B62VQSO5Jq8Q==}
    dependencies:
      motion-utils: 12.7.2
    dev: false

  /motion-utils/12.7.2:
    resolution: {integrity: sha512-XhZwqctxyJs89oX00zn3OGCuIIpVevbTa+u82usWBC6pSHUd2AoNWiYa7Du8tJxJy9TFbZ82pcn5t7NOm1PHAw==}
    dev: false

  /motion/12.7.4_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-MBGrMbYageHw4iZJn+pGTr7abq5n53jCxYkhFC1It3vYukQPRWg5zij46MnwYGpLR8KG465MLHSASXot9edYOw==}
    peerDependencies:
      '@emotion/is-prop-valid': '*'
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/is-prop-valid':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
    dependencies:
      framer-motion: 12.7.4_j6k6oay3ugsr56slyfvma2drry
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      tslib: 2.8.1
    dev: false

  /mz/2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  /nanoid/3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  /next-themes/0.4.6_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-pZvgD5L0IEvX5/9GWyHMf3m8BKiVQwsCMHfoFosXtXBMnaS0ZnIJ9ST4b4NqLVKDEm8QBxoNNGNaBv2JNF6XNA==}
    peerDependencies:
      react: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc
      react-dom: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /next/15.2.4_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-VwL+LAaPSxEkd3lU2xWbgEOtrM8oedmyhBqaVNmgKB+GvZlCy9rgaEc+y2on0wv+l0oSFqLtYD6dcC1eAedUaQ==}
    engines: {node: ^18.18.0 || ^19.8.0 || >= 20.0.0}
    hasBin: true
    peerDependencies:
      '@opentelemetry/api': ^1.1.0
      '@playwright/test': ^1.41.2
      babel-plugin-react-compiler: '*'
      react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
      react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
      sass: ^1.3.0
    peerDependenciesMeta:
      '@opentelemetry/api':
        optional: true
      '@playwright/test':
        optional: true
      babel-plugin-react-compiler:
        optional: true
      sass:
        optional: true
    dependencies:
      '@next/env': 15.2.4
      '@swc/counter': 0.1.3
      '@swc/helpers': 0.5.15
      busboy: 1.6.0
      caniuse-lite: 1.0.30001714
      postcss: 8.4.31
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      styled-jsx: 5.1.6_react@19.1.0
    optionalDependencies:
      '@next/swc-darwin-arm64': 15.2.4
      '@next/swc-darwin-x64': 15.2.4
      '@next/swc-linux-arm64-gnu': 15.2.4
      '@next/swc-linux-arm64-musl': 15.2.4
      '@next/swc-linux-x64-gnu': 15.2.4
      '@next/swc-linux-x64-musl': 15.2.4
      '@next/swc-win32-arm64-msvc': 15.2.4
      '@next/swc-win32-x64-msvc': 15.2.4
      sharp: 0.33.5
    transitivePeerDependencies:
      - '@babel/core'
      - babel-plugin-macros
    dev: false

  /node-releases/2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}
    dev: false

  /normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  /normalize-range/0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /object-assign/4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  /object-hash/3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}

  /package-json-from-dist/1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==}

  /path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  /path-parse/1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  /path-scurry/1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  /picocolors/1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  /picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  /pify/2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}

  /pirates/4.0.7:
    resolution: {integrity: sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==}
    engines: {node: '>= 6'}

  /postcss-import/15.1.0_postcss@8.5.3:
    resolution: {integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.10

  /postcss-js/4.0.1_postcss@8.5.3:
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.5.3

  /postcss-load-config/4.0.2_postcss@8.5.3:
    resolution: {integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true
    dependencies:
      lilconfig: 3.1.3
      postcss: 8.5.3
      yaml: 2.7.1

  /postcss-nested/6.2.0_postcss@8.5.3:
    resolution: {integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  /postcss-selector-parser/6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  /postcss-value-parser/4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  /postcss/8.4.31:
    resolution: {integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1
    dev: false

  /postcss/8.5.3:
    resolution: {integrity: sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  /preact/10.12.1:
    resolution: {integrity: sha512-l8386ixSsBdbreOAkqtrwqHwdvR35ID8c3rKPa8lCWuO86dBi32QWHV4vfsZK1utLLFMvw+Z5Ad4XLkZzchscg==}
    dev: false

  /prop-types/15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1
    dev: false

  /queue-microtask/1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  /react-day-picker/8.10.1_azwu7r6w53kir3xxtk6jezvccy:
    resolution: {integrity: sha512-TMx7fNbhLk15eqcMt+7Z7S2KF7mfTId/XJDjKE8f+IUcFn0l08/kI4FiYTL/0yuOLmEcbR4Fwe3GJf/NiiMnPA==}
    peerDependencies:
      date-fns: ^2.28.0 || ^3.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      date-fns: 4.1.0
      react: 19.1.0
    dev: false

  /react-dom/19.1.0_react@19.1.0:
    resolution: {integrity: sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g==}
    peerDependencies:
      react: ^19.1.0
    dependencies:
      react: 19.1.0
      scheduler: 0.26.0
    dev: false

  /react-hook-form/7.55.0_react@19.1.0:
    resolution: {integrity: sha512-XRnjsH3GVMQz1moZTW53MxfoWN7aDpUg/GpVNc4A3eXRVNdGXfbzJ4vM4aLQ8g6XCUh1nIbx70aaNCl7kxnjog==}
    engines: {node: '>=18.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17 || ^18 || ^19
    dependencies:
      react: 19.1.0
    dev: false

  /react-is/16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}
    dev: false

  /react-is/18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}
    dev: false

  /react-remove-scroll-bar/2.3.8_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      react: 19.1.0
      react-style-singleton: 2.2.3_dl52hbs33murufky2ojig52vdq
      tslib: 2.8.1
    dev: false

  /react-remove-scroll/2.6.3_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-pnAi91oOk8g8ABQKGF5/M9qxmmOPxaAnopyTHYfqYEwJhyFrbbBtHuSgtKEoH0jpcxx5o3hXqH1mNd9/Oi+8iQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      react: 19.1.0
      react-remove-scroll-bar: 2.3.8_dl52hbs33murufky2ojig52vdq
      react-style-singleton: 2.2.3_dl52hbs33murufky2ojig52vdq
      tslib: 2.8.1
      use-callback-ref: 1.3.3_dl52hbs33murufky2ojig52vdq
      use-sidecar: 1.1.3_dl52hbs33murufky2ojig52vdq
    dev: false

  /react-resizable-panels/2.1.7_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-JtT6gI+nURzhMYQYsx8DKkx6bSoOGFp7A3CwMrOb8y5jFHFyqwo9m68UhmXRw57fRVJksFn1TSlm3ywEQ9vMgA==}
    peerDependencies:
      react: ^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /react-smooth/4.0.4_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-gnGKTpYwqL0Iii09gHobNolvX4Kiq4PKx6eWBCYYix+8cdw+cGo3do906l1NBPKkSWx1DghC1dlWG9L2uGd61Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      fast-equals: 5.2.2
      prop-types: 15.8.1
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      react-transition-group: 4.4.5_j6k6oay3ugsr56slyfvma2drry
    dev: false

  /react-style-singleton/2.2.3_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      get-nonce: 1.0.1
      react: 19.1.0
      tslib: 2.8.1
    dev: false

  /react-transition-group/4.4.5_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'
    dependencies:
      '@babel/runtime': 7.27.0
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /react/19.1.0:
    resolution: {integrity: sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /read-cache/1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}
    dependencies:
      pify: 2.3.0

  /readdirp/3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1

  /recharts-scale/0.4.5:
    resolution: {integrity: sha512-kivNFO+0OcUNu7jQquLXAxz1FIwZj8nrj+YkOKc5694NbjCvcT6aSZiIzNzd2Kul4o4rTto8QVR9lMNtxD4G1w==}
    dependencies:
      decimal.js-light: 2.5.1
    dev: false

  /recharts/2.15.0_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-cIvMxDfpAmqAmVgc4yb7pgm/O1tmmkl/CjrvXuW+62/+7jj/iF9Ykm+hb/UJt42TREHMyd3gb+pkgoa2MxgDIw==}
    engines: {node: '>=14'}
    peerDependencies:
      react: ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      clsx: 2.1.1
      eventemitter3: 4.0.7
      lodash: 4.17.21
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
      react-is: 18.3.1
      react-smooth: 4.0.4_j6k6oay3ugsr56slyfvma2drry
      recharts-scale: 0.4.5
      tiny-invariant: 1.3.3
      victory-vendor: 36.9.2
    dev: false

  /regenerator-runtime/0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}
    dev: false

  /resolve/1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  /reusify/1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  /run-parallel/1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3

  /scheduler/0.26.0:
    resolution: {integrity: sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==}
    dev: false

  /semver/7.7.1:
    resolution: {integrity: sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==}
    engines: {node: '>=10'}
    hasBin: true
    dev: false
    optional: true

  /sharp/0.33.5:
    resolution: {integrity: sha512-haPVm1EkS9pgvHrQ/F3Xy+hgcuMV0Wm9vfIBSiwZ05k+xgb0PkBQpGsAA/oWdDobNaZTH5ppvHtzCFbnSEwHVw==}
    engines: {node: ^18.17.0 || ^20.3.0 || >=21.0.0}
    requiresBuild: true
    dependencies:
      color: 4.2.3
      detect-libc: 2.0.3
      semver: 7.7.1
    optionalDependencies:
      '@img/sharp-darwin-arm64': 0.33.5
      '@img/sharp-darwin-x64': 0.33.5
      '@img/sharp-libvips-darwin-arm64': 1.0.4
      '@img/sharp-libvips-darwin-x64': 1.0.4
      '@img/sharp-libvips-linux-arm': 1.0.5
      '@img/sharp-libvips-linux-arm64': 1.0.4
      '@img/sharp-libvips-linux-s390x': 1.0.4
      '@img/sharp-libvips-linux-x64': 1.0.4
      '@img/sharp-libvips-linuxmusl-arm64': 1.0.4
      '@img/sharp-libvips-linuxmusl-x64': 1.0.4
      '@img/sharp-linux-arm': 0.33.5
      '@img/sharp-linux-arm64': 0.33.5
      '@img/sharp-linux-s390x': 0.33.5
      '@img/sharp-linux-x64': 0.33.5
      '@img/sharp-linuxmusl-arm64': 0.33.5
      '@img/sharp-linuxmusl-x64': 0.33.5
      '@img/sharp-wasm32': 0.33.5
      '@img/sharp-win32-ia32': 0.33.5
      '@img/sharp-win32-x64': 0.33.5
    dev: false
    optional: true

  /shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0

  /shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  /signal-exit/4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  /simple-swizzle/0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}
    dependencies:
      is-arrayish: 0.3.2
    dev: false
    optional: true

  /sonner/1.7.4_j6k6oay3ugsr56slyfvma2drry:
    resolution: {integrity: sha512-DIS8z4PfJRbIyfVFDVnK9rO3eYDtse4Omcm6bt0oEr5/jtLgysmjuBl1frJ9E/EQZrFmKx2A8m/s5s9CRXIzhw==}
    peerDependencies:
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    dev: false

  /source-map-js/1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  /streamsearch/1.1.0:
    resolution: {integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==}
    engines: {node: '>=10.0.0'}
    dev: false

  /string-width/4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  /string-width/5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  /strip-ansi/6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1

  /strip-ansi/7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: 6.1.0

  /styled-jsx/5.1.6_react@19.1.0:
    resolution: {integrity: sha512-qSVyDTeMotdvQYoHWLNGwRFJHC+i+ZvdBRYosOFgC+Wg1vx4frN2/RG/NA7SYqqvKNLf39P2LSRA2pu6n0XYZA==}
    engines: {node: '>= 12.0.0'}
    peerDependencies:
      '@babel/core': '*'
      babel-plugin-macros: '*'
      react: '>= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      babel-plugin-macros:
        optional: true
    dependencies:
      client-only: 0.0.1
      react: 19.1.0
    dev: false

  /sucrase/3.35.0:
    resolution: {integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.7
      ts-interface-checker: 0.1.13

  /supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  /tailwind-merge/2.6.0:
    resolution: {integrity: sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==}
    dev: false

  /tailwindcss-animate/1.0.7_tailwindcss@3.4.17:
    resolution: {integrity: sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders'
    dependencies:
      tailwindcss: 3.4.17
    dev: false

  /tailwindcss/3.4.17:
    resolution: {integrity: sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.3
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.7
      lilconfig: 3.1.3
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.1.1
      postcss: 8.5.3
      postcss-import: 15.1.0_postcss@8.5.3
      postcss-js: 4.0.1_postcss@8.5.3
      postcss-load-config: 4.0.2_postcss@8.5.3
      postcss-nested: 6.2.0_postcss@8.5.3
      postcss-selector-parser: 6.1.2
      resolve: 1.22.10
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node

  /thenify-all/1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}
    dependencies:
      thenify: 3.3.1

  /thenify/3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}
    dependencies:
      any-promise: 1.3.0

  /tiny-invariant/1.3.3:
    resolution: {integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==}
    dev: false

  /to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0

  /ts-interface-checker/0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}

  /tslib/2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}
    dev: false

  /typescript/5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true
    dev: true

  /undici-types/6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}
    dev: true

  /update-browserslist-db/1.1.3_browserslist@4.24.4:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1
    dev: false

  /use-callback-ref/1.3.3_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      react: 19.1.0
      tslib: 2.8.1
    dev: false

  /use-sidecar/1.1.3_dl52hbs33murufky2ojig52vdq:
    resolution: {integrity: sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 19.1.2
      detect-node-es: 1.1.0
      react: 19.1.0
      tslib: 2.8.1
    dev: false

  /use-sync-external-store/1.5.0_react@19.1.0:
    resolution: {integrity: sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      react: 19.1.0
    dev: false

  /util-deprecate/1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  /uuid/11.1.0:
    resolution: {integrity: sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==}
    hasBin: true
    dev: false

  /vaul/0.9.9_5j5q5giq4a6q5rwe55huzqxn5e:
    resolution: {integrity: sha512-7afKg48srluhZwIkaU+lgGtFCUsYBSGOl8vcc8N/M3YQlZFlynHD15AE+pwrYdc826o7nrIND4lL9Y6b9WWZZQ==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@radix-ui/react-dialog': 1.1.14_5j5q5giq4a6q5rwe55huzqxn5e
      react: 19.1.0
      react-dom: 19.1.0_react@19.1.0
    transitivePeerDependencies:
      - '@types/react'
      - '@types/react-dom'
    dev: false

  /victory-vendor/36.9.2:
    resolution: {integrity: sha512-PnpQQMuxlwYdocC8fIJqVXvkeViHYzotI+NJrCuav0ZYFoq912ZHBk3mCeuj+5/VpodOjPe1z0Fk2ihgzlXqjQ==}
    dependencies:
      '@types/d3-array': 3.2.1
      '@types/d3-ease': 3.0.2
      '@types/d3-interpolate': 3.0.4
      '@types/d3-scale': 4.0.9
      '@types/d3-shape': 3.1.7
      '@types/d3-time': 3.0.4
      '@types/d3-timer': 3.0.2
      d3-array: 3.2.4
      d3-ease: 3.0.1
      d3-interpolate: 3.0.1
      d3-scale: 4.0.2
      d3-shape: 3.2.0
      d3-time: 3.1.0
      d3-timer: 3.0.1
    dev: false

  /which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0

  /wrap-ansi/7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  /wrap-ansi/8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  /yaml/2.7.1:
    resolution: {integrity: sha512-10ULxpnOCQXxJvBgxsn9ptjq6uviG/htZKk9veJGhlqn3w/DxQ631zFF+nlQXLwmImeS5amR2dl2U8sg6U9jsQ==}
    engines: {node: '>= 14'}
    hasBin: true

  /zod/3.24.3:
    resolution: {integrity: sha512-HhY1oqzWCQWuUqvBFnsyrtZRhyPeR7SUGv+C4+MsisMuVfSPx8HpwWqH8tRahSlt6M3PiFAcoeFhZAqIXTxoSg==}
    dev: false

  /zustand/5.0.6_ymjxa3ljbsttey3xxcr7vsvgzu:
    resolution: {integrity: sha512-ihAqNeUVhe0MAD+X8M5UzqyZ9k3FFZLBTtqo6JLPwV53cbRB/mJwBI0PxcIgqhBBHlEs8G45OTDTMq3gNcLq3A==}
    engines: {node: '>=12.20.0'}
    peerDependencies:
      '@types/react': '>=18.0.0'
      immer: '>=9.0.6'
      react: '>=18.0.0'
      use-sync-external-store: '>=1.2.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true
      use-sync-external-store:
        optional: true
    dependencies:
      '@types/react': 19.1.2
      immer: 10.1.1
      react: 19.1.0
      use-sync-external-store: 1.5.0_react@19.1.0
    dev: false
