{"name": "@booking-calendar/spa", "version": "6.4.2", "author": "<PERSON>", "homepage": "https://conventionsuite.com", "private": true, "type": "module", "scripts": {"dev": "vite", "dev-spa": "vite", "server": "cd ./api && npm start", "build": "tsc && vite build", "start": "vite preview", "lint": "npx oxlint && eslint ./src", "clean": "rm -rf dist", "lint-fix": "eslint --fix ./src", "format:write": "prettier --write \"**/*.{js,jsx,ts,tsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,mdx}\" --cache", "spaPostBuild": "node spaPostBuild.cjs -U", "build-spa": "npm run clean && npm run build && npm run spaPostBuild", "build-spa-bypass": "npm run clean && vite build && npm run spaPostBuild"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "browser": {"crypto": false}, "dependencies": {"@bryntum/calendar-react-thin": "6.0.4", "@bryntum/calendar-thin": "6.0.4", "@bryntum/core-react-thin": "6.0.4", "@bryntum/core-thin": "6.0.4", "@bryntum/engine-thin": "6.0.4", "@bryntum/grid-thin": "6.0.4", "@bryntum/scheduler-react-thin": "6.0.4", "@bryntum/scheduler-thin": "6.0.4", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/inter": "^5.0.18", "@fontsource/plus-jakarta-sans": "^5.0.20", "@fontsource/roboto-mono": "^5.0.18", "@mui/icons-material": "^5.15.16", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.16", "@mui/system": "^5.15.15", "@mui/x-date-pickers": "^7.3.2", "@mui/x-date-pickers-pro": "^7.3.2", "@untitled-ui/icons-react": "^0.1.2", "@vitejs/plugin-react-swc": "^3.6.0", "apexcharts": "^3.49.0", "axios": "1.7.2", "axios-oauth-1.0a": "^0.3.6", "date-fns": "*", "i18next": "^23.11.3", "js-cookie": "^3.0.5", "lodash.isequal": "^4.5.0", "moment-timezone": "^0.5.45", "nprogress": "^0.2.0", "numeral": "^2.0.6", "prop-types": "^15.8.1", "react": "^18.3.1", "react-apexcharts": "^1.4.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.4.1", "react-i18next": "^14.1.1", "react-router": "^6.23.0", "react-router-dom": "^6.23.0", "react-slick": "^0.30.2", "react-split": "^2.0.14", "react-split-grid": "^1.0.4", "request": "^2.88.2", "sass": "^1.77.0", "simplebar-react": "^3.2.5", "slick-carousel": "^1.8.1", "stylis": "^4.3.2", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.2.5", "zustand": "^4.5.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.13", "@types/js-cookie": "^3.0.6", "@types/lodash.isequal": "^4.5.8", "@types/node": "^20.12.10", "@types/nprogress": "^0.2.3", "@types/numeral": "^2.0.5", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "clsx": "^2.1.1", "crypto-browserify": "^3.12.0", "crypto-js": "^4.2.0", "daisyui": "^4.11.1", "dotenv": "^16.4.5", "eslint": "^9.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.6", "express": "^4.19.2", "fs-extra": "^11.2.0", "oauth-1.0a": "^2.2.6", "postcss": "^8.4.38", "postcss-loader": "^8.1.1", "prettier": "^3.2.5", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "vite": "^5.2.11", "vite-plugin-pwa": "^0.20.1", "vite-plugin-singlefile": "^2.0.1", "yarn-upgrade-all": "^0.7.2"}}