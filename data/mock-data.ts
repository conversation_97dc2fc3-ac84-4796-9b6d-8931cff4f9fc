import type { Booking, Venue, Building, Room } from "@/types/calendar"

// Define venues first
export const venues: Venue[] = [
  { id: "venue1", name: "Main Venue", color: "#4285F4", isSelected: true, isExpanded: true },
  { id: "venue2", name: "Conference Center", color: "#34A853", isSelected: true, isExpanded: true },
  { id: "venue3", name: "Event Hall", color: "#EA4335", isSelected: true, isExpanded: true },
]

export const buildings: Building[] = [
  { id: "building1", name: "Main Campus", isExpanded: true, isSelected: true },
  { id: "building2", name: "East Wing", isExpanded: true, isSelected: true },
  { id: "building3", name: "West Wing", isExpanded: true, isSelected: true },
]

// Update rooms to include venueId
export const rooms: Room[] = [
  // Main Venue rooms
  { id: "room1", name: "Main Hall", color: "#4285F4", isSelected: true, buildingId: "building1", venueId: "venue1" },
  {
    id: "room2",
    name: "Conference Room A",
    color: "#4285F4",
    isSelected: true,
    buildingId: "building1",
    venueId: "venue1",
  },
  {
    id: "room3",
    name: "Conference Room B",
    color: "#4285F4",
    isSelected: true,
    buildingId: "building1",
    venueId: "venue1",
  },

  // Conference Center rooms
  { id: "room4", name: "Auditorium", color: "#34A853", isSelected: true, buildingId: "building2", venueId: "venue2" },
  { id: "room5", name: "Studio", color: "#34A853", isSelected: true, buildingId: "building2", venueId: "venue2" },

  // Event Hall rooms
  {
    id: "room6",
    name: "Meeting Room 101",
    color: "#EA4335",
    isSelected: true,
    buildingId: "building3",
    venueId: "venue3",
  },
  {
    id: "room7",
    name: "Meeting Room 102",
    color: "#EA4335",
    isSelected: true,
    buildingId: "building3",
    venueId: "venue3",
  },
  {
    id: "room8",
    name: "Training Room",
    color: "#EA4335",
    isSelected: true,
    buildingId: "building3",
    venueId: "venue3",
  },
]

// Generate bookings for the next 30 days
export const generateMockBookings = (): Booking[] => {
  const bookings: Booking[] = []
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  for (let i = 0; i < 50; i++) {
    const room = rooms[Math.floor(Math.random() * rooms.length)]
    const venueId = room.venueId
    const roomId = room.id

    // Create a new Date object for the start day
    const startDay = new Date(today)
    startDay.setDate(today.getDate() + Math.floor(Math.random() * 30))

    const startHour = 8 + Math.floor(Math.random() * 10) // Between 8 AM and 6 PM
    const durationHours = 1 + Math.floor(Math.random() * 3) // 1 to 3 hours

    // Create proper Date objects for start and end
    const start = new Date(startDay)
    start.setHours(startHour, 0, 0, 0)

    const end = new Date(start)
    end.setHours(start.getHours() + durationHours)

    const isAllDay = Math.random() > 0.9 // 10% chance of all-day event

    const eventTypes = [
      "Meeting",
      "Conference",
      "Workshop",
      "Presentation",
      "Concert",
      "Exhibition",
      "Wedding",
      "Corporate Event",
      "Birthday Party",
      "Seminar",
    ]

    const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)]
    const clientNames = ["Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller", "Wilson"]
    const clientName = clientNames[Math.floor(Math.random() * clientNames.length)]

    // Create a new Date object for all-day events
    const allDayStart = new Date(startDay)
    allDayStart.setHours(0, 0, 0, 0)

    const allDayEnd = new Date(startDay)
    allDayEnd.setHours(23, 59, 59, 999)

    bookings.push({
      id: `booking-${i}`,
      title: `${eventType} - ${clientName}`,
      start: isAllDay ? allDayStart : start,
      end: isAllDay ? allDayEnd : end,
      venueId,
      roomId,
      description: `${eventType} for ${clientName}`,
      isAllDay,
    })
  }

  return bookings
}

export const mockBookings = generateMockBookings()

