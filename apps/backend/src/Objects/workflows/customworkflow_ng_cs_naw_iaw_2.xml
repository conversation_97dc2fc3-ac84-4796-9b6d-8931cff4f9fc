<workflow scriptid="customworkflow_ng_cs_naw_iaw_2">
  <description></description>
  <initcontexts></initcontexts>
  <initeventtypes></initeventtypes>
  <initlocalizationcontext></initlocalizationcontext>
  <initoncreate>T</initoncreate>
  <initonvieworupdate>F</initonvieworupdate>
  <initsavedsearchcondition></initsavedsearchcondition>
  <inittriggertype></inittriggertype>
  <isinactive>F</isinactive>
  <islogenabled>F</islogenabled>
  <keephistory>ALWAYS</keephistory>
  <name>CS Invoice Approval Workflow</name>
  <recordtypes>INVOICE</recordtypes>
  <releasestatus>SUSPENDED</releasestatus>
  <runasadmin>T</runasadmin>
  <initcondition>
    <formula><![CDATA[isEmpty("User:Supervisor") = 'F']]></formula>
    <type>VISUAL_BUILDER</type>
    <parameters>
      <parameter>
        <name>User:Supervisor</name>
        <value>STDUSERUSER:STDENTITYSUPERVISOR</value>
      </parameter>
    </parameters>
  </initcondition>
  <workflowcustomfields>
    <workflowcustomfield scriptid="custworkflow_iaw_exception_flag_2">
      <applyformatting>F</applyformatting>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displaytype>NORMAL</displaytype>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CHECKBOX</fieldtype>
      <help></help>
      <label>[IAW] exception flag</label>
      <linktext></linktext>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <selectrecordtype></selectrecordtype>
      <storevalue>T</storevalue>
    </workflowcustomfield>
    <workflowcustomfield scriptid="custworkflow_iaw_invoice_creator_2">
      <applyformatting>F</applyformatting>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displaytype>NORMAL</displaytype>
      <dynamicdefault></dynamicdefault>
      <fieldtype>SELECT</fieldtype>
      <help></help>
      <label>[IAW] Invoice Creator</label>
      <linktext></linktext>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <selectrecordtype>-4</selectrecordtype>
      <storevalue>T</storevalue>
    </workflowcustomfield>
    <workflowcustomfield scriptid="custworkflow_iaw_app_routing_pref_2">
      <applyformatting>T</applyformatting>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displaytype>NORMAL</displaytype>
      <dynamicdefault></dynamicdefault>
      <fieldtype>INTEGER</fieldtype>
      <help></help>
      <label>[IAW] Invoice Approval Routing</label>
      <linktext></linktext>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <selectrecordtype></selectrecordtype>
      <storevalue>T</storevalue>
    </workflowcustomfield>
    <workflowcustomfield scriptid="custworkflow_iaw_custval_pref_2">
      <applyformatting>F</applyformatting>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displaytype>NORMAL</displaytype>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CHECKBOX</fieldtype>
      <help></help>
      <label>[PREF] Customer Validation</label>
      <linktext></linktext>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <selectrecordtype></selectrecordtype>
      <storevalue>T</storevalue>
    </workflowcustomfield>
    <workflowcustomfield scriptid="custworkflow_iaw_taxamount_pref_2">
      <applyformatting>F</applyformatting>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displaytype>NORMAL</displaytype>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CHECKBOX</fieldtype>
      <help></help>
      <label>[PREF] Tax Amount check</label>
      <linktext></linktext>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <selectrecordtype></selectrecordtype>
      <storevalue>T</storevalue>
    </workflowcustomfield>
    <workflowcustomfield scriptid="custworkflow_iaw_custinvneedapp_val_pref_2">
      <applyformatting>F</applyformatting>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displaytype>NORMAL</displaytype>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CHECKBOX</fieldtype>
      <help></help>
      <label>[PREF] Customer Invoice Needs Approval</label>
      <linktext></linktext>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <selectrecordtype></selectrecordtype>
      <storevalue>T</storevalue>
    </workflowcustomfield>
    <workflowcustomfield scriptid="custworkflow_iaw_first_inv_val_pref_2">
      <applyformatting>F</applyformatting>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displaytype>NORMAL</displaytype>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CHECKBOX</fieldtype>
      <help></help>
      <label>[PREF] Customer 1st Invoice Validation</label>
      <linktext></linktext>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <selectrecordtype></selectrecordtype>
      <storevalue>T</storevalue>
    </workflowcustomfield>
    <workflowcustomfield scriptid="custworkflow_iaw_setflag_1st_inv_2">
      <applyformatting>F</applyformatting>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displaytype>NORMAL</displaytype>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CHECKBOX</fieldtype>
      <help></help>
      <label>[IAW] Is Customers First Invoice</label>
      <linktext></linktext>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <selectrecordtype></selectrecordtype>
      <storevalue>T</storevalue>
    </workflowcustomfield>
    <workflowcustomfield scriptid="custworkflow_iaw_exceptionrec_id_2">
      <applyformatting>T</applyformatting>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displaytype>NORMAL</displaytype>
      <dynamicdefault></dynamicdefault>
      <fieldtype>INTEGER</fieldtype>
      <help></help>
      <label>[IAW] Exceptions Record ID</label>
      <linktext></linktext>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <selectrecordtype></selectrecordtype>
      <storevalue>T</storevalue>
    </workflowcustomfield>
    <workflowcustomfield scriptid="custworkflow_iaw_termsval_pref_2">
      <applyformatting>F</applyformatting>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displaytype>NORMAL</displaytype>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CHECKBOX</fieldtype>
      <help></help>
      <label>[PREF] Terms Validation</label>
      <linktext></linktext>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <selectrecordtype></selectrecordtype>
      <storevalue>T</storevalue>
    </workflowcustomfield>
  </workflowcustomfields>
  <workflowstates>
    <workflowstate scriptid="workflowstate_iaw_st_routing_chk">
      <description></description>
      <donotexitworkflow>F</donotexitworkflow>
      <name>Approval Routing Feature Check</name>
      <positionx>243</positionx>
      <positiony>133</positiony>
      <workflowactions triggertype="ONENTRY">
        <customaction scriptid="workflowaction_iaw_act_user_get_pref">
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <resultfield>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_routing_chk.custwfstate_iaw_approvalrouting_2]</resultfield>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <scripttype>[scriptid=customscript_naw_ws_get_user_pref]</scripttype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
          <parametersettings>
            <parametersetting>
              <targetparameter>[scriptid=customscript_naw_ws_get_user_pref.custscript_naw_pref_type]</targetparameter>
              <valuechecked></valuechecked>
              <valuedate></valuedate>
              <valuefield></valuefield>
              <valueformula></valueformula>
              <valuejoinfield></valuejoinfield>
              <valueselect></valueselect>
              <valuetext>accountingpreferences</valuetext>
            </parametersetting>
            <parametersetting>
              <targetparameter>[scriptid=customscript_naw_ws_get_user_pref.custscript_naw_pref_id]</targetparameter>
              <valuechecked></valuechecked>
              <valuedate></valuedate>
              <valuefield></valuefield>
              <valueformula></valueformula>
              <valuejoinfield></valuejoinfield>
              <valueselect></valueselect>
              <valuetext>CUSTOMAPPROVALCUSTINVC</valuetext>
            </parametersetting>
          </parametersettings>
        </customaction>
      </workflowactions>
      <workflowstatecustomfields>
        <workflowstatecustomfield scriptid="custwfstate_iaw_approvalrouting_2">
          <applyformatting>T</applyformatting>
          <defaultchecked>F</defaultchecked>
          <defaultselection></defaultselection>
          <defaultvalue></defaultvalue>
          <description></description>
          <displaytype>NORMAL</displaytype>
          <dynamicdefault></dynamicdefault>
          <fieldtype>INTEGER</fieldtype>
          <help></help>
          <label>Approval routing</label>
          <linktext></linktext>
          <maxvalue></maxvalue>
          <minvalue></minvalue>
          <selectrecordtype></selectrecordtype>
          <storevalue>T</storevalue>
        </workflowstatecustomfield>
      </workflowstatecustomfields>
      <workflowtransitions>
        <workflowtransition scriptid="workflowtransition188">
          <buttonaction></buttonaction>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <scheduledelay></scheduledelay>
          <scheduletimeunit></scheduletimeunit>
          <tostate>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_invoice_val]</tostate>
          <triggertype></triggertype>
          <waitforworkflow></waitforworkflow>
          <waitforworkflowstate></waitforworkflowstate>
          <initcondition>
            <formula><![CDATA["State:Approval routing" = 1]]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>State:Approval routing</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_routing_chk.custwfstate_iaw_approvalrouting_2]</value>
              </parameter>
            </parameters>
          </initcondition>
        </workflowtransition>
      </workflowtransitions>
    </workflowstate>
    <workflowstate scriptid="workflowstate_iaw_st_invoice_val">
      <description></description>
      <donotexitworkflow>F</donotexitworkflow>
      <name>Invoice Validation</name>
      <positionx>243</positionx>
      <positiony>223</positiony>
      <workflowactions triggertype="ONENTRY">
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setappstativ">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>STDBODYAPPROVALSTATUS</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield></valuefield>
          <valueformula></valueformula>
          <valuejoinfield></valuejoinfield>
          <valueselect>1</valueselect>
          <valuetext></valuetext>
          <valuetype>STATIC</valuetype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setfieldvalueaction>
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setexpflgiv">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exception_flag_2]</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield></valuefield>
          <valueformula></valueformula>
          <valuejoinfield></valuejoinfield>
          <valueselect></valueselect>
          <valuetext></valuetext>
          <valuetype>STATIC</valuetype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setfieldvalueaction>
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setexpidiv">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield></valuefield>
          <valueformula></valueformula>
          <valuejoinfield></valuejoinfield>
          <valueselect></valueselect>
          <valuetext></valuetext>
          <valuetype>STATIC</valuetype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setfieldvalueaction>
        <setdisplaytypeaction scriptid="workflowaction_iaw_act_hidnxtapp">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <displaytype>HIDDEN</displaytype>
          <eventtypes></eventtypes>
          <field>STDBODYNEXTAPPROVER</field>
          <isinactive>F</isinactive>
          <issublistfield>F</issublistfield>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setdisplaytypeaction>
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setcreatoriv">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_invoice_creator_2]</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield>STDUSERUSER</valuefield>
          <valueformula></valueformula>
          <valuejoinfield></valuejoinfield>
          <valueselect></valueselect>
          <valuetext></valuetext>
          <valuetype>FIELD</valuetype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setfieldvalueaction>
      </workflowactions>
      <workflowactions triggertype="BEFOREUSERSUBMIT">
        <showmessageaction scriptid="workflowaction_iaw_act_shownoticebs">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <isinactive>F</isinactive>
          <messagetext>The current transaction has been set to Pending Approval and will undergo a set of validations. Should the transaction pass these validations, it will automatically be set to Approved status.</messagetext>
          <initcondition>
            <formula><![CDATA["Approval Status" IN ("Approval Status1") AND "User Role" NOT IN ("Role1")]]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Approval Status</name>
                <value>STDBODYAPPROVALSTATUS</value>
              </parameter>
              <parameter>
                <name>Approval Status1</name>
                <selectrecordtype>-243</selectrecordtype>
                <value>2</value>
              </parameter>
              <parameter>
                <name>User Role</name>
                <value>STDUSERROLE</value>
              </parameter>
              <parameter>
                <name>Role1</name>
                <selectrecordtype>-118</selectrecordtype>
                <value>ADMINISTRATOR</value>
              </parameter>
            </parameters>
          </initcondition>
        </showmessageaction>
      </workflowactions>
      <workflowactions triggertype="BEFORESUBMIT">
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setnxtapp">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>STDBODYNEXTAPPROVER</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield>STDENTITYSUPERVISOR</valuefield>
          <valueformula></valueformula>
          <valuejoinfield>STDUSERUSER</valuejoinfield>
          <valueselect></valueselect>
          <valuetext></valuetext>
          <valuetype>FIELD</valuetype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setfieldvalueaction>
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setappstatbspa">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>STDBODYAPPROVALSTATUS</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield></valuefield>
          <valueformula></valueformula>
          <valuejoinfield></valuejoinfield>
          <valueselect>1</valueselect>
          <valuetext></valuetext>
          <valuetype>STATIC</valuetype>
          <initcondition>
            <formula><![CDATA["Approval Status" IN ("Approval Status1") AND "User Role" NOT IN ("Role1")]]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Approval Status</name>
                <value>STDBODYAPPROVALSTATUS</value>
              </parameter>
              <parameter>
                <name>Approval Status1</name>
                <selectrecordtype>-243</selectrecordtype>
                <value>2</value>
              </parameter>
              <parameter>
                <name>User Role</name>
                <value>STDUSERROLE</value>
              </parameter>
              <parameter>
                <name>Role1</name>
                <selectrecordtype>-118</selectrecordtype>
                <value>ADMINISTRATOR</value>
              </parameter>
            </parameters>
          </initcondition>
        </setfieldvalueaction>
      </workflowactions>
      <workflowactions triggertype="AFTERSUBMIT">
        <workflowactiongroup scriptid="workflowaction_iaw_grp_exp_cust">
          <conditionsavedsearch>[scriptid=customsearch_ng_cs_iaw_certain_cust]</conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <initcondition>
            <formula><![CDATA[isChecked("Workflow:[PREF] Customer Validation") = 'T']]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Workflow:[PREF] Customer Validation</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_custval_pref_2]</value>
              </parameter>
            </parameters>
          </initcondition>
          <setfieldvalueaction scriptid="workflowaction_iaw_act_setflgcust">
            <field>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exception_flag_2]</field>
            <isinactive>F</isinactive>
            <valuechecked>T</valuechecked>
            <valuedate></valuedate>
            <valuefield></valuefield>
            <valueformula></valueformula>
            <valuejoinfield></valuejoinfield>
            <valueselect></valueselect>
            <valuetext></valuetext>
            <valuetype>STATIC</valuetype>
          </setfieldvalueaction>
          <customaction scriptid="workflowaction_iaw_act_setexpcust">
            <isinactive>F</isinactive>
            <resultfield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</resultfield>
            <scripttype>[scriptid=customscript_naw_ws_create_exceptionrec]</scripttype>
            <parametersettings>
              <parametersetting>
                <targetparameter>[scriptid=customscript_naw_ws_create_exceptionrec.custscript_naw_exception]</targetparameter>
                <valuechecked></valuechecked>
                <valuedate></valuedate>
                <valuefield></valuefield>
                <valueformula></valueformula>
                <valuejoinfield></valuejoinfield>
                <valueselect></valueselect>
                <valuetext>The customer has a credit hold on the account.</valuetext>
              </parametersetting>
              <parametersetting>
                <targetparameter>[scriptid=customscript_naw_ws_create_exceptionrec.custscript_naw_exceptionrec_id]</targetparameter>
                <valuechecked></valuechecked>
                <valuedate></valuedate>
                <valuefield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</valuefield>
                <valueformula></valueformula>
                <valuejoinfield></valuejoinfield>
                <valueselect></valueselect>
                <valuetext></valuetext>
              </parametersetting>
            </parametersettings>
          </customaction>
        </workflowactiongroup>
        <workflowactiongroup scriptid="workflowaction_iaw_grp_exp_tax">
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <initcondition>
            <formula><![CDATA[isChecked("Workflow:[PREF] Tax Amount check") = 'T' AND ( "Tax Total" = 0.00 OR isEmpty("Tax Total") = 'T' )]]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Workflow:[PREF] Tax Amount check</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_taxamount_pref_2]</value>
              </parameter>
              <parameter>
                <name>Tax Total</name>
                <value>STDBODYTAXTOTAL</value>
              </parameter>
            </parameters>
          </initcondition>
          <setfieldvalueaction scriptid="workflowaction_iaw_act_setflgtax">
            <field>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exception_flag_2]</field>
            <isinactive>F</isinactive>
            <valuechecked>T</valuechecked>
            <valuedate></valuedate>
            <valuefield></valuefield>
            <valueformula></valueformula>
            <valuejoinfield></valuejoinfield>
            <valueselect></valueselect>
            <valuetext></valuetext>
            <valuetype>STATIC</valuetype>
          </setfieldvalueaction>
          <customaction scriptid="workflowaction_iaw_act_setexptax">
            <isinactive>F</isinactive>
            <resultfield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</resultfield>
            <scripttype>[scriptid=customscript_naw_ws_create_exceptionrec]</scripttype>
            <parametersettings>
              <parametersetting>
                <targetparameter>[scriptid=customscript_naw_ws_create_exceptionrec.custscript_naw_exception]</targetparameter>
                <valuechecked></valuechecked>
                <valuedate></valuedate>
                <valuefield></valuefield>
                <valueformula></valueformula>
                <valuejoinfield></valuejoinfield>
                <valueselect></valueselect>
                <valuetext>There is no tax amount on the invoice.</valuetext>
              </parametersetting>
              <parametersetting>
                <targetparameter>[scriptid=customscript_naw_ws_create_exceptionrec.custscript_naw_exceptionrec_id]</targetparameter>
                <valuechecked></valuechecked>
                <valuedate></valuedate>
                <valuefield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</valuefield>
                <valueformula></valueformula>
                <valuejoinfield></valuejoinfield>
                <valueselect></valueselect>
                <valuetext></valuetext>
              </parametersetting>
            </parametersettings>
          </customaction>
        </workflowactiongroup>
        <workflowactiongroup scriptid="workflowaction_iaw_grp_exp_needsapp">
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <initcondition>
            <formula><![CDATA[isChecked("Workflow:[PREF] Customer Invoice Needs Approval") = 'T' AND isChecked("Customer:Transactions Need Approval") = 'T']]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Workflow:[PREF] Customer Invoice Needs Approval</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_custinvneedapp_val_pref_2]</value>
              </parameter>
              <parameter>
                <name>Customer:Transactions Need Approval</name>
                <value>STDBODYCUSTOMER:[scriptid=custentity_naw_trans_need_approval]</value>
              </parameter>
            </parameters>
          </initcondition>
          <setfieldvalueaction scriptid="workflowaction_iaw_act_setflgneedsapp">
            <field>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exception_flag_2]</field>
            <isinactive>F</isinactive>
            <valuechecked>T</valuechecked>
            <valuedate></valuedate>
            <valuefield></valuefield>
            <valueformula></valueformula>
            <valuejoinfield></valuejoinfield>
            <valueselect></valueselect>
            <valuetext></valuetext>
            <valuetype>STATIC</valuetype>
          </setfieldvalueaction>
          <customaction scriptid="workflowaction_iaw_act_setexpneedsapp">
            <isinactive>F</isinactive>
            <resultfield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</resultfield>
            <scripttype>[scriptid=customscript_naw_ws_create_exceptionrec]</scripttype>
            <parametersettings>
              <parametersetting>
                <targetparameter>[scriptid=customscript_naw_ws_create_exceptionrec.custscript_naw_exception]</targetparameter>
                <valuechecked></valuechecked>
                <valuedate></valuedate>
                <valuefield></valuefield>
                <valueformula></valueformula>
                <valuejoinfield></valuejoinfield>
                <valueselect></valueselect>
                <valuetext>This account is tagged for monitoring and all its invoices require approval.</valuetext>
              </parametersetting>
              <parametersetting>
                <targetparameter>[scriptid=customscript_naw_ws_create_exceptionrec.custscript_naw_exceptionrec_id]</targetparameter>
                <valuechecked></valuechecked>
                <valuedate></valuedate>
                <valuefield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</valuefield>
                <valueformula></valueformula>
                <valuejoinfield></valuejoinfield>
                <valueselect></valueselect>
                <valuetext></valuetext>
              </parametersetting>
            </parametersettings>
          </customaction>
        </workflowactiongroup>
        <customaction scriptid="workflowaction_iaw_act_set1stinv">
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <resultfield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_setflag_1st_inv_2]</resultfield>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <scripttype>[scriptid=customscript_iaw_ws_iscustomerfirstinv]</scripttype>
          <initcondition>
            <formula><![CDATA[isChecked("Workflow:[PREF] Customer 1st Invoice Validation") = 'T']]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Workflow:[PREF] Customer 1st Invoice Validation</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_first_inv_val_pref_2]</value>
              </parameter>
            </parameters>
          </initcondition>
        </customaction>
        <workflowactiongroup scriptid="workflowaction_iaw_grp_exp_first">
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <initcondition>
            <formula><![CDATA[isChecked("Workflow:[PREF] Customer 1st Invoice Validation") = 'T' AND isChecked("Workflow:[IAW] Is Customers First Invoice") = 'T']]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Workflow:[PREF] Customer 1st Invoice Validation</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_first_inv_val_pref_2]</value>
              </parameter>
              <parameter>
                <name>Workflow:[IAW] Is Customers First Invoice</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_setflag_1st_inv_2]</value>
              </parameter>
            </parameters>
          </initcondition>
          <setfieldvalueaction scriptid="workflowaction_iaw_act_setflgfirst">
            <field>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exception_flag_2]</field>
            <isinactive>F</isinactive>
            <valuechecked>T</valuechecked>
            <valuedate></valuedate>
            <valuefield></valuefield>
            <valueformula></valueformula>
            <valuejoinfield></valuejoinfield>
            <valueselect></valueselect>
            <valuetext></valuetext>
            <valuetype>STATIC</valuetype>
          </setfieldvalueaction>
          <customaction scriptid="workflowaction_iaw_act_setexpfirst">
            <isinactive>F</isinactive>
            <resultfield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</resultfield>
            <scripttype>[scriptid=customscript_naw_ws_create_exceptionrec]</scripttype>
            <parametersettings>
              <parametersetting>
                <targetparameter>[scriptid=customscript_naw_ws_create_exceptionrec.custscript_naw_exception]</targetparameter>
                <valuechecked></valuechecked>
                <valuedate></valuedate>
                <valuefield></valuefield>
                <valueformula></valueformula>
                <valuejoinfield></valuejoinfield>
                <valueselect></valueselect>
                <valuetext>This record is tagged as the customer&apos;s first invoice.</valuetext>
              </parametersetting>
              <parametersetting>
                <targetparameter>[scriptid=customscript_naw_ws_create_exceptionrec.custscript_naw_exceptionrec_id]</targetparameter>
                <valuechecked></valuechecked>
                <valuedate></valuedate>
                <valuefield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</valuefield>
                <valueformula></valueformula>
                <valuejoinfield></valuejoinfield>
                <valueselect></valueselect>
                <valuetext></valuetext>
              </parametersetting>
            </parametersettings>
          </customaction>
        </workflowactiongroup>
        <workflowactiongroup scriptid="workflowaction_iaw_grp_exp_terms">
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <initcondition>
            <formula><![CDATA[isChecked("Workflow:[PREF] Terms Validation") = 'T' AND "Customer:Terms" NOT IN ("Terms1")]]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Workflow:[PREF] Terms Validation</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_termsval_pref_2]</value>
              </parameter>
              <parameter>
                <name>Customer:Terms</name>
                <value>STDBODYCUSTOMER:STDENTITYTERMS</value>
              </parameter>
              <parameter>
                <name>Terms1</name>
                <value>STDBODYTERMS</value>
              </parameter>
            </parameters>
          </initcondition>
          <setfieldvalueaction scriptid="workflowaction_iaw_act_setflgterms">
            <field>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exception_flag_2]</field>
            <isinactive>F</isinactive>
            <valuechecked>T</valuechecked>
            <valuedate></valuedate>
            <valuefield></valuefield>
            <valueformula></valueformula>
            <valuejoinfield></valuejoinfield>
            <valueselect></valueselect>
            <valuetext></valuetext>
            <valuetype>STATIC</valuetype>
          </setfieldvalueaction>
          <customaction scriptid="workflowaction_iaw_act_setexpterms">
            <isinactive>F</isinactive>
            <resultfield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</resultfield>
            <scripttype>[scriptid=customscript_naw_ws_create_exceptionrec]</scripttype>
            <parametersettings>
              <parametersetting>
                <targetparameter>[scriptid=customscript_naw_ws_create_exceptionrec.custscript_naw_exception]</targetparameter>
                <valuechecked></valuechecked>
                <valuedate></valuedate>
                <valuefield></valuefield>
                <valueformula></valueformula>
                <valuejoinfield></valuejoinfield>
                <valueselect></valueselect>
                <valuetext>The terms on the invoice and customer record do not match.</valuetext>
              </parametersetting>
              <parametersetting>
                <targetparameter>[scriptid=customscript_naw_ws_create_exceptionrec.custscript_naw_exceptionrec_id]</targetparameter>
                <valuechecked></valuechecked>
                <valuedate></valuedate>
                <valuefield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</valuefield>
                <valueformula></valueformula>
                <valuejoinfield></valuejoinfield>
                <valueselect></valueselect>
                <valuetext></valuetext>
              </parametersetting>
            </parametersettings>
          </customaction>
        </workflowactiongroup>
      </workflowactions>
      <workflowtransitions>
        <workflowtransition scriptid="workflowtransition187">
          <buttonaction></buttonaction>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <scheduledelay></scheduledelay>
          <scheduletimeunit></scheduletimeunit>
          <tostate>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_approved]</tostate>
          <triggertype>AFTERSUBMIT</triggertype>
          <waitforworkflow></waitforworkflow>
          <waitforworkflowstate></waitforworkflowstate>
          <initcondition>
            <formula><![CDATA["Approval Status" IN ("Approval Status1") AND "User Role" IN ("Role1")]]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Approval Status</name>
                <value>STDBODYAPPROVALSTATUS</value>
              </parameter>
              <parameter>
                <name>Approval Status1</name>
                <selectrecordtype>-243</selectrecordtype>
                <value>2</value>
              </parameter>
              <parameter>
                <name>User Role</name>
                <value>STDUSERROLE</value>
              </parameter>
              <parameter>
                <name>Role1</name>
                <selectrecordtype>-118</selectrecordtype>
                <value>ADMINISTRATOR</value>
              </parameter>
            </parameters>
          </initcondition>
        </workflowtransition>
        <workflowtransition scriptid="workflowtransition186">
          <buttonaction></buttonaction>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <scheduledelay></scheduledelay>
          <scheduletimeunit></scheduletimeunit>
          <tostate>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_pend_approval]</tostate>
          <triggertype>AFTERSUBMIT</triggertype>
          <waitforworkflow></waitforworkflow>
          <waitforworkflowstate></waitforworkflowstate>
          <initcondition>
            <formula><![CDATA[isChecked("Workflow:[IAW] exception flag") = 'T']]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Workflow:[IAW] exception flag</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exception_flag_2]</value>
              </parameter>
            </parameters>
          </initcondition>
        </workflowtransition>
        <workflowtransition scriptid="workflowtransition185">
          <buttonaction></buttonaction>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <scheduledelay></scheduledelay>
          <scheduletimeunit></scheduletimeunit>
          <tostate>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_approved]</tostate>
          <triggertype>AFTERSUBMIT</triggertype>
          <waitforworkflow></waitforworkflow>
          <waitforworkflowstate></waitforworkflowstate>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </workflowtransition>
      </workflowtransitions>
    </workflowstate>
    <workflowstate scriptid="workflowstate_iaw_st_pend_approval">
      <description></description>
      <donotexitworkflow>F</donotexitworkflow>
      <name>Pending Approval</name>
      <positionx>263</positionx>
      <positiony>323</positiony>
      <workflowactions triggertype="ONENTRY">
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setappstatpa">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>STDBODYAPPROVALSTATUS</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield></valuefield>
          <valueformula></valueformula>
          <valuejoinfield></valuejoinfield>
          <valueselect>1</valueselect>
          <valuetext></valuetext>
          <valuetype>STATIC</valuetype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setfieldvalueaction>
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setcreatorpa">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_pend_approval.custwfstate_iaw_invoice_creator_2]</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_invoice_creator_2]</valuefield>
          <valueformula></valueformula>
          <valuejoinfield></valuejoinfield>
          <valueselect></valueselect>
          <valuetext></valuetext>
          <valuetype>FIELD</valuetype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setfieldvalueaction>
        <sendemailaction scriptid="workflowaction_iaw_act_emailnxtapp">
          <attachmentfield></attachmentfield>
          <attachmentfile></attachmentfile>
          <attachmentjoinfield></attachmentjoinfield>
          <attachmenttype>SPECIFIC</attachmenttype>
          <body>&lt;br&gt;You have a new Invoice {tranid} to review and approve. &lt;br&gt;Kindly log in to your NetSuite account at http://www.netsuite.com.</body>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <includeformat></includeformat>
          <includerecordlink>T</includerecordlink>
          <includetransaction>F</includetransaction>
          <isinactive>T</isinactive>
          <recipientbccemail></recipientbccemail>
          <recipientccemail></recipientccemail>
          <recipientemail></recipientemail>
          <recipientfield>STDBODYNEXTAPPROVER</recipientfield>
          <recipientjoinfield></recipientjoinfield>
          <recipienttype>FIELD</recipienttype>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <senderfield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_invoice_creator_2]</senderfield>
          <senderjoinfield></senderjoinfield>
          <sendertype>FIELD</sendertype>
          <subject>Invoice {tranid} for Approval</subject>
          <template></template>
          <usetemplate>F</usetemplate>
          <initcondition>
            <formula><![CDATA[isEmpty("Next Approver") = 'F' AND isEmpty("Next Approver:E-mail") = 'F']]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Next Approver</name>
                <value>STDBODYNEXTAPPROVER</value>
              </parameter>
              <parameter>
                <name>Next Approver:E-mail</name>
                <value>STDBODYNEXTAPPROVER:STDENTITYEMAIL</value>
              </parameter>
            </parameters>
          </initcondition>
        </sendemailaction>
      </workflowactions>
      <workflowactions triggertype="BEFORELOAD">
        <addbuttonaction scriptid="workflowaction_iaw_approve_button">
          <checkconditionbeforeexecution>T</checkconditionbeforeexecution>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <label>Approve</label>
          <saverecordfirst>F</saverecordfirst>
          <initcondition>
            <formula><![CDATA["User" IN ("Next Approver") OR "User" IN ("Employee1","Employee2")]]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>User</name>
                <value>STDUSERUSER</value>
              </parameter>
              <parameter>
                <name>Next Approver</name>
                <value>STDBODYNEXTAPPROVER</value>
              </parameter>
              <parameter>
                <name>Employee1</name>
                <selectrecordtype>-4</selectrecordtype>
                <value>[ACCOUNT_SPECIFIC_VALUE]</value>
              </parameter>
              <parameter>
                <name>Employee2</name>
                <selectrecordtype>-4</selectrecordtype>
                <value>[ACCOUNT_SPECIFIC_VALUE]</value>
              </parameter>
            </parameters>
          </initcondition>
        </addbuttonaction>
        <addbuttonaction scriptid="workflowaction_iaw_reject_button">
          <checkconditionbeforeexecution>T</checkconditionbeforeexecution>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <label>Reject Invoice</label>
          <saverecordfirst>F</saverecordfirst>
          <initcondition>
            <formula><![CDATA["User" IN ("Next Approver") OR "User" IN ("Employee1","Employee2")]]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>User</name>
                <value>STDUSERUSER</value>
              </parameter>
              <parameter>
                <name>Next Approver</name>
                <value>STDBODYNEXTAPPROVER</value>
              </parameter>
              <parameter>
                <name>Employee1</name>
                <selectrecordtype>-4</selectrecordtype>
                <value>[ACCOUNT_SPECIFIC_VALUE]</value>
              </parameter>
              <parameter>
                <name>Employee2</name>
                <selectrecordtype>-4</selectrecordtype>
                <value>[ACCOUNT_SPECIFIC_VALUE]</value>
              </parameter>
            </parameters>
          </initcondition>
        </addbuttonaction>
        <addbuttonaction scriptid="workflowaction_iaw_showexcept_button">
          <checkconditionbeforeexecution>T</checkconditionbeforeexecution>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <label>Show Exceptions</label>
          <saverecordfirst>F</saverecordfirst>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </addbuttonaction>
        <lockrecordaction scriptid="workflowaction_iaw_act_lockrecpa">
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <initcondition>
            <formula><![CDATA["User Role" NOT IN ("Role1")]]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>User Role</name>
                <value>STDUSERROLE</value>
              </parameter>
              <parameter>
                <name>Role1</name>
                <selectrecordtype>-118</selectrecordtype>
                <value>ADMINISTRATOR</value>
              </parameter>
            </parameters>
          </initcondition>
        </lockrecordaction>
      </workflowactions>
      <workflowstatecustomfields>
        <workflowstatecustomfield scriptid="custwfstate_iaw_invoice_creator_2">
          <applyformatting>F</applyformatting>
          <defaultchecked>F</defaultchecked>
          <defaultselection></defaultselection>
          <defaultvalue></defaultvalue>
          <description></description>
          <displaytype>NORMAL</displaytype>
          <dynamicdefault></dynamicdefault>
          <fieldtype>TEXT</fieldtype>
          <help></help>
          <label>invoice creator</label>
          <linktext></linktext>
          <maxvalue></maxvalue>
          <minvalue></minvalue>
          <selectrecordtype></selectrecordtype>
          <storevalue>T</storevalue>
        </workflowstatecustomfield>
      </workflowstatecustomfields>
      <workflowtransitions>
        <workflowtransition scriptid="workflowtransition184">
          <buttonaction>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_pend_approval.workflowaction_iaw_approve_button]</buttonaction>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <scheduledelay></scheduledelay>
          <scheduletimeunit></scheduletimeunit>
          <tostate>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_approved]</tostate>
          <triggertype></triggertype>
          <waitforworkflow></waitforworkflow>
          <waitforworkflowstate></waitforworkflowstate>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </workflowtransition>
        <workflowtransition scriptid="workflowtransition183">
          <buttonaction>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_pend_approval.workflowaction_iaw_reject_button]</buttonaction>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <scheduledelay></scheduledelay>
          <scheduletimeunit></scheduletimeunit>
          <tostate>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_rejected]</tostate>
          <triggertype></triggertype>
          <waitforworkflow></waitforworkflow>
          <waitforworkflowstate></waitforworkflowstate>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </workflowtransition>
        <workflowtransition scriptid="workflowtransition182">
          <buttonaction>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_pend_approval.workflowaction_iaw_showexcept_button]</buttonaction>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <scheduledelay></scheduledelay>
          <scheduletimeunit></scheduletimeunit>
          <tostate>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_show_exp]</tostate>
          <triggertype></triggertype>
          <waitforworkflow></waitforworkflow>
          <waitforworkflowstate></waitforworkflowstate>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </workflowtransition>
      </workflowtransitions>
    </workflowstate>
    <workflowstate scriptid="workflowstate_iaw_st_rejected">
      <description></description>
      <donotexitworkflow>F</donotexitworkflow>
      <name>Rejected</name>
      <positionx>263</positionx>
      <positiony>523</positiony>
      <workflowactions triggertype="ONENTRY">
        <customaction scriptid="workflowaction_iaw_act_delexprej">
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <resultfield></resultfield>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <scripttype>[scriptid=customscript_naw_ws_delete_exceptionrec]</scripttype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
          <parametersettings>
            <parametersetting>
              <targetparameter>[scriptid=customscript_naw_ws_delete_exceptionrec.custscript_naw_recid_target]</targetparameter>
              <valuechecked></valuechecked>
              <valuedate></valuedate>
              <valuefield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</valuefield>
              <valueformula></valueformula>
              <valuejoinfield></valuejoinfield>
              <valueselect></valueselect>
              <valuetext></valuetext>
            </parametersetting>
          </parametersettings>
        </customaction>
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setstatrej">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>STDBODYAPPROVALSTATUS</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield></valuefield>
          <valueformula></valueformula>
          <valuejoinfield></valuejoinfield>
          <valueselect>3</valueselect>
          <valuetext></valuetext>
          <valuetype>STATIC</valuetype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setfieldvalueaction>
        <sendemailaction scriptid="workflowaction_iaw_act_emailrej">
          <attachmentfield></attachmentfield>
          <attachmentfile></attachmentfile>
          <attachmentjoinfield></attachmentjoinfield>
          <attachmenttype>SPECIFIC</attachmenttype>
          <body>&lt;br&gt;The Invoice {tranid} has been rejected by {nextapprover}.&lt;br&gt;To view the Invoice, kindly log in to your NetSuite account at http://www.netsuite.com.</body>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <includeformat></includeformat>
          <includerecordlink>T</includerecordlink>
          <includetransaction>F</includetransaction>
          <isinactive>F</isinactive>
          <recipientbccemail></recipientbccemail>
          <recipientccemail></recipientccemail>
          <recipientemail></recipientemail>
          <recipientfield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_invoice_creator_2]</recipientfield>
          <recipientjoinfield></recipientjoinfield>
          <recipienttype>FIELD</recipienttype>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <senderfield>STDUSERUSER</senderfield>
          <senderjoinfield></senderjoinfield>
          <sendertype>FIELD</sendertype>
          <subject>Your Invoice {tranid} is Rejected</subject>
          <template></template>
          <usetemplate>F</usetemplate>
          <initcondition>
            <formula><![CDATA[isEmpty("Next Approver") = 'F' AND isEmpty("Workflow:[IAW] Invoice Creator:E-mail") = 'F']]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Next Approver</name>
                <value>STDBODYNEXTAPPROVER</value>
              </parameter>
              <parameter>
                <name>Workflow:[IAW] Invoice Creator:E-mail</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_invoice_creator_2]:STDENTITYEMAIL</value>
              </parameter>
            </parameters>
          </initcondition>
        </sendemailaction>
      </workflowactions>
      <workflowactions triggertype="BEFORELOAD">
        <addbuttonaction scriptid="workflowaction_iaw_resubmit">
          <checkconditionbeforeexecution>T</checkconditionbeforeexecution>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <label>Re-submit for Approvals</label>
          <saverecordfirst>F</saverecordfirst>
          <initcondition>
            <formula><![CDATA["User" NOT IN ("Next Approver") AND isEmpty("User:Supervisor") = 'F']]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>User</name>
                <value>STDUSERUSER</value>
              </parameter>
              <parameter>
                <name>Next Approver</name>
                <value>STDBODYNEXTAPPROVER</value>
              </parameter>
              <parameter>
                <name>User:Supervisor</name>
                <value>STDUSERUSER:STDENTITYSUPERVISOR</value>
              </parameter>
            </parameters>
          </initcondition>
        </addbuttonaction>
      </workflowactions>
      <workflowtransitions>
        <workflowtransition scriptid="workflowtransition181">
          <buttonaction>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_rejected.workflowaction_iaw_resubmit]</buttonaction>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <scheduledelay></scheduledelay>
          <scheduletimeunit></scheduletimeunit>
          <tostate>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_routing_chk]</tostate>
          <triggertype></triggertype>
          <waitforworkflow></waitforworkflow>
          <waitforworkflowstate></waitforworkflowstate>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </workflowtransition>
      </workflowtransitions>
    </workflowstate>
    <workflowstate scriptid="workflowstate_iaw_st_approved">
      <description></description>
      <donotexitworkflow>T</donotexitworkflow>
      <name>Approved</name>
      <positionx>53</positionx>
      <positiony>323</positiony>
      <workflowactions triggertype="ONENTRY">
        <customaction scriptid="workflowaction_iaw_act_delexpapp">
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <resultfield></resultfield>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <scripttype>[scriptid=customscript_naw_ws_delete_exceptionrec]</scripttype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
          <parametersettings>
            <parametersetting>
              <targetparameter>[scriptid=customscript_naw_ws_delete_exceptionrec.custscript_naw_recid_target]</targetparameter>
              <valuechecked></valuechecked>
              <valuedate></valuedate>
              <valuefield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</valuefield>
              <valueformula></valueformula>
              <valuejoinfield></valuejoinfield>
              <valueselect></valueselect>
              <valuetext></valuetext>
            </parametersetting>
          </parametersettings>
        </customaction>
        <sendemailaction scriptid="workflowaction_iaw_act_emailapp">
          <attachmentfield></attachmentfield>
          <attachmentfile></attachmentfile>
          <attachmentjoinfield></attachmentjoinfield>
          <attachmenttype>SPECIFIC</attachmenttype>
          <body>&lt;br&gt;The Invoice {tranid} has been approved by {nextapprover}.&lt;br&gt;To view the Invoice, kindly log in to your NetSuite account at http://www.netsuite.com.</body>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <includeformat></includeformat>
          <includerecordlink>T</includerecordlink>
          <includetransaction>F</includetransaction>
          <isinactive>T</isinactive>
          <recipientbccemail></recipientbccemail>
          <recipientccemail></recipientccemail>
          <recipientemail></recipientemail>
          <recipientfield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_invoice_creator_2]</recipientfield>
          <recipientjoinfield></recipientjoinfield>
          <recipienttype>FIELD</recipienttype>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <senderfield>STDUSERUSER</senderfield>
          <senderjoinfield></senderjoinfield>
          <sendertype>FIELD</sendertype>
          <subject>Your Invoice {tranid} is Approved</subject>
          <template></template>
          <usetemplate>F</usetemplate>
          <initcondition>
            <formula><![CDATA[isEmpty("Next Approver") = 'F' AND isEmpty("Workflow:[IAW] Invoice Creator:E-mail") = 'F']]></formula>
            <type>VISUAL_BUILDER</type>
            <parameters>
              <parameter>
                <name>Next Approver</name>
                <value>STDBODYNEXTAPPROVER</value>
              </parameter>
              <parameter>
                <name>Workflow:[IAW] Invoice Creator:E-mail</name>
                <value>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_invoice_creator_2]:STDENTITYEMAIL</value>
              </parameter>
            </parameters>
          </initcondition>
        </sendemailaction>
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setstatapp">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>STDBODYAPPROVALSTATUS</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield></valuefield>
          <valueformula></valueformula>
          <valuejoinfield></valuejoinfield>
          <valueselect>2</valueselect>
          <valuetext></valuetext>
          <valuetype>STATIC</valuetype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setfieldvalueaction>
        <setfieldvalueaction scriptid="workflowaction_iaw_act_setnxtappnull">
          <clienttriggerfields></clienttriggerfields>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <field>STDBODYNEXTAPPROVER</field>
          <isinactive>F</isinactive>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <valuechecked>F</valuechecked>
          <valuedate></valuedate>
          <valuefield></valuefield>
          <valueformula></valueformula>
          <valuejoinfield></valuejoinfield>
          <valueselect></valueselect>
          <valuetext></valuetext>
          <valuetype>STATIC</valuetype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </setfieldvalueaction>
      </workflowactions>
    </workflowstate>
    <workflowstate scriptid="workflowstate_iaw_st_show_exp">
      <description></description>
      <donotexitworkflow>F</donotexitworkflow>
      <name>Show Exceptions</name>
      <positionx>33</positionx>
      <positiony>463</positiony>
      <workflowactions triggertype="ONENTRY">
        <customaction scriptid="workflowaction_iaw_act_showexp">
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <isinactive>F</isinactive>
          <resultfield></resultfield>
          <scheduledelay></scheduledelay>
          <schedulemode>DELAY</schedulemode>
          <schedulerecurrence></schedulerecurrence>
          <scheduletimeofday></scheduletimeofday>
          <scheduletimeunit></scheduletimeunit>
          <scripttype>[scriptid=customscript_ws_showexceptions]</scripttype>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
          <parametersettings>
            <parametersetting>
              <targetparameter>[scriptid=customscript_ws_showexceptions.custscript_naw_exception_recid]</targetparameter>
              <valuechecked></valuechecked>
              <valuedate></valuedate>
              <valuefield>[scriptid=customworkflow_ng_cs_naw_iaw_2.custworkflow_iaw_exceptionrec_id_2]</valuefield>
              <valueformula></valueformula>
              <valuejoinfield></valuejoinfield>
              <valueselect></valueselect>
              <valuetext></valuetext>
            </parametersetting>
          </parametersettings>
        </customaction>
      </workflowactions>
      <workflowtransitions>
        <workflowtransition scriptid="workflowtransition180">
          <buttonaction></buttonaction>
          <conditionsavedsearch></conditionsavedsearch>
          <contexttypes></contexttypes>
          <eventtypes></eventtypes>
          <scheduledelay></scheduledelay>
          <scheduletimeunit></scheduletimeunit>
          <tostate>[scriptid=customworkflow_ng_cs_naw_iaw_2.workflowstate_iaw_st_pend_approval]</tostate>
          <triggertype></triggertype>
          <waitforworkflow></waitforworkflow>
          <waitforworkflowstate></waitforworkflowstate>
          <initcondition>
            <formula></formula>
            <type>VISUAL_BUILDER</type>
          </initcondition>
        </workflowtransition>
      </workflowtransitions>
    </workflowstate>
  </workflowstates>
</workflow>