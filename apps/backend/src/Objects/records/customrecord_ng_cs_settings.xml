<customrecordtype scriptid="customrecord_ng_cs_settings">
  <accesstype>NONENEEDED</accesstype>
  <allowattachments>F</allowattachments>
  <allowinlinedeleting>F</allowinlinedeleting>
  <allowinlinedetaching>F</allowinlinedetaching>
  <allowinlineediting>F</allowinlineediting>
  <allowmobileaccess>F</allowmobileaccess>
  <allownumberingoverride>F</allownumberingoverride>
  <allowquickadd>F</allowquickadd>
  <allowquicksearch>F</allowquicksearch>
  <allowuiaccess>T</allowuiaccess>
  <customsegment></customsegment>
  <description></description>
  <enabledle>F</enabledle>
  <enablekeywords>F</enablekeywords>
  <enablemailmerge>F</enablemailmerge>
  <enablenumbering>F</enablenumbering>
  <enableoptimisticlocking>T</enableoptimisticlocking>
  <enablesystemnotes>T</enablesystemnotes>
  <hierarchical>F</hierarchical>
  <icon></icon>
  <iconbuiltin>T</iconbuiltin>
  <iconindex></iconindex>
  <includeinsearchmenu>F</includeinsearchmenu>
  <includename>F</includename>
  <isinactive>F</isinactive>
  <isordered>F</isordered>
  <numberinginit></numberinginit>
  <numberingmindigits></numberingmindigits>
  <numberingprefix></numberingprefix>
  <numberingsuffix></numberingsuffix>
  <recordname>CS Settings</recordname>
  <showcreationdate>F</showcreationdate>
  <showcreationdateonlist>F</showcreationdateonlist>
  <showid>F</showid>
  <showlastmodified>T</showlastmodified>
  <showlastmodifiedonlist>T</showlastmodifiedonlist>
  <shownotes>T</shownotes>
  <showowner>F</showowner>
  <showownerallowchange>F</showownerallowchange>
  <showowneronlist>F</showowneronlist>
  <customrecordcustomfields>
    <customrecordcustomfield scriptid="custrecord_ng_cs_web_img_folder_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Internal ID of the folder to be used for web images.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INTEGER</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Internal ID of the folder to be used for web images. This folder can be created after installation in the File Cabinet. Obtain the ID of the folder and enter it here. If the images are going to be used on the internet it is best created in an area where live hosting files are stored.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Web Image Folder ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_exhb_kit_folder_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Internal ID of the folder to be used for holding exhibitor kit files.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INTEGER</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Internal ID of the folder to be used for holding exhibitor kit files. Recommended folder structure in File Cabinet: &quot;Live Hosting Files/site/documents/Exhb_Kits&quot;. Folder should at least be located somewhere under &quot;Live Hosting Files/site&quot; to give files online access without needing a login and to make for easier downloading access for exhibitors. Obtain the ID of the folder and enter it here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Exhibitor Kit Folder ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_csv_import_folder_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INTEGER</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This folder can be created after installation in the File Cabinet. Obtain the ID of the folder and enter it here. To obtain this ID navigate to Documents &amp;gt; Files &amp;gt; File Cabinet and click on ConventionSuite. Look for the Internal ID of the folder named “Exhibitor Import Files” and enter it here. If you wish to use a different folder, navigate to that folder and select that folder’s internal ID.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>CSV Import Folder ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_file_upload_folder_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INTEGER</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter file cabinet folder that the system will store web store uploads in.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Web File Upload Folder ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_import_log_record_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The Internal ID of exhibitor import log record.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INTEGER</fieldtype>
      <globalsearch>F</globalsearch>
      <help>The Internal ID of exhibitor import log record. Select Customization &amp;gt; Lists, Records, &amp; Fields. Locate the Internal ID for the Exhibitor Import Log and enter it here. Required  to link the import log to the CSV import form.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Import Log Record ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_import_log_search_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INTEGER</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Internal ID of exhibitor import log search view to display for link from CSV import form. Select ConventionSuite &amp;gt; Show Maintenance &amp;gt; Exhibitor Import Log. From the URL bar select the number next to the searchid and enter it here. Not required for linking to import log from CSV import form.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Import Log Search ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_log_time_zone">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Time zone to be used for logging</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the time zone to be used for logging.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Logging Timestamp Time Zone</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|243192, scriptid=customlist_time_zones]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_activity_log_rec_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Internal ID of Activity Log Record</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INTEGER</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the Internal ID of Activity Log Record. Select Customization &amp;gt; Lists, Records, &amp; Fields. Locate the Internal ID for the NG Activity Log and enter it here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Activity Log Record ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_activity_log_srch_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Internal ID of Activity Log Search</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INTEGER</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the Internal ID of Activity Log Search. Select Reports &amp;gt; Saved Searches &amp;gt; All Saved Searches. Locate the Custom NG Activity Log Default View and select the Internal ID. Enter the value here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Activity Log Search ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_job_numbering">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to use CS custom job numbering. Simple CS Job numbering allows a prefix for all job numbers along with a number that can be placed before (Number + Name) or after (Name + Number) with a separator in-between them. &#xd;
&#xd;
NOTE: Custom Automated Job Numbering should not be used unless custom numbering logic was created for your account.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Job Numbering</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_simple_job_numbering">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to use CS custom job numbering. Simple CS Job numbering allows a prefix for all job numbers along with a number that can be placed before (Number + Name) or after (Name + Number) with a separator in-between them. &#xd;
&#xd;
Note: Custom Automated Job Numbering should not be used unless custom numbering logic was created for your account.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Simple Automated Job Numbering</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_job_num_prefix">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Prefix to assign before job numbers</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the prefix you want assigned before a job number.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Job Number Prefix</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_custom_job_numbering">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Automatic job numbering.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Note: Custom Automated Job Numbering should not be used unless custom numbering logic was created for your account.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Custom Automated Job Numbering</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_name_number_ordering">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Name and numbering order option</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the name / numbering option from the list.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Name/Number Ordering</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|243192, scriptid=customlist_ng_cs_name_number_ordering]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_name_number_separator">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection>[bundleid=176152|243192, scriptid=customlist_ng_cs_name_number_separator.val_53136_t1675345_970]</defaultselection>
      <defaultvalue></defaultvalue>
      <description>Name / number separator.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select a separator to use between your show name and number.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Name/Number Separator</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|243192, scriptid=customlist_ng_cs_name_number_separator]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_dflt_shw_tbl_form">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>The ID of the default show table form</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INTEGER</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the ID of the default form to be used for entering events.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default CS Event Form</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_dflt_exhibtr_form">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the form you want to use for exhibitors type customers.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Exhibitor Form</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-167</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_settings_access">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>By default the settings record is only accessible to those logged in as Administrator. Add additional roles here for those allowed to have access to ConventionSuite settings.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Additional Settings Access</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-118</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_dflt_d_calc_date_types">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Choose the Show Date Types that should be selectable when using a “Is Days Calc” type item.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Days Calc Event Date Types</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_ng_cs_date_types]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_dflt_labor_date_types">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Choose the Show Date Types that should be selectable when using a “Is Labor” type item.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Labor Event Date Types</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_ng_cs_date_types]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_show_date">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Show Date type.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the default &quot;Show Date&quot; type. This is typically &quot;Show Date&quot;, but could be another value.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default &quot;Event Date&quot; Date Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_ng_cs_date_types]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_show_move_in">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Default Show Move-In date type</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the default &quot;Show Move-In&quot; type. This is typically &quot;Show Move-In&quot;, but could be another value.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default &quot;Event Move-In&quot; Date Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_ng_cs_date_types]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_show_move_out">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the default &quot;Show Move-Out&quot; type. This is typically &quot;Show Move-Out&quot;, but could be another value.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default &quot;Event Move-Out&quot; Date Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_ng_cs_date_types]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_exhib_move_in">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Exhibitor Move-In Date Type</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the default &quot;Exhibitor Move-In&quot; type. This is typically &quot;Exhibitor Move-In&quot;, but could be another value.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default &quot;Exhibitor Move-In&quot; Date Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_ng_cs_date_types]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_exhib_move_out">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the default &quot;Exhibitor Move-Out type. This is typically &quot;Exhibitor Move-Out&quot;, but could be another value.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default &quot;Exhibitor Move-Out&quot; Date Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customrecord_ng_cs_date_types]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_matrix_option_web">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Allow web users to order matrix options and add the items to cart.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>When this option is turned on, product options will load on category listing page,  allowing the web user to order matrix options and add the item to the cart. When the option is turned off, the user must navigate to the item detail page in order to see/order matrix options.  Turning on this option will decrease the speed of category web pages.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Matrix Options on Web</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_show_subsidiary">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the subsidiary you want for new CS Events to be created in (if you are using multiple subsidiaries).</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Event Subsidiary</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-117</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
      <customfieldfilters>
        <customfieldfilter>
          <fldcomparefield></fldcomparefield>
          <fldfilter>STDRECORDSUBSIDIARYISELIMINATION</fldfilter>
          <fldfilterchecked>F</fldfilterchecked>
          <fldfiltercomparetype>EQ</fldfiltercomparetype>
          <fldfilternotnull>F</fldfilternotnull>
          <fldfilternull>F</fldfilternull>
          <fldfiltersel></fldfiltersel>
          <fldfilterval></fldfilterval>
        </customfieldfilter>
      </customfieldfilters>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_show_calendar_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Calendar ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_exhb_kit_path">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>HIDDEN</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CLOBTEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Exhibitor Kit Folder Path</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_undep_funds">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enable to default payments/deposits to use &quot;Undeposited Funds&quot;.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Undeposited Funds</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_def_dep_account">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>If not using &quot;Undeposited Funds&quot;, set the desired default payment/deposit account in this field. This value will be ignored if &quot;Use Undeposited Funds&quot; is enabled.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Deposit Account</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-112</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
      <customfieldfilters>
        <customfieldfilter>
          <fldcomparefield></fldcomparefield>
          <fldfilter>STDRECORDACCOUNTACCTTYPE</fldfilter>
          <fldfilterchecked></fldfilterchecked>
          <fldfiltercomparetype>EQ</fldfiltercomparetype>
          <fldfilternotnull>F</fldfilternotnull>
          <fldfilternull>F</fldfilternull>
          <fldfiltersel>1|13</fldfiltersel>
          <fldfilterval></fldfilterval>
        </customfieldfilter>
      </customfieldfilters>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_payment_ar_account">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Default AR account to use on payment records.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Payment AR Account</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-112</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_def_exhb_dept">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Default department on forms.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This is the default department that will be used on ConventionSuite transactions and forms.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Exhibitor Department</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-102</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_def_exhb_ord_type">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>default exhibitor order type</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the default exhibitor order type to appear on transactions and forms.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Exhibitor Order Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=243192, scriptid=customlist_ng_cs_order_type]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_def_show_mgmt_dept">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Show Management Department</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-102</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_def_show_mgmt_ord_type">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Show Management Order Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=243192, scriptid=customlist_ng_cs_order_type]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_show_mgmt_ord_types">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Show Management Order Types</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=243192, scriptid=customlist_ng_cs_order_type]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_prev_adtl_orders">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enable to force a single booth order for each booth in a show. Additional web orders will be combined with pre-existing booth orders, if existing. Users will be redirected to existing orders for a booth and blocked from creating additional orders for a booth.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Prevent Additional Booth Orders</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_allow_mult_billng_part">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Allow more than one billing party per booth.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this box if you want more than one billing party booth allowed.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Allow Multiple Billing Parties Per Booth</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_scripted_pynt_frm">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Attach scripts to payment form.</description>
      <displayheight></displayheight>
      <displaytype>HIDDEN</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to enable scripting on payment forms.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Scripted Payment Form</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_show_tax">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enable to set sales tax item set for a show on all related booth orders for the show.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Event Tax Code</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_canadian_sales_tax">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Canadian Sales Tax</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_dflt_booth_order_form">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Default booth order form.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the form to be used by default for booth orders.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Booth Order Form</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-171</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_dflt_show_mgmt_ord_form">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the form to be used by default for show management orders.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Show Management Order Form</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-171</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_auth_non_web_orders">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Authorize Non-Web Orders</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_clear_order_cc_details">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Clear CC Details From Booth Order</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_custom_job">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Check to enable CS job custom segment.</description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to enable CS job custom segment. This should be enabled for all new ConventionSuite installations.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use CS Job</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_enable_rentals">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Enable Rentals</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_no_billed_order_editing">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Prevent users from editing billed orders.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to prevent users from editing billed orders.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Prevent Editing of Billed Orders</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_billed_ord_edit_users">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>List of users allowed to edit billed orders.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the users allowed to edit billed orders. This is a multi-select list.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Billed Order Authed Editing Users</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_avalara_tax_message">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Avalara Tax Message</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_do_not_prompt_terms">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enable to prevent prompting of user to create a payment for the exhibitor on a booth order if the order is set to use terms.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Do Not Prompt if Terms</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_no_prompt_under_zero">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check if you do not want to be prompted when a customer has a negative balance.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Do Not Prompt if Balance is $0 or Less</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_prompt_for_new_line">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Forces prompt for new line</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this to prompt for a new line even if a new line is not added.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Prompt Regardless If A New Line Is Added</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_prompt_exclusion_roles">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>User roles for which to always prevent prompting to create a payment for a booth order. This value is independent of the value for &quot;Do Not Prompt if Terms&quot;.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Payment Prompting Exclusion Roles</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-118</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_payment_type">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection>[bundleid=176152|243192, scriptid=customlist_pay_trans_types.val_51953_t1675345_884]</defaultselection>
      <defaultvalue></defaultvalue>
      <description>Payment types</description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the payment types supported by ConventionSuite. The recommendation is to add &quot;Deposit&quot; here.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Payment Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|243192, scriptid=customlist_pay_trans_types]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_exempt_estimated_items">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select this to keep estimated items from automatically being charged on auto-charge.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Exempt Estimated Items From Payments</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_adv_show_price">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Advance Show price level.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the price level to apply to the default advanced show price.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Advance Show Price</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-186</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_std_show_price">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Standard Show price level.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the price level to apply to the default standard show price.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Standard Show Price</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-186</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_onst_show_price">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>On-Site Show price level.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the price level to apply to the default on-site show price.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default On-Site Show Price</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-186</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_show_mgmt_price">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the price level for the default show management price.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Show Management Price</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-186</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_supervisor_item">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Supervisory labor item.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the item to be used for supervisory labor.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Supervisor Item</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-10</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
      <customfieldfilters>
        <customfieldfilter>
          <fldcomparefield></fldcomparefield>
          <fldfilter>STDITEMITEMTYPE</fldfilter>
          <fldfilterchecked></fldfilterchecked>
          <fldfiltercomparetype>EQ</fldfiltercomparetype>
          <fldfilternotnull>F</fldfilternotnull>
          <fldfilternull>F</fldfilternull>
          <fldfiltersel>2</fldfiltersel>
          <fldfilterval></fldfilterval>
        </customfieldfilter>
      </customfieldfilters>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_sprvisor_markup">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>30.0%</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PERCENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Set the default supervisor markup for labor services.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Supervisor Markup</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue>0</minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_multi_cc_proc">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Provides support for multiple credit card processors and profiles.</description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this box to use multiple credit card processors or profiles. Custom coding will be required for this solution.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Multiple CC Processors</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_cc_auth_item">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>&quot;Other Charge&quot; item to be used for credit card authorization orders.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Credit Card Authorization Item</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-10</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
      <customfieldfilters>
        <customfieldfilter>
          <fldcomparefield></fldcomparefield>
          <fldfilter>STDITEMITEMTYPE</fldfilter>
          <fldfilterchecked></fldfilterchecked>
          <fldfiltercomparetype>EQ</fldfiltercomparetype>
          <fldfilternotnull>F</fldfilternotnull>
          <fldfilternull>F</fldfilternull>
          <fldfiltersel>4</fldfiltersel>
          <fldfilterval></fldfilterval>
        </customfieldfilter>
      </customfieldfilters>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_mastercard">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Mastercard payment method</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the corresponding payment for MasterCard.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>MasterCard Payment Method</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-183</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_visa">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Visa payment method</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the corresponding payment for Visa.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Visa Payment Method</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-183</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_amex">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>American Express payment method.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the corresponding payment for American Express.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>AmEx Payment Method</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-183</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_discover">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Discover payment method</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the corresponding payment for Discover.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Discover Payment Method</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-183</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_enable_paytrace">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>T</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Dis</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this box if you have the Paytrace bundle installed with your installation of ConventionSuite UI bundle. UNCHECK THIS IF YOU ARE USING AN ALTERNATIVE PAYMENTS INTEGRATION.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Enable PayTrace Integration</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_gen_rand_email">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Generate random email addresses for imported exhibitors with no email address to allow web store access.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Generate Random Email Addresses</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_13_t1474328_889]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_rand_email_domain">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>@domain.com</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Domain to use for generated random email addresses.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Random Email Address Domain</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_13_t1474328_889]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_rand_email_prefix">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>rand_pref</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Prefix to use for randomly generated email addresses. This will be placed ahead of the randomly generated characters.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Random Email Address Prefix</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_13_t1474328_889]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_give_contacts_access">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Grant contacts records web store access.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Give Contacts Web Store Access</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_13_t1474328_889]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_csv_import_file">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DOCUMENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>CSV Import File</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_13_t1474328_889]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_enable_graphics_option">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to enable ordering of graphics by matrix options.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Enable Graphic Option</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_47_t1675345_760]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_enable_orientation_opt">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to enable ordering of items by an orientation matrix type.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Enable Orientation Option</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_47_t1675345_760]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_enable_freight_opts_opt">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to enable ordering of freight by matrix options (as opposed to freight tables).</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Enable Freight Matrix Option</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_47_t1675345_760]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_enable_labor_matrix_opt">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to enable ordering of labor by matrix options (as opposed to labor tables).</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Enable Labor Matrix Options</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_47_t1675345_760]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_enforce_item_max_qty">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this box to enforce item qty minimums set on item records during ordering.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Enforce Item Quantity Maximum</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_47_t1675345_760]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_graphics_item_cat">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection>[bundleid=176152|243192|260220, scriptid=customlist_item_category.value_38_t1509349_311]</defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Not typically set.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Graphics Item Category</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|243192|260220, scriptid=customlist_item_category]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_47_t1675345_760]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_cancl_charge">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enable to apply cancellation charges for items removed from a booth order after deadline (by default earliest move-in date for show).</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Cancellation Charge</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_cancl_charge_item">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>&quot;Other Charge&quot; item to use for applying cancellation fees to a booth order. It is recommended that you create an item for cancellations before setting this option.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Cancellation Charge Item</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-10</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
      <customfieldfilters>
        <customfieldfilter>
          <fldcomparefield></fldcomparefield>
          <fldfilter>STDITEMITEMTYPE</fldfilter>
          <fldfilterchecked></fldfilterchecked>
          <fldfiltercomparetype>EQ</fldfiltercomparetype>
          <fldfilternotnull>F</fldfilternotnull>
          <fldfilternull>F</fldfilternull>
          <fldfiltersel>4</fldfiltersel>
          <fldfilterval></fldfilterval>
        </customfieldfilter>
      </customfieldfilters>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_def_canc_chrg_pct">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>0.0%</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PERCENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Default percent of cancelled item to charge.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Cancellation Charge %</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_canc_threshold">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select which date threshold to start charging a cancellation charge.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Cancellation Charge Threshold</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|243192, scriptid=customlist_cancellation_threshold]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_show_auditing">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enable to use show booth order auditing prior to invoice creation.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Enable Event Auditing</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_show_audit_form">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>(OPTIONAL) Form to use when auditing booth orders for a show.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Audit Form</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-171</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_pre_invoicing">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enable to use show booth order pre-invoicing prior to invoice creation.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Enable Pre-Invoicing</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_pre_invoicing_form">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>(OPTIONAL) Form to use when pre-invoicing booth orders for a show.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Pre-Invoicing Form</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-171</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_alt_forms">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enable to use the selected forms for show auditing and pre-invoicing. If not enabled, any custom form selections for auditing and pre-invoicing will be ignored.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Alternate Forms</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_send_invoice_fail_email">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Enable sending of invoice failure emails.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to send email when invoicing fails. This is usually due to a payment failure.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Send Invoicing Failure Emails</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_inv_fail_recip">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Failure recipient</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the employee record that will receive the email. It is recommended that email of this employee be accessible by more than one person so that responses to the email can be reviewed if that person is not available.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Invoice Failure Recipient</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_inv_fail_sender">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Sender of the email.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the employee record that will send the email. It is recommended that email of this employee be accessible by more than one person so that responses to the email can be reviewed if that person is not available.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Invoice Failure Sender</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_inv_fail_cc">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Invoice failure CC recipient.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the CC recipient for invoice failures. This can be an individual or distribution list.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Invoice Failure CC Recipients</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_send_exhib_invoice">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Check to enable invoice automation to Exhibitors.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this box to enable invoice automation to Exhibitors.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Automate Sending Invoices To Exhibitors</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_exhib_invoice_sender">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Exhibitor Invoice Sender</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the employee from the list. The employee&apos;s email address will be used.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Exhibitor Invoice Sender</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_exhb_inv_email_template">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Exhibitor Invoice Email Template</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the Exhibitor Invoice Email Template to use when sending invoices to exhibitors.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Exhibitor Invoice Email Template</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-120</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_inv_email_conditions">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Send Invoice Email Conditions: Values TBD</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Send Invoice Email Conditions</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|176212|243192, scriptid=customlist68]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>F</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_retain_last_show">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this box to retain the last selected show when creating new orders off existing orders.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Retain Last Selected Show</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_retain_last_item_cat">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this box to retain the last selected item category when adding items to an order using the line item script feature.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Retain Last Selected Item Category</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_auto_charge_web_orders">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Set this to automatically create a payment record and charge the credit card on file for web orders that come in.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Auto Charge Web Orders</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_send_web_pymnt_email">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Send web payment notice.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this box to enable sending of web payment confirmations.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Send Web Payment Notice</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_web_pymnt_notice_sender">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Sender of web payment confirmation.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the employee record that will send the web payment confirmation.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Web Payment Notice Sender</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_web_pymnt_fail_recip">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Web payment failure recipient</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the employee who receives failure notices for web payments.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Web Payment Failure Recipient</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_web_pymnt_fail_cc">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Web Payment CC recipients.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the CC recipients for web payment failures. This can be an email address or distribution list.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Web Payment Failure CC Recipient</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_pymnt_fail_eml_template">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Email template for sending web order payment failure notifications to exhibitors. If not set, no notification emails will be sent if a payment for a web order fails.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Payment Failure Email Template</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-120</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_allow_show_autopay">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Allow Event Auto Payment</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_autochrg_cat_excl">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Set certain categories of items to not be included in auto charge runs.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>AutoCharge Category Exclusions</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|243192|260220, scriptid=customlist_item_category]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_pymt_rcpt_template">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Template to use for payment receipts.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the template to use for payment receipts.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Payment Receipt Template</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-171</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_dpst_rcpt_template">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Deposit receipt template</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the deposit receipt template.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Deposit Receipt Template</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-171</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_freight_minimum">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>0</defaultvalue>
      <description>Freight minimum weight</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>FLOAT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the minimum freight amount for purchases that will be applied to the freight item. For example, if you enter 100 here and your freight item is freight with a unit of measure of “lbs.” When you enter an order with freight on it, a minimum amount of 100 will be ordered.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Freight Minimum</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue>0</minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_inv_transfer_type">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Transfer Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=243192, scriptid=customlist_ng_cs_show_inv_mvment_type]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_transfer_count_markup">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>10.0%</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PERCENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Transfer Count Markup</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue>0</minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_trnsfr_exmpt_cats">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Transfer Exempt Categories</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|243192|260220, scriptid=customlist_item_category]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_transfer_from">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default From Location</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-103</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_default_to_as_st_loc">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default &apos;To&apos; Location to Event&apos;s Location</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_use_cc_conv_fee">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Use Convenience Fees</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_cc_conv_fee_rate">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>T</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>0.0%</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PERCENT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Convenience Fee Rate</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue>0</minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_cc_conv_fee_item">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Convenience Fee Item</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-10</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
      <customfieldfilters>
        <customfieldfilter>
          <fldcomparefield></fldcomparefield>
          <fldfilter>STDITEMITEMTYPE</fldfilter>
          <fldfilterchecked></fldfilterchecked>
          <fldfiltercomparetype>EQ</fldfiltercomparetype>
          <fldfilternotnull>F</fldfilternotnull>
          <fldfilternull>F</fldfilternull>
          <fldfiltersel>4</fldfiltersel>
          <fldfilterval></fldfilterval>
        </customfieldfilter>
      </customfieldfilters>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_cc_conv_fee_order_types">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Convenience Fee Order Types</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=243192, scriptid=customlist_ng_cs_order_type]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_14_t1474328_385]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_booth_ord_forms">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Specific Sales Order transaction forms to apply booth order scripting. This value is ignored if &quot;Enable Global Booth Order Scripting&quot; is enabled.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Booth Order Form ID Listing</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-171</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_add_item_forms">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Specific Sales Order transaction forms to apply add item scripting. This value is ignored if &quot;Enable Global Add Item Scripting&quot; is enabled.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Add Item Form ID Listing</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-171</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_show_mgt_forms">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the forms in which to use show management scripted order features.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Show Management Form ID Listing</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-171</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_enable_rentals_by_form">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Enable Rentals By Form</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_rental_forms">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Rentals Form ID Listing</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-171</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_allow_mass_booth_delete">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Enables mass booth deletion</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to enable mass booth deletion.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Allow Mass Booth Order Deletion</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_mass_booth_delete_roles">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Defines roles allowed to perform mass booth deletion.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the roles allowed to perform mass booth deletion. This is a multi-select field.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Mass Booth Deletion Roles</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-118</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_acct_domain_url">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Account Domain</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_prev_bo_redir_alert">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this box to prevent the &quot;You are leaving&quot; alerts from appearing when a user is redirected from a new order to existing order if an existing order is detected for the currently selected show and booth.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Prevent Booth Order Redirection Alert</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_name_from_subsidiary">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>If this option is selected, and subsidiaries are enabled, the address template merge operation will use the name of the subsidiary on the show table when setting &quot;Company Name&quot; instead of the company name as defined in &quot;Company Information&quot;.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Get Company Name From Event Subsidiary</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_booth_num_line_text">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>Booth Number __________</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Default text to use when adding a line to the shipping addresses for exhibitors to use in order to write in the booth number related to the shipment.&#xd;
&#xd;
For use with the address template fields.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Default Booth Number Line Text</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_wrhs_addy_template">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXTAREA</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Create an address template to be used for the &quot;Ship To Warehouse Address&quot; on the show table. Use the following placeholders for address information (include the number and brackets):&#xd;
&amp;lt;br /&amp;gt;&#xd;
&amp;lt;b&amp;gt;{0}&amp;lt;/b&amp;gt; &amp;gt;&amp;gt; Show Name&#xd;
{1} &amp;gt;&amp;gt; Customer Name (customer on show table)&#xd;
{2} &amp;gt;&amp;gt; Booth Number Line&#xd;
{3} &amp;gt;&amp;gt; Company Name&#xd;
{4} &amp;gt;&amp;gt; Street Address (address line 1 and line 2; no blank line if line 2 is empty)&#xd;
{5} &amp;gt;&amp;gt; City&#xd;
{6} &amp;gt;&amp;gt; State&#xd;
{7} &amp;gt;&amp;gt; Zip Code</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Ship to Warehouse Address Template</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_fclty_addy_template">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXTAREA</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Create an address template to be used for the &quot;Ship To Facility Address&quot; on the show table. Use the following placeholders for address information (include the number and brackets):&#xd;
&#xd;
{0} &amp;gt;&amp;gt; Show Name&#xd;
{1} &amp;gt;&amp;gt; Customer Name (customer on show table)&#xd;
{2} &amp;gt;&amp;gt; Booth Number Line&#xd;
{3} &amp;gt;&amp;gt; Company Name&#xd;
{4} &amp;gt;&amp;gt; Street Address (address line 1 and line 2; no blank line if line 2 is empty)&#xd;
{5} &amp;gt;&amp;gt; City&#xd;
{6} &amp;gt;&amp;gt; State&#xd;
{7} &amp;gt;&amp;gt; Zip Code</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Ship to Facility Address Template</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_algolia_application_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This is used to integrate Algolia search indexing in to the CS Web Store. These fields are used by NewGen PS only.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Algolia Application ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_algolia_search_key">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This is used to integrate Algolia search indexing in to the CS Web Store. These fields are used by NewGen PS only.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Algolia Search Key</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_algolia_api_key">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This is used to integrate Algolia search indexing in to the CS Web Store. These fields are used by NewGen PS only.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Algolia API Key</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_algolia_index">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This is used to integrate Algolia search indexing in to the CS Web Store. These fields are used by NewGen PS only.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Algolia Index</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_canonical_base_url">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This is used to integrate Algolia search indexing in to the CS Web Store. These fields are used by NewGen PS only.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Canonical Base URL</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_wo_img">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>IMAGE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select and image to be displayed on your Work Order PDFs.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Report and Work Order Logo Image</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_wo_logo_img_url">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Autofilled from the image upload field.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Logo Image URL</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_item_rprts_exluded_cats">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select certain categories of items to be excluded from the reports in ConventionSuite &amp;gt; Event Reports.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Booth Item Reports Category Exclusions</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|243192|260220, scriptid=customlist_item_category]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_exhb_wo_exluded_cats">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>MULTISELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select certain categories of items to be excluded from the Exhibitor Work Order report in ConventionSuite &amp;gt; Event Reports.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Exhibitor Work Order Report Category Exclusions</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=176152|243192|260220, scriptid=customlist_item_category]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_hide_bthchklst_cnt_info">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check to hide exhibitor contact information on the Booth Checklist report in ConventionSuite &amp;gt; Event Reports.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Hide Contact Info on Booth Checklist</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_shade_alt_report_lines">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Check this if you prefer alternate lines in the reports in ConventionSuite &amp;gt; Event Reports to have shading. This helps with readability for some users.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Shade Alternate Report Lines</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_report_line_shade_hex">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>#F0F0F0</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>A hexadecimal value to represent the color of the shaded report lines. Alpha characters used in the hex value should be entered in upper case form otherwise an error will be returned.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Report Line Shade Hex</label>
      <linktext></linktext>
      <maxlength>7</maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_report_item_display">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection>[bundleid=243192, scriptid=customlist_ng_cs_report_item_display.val_13936_t1516212_337]</defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the method in which you&apos;d like items and lines in the Event Reports categorized.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Report Item Display</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[bundleid=243192, scriptid=customlist_ng_cs_report_item_display]</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_company_header_logo">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>IMAGE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Upload the logo you want to use on your webportal from the NetSuite file cabinet.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Company Header Logo</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_header_logo_url">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>URL</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Enter the URL of the logo you want to use on your webportal from the NetSuite file cabinet. This field will be autoset from the &quot;COMPANY HEADER LOGO&quot; field on save.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Company Header Logo URL</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_contact_us_url">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>URL</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Set your Contact Us URL. This is visible in the top right corner of the web portal nav bar.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Contact Us URL</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_exhibitor_serv_phone">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>PHONE</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Set your exhibitor services phone. This is visible in the top right corner of the web portal nav bar.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Exhibitor Services Phone</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_navbar_bckgrnd_color">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>#ffffff</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the background color for your web portal. Press the button below to use a hex color picker.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Navigation Bar Background Color</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_navbar_bckgrnd_picker">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>&lt;input type=&quot;color&quot; id=&quot;navbar_background_color&quot; name=&quot;navbar_background_color&quot; value=&quot;#ffffff&quot; onchange=&quot;nlapiSetFieldValue(&apos;custrecord_ng_cs_navbar_bckgrnd_color&apos;, this.value)&quot;&gt;&#xd;
&#xd;
&lt;script type=&quot;text/javascript&quot;&gt;&#xd;
var el = document.getElementById(&apos;custrecord_ng_cs_navbar_bckgrnd_color&apos;);&#xd;
var currentColor  = &apos;&apos;;&#xd;
if (el == null) currentColor  = nlapiLookupField(&apos;customrecord_ng_cs_settings&apos;, 1, &apos;custrecord_ng_cs_navbar_bckgrnd_color&apos;);&#xd;
if (el != null) currentColor = el.value;&#xd;
document.getElementById(&apos;navbar_background_color&apos;).setAttribute(&apos;value&apos;, currentColor);&#xd;
var elFocus = &quot;this.checkvalid=true; document.getElementById(&apos;navbar_background_color&apos;).click();&quot;;&#xd;
if (el != null) el.setAttribute(&quot;onfocus&quot;, elFocus);&#xd;
&lt;/script&gt;</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INLINEHTML</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Navigation Bar Background Color Picker</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>F</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_accent_color">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>#000000</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Select the accent color for your web portal. Press the button below to use a hex color picker.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Website Accent Color</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_accent_color_picker">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>&lt;input type=&quot;color&quot; id=&quot;web_accent_color&quot; name=&quot;web_accent_color&quot; value=&quot;#000000&quot; onchange=&quot;nlapiSetFieldValue(&apos;custrecord_ng_cs_accent_color&apos;, this.value)&quot;&gt;&#xd;
&#xd;
&lt;script type=&quot;text/javascript&quot;&gt;&#xd;
var el = document.getElementById(&apos;custrecord_ng_cs_accent_color&apos;);&#xd;
var currentColor = &apos;&apos;;&#xd;
if (el == null) currentColor = nlapiLookupField(&apos;customrecord_ng_cs_settings&apos;, 1, &apos;custrecord_ng_cs_accent_color&apos;);&#xd;
if (el != null) currentColor = el.value;&#xd;
document.getElementById(&apos;web_accent_color&apos;).setAttribute(&apos;value&apos;, currentColor);&#xd;
var elFocus = &quot;this.checkvalid=true; document.getElementById(&apos;web_accent_color&apos;).click();&quot;;&#xd;
if (el != null) el.setAttribute(&quot;onfocus&quot;, elFocus);&#xd;
&lt;/script&gt;</defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INLINEHTML</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Website Accent Color Picker</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>F</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_event_selection_info">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight>750</displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth>750</displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>RICHTEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Selection Info</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_report_xml_folder_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>INTEGER</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Scripted Report Error XML Folder ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue>0</minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_11_t1474328_673]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_cust_web_access_role">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Exhibitor/Contact Web Access Role</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-118</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_13_t1474328_889]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_web_welcome_blurb">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue>CLIENT is pleased to have been selected as the general contractor for your upcoming event!&lt;br&gt;&lt;br&gt;Welcome to our online ordering portal. This system allows you to place orders for event furnishings, labor, material handling and any other event services.&amp;nbsp;&lt;br&gt;&lt;br&gt;The portal also has a &quot;My Account&quot; center where you review past orders, invoices and payments.&lt;br&gt;&lt;br&gt;Should you have any questions, please don’t hesitate to contact us at XXX-XXX-<NAME_EMAIL></defaultvalue>
      <description></description>
      <displayheight>750</displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth>750</displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>RICHTEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>T</ismandatory>
      <isparent>F</isparent>
      <label>Web Welcome Screen Blurb</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_conv_fee_web_displayed">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>A checkbox (bool) that allows users to display the convenience fee at checkout.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Show the convenience fee on the checkout screen?</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Show Web Convenience Fee</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_73_t1516212_655]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_sales_tax_is_on_lines">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description>Only set this field if user account resides in Canada - Will apply tax to line items.</description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>CHECKBOX</fieldtype>
      <globalsearch>F</globalsearch>
      <help>This should be enabled for companies operating in Canada.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Sales Tax is Set on Item Lines</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_conv_fee_zero_tax">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Zero Tax Item for Conv Fees</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-128</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_tax_auto_sel_txt_filter">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help>Text string to filter out tax group search results for when more than one tax group can be returned in a search based on state/province/zip code. Filter will assist in returning only a single value where the tax group name contains the specified text.</help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Tax Auto Select Text Filter</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_10_t1474328_843]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_evt_mm_attch_folder_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Mass Mailer Attachments Folder ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_cs_evt_mm_temp_folder_id">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>STATICTEXT</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Event Mass Mailer Temp Folder ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab>[scriptid=customrecord_ng_cs_settings.tab_12_t1474328_193]</subtab>
    </customrecordcustomfield>
  </customrecordcustomfields>
  <permissions>
    <permission>
      <permittedlevel>FULL</permittedlevel>
      <permittedrole>[bundleid=243192, scriptid=customrole_token_based_auth]</permittedrole>
      <restriction></restriction>
    </permission>
  </permissions>
  <subtabs>
    <subtab scriptid="tab_12_t1474328_193">
      <tabparent></tabparent>
      <tabtitle>General</tabtitle>
    </subtab>
    <subtab scriptid="tab_10_t1474328_843">
      <tabparent></tabparent>
      <tabtitle>Accounting</tabtitle>
    </subtab>
    <subtab scriptid="tab_13_t1474328_889">
      <tabparent></tabparent>
      <tabtitle>Exhibitors</tabtitle>
    </subtab>
    <subtab scriptid="tab_47_t1675345_760">
      <tabparent></tabparent>
      <tabtitle>Items</tabtitle>
    </subtab>
    <subtab scriptid="tab_14_t1474328_385">
      <tabparent></tabparent>
      <tabtitle>Transactions</tabtitle>
    </subtab>
    <subtab scriptid="tab_11_t1474328_673">
      <tabparent></tabparent>
      <tabtitle>Scripting</tabtitle>
    </subtab>
    <subtab scriptid="tab_73_t1516212_655">
      <tabparent></tabparent>
      <tabtitle>Website</tabtitle>
    </subtab>
  </subtabs>
</customrecordtype>