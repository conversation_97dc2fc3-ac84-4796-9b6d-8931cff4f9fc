<transactionForm scriptid="custform_17_t1474328_734" standard="STANDARDSALESORDER">
  <name>CS Sales Order</name>
  <recordType>SALESORDER</recordType>
  <inactive>F</inactive>
  <preferred>T</preferred>
  <mainFields>
    <fieldGroup scriptid="primaryinformation">
      <label>Primary Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>CUSTOMFORM</id>
          <label>Custom Form</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANID</id>
          <label>Order #</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANDATE</id>
          <label>Date</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ENTITY</id>
          <label>Customer</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>OTHERREFNUM</id>
          <label>PO #</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ORDERSTATUS</id>
          <label>Status</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>STARTDATE</id>
          <label>Start Date</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ENDDATE</id>
          <label>End Date</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>MEMO</id>
          <label>Memo</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>SALESREP</id>
          <label>Sales Rep</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>JOB</id>
          <label/>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_deposit_recd]</id>
          <label>Deposit Rec'd</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="salesinformation">
      <label>Sales Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>SALESEFFECTIVEDATE</id>
          <label>Sales Effective Date</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CREATEDFROM</id>
          <label>Created From</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>DISABLED</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>LEADSOURCE</id>
          <label>Lead Source</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>SOURCE</id>
          <label>Source</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="classification">
      <label>Classification</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>SUBSIDIARY</id>
          <label>Subsidiary</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>DEPARTMENT</id>
          <label>Department</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CLASS</id>
          <label>Class</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>LOCATION</id>
          <label>Location</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_show_table]</id>
          <label>CS Event</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[appid=com.newgennow.cseventservices, scriptid=custbody_cseg_ng_cs_job]</id>
          <label>CS Job</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_cs_event_session]</id>
          <label>Event Session</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="intercompanymanagement">
      <label>Intercompany Management</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
    </fieldGroup>
    <defaultFieldGroup>
      <fields position="MIDDLE">
        <field>
          <id>[scriptid=custbody_ng_paytrace_pay_link_url]</id>
          <label>Payment Link URL</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_customer_filter]</id>
          <label>Customer Select</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_total_paid]</id>
          <label>Total Paid</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_booth_order]</id>
          <label>Booth Order</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_booth]</id>
          <label>Booth</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_balance]</id>
          <label>Balance</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_booth_size]</id>
          <label>Booth Size</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_booth_actual_exhibitor]</id>
          <label>Booth Exhibitor</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ONETIME</id>
          <label>One Time</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>RECURWEEKLY</id>
          <label>Weekly</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>RECURMONTHLY</id>
          <label>Monthly</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>RECURQUARTERLY</id>
          <label>Quarterly</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>RECURANNUALLY</id>
          <label>Annually</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_batchdate]</id>
          <label>Batch Date</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_last4digits]</id>
          <label>Last 4 Digits</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_fundedamount]</id>
          <label>Funded Amount</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_variance]</id>
          <label>Variance</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_invoice]</id>
          <label>Invoice (2)</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_batchamount]</id>
          <label>Batch Amount</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_cs_order_type]</id>
          <label>Order Type</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_cs_event_venue]</id>
          <label>CS Event Venue</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_cs_event_est_version_num]</id>
          <label>Version</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_paytrace_conv_fee_exempt]</id>
          <label>Convenience Fee Exempt (2)</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[scriptid=custbody_ng_paytrace_deposit_link_url]</id>
          <label>Deposit Link URL</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
      <fields position="BOTTOM">
        <field>
          <id>TRANSACTIONNUMBER</id>
          <label>Transaction Number</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>GENERATETRANIDONSAVE</id>
          <label>Generate Document Number on Save</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>UNCHECKED</checkBoxDefault>
        </field>
      </fields>
    </defaultFieldGroup>
  </mainFields>
  <tabs>
    <tab>
      <id>TRANSACTIONITEMS</id>
      <label>Items</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_item_category]</id>
              <label>Item Category</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_item]</id>
              <label>Item</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_quantity]</id>
              <label>Quantity</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_price_level]</id>
              <label>Price Level</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_color]</id>
              <label>Color</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_size]</id>
              <label>Size</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_carpet_width]</id>
              <label>Width</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_carpet_length]</id>
              <label>Depth</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_days]</id>
              <label>Days</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_freight_weight]</id>
              <label>Freight Weight</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[appid=com.newgennow.cseventservices, scriptid=custbody_variant]</id>
              <label>Orientation</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>DISCOUNTITEM</id>
              <label>Discount Item</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>DISCOUNTRATE</id>
              <label>Rate</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_substrate]</id>
              <label>Graphic Material</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ITEM</id>
          <neverEmpty>F</neverEmpty>
          <columns>
            <column>
              <id>JOB</id>
              <label>Project</label>
              <visible>F</visible>
            </column>
            <column>
              <id>ITEM</id>
              <label>Item</label>
              <visible>T</visible>
            </column>
            <column>
              <id>QUANTITYAVAILABLE</id>
              <label>Available</label>
              <visible>F</visible>
            </column>
            <column>
              <id>QUANTITYCOMMITTED</id>
              <label>Committed</label>
              <visible>F</visible>
            </column>
            <column>
              <id>QUANTITYFULFILLED</id>
              <label>Fulfilled</label>
              <visible>F</visible>
            </column>
            <column>
              <id>QUANTITYBACKORDERED</id>
              <label>Back Ordered</label>
              <visible>F</visible>
            </column>
            <column>
              <id>AVERAGECOST</id>
              <label>Average Cost</label>
              <visible>F</visible>
            </column>
            <column>
              <id>LASTPURCHASEPRICE</id>
              <label>Last Purchase Price</label>
              <visible>F</visible>
            </column>
            <column>
              <id>QUANTITYONHAND</id>
              <label>On Hand</label>
              <visible>F</visible>
            </column>
            <column>
              <id>QUANTITY</id>
              <label>Quantity</label>
              <visible>T</visible>
            </column>
            <column>
              <id>UNITS</id>
              <label>Units</label>
              <visible>T</visible>
            </column>
            <column>
              <id>PRICE</id>
              <label>Price Level</label>
              <visible>T</visible>
            </column>
            <column>
              <id>RATE</id>
              <label>Rate</label>
              <visible>T</visible>
            </column>
            <column>
              <id>AMOUNT</id>
              <label>Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>COMMITINVENTORY</id>
              <label>Commit</label>
              <visible>F</visible>
            </column>
            <column>
              <id>COMMITMENTFIRM</id>
              <label>Commitment Confirmed</label>
              <visible>F</visible>
            </column>
            <column>
              <id>ORDERPRIORITY</id>
              <label>Order Priority</label>
              <visible>F</visible>
            </column>
            <column>
              <id>DESCRIPTION</id>
              <label>Description</label>
              <visible>T</visible>
            </column>
            <column>
              <id>ISTAXABLE</id>
              <label>Tax</label>
              <visible>T</visible>
            </column>
            <column>
              <id>OPTIONS</id>
              <label>Options</label>
              <visible>T</visible>
            </column>
            <column>
              <id>DEPARTMENT</id>
              <label>Company</label>
              <visible>F</visible>
            </column>
            <column>
              <id>CLASS</id>
              <label>Financial Show</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_custom_carpet_size]</id>
              <label>Custom Size</label>
              <visible>T</visible>
            </column>
            <column>
              <id>EXCLUDEFROMRATEREQUEST</id>
              <label>Exclude Item from Rate Request</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_add_date]</id>
              <label>Add Date</label>
              <visible>T</visible>
            </column>
            <column>
              <id>LOCATION</id>
              <label>Location</label>
              <visible>F</visible>
            </column>
            <column>
              <id>ISCLOSED</id>
              <label>Closed</label>
              <visible>T</visible>
            </column>
            <column>
              <id>PERCENTCOMPLETE</id>
              <label>% Complete</label>
              <visible>F</visible>
            </column>
            <column>
              <id>TAXCODE</id>
              <label>Tax Code</label>
              <visible>T</visible>
            </column>
            <column>
              <id>TAXRATE1</id>
              <label>Tax Rate</label>
              <visible>T</visible>
            </column>
            <column>
              <id>TAXRATE2</id>
              <label>PST</label>
              <visible>T</visible>
            </column>
            <column>
              <id>TAX1AMT</id>
              <label>Tax Amt</label>
              <visible>T</visible>
            </column>
            <column>
              <id>GROSSAMT</id>
              <label>Gross Amt</label>
              <visible>T</visible>
            </column>
            <column>
              <id>EXPECTEDSHIPDATE</id>
              <label>Expected Ship Date</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_startdate]</id>
              <label>Start Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_complete]</id>
              <label>Complete</label>
              <visible>F</visible>
            </column>
            <column>
              <id>FREEGIFTPROMOTION</id>
              <label>Free Gift Promotion</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_stockitem]</id>
              <label>Stock Item</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_completedate]</id>
              <label>Complete Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_removaldate]</id>
              <label>Removal Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_removalcompletedate]</id>
              <label>Removal Complete Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_cost_is_estimated]</id>
              <label>Estimated</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.cseventservices, scriptid=custcol_cseg_ng_cs_job]</id>
              <label>CS Job</label>
              <visible>F</visible>
            </column>
            <column>
              <id>TOBEFULFILLED</id>
              <label>To Be Fulfilled</label>
              <visible>F</visible>
            </column>
            <column>
              <id>FROMJOB</id>
              <label>Project Item</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_attached_document]</id>
              <label>Attached Document</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_area]</id>
              <label>Area</label>
              <visible>T</visible>
            </column>
            <column>
              <id>ISESTIMATE</id>
              <label>Billable Estimate</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_loc]</id>
              <label>Rental Location</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_start_date]</id>
              <label>Rental Start Date</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_end_date]</id>
              <label>Rental End Date</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_availability]</id>
              <label>Rental Availability</label>
              <visible>T</visible>
            </column>
            <column>
              <id>COSTESTIMATETYPE</id>
              <label>Cost Estimate Type</label>
              <visible>T</visible>
            </column>
            <column>
              <id>COSTESTIMATERATE</id>
              <label>Est. Unit Cost</label>
              <visible>F</visible>
            </column>
            <column>
              <id>COSTESTIMATE</id>
              <label>Est. Extended Cost</label>
              <visible>T</visible>
            </column>
            <column>
              <id>ESTGROSSPROFIT</id>
              <label>Est. Gross Profit</label>
              <visible>T</visible>
            </column>
            <column>
              <id>ESTGROSSPROFITPERCENT</id>
              <label>Est. Gross Profit Percent</label>
              <visible>T</visible>
            </column>
            <column>
              <id>CREATEPO</id>
              <label>Create PO</label>
              <visible>T</visible>
            </column>
            <column>
              <id>POVENDOR</id>
              <label>PO Vendor</label>
              <visible>T</visible>
            </column>
            <column>
              <id>PORATE</id>
              <label>PO Rate</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_session]</id>
              <label>CS Event Session</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event]</id>
              <label>CS Event</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_session_line]</id>
              <label>Event Session</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_session_equip_rec]</id>
              <label>Event Equipment Record</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_session_task_rec]</id>
              <label>Event Session Task Record</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_days_number]</id>
              <label># of Days</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_task_hours]</id>
              <label>Task Hours</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_discount_amt]</id>
              <label>Discount Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_serv_fee_amt]</id>
              <label>Service Fee Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_tax_amt]</id>
              <label>Tax Amount</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_mb_orig_order]</id>
              <label>Originating Order</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_mb_customer]</id>
              <label>Customer</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_mb_bill_date]</id>
              <label>Order Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_item_category]</id>
              <label>Category</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_item_service_tier]</id>
              <label>Service Tier</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_show_dates]</id>
              <label>Event Show Dates </label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_session_days]</id>
              <label>Days</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_item_internal_notes]</id>
              <label>Internal Notes</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_item_notes]</id>
              <label>Notes</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_start_time]</id>
              <label>Start Time</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_end_time]</id>
              <label>End time</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_sess_start_date]</id>
              <label>Event Session Start Date</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_sess_start_time]</id>
              <label>Event Session Start Time</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_sess_end_date]</id>
              <label>Event Session End Date</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_sess_end_time]</id>
              <label>Event Session End Time</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_tax_rate]</id>
              <label>Tax Rate</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_item_commission_rate]</id>
              <label>Commission Rate</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_item_commission_amount]</id>
              <label>Commission Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_task_hours_ot]</id>
              <label>Overtime Hours</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_task_hours_dt]</id>
              <label>Double Time Hours</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_pushed_to_master_bill]</id>
              <label>Pushed to Master Bill</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_sess_venue_space]</id>
              <label>Event Session Venue Space</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_sess_ven_sp_name]</id>
              <label>Event Session Venue Space (Sales Name)</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_item_sales_type]</id>
              <label>Sales Type</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_event_session_title]</id>
              <label>Event Session Title</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_item_default_rate]</id>
              <label>Default Rate</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_flat_discount]</id>
              <label>Flat Discount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_is_subrental]</id>
              <label>Sub-Rental</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_subrental_cost]</id>
              <label>Sub-rental Cost</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_serv_fee_comm_rate]</id>
              <label>Service Fee Commission Rate</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_serv_fee_comm_amount]</id>
              <label>Service Fee Commission Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_commission_total]</id>
              <label>Commission Total</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_daily_price]</id>
              <label>Daily Price</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_day_serv_fee_amount]</id>
              <label>Daily Service Fee Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_day_discount_amount]</id>
              <label>Daily Discount Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_day_sub_total]</id>
              <label>Daily Sub Total</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_day_tax_amount]</id>
              <label>Daily Tax Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_day_amount]</id>
              <label>Daily Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_day_commission_amount]</id>
              <label>Daily Commission Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_day_serv_comm_amount]</id>
              <label>Daily Service Fee Commission Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_day_commission_total]</id>
              <label>Daily Commission Total</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_tax_rate_pst]</id>
              <label>PST Rate</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_tax_amount_pst]</id>
              <label>PST Tax Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_day_tax_amount_pst]</id>
              <label>Daily PST Tax Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_show_on_pdf]</id>
              <label>Show on PDF</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_test_currency_field]</id>
              <label>Test Currency Field</label>
              <visible>T</visible>
            </column>
          </columns>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>PROMOTIONSTAB</id>
      <label>Promotions</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>TRANSACTIONSHIPPING</id>
      <label>Shipping</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="shippinginformation">
          <label>Shipping Information</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>SHIPDATE</id>
              <label>Ship Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ACTUALSHIPDATE</id>
              <label>Actual</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>T</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPCARRIER</id>
              <label>Shipping Carrier</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPMETHOD</id>
              <label>Shipping Method</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPPINGCOST</id>
              <label>Shipping Cost</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>HANDLINGCOST</id>
              <label>Handling Cost</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>FOB</id>
              <label>FOB</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>TRACKINGNUMBERS</id>
              <label>Tracking #</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPCOMPLETE</id>
              <label>Ship Complete</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>SHIPPINGTAXCODE</id>
              <label>Shipping Tax Code</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPPINGTAX1RATE</id>
              <label>Shipping Tax Rate</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>HANDLINGTAXCODE</id>
              <label>Handling Tax Code</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>HANDLINGTAX1RATE</id>
              <label>Handling Tax Rate</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="shippingaddress">
          <label>Shipping Address</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>SHIPADDRESSLIST</id>
              <label>Ship To Select</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPADDRESS</id>
              <label>Ship To</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fulfillments">
          <label>Fulfillments</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
        </fieldGroup>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>SHIPPINGTAX2RATE</id>
              <label>Shipping Tax Rate 2</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>HANDLINGTAX2RATE</id>
              <label>Handling Tax Rate 2</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>TRANSACTIONBILLING</id>
      <label>Billing</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="billinginformation">
          <label>Billing Information</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>UNBILLEDORDERS</id>
              <label>Unbilled Orders</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>BALANCE</id>
              <label>Balance</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>TERMS</id>
              <label>Terms</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="billingaddress">
          <label>Billing Address</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>BILLADDRESSLIST</id>
              <label>Bill To Select</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>BILLADDRESS</id>
              <label>Bill To</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_ng_cs_pushed_to_master_bill]</id>
              <label>Pushed to Master Bill</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_ap_payment_count]</id>
              <label># of Payments</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_ap_schedule]</id>
              <label>AutoPay Payment Schedule</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_ap_next_pay_date]</id>
              <label>Next Pay Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_ap_first_pay_amnt]</id>
              <label>Initial Payment Amount</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_ap_sched_amount]</id>
              <label>Scheduled Payment Amount</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_ap_status]</id>
              <label>AutoPay Status</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_ap_profile]</id>
              <label>AutoPay Profile</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_ap_signup_lnk_url]</id>
              <label>AutoPay Sign-Up Link URL</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_ap_update_lnk_url]</id>
              <label>AutoPay Update Link URL</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_pt_autopay_schedule]</id>
              <label>AutoPay Schedule Link</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subTab>
          <id>TRANSACTIONPAYMENT</id>
          <label>Payment</label>
          <visible>T</visible>
          <fieldGroups>
            <defaultFieldGroup>
              <fields position="MIDDLE">
                <field>
                  <id>PAYMENTSESSIONAMOUNT</id>
                  <label>Payment Amount</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>PAYMENTMETHOD</id>
                  <label>Payment Method</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CREDITCARD</id>
                  <label>Credit Card Select</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCNUMBER</id>
                  <label>Credit Card #</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCSECURITYCODE</id>
                  <label>CSC</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCEXPIREDATE</id>
                  <label>Expires (MM/YYYY)</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CARDDATAPROVIDED</id>
                  <label>Card Data Provided</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCNAME</id>
                  <label>Name on Card</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCSTREET</id>
                  <label>Card Street</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCZIPCODE</id>
                  <label>Card Zip Code</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CUSTOMERCODE</id>
                  <label>Customer Code</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CREDITCARDPROCESSOR</id>
                  <label>Payment Processing Profile</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>GETAUTH</id>
                  <label>Get Authorization</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
                </field>
                <field>
                  <id>CCAPPROVED</id>
                  <label>Credit Card Approved</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
                </field>
                <field>
                  <id>PNREFNUM</id>
                  <label>P/N Ref.</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>AUTHCODE</id>
                  <label>Auth. Code</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCAVSSTREETMATCH</id>
                  <label>AVS Street Match</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCAVSZIPMATCH</id>
                  <label>AVS Zip Match</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCSECURITYCODEMATCH</id>
                  <label>CSC Match</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>SOFTDESCRIPTOR</id>
                  <label>Soft Descriptor</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>IGNORECSC</id>
                  <label>Ignore CSC</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCISPURCHASECARDBIN</id>
                  <label>Purchase Card BIN</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>DISABLED</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CCPROCESSASPURCHASECARD</id>
                  <label>Send Line-Level Data</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>ISRECURRINGPAYMENT</id>
                  <label>Recurring Payment</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>PAYMENTOPERATION</id>
                  <label>Payment Operation</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_solupay_bank_account]</id>
                  <label>Bank Account Number</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_solupay_routing_number]</id>
                  <label>Routing Number</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_solupay_check_number]</id>
                  <label>Check Number</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_solupay_3dsecurestatus]</id>
                  <label>3D Secure Status</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>INLINETEXT</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_solupay_3dsecureapproved]</id>
                  <label>3D Secure Approved</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
                </field>
                <field>
                  <id>[scriptid=custbody_solupay_cctoken]</id>
                  <label>CC Token</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>INLINETEXT</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_paytrace_settlement_batch]</id>
                  <label>PayTrace Settlement Batch</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>INLINETEXT</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_paytrace_apply_conv_fee]</id>
                  <label>Apply Convenience Fee</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
                </field>
              </fields>
            </defaultFieldGroup>
          </fieldGroups>
          <subLists/>
        </subTab>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONADDRESS</id>
      <label>Address</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>TRANSACTIONACCOUNTING</id>
      <label>Accounting</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="accountinformation">
          <label>Account Information</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
        </fieldGroup>
        <fieldGroup scriptid="taxinformation">
          <label>Tax Information</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>ISTAXABLE</id>
              <label>Taxable</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>TAXITEM</id>
              <label>Tax</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>TAXRATE</id>
              <label>Tax %</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="revenue">
          <label>Revenue</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
        </fieldGroup>
        <fieldGroup scriptid="grossprofit">
          <label>Gross Profit</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
        </fieldGroup>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>VATREGNUM</id>
              <label>Tax ID</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>TOTALCOSTESTIMATE</id>
              <label>Est. Extended Cost</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ESTGROSSPROFIT</id>
              <label>Est. Gross Profit</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ESTGROSSPROFITPERCENT</id>
              <label>Est. Gross Profit Percent</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>TRANSACTIONRELATIONSHIPS</id>
      <label>Relationships</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>CRMCONTACTS</id>
          <label>Contacts</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>PARTNERS</id>
          <label>Partners</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONCOMMUNICATION</id>
      <label>Communication</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subTab>
          <id>TRANSACTIONOUTPUTOPTIONS</id>
          <label>Messages</label>
          <visible>T</visible>
          <fieldGroups>
            <defaultFieldGroup>
              <fields position="MIDDLE">
                <field>
                  <id>TOBEPRINTED</id>
                  <label>To Be Printed</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
                </field>
                <field>
                  <id>TOBEEMAILED</id>
                  <label>To Be E-mailed</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
                </field>
                <field>
                  <id>TOBEFAXED</id>
                  <label>To Be Faxed</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                  <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
                </field>
                <field>
                  <id>MESSAGESEL</id>
                  <label>Select Message</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>MESSAGE</id>
                  <label>Customer Message</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
              </fields>
            </defaultFieldGroup>
          </fieldGroups>
          <subLists>
            <subList>
              <id>MESSAGES</id>
              <label>Messages</label>
              <visible>T</visible>
              <neverEmpty>F</neverEmpty>
            </subList>
          </subLists>
        </subTab>
        <subList>
          <id>ACTIVITIES</id>
          <label>Activities</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>EVENTS</id>
          <label>Events</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>TASKS</id>
          <label>Tasks</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>CALLS</id>
          <label>Phone Calls</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>MEDIAITEM</id>
          <label>Files</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>USERNOTES</id>
          <label>User Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONRELATEDRECORDS</id>
      <label>Related Records</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>OPPORTUNITY</id>
              <label>Opportunity</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>LINKS</id>
          <label>Related Records</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>CASES</id>
          <label>Support Cases</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>TRANSFORMATIONS</id>
          <label>Transformations</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONSYSTEMINFORMATION</id>
      <label>System Information</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_sortboothnum]</id>
              <label>Sort Booth Number</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_invoice]</id>
              <label>Invoiced</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_hasbalance]</id>
              <label>Has Balance</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_finvoice]</id>
              <label>Final Invoice</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_audit]</id>
              <label>Audit</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>PARTNER</id>
              <label>Partner</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_jobshow_name]</id>
              <label>Job/Event Name</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>SYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>WORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>CUSTOM</id>
      <label>Custom</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_to_be_deleted]</id>
              <label>Web Order To Be Deleted</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_check_num]</id>
              <label>Check #</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_isweborder]</id>
              <label>Is Web Order</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_invoice_num]</id>
              <label>Invoice Number</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupaysp_oauth_token]</id>
              <label>Solupay OAuth Token</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_upload_docs_ids]</id>
              <label>Upload Document IDs</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_web_enc_cc_data]</id>
              <label>PayTrace Web Encrypted Card Data</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_pt_amount_due]</id>
              <label>Amount Due</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[appid=com.newgennow.cseventservices, scriptid=custbody_ng_cs_so_related_pymnt_rnder]</id>
              <label>Related Payments (Template Render)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[appid=com.newgennow.cseventservices, scriptid=custbody_ng_cs_invoice_payments_rndr]</id>
              <label>Invoice Payments Render (Print Template)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311213]</id>
      <label>Upload Files</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>MULTIPARTNER</id>
      <label>Partners</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311212]</id>
      <label>Show Dates</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_28_t1474328_426]</id>
      <label>Proposal Details</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_69_t1516212_733]</id>
      <label>Area Images</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_295_t1310406_924]</id>
      <label>External Payment</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_solupay_extpmt_lastccdigits]</id>
              <label>Last 4 Credit Card Digits</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_ccexpiration]</id>
              <label>Credit Card Expiration Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_cardholdername]</id>
              <label>Cardholder Name</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_authcode]</id>
              <label>Authorization Code</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_transactionid]</id>
              <label>Transaction Id (Tag)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_cctoken]</id>
              <label>Credit Card Token</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_cctype]</id>
              <label>Credit Card Type</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_cavv]</id>
              <label>CAVV (3D Secure)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_eci]</id>
              <label>ECI (3D Secure)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_xid]</id>
              <label>XID (3D Secure)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_actioncode]</id>
              <label>Action Code (3D Secure)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_ipaddress]</id>
              <label>Ip Address</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_accountnumber]</id>
              <label>ACH Account Number</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_routingnumber]</id>
              <label>ACH Routing Number</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_98_t1516212_114]</id>
      <label>Master Billing Detail</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
  </tabs>
  <quickViewFields>
    <field>
      <id>ENTITY</id>
    </field>
    <field>
      <id>TOTAL</id>
    </field>
    <field>
      <id>ORDERSTATUS</id>
    </field>
    <field>
      <id>TRANDATE</id>
    </field>
    <field>
      <id>SALESREP</id>
    </field>
  </quickViewFields>
  <actionbar>
    <buttons>
      <button>
        <id>ALLOCATESALESORDER</id>
        <label>Allocate</label>
        <visible>T</visible>
      </button>
      <button>
        <id>APPROVE</id>
        <label>Approve</label>
        <visible>T</visible>
      </button>
      <button>
        <id>RETURN</id>
        <label>Authorize Return</label>
        <visible>T</visible>
      </button>
      <button>
        <id>AUTOFILL</id>
        <label>Auto Fill</label>
        <visible>T</visible>
      </button>
      <button>
        <id>BILLREMAINING</id>
        <label>Bill Remaining</label>
        <visible>T</visible>
      </button>
      <button>
        <id>CANCELORDER</id>
        <label>Cancel Order</label>
        <visible>T</visible>
      </button>
      <button>
        <id>CLEARSPLITS</id>
        <label>Clear Splits</label>
        <visible>T</visible>
      </button>
      <button>
        <id>REVCOMM</id>
        <label>Commit Revenue</label>
        <visible>T</visible>
      </button>
      <button>
        <id>CREATEDEPOSIT</id>
        <label>Create Deposit</label>
        <visible>T</visible>
      </button>
      <button>
        <id>CREATEAUTHORIZATION</id>
        <label>Create Payment Authorization</label>
        <visible>T</visible>
      </button>
      <button>
        <id>CREATEPICKUP</id>
        <label>Create Pickup</label>
        <visible>T</visible>
      </button>
      <button>
        <id>PROCESS</id>
        <label>Fulfill</label>
        <visible>T</visible>
      </button>
      <button>
        <id>UPDATEREVREC</id>
        <label>Manage Revenue Recognition</label>
        <visible>T</visible>
      </button>
      <button>
        <id>NEXTBILL</id>
        <label>Next Bill</label>
        <visible>T</visible>
      </button>
      <button>
        <id>ITEM_ADDPROJECTITEMS</id>
        <label>Refresh Items from Project</label>
        <visible>T</visible>
      </button>
      <button>
        <id>REJECT</id>
        <label>Reject</label>
        <visible>T</visible>
      </button>
      <button>
        <id>REQUESTFULFILLMENT</id>
        <label>Request Fulfillment</label>
        <visible>T</visible>
      </button>
      <button>
        <id>RESETTER</id>
        <label>Reset</label>
        <visible>T</visible>
      </button>
      <button>
        <id>SUBMITACCEPTPAYMENTAUTHORIZATION</id>
        <label>Save &amp; Accept Payment Authorization</label>
        <visible>T</visible>
      </button>
      <button>
        <id>UPDATEVSOE</id>
        <label>Update VSOE</label>
        <visible>T</visible>
      </button>
    </buttons>
    <menu>
      <menuitem>
        <id>CLOSEREMAINING</id>
        <label>Close</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>EMAIL</id>
        <label>Email</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>FAX</id>
        <label>Fax</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>GLIMPACT</id>
        <label>GL Impact</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>GOTOREGISTER</id>
        <label>Go To Register</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>MAKECOPY</id>
        <label>Make Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>MEMORIZE</id>
        <label>Memorize</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINT</id>
        <label>Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINTGLIMPACT</id>
        <label>Print GL Impact</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINTLABEL</id>
        <label>Print Label</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINTPICKTICK</id>
        <label>Print Picking Ticket</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITACCEPTDEPOSIT</id>
        <label>Save &amp; Accept Deposit</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SAVEEMAIL</id>
        <label>Save &amp; Email</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITFULFILL</id>
        <label>Save &amp; Fulfill</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEW</id>
        <label>Save &amp; New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SAVEPRINT</id>
        <label>Save &amp; Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>ACTIVITYHISTORY</id>
        <label>Show Activity</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <disclaimer/>
  <address/>
  <allowAddMultiple>T</allowAddMultiple>
  <emailMessageTemplate>USE_DEFAULT</emailMessageTemplate>
  <printingType>
    <advanced>
      <printTemplate>[scriptid=custtmpl_tstdrv1483112_101]</printTemplate>
      <emailTemplate>[scriptid=custtmpl_tstdrv1483112_101]</emailTemplate>
    </advanced>
  </printingType>
  <totalBox>
    <totalBoxField>
      <id>TOTAL</id>
      <label>Total</label>
      <visible>T</visible>
    </totalBoxField>
    <totalBoxField>
      <id>SUBTOTAL</id>
      <label>Subtotal</label>
      <visible>T</visible>
    </totalBoxField>
  </totalBox>
  <linkedForms>
    <linkedForm>
      <type>SALES_ORDER_CASH_SALE_TRANSACTION</type>
      <form>USE_DEFAULT</form>
    </linkedForm>
    <linkedForm>
      <type>SALES_ORDER_INVOICE_TRANSACTION</type>
      <form>USE_DEFAULT</form>
    </linkedForm>
    <linkedForm>
      <type>SALES_ORDER_ITEM_FULFILLMENT_TRANSACTION</type>
      <form>USE_DEFAULT</form>
    </linkedForm>
    <linkedForm>
      <type>SALES_ORDER_INVOICE_PACKING_SLIP</type>
      <form>USE_DEFAULT</form>
    </linkedForm>
    <linkedForm>
      <type>SALES_ORDER_INVOICE_PICKING_TICKET</type>
      <form>USE_DEFAULT</form>
    </linkedForm>
    <linkedForm>
      <type>SALES_ORDER_PURCHASE_ORDER_DROP_SHIPMENT_TRANSACTION</type>
      <form>USE_DEFAULT</form>
    </linkedForm>
    <linkedForm>
      <type>SALES_ORDER_PURCHASE_ORDER_SPECIAL_ORDER_TRANSACTION</type>
      <form>USE_DEFAULT</form>
    </linkedForm>
    <linkedForm>
      <type>SALES_ORDER_RETURN_AUTHORIZATION_TRANSACTION</type>
      <form>USE_DEFAULT</form>
    </linkedForm>
  </linkedForms>
  <roles>
    <role>
      <id>CUSTOMROLE1003</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1005</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1021</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1014</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1031</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1012</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1070</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1057</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1013</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>AR_CLERK</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>ACCOUNTANT</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>ACCOUNTANT__REVIEWER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>ADMINISTRATOR</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1023</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>ADVANCED_PARTNER_CENTER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>BOOKKEEPER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CEO</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CEO_HANDS_OFF</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE41</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_ng_cs_account_manager]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_ng_cs_all_accounting]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_ng_cs_customer_center]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_ng_cs_exhibitor_services_rep]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_ng_cs_limited_accounting]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_ng_cs_sales_rep]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1022</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole1150]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMER_CENTER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1032</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1018</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1039</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1028</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_customer_center_oauth]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>DATA_WAREHOUSE_INTEGRATOR</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1047</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1025</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>FULL_ACCESS</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1020</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE42</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE43</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>SALES_ADMINISTRATOR</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>SALES_MANAGER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>SALES_PERSON</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1054</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>SALES_VICE_PRESIDENT</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1019</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1064</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>STORE_MANAGER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1037</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_token_based_auth]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>WAREHOUSE_MANAGER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1033</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1038</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1035</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1036</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1027</id>
      <preferred>F</preferred>
    </role>
  </roles>
</transactionForm>
