<transactionForm scriptid="custform_23_t1474328_188" standard="STANDARDDEPOSIT">
  <name>CS Deposit</name>
  <recordType>DEPOSIT</recordType>
  <inactive>F</inactive>
  <preferred>F</preferred>
  <storedWithRecord>T</storedWithRecord>
  <mainFields>
    <fieldGroup scriptid="primaryinformation">
      <label>Primary Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>CUSTOMFORM</id>
          <label>Custom Form</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANID</id>
          <label>Deposit #</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ACCOUNT</id>
          <label>Account</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TOTAL</id>
          <label>Amount</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANDATE</id>
          <label>Date</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>POSTINGPERIOD</id>
          <label>Posting Period</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>MEMO</id>
          <label>Memo</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TOBEPRINTED</id>
          <label>To Be Printed</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>TRANSACTIONNUMBER</id>
          <label>Transaction Number</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>GENERATETRANIDONSAVE</id>
          <label>Generate TranId on Save</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>UNCHECKED</checkBoxDefault>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="classification">
      <label>Classification</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>DEPARTMENT</id>
          <label>Department</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CLASS</id>
          <label>Job</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="intercompanymanagement">
      <label>Intercompany Management</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
    </fieldGroup>
    <defaultFieldGroup>
      <fields position="MIDDLE">
        <field>
          <id>LOCATION</id>
          <label>Location</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>SUBSIDIARY</id>
          <label>Subsidiary</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_total_paid]</id>
          <label>Total Paid</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_booth_order]</id>
          <label>Booth Order</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_booth]</id>
          <label>Booth</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_balance]</id>
          <label>Balance</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_customer_filter]</id>
          <label>Customer Select</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_booth_size]</id>
          <label>Booth Size</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_deposit_recd]</id>
          <label>Deposit Rec'd</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_booth_actual_exhibitor]</id>
          <label>Booth Exhibitor</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_batchdate]</id>
          <label>Batch Date</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_last4digits]</id>
          <label>Last 4 Digits</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_fundedamount]</id>
          <label>Funded Amount</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_variance]</id>
          <label>Variance</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_invoice]</id>
          <label>Invoice (2)</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_solupay_batchamount]</id>
          <label>Batch Amount</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_paytrace_pay_link_url]</id>
          <label>Payment Link URL</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_cs_event_session]</id>
          <label>Event Session</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_cs_order_type]</id>
          <label>Order Type</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_cs_event_venue]</id>
          <label>CS Event Venue</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_cs_event_est_version_num]</id>
          <label>Version</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_paytrace_conv_fee_exempt]</id>
          <label>Convenience Fee Exempt (2)</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[scriptid=custbody_ng_paytrace_deposit_link_url]</id>
          <label>Deposit Link URL</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </defaultFieldGroup>
  </mainFields>
  <tabs>
    <tab>
      <id>TRANSACTIONITEMS</id>
      <label>Deposits</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_size]</id>
              <label>Size</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_quantity]</id>
              <label>Quantity</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_price_level]</id>
              <label>Price Level</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[appid=com.newgennow.cseventservices, scriptid=custbody_variant]</id>
              <label>Orientation</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_item_category]</id>
              <label>Item Category</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_item]</id>
              <label>Item</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_freight_weight]</id>
              <label>Freight Weight</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_days]</id>
              <label>Days</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_color]</id>
              <label>Color</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_carpet_width]</id>
              <label>Carpet Width</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_carpet_length]</id>
              <label>Carpet Length</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_substrate]</id>
              <label>Graphic Material</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>EXPENSE</id>
          <neverEmpty>F</neverEmpty>
          <columns>
            <column>
              <id>ENTITY</id>
              <label>Name</label>
              <visible>T</visible>
            </column>
            <column>
              <id>AMOUNT</id>
              <label>Amount</label>
              <visible>T</visible>
            </column>
            <column>
              <id>ACCOUNT</id>
              <label>Account</label>
              <visible>T</visible>
            </column>
            <column>
              <id>PAYMENTMETHOD</id>
              <label>Payment Method</label>
              <visible>T</visible>
            </column>
            <column>
              <id>REFNUM</id>
              <label>Number</label>
              <visible>T</visible>
            </column>
            <column>
              <id>DEPARTMENT</id>
              <label>Department</label>
              <visible>T</visible>
            </column>
            <column>
              <id>CLASS</id>
              <label>Job</label>
              <visible>T</visible>
            </column>
            <column>
              <id>LOCATION</id>
              <label>Location</label>
              <visible>T</visible>
            </column>
            <column>
              <id>MEMO</id>
              <label>Memo</label>
              <visible>T</visible>
            </column>
          </columns>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONCOMMUNICATION</id>
      <label>Communication</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ACTIVITIES</id>
          <label>Activities</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>EVENTS</id>
          <label>Events</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>TASKS</id>
          <label>Tasks</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>CALLS</id>
          <label>Phone Calls</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>MEDIAITEM</id>
          <label>Files</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>USERNOTES</id>
          <label>User Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONSYSTEMINFORMATION</id>
      <label>System Information</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_sortboothnum]</id>
              <label>Sort Booth Number</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_invoice]</id>
              <label>Invoiced</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_hasbalance]</id>
              <label>Has Balance</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_finvoice]</id>
              <label>Final Invoice</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_audit]</id>
              <label>Audit</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_jobshow_name]</id>
              <label>Job/Event Name</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>SYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>WORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>CUSTOM</id>
      <label>Custom</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_to_be_deleted]</id>
              <label>Web Order To Be Deleted</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_show_table]</id>
              <label>CS Event</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_check_num]</id>
              <label>Check #</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_isweborder]</id>
              <label>Is Web Order</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_invoice_num]</id>
              <label>Invoice Number</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupaysp_oauth_token]</id>
              <label>Solupay OAuth Token</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[appid=com.newgennow.cseventservices, scriptid=custbody_cseg_ng_cs_job]</id>
              <label>CS Job</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_upload_docs_ids]</id>
              <label>Upload Document IDs</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_paytrace_web_enc_cc_data]</id>
              <label>PayTrace Web Encrypted Card Data</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_pt_amount_due]</id>
              <label>Amount Due</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[appid=com.newgennow.cseventservices, scriptid=custbody_ng_cs_so_related_pymnt_rnder]</id>
              <label>Related Payments (Template Render)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[appid=com.newgennow.cseventservices, scriptid=custbody_ng_cs_invoice_payments_rndr]</id>
              <label>Invoice Payments Render (Print Template)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311213]</id>
      <label>Upload Files</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311212]</id>
      <label>Show Dates</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_28_t1474328_426]</id>
      <label>Proposal Details</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_69_t1516212_733]</id>
      <label>Area Images</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>GLIMPACTTAB</id>
      <label>GL Impact</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_295_t1310406_924]</id>
      <label>External Payment</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_solupay_extpmt_lastccdigits]</id>
              <label>Last 4 Credit Card Digits</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_ccexpiration]</id>
              <label>Credit Card Expiration Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_cardholdername]</id>
              <label>Cardholder Name</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_authcode]</id>
              <label>Authorization Code</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_transactionid]</id>
              <label>Transaction Id (Tag)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_cctoken]</id>
              <label>Credit Card Token</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_cctype]</id>
              <label>Credit Card Type</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_cavv]</id>
              <label>CAVV (3D Secure)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_eci]</id>
              <label>ECI (3D Secure)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_xid]</id>
              <label>XID (3D Secure)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_actioncode]</id>
              <label>Action Code (3D Secure)</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_ipaddress]</id>
              <label>Ip Address</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_accountnumber]</id>
              <label>ACH Account Number</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_solupay_extpmt_routingnumber]</id>
              <label>ACH Routing Number</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_98_t1516212_114]</id>
      <label>Master Billing Detail</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
  </tabs>
  <actionbar>
    <menu>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>GLIMPACT</id>
        <label>GL Impact</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>GOTOREGISTER</id>
        <label>Go To Register</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINT</id>
        <label>Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINTGLIMPACT</id>
        <label>Print GL Impact</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>DEPOSITSUMMARY</id>
        <label>Print Summary</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEW</id>
        <label>Save &amp; New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SAVEPRINT</id>
        <label>Save &amp; Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>ACTIVITYHISTORY</id>
        <label>Show Activity</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <allowAddMultiple>F</allowAddMultiple>
  <emailMessageTemplate>USE_DEFAULT</emailMessageTemplate>
  <roles>
    <role>
      <id>CUSTOMROLE1003</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1013</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>ACCOUNTANT</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>ACCOUNTANT__REVIEWER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>ADMINISTRATOR</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>BOOKKEEPER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CEO</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CEO_HANDS_OFF</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE41</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_ng_cs_all_accounting]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1022</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole1150]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1039</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>DATA_WAREHOUSE_INTEGRATOR</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>FULL_ACCESS</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1019</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1037</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_token_based_auth]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1033</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1036</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE1027</id>
      <preferred>F</preferred>
    </role>
  </roles>
</transactionForm>
