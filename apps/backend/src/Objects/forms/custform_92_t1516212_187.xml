<entryForm scriptid="custform_92_t1516212_187" standard="STANDARDTASKFORM">
  <name>CS Task Form</name>
  <recordType>TASK</recordType>
  <inactive>F</inactive>
  <preferred>T</preferred>
  <storedWithRecord>F</storedWithRecord>
  <mainFields>
    <fieldGroup scriptid="primaryinformation">
      <label>Primary Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>CUSTOMFORM</id>
          <label>Custom Form</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>TITLE</id>
          <label>Title</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>ASSIGNED</id>
          <label>Assigned To</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>SENDEMAIL</id>
          <label>Notify Assignee by Email</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>PARENT</id>
          <label>Parent Task</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>PRIORITY</id>
          <label>Priority</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>ORDER</id>
          <label>Insert Before</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>STATUS</id>
          <label>Status</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>ACCESSLEVEL</id>
          <label>Private Task</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="dateandtime">
      <label>Date and Time</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>STARTDATE</id>
          <label>Start Date</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>DUEDATE</id>
          <label>Due Date</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>COMPLETEDDATE</id>
          <label>Date Completed</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>TIMEDEVENT</id>
          <label>Reserve Time</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>STARTTIME</id>
          <label>Start Time</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>ENDTIME</id>
          <label>End Time</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>REMINDERTYPE</id>
          <label>Reminder Type</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>REMINDERMINUTES</id>
          <label>Reminder</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <defaultFieldGroup/>
  </mainFields>
  <tabs>
    <tab>
      <id>CRMMESSAGE</id>
      <label>Message</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>MESSAGE</id>
              <label/>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>CRMRELATEDINFO</id>
      <label>Related Records</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>COMPANY</id>
              <label>Company</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>CONTACT</id>
              <label>Contact</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>SUPPORTCASE</id>
              <label>Support Case</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>TRANSACTION</id>
              <label>Transaction</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=custevent_ng_cs_task_show]</id>
              <label>CS Event</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>[scriptid=custevent_ng_cs_task_function]</id>
              <label>CS Event Function</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>EVENTCONTACT</id>
          <label>Companies and Contacts</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subTab>
          <id>CRMCONTACTS</id>
          <label>Contacts</label>
          <visible>T</visible>
          <fieldGroups>
            <defaultFieldGroup/>
          </fieldGroups>
          <subLists/>
        </subTab>
      </subItems>
    </tab>
    <tab>
      <id>EVENTICALENDAR</id>
      <label>Availability</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>EVENTINVITECALENDAR</id>
          <label>Availability</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>EVENTCOMMUNICATION</id>
      <label>Communication</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>EVENTMEDIAITEM</id>
          <label>Files</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>EVENTUSERNOTES</id>
          <label>User Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subTab>
          <id>EVENTMEDIA</id>
          <label>Files</label>
          <visible>F</visible>
          <fieldGroups>
            <defaultFieldGroup/>
          </fieldGroups>
          <subLists/>
        </subTab>
      </subItems>
    </tab>
    <tab>
      <id>EVENTTIME</id>
      <label>Time Tracking</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>ESTIMATEDTIME</id>
              <label>Initial Time Budget</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>ESTIMATEDTIMEOVERRIDE</id>
              <label>Current Time Budget</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>ACTUALTIME</id>
              <label>Actual Time</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>TIMEREMAINING</id>
              <label>Time Remaining</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>PERCENTTIMECOMPLETE</id>
              <label>Percent Time Complete</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
            <field>
              <id>PERCENTCOMPLETE</id>
              <label>Percent Complete</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>EVENTTIMEITEM</id>
          <label>Time Tracking</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>CRMSYSTEMINFORMATION</id>
      <label>System Information</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>OWNER</id>
              <label>Created By</label>
              <visible>T</visible>
              <mandatory>T</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <quickAdd>F</quickAdd>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>EVENTSYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>EVENTACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>EVENTWORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>EVENTCUSTOM</id>
      <label>Custom</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311222]</id>
      <label>Attachments</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtabtstdrv148311221]</id>
      <label>Message</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
  </tabs>
  <quickViewFields>
    <field>
      <id>STATUS</id>
    </field>
    <field>
      <id>PRIORITY</id>
    </field>
    <field>
      <id>DUEDATE</id>
    </field>
    <field>
      <id>ASSIGNED</id>
    </field>
    <field>
      <id>STARTDATE</id>
    </field>
  </quickViewFields>
  <actionbar>
    <menu>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>MAKECOPY</id>
        <label>Make Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINT</id>
        <label>Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITCOPY</id>
        <label>Save &amp; Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEW</id>
        <label>Save &amp; New</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <editingInList>T</editingInList>
</entryForm>
