/* eslint-disable suitescript/module-vars,no-useless-escape,no-control-regex */
// noinspection JSCheckFunctionSignatures,JSUnresolvedVariable,DuplicatedCode.ES6ConvertVarToLetConst

/**
 * newgen.library.v2.pt.js
 * @NApiVersion 2.x
 * @NModuleScope Public
 */


define(['N/error', 'N/format', 'N/record', 'N/runtime', 'N/search', 'N/util', 'N/xml'],
	/**
	 * @param {error} error
	 * @param {format} format
	 * @param {record} record
	 * @param {search} search
	 * @param {runtime} runtime
	 * @param {util} util
	 * @param {xml} xml
	 */
	function(error, format, record, runtime, search, util, xml) {
		
		var TOOLS = {
				/**
				 * Checks a passed in variable to see if it's null, unassigned, or an empty string
				 * @param {*} value
				 * @returns {boolean}
				 */
				isEmpty : function(value) {
					return isEmpty(value);
				}
			
				/**
				 * Checks a passed in Object to see if it's null, unassigned, or an empty string
				 * @param {Object} object
				 * @returns {boolean}
				 */
			,	isEmptyObject : function(object) {
					if (isEmpty(object)) {
						return true;
					}
					return Object.keys(object).length === 0;
				}
			
				/**
				 * Scans a passed in array to see if the referenced value is contained within
				 * @param {*} value Value to look for
				 * @param {array} arr Array to scan
				 * @param {boolean} caseInsensitive Perform a case insensitive scan of the array (only valid for strings)
				 * @returns {boolean}
				 */
			,	isInArray : function(value, arr, caseInsensitive)  {
					return isInArray(value, arr, caseInsensitive);
				}
			
			,	isInObject : function(value, object) {
					if (isEmpty(object)) {
						return false;
					} else if (isEmpty(value)) {
						return false;
					}
					var isFound = true;
					Object.keys(object).forEach(function(key) {
						if (!isFound) {
							if (object[key] === value) {
								isFound = true;
							}
						}
					});
					
					return isFound;
				}
			
			,	hasKey : function(value, obj) {
					if (isEmpty(obj)) {
						return false;
					} else if (isEmpty(value)) {
						return false;
					}
					return obj.hasOwnProperty.call(obj, value);
				}
			
				/**
				 *
				 *  Javascript trim, ltrim, rtrim
				 *  http://www.webtoolkit.info/
				 *
				 **/
			,	trim : function(str, chars) {
					return TOOLS.ltrim(TOOLS.rtrim(str, chars), chars);
				}
			
			,	ltrim : function(str, chars) {
					chars = chars || "\\s";
					return str.replace(new RegExp("^[" + chars + "]+", "g"), "");
				}
			
			,	rtrim : function(str, chars) {
					chars = chars || "\\s";
					return str.replace(new RegExp("[" + chars + "]+$", "g"), "");
				}
				/**  End */
			
				/**
				 * Creates a string of random characters
				 * @param {Number} llen Amount of letter characters to add; Ignored if rlen is set
				 * @param nlen Amount of number characters to add; Ignored if rlen is set
				 * @param [rlen] Amount of mixed letter and number characters to add; Takes precedence over llen and nlen
				 * @param [isPW] Adds special characters for password generation; Requires rlen to be set
				 * @returns {string}
				 */
			,	randomString : function(llen, nlen, rlen, isPW) {
					var letters = ["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];
					var lower = ["a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"];
					var numbers = ["0","1","2","3","4","5","6","7","8","9"];
					var symbols = ["!","@","#","$","%","*","="];
					var rcode = "", ll = letters.length, nl = numbers.length, pwTest = /[ IilO01]/g, pos;
					isPW = isPW || false;
					
					if (isEmpty(rlen) || isNaN(Number(rlen)) || (Number(rlen)) === 0) {
						for (var l = 0; l < llen; l++) {
							pos = Math.floor(Math.random() * ll);
							rcode += letters[pos];
						}
						
						for (var n = 0; n < nlen; n++) {
							pos = Math.floor(Math.random() * nl);
							rcode += numbers[pos];
						}
					} else {
						var full = [];
						if (isPW) {
							full = letters.concat(lower, numbers, symbols);
						} else {
							full = letters.concat(numbers);
						}
						var rl = full.length;
						
						for (var r = 0; r < rlen; r++) {
							if (isPW) {
								var char = " ";
								do {
									pos = Math.floor(Math.random() * rl);
									char = full[pos];
								} while (pwTest.test(char));
								rcode += char;
							} else {
								pos = Math.floor(Math.random() * rl);
								rcode += full[pos];
							}
						}
					}
					
					return rcode;
				}
			
				/**
				 * @param {Object} keyedObject Data object with keyed values
				 * @returns {Number} Total count of unique data keys
				 */
			,	getKeyCount : function(keyedObject) {
					var counter = Number(0);
					
					if (this.isEmpty(keyedObject) || this.isEmptyObject(keyedObject)) {
						return counter;
					}
					
					for (var key in keyedObject) {
						if (key) {
							counter++;
						}
					}
					
					return counter;
				}
			
				/**
				 * @param {string} [recType] Record type on which to perform search
				 * @param {Array} [filt] Search filters
				 * @param {Array} [cols] Search result columns
				 * @param {string} [searchId] Script ID of a saved search
				 * @param {number} [pageSize] Size of page for pages search results
				 * @param {boolean} [returnIDs] Only return the IDs of records in results
				 * @param {boolean} [firstPageOnly] Only return the first page of search results
				 */
			,	getSearchResults : function(recType, filt, cols, searchId, pageSize, returnIDs, firstPageOnly) {
					var results = [];
					pageSize = pageSize || 1000;
					returnIDs = returnIDs || false;
					firstPageOnly = firstPageOnly || false;
					var searchInit;
					if (isEmpty(searchId)) {
						searchInit = search.create({ type : recType , filters : filt , columns : cols });
					} else {
						var savedSearch;
						if (isEmpty(recType)) {
							savedSearch = search.load({ id : searchId });
						} else {
							savedSearch = search.load({ type : recType , id : searchId });
						}
						var searchFilters = savedSearch.filterExpression;
						var searchColumns = savedSearch.columns;
						var finalFilt = [];
						var finalCols = [];
						if (!isEmpty(searchFilters) && util.isArray(searchFilters)) {
							if (!isEmpty(filt) && util.isArray(filt)) {
								if (filt.length > 0) {
									finalFilt = searchFilters.concat(["and"], filt);
								} else {
									finalFilt = searchFilters.concat(filt);
								}
							} else {
								finalFilt = searchFilters.concat([]);
							}
						} else {
							if (!isEmpty(filt) && util.isArray(filt)) {
								finalFilt = [].concat(filt);
							} else {
								finalFilt = null;
							}
						}
						if (!isEmpty(searchColumns) && util.isArray(searchColumns)) {
							if (!isEmpty(cols) && util.isArray(cols)) {
								finalCols = searchColumns.concat(cols);
							} else {
								finalCols = searchColumns.concat([]);
							}
						} else {
							if (!isEmpty(cols) && util.isArray(cols)) {
								finalCols = [].concat(cols);
							} else {
								finalCols = null;
							}
						}
						searchInit = search.create({ type : recType , filters : finalFilt , columns : finalCols });
					}
					var pages = searchInit.runPaged({ pageSize : pageSize });
					for (var pg = 0; pg < pages.pageRanges.length; pg++) {
						var page = pages.fetch({ index : pages.pageRanges[pg].index });
						if (returnIDs) {
							for (var p = 0; p < page.data.length; p++) {
								results.push(page.data[p].id);
							}
						} else {
							results = results.concat(page.data);
						}
						
						if (firstPageOnly) {
							if (page.isFirst) {
								break;
							}
						}
					}
					
					return results.length > 0 ? results : null;
				}
			
				/**
				 * @param {Object} options
				 * @param {string} [options.type] Record type on which to perform search
				 * @param {Array} [options.filters] Search filters
				 * @param {Array} [options.filterExp] Filter expression for search
				 * @param {Array} [options.columns] Search result columns
				 * @param {string} [options.id] Script ID of a saved search
				 * @param {number} [options.pageSize] Size of page for pages search results
				 * @param {boolean} [options.returnIds] Only return the IDs of records in results
				 * @param {boolean} [options.firstPage] Only return the first page of search results
				 */
			,	getSearchResultsAdv : function(options) {
					var pageSize = options.pageSize || 1000;
					var returnIDs = options.returnIds || false;
					var firstPageOnly = options.firstPage || false;
					var filtA = options.filters || null;
					var filtB = options.filterExp || null;
					var filt = filtA || filtB || [];
					var cols = options.columns || [];
					var recType = options.type || null;
					var searchId = options.id || null;
					
					return TOOLS.getSearchResults(recType, filt, cols, searchId, pageSize, returnIDs, firstPageOnly);
				}
			
				/**
				 * Retrieves the internal IDs of a set of search results
				 * @param {Result[]} searchResults Array of search result objects
				 * @param {boolean} [getUnique=false] Filters results to not return duplicate internal IDs
				 * @returns {*[]}
				 */
			,	getResultsIdList : function(searchResults, getUnique) {
					getUnique = getUnique || false;
					var list = [];
					if (isEmpty(searchResults)) {
						return list;
					} else if (!util.isArray(searchResults)) {
						return list;
					}
					for (var i = 0; i < searchResults.length; i++) {
						var id = searchResults[i].id;
						if (getUnique) {
							if (!isInArray(list, id)) {
								list.push(id);
							}
						} else {
							list.push(id);
						}
					}
					
					return list;
				}
			
			,	rescheduleScriptFromSearch : function(context, i, search, limit, params, isLargeSearch, fail) {
					/*var queued = false;
					var proceed = true;
					var triggered = false;
					var yieldObj = null;
					var num = Number(((i + 1) / search.length) * 100);
					context.setPercentComplete(num.toFixed(2));
					context.getPercentComplete();
					
					var remainingUsage = context.getRemainingUsage();
					
					if ((isLargeSearch && remainingUsage <= limit) || (!isLargeSearch && ((remainingUsage <= limit && (i + 1) < search.length) || (i + 1 == 990)))) {
						triggered = true;
						var ys = nlapiYieldScript();
						if (ys.status == "FAILURE") {
							yieldObj = ys;
							nlapiLogExecution("ERROR", "Unable to yield script. Attempting to reschedule script...", "[" + ys.reason + "] " + ys.information);
							if (fail)
								return { queued : false , proceed : false , triggered : triggered , yld : yieldObj };
							
							if (ys.reason != "SS_EXCESSIVE_MEMORY_FOOTPRINT") {
								var status = null;
								while (remainingUsage >= 25 && status != "QUEUED") {
									status = nlapiScheduleScript(context.getScriptId(), context.getDeploymentId(), params);
									if (status == 'QUEUED') {
										queued = true;
									}
								}
							}
							
							if (queued) {
								nlapiLogExecution("AUDIT", "Script queued for additional execution.", "Remaining script usage: " + remainingUsage + " -- Ending position: " + i + " -- Per cent complete: " + num.toFixed(2) + "%");
							} else {
								nlapiLogExecution("ERROR", "Script could not be queued for additional execution.", "Remaining script usage: " + remainingUsage + " -- Ending position: " + i + " -- Per cent complete: " + num.toFixed(2) + "%");
							}
							
							proceed = false;
						}
					}
					
					return { queued : queued , proceed : proceed , triggered : triggered , yld : yieldObj };*/
				}
			
			,	rescheduleScriptOnCount : function(context, i, length, count) {
					/*var queued = false;
					var proceed = true;
					var triggered = false;
					var yieldObj = null;
					
					var num = Number(((i + 1) / length) * 100);
					context.setPercentComplete(num.toFixed(2));
					context.getPercentComplete();
					var remainingUsage = context.getRemainingUsage();
					
					if (Math.round(i + 1) % count == 0) {
						triggered = true;
						var ys = nlapiYieldScript();
						if (ys.status == "FAILURE") {
							yieldObj = ys;
							nlapiLogExecution("ERROR", "Unable to yield script. Attempting to reschedule script...", "[" + ys.reason + "] " + ys.information);
							if (fail) {
								return { queued : false , proceed : false , triggered : triggered , yld : yieldObj };
							}
							
							if (ys.reason != "SS_EXCESSIVE_MEMORY_FOOTPRINT") {
								var status = null;
								while (remainingUsage >= 25 && status != "QUEUED") {
									status = nlapiScheduleScript(context.getScriptId(), context.getDeploymentId(), params);
									if (status == 'QUEUED') {
										queued = true;
									}
								}
							}
							
							if (queued) {
								nlapiLogExecution("AUDIT", "Script queued for additional execution.", "Remaining script usage: " + remainingUsage + " -- Ending position: " + i + " -- Per cent complete: " + num.toFixed(2) + "%");
							} else {
								nlapiLogExecution("ERROR", "Script could not be queued for additional execution.", "Remaining script usage: " + remainingUsage + " -- Ending position: " + i + " -- Per cent complete: " + num.toFixed(2) + "%");
							}
							
							proceed = false;
						}
					}
					
					return { queued : queued , proceed : proceed , triggered : triggered , yld : yieldObj };*/
				}
			
				/**
				 * Converts an XML document to JSON
				 * @param {document} xmlDoc XML document
				 * @param {boolean} [fixText=false] Cleans up resulting JSON to eliminate node values being entered under '#text' attribute
				 * @returns {*}
				 */
			,	xmlToJSON : function(xmlDoc, fixText) {
					fixText = fixText || true;
					var obj = { };
					if (xmlDoc.nodeType === 1 || xmlDoc.nodeType === "ELEMENT_NODE") {
						if (xmlDoc.hasAttributes() === true) {
							obj["@attributes"] = { };
							Object.keys(xmlDoc.attributes).forEach(function(attrb) {
								var attr = xmlDoc.attributes[attrb];
								obj["@attributes"][attr.name] = attr.value;
							});
						}
					} else if (xmlDoc.nodeType === 3 || xmlDoc.nodeType === "TEXT_NODE") {
						obj = xmlDoc.nodeValue;
					}
					if (xmlDoc.hasChildNodes()) {
						xmlDoc.childNodes.forEach(function(node) {
							var nodeName = node.nodeName;
							if (typeof (obj[nodeName]) == "undefined") {
								obj[nodeName] = TOOLS.xmlToJSON(node);
							} else {
								if (typeof (obj[nodeName].push) == "undefined") {
									var old = obj[nodeName];
									obj[nodeName] = [];
									obj[nodeName].push(old);
								}
								obj[nodeName].push(TOOLS.xmlToJSON(node));
							}
						});
					}
					
					if (fixText) {
						return TOOLS.fixTextXmlJson(TOOLS.fixEmptyXmlJson(TOOLS.cleanXmlJson(obj)));
					} else {
						return TOOLS.fixEmptyXmlJson(TOOLS.cleanXmlJson(obj));
					}
				}
			
			,	cleanXmlJson : function(obj) {
					for (var property in obj) {
						if (obj.hasOwnProperty.call(obj, property)) {
							if (util.isArray(obj[property]) && property === "#text") {
								delete(obj[property]);
							} else if (typeof obj[property] === "object") {
								TOOLS.cleanXmlJson(obj[property]);
							}
						}
					}
					
					return obj;
				}
			
			,	fixEmptyXmlJson : function(obj) {
					for (var property in obj) {
						if (obj.hasOwnProperty.call(obj, property)) {
							if (typeof obj[property] === "object") {
								if (Object.keys(obj[property]).length > 0) {
									TOOLS.cleanXmlJson(obj[property]);
								} else {
									obj[property] = {
										"#text" : ""
									};
								}
							}
						}
					}
					
					return obj;
				}
			
			,	fixTextXmlJson : function(obj) {
					for (var property in obj) {
						if (obj.hasOwnProperty.call(obj, property)) {
							if (typeof obj[property] === "object") {
								var keys = Object.keys(obj[property]);
								if (keys.length > 1) {
									TOOLS.fixTextXmlJson(obj[property]);
								} else {
									if (keys[0] === "#text") {
										obj[property] = obj[property]['#text'];
									} else {
										TOOLS.fixTextXmlJson(obj[property]);
									}
								}
							}
						}
					}
					
					return obj;
				}
			
				/**
				 * Converts JSON object to an XML document
				 * @param o
				 * @param tab
				 * @returns {string}
				 */
			,	jsonToXML : function(o,tab) {
					var toXml = function(v, name, ind) {
						var xml = "", m;
						if (v instanceof Array) {
							for (var i=0, n=v.length; i<n; i++) {
								xml += ind + toXml(v[i], name, ind+"\t") + "\n";
							}
						}
						else if (typeof(v) === "object") {
							var hasChild = false;
							xml += ind + "<" + name;
							for (m in v) {
								if (m.charAt(0) === "@") {
									xml += " " + m.substr(1) + "=\"" + v[m].toString() + "\"";
								} else {
									hasChild = true;
								}
							}
							xml += hasChild ? ">" : "/>";
							if (hasChild) {
								for (m in v) {
									if (m === "#text") {
										xml += v[m];
									} else if (m === "#cdata") {
										xml += "<![CDATA[" + v[m] + "]]>";
									} else if (m.charAt(0) !== "@") {
										xml += toXml(v[m], m, ind+"\t");
									}
								}
								xml += (xml.charAt(xml.length-1)==="\n"?ind:"") + "</" + name + ">";
							}
						} else {
							xml += ind + "<" + name + ">" + v.toString() +  "</" + name + ">";
						}
						return xml;
					}, xml="";
					for (var m in o) {
						xml += toXml(o[m], m, "");
					}
					
					return tab ? xml.replace(/\t/g, tab) : xml.replace(/\t|\n/g, "");
				}
			
				/**
				 * Validates an email address to verify it meets NetSuite email requirements
				 * @param {string} emailAddress
				 * @returns {boolean}
				 */
			,	checkNSEmailAddress : function(emailAddress) {
					var sQtext = '[^\\x0d\\x22\\x5c\\x80-\\xff]';
					var sDtext = '[^\\x0d\\x5b-\\x5d\\x80-\\xff]';
					var sAtom = '[^\\x00-\\x20\\x22\\x28\\x29\\x2c\\x2e\\x3a-\\x3c\\x3e\\x40\\x5b-\\x5d\\x7f-\\xff]+';
					var sQuotedPair = '\\x5c[\\x00-\\x7f]';
					var sDomainLiteral = '\\x5b(' + sDtext + '|' + sQuotedPair + ')*\\x5d';
					var sQuotedString = '\\x22(' + sQtext + '|' + sQuotedPair + ')*\\x22';
					var sDomain_ref = sAtom;
					var sSubDomain = '(' + sDomain_ref + '|' + sDomainLiteral + ')';
					var sWord = '(' + sAtom + '|' + sQuotedString + ')';
					var sDomain = sSubDomain + '(\\x2e' + sSubDomain + ')*';
					var sLocalPart = sWord + '(\\x2e' + sWord + ')*';
					var sAddrSpec = sLocalPart + '\\x40' + sDomain; // complete RFC822 email address spec
					var sValidEmail = '^' + sAddrSpec + '$'; // as whole string
					
					var reValidEmail = new RegExp(sValidEmail);
					
					var firstCheck = reValidEmail.test(emailAddress);
					
					var lastAtPos = emailAddress.lastIndexOf('@');
					var lastDotPos = emailAddress.lastIndexOf('.');
					var secondCheck = (lastAtPos < lastDotPos && lastAtPos > 0 && emailAddress.indexOf('@@') === -1 && lastDotPos > 2 && (emailAddress.length - lastDotPos) > 2);
					
					var nsRegEx = new RegExp('[",: <>;]', "g");
					var thirdCheck = !nsRegEx.test(emailAddress);
					
					var fourthMatch = emailAddress.match(/@/g);
					var fourthCheck = fourthMatch != null && fourthMatch.length === 1;
					
					return (firstCheck && secondCheck && thirdCheck && fourthCheck);
				}
			
				/**
				 * Marks an open form in NetSuite as changed or not changed
				 * @param {boolean} [isChanged=false]
				 */
			,	clearFormChanged : function(isChanged) {
					NS = NS || null;
					isChanged = isChanged || false;
					if (!isEmpty(NS)) {
						if (NS.form.isChanged())
							NS.form.setChanged(isChanged);
					}
				}
			
				/**
				 * Generates a text value based upon passed in arguments for the purpose of creating an identifier value on a transaction item line that gets passed along the transaction chain
				 * @returns {null|*}
				 */
			,	createLineCode : function() {
					if (!isEmpty(arguments)  && arguments.length > 0) {
						var codeString = "";
						for (var a = 0; a < arguments.length; a++) {
							codeString += "{" + a.toFixed(0) + "}";
						}
						return codeString.NG_Format.apply(codeString, arguments);
					} else {
						return null;
					}
				}
			
			,	generateUUID : function() {
					var d = new Date().getTime();
					var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
						var r = (d + Math.random()*16)%16 | 0;
						d = Math.floor(d/16);
						return (c=='x' ? r : (r&0x3|0x8)).toString(16);
					});
					return uuid;
				}
			
				/**
				 * @param {Array} a Array object to chunkify
				 * @param {Number} n Number object for how many chunks to create
				 * @param {Boolean} balanced Boolean object to determine if chunk lengths should be balanced; ignored if nLength parameter is TRUE
				 * @param {Boolean} nLength Boolean object to determine if n is the chunk length instead of chunk count
				 * @returns {Array} Array of array chunks created from passed in array
				 */
			,	chunkify : function(a, n, balanced, nLength) { // only use "balanced" set to true to produce as equal as possible length n-count of arrays; not using balanced produces chunk lengths based on len/(n-1) rounded down
					if (a.length === 0 || !a.length) {
						return [];
					} else if ((!n || n < 1) && nLength) {
						return a;
					} else if (n < 2 && !nLength) {
						return [a];
					}
					
					var len = a.length,
						out = [],
						i = 0,
						size;
					
					if (nLength) {
						for (i = 0; i < len; i += n) {
							out.push(a.slice(i, i + n));
						}
					} else {
						if (len % n === 0) {
							size = Math.floor(len / n);
							while (i < len) {
								out.push(a.slice(i, i += size));
							}
						} else if (balanced) {
							while (i < len) {
								size = Math.ceil((len - i) / n--);
								out.push(a.slice(i, i += size));
							}
						} else {
							n--;
							size = Math.floor(len / n);
							if (len % size === 0) {
								size--;
							}
							while (i < size * n) {
								out.push(a.slice(i, i += size));
							}
							out.push(a.slice(size * n));
						}
					}
					
					return out;
				}
			
				/**
				 * @param {Array} a Array object to chunkify
				 * @param {Number} n Number object for max length of chunks to create
				 * @returns {Array} Array of array chunks created from passed in array
				 */
			,	chunked : function(a, n) { // produce one array full of arrays of max length n
					var R = [];
					if (a.length === 0 || !a.length) {
						return R;
					} else if (!n || n < 1) {
						return a;
					}
					for (var i = 0; i < a.length; i += n) {
						R.push(a.slice(i, i + n));
					}
					return R;
				}
			
				/**
				 * Function to encode/decode base64 data to/from UTF-8
				 * @deprecated Preferred method is to use the NetSuite N/encode module and encode.convert() function; Still preferred for client-side applications
				 */
			,	B64 : {
					alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
					lookup: null,
					ie: /*navigator ? /MSIE /.test(navigator.userAgent) :*/ false,
					ieo: /*navigator ? /MSIE [67]/.test(navigator.userAgent) :*/ false,
					encode: function(s) {
						var buffer = this.toUtf8(s),
							position = -1,
							len = buffer.length,
							nan1, nan2, enc = [, , , ];
						if (this.ie) {
							var result = [];
							while (++position < len) {
								nan1 = buffer[position + 1], nan2 = buffer[position + 2];
								enc[0] = buffer[position] >> 2;
								enc[1] = ((buffer[position] & 3) << 4) | (buffer[++position] >> 4);
								if (isNaN(nan1)) enc[2] = enc[3] = 64;
								else {
									enc[2] = ((buffer[position] & 15) << 2) | (buffer[++position] >> 6);
									enc[3] = (isNaN(nan2)) ? 64 : buffer[position] & 63;
								}
								result.push(this.alphabet[enc[0]], this.alphabet[enc[1]], this.alphabet[enc[2]], this.alphabet[enc[3]]);
							}
							return result.join('');
						} else {
							result = '';
							while (++position < len) {
								nan1 = buffer[position + 1], nan2 = buffer[position + 2];
								enc[0] = buffer[position] >> 2;
								enc[1] = ((buffer[position] & 3) << 4) | (buffer[++position] >> 4);
								if (isNaN(nan1)) enc[2] = enc[3] = 64;
								else {
									enc[2] = ((buffer[position] & 15) << 2) | (buffer[++position] >> 6);
									enc[3] = (isNaN(nan2)) ? 64 : buffer[position] & 63;
								}
								result += this.alphabet[enc[0]] + this.alphabet[enc[1]] + this.alphabet[enc[2]] + this.alphabet[enc[3]];
							}
							return result;
						}
					},
					decode: function(s) {
						var buffer = this.fromUtf8(s),
							position = 0,
							len = buffer.length,
							result;
						if (this.ieo) {
							result = [];
							while (position < len) {
								if (buffer[position] < 128) result.push(String.fromCharCode(buffer[position++]));
								else if (buffer[position] > 191 && buffer[position] < 224) result.push(String.fromCharCode(((buffer[position++] & 31) << 6) | (buffer[position++] & 63)));
								else result.push(String.fromCharCode(((buffer[position++] & 15) << 12) | ((buffer[position++] & 63) << 6) | (buffer[position++] & 63)));
							}
							return result.join('');
						} else {
							result = '';
							while (position < len) {
								if (buffer[position] < 128) result += String.fromCharCode(buffer[position++]);
								else if (buffer[position] > 191 && buffer[position] < 224) result += String.fromCharCode(((buffer[position++] & 31) << 6) | (buffer[position++] & 63));
								else result += String.fromCharCode(((buffer[position++] & 15) << 12) | ((buffer[position++] & 63) << 6) | (buffer[position++] & 63));
							}
							return result;
						}
					},
					toUtf8: function(s) {
						var position = -1,
							len = s.length,
							chr, buffer = [];
						if (/^[\x00-\x7f]*$/.test(s)) while (++position < len)
							buffer.push(s.charCodeAt(position));
						else while (++position < len) {
							chr = s.charCodeAt(position);
							if (chr < 128) buffer.push(chr);
							else if (chr < 2048) buffer.push((chr >> 6) | 192, (chr & 63) | 128);
							else buffer.push((chr >> 12) | 224, ((chr >> 6) & 63) | 128, (chr & 63) | 128);
						}
						return buffer;
					},
					fromUtf8: function(s) {
						var position = -1,
							len, buffer = [],
							enc = [, , , ];
						if (!this.lookup) {
							len = this.alphabet.length;
							this.lookup = { };
							while (++position < len)
								this.lookup[this.alphabet[position]] = position;
							position = -1;
						}
						len = s.length;
						while (position < len) {
							enc[0] = this.lookup[s.charAt(++position)];
							enc[1] = this.lookup[s.charAt(++position)];
							buffer.push((enc[0] << 2) | (enc[1] >> 4));
							enc[2] = this.lookup[s.charAt(++position)];
							if (enc[2] === 64) break;
							buffer.push(((enc[1] & 15) << 4) | (enc[2] >> 2));
							enc[3] = this.lookup[s.charAt(++position)];
							if (enc[3] === 64) break;
							buffer.push(((enc[2] & 3) << 6) | enc[3]);
						}
						return buffer;
					}
				}
			
			,	getMultiSelect : function(field, record, getText, lookupValue) {
					var selections = [];
					
					if (!isEmpty(record) && !isEmpty(field)) {
						if (getText) {
							selections = record.getText({ fieldId : field }) || [];
						} else {
							selections = record.getValue({ fieldId : field }) || [];
						}
					} else if (!isEmpty(lookupValue)) {
						var values = lookupValue;
						for (var v = 0; v < values.length; v++) {
							if (getText) {
								selections.push(values[v].text);
							} else {
								selections.push(values[v].value);
							}
						}
					}
					
					return selections;
				}
			
				/**
				 * Wrapper for the N/search module's lookupFields() function to return results similar to the 1.0 lookup output
				 * @param {string} recType Record type for the lookup
				 * @param {string|number} recId Record ID for the lookup
				 * @param {string[]} fields Array of field IDs to return
				 * @param {string[]} [sSelFields] Array of field IDs for select fields being returned
				 * @param {string[]} [mSelFields] Array of field IDs for multi-select fields being returned
				 * @param {boolean} [getText] Get text values for (multi)select fields; For select fields an attribute of fieldid_text will be added
				 * @returns {{}}
				 */
			,	getLookupFields : function(recType, recId, fields, sSelFields, mSelFields, getText) {
					sSelFields = sSelFields || [];
					mSelFields = mSelFields || [];
					getText = getText || false;
					var results = { };
					var fieldSearch = search.lookupFields({
							type : recType
						,	id : recId
						,	columns : fields
					});
					
					for (var field in fieldSearch) {
						if (isInArray(field, sSelFields)) {
							results[field] = TOOLS.resolveSelectValue(fieldSearch[field], false);
							// if (getText) {
								results["{0}_text".NG_Format(field)] = TOOLS.resolveSelectValue(fieldSearch[field], getText);
							// }
						} else if (isInArray(field, mSelFields)) {
							results[field] = TOOLS.resolveMultiSelectValue(fieldSearch[field], getText);
						} else if (util.isArray(fieldSearch[field])) {
							var objValue = fieldSearch[field];
							var value = [];
							if (!isEmpty(objValue) && !isEmpty(objValue[0])) {
								for (var i = 0; i < objValue.length; i++) { value.push(objValue[i].value); }
							} else {
								value.push(null);
							}
							results[field] = value;
						} else if (util.isBoolean(fieldSearch[field])) {
							results[field] = fieldSearch[field];
						} else {
							results[field] = fieldSearch[field] || null;
						}
					}
					
					return results;
				}
			
				/**
				 * Wrapper for N/record submitFields() function
				 * @param {Object} options
				 * @param {string} options.type
				 * @param {string|number} options.id
				 * @param {Object} options.values
				 * @param {boolean} options.ignoreMandatory
				 * @param {boolean} options.enableSourcing
				 */
			,	submitFields : function(options) {
					if (isEmpty(options.type)) {
						throw error.create({ name : "INVALID_PARAMETER" , message : "Record type has not been set" });
					}
					if (isEmpty(options.id)) {
						throw error.create({ name : "INVALID_PARAMETER" , message : "Record ID has not been set" });
					}
					if (isEmpty(options.values)) {
						throw error.create({ name : "INVALID_PARAMETER" , message : "Update values have not been set" });
					}
					if (Object.keys(options.values).length < 1) {
						throw error.create({ name : "INVALID_PARAMETER" , message : "Update values have not been set" });
					}
					var ignoreMandatory = !isEmpty(options.ignoreMandatory) ? options.ignoreMandatory : false;
					var enableSourcing = !isEmpty(options.enableSourcing) ? options.enableSourcing : true;
					record.submitFields({ type : options.type , id : options.id , values : options.values , options : { ignoreMandatoryFields : ignoreMandatory , enableSourcing : enableSourcing } });
				}
			
				/**
				 * Retrieves state/province names/abbrev
				 * @param {string} cCode Country code
				 * @param {string} [sCode] State/Province code/abbrev; Required to retrieve name
				 * @param {string} [sName] State/Province name; Required to retrieve code/abbrev
				 * @returns {string}
				 */
			,	getStateProvince : function(cCode, sCode, sName) {
					if (isInArray(cCode, LIB.csList)) {
						var s;
						if (!isEmpty(sCode)) {
							for (s = 0; s < LIB.states[cCode].length; s++) {
								if (LIB.states[cCode][s]['value'] === sCode) {
									return LIB.states[cCode][s]['text'];
								}
							}
							return "";
						} else if (!isEmpty(sName)) {
							for (s = 0; s < LIB.states[cCode].length; s++) {
								if (LIB.states[cCode][s]['text'] === sCode) {
									return LIB.states[cCode][s]['value'];
								}
							}
							return "";
						} else {
							return "";
						}
					} else {
						return "";
					}
				}
			
			,	convertJavaArray : function(value) {
					if (isEmpty(value)) {
						return value;
					}
					if (!util.isArray(value)) {
						return value;
					}
					
					if (Object.prototype.toString.call(value) === "[object JavaArray]") {
						return [].concat(value);
					} else {
						return value;
					}
				}
			
				/**
				 * @param {Array} arguments Array of arguments passed in
				 * @returns {Array} Sorted object array based upon arguments passed in
				 *
				 * @summary The first argument must be the array to be sorted. If wanting to execute a case sensitive sort, the final argument must be a Boolean 'true'. After
				 * the array to be sorted, but before the case sensitive Boolean, all sort parameters are entered: string for field name, or array of field name string, Boolean
				 * for if descending order, and primer function(primer is optional)
				 */
			,	objectSort : function() {
					var args = arguments,
						array = args[0],
						case_sensitive, keys_length, key, desc, primer, a, b, i;
					
					if (typeof arguments[arguments.length - 1] === 'boolean') {
						case_sensitive = arguments[arguments.length - 1];
						keys_length = arguments.length - 1;
					} else {
						case_sensitive = false;
						keys_length = arguments.length;
					}
					
					return array.sort(function(obj1, obj2) {
						for (i = 1; i < keys_length; i++) {
							key = args[i];
							if (typeof key !== 'string') {
								desc = key[1];
								primer = key[2];
								key = key[0];
								if (primer === undefined) {
									a = obj1[args[i][0]];
									b = obj2[args[i][0]];
								} else {
									a = primer(obj1[args[i][0]]);
									b = primer(obj2[args[i][0]]);
								}
							} else {
								desc = false;
								a = obj1[args[i]];
								b = obj2[args[i]];
							}
							
							if (case_sensitive === false && typeof a === 'string') {
								a = a.toLowerCase();
								b = b.toLowerCase();
							}
							
							if (!desc) {
								if (a < b) return -1;
								if (a > b) return 1;
							} else {
								if (a > b) return -1;
								if (a < b) return 1;
							}
						}
						return 0;
					});
				}
			
			,	findKey : function(obj, func) {
					if (isEmpty(obj)) { return null; }
					if (isEmpty(func)) { return null; }
					for (var key in obj) {
						if (func(obj[key])) {
							return key;
						}
					}
					
					return null;
				}
			
			,	findAllKeys : function(obj, func) {
					var results = [];
					if (isEmpty(obj)) { return results; }
					if (isEmpty(func)) { return results; }
					for (var key in obj) {
						if (func(obj[key])) {
							results.push(key);
						}
					}
					
					return results;
				}
			
				/**
				 * Generates HTML to create a form button that imitates a native grey NetSuite button
				 * @param {string} buttonID Internal ID to assign to button
				 * @param {string} buttonText Text to display in button
				 * @param {string} buttonFunction Function to be called when button is activated
				 * @param {number|string} [marginTop=10] Size of CSS margin to be applied to top of button in pixels
				 * @returns {string}
				 */
			,	addNSStyleButton : function(buttonID, buttonText, buttonFunction, marginTop) {
					var scriptID = "";
					var deployID = "";
					marginTop = marginTop || "10";
					try {
						var libContext = runtime.getCurrentScript();
						scriptID = libContext.id;
						deployID = libContext.deploymentId.toUpperCase();
					} catch (err) { /* do nothing */ }
					var html = '';
					html += '<table id="tbl_{0}" cellpadding="0" cellspacing="0" border="0" class="uir-button" style="margin-right:6px; margin-top:{1}px; cursor:hand;" role="presentation">\n'.NG_Format(buttonID, marginTop);
					html += '	<tbody>\n';
					html += '		<tr id="tr_{0}" class="pgBntG">\n'.NG_Format(buttonID);
					html += '			<td id="tdleftcap_{0}">\n'.NG_Format(buttonID);
					html += '				<img pt-src="/images/nav/ns_x.gif" class="bntLT" border="0" height="50%" width="3" alt="">\n';
					html += '				<img pt-src="/images/nav/ns_x.gif" class="bntLB" border="0" height="50%" width="3" alt="">\n';
					html += '			</td>\n';
					html += '			<td id="tdbody_{0}" height="20" valign="top" nowrap="" class="bntBgB">\n'.NG_Format(buttonID);
					html += '				<input type="button" style="" class="rndbuttoninpt bntBgT" value="{0}" id="{1}" name="{1}" onclick="try { if (!!window) { var origScriptIdForLogging = window.NLScriptIdForLogging; var origDeploymentIdForLogging = window.NLDeploymentIdForLogging; window.NLScriptIdForLogging = \'{2}\'; window.NLDeploymentIdForLogging = \'{3}\'; } {4} } finally { if (!!window) { window.NLScriptIdForLogging = origScriptIdForLogging; window.NLDeploymentIdForLogging = origDeploymentIdForLogging; } }; return false;" onmousedown="this.setAttribute(\'_mousedown\',\'T\'); setButtonDown(true, false, this);" onmouseup="this.setAttribute(\'_mousedown\',\'F\'); setButtonDown(false, false, this);" onmouseout="if(this.getAttribute(\'_mousedown\')==\'T\') setButtonDown(false, false, this);" onmouseover="if(this.getAttribute(\'_mousedown\')==\'T\') setButtonDown(true, false, this);" _mousedown="F">\n'.NG_Format(buttonText, buttonID, (scriptID || 'window.NLScriptIdForLogging'), (deployID || 'window.NLDeploymentIdForLogging'), buttonFunction);
					html += '			</td>\n';
					html += '			<td id="tdrightcap_{0}">\n'.NG_Format(buttonID);
					html += '				<img pt-src="/images/nav/ns_x.gif" height="50%" class="bntRT" border="0" width="3" alt="">\n';
					html += '				<img pt-src="/images/nav/ns_x.gif" height="50%" class="bntRB" border="0" width="3" alt="">\n';
					html += '			</td>\n';
					html += '		</tr>\n';
					html += '	</tbody>\n';
					html += '</table>';
					return html;
				}
			
				/**
				 * Generates HTML to create a form button that imitates a native grey NetSuite sublist button
				 * @param {Object} options
				 * @param {string} options.id Internal ID to assign to button
				 * @param {string} options.label Text to display in button
				 * @param {string} options.func Function to be called when button is activated
				 * @returns {*}
				 */
			,	addNSStyleSublistButton : function(options) {
					var html = '';
					html += '<table id="tbl_{0}" cellpadding="0" cellspacing="0" border="0" class="uir-button" style="margin-right:6px;cursor:hand;" role="presentation">';
					html += '<tbody><tr id="tr_{0}" class="tabBnt">';
					html += '<td id="tdleftcap_{0}"><img pt-src="/images/nav/ns_x.gif" class="bntLT" border="0" height="50%" width="10" alt="">';
					html += '<img pt-src="/images/nav/ns_x.gif" class="bntLB" border="0" height="50%" width="10" alt=""></td>';
					html += '<td id="tdbody_{0}" height="20" valign="top" nowrap="" class="bntBgB">';
					html += '<input type="button" style="" class="rndbuttoninpt bntBgT" value="{1}" id="{0}" name="{0}" onclick="{2}" onmousedown="this.setAttribute(\'_mousedown\',\'T\'); setButtonDown(true, true, this);" onmouseup="this.setAttribute(\'_mousedown\',\'F\'); setButtonDown(false, true, this);" onmouseout="if(this.getAttribute(\'_mousedown\')==\'T\') setButtonDown(false, true, this);" onmouseover="if(this.getAttribute(\'_mousedown\')==\'T\') setButtonDown(true, true, this);" _mousedown="F"></td>';
					html += '<td id="tdrightcap_{0}">';
					html += '<img pt-src="/images/nav/ns_x.gif" height="50%" class="bntRT" border="0" width="10" alt="">';
					html += '<img pt-src="/images/nav/ns_x.gif" height="50%" class="bntRB" border="0" width="10" alt="">';
					html += '</td></tr></tbody></table>';
					
					return html.NG_Format(options.id, options.label, options.func);
				}
			
			,	resolveSelectValue : function(objValue, getText) {
					getText = getText || false;
					var selVal = null;
					if (!this.isEmpty(objValue) && !this.isEmpty(objValue[0])) { selVal = getText ? objValue[0].text || "" : objValue[0].value; }
					return selVal;
				}
			
			,	resolveMultiSelectValue : function(objValue, getText) {
					getText = getText || false;
					var arr = [];
					if (!this.isEmpty(objValue) && !this.isEmpty(objValue[0])) {
						for (var i = 0; i < objValue.length; i++) { arr.push(getText ? objValue[i].text || "" : objValue[i].value); }
					}
					return arr;
				}
			
				/**
				 * Calculates freight class based upon package dimensions and weight
				 * @param {number} wgt Package weight (in lbs)
				 * @param {number} l Package length (in inches)
				 * @param {number} w Package width (in inches)
				 * @param {number} h Package height (in inches)
				 * @param {boolean} isFeet Package dimensions are provided in feet instead of inches
				 * @returns {string}
				 */
			,	calculateFreightClass : function(wgt, l, w, h, isFeet) {
					var fClass = "";
					isFeet = isFeet || false;
					wgt = wgt || 1;
					l = l || (!isFeet ? 12 : 1);
					w = w || (!isFeet ? 12 : 1);
					h = h || (!isFeet ? 12 : 1);
					if (!isFeet) {
						l = MATH.roundToHundredths(l / 12);
						w = MATH.roundToHundredths(w / 12);
						h = MATH.roundToHundredths(h / 12);
					}
					
					var d = MATH.roundToHundredths(wgt / (l * w * h));
					if (d >= 50) {
						fClass = "50";
					} else if (d >= 35 && d < 50) {
						fClass = "55";
					} else if (d >= 30 && d < 35) {
						fClass = "60";
					} else if (d >= 22.5 && d < 30) {
						fClass = "65";
					} else if (d >= 15 && d < 22.5) {
						fClass = "70";
					} else if (d >= 13.5 && d < 15) {
						fClass = "77.5";
					} else if (d >=12 && d < 13.5) {
						fClass = "85";
					} else if (d >= 10.5 && d < 12) {
						fClass = "92.5";
					} else if (d >= 9 && d < 10.5) {
						fClass = "100";
					} else if (d >= 8 && d < 9) {
						fClass = "110";
					} else if (d >= 7 && d < 8) {
						fClass = "125";
					} else if (d >= 6 && d < 7) {
						fClass = "150";
					} else if (d >= 5 && d < 6) {
						fClass = "175";
					} else if (d >= 4 && d < 5) {
						fClass = "200";
					} else if (d >= 3 && d < 4) {
						fClass = "250";
					} else if (d >= 2 && d < 3) {
						fClass = "300";
					} else if (d >= 1 && d < 2) {
						fClass = "400";
					} else {
						fClass = "500";
					}
					
					return fClass;
				}
			
				/**
				 * Calculates freight class based upon package dimensions and weight
				 * @param {Object} options
				 * @param {number} options.wgt Package weight (in lbs)
				 * @param {number} options.l Package length (in inches)
				 * @param {number} options.w Package width (in inches)
				 * @param {number} options.h Package height (in inches)
				 * @param {boolean} options.isFeet Package dimensions are provided in feet instead of inches
				 * @returns {string}
				 */
			,	calculateFreightClassAlt : function(options) {
					// var fClass = "";
					var isFeet = options.isFeet || false;
					var wgt = options.wgt || 1;
					var l = options.l || (!isFeet ? 12 : 1);
					var w = options.w || (!isFeet ? 12 : 1);
					var h = options.h || (!isFeet ? 12 : 1);
					if (!isFeet) {
						l = MATH.roundToHundredths(l / 12);
						w = MATH.roundToHundredths(w / 12);
						h = MATH.roundToHundredths(h / 12);
					}
					
					return TOOLS.calculateFreightClass(wgt, l, w, h, isFeet);
				}
			
			,	objectFilterKeyArr : function(obj, atr, value) {
					return Object.keys(obj).filter(function(key) {
						return obj[key][atr] === value;
					});
				}
			
			,	objectFilterKeyArrAdv : function(obj, func) {
					return Object.keys(obj).filter(func);
				}
			
			,	objectFilterArr : function(obj, atr, value) {
					return Object.keys(obj).filter(function(key) {
						return obj[key][atr] === value;
					}).map(function(key) {
						var fObj = obj[key];
						fObj.filterKey = key;
						return fObj;
					});
				}
			
			,	objectFilterArrAdv : function(obj, func) {
					return Object.keys(obj)
						.filter(func)
						.map(function(key) {
							var fObj = obj[key];
							fObj.filterKey = key;
							return fObj;
						});
				}
			
			,	objectFilter : function(obj, atr, value) {
					var keys = Object.keys(obj).filter(function(key) {
						return obj[key][atr] === value;
					});
					var filtObj = { };
					keys.forEach(function(key) {
						filtObj[key] = obj[key];
					});
					return filtObj;
				}
			
			,	objectFilterAdv : function(obj, func) {
					var keys = Object.keys(obj).filter(func);
					var filtObj = { };
					keys.forEach(function(key) {
						filtObj[key] = obj[key];
					});
					return filtObj;
				}
			
				/**
				 * Wrapper for the N/search module's createColumn()
				 * @param {Object} options Object with search column creation attributes
				 * @returns {{}}
				 */
			,	searchColumn : function(options) {
					return search.createColumn(options);
				}
			
				/**
				 * Commits plaintext data to the File Cabinet
				 * @param {string} contents File contents
				 * @param {string} fileName File name
				 * @param {number} folderId Folder ID
				 * @param {string} fileType File type
				 * @returns {void}
				 */
			,	saveToTextFile : function(contents, fileName, folderId, fileType) {
					require(['N/file'], function(file) {
						fileType = fileType || file.Type.PLAINTEXT;
						var ext = "";
						switch(fileType) {
							case file.Type.PLAINTEXT :
								ext = "txt";
								break;
							case file.Type.XMLDOC :
								ext = "xml";
								break;
							case file.Type.CSV :
								ext = "csv";
								break;
							case file.Type.CONFIG :
								ext = "config";
								break;
							case file.Type.HTMLDOC :
								ext = "html";
								break;
							case file.Type.JAVASCRIPT :
								ext = "js";
								break;
							case file.Type.JSON :
								ext = "json";
								break;
							case file.Type.POSTSCRIPT :
								ext = "ps";
								break;
							case file.Type.STYLESHEET :
								ext = "css";
								break;
							default:
								LOGGING.logError(null, "Invalid file type defined");
								return;
						}
						try {
							return file.create({
									name : "{0}.{1}".NG_Format(fileName, ext)
								,	fileType : fileType
								,	contents : contents
								,	folder : folderId
								,	encoding : file.Encoding.UTF_8
							}).save();
						} catch (err) {
							LOGGING.logError(err, "Error encountered saving text data to file");
						}
					});
				}
			
				/**
				 * Formats plain xml string into a more human-readable layout
				 * @param {string} xml
				 */
			,	formatXml : function(xml) {
					var reg = /(>)\s*(<)(\/*)/g; // updated Mar 30, 2015
					var wsexp = / *(.*) +\n/g;
					var contexp = /(<.+>)(.+\n)/g;
					xml = xml.replace(reg, '$1\n$2$3').replace(wsexp, '$1\n').replace(contexp, '$1\n$2');
					// var pad = 0;
					var formatted = '';
					var lines = xml.split('\n');
					var indent = 0;
					var lastType = 'other';
					// 4 types of tags - single, closing, opening, other (text, doctype, comment) - 4*4 = 16 transitions
					var transitions = {
						'single->single': 0,
						'single->closing': -1,
						'single->opening': 0,
						'single->other': 0,
						'closing->single': 0,
						'closing->closing': -1,
						'closing->opening': 0,
						'closing->other': 0,
						'opening->single': 1,
						'opening->closing': 0,
						'opening->opening': 1,
						'opening->other': 1,
						'other->single': 0,
						'other->closing': -1,
						'other->opening': 0,
						'other->other': 0
					};
					
					for (var i = 0; i < lines.length; i++) {
						var ln = lines[i];
						
						// Luca Viggiani 2017-07-03: handle optional <?xml ... ?> declaration
						if (ln.match(/\s*<\?xml/)) {
							formatted += ln + "\n";
							continue;
						}
						// ---
						
						var single = Boolean(ln.match(/<.+\/>/)); // is this line a single tag? ex. <br />
						var closing = Boolean(ln.match(/<\/.+>/)); // is this a closing tag? ex. </a>
						var opening = Boolean(ln.match(/<[^!].*>/)); // is this even a tag (that's not <!something>)
						var type = single ? 'single' : closing ? 'closing' : opening ? 'opening' : 'other';
						var fromTo = lastType + '->' + type;
						lastType = type;
						var padding = '';
						
						indent += transitions[fromTo];
						for (var j = 0; j < indent; j++) {
							padding += '\t';
						}
						if (fromTo === 'opening->closing')
							formatted = formatted.substr(0, formatted.length - 1) + ln + '\n'; // substr removes line break (\n) from prev loop
						else
							formatted += padding + ln + '\n';
					}
					
					return formatted;
				}
			
				/**
				 * Retrieves the path in the File Cabinet where the specified file is located
				 * @param {Object} options
				 * @param {string} [options.fileName] Name of file to look for; Required if fileId not defined
				 * @param {string} [options.fileId] Internal ID of file to look for; Required if fileName not defined
				 * @param {boolean} [options.expSep=false] Add spaces around the file path separators
				 * @returns {string}
				 */
			,	getFileCabinetPath : function(options) {
					var fileName = options.fileName || "";
					var fileId = options.fileId || "";
					var expandSeparators = options.expSep || false;
					var filePath = "";
					
					var fileFilt = [];
					if (!isEmpty(fileName) || isEmpty(fileId)) {
						fileFilt.push( ["name","is",fileName] );
					} else if (isEmpty(fileName) || !isEmpty(fileId)) {
						fileFilt.push( ["internalid","anyof",[fileId]] );
					} else {
						return filePath;
					}
					var fileCols = [
							search.createColumn({ name : "folder" })
					];
					var fileResults = TOOLS.getSearchResults("file", fileFilt, fileCols, null, 1, false, true);
					
					if (!isEmpty(fileResults)) {
						var folderId = fileResults[0].getValue({ name : "folder" });
						if (!isEmpty(folderId)) {
							var folderList = [];
							var fFilt = [
									["internalid","is",folderId]
							];
							var fCols = [
									search.createColumn({ name : "name" })
								,	search.createColumn({ name : "parent" })
							];
							var fSearch = TOOLS.getSearchResults("folder", fFilt, fCols);
							if (!isEmpty(fSearch) && (fSearch || []).length > 0) {
								var name = fSearch[0].getValue({ name : "name" });
								var parent = fSearch[0].getValue({ name : "parent" });
								folderList.unshift(name);
								while (!isEmpty(parent)) {
									fFilt = [
											["internalid","is",parent]
									];
									var fSearchP = TOOLS.getSearchResults("folder", fFilt, fCols);
									if (!isEmpty(fSearchP)) {
										var pName = fSearchP[0].getValue({ name : "name" }) || "";
										parent = fSearchP[0].getValue({ name : "parent" }) || "";
										folderList.unshift(pName);
									}
								}
								filePath = folderList.join(expandSeparators ? " / " : "/");
							}
						}
					}
					
					return filePath;
				}
			
				/**
				 * @params options
				 * @params options.scriptPath
				 * @params options.logScriptId
				 * @params options.logDeploymentId
				 */
			,	altButtonTriggerScript : function(options) {
					/*var rConfig = JSON.parse('{}');
					rConfig['context'] = '/SuiteBundles/Bundle 397059/ng_gilmer_clientLabels';
					var entryPointRequire = require.config(rConfig);
					entryPointRequire(['/SuiteBundles/Bundle 397059/ng_gilmer_clientLabels'], function(mod) {
						try {
							if (!!window) {
								var origScriptIdForLogging = window.NLScriptIdForLogging;
								var origDeploymentIdForLogging = window.NLDeploymentIdForLogging;
								window.NLScriptIdForLogging = 'customscript_ng_gilmer_labels_ue';
								window.NLDeploymentIdForLogging = 'customdeploy_ng_gilmer_labels_ue_if';
							}
							mod.printLabels();
						} finally {
							if (!!window) {
								window.NLScriptIdForLogging = origScriptIdForLogging;
								window.NLDeploymentIdForLogging = origDeploymentIdForLogging;
							}
						}
					});*/
				}
			
				/**
				 * Decode HTML form data into JSON object
				 * @param {string} formData
				 * @returns {{}}
				 */
			,	decodeFormData : function(formData) {
					var formDataMapInit = formData.split("&");
					var formDataMap = { };
					formDataMapInit.forEach(function(formInput) {
						var valuePair = formInput.split("=");
						formDataMap[decodeURIComponent(valuePair[0])] = decodeURIComponent(valuePair[1].replace(/\+/g, " "));
					});
					
					return formDataMap;
				}
			
				/**
				 * @params {Object} options
				 * @params {Object[]} options.data
				 * @params {string|string[]} [options.headers]
				 * @returns {string}
				 */
			,	convertObjectArrToCSVOutput : function(options) {
					var output = [];
					if (isEmpty(options.data)) {
						throw error.create({ name : "INVALID_PARAMETER" , message : "Source CSV data has not been provided" });
					}
					if (!util.isArray(options.data)) {
						throw error.create({ name : "INVALID_PARAMETER" , message : "Source CSV data is not an array" });
					}
					if (options.data.length < 1) {
						throw error.create({ name : "INVALID_DATA" , message : "Source CSV data is empty" });
					}
					
					var attrList = Object.keys(options.data[0]);
					if (isEmpty(options.headers)) {
						output.push(attrList.join(","));
					} else {
						if (util.isArray(options.headers)) {
							output.push(options.headers.join(","));
						} else {
							output.push(options.headers);
						}
					}
					
					options.data.forEach(function(data) {
						output.push(attrList.map(function(attr) {
							return data[attr];
						}));
					});
					
					return output.join("\r\n");
				}
		};
		
		var OBJ = {
			isArray : function() {
					if (arguments.length === 0 || isEmpty(arguments[0])) {
						return false;
					}
					return util.isArray(arguments[0]);
				}
			
			,	isBoolean : function() {
					if (arguments.length === 0 || isEmpty(arguments[0])) {
						return false;
					}
					return util.isBoolean(arguments[0]);
				}
			
			,	isNumber : function() {
					if (arguments.length === 0 || isEmpty(arguments[0])) {
						return false;
					}
					return util.isNumber(arguments[0]);
				}
			
			,	isString : function() {
					if (arguments.length === 0 || isEmpty(arguments[0])) {
						return false;
					}
					return util.isString(arguments[0]);
				}
		};
		
		var TIME = {
				/**
				 * @param {Boolean} inDateTimeFormat If a date/time formatted string is wanted, pass true (default: false)
				 * @param {String} zone Continental US time zone for desired time stamp; Options: EST,EDT,CST,CDT,MST,MDT,PST,PDT (default: current Netsuite server time zone)
				 * @param {Boolean} inDateTimeSecFormat If a date/time formatted string with seconds is wanted, pass true (default: false)
				 *
				 * @returns {String}
				 * NOTE: Does not factor in Daylight Savings Time
				 */
				timeStamp : function(inDateTimeFormat, zone, inDateTimeSecFormat) {
					try {
						var now = new Date();
						
						// attempt to convert time to designated time zone (default: current Netsuite server time zone)
						var nowString = now.toString();
						var est = nowString.search(/EDT|EST|Eastern Standard Time|Eastern Daylight Time/i);
						var cst = nowString.search(/CDT|CST|Central Standard Time|Central Daylight Time/i);
						var mst = nowString.search(/MDT|MST|Mountain Standard Time|Mountain Daylight Time/i);
						var pst = nowString.search(/PDT|PST|Pacific Standard Time|Pacific Daylight Time/i);
						var diff = Number(0);
						
						if (!isEmpty(zone)) {
							switch (zone) {
								case "EST":
								case "EDT":
									if (est !== -1)
										diff = Number(0);
									else if (cst !== -1)
										diff = Number(1);
									else if (mst !== -1)
										diff = Number(2);
									else if (pst !== -1)
										diff = Number(3);
									break;
								case "CST":
								case "CDT":
									if (est !== -1)
										diff = Number(-1);
									else if (cst !== -1)
										diff = Number(0);
									else if (mst !== -1)
										diff = Number(1);
									else if (pst !== -1)
										diff = Number(2);
									break;
								case "MST":
								case "MDT":
									if (est !== -1)
										diff = Number(-2);
									else if (cst !== -1)
										diff = Number(-1);
									else if (mst !== -1)
										diff = Number(0);
									else if (pst !== -1)
										diff = Number(1);
									break;
								case "PST":
								case "PDT":
									if (est !== -1)
										diff = Number(-3);
									else if (cst !== -1)
										diff = Number(-2);
									else if (mst !== -1)
										diff = Number(-1);
									else if (pst !== -1)
										diff = Number(0);
									break;
							}
						}
						
						var msIncrement = Number(diff * 60 * 60 * 1000);
						var msNow = Date.parse(now);
						now = new Date(msNow + msIncrement);
						
						var disp = "";
						if (inDateTimeFormat) {
							disp = format.format({ value : now , type : format.Type.DATETIME });
						} else if (inDateTimeSecFormat) {
							disp = format.format({ value : now , type : format.Type.DATETIMETZ });
						} else {
							var m = (now.getMonth() + 1).toFixed(0).NG_paddingLeft("00");
							var d = now.getDate().toFixed(0).NG_paddingLeft("00");
							var y = now.getFullYear().toFixed(0);
							var h = now.getHours().toFixed(0).NG_paddingLeft("00");
							var mm = now.getMinutes().toFixed(0).NG_paddingLeft("00");
							var s = now.getSeconds().toFixed(0).NG_paddingLeft("00");
							
							disp = "{0}{1}{2}{3}{4}{5}".NG_Format(y,m,d,h,mm,s);
						}
						return disp;
					} catch (err) {
						return "";
					}
				}
			
			,	adjustTime : function(now, zone) {
					// attempt to convert time to designated time zone (default: current Netsuite server time zone)
					var nowString = now.toString();
					var est = nowString.search(/EDT|EST|Eastern Standard Time|Eastern Daylight Time/i);
					var cst = nowString.search(/CDT|CST|Central Standard Time|Central Daylight Time/i);
					var mst = nowString.search(/MDT|MST|Mountain Standard Time|Mountain Daylight Time/i);
					var pst = nowString.search(/PDT|PST|Pacific Standard Time|Pacific Daylight Time/i);
					var diff = Number(0);
					
					if (!isEmpty(zone)) {
						switch (zone) {
							case "EST":
							case "EDT":
								if (est !== -1)
									diff = Number(0);
								else if (cst !== -1)
									diff = Number(1);
								else if (mst !== -1)
									diff = Number(2);
								else if (pst !== -1)
									diff = Number(3);
								break;
							case "CST":
							case "CDT":
								if (est !== -1)
									diff = Number(-1);
								else if (cst !== -1)
									diff = Number(0);
								else if (mst !== -1)
									diff = Number(1);
								else if (pst !== -1)
									diff = Number(2);
								break;
							case "MST":
							case "MDT":
								if (est !== -1)
									diff = Number(-2);
								else if (cst !== -1)
									diff = Number(-1);
								else if (mst !== -1)
									diff = Number(0);
								else if (pst !== -1)
									diff = Number(1);
								break;
							case "PST":
							case "PDT":
								if (est !== -1)
									diff = Number(-3);
								else if (cst !== -1)
									diff = Number(-2);
								else if (mst !== -1)
									diff = Number(-1);
								else if (pst !== -1)
									diff = Number(0);
								break;
							default:
								return now;
						}
					}
					
					return this.addHours(now, diff);
				}
			
			,	addMinutes : function(date, minutes) {
					return new Date(date.getTime() + (minutes * 60 * 1000));
				}
			
			,	addHours : function(date, hours) {
					return new Date(date.getTime() + (hours * 60 * 60 * 1000));
				}
			
			,	addDays : function(date, days) {
					return new Date(date.getTime() + (days * 24 * 60 * 60 * 1000));
				}
			
			,	addMonths : function(date, months) {
					var d = new Date(date.getTime());
					var n = d.getDate();
					d.setDate(1);
					d.setMonth(date.getMonth() + months);
					d.setDate(Math.min(n, this.getDaysInMonth(d.getFullYear(), d.getMonth())));
					return d;
				}
			
			,	isLeapYear : function(year) {
					return (((year % 4 === 0) && (year % 100 !== 0)) || (year % 400 === 0));
				}
			
			,	getDaysInMonth : function(year, month) {
					return [31, (this.isLeapYear(year) ? 29 : 28), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month];
				}
			
			,	logTimeElapsed : function(time1, time2, msg) {
					msg = !isEmpty(msg) ? msg : "Time Elapsed";
					var time1MS = !isEmpty(time1) ? Date.parse(time1) : 0;
					var time2MS = !isEmpty(time2) ? Date.parse(time2) : 0;
					var elapsed = (Number(time2MS) - Number(time1MS)) / 1000;
					log.audit({ title : msg , details : "{0} seconds".NG_Format(elapsed) });
				}
			
			,	getTimeElapsed : function(time1, time2) {
					var time1MS = !isEmpty(time1) ? Date.parse(time1) : 0;
					var time2MS = !isEmpty(time2) ? Date.parse(time2) : 0;
					return (Number(time2MS) - Number(time1MS)) / 1000;
				}
			
				// written by michaelkhalili 9/27/2009
				// returns time zone offset with respect to UTC and ignores DST
				// offset is neg for TZ west of UTC, pos for TZ east of UTC
			,	TimezoneDetect : function() {
					var dtDate = new Date('1/1/' + (new Date()).getUTCFullYear());
					var intOffset = 10000; //set initial offset high so it is adjusted on the first attempt
					var intMonth;
					
					//go through each month to find the lowest offset to account for DST
					for (intMonth = 0; intMonth < 12; intMonth++) {
						//go to the next month
						dtDate.setUTCMonth(dtDate.getUTCMonth() + 1);
						
						//To ignore daylight saving time look for the lowest offset.
						//Since, during DST, the clock moves forward, it'll be a bigger number.
						if (intOffset > (dtDate.getTimezoneOffset() * (-1))) {
							intOffset = (dtDate.getTimezoneOffset() * (-1));
						}
					}
					
					return intOffset;
				}
			
				//written by michaelkhalili 9/27/2009
				//Find start and end of DST
			,	DstDetect : function() {
					var dtDstDetect = new Date();
					var dtDstStart = '';
					var dtDstEnd = '';
					var dtDstStartHold = ''; //Temp date hold
					var intYearDayCount = 732; //366 (include leap year) * 2 (for two years)
					var intHourOfYear = 1;
					var intDayOfYear;
					var intOffset = TIME.TimezoneDetect(); //Custom function. Make sure you include it.
					
					//Start from a year ago to make sure we include any previously starting DST
					dtDstDetect = new Date();
					dtDstDetect.setUTCFullYear(dtDstDetect.getUTCFullYear() - 1);
					dtDstDetect.setUTCHours(0,0,0,0);
					
					//Going hour by hour through the year will detect DST with shorter code but that could result in 8760
					//FOR loops and several seconds of script execution time. Longer code narrows this down a little.
					//Go one day at a time and find out approx time of DST and if there even is DST on this computer.
					//Also need to make sure we catch the most current start and end cycle.
					for (intDayOfYear = 1; intDayOfYear <= intYearDayCount; intDayOfYear++) {
						dtDstDetect.setUTCDate(dtDstDetect.getUTCDate() + 1);
						
						if ((dtDstDetect.getTimezoneOffset() * (-1)) !== intOffset && isEmpty(dtDstStartHold)) {
							dtDstStartHold = new Date(dtDstDetect);
						}
						if ((dtDstDetect.getTimezoneOffset() * (-1)) === intOffset && !isEmpty(dtDstStartHold)) {
							dtDstStart = new Date(dtDstStartHold);
							dtDstEnd = new Date(dtDstDetect);
							dtDstStartHold = '';
							
							//DST is being used in this timezone. Narrow the time down to the exact hour the change happens
							//Remove 48 hours (a few extra to be on safe side) from the start/end date and find the exact change point
							//Go hour by hour until a change in the timezone offset is detected.
							dtDstStart.setUTCHours(dtDstStart.getUTCHours() - 48);
							dtDstEnd.setUTCHours(dtDstEnd.getUTCHours() - 48);
							
							//First find when DST starts
							for (intHourOfYear=1; intHourOfYear <= 48; intHourOfYear++) {
								dtDstStart.setUTCHours(dtDstStart.getUTCHours() + 1);
								
								//If we found it then exit the loop. dtDstStart will have the correct value left in it.
								if ((dtDstStart.getTimezoneOffset() * (-1)) !== intOffset) {
									break;
								}
							}
							
							//Now find out when DST ends
							for (intHourOfYear=1; intHourOfYear <= 48; intHourOfYear++) {
								dtDstEnd.setUTCHours(dtDstEnd.getUTCHours() + 1);
								
								//If we found it then exit the loop. dtDstEnd will have the correct value left in it.
								if ((dtDstEnd.getTimezoneOffset() * (-1)) !== (intOffset + 60)) {
									break;
								}
							}
							
							//Check if DST is currently on for this time frame. If it is then return these values.
							//If not then keep going. The function will either return the last values collected
							//or another value that is currently in effect
							if ((new Date()).getTime() >= dtDstStart.getTime() && (new Date()).getTime() <= dtDstEnd.getTime()){
								return [dtDstStart,dtDstEnd];
							}
						}
					}
					return [dtDstStart,dtDstEnd];
				}
			
				// wait timer
				// max wait: 15 seconds
			,	doWait : function(seconds, ignoreMax) {
					var ms = seconds * 1000;
					var start = new Date();
					var startMS = start.getTime();
					var diff = 0;
					var endMS = 0;
					do {
						var test = new Date();
						endMS = test.getTime();
						diff = endMS - startMS;
						if (diff >= 15000 && !ignoreMax) {
							diff = ms;
						}
					} while (diff < ms);
					
					return { s : startMS , e : endMS };
				}
			
			,	countDays : function(date1, date2) {
					return Math.round((date2-date1)/(1000*60*60*24));
				}
			
			,	dateToNSString : function(date, fmat) {
					fmat = fmat || "date";
					var dateString = "", h, ap;
					if (fmat === "date") {
						dateString = "{0}/{1}/{2}".NG_Format((date.getMonth() + 1).toFixed(0),date.getDate().toFixed(0),date.getFullYear().toFixed(0));
					} else if (fmat === "datetime") {
						h = date.getHours() > 12 ? (date.getHours() - 12).toFixed(0) : date.getHours().toFixed(0);
						ap = date.getHours() >= 12 ? "pm" : "am";
						dateString = "{0}/{1}/{2} {3}:{4} {5}".NG_Format((date.getMonth() + 1).toFixed(0),date.getDate().toFixed(0),date.getFullYear().toFixed(0),h,(date.getMinutes().toFixed(0)).NG_paddingLeft("00"),ap);
					} else if (fmat === "datetimetz") {
						h = date.getHours() > 12 ? (date.getHours() - 12).toFixed(0) : date.getHours().toFixed(0);
						ap = date.getHours() >= 12 ? "pm" : "am";
						dateString = "{0}/{1}/{2} {3}:{4}:{5} {6}".NG_Format((date.getMonth() + 1).toFixed(0),date.getDate().toFixed(0),date.getFullYear().toFixed(0),h,(date.getMinutes().toFixed(0)).NG_paddingLeft("00"),(date.getSeconds().toFixed(0)).NG_paddingLeft("00"),ap);
					} else if (fmat === "24H") {
						dateString = "{0}/{1}/{2} {3}:{4}".NG_Format((date.getMonth() + 1).toFixed(0),date.getDate().toFixed(0),date.getFullYear().toFixed(0),(date.getHours().toFixed(0)).NG_paddingLeft("00"),(date.getMinutes().toFixed(0)).NG_paddingLeft("00"));
					} else if (fmat === "24Htz") {
						dateString = "{0}/{1}/{2} {3}:{4}:{5}".NG_Format((date.getMonth() + 1).toFixed(0),date.getDate().toFixed(0),date.getFullYear().toFixed(0),date.getHours().toFixed(0).NG_paddingLeft("00"),(date.getMinutes().toFixed(0)).NG_paddingLeft("00"),(date.getSeconds().toFixed(0)).NG_paddingLeft("00"));
					}
					
					return dateString;
				}
			
			/**
			 * @param {Date} date Date object tp be formatted
			 * @param {String} fmat Template string for desired date/time output
			 * YYYY		four digit year
			 * YY		two digit year
			 * MONTH	full month name (for compatibility with NetSuite general preferences value DATEFORMAT)
			 * Month	full month name (for compatibility with NetSuite general preferences value DATEFORMAT)
			 * MMMM		full month name
			 * Mon		abbrev month name (for compatibility with NetSuite general preferences value DATEFORMAT)
			 * MMM		abbrev month name
			 * MM		month number with leading zero
			 * M		month number without leading zero
			 * DDDD		full day of week name
			 * DDD		abbrev day of week name
			 * DD		day of month number with leading zero
			 * D		day of month number without leading zero
			 * HH		Hour with leading zero (24h)
			 * H		Hour without leading zero (24h)
			 * hh		Hour with leading zero (12h)
			 * h		Hour without leading zero (12h)
			 * T		AM/PM
			 * t		am/pm
			 * A		AM/PM
			 * a		am/pm (for compatibility with NetSuite general preferences value TIMEFORMAT)
			 * mm		Minutes with leading zero
			 * m		Minutes without leading zero
			 * ss		Seconds with leading zero
			 * s		Seconds without leading zero
			 * zz		Milliseconds with leading zeroes
			 * z		Milliseconds without leading zeroes
			 * @param {boolean} [toUTC] (Optional) Process date as UTC time values
			 * @returns {String}
			 */
			,	formatDateTime: function (date, fmat, toUTC) {
					toUTC = toUTC || false;
					var days = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];
					var daysAlt = ["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
					var daysAbbrev = ["Sun","Mon","Tue","Wed","Thu","Fri","Sat"];
					var daysAltAbbrev = ["Mon","Tue","Wed","Thu","Fri","Sat","Sun"];
					var months = ["January","February","March","April","May","June","July","August","September","October","November","December"];
					var monthsAbbrev = ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];
					var y4 = toUTC ? date.getUTCFullYear().toFixed(0) : date.getFullYear().toFixed(0);
					var y2 = y4.substr(2);
					var m = toUTC ? date.getUTCMonth() : date.getMonth();
					var mo = Math.round(m + 1).toFixed(0);
					var mmo = mo.NG_paddingLeft("00");
					var d = toUTC ? date.getUTCDate().toFixed(0) : date.getDate().toFixed(0);
					var dd = d.NG_paddingLeft("00");
					var dow = toUTC ? date.getUTCDay() : date.getDay();
					var dw = daysAbbrev[dow];
					var ddw = days[dow];
					var hInit = toUTC ? date.getUTCHours() : date.getHours();
					var h = hInit > 12 ? Math.round(hInit - 12).toFixed(0) : (hInit == 0 ? "12" : hInit.toFixed(0));
					var hh = Number(h).toFixed(0).NG_paddingLeft("00");
					var ap = hInit >= 12 ? "pm" : "am";
					var aapp = ap.toUpperCase();
					var h24 = hInit.toFixed(0);
					var hh24 = h24.NG_paddingLeft("00");
					var mi = toUTC ? date.getUTCMinutes().toFixed(0) : date.getMinutes().toFixed(0);
					var mmi = mi.NG_paddingLeft("00");
					var s = toUTC ? date.getUTCSeconds().toFixed(0) : date.getSeconds().toFixed(0);
					var ss = s.NG_paddingLeft("00");
					var z = toUTC ? date.getUTCMilliseconds().toFixed(0) : date.getMilliseconds().toFixed(0);
					var zz = z.NG_paddingLeft("000");
					
					// return fmat
					// 	.replace("YYYY",y4).replace("YY",y2)
					// 	.replace("HH",hh24).replace(/^(?!NT)H/,h24).replace("hh",hh).replace(/^(?!nt)h/,h).replace("mm",mmi).replace("m",mi).replace("ss",ss).replace("s",s)
					// 	.replace("zz",zz).replace("z",z).replace(/^(?!N)T(?!.*H)/,aapp).replace(/^(?!n)t(?!.*h)/,ap).replace("A",aapp).replace("a",ap)
					// 	.replace("MONTH",months[m]).replace("MMMM",months[m]).replace("Mon",monthsAbbrev[m]).replace("MMM",monthsAbbrev[m]).replace("MM",mmo).replace("M",mo)
					// 	.replace("DDDD",ddw).replace("DDD",dw).replace("DD",dd).replace(/D(?!.*e)/,d);
					
					fmat = fmat.replace(/MONTH|Month/, "QQQQQ");
					return fmat
						.replace("YYYY",y4).replace("YY",y2)
						.replace("HH",hh24).replace("H",h24).replace("hh",hh).replace("h",h).replace("mm",mmi).replace("m",mi).replace("ss",ss).replace("s",s)
						.replace("zz",zz).replace("z",z).replace("T",aapp).replace("t",ap).replace("A",aapp).replace("a",ap)
						.replace("MMMM",months[m]).replace("MM",mmo).replace("M",mo)
						.replace("DDDD",ddw).replace("DDD",dw).replace("DD",dd).replace("D",d)
						.replace("QQQQQ",months[m]).replace("Mon",monthsAbbrev[m]).replace("MMM",monthsAbbrev[m]);
			}
			
			,	getSimplifiedDate : function(date) {
					if (util.isDate(date)) {
						return format.parse({ value : format.format({ value : date , type : format.Type.DATE }) , type : format.Type.DATE });
					} else if (util.isString(date)) {
						return format.format({ value : format.parse({ value : date , type : format.Type.DATE }) , type : format.Type.DATE });
					}
				}
			
			,	dateToString : function(date, fmat, tz) {
					if (!isEmpty(tz)) {
						return format.format({ value : date , type : fmat || format.Type.DATE , timezone : tz });
					} else {
						return format.format({ value : date , type : fmat || format.Type.DATE });
					}
				}
			
			,	stringToDate : function(date, fmat, tz) {
					if (!isEmpty(tz)) {
						return format.parse({ value : date , type : fmat || format.Type.DATE , timezone : tz });
					} else {
						return format.parse({ value : date , type : fmat || format.Type.DATE });
					}
				}
			
			,	quickDate : function(date, tz) {
					return format.parse({ value : format.format({ value : date , type : format.Type.DATETIMETZ , timezone : tz }) , type : format.Type.DATETIME });
				}
			
			,	getCompanyTime : function(d) {
					var cdt = null;
					require(['N/config'], function(config) {
						var currentDateTime = d || new Date();
						var companyTimeZone = config.load({ type : config.Type.COMPANY_INFORMATION }).getText({ fieldId : "timezone" });
						var timeZoneOffSet = (companyTimeZone.indexOf('(GMT)') === 0) ? 0 : Number(companyTimeZone.substr(4, 6).replace(/\+|:00/gi, '').replace(/:30/gi, '.5'));
						var UTC = currentDateTime.getTime() + (currentDateTime.getTimezoneOffset() * 60000);
						var companyDateTime = UTC + (timeZoneOffSet * 60 * 60 * 1000);
						
						cdt = new Date(companyDateTime);
					});
					return cdt;
				}
			
			,	calculateDuration : function(start, end) {
					if (isEmpty(start) || isEmpty(end)) {
						return null;
					}
					if (util.isDate(start) || util.isDate(end)) {
						return null;
					}
					
					var s = start.getTime(), e = end.getTime();
					
					var initDur =		MATH.roundToHundredths(e - s);
					var initDays =		MATH.roundToTenThousandths(initDur / (1000 * 60 * 60 * 60 * 24));
					var initHours =		MATH.roundToTenThousandths(initDur / (1000 * 60 * 60 * 60));
					var initMinutes =	MATH.roundToTenThousandths(initDur / (1000 * 60 * 60));
					var initSeconds =	MATH.roundToTenThousandths(initDur / (1000 * 60));
					
					var days =			Math.floor(initDays);
					var remHours =		MATH.roundToTenThousandths((initDays - days) * 24);
					var hours =			Math.floor(remHours);
					var remMinutes =	MATH.roundToTenThousandths((remHours - hours) * 60);
					var minutes =		Math.floor(remMinutes);
					var remSeconds =	MATH.roundToTenThousandths((remMinutes - minutes) * 60);
					var seconds =		Math.floor(remSeconds);
					
					return {
							durationInMS : initDur
						,	durationInDays : initDays
						,	durationInHours : initHours
						,	durationInMinutes : initMinutes
						,	durationInSeconds : initSeconds
						,	days : days
						,	hours : hours
						,	minutes : minutes
						,	seconds : seconds
					};
				}
			
				/**
				 * @param {Object} options Options for string-to-date reconstruction
				 * @param {string} options.dateString String of the date to be converted to a Date object
				 * @param {string|RegExp} [options.separator= ] String or RegEx to define where to split the string between the date and time components; Use /[A-z]/ on date strings ending in Z (Default: blank space)
				 * @param {string|RegExp} [options.dateSeparator=/] String or RegEx to define where to split the date components (Default: / )
				 * @param {string|RegExp} [options.timeSeparator=:] String or RegEx to define where to split the time components (Default: : )
				 * @returns {Date}
				 */
			,	convertStringToDate : function(options) {
					if (isEmpty(options.dateString)) {
						return null;
					}
					var partSep = options.separator || " ";
					var dateSep = options.dateSeparator || "/";
					var timeSep = options.timeSeparator || ":";
					var dateTimePieces = options.dateString.split(partSep);
					var datePieces = dateTimePieces[0].split(dateSep);
					var timePieces = dateTimePieces[1].split(timeSep);
					var y = Number(datePieces[2]);
					var m = Math.round(Number(datePieces[0]) - 1);
					var d = Number(datePieces[1]);
					var h = dateTimePieces[2].toUpperCase() === "PM" ? (timePieces[0] === "12" ? Number(timePieces[0]) : Math.round(Number(timePieces[0]) + 12)) : (timePieces[0] === "12" ? Math.round(Number(timePieces[0]) - 12) : Number(timePieces[0]));
					var mm = Number(timePieces[1]);
					var s = Number(timePieces[2]) || 0;
					return new Date(y, m, d, h, mm, s);
				}
		};
		
		var URL = {
				/**
				 * Reads in a web page's URL and returns a JSON object containing mapped URL parameters
				 * @returns {Object} data Object encapsulating all parameters in page URL
				 * NOTE: Only use in client SuiteScripts, will not work for server-side scripts
				 */
				QueryString : function() {
					// This function is anonymous, is executed immediately and
					// the return value is assigned to QueryString!
					var query_string = {};
					var query = window.location.search.substring(1);
					var vars = query.split("&");
					for (var i = 0; i < vars.length; i++) {
						var pair = vars[i].split("=");
						// If first entry with this name
						if (typeof query_string[pair[0]] === "undefined") {
							query_string[pair[0]] = pair[1];
							// If second entry with this name
						} else if (typeof query_string[pair[0]] === "string") {
							var arr = [ query_string[pair[0]], pair[1] ];
							query_string[pair[0]] = arr;
							// If third or later entry with this name
						} else {
							query_string[pair[0]].push(pair[1]);
						}
					}
					
					return query_string;
				}
			
				/**
				 * Escapes special characters in URL text
				 * @param {string} urlVal URL string to be escaped
				 * @returns {string}
				 */
			,	escapeURL : function(urlVal) {
					urlVal = urlVal || "";
					return urlVal
						.replace(/%/ig, "%25")
						.replace(/ /ig, "%20")
						.replace(/\$/ig, "%24")
						.replace(/\&/ig, "%26")
						.replace(/`/ig, "%60")
						.replace(/:/ig, "%3A")
						.replace(/</ig, "%3C")
						.replace(/>/ig, "%3E")
						.replace(/\[/ig, "%5B")
						.replace(/\]/ig, "%5D")
						.replace(/\{/ig, "%7B")
						.replace(/\}/ig, "%7D")
						.replace(/\"/ig, "%22")
						.replace(/\'/ig, "%20")
						.replace(/;/ig, "%3B")
						.replace(/\+/ig, "%2B")
						.replace(/#/ig, "%23")
						.replace(/\?/ig, "%3F")
						.replace(/\//ig, "%2F")
						.replace(/\\/ig, "%5C")
						.replace(/\=/ig, "%3D")
						.replace(/\|/ig, "%7C")
						.replace(/~/ig, "%7E")
						.replace(/\^/ig, "%5E")
						.replace(/@/ig, "%40")
						.replace(/\,/ig, "%2C")
						.replace(/\!/ig, "%21")
						.replace(/\*/ig, "%2A")
						.replace(/\(/ig, "%28")
						.replace(/\)/ig, "%29")
						;
				}
			
				/**
				 * Decodes escaped URL special characters in a string
				 * @param {string} text
				 * @returns {string}
				 */
			,	decodeURL : function(text) {
					return text
						.replace(/\+/g, " ")
						.replace(/\%26/g, "&")
						.replace(/\%2B/g, "+")
						.replace(/\%3D/g, "=")
						.replace(/\%24/g, "$")
						.replace(/\%60/g, "`")
						.replace(/\%3A/g, ":")
						.replace(/\%3C/g, "<")
						.replace(/\%3E/g, ">")
						.replace(/\%5B/g, "[")
						.replace(/\%5D/g, "]")
						.replace(/\%7B/g, "{")
						.replace(/\%7D/g, "}")
						.replace(/\%22/g, "\"")
						.replace(/\%20/g, "'")
						.replace(/\%3B/g, ";")
						.replace(/\%23/g, "#")
						.replace(/\%3F/g, "?")
						.replace(/\%2F/g, "/")
						.replace(/\%5C/g, "\\")
						.replace(/\%7C/g, "|")
						.replace(/\%7E/g, "~")
						.replace(/\%5E/g, "^")
						.replace(/\%40/g, "@")
						.replace(/\%2C/g, ",")
						.replace(/\%21/g, "!")
						.replace(/\%2A/g, "*")
						.replace(/\%25/g, "%")
						;
				}
		};
		
		var LOGGING = {
				createActivityLog : function(status, title, details, zone, err, showSec, type) {
					try {
						var logRec = record.create({ type : "customrecord_ng_log" }), rawStack, stack;
						
						if (!isEmpty(type)) {
							logRec.setValue({ fieldId : "custrecord_nglog_type" , value : type });
						}
						if (!isEmpty(status)) {
							logRec.setValue({ fieldId : "custrecord_nglog_status" , value : status });
						}
						if (!isEmpty(title)) {
							logRec.setValue({ fieldId : "custrecord_nglog_title" , value : xml.escape({ xmlText : title }) });
						}
						
						if (!isEmpty(details) && isEmpty(err)) {
							logRec.setValue({ fieldId : "custrecord_nglog_details" , value : xml.escape({ xmlText : details }) });
						} else if (isEmpty(details) && !isEmpty(err)) {
							if (!(err instanceof Error)) {
								rawStack = err.stack;
								stack = util.isArray(rawStack) ? rawStack.join(", ") : rawStack;
								logRec.setValue({ fieldId : "custrecord_nglog_details" , value : "[{0}] {1}{2}".NG_Format(err.name,err.message,!isEmpty(stack)?" -- {0}".NG_Format(stack):"") });
							} else {
								logRec.setValue({ fieldId : "custrecord_nglog_details" , value : "[{0}] {1}".NG_Format(err.name,err.message) });
							}
						} else if (!isEmpty(details) && !isEmpty(err)) {
							var msg = "";
							if (!(err instanceof Error)) {
								rawStack = err.stack;
								stack = util.isArray(rawStack) ? rawStack.join(", ") : rawStack;
								msg = "[{0}] {1}{2}".NG_Format(err.name,err.message,!isEmpty(stack)?" -- {0}".NG_Format(stack):"");
							} else {
								msg = "[{0}] {1}".NG_Format(err.name,err.message);
							}
							
							logRec.setValue({ fieldId : "custrecord_nglog_details" , value : "{0} \//*****\// {1}".NG_Format(details,msg) });
						} else {
							logRec.setValue({ fieldId : "custrecord_nglog_details" , value : "N/A" });
						}
						logRec.save({ enableSourcing : true , ignoreMandatoryFields : true });
					} catch (err) {
						LOGGING.logError(err, "Error encountered submitting activity log record");
					}
				}
			
			,	logError : function(err, message, details) {
					if (!isEmpty(err) /*&& !(err instanceof Error)*/) {
						var rawStack = err.stack;
						var stack = util.isArray(rawStack) ? rawStack.join(", ") : rawStack;
						if (!isEmpty(details)) {
							log.error({ title : message , details : "[{0}] {1}{2} -- {3}".NG_Format(err.name,err.message,!isEmpty(stack)?" -- {0}".NG_Format(stack):"",details) });
						} else {
							log.error({ title : message , details : "[{0}] {1}{2}".NG_Format(err.name,err.message,!isEmpty(stack)?" -- {0}".NG_Format(stack):"") });
						}
					} /*else if (!isEmpty(err)) {
						if (!isEmpty(details)) {
							log.error({ title : message , details : "[{0}] {1} -- {2}".NG_Format(err.name,err.message,details) });
						} else {
							log.error({ title : message , details : "[{0}] {1}".NG_Format(err.name,err.message) });
						}
					}*/ else {
						if (!isEmpty(details)) {
							log.error({ title : message , details : details });
						} else {
							log.error({ title : message , details : "" });
						}
					}
				}
			
			,	prettyError : function(name, message) {
					var errCSS = '<style>.text {display: none;}'; // this will hide the JSON message
					errCSS += '.bglt td:first-child:not(.textboldnolink):after {';
					errCSS += 'color:black;font-size:12pt;'; // set the desired css for our message';
					errCSS += 'content: url(/images/5square.gif) \'';
					errCSS += '  {0}'.NG_Format(message);
					errCSS += '\'}';
					errCSS += '.textboldnolink {display: none;}';
					errCSS += '</style>';
					return error.create({
							name : name
						,	message : errCSS
						,	notifyOff : true
					});
				}
		};
		
		var MATH = {
				/**
				 * Rounds the passed in value to 1 decimal point
				 * @param {Number} num Number to be rounded
				 * @returns {Number}
				 */
				roundToTenths: function(num) {
					if (isNaN(Number(num))) {
						return num;
					}
					
					return Number(((Math.round(num * 10)) / 10));
				}
			
				/**
				 * Rounds the passed in value to 2 decimal points
				 * @param {Number} num Number to be rounded
				 * @returns {Number}
				 */
			,	roundToHundredths : function(num) {
					if (isNaN(Number(num))) {
						return num;
					}
					
					return (Math.round(num * 100) / 100);
				}
			
				/**
				 * Rounds the passed in value to 3 decimal points
				 * @param {Number} num Number to be rounded
				 * @returns {Number}
				 */
			,	roundToThousandths : function(num) {
					if (isNaN(Number(num))) {
						return num;
					}
					
					return (Math.round(num * 1000) / 1000);
				}
			
				/**
				 * Rounds the passed in value to 4 decimal points
				 * @param {Number} num Number to be rounded
				 * @returns {Number}
				 */
			,	roundToTenThousandths : function(num) {
					if (isNaN(Number(num))) {
						return num;
					}
					
					return (Math.round(num * 10000) / 10000);
				}
			
				/**
				 * Rounds the passed in value to 5 decimal points
				 * @param {Number} num Number to be rounded
				 * @returns {Number}
				 */
			,	roundToHundThousandths : function(num) {
					if (isNaN(Number(num))) {
						return num;
					}
					
					return (Math.round(num * 100000) / 100000);
				}
			
				/**
				 * @param {Number} num Number to be rounded
				 * @param {Number} dec Positive integer value of decimal places to return
				 *
				 * @returns {Number}
				 */
			,	roundToDecimal : function(num, dec) {
					if (isNaN(Number(num))) {
						return num;
					} else if (isNaN(Number(dec))) {
						return num;
					} else if (Number(dec) < 0) {
						return num;
					}
					dec = Math.floor(Number(dec));
					
					return (Math.round(num * Math.pow(10, dec)) / Math.pow(10, dec));
				}
			
				/**
				 * Generates a random number within the range specified
				 * @param {Number} min=0
				 * @param {Number} max=100
				 * @returns {Number}
				 */
			,	randomInt : function(min, max) {
					min = Math.ceil(min || 0);
					max = Math.floor(max || 100);
					return Math.floor(Math.random() * (max - min + 1)) + min;
				}
		};
		
		var FEATURES = {
				ADVINVENTORYMGMT :		function() { return runtime.isFeatureInEffect({ feature : "ADVINVENTORYMGMT" }); }
			,	CLASSES :				function() { return runtime.isFeatureInEffect({ feature : "CLASSES" }); }
			,	DEPARTMENTS :			function() { return runtime.isFeatureInEffect({ feature : "DEPARTMENTS" }); }
			,	DOCUMENTS :				function() { return runtime.isFeatureInEffect({ feature : "DOCUMENTS" }); }
			,	DUPLICATES :			function() { return runtime.isFeatureInEffect({ feature : "DUPLICATES" }); }
			,	GIFTCERTIFICATES :		function() { return runtime.isFeatureInEffect({ feature : "GIFTCERTIFICATES" }); }
			,	GROSSPROFIT :			function() { return runtime.isFeatureInEffect({ feature : "GROSSPROFIT" }); }
			,	INVAPPROVAL :			function() { return runtime.isFeatureInEffect({ feature : "CUSTOMAPPROVALCUSTINVC" }); }
			,	INVENTORY :				function() { return runtime.isFeatureInEffect({ feature : "INVENTORY" }); }
			,	ITEMOPTIONS :			function() { return runtime.isFeatureInEffect({ feature : "ITEMOPTIONS" }); }
			,	LANDEDCOST :			function() { return runtime.isFeatureInEffect({ feature : "LANDEDCOST" }); }
			,	LOCATIONS :				function() { return runtime.isFeatureInEffect({ feature : "LOCATIONS" }); }
			,	LOTNUMBEREDINVENTORY :	function() { return runtime.isFeatureInEffect({ feature : "LOTNUMBEREDINVENTORY" }); }
			,	MATRIXITEMS :			function() { return runtime.isFeatureInEffect({ feature : "MATRIXITEMS" }); }
			,	MULTICURRENCY :			function() { return runtime.isFeatureInEffect({ feature : "MULTICURRENCY" }); }
			,	MULTICURRENCYVENDOR :	function() { return runtime.isFeatureInEffect({ feature : "MULTICURRENCYVENDOR" }); }
			,	MULTILOCINVT :			function() { return runtime.isFeatureInEffect({ feature : "MULTILOCINVT" }); }
			,	MULTIPARTNER :			function() { return runtime.isFeatureInEffect({ feature : "MULTIPARTNER" }); }
			,	MULTPRICE :				function() { return runtime.isFeatureInEffect({ feature : "MULTPRICE" }); }
			,	MULTISUBCUSTOMER :		function() { return runtime.isFeatureInEffect({ feature : "MULTISUBSIDIARYCUSTOMER" }); }
			,	PICKPACKSHIP :			function() { return runtime.isFeatureInEffect({ feature : "PICKPACKSHIP" }); }
			,	QUANTITYPRICING :		function() { return runtime.isFeatureInEffect({ feature : "QUANTITYPRICING" }); }
			,	REVENUERECOGNITION :	function() { return runtime.isFeatureInEffect({ feature : "REVENUERECOGNITION" }); }
			,	SERIALIZEDINVENTORY :	function() { return runtime.isFeatureInEffect({ feature : "SERIALIZEDINVENTORY" }); }
			,	SHIPPINGLABELS :		function() { return runtime.isFeatureInEffect({ feature : "SHIPPINGLABELS" }); }
			,	SUBSIDIARIES :			function() { return runtime.isFeatureInEffect({ feature : "SUBSIDIARIES" }); }
			,	TIMETRACKING :			function() { return runtime.isFeatureInEffect({ feature : "TIMETRACKING" }); }
			,	UNITSOFMEASURE :		function() { return runtime.isFeatureInEffect({ feature : "UNITSOFMEASURE" }); }
			,	WMS :					function() { return runtime.isFeatureInEffect({ feature : "WMSSYSTEM" }); }
		};
		
		var JQ_CSV = {
				defaults : {
						separator : ','
					,	delimiter : '"'
					,	headers : true
				}
			
			,	hooks : {
					castToScalar: function(value, state) {
						state = state || null;
						if (state !== null) { /* do nothing */ }
						var hasDot = /\./;
						if (isNaN(value)) {
							return value;
						} else {
							if (hasDot.test(value)) {
								return parseFloat(value);
							} else {
								var integer = parseInt(value);
								if (isNaN(integer)) {
									return null;
								} else {
									return integer;
								}
							}
						}
					}
				}
			
			,	parsers: {
					parse: function(csv, options) {
						// cache settings
						var separator = options.separator;
						var delimiter = options.delimiter;
						
						// set initial state if it's missing
						if (!options.state.rowNum) {
							options.state.rowNum = 1;
						}
						if (!options.state.colNum) {
							options.state.colNum = 1;
						}
						
						// clear initial state
						var data = [];
						var entry = [];
						var state = 0;
						var value = '';
						var exit = false;
						
						function endOfEntry() {
							// reset the state
							state = 0;
							value = '';
							
							// if 'start' hasn't been met, don't output
							if (options.start && options.state.rowNum < options.start) {
								// update global state
								entry = [];
								options.state.rowNum++;
								options.state.colNum = 1;
								return;
							}
							
							if (options.onParseEntry === undefined) {
								// onParseEntry hook not set
								data.push(entry);
							} else {
								var hookVal = options.onParseEntry(entry, options.state); // onParseEntry Hook
								// false skips the row, configurable through a hook
								if (hookVal !== false) {
									data.push(hookVal);
								}
							}
							//console.log('entry:' + entry);
							
							// cleanup
							entry = [];
							
							// if 'end' is met, stop parsing
							if (options.end && options.state.rowNum >= options.end) {
								exit = true;
							}
							
							// update global state
							options.state.rowNum++;
							options.state.colNum = 1;
						}
						
						function endOfValue() {
							if (options.onParseValue === undefined) {
								// onParseValue hook not set
								entry.push(value);
							} else {
								var hook = options.onParseValue(value, options.state); // onParseValue Hook
								// false skips the row, configurable through a hook
								if (hook !== false) {
									entry.push(hook);
								}
							}
							//console.log('value:' + value);
							// reset the state
							value = '';
							state = 0;
							// update global state
							options.state.colNum++;
						}
						
						// escape regex-specific control chars
						var escSeparator = RegExp.escape(separator);
						var escDelimiter = RegExp.escape(delimiter);
						
						// compile the regEx str using the custom delimiter/separator
						var match = /(D|S|\n|\r|[^DS\r\n]+)/;
						var matchSrc = match.source;
						matchSrc = matchSrc.replace(/S/g, escSeparator);
						matchSrc = matchSrc.replace(/D/g, escDelimiter);
						match = RegExp(matchSrc, 'gm');
						
						// put on your fancy pants...
						// process control chars individually, use look-ahead on non-control chars
						csv.replace(match, function(m0) {
							if (exit) {
								return;
							}
							switch (state) {
								// the start of a value
								case 0:
									// null last value
									if (m0 === separator) {
										value += '';
										endOfValue();
										break;
									}
									// opening delimiter
									if (m0 === delimiter) {
										state = 1;
										break;
									}
									// null last value
									if (m0 === '\n') {
										endOfValue();
										endOfEntry();
										break;
									}
									// phantom carriage return
									if (/^\r$/.test(m0)) {
										break;
									}
									// un-delimited value
									value += m0;
									state = 3;
									break;
								
								// delimited input
								case 1:
									// second delimiter? check further
									if (m0 === delimiter) {
										state = 2;
										break;
									}
									// delimited data
									value += m0;
									state = 1;
									break;
								
								// delimiter found in delimited input
								case 2:
									// escaped delimiter?
									if (m0 === delimiter) {
										value += m0;
										state = 1;
										break;
									}
									// null value
									if (m0 === separator) {
										endOfValue();
										break;
									}
									// end of entry
									if (m0 === '\n') {
										endOfValue();
										endOfEntry();
										break;
									}
									// phantom carriage return
									if (/^\r$/.test(m0)) {
										break;
									}
									// broken parser?
									throw new Error('CSVDataError: Illegal State [Row:' + options.state.rowNum + '][Col:' + options.state.colNum + ']');
								
								// un-delimited input
								case 3:
									// null last value
									if (m0 === separator) {
										endOfValue();
										break;
									}
									// end of entry
									if (m0 === '\n') {
										endOfValue();
										endOfEntry();
										break;
									}
									// phantom carriage return
									if (/^\r$/.test(m0)) {
										break;
									}
									if (m0 === delimiter) {
										// non-compliant data
										throw new Error('CSVDataError: Illegal Quote [Row:' + options.state.rowNum + '][Col:' + options.state.colNum + ']');
									}
									// broken parser?
									throw new Error('CSVDataError: Illegal Data [Row:' + options.state.rowNum + '][Col:' + options.state.colNum + ']');
								
								default:
									// shenanigans
									throw new Error('CSVDataError: Unknown State [Row:' + options.state.rowNum + '][Col:' + options.state.colNum + ']');
							}
							//console.log('val:' + m0 + ' state:' + state);
						});
						
						// submit the last entry
						// ignore null last line
						if (entry.length !== 0) {
							endOfValue();
							endOfEntry();
						}
						
						return data;
					}
				
					// a csv-specific line splitter
				,	splitLines: function(csv, options) {
						// cache settings
						var separator = options.separator;
						var delimiter = options.delimiter;
						
						// set initial state if it's missing
						if (!options.state.rowNum) {
							options.state.rowNum = 1;
						}
						
						// clear initial state
						var entries = [];
						var state = 0;
						var entry = '';
						var exit = false;
						
						function endOfLine() {
							// reset the state
							state = 0;
							
							// if 'start' hasn't been met, don't output
							if (options.start && options.state.rowNum < options.start) {
								// update global state
								entry = '';
								options.state.rowNum++;
								return;
							}
							
							if (options.onParseEntry === undefined) {
								// onParseEntry hook not set
								entries.push(entry);
							} else {
								var hookVal = options.onParseEntry(entry, options.state); // onParseEntry Hook
								// false skips the row, configurable through a hook
								if (hookVal !== false) {
									entries.push(hookVal);
								}
							}
							
							// cleanup
							entry = '';
							
							// if 'end' is met, stop parsing
							if (options.end && options.state.rowNum >= options.end) {
								exit = true;
							}
							
							// update global state
							options.state.rowNum++;
						}
						
						// escape regex-specific control chars
						var escSeparator = RegExp.escape(separator);
						var escDelimiter = RegExp.escape(delimiter);
						
						// compile the regEx str using the custom delimiter/separator
						var match = /(D|S|\n|\r|[^DS\r\n]+)/;
						var matchSrc = match.source;
						matchSrc = matchSrc.replace(/S/g, escSeparator);
						matchSrc = matchSrc.replace(/D/g, escDelimiter);
						match = RegExp(matchSrc, 'gm');
						
						// put on your fancy pants...
						// process control chars individually, use look-ahead on non-control chars
						csv.replace(match, function(m0) {
							if (exit) {
								return;
							}
							switch (state) {
								// the start of a value/entry
								case 0:
									// null value
									if (m0 === separator) {
										entry += m0;
										state = 0;
										break;
									}
									// opening delimiter
									if (m0 === delimiter) {
										entry += m0;
										state = 1;
										break;
									}
									// end of line
									if (m0 === '\n') {
										endOfLine();
										break;
									}
									// phantom carriage return
									if (/^\r$/.test(m0)) {
										break;
									}
									// un-delimit value
									entry += m0;
									state = 3;
									break;
								
								// delimited input
								case 1:
									// second delimiter? check further
									if (m0 === delimiter) {
										entry += m0;
										state = 2;
										break;
									}
									// delimited data
									entry += m0;
									state = 1;
									break;
								
								// delimiter found in delimited input
								case 2:
									// escaped delimiter?
									var prevChar = entry.substr(entry.length - 1);
									if (m0 === delimiter && prevChar === delimiter) {
										entry += m0;
										state = 1;
										break;
									}
									// end of value
									if (m0 === separator) {
										entry += m0;
										state = 0;
										break;
									}
									// end of line
									if (m0 === '\n') {
										endOfLine();
										break;
									}
									// phantom carriage return
									if (m0 === '\r') {
										break;
									}
									// broken parser?
									throw new Error('CSVDataError: Illegal state [Row:' + options.state.rowNum + ']');
								
								// un-delimited input
								case 3:
									// null value
									if (m0 === separator) {
										entry += m0;
										state = 0;
										break;
									}
									// end of line
									if (m0 === '\n') {
										endOfLine();
										break;
									}
									// phantom carriage return
									if (m0 === '\r') {
										break;
									}
									// non-compliant data
									if (m0 === delimiter) {
										throw new Error('CSVDataError: Illegal quote [Row:' + options.state.rowNum + ']');
									}
									// broken parser?
									throw new Error('CSVDataError: Illegal state [Row:' + options.state.rowNum + ']');
								
								default:
									// shenanigans
									throw new Error('CSVDataError: Unknown state [Row:' + options.state.rowNum + ']');
							}
							//console.log('val:' + m0 + ' state:' + state);
						});
						
						// submit the last entry
						// ignore null last line
						if (entry !== '') {
							endOfLine();
						}
						
						return entries;
					}
				
					// a csv entry parser
				,	parseEntry: function(csv, options) {
						// cache settings
						var separator = options.separator;
						var delimiter = options.delimiter;
						
						// set initial state if it's missing
						if (!options.state.rowNum) {
							options.state.rowNum = 1;
						}
						if (!options.state.colNum) {
							options.state.colNum = 1;
						}
						
						// clear initial state
						var entry = [];
						var state = 0;
						var value = '';
						
						function endOfValue() {
							if (options.onParseValue === undefined) {
								// onParseValue hook not set
								entry.push(value);
							} else {
								var hook = options.onParseValue(value, options.state); // onParseValue Hook
								// false skips the value, configurable through a hook
								if (hook !== false) {
									entry.push(hook);
								}
							}
							// reset the state
							value = '';
							state = 0;
							// update global state
							options.state.colNum++;
						}
						
						// checked for a cached regEx first
						if (!options.match) {
							// escape regex-specific control chars
							var escSeparator = RegExp.escape(separator);
							var escDelimiter = RegExp.escape(delimiter);
							
							// compile the regEx str using the custom delimiter/separator
							var match = /(D|S|\n|\r|[^DS\r\n]+)/;
							var matchSrc = match.source;
							matchSrc = matchSrc.replace(/S/g, escSeparator);
							matchSrc = matchSrc.replace(/D/g, escDelimiter);
							options.match = RegExp(matchSrc, 'gm');
						}
						
						// put on your fancy pants...
						// process control chars individually, use look-ahead on non-control chars
						csv.replace(options.match, function(m0) {
							switch (state) {
								// the start of a value
								case 0:
									// null last value
									if (m0 === separator) {
										value += '';
										endOfValue();
										break;
									}
									// opening delimiter
									if (m0 === delimiter) {
										state = 1;
										break;
									}
									// skip un-delimited new-lines
									if (m0 === '\n' || m0 === '\r') {
										break;
									}
									// un-delimited value
									value += m0;
									state = 3;
									break;
								
								// delimited input
								case 1:
									// second delimiter? check further
									if (m0 === delimiter) {
										state = 2;
										break;
									}
									// delimited data
									value += m0;
									state = 1;
									break;
								
								// delimiter found in delimited input
								case 2:
									// escaped delimiter?
									if (m0 === delimiter) {
										value += m0;
										state = 1;
										break;
									}
									// null value
									if (m0 === separator) {
										endOfValue();
										break;
									}
									// skip un-delimited new-lines
									if (m0 === '\n' || m0 === '\r') {
										break;
									}
									// broken parser?
									throw new Error('CSVDataError: Illegal State [Row:' + options.state.rowNum + '][Col:' + options.state.colNum + ']');
								
								// un-delimited input
								case 3:
									// null last value
									if (m0 === separator) {
										endOfValue();
										break;
									}
									// skip un-delimited new-lines
									if (m0 === '\n' || m0 === '\r') {
										break;
									}
									// non-compliant data
									if (m0 === delimiter) {
										throw new Error('CSVDataError: Illegal Quote [Row:' + options.state.rowNum + '][Col:' + options.state.colNum + ']');
									}
									// broken parser?
									throw new Error('CSVDataError: Illegal Data [Row:' + options.state.rowNum + '][Col:' + options.state.colNum + ']');
								
								default:
									// shenanigans
									throw new Error('CSVDataError: Unknown State [Row:' + options.state.rowNum + '][Col:' + options.state.colNum + ']');
							}
							//console.log('val:' + m0 + ' state:' + state);
						});
						
						// submit the last value
						endOfValue();
						
						return entry;
					}
			}
			
				/**
				 * $.csv.toArray(csv)
				 * Converts a CSV entry string to a javascript array.
				 *
				 * @param {Array} csv The string containing the CSV data.
				 * @param {Object} [options] An object containing user-defined options.
				 * @param {Character} [separator] An override for the separator character. Defaults to a comma(,).
				 * @param {Character} [delimiter] An override for the delimiter character. Defaults to a double-quote(").
				 *
				 * This method deals with simple CSV strings only. It's useful if you only
				 * need to parse a single entry. If you need to parse more than one line,
				 * use $.csv2Array instead.
				 */
			,	toArray : function(csv, options, callback) {
					options = (options !== undefined ? options : {});
					var config = {};
					config.callback = ((callback !== undefined && typeof(callback) === 'function') ? callback : false);
					config.separator = 'separator' in options ? options.separator : JQ_CSV.defaults.separator;
					config.delimiter = 'delimiter' in options ? options.delimiter : JQ_CSV.defaults.delimiter;
					var state = (options.state !== undefined ? options.state : {});
					
					// setup
					options = {
							delimiter : config.delimiter
						,	separator : config.separator
						,	onParseEntry : options.onParseEntry
						,	onParseValue : options.onParseValue
						,	state : state
					};
					
					var entry = JQ_CSV.parsers.parseEntry(csv, options);
					
					// push the value to a callback if one is defined
					if (!config.callback) {
						return entry;
					} else {
						config.callback('', entry);
					}
				}
			
				/**
				 * $.csv.toArrays(csv)
				 * Converts a CSV string to a javascript array.
				 *
				 * @param {String} csv The string containing the raw CSV data.
				 * @param {Object} [options] An object containing user-defined options.
				 * @param {Character} [separator] An override for the separator character. Defaults to a comma(,).
				 * @param {Character} [delimiter] An override for the delimiter character. Defaults to a double-quote(").
				 *
				 * This method deals with multi-line CSV. The breakdown is simple. The first
				 * dimension of the array represents the line (or entry/row) while the second
				 * dimension contains the values (or values/columns).
				 */
			,	toArrays : function(csv, options, callback) {
					options = (options !== undefined ? options : {});
					var config = {};
					config.callback = ((callback !== undefined && typeof(callback) === 'function') ? callback : false);
					config.separator = 'separator' in options ? options.separator : JQ_CSV.defaults.separator;
					config.delimiter = 'delimiter' in options ? options.delimiter : JQ_CSV.defaults.delimiter;
					
					// setup
					var data = [];
					options = {
							delimiter : config.delimiter
						,	separator : config.separator
						,	onParseEntry : options.onParseEntry
						,	onParseValue : options.onParseValue
						,	start : options.start
						,	end : options.end
						,	state : {
								rowNum : 1
							,	colNum : 1
						}
					};
					
					// break the data down to lines
					data = JQ_CSV.parsers.parse(csv, options);
					
					// push the value to a callback if one is defined
					if (!config.callback) {
						return data;
					} else {
						config.callback('', data);
					}
				}
			
				/**
				 * $.csv.toObjects(csv)
				 * Converts a CSV string to a javascript object.
				 * @param {String} csv The string containing the raw CSV data.
				 * @param {Object} [options] An object containing user-defined options.
				 * @param {Character} [separator] An override for the separator character. Defaults to a comma(,).
				 * @param {Character} [delimiter] An override for the delimiter character. Defaults to a double-quote(").
				 * @param {Boolean} [headers] Indicates whether the data contains a header line. Defaults to true.
				 *
				 * This method deals with multi-line CSV strings. Where the headers line is
				 * used as the key for each value per entry.
				 */
			,	toObjects : function(csv, options, callback) {
					options = (options !== undefined ? options : {});
					var config = {};
					config.callback = ((callback !== undefined && typeof(callback) === 'function') ? callback : false);
					config.separator = 'separator' in options ? options.separator : JQ_CSV.defaults.separator;
					config.delimiter = 'delimiter' in options ? options.delimiter : JQ_CSV.defaults.delimiter;
					config.headers = 'headers' in options ? options.headers : JQ_CSV.defaults.headers;
					options.start = 'start' in options ? options.start : 1;
					
					// account for headers
					if (config.headers) {
						options.start++;
					}
					if (options.end && config.headers) {
						options.end++;
					}
					
					// setup
					var lines = [];
					var data = [];
					
					options = {
							delimiter : config.delimiter
						,	separator : config.separator
						,	onParseEntry : options.onParseEntry
						,	onParseValue : options.onParseValue
						,	start : options.start
						,	end : options.end
						,	state : {
								rowNum : 1
							,	colNum : 1
						}
						,	match : false
					};
					
					// fetch the headers
					var headerOptions = {
							delimiter : config.delimiter
						,	separator : config.separator
						,	start : 1
						,	end : 1
						,	state : {
								rowNum : 1
							,	colNum : 1
						}
					};
					var headerLine = JQ_CSV.parsers.splitLines(csv, headerOptions);
					var headers = JQ_CSV.toArray(headerLine[0], options);
					
					// fetch the data
					lines = JQ_CSV.parsers.splitLines(csv, options);
					
					// reset the state for re-use
					options.state.colNum = 1;
					if (headers){
						options.state.rowNum = 2;
					} else {
						options.state.rowNum = 1;
					}
					
					// convert data to objects
					for (var i = 0, len = lines.length; i < len; i++) {
						var entry = JQ_CSV.toArray(lines[i], options);
						var object = {};
						for (var j in headers) {
							object[headers[j]] = entry[j];
						}
						data.push(object);
						
						// update row state
						options.state.rowNum++;
					}
					
					// push the value to a callback if one is defined
					if (!config.callback) {
						return data;
					} else {
						config.callback('', data);
					}
				}
			
				/**
				 * $.csv.fromArrays(arrays)
				 * Converts a javascript array to a CSV String.
				 *
				 * @param {Array} array An array containing an array of CSV entries.
				 * @param {Object} [options] An object containing user-defined options.
				 * @param {Character} [separator] An override for the separator character. Defaults to a comma(,).
				 * @param {Character} [delimiter] An override for the delimiter character. Defaults to a double-quote(").
				 *
				 * This method generates a CSV file from an array of arrays (representing entries).
				 */
			,	fromArrays: function(arrays, options, callback) {
					options = (options !== undefined ? options : {});
					var config = {};
					config.callback = ((callback !== undefined && typeof(callback) === 'function') ? callback : false);
					config.separator = 'separator' in options ? options.separator : JQ_CSV.defaults.separator;
					config.delimiter = 'delimiter' in options ? options.delimiter : JQ_CSV.defaults.delimiter;
					config.escaper = 'escaper' in options ? options.escaper : JQ_CSV.defaults.escaper;
					config.experimental = 'experimental' in options ? options.experimental : false;
					
					if (!config.experimental) {
						throw new Error('not implemented');
					}
					
					var output = [];
					for (var i in arrays) {
						output.push(arrays[i]);
					}
					
					// push the value to a callback if one is defined
					if (!config.callback) {
						return output;
					} else {
						config.callback('', output);
					}
				}
			
				/**
				 * $.csv.fromObjects(objects)
				 * Converts a javascript dictionary to a CSV string.
				 * @param {Object} objects An array of objects containing the data.
				 * @param {Object} [options] An object containing user-defined options.
				 * @param {Character} [separator] An override for the separator character. Defaults to a comma(,).
				 * @param {Character} [delimiter] An override for the delimiter character. Defaults to a double-quote(").
				 *
				 * This method generates a CSV file from an array of objects (name:value pairs).
				 * It starts by detecting the headers and adding them as the first line of
				 * the CSV file, followed by a structured dump of the data.
				 */
			,	fromObjects2CSV: function(objects, options, callback) {
					options = (options !== undefined ? options : {});
					var config = {};
					config.callback = ((callback !== undefined && typeof(callback) === 'function') ? callback : false);
					config.separator = 'separator' in options ? options.separator : JQ_CSV.defaults.separator;
					config.delimiter = 'delimiter' in options ? options.delimiter : JQ_CSV.defaults.delimiter;
					config.experimental = 'experimental' in options ? options.experimental : false;
					
					if (!config.experimental) {
						throw new Error('not implemented');
					}
					
					var output = [], arrays = [];
					for (var i in objects) {
						output.push(arrays[i]);
					}
					
					// push the value to a callback if one is defined
					if (!config.callback) {
						return output;
					} else {
						config.callback('', output);
					}
				}
		};
		
		var MODAL = {
				/**
				 * Adds NetSuite HTML field to the current form containing JavaScript/HTML to create a pop-up message modal
				 * @param {Form} form NetSuite page form
				 * @param {string} [text] Desired test to be displayed; Defaults to "Please wait..."
				 * @param {string} [modalID] Desired DOM element ID for the modal; Defaults to "ng_overlay"
				 */
				addMessageModal : function(form, text, modalID) {
					text = xml.escape({ xmlText : text || "Please wait..." });
					modalID = modalID || "ng_overlay";
					var rando = TOOLS.randomString(5, 0).toLowerCase();
					var htmlField = form.addField({ id : "custpage_ng_lib_cssmodal_{0}".NG_Format(rando) , type : "inlinehtml" , label : "message modal html" });
					// modal message box style definition (CSS)
					var html = "<style>\n";
					html += ".overlay {\n position: fixed;\n top: 0;\n bottom: 0;\n left: 0;\n right: 0;\n background: rgba(0, 0, 0, 0.7);\n transition: opacity 500ms, visibility 500ms;\n visibility: hidden;\n opacity: 0;\n z-index: 100000;\n}\n";
					html += ".overlay_a {\n  visibility: visible;\n opacity: 1;\n}\n";
					html += ".overlay_b {\n  visibility: hidden;\n opacity: 0;\n}\n";
					html += ".overlay:target {\n  visibility: visible;\n opacity: 1;\n}\n";
					html += ".popup {\n margin: 250px auto;\n padding: 20px;\n background: #fff;\n border-radius: 5px;\n width: 30%;\n position: relative;\n transition: all 5s ease-in-out;\n}\n";
					html += ".popup h2 {\n margin-top: 0;\n color: #333;\n font-family: Tahoma, Arial, sans-serif;\n font-size: 16px;\n}\n";
					html += ".popup .close {\n position: absolute;\n top: 20px;\n right: 30px;\n transition: all 200ms;\n font-size: 30px;\n font-weight: bold;\n text-decoration: none;\n color: #333;\n}\n";
					html += ".popup .close:hover {\n color: #06D85F;\n}\n";
					html += ".popup .content {\n max-height: 30%;\n overflow: auto;\n}\n";
					html += "@media screen and (max-width: 700px){\n";
					html += "	.box {\n width: 70%;\n}\n";
					html += "	.popup {\n width: 70%;\n}\n";
					html += "}\n";
					html += "</style>\n";
					// modal message box html
					html += "<div id=\"{0}\" class=\"overlay\">\n".NG_Format(modalID);
					html += "	<div class=\"popup\">\n";
					html += "		<a id=\"modalclose\" class=\"close\" href=\"#\">&nbsp;</a>\n";
					html += "		<div class=\"content\">\n";
					html += "			<h2 style=\"width:100%; text-align:center;\"><br />{0}<br /><br /></h2>\n".NG_Format(text);
					html += "		</div>\n";
					html += "	</div>\n";
					html += "</div>\n";
					
					htmlField.defaultValue = html;
				}
			
				/**
				 * Displays the message modal added to the current page
				 * @param [modalID] DOM element ID for the modal; Defaults to "ng_overlay"
				 */
			,	showMessageModal : function(modalID) {
					var popup = document.getElementById(modalID || "ng_overlay");
					if (!isEmpty(popup)) {
						popup.className = "overlay overlay_a";
					}
				}
			
				/**
				 * Hides the message modal added to the current page
				 * @param [modalID] DOM element ID for the modal; Defaults to "ng_overlay"
				 */
			,	hideMessageModal : function(modalID) {
					var popup = document.getElementById(modalID || "ng_overlay");
					if (!isEmpty(popup)) {
						popup.className = "overlay overlay_b";
					}
				}
			
				/**
				 * Creates a modal-based window alert
				 * @param message
				 * @param popupTitle
				 * @param hideClose
				 * @param closeCallback
				 * @param modalWidth
				 * @param z_index
				 * @returns {string}
				 */
			,	createModalAlert : function(message, popupTitle, hideClose, closeCallback, modalWidth, z_index) {
					var time = new Date().getTime();
					var modalID = 'popup_alert_modal_' + time;
					var callBackType;
					
					if (isEmpty(modalWidth)) {
						modalWidth = "30%";
					}
					
					if (isEmpty(z_index)) {
						z_index = 1000;
					}
					
					if (!isEmpty(closeCallback)) {
						callBackType = typeof closeCallback;
						console.log('Close Callback Type: ' + callBackType);
					}
					
					var html = "<div id=\"" + modalID + "\" class=\"overlayAlert" + modalID + "\">\n";
					html +=		"<style>\n";
					html += 		".overlayAlert" + modalID + " {\n position: fixed;\n top: 0;\n bottom: 0;\n left: 0;\n right: 0;\n background: rgba(0, 0, 0, 0.7);\n transition: opacity 500ms, visibility 500ms;\n visibility: hidden;\n opacity: 0;\n z-index: {0};\n}\n".NG_Format(z_index);
					html += 		".overlayAlert" + modalID + "_a {\n  visibility: visible;\n opacity: 1;\n}\n";
					html += 		".overlayAlert" + modalID + "_b {\n  visibility: hidden;\n opacity: 0;\n}\n";
					html += 		".overlayAlert" + modalID + ":target {\n  visibility: visible;\n opacity: 1;\n}\n";
					html += 		".popupAlert" + modalID + " {\n margin: auto;\n background: #fff;\n border-radius: 5px;\n width: " + modalWidth + ";\n position: relative;\n transition: all 5s ease-in-out;\n overflow: auto;\n max-height: 50%}\n";
					html += 		".popupAlert" + modalID + " h2 {\n margin-top: 0;\n color: #333;\n font-family: Tahoma, Arial, sans-serif;\n font-size: 16px;\n}\n";
					//html += ".popupAlert .close {\n position: absolute;\n top: 10px;\n right: 10px;\n transition: all 200ms;\n font-size: 10px;\n font-weight: bold;\n text-decoration: none;\n color: #333;\n opacity: 1;\n}\n";
					//html += ".popupAlert .close:hover {\n color: #06D85F;\n}\n";
					html += 		".popupAlert" + modalID + " .closeButton" + modalID + " {\n box-shadow:inset 0px 1px 0px 0px #54a3f7;\n background:linear-gradient(to bottom, #007dc1 5%, #0061a7 100%);\n background-color:#007dc1;\n border-radius:3px;\n border:1px solid #124d77;\n display:inline-block;\n cursor:pointer;\n color:#ffffff;\n font-family:Arial;\n font-size:8px;\n padding:4px 5px;\n text-decoration:none;\n text-shadow:0px 1px 0px #154682;\n position: absolute;\n top: 10px;\n right:10px;\n }\n";
					html += 		".popupAlert" + modalID + " .closeButton" + modalID + ":hover {\n background:linear-gradient(to bottom, #0061a7 5%, #007dc1 100%);\n background-color:#0061a7;\n }\n";
					html += 		".popupAlert" + modalID + " .closeButton" + modalID + ":active {\n position:absolute;\n top:11px;\n right:10px\n }\n";
					html += 		".popupAlert" + modalID + " .content" + modalID + " {\n max-height: 30%;\n min-height:75px;\n overflow: auto;\n position: relative;\n padding: 15px;\n text-align: center;}\n";
					html += 		".popupAlert-dialog-centered" + modalID + " {\n display: flex;\n align-items: center; \n min-height: calc(100% - (1.75em * 2));\n }\n";
					html +=			".popupAlert-header" + modalID + " {\n padding: 15px;\n border-bottom: 1px solid #e5e5e5;\n }\n";
					html += 		".popupAlert-title" + modalID + " {\n font-weight: bold;\n margin: 0;\n line-height: 1.42857143;\n }\n";
					html +=		 	"@media screen and (max-width: 700px){\n";
					html += 		"	.box" + modalID + "{\n width: 70%;\n}\n";
					html += 		"	.popupAlert" + modalID + "{\n width: 70%;\n}\n";
					html += 		"}\n";
					html += 	"</style>\n";
					
					// modal message box html
					
					html += "	 <div class=\"popupAlert-dialog-centered" + modalID + "\">\n";
					html += "		<div class=\"popupAlert" + modalID + "\">\n";
					html += "			<div class=\"popupAlert-header" + modalID + "\">\n";
					if (popupTitle) {
						html += "			<h5 class=\"popupAlert-title" + modalID + "\"><b>" + popupTitle + "</b></h5>\n";
					}
					if (!hideClose) {
						if (!isEmpty(closeCallback) && !isEmpty(callBackType) && callBackType === 'string') {
							html += "			<a id=\"modalclose_alert_modal" + modalID + "\" class=\"closeButton" + modalID + "\" onclick=\"closeModalAlert" + modalID + "('" + modalID + "', '" + closeCallback + "')\" href=\"#\">X</a>\n";
						} else if ((!isEmpty(closeCallback) && !isEmpty(callBackType) && callBackType === 'function') || isEmpty(closeCallback)) {
							html += "			<a id=\"modalclose_alert_modal" + modalID + "\" class=\"closeButton" + modalID + "\" onclick=\"closeModalAlert" + modalID + "('" + modalID + "')\" href=\"#\">X</a>\n";
						}
						
					}
					html += "			</div>\n";
					
					html += "			<div id=\"modalcontent_" + modalID + "\" class=\"content" + modalID + "\">\n";
					html +=  				message + "\n";
					html += "			</div>\n";
					html += "		</div>\n";
					html += "	</div>\n";
					html += "</div>\n";
					var html2 = "";
					html2 += "function closeModalAlert" + modalID + "(modalID,closeCallback){\n";
					html2 += 	"var popup = document.getElementById(modalID);\n";
					html2 += 	"if (popup != null){\n";
					html2 += 		"popup.className = \"overlay overlayAlert" + modalID + "_b\";\n";
					html2 += 		"setTimeout(function(){\n";
					html2 += 			"popup.parentNode.remove();\n";
					if (!isEmpty(closeCallback) && !isEmpty(callBackType) && callBackType === 'string') {
						html2 +=		"if(!_tools.isEmpty(closeCallback)){\n";
						html2 +=			"console.log(closeCallback);\n";
						
						html2 +=			"window[closeCallback]();\n";
						
						html2 +=		"}\n";
					} else if (!isEmpty(closeCallback) && !isEmpty(callBackType) && callBackType === 'function') {
						html2 +=		"var callback =  " + closeCallback + "\n;";
						html2 +=		"callback();\n";
					}
					
					html2 += 		"},500);\n";
					html2 += 	"}\n";
					html2 += "}\n";
					
					var customModal = document.createElement('div');
					customModal.setAttribute('id', 'customModal_' + modalID);
					customModal.innerHTML = html;
					
					document.body.appendChild(customModal);
					
					var closeScript = document.createElement('script');
					closeScript.type = "text/javascript";
					closeScript.innerHTML = html2;
					
					document.getElementById('customModal_' + modalID).appendChild(closeScript);
					
					setTimeout(function(){
						var popup = document.getElementById(modalID);
						
						if (popup != null){
							popup.className = "overlayAlert" + modalID + " overlayAlert" + modalID + "_a";
						}
					}, 100);
					
					return modalID;
				}
			
				/**
				 * Creates a modal-based window confirmation
				 * @param message
				 * @param yesCallback
				 * @param noCallback
				 * @param modalWidth
				 * @param z_index
				 */
			,	createModalConfirm : function(message, yesCallback, noCallback, modalWidth, z_index) {
					var time = new Date().getTime();
					var modalID = 'popup_confirm_modal_' + time;
					var yesCallbackType, noCallbackType;
					
					if (isEmpty(modalWidth)) {
						modalWidth = "30%";
					}
					
					if((isEmpty(z_index))){
						z_index = 1000;
					}
					
					if (!isEmpty(yesCallback)) {
						yesCallbackType = typeof yesCallback;
						console.log('Yes Close Callback Type: ' + yesCallbackType);
					}
					
					if (!isEmpty(noCallback)) {
						noCallbackType = typeof noCallback;
						console.log('No Close Callback Type: ' + noCallbackType);
					}
					
					var html = "<style>\n";
					html += ".overlayConfirm {\n position: fixed;\n top: 0;\n bottom: 0;\n left: 0;\n right: 0;\n background: rgba(0, 0, 0, 0.7);\n transition: opacity 500ms, visibility 500ms;\n visibility: hidden;\n opacity: 0;\n z-index: {0};\n}\n".NG_Format(z_index);
					html += ".overlayConfirm_a {\n  visibility: visible;\n opacity: 1;\n}\n";
					html += ".overlayConfirm_b {\n  visibility: hidden;\n opacity: 0;\n}\n";
					html += ".overlayConfirm:target {\n  visibility: visible;\n opacity: 1;\n}\n";
					html += ".popupConfirm {\n margin: 250px auto;\n padding: 20px;\n background: #fff;\n border-radius: 5px;\n width: " + modalWidth + ";\n position: relative;\n transition: all 5s ease-in-out;\n}\n";
					html += ".popupConfirm h2 {\n margin-top: 0;\n color: #333;\n font-family: Tahoma, Arial, sans-serif;\n font-size: 16px;\n}\n";
					html += ".popupConfirm .close {\n position: absolute;\n top: 10px;\n right: 10px;\n transition: all 200ms;\n font-size: 10px;\n font-weight: bold;\n text-decoration: none;\n color: #333;\n}\n";
					html += ".popupConfirm .close:hover {\n color: #06D85F;\n}\n";
					html += ".popupConfirm .content {\n max-height: 50%;\n overflow: auto;\n}\n";
					html += ".buttonConfirm {\n";
					html += "	background-color: #4CAF50;\n";
					html += "	border: none;\n";
					html += "	color: white;\n";
					html += "	padding: 15px 32px;\n";
					html += "	text-align: center;\n";
					html += "	text-decoration: none;\n";
					html += "	display: inline-block;\n";
					html += "	font-size: 16px;\n";
					html += "	margin: 4px 2px;\n";
					html += "	cursor: pointer;\n";
					html += "	border-radius: 12px;\n";
					html += "}\n";
					html += ".buttonConfirm2 {\n";
					html += "	background-color: #990000;\n";
					html += "	border: none;\n";
					html += "	color: white;\n";
					html += "	padding: 15px 32px;\n";
					html += "	text-align: center;\n";
					html += "	text-decoration: none;\n";
					html += "	display: inline-block;\n";
					html += "	font-size: 16px;\n";
					html += "	margin: 4px 2px;\n";
					html += "	cursor: pointer;\n";
					html += "	border-radius: 12px;\n";
					html += "}\n";
					
					html += "@media screen and (max-width: 700px){\n";
					html += "	.box{\n width: 70%;\n}\n";
					html += "	.popupConfirm{\n width: 70%;\n}\n";
					html += "}\n";
					html += "</style>\n";
					
					// modal message box html
					html += "<div id=\"popup_confirm_modal\" class=\"overlayConfirm\">\n";
					html += "	<div class=\"popupConfirm\">\n";
					html += "		<a id=\"modalclose_confirm_modal\" style=\"display:block;\" class=\"close\" onclick=\"closeModalConfirm('popup_confirm_modal')\" href=\"#\">close</a>\n";
					html += "		<div id=\"modalcontent\" class=\"content\">\n";
					html += "			<h2 style=\"width:100%; text-align:center;\"><br />" + message + "<br /><br /></h2>\n";
					html += "			<div style=\"text-align:center;\">";
					html +=	"				<a href=\"#\" class=\"buttonConfirm\" onclick=\"closeModalConfirm('popup_confirm_modal','Yes')\">Yes</a>\n";
					html +=	"				<a href=\"#\" class=\"buttonConfirm2\" onclick=\"closeModalConfirm('popup_confirm_modal','No')\">No</a>\n";
					html += "			</div>";
					html += "		</div>\n";
					html += "	</div>\n";
					html += "</div>\n";
					var html2 = "";
					html2 += "function closeModalConfirm(modalID,Answer){\n";
					html2 += "var popup = document.getElementById(modalID);\n";
					html2 += "if (popup != null){\n";
					html2 += "popup.className = \"overlay overlayConfirm_b\";\n";
					html2 += "setTimeout(function(){\n";
					html2 += "if(Answer == 'Yes'){\n";
					if (!isEmpty(yesCallback) && yesCallbackType === 'string') {
						html2 += yesCallback + ";\n";
					} else if (!isEmpty(yesCallback) && yesCallbackType === 'function'){
						html2 += "var callback = " + yesCallback + ";\n";
						html2 += "callback();\n";
					}
					html2 += "}else if(Answer == 'No'){\n";
					if (!isEmpty(noCallback) && noCallbackType === 'string') {
						html2 += noCallback + ";\n";
					} else if (!isEmpty(noCallback) && noCallbackType === 'function'){
						html2 += "var callback = " + noCallback + ";\n";
						html2 += "callback();\n";
					}
					html2 += "}\n";
					html2 += "popup.parentNode.removeChild(popup);\n";
					html2 += "},500);\n";
					html2 += "}\n";
					html2 += "}\n";
					
					var customModal = document.createElement('div');
					customModal.setAttribute('id', 'customModal');
					customModal.innerHTML = html;
					
					document.body.appendChild(customModal);
					
					var closeScript = document.createElement('script');
					closeScript.type = "text/javascript";
					closeScript.innerHTML = html2;
					
					document.body.appendChild(closeScript);
					
					setTimeout(function() {
						var popup = document.getElementById(modalID);
						if (!isEmpty(popup)) {
							popup.className = "overlayConfirm overlayConfirm_a";
						}
					},100);
				}
		};
		
		var UI = {
				addSuccessDiv : function(form, message, fieldId, atTop, fieldGroup, divId) {
					fieldGroup = fieldGroup || null;
					var content = LIB.successMsgAction.replace("<MESSAGE>", message);
					if (!isEmpty(divId)) {
						content.replace("div__alert", divId);
					}
					var divField;
					if (!isEmpty(fieldGroup)) {
						divField = form.addField({ id : fieldId , type : "inlinehtml" , label : "success" , container : fieldGroup });
					} else {
						divField = form.addField({ id : fieldId , type : "inlinehtml" , label : "success" });
					}
					if (atTop) {
						divField.updateLayoutType({ layoutType : "outsideabove" });
						divField.updateBreakType({ breakType : "startrow" });
					} else {
						divField.updateLayoutType({ layoutType : "normal" });
						divField.updateBreakType({ breakType : "none" });
					}
					divField.defaultValue = content;
				}
			
			,	addWarningDiv : function(form, message, fieldId, atTop, fieldGroup, divId) {
					fieldGroup = fieldGroup || null;
					var content = LIB.warningMsgAlt.replace("<MESSAGE>", message);
					if (!isEmpty(divId)) {
						content.replace("div__alert", divId);
					}
					var divField;
					if (!isEmpty(fieldGroup)) {
						divField = form.addField({ id : fieldId , type : "inlinehtml" , label : "warning" , container : fieldGroup });
					} else {
						divField = form.addField({ id : fieldId , type : "inlinehtml" , label : "warning" });
					}
					if (atTop) {
						divField.updateLayoutType({ layoutType : "outsideabove" });
						divField.updateBreakType({ breakType : "startrow" });
					} else {
						divField.updateLayoutType({ layoutType : "normal" });
						divField.updateBreakType({ breakType : "none" });
					}
					divField.defaultValue = content;
				}
			
			,	addFailureDiv : function(form, message, fieldId, atTop, fieldGroup, divId) {
					fieldGroup = fieldGroup || null;
					var content = LIB.failureMsgAlt.replace("<MESSAGE>", message);
					if (!isEmpty(divId)) {
						content.replace("div__alert", divId);
					}
					var divField;
					if (!isEmpty(fieldGroup)) {
						divField = form.addField({ id : fieldId , type : "inlinehtml" , label : "failure" , container : fieldGroup });
					} else {
						divField = form.addField({ id : fieldId , type : "inlinehtml" , label : "failure" });
					}
					if (atTop) {
						divField.updateLayoutType({ layoutType : "outsideabove" });
						divField.updateBreakType({ breakType : "startrow" });
					} else {
						divField.updateLayoutType({ layoutType : "normal" });
						divField.updateBreakType({ breakType : "none" });
					}
					divField.defaultValue = content;
				}
		};
		
		
		var LIB = {
				countries : [{"value":"AF","text":"Afghanistan"},{"value":"AX","text":"Aland Islands"},{"value":"AL","text":"Albania"},{"value":"DZ","text":"Algeria"},{"value":"AS","text":"American Samoa"},{"value":"AD","text":"Andorra"},{"value":"AO","text":"Angola"},{"value":"AI","text":"Anguilla"},{"value":"AQ","text":"Antarctica"},{"value":"AG","text":"Antigua and Barbuda"},{"value":"AR","text":"Argentina"},{"value":"AM","text":"Armenia"},{"value":"AW","text":"Aruba"},{"value":"AU","text":"Australia"},{"value":"AT","text":"Austria"},{"value":"AZ","text":"Azerbaijan"},{"value":"BS","text":"Bahamas"},{"value":"BH","text":"Bahrain"},{"value":"BD","text":"Bangladesh"},{"value":"BB","text":"Barbados"},{"value":"BY","text":"Belarus"},{"value":"BE","text":"Belgium"},{"value":"BZ","text":"Belize"},{"value":"BJ","text":"Benin"},{"value":"BM","text":"Bermuda"},{"value":"BT","text":"Bhutan"},{"value":"BO","text":"Bolivia"},{"value":"BQ","text":"Bonaire, Saint Eustatius and Saba"},{"value":"BA","text":"Bosnia and Herzegovina"},{"value":"BW","text":"Botswana"},{"value":"BV","text":"Bouvet Island"},{"value":"BR","text":"Brazil"},{"value":"IO","text":"British Indian Ocean Territory"},{"value":"BN","text":"Brunei Darussalam"},{"value":"BG","text":"Bulgaria"},{"value":"BF","text":"Burkina Faso"},{"value":"BI","text":"Burundi"},{"value":"KH","text":"Cambodia"},{"value":"CM","text":"Cameroon"},{"value":"CA","text":"Canada"},{"value":"IC","text":"Canary Islands"},{"value":"CV","text":"Cape Verde"},{"value":"KY","text":"Cayman Islands"},{"value":"CF","text":"Central African Republic"},{"value":"EA","text":"Ceuta and Melilla"},{"value":"TD","text":"Chad"},{"value":"CL","text":"Chile"},{"value":"CN","text":"China"},{"value":"CX","text":"Christmas Island"},{"value":"CC","text":"Cocos (Keeling) Islands"},{"value":"CO","text":"Colombia"},{"value":"KM","text":"Comoros"},{"value":"CD","text":"Congo, Democratic Republic of"},{"value":"CG","text":"Congo, Republic of"},{"value":"CK","text":"Cook Islands"},{"value":"CR","text":"Costa Rica"},{"value":"CI","text":"Cote d'Ivoire"},{"value":"HR","text":"Croatia/Hrvatska"},{"value":"CU","text":"Cuba"},{"value":"CW","text":"Cura\u00E7ao"},{"value":"CY","text":"Cyprus"},{"value":"CZ","text":"Czech Republic"},{"value":"DK","text":"Denmark"},{"value":"DJ","text":"Djibouti"},{"value":"DM","text":"Dominica"},{"value":"DO","text":"Dominican Republic"},{"value":"TL","text":"East Timor"},{"value":"EC","text":"Ecuador"},{"value":"EG","text":"Egypt"},{"value":"SV","text":"El Salvador"},{"value":"GQ","text":"Equatorial Guinea"},{"value":"ER","text":"Eritrea"},{"value":"EE","text":"Estonia"},{"value":"ET","text":"Ethiopia"},{"value":"FK","text":"Falkland Islands"},{"value":"FO","text":"Faroe Islands"},{"value":"FJ","text":"Fiji"},{"value":"FI","text":"Finland"},{"value":"FR","text":"France"},{"value":"GF","text":"French Guiana"},{"value":"PF","text":"French Polynesia"},{"value":"TF","text":"French Southern Territories"},{"value":"GA","text":"Gabon"},{"value":"GM","text":"Gambia"},{"value":"GE","text":"Georgia"},{"value":"DE","text":"Germany"},{"value":"GH","text":"Ghana"},{"value":"GI","text":"Gibraltar"},{"value":"GR","text":"Greece"},{"value":"GL","text":"Greenland"},{"value":"GD","text":"Grenada"},{"value":"GP","text":"Guadeloupe"},{"value":"GU","text":"Guam"},{"value":"GT","text":"Guatemala"},{"value":"GG","text":"Guernsey"},{"value":"GN","text":"Guinea"},{"value":"GW","text":"Guinea-Bissau"},{"value":"GY","text":"Guyana"},{"value":"HT","text":"Haiti"},{"value":"HM","text":"Heard and McDonald Islands"},{"value":"VA","text":"Holy See (City Vatican State)"},{"value":"HN","text":"Honduras"},{"value":"HK","text":"Hong Kong"},{"value":"HU","text":"Hungary"},{"value":"IS","text":"Iceland"},{"value":"IN","text":"India"},{"value":"ID","text":"Indonesia"},{"value":"IR","text":"Iran (Islamic Republic of)"},{"value":"IQ","text":"Iraq"},{"value":"IE","text":"Ireland"},{"value":"IM","text":"Isle of Man"},{"value":"IL","text":"Israel"},{"value":"IT","text":"Italy"},{"value":"JM","text":"Jamaica"},{"value":"JP","text":"Japan"},{"value":"JE","text":"Jersey"},{"value":"JO","text":"Jordan"},{"value":"KZ","text":"Kazakhstan"},{"value":"KE","text":"Kenya"},{"value":"KI","text":"Kiribati"},{"value":"KP","text":"Korea, Democratic People's Republic"},{"value":"KR","text":"Korea, Republic of"},{"value":"XK","text":"Kosovo"},{"value":"KW","text":"Kuwait"},{"value":"KG","text":"Kyrgyzstan"},{"value":"LA","text":"Lao People's Democratic Republic"},{"value":"LV","text":"Latvia"},{"value":"LB","text":"Lebanon"},{"value":"LS","text":"Lesotho"},{"value":"LR","text":"Liberia"},{"value":"LY","text":"Libya"},{"value":"LI","text":"Liechtenstein"},{"value":"LT","text":"Lithuania"},{"value":"LU","text":"Luxembourg"},{"value":"MO","text":"Macau"},{"value":"MK","text":"Macedonia"},{"value":"MG","text":"Madagascar"},{"value":"MW","text":"Malawi"},{"value":"MY","text":"Malaysia"},{"value":"MV","text":"Maldives"},{"value":"ML","text":"Mali"},{"value":"MT","text":"Malta"},{"value":"MH","text":"Marshall Islands"},{"value":"MQ","text":"Martinique"},{"value":"MR","text":"Mauritania"},{"value":"MU","text":"Mauritius"},{"value":"YT","text":"Mayotte"},{"value":"MX","text":"Mexico"},{"value":"FM","text":"Micronesia, Federal State of"},{"value":"MD","text":"Moldova, Republic of"},{"value":"MC","text":"Monaco"},{"value":"MN","text":"Mongolia"},{"value":"ME","text":"Montenegro"},{"value":"MS","text":"Montserrat"},{"value":"MA","text":"Morocco"},{"value":"MZ","text":"Mozambique"},{"value":"MM","text":"Myanmar (Burma)"},{"value":"NA","text":"Namibia"},{"value":"NR","text":"Nauru"},{"value":"NP","text":"Nepal"},{"value":"NL","text":"Netherlands"},{"value":"AN","text":"Netherlands Antilles (Deprecated)"},{"value":"NC","text":"New Caledonia"},{"value":"NZ","text":"New Zealand"},{"value":"NI","text":"Nicaragua"},{"value":"NE","text":"Niger"},{"value":"NG","text":"Nigeria"},{"value":"NU","text":"Niue"},{"value":"NF","text":"Norfolk Island"},{"value":"MP","text":"Northern Mariana Islands"},{"value":"NO","text":"Norway"},{"value":"OM","text":"Oman"},{"value":"PK","text":"Pakistan"},{"value":"PW","text":"Palau"},{"value":"PS","text":"Palestinian Territories"},{"value":"PA","text":"Panama"},{"value":"PG","text":"Papua New Guinea"},{"value":"PY","text":"Paraguay"},{"value":"PE","text":"Peru"},{"value":"PH","text":"Philippines"},{"value":"PN","text":"Pitcairn Island"},{"value":"PL","text":"Poland"},{"value":"PT","text":"Portugal"},{"value":"PR","text":"Puerto Rico"},{"value":"QA","text":"Qatar"},{"value":"RE","text":"Reunion Island"},{"value":"RO","text":"Romania"},{"value":"RU","text":"Russian Federation"},{"value":"RW","text":"Rwanda"},{"value":"BL","text":"Saint Barth\u00E9lemy"},{"value":"SH","text":"Saint Helena"},{"value":"KN","text":"Saint Kitts and Nevis"},{"value":"LC","text":"Saint Lucia"},{"value":"MF","text":"Saint Martin"},{"value":"VC","text":"Saint Vincent and the Grenadines"},{"value":"WS","text":"Samoa"},{"value":"SM","text":"San Marino"},{"value":"ST","text":"Sao Tome and Principe"},{"value":"SA","text":"Saudi Arabia"},{"value":"SN","text":"Senegal"},{"value":"RS","text":"Serbia"},{"value":"CS","text":"Serbia and Montenegro (Deprecated)"},{"value":"SC","text":"Seychelles"},{"value":"SL","text":"Sierra Leone"},{"value":"SG","text":"Singapore"},{"value":"SX","text":"Sint Maarten"},{"value":"SK","text":"Slovak Republic"},{"value":"SI","text":"Slovenia"},{"value":"SB","text":"Solomon Islands"},{"value":"SO","text":"Somalia"},{"value":"ZA","text":"South Africa"},{"value":"GS","text":"South Georgia"},{"value":"SS","text":"South Sudan"},{"value":"ES","text":"Spain"},{"value":"LK","text":"Sri Lanka"},{"value":"PM","text":"St. Pierre and Miquelon"},{"value":"SD","text":"Sudan"},{"value":"SR","text":"Suriname"},{"value":"SJ","text":"Svalbard and Jan Mayen Islands"},{"value":"SZ","text":"Swaziland"},{"value":"SE","text":"Sweden"},{"value":"CH","text":"Switzerland"},{"value":"SY","text":"Syrian Arab Republic"},{"value":"TW","text":"Taiwan"},{"value":"TJ","text":"Tajikistan"},{"value":"TZ","text":"Tanzania"},{"value":"TH","text":"Thailand"},{"value":"TG","text":"Togo"},{"value":"TK","text":"Tokelau"},{"value":"TO","text":"Tonga"},{"value":"TT","text":"Trinidad and Tobago"},{"value":"TN","text":"Tunisia"},{"value":"TR","text":"Turkey"},{"value":"TM","text":"Turkmenistan"},{"value":"TC","text":"Turks and Caicos Islands"},{"value":"TV","text":"Tuvalu"},{"value":"UG","text":"Uganda"},{"value":"UA","text":"Ukraine"},{"value":"AE","text":"United Arab Emirates"},{"value":"GB","text":"United Kingdom (GB)"},{"value":"US","text":"United States"},{"value":"UY","text":"Uruguay"},{"value":"UM","text":"US Minor Outlying Islands"},{"value":"UZ","text":"Uzbekistan"},{"value":"VU","text":"Vanuatu"},{"value":"VE","text":"Venezuela"},{"value":"VN","text":"Vietnam"},{"value":"VG","text":"Virgin Islands (British)"},{"value":"VI","text":"Virgin Islands (USA)"},{"value":"WF","text":"Wallis and Futuna"},{"value":"EH","text":"Western Sahara"},{"value":"YE","text":"Yemen"},{"value":"ZM","text":"Zambia"},{"value":"ZW","text":"Zimbabwe"}]
			,	csList : ["US","CA","MX"]
			,	states : {
						"US" : [{"value":"AL","text":"Alabama"},{"value":"AK","text":"Alaska"},{"value":"AZ","text":"Arizona"},{"value":"AR","text":"Arkansas"},{"value":"AA","text":"Armed Forces Americas"},{"value":"AE","text":"Armed Forces Europe"},{"value":"AP","text":"Armed Forces Pacific"},{"value":"CA","text":"California"},{"value":"CO","text":"Colorado"},{"value":"CT","text":"Connecticut"},{"value":"DE","text":"Delaware"},{"value":"DC","text":"District of Columbia"},{"value":"FL","text":"Florida"},{"value":"GA","text":"Georgia"},{"value":"HI","text":"Hawaii"},{"value":"ID","text":"Idaho"},{"value":"IL","text":"Illinois"},{"value":"IN","text":"Indiana"},{"value":"IA","text":"Iowa"},{"value":"KS","text":"Kansas"},{"value":"KY","text":"Kentucky"},{"value":"LA","text":"Louisiana"},{"value":"ME","text":"Maine"},{"value":"MD","text":"Maryland"},{"value":"MA","text":"Massachusetts"},{"value":"MI","text":"Michigan"},{"value":"MN","text":"Minnesota"},{"value":"MS","text":"Mississippi"},{"value":"MO","text":"Missouri"},{"value":"MT","text":"Montana"},{"value":"NE","text":"Nebraska"},{"value":"NV","text":"Nevada"},{"value":"NH","text":"New Hampshire"},{"value":"NJ","text":"New Jersey"},{"value":"NM","text":"New Mexico"},{"value":"NY","text":"New York"},{"value":"NC","text":"North Carolina"},{"value":"ND","text":"North Dakota"},{"value":"OH","text":"Ohio"},{"value":"OK","text":"Oklahoma"},{"value":"OR","text":"Oregon"},{"value":"PA","text":"Pennsylvania"},{"value":"PR","text":"Puerto Rico"},{"value":"RI","text":"Rhode Island"},{"value":"SC","text":"South Carolina"},{"value":"SD","text":"South Dakota"},{"value":"TN","text":"Tennessee"},{"value":"TX","text":"Texas"},{"value":"UT","text":"Utah"},{"value":"VT","text":"Vermont"},{"value":"VA","text":"Virginia"},{"value":"WA","text":"Washington"},{"value":"WV","text":"West Virginia"},{"value":"WI","text":"Wisconsin"},{"value":"WY","text":"Wyoming"},{"value":"AS","text":"American Samoa"},{"value":"GU","text":"Guam"},{"value":"MP","text":"Northern Mariana Islands"},{"value":"UM","text":"United States Minor Outlying Islands"},{"value":"VI","text":"Virgin Islands"}]
					,	"CA" : [{"value":"AB","text":"Alberta"},{"value":"BC","text":"British Columbia"},{"value":"MB","text":"Manitoba"},{"value":"NB","text":"New Brunswick"},{"value":"NL","text":"Newfoundland"},{"value":"NT","text":"Northwest Territories"},{"value":"NS","text":"Nova Scotia"},{"value":"NU","text":"Nunavut"},{"value":"ON","text":"Ontario"},{"value":"PE","text":"Prince Edward Island"},{"value":"QC","text":"Quebec"},{"value":"SK","text":"Saskatchewan"},{"value":"YT","text":"Yukon"}]
					,	"MX" : [{"value":"AG","text":"Aguascalientes"},{"value":"BC","text":"Baja California"},{"value":"BS","text":"Baja California Sur"},{"value":"CM","text":"Campeche"},{"value":"CS","text":"Chiapas"},{"value":"CH","text":"Chihuahua"},{"value":"CO","text":"Coahuila"},{"value":"CL","text":"Colima"},{"value":"DF","text":"Distrito Federal"},{"value":"DG","text":"Durango"},{"value":"GT","text":"Guanajuanto"},{"value":"GR","text":"Guerrero"},{"value":"HG","text":"Hidalgo"},{"value":"JA","text":"Jalisco"},{"value":"MX","text":"Mexico"},{"value":"MI","text":"Michoacan"},{"value":"MO","text":"Morelos"},{"value":"NA","text":"Nayarit"},{"value":"NL","text":"Nuevo Leon"},{"value":"OA","text":"Oaxaca"},{"value":"PU","text":"Puebla"},{"value":"QT","text":"Queretaro"},{"value":"QR","text":"Quintana Roo"},{"value":"SL","text":"San Luis Potosi"},{"value":"SI","text":"Sinaloa"},{"value":"SO","text":"Sonora"},{"value":"TB","text":"Tabasco"},{"value":"TM","text":"Tamaulipas"},{"value":"TL","text":"Tlaxcala"},{"value":"VE","text":"Veracruz"},{"value":"YU","text":"Yucatan"},{"value":"ZA","text":"Zacatecas"}]
				}
			
			// placeholders: <RECTYPE>  successfully saved
			,	successMsg : "<div id=\"div__alert\" align=\"center\" width=\"100%\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#DAEBD5\"><tbody><tr><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_tl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_tr.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img pt-src=\"/images/icons/messagebox/icon_msgbox_confirmation.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><b>Confirmation:</b> <RECTYPE> successfully saved</td></tr><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_bl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_br.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>"
			// placeholders: <RECORD> <ERROR_CODE> <ERROR_DETAIL>  failed to save with error
			,	failureMsg : "<div id=\"div__alert\" align=\"center\" width=\"100%\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFAD9F\"><tbody><tr><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_tl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_tr.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img pt-src=\"/images/icons/messagebox/icon_msgbox_error.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><b>Error:</b> <RECORD> could not be Saved<br /><br />[<ERROR_CODE>]<br /><i><ERROR_DETAIL></i></td></tr><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_bl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_br.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>"
			// placeholders: <MESSAGE>
			,	failureMsgAlt : "<div id=\"div__alert\" align=\"center\" width=\"100%\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFAD9F\"><tbody><tr><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_tl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_tr.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img pt-src=\"/images/icons/messagebox/icon_msgbox_error.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><MESSAGE></td></tr><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_bl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_br.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>"
			// placeholders: <MESSAGE>
			,	warningMsg : "<div id=\"div__alert\" align=\"center\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFF2CC\"><tbody><tr><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_tl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_tr.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img pt-src=\"/images/icons/messagebox/icon_msgbox_warning.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><b>WARNING:</b> <MESSAGE></td></tr><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_bl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_br.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>"
			// placeholders: <MESSAGE>
			,	warningMsgAlt : "<div id=\"div__alert\" align=\"center\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFF2CC\"><tbody><tr><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_tl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_tr.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img pt-src=\"/images/icons/messagebox/icon_msgbox_warning.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><MESSAGE></td></tr><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_bl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/messagebox/msgbox_corner_br.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>"
			// placeholders: <MESSAGE>
			,	successMsgAction : "<div id=\"div__alert\" align=\"center\" width=\"100%\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#DAEBD5\"><tbody><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img pt-src=\"/images/icons/messagebox/icon_msgbox_confirmation.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><MESSAGE></td></tr><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>"
			// placeholders: <MESSAGE> <ERROR_CODE> <ERROR_DETAIL>
			,	failureMsgAction : "<div id=\"div__alert\" align=\"center\" width=\"100%\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFAD9F\"><tbody><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img pt-src=\"/images/icons/messagebox/icon_msgbox_error.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><b>Error:</b> <MESSAGE><br /><br />[<ERROR_CODE>]<br /><i><ERROR_DETAIL></i></td></tr><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img pt-src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img pt-src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>"
			
			,	lineBreak :			/\r\n|\r|\n/
			
			,	nsDateRegEx :		new RegExp("^(1[0-2]|[1-9])/(3[01]|[12][0-9]|[1-9])/[12][0-9]{3}$")
			
			,	rType : {
						so :		"salesorder"
					,	iff :		"itemfulfillment"
					,	inv :		"invoice"
					,	cs :		"cashsale"
					,	est :		"estimate"
					,	op :		"opportunity"
					,	rf :		"customerrefund"
					,	cdep :		"customerdeposit"
					,	dpap :		"depositapplication"
					,	dep :		"deposit"
					,	cm :		"creditmemo"
					,	pay :		"customerpayment"
					,	adj :		"inventoryadjustment"
					,	item :		"item"
					,	invi :		"inventoryitem"
					,	ninvi :		"noninventoryitem"
					,	asmb :		"assemblyitem"
					,	kit :		"kititem"
					,	cus :		"customer"
					,	lead :		"lead"
					,	pros : 		"prospect"
					,	prtr :	 	"partner"
					,	vend :		"vendor"
					,	con :		"contact"
					,	je :		"journalentry"
					,	'case' :	"supportcase"
				}
			
			,	altType : {
						so : 		"SalesOrd"
				}
			
			,	altRecStatus : {
						soPendAprvl : 	"A"
					,	soPendFulfill :	"B"
					,	soCancelled :	"C"
					,	soPartFulfill :	"D"
					,	soBillFulfill : "E"
					,	soPendBill : 	"F"
					,	soBilled :		"G"
					,	soClosed :		"H"
				}
			
			,	recStatus : {
						soPendAprvl : 	"SalesOrd:A"
					,	soPendFulfill :	"SalesOrd:B"
					,	soCancelled :	"SalesOrd:C"
					,	soPartFulfill :	"SalesOrd:D"
					,	soBillFulfill : "SalesOrd:E"
					,	soPendBill : 	"SalesOrd:F"
					,	soBilled :		"SalesOrd:G"
					,	soClosed :		"SalesOrd:H"
				}
			
			,	textRecStatus : {
						soPendAprvl : 	"pendingApproval"
					,	soPendFulfill :	"pendingFulfillment"
					,	soCancelled :	"cancelled"
					,	soPartFulfill :	"partiallyFulfilled"
					,	soBillFulfill : "pendingBillingPartFulfilled"
					,	soPendBill : 	"pendingBilling"
					,	soBilled :		"fullyBilled"
					,	soClosed :		"closed"
				}
			
			,	dynRec : {
						recordmode : 	"dynamic"
				}
			
			,	dynRecTrig : {
						recordmode : 			"dynamic"
					,	enablefieldtriggers : 	true
				}
			
			,	itemType : [
						{ id : "assemblyitem"				, type : "Assembly"		, name : "Build/Assembly Item" }
					,	{ id : "descriptionitem"			, type : "Description"	, name : "Description" }
					,	{ id : "discountitem"				, type : "Discount"		, name : "Discount Item" }
					,	{ id : "downloaditem"				, type : "DwnLdItem"	, name : "Download Item" }
					,	{ id : ""							, type : "EndGroup"		, name : "Item Group (end)" }  // valid only on transaction item lines
					,	{ id : "giftcertificateitem"		, type : "GiftCert"		, name : "Gift Certificate" }
					,	{ id : "itemgroup"					, type : "Group"		, name : "Item Group (start)" }
					,	{ id : "inventoryitem"				, type : "InvtPart"		, name : "Inventory Item" }
					,	{ id : "kititem"					, type : "Kit"			, name : "Kit Item" }
					,	{ id : "lotnumberedassemblyitem"	, type : ""				, name : "Lot Numbered Build/Assembly Item" }
					,	{ id : "lotnumberedinventoryitem"	, type : ""				, name : "Lot Numbered Inventory Item" }
					,	{ id : "markupitem"					, type : "Markup"		, name : "Markup" }
					,	{ id : "noninventoryitem"			, type : "NonInvtPart"	, name : "Non-inventory Item" }
					,	{ id : "otherchargeitem"			, type : "OthCharge"	, name : "Other Charge" }
					,	{ id : "paymentitem"				, type : "Payment"		, name : "Payment" }
					,	{ id : "serializedassemblyitem"		, type : ""				, name : "Serialized Build/Assembly Item" }
					,	{ id : "serializedinventoryitem"	, type : ""				, name : "Serialized Inventory Item" }
					,	{ id : "serviceitem"				, type : "Service"		, name : "Service" }
					,	{ id : "subtotalitem"				, type : "Subtotal"		, name : "Subtotal" }
				]
			
			,	fileType : {
						txt :		"PLAINTEXT"
					,	xml :		"XMLDOC"
					,	csv :		"CSV"
					,	config :	"CONFIG"
					,	cfg :		"CONFIG"
					,	html :		"HTMLDOC"
					,	js :		"JAVASCRIPT"
					,	json :		"JSON"
					,	ps :		"POSTSCRIPT"
					,	css :		"STYLESHEET"
					,	pdf :		"PDF"
					,	xsd :		"XSD"
					,	scss :		"SCSS"
				}
			
			,	runtimeContexts : {
						ACTION :				"ACTION"
					,	ADVANCEDREVREC :		"ADVANCEDREVREC"
					,	BANKCONNECTIVITY :		"BANKCONNECTIVITY"
					,	BANKSTATEMENTPARSER :	"BANKSTATEMENTPARSER"
					,	BUNDLE_INSTALLATION :	"BUNDLEINSTALLATION"
					,	CLIENT :				"CLIENT"
					,	CONSOLRATEADJUSTOR :	"CONSOLRATEADJUSTOR"
					,	CSV_IMPORT :			"CSVIMPORT"
					,	CUSTOMGLLINES :			"CUSTOMGLLINES"
					,	CUSTOM_MASSUPDATE :		"CUSTOMMASSUPDATE"
					,	DATASETBUILDER :		"DATASETBUILDER"
					,	DEBUGGER :				"DEBUGGER"
					,	EMAIL_CAPTURE :			"EMAILCAPTURE"
					,	FICONNECTIVITY :		"FICONNECTIVITY"
					,	FIPARSER :				"FIPARSER"
					,	MAP_REDUCE :			"MAPREDUCE"
					,	NONE :					"NONE"
					,	PAYMENTGATEWAY :		"PAYMENTGATEWAY"
					,	PAYMENTPOSTBACK :		"PAYMENTPOSTBACK"
					,	PLATFORMEXTENSION :		"PLATFORMEXTENSION"
					,	PORTLET :				"PORTLET"
					,	PROMOTIONS :			"PROMOTIONS"
					,	RECORDACTION :			"RECORDACTION"
					,	RESTLET :				"RESTLET"
					,	REST_WEBSERVICES :		"RESTWEBSERVICES"
					,	SCHEDULED :				"SCHEDULED"
					,	SDF_INSTALLATION :		"SDFINSTALLATION"
					,	SHIPPING_PARTNERS :		"SHIPPINGPARTNERS"
					,	SUITELET :				"SUITELET"
					,	TAX_CALCULATION :		"TAXCALCULATION"
					,	USEREVENT :				"USEREVENT"
					,	USER_INTERFACE :		"USERINTERFACE"
					,	WEBAPPLICATION :		"WEBAPPLICATION"
					,	WEBSERVICES :			"WEBSERVICES"
					,	WEBSTORE :				"WEBSTORE"
					,	WORKBOOKBUILDER :		"WORKBOOKBUILDER"
					,	WORKFLOW :				"WORKFLOW"
				}
		};
		
		function isEmpty(value) {
			return (value === null || value === undefined || value === "");
		}
		
		function isInArray(value, arr, caseInsensitive) {
			var match = false;
			var ciValue = value;
			
			if (isEmpty(arr)) {
				return match;
			} else if (!util.isArray(arr)) {
				return match;
			} else if (arr.length === 0) {
				return match;
			}
			
			if (caseInsensitive && util.isString(value)) {
				try { ciValue = value.toUpperCase(); } catch (err) { /* do nothing */ }
			}
			
			arr.forEach(function(el) {
				if (el === ciValue && !match) {
					match = true;
				}
			});
			
			return match;
		}
		
		return {
				tools :			TOOLS
			,	obj :			OBJ
			,	url :			URL
			,	time :			TIME
			,	log :			LOGGING
			,	M :				MATH
			,	NSFeatures :	FEATURES
			,	lib :			LIB
			,	csv :			JQ_CSV
			,	modal :			MODAL
			,	UI :			UI
		};
		
	});

////////////////////////////
//NUMBER prototypes
////////////////////////////
//region . NUMBER prototypes

/**
 * @param {Number} c Decimal places to display [optional] (default: 2)
 * @param {String} d Decimal separator [optional] (default: . )
 * @param {Date} t Thousands separator [optional] (default: , )
 * @returns {String} Currency amount formatted into string
 */
Object.defineProperty(Number.prototype, 'NG_formatMoney',
	{
			value : function(c, d, t) {
				c = isNaN(c = Math.abs(c)) ? 2 : c;
				d = d === undefined ? "." : d;
				t = t === undefined ? "," : t;
				var n = this,
					s = n < 0 ? "-" : "",
					i = parseInt(n = Math.abs(+n || 0).toFixed(c)) + "",
					j = (j = i.length) > 3 ? j % 3 : 0;
				return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
			}
		,	configurable : true
		,	writable : true
	}
);
//endregion

////////////////////////////
//STRING prototypes
////////////////////////////
//region . STRING prototypes

Object.defineProperty(String.prototype, 'NG_Format',
	{
			value : function() {
				var args = arguments;
				return this.replace(/{(\d+)}/g, function(match, number) {
					return typeof args[number] != 'undefined' ? args[number] : "null"; //match;
				});
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(String.prototype, 'NG_paddingLeft',
	{
			value : function(paddingValue) {
				return String(paddingValue + this).slice(-paddingValue.length);
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(String.prototype, 'NG_paddingRight',
	{
			value : function(paddingValue) {
				return String(this + paddingValue).substr(0, paddingValue.length);
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(String.prototype, 'NG_toProperCase',
	{
			value : function() {
				return this.replace(/([^\W_]+[^\s-]*) */g, function(txt){return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();});
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(String.prototype, 'NG_toUpperRoman',
	{
			value : function() {
				return this.replace(/(M{0,4}(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3}))/g, function(txt){return txt.toUpperCase();});
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(String.prototype, 'NG_toTitleCase',
	{
			value : function() {
				var i, j, str, lowers, uppers;
				str = this.replace(/([^\W_]+[^\s-]*) */g, function(txt) {
					return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
				});
				
				// Certain minor words should be left lowercase unless
				// they are the first or last words in the string
				lowers = ['A', 'An', 'The', 'And', 'But', 'Or', 'For', 'Nor', 'As', 'At', 'By', 'For', 'From', 'In', 'Into', 'Near', 'Of', 'On', 'Onto', 'To', 'With'];
				for (i = 0, j = lowers.length; i < j; i++) {
					str = str.replace(new RegExp('\\s' + lowers[i] + '\\s', 'g'), function(txt) {
						return txt.toLowerCase();
					});
				}
				
				// Certain words such as initialisms or acronyms should be left uppercase
				uppers = ['Id', 'Tv'];
				for (i = 0, j = uppers.length; i < j; i++) {
					str = str.replace(new RegExp('\\b' + uppers[i] + '\\b', 'g'),
						uppers[i].toUpperCase());
				}
				
				return str;
			}
		,	configurable : true
		,	writable : true
	}
);

/**
 * <AUTHOR> https://stackoverflow.com/questions/21647928/javascript-unicode-string-to-hex
 * @returns {string}
 */
Object.defineProperty(String.prototype, 'hexEncode',
	{
			value : function() {
				var hex, i;
				
				var result = "";
				for (i = 0; i < this.length; i++) {
					hex = this.charCodeAt(i).toString(16);
					result += ("000"+hex).slice(-4);
				}
				
				return result;
			}
		,	configurable : true
		,	writable : true
	}
);

/**
 * @author: McDowell, https://stackoverflow.com/questions/21647928/javascript-unicode-string-to-hex
 * @returns {string}
 */
Object.defineProperty(String.prototype, 'hexDecode',
	{
			value : function() {
				var j;
				var hexes = this.match(/.{1,4}/g) || [];
				var back = "";
				for(j = 0; j < hexes.length; j++) {
					back += String.fromCharCode(parseInt(hexes[j], 16));
				}
				
				return back;
			}
		,	configurable : true
		,	writable : true
	}
);
//endregion

////////////////////////////
//ARRAY prototypes
////////////////////////////
//region . ARRAY prototypes

Object.defineProperty(Array.prototype, 'NG_intersect',
	{
			value : function(target) {
				var sorted_a = this.concat().sort();
				var sorted_b = target.concat().sort();
				var common = [];
				var a_i = 0;
				var b_i = 0;
				
				while (a_i < this.length && b_i < target.length) {
					if (sorted_a[a_i] === sorted_b[b_i]) {
						common.push(sorted_a[a_i]);
						a_i++;
						b_i++;
					} else if(sorted_a[a_i] < sorted_b[b_i]) {
						a_i++;
					} else {
						b_i++;
					}
				}
				return common;
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(Array.prototype, 'NG_differential',
	{
			value : function(comp) {
				return this.filter(function(el) {
					return comp.indexOf(el) < 0;
				});
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(Array.prototype, 'NG_contains',
	{
			value : function(value, strict, caseInsensitive) {
				var match = false;
				var ciValue = value;
				strict = strict || false;
				caseInsensitive = caseInsensitive || false;
				
				if (caseInsensitive) {
					try { ciValue = value.toUpperCase(); } catch (err) { /* do nothing*/ }
				}
				
				for (var i = 0; i < this.length; i++) {
					if (caseInsensitive) {
						try {
							if ((strict && ciValue === this[i].toUpperCase()) || (!strict && ciValue === this[i].toUpperCase())) {
								match = true;
								break;
							}
						} catch (err) {
							if ((strict && value === this[i]) || (!strict && value === this[i])) {
								match = true;
								break;
							}
						}
					} else {
						if ((strict && value === this[i]) || (!strict && value === this[i])) {
							match = true;
							break;
						}
					}
				}
				return match;
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(Array.prototype, 'NG_find',
	{
			value : function(predicate) {
				if (NG_IsEmpty(predicate)) { return null; }
				for (var i = 0; i < this.length; i++) {
					if (predicate(this[i])) {
						return this[i];
					}
				}
				
				return null;
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(Array.prototype, 'NG_findAlt',
	{
			value : function(property, value) {
				if (NG_IsEmpty(property)) { return null; }
				for (var i = 0; i < this.length; i++) {
					if (this[i][property] === value) {
						return this[i];
					}
				}
				
				return null;
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(Array.prototype, 'NG_findIndex',
	{
			value : function(predicate) {
				if (NG_IsEmpty(predicate)) { return -1; }
				for (var i = 0; i < this.length; i++) {
					if (predicate(this[i])) {
						return i;
					}
				}
				
				return -1;
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(Array.prototype, 'NG_findIndexAlt',
	{
			value : function(property, value) {
				if (NG_IsEmpty(property)) { return -1; }
				for (var i = 0; i < this.length; i++) {
					if (this[i][property] === value) {
						return i;
					}
				}
				
				return -1;
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(Array.prototype, 'NG_findAll',
	{
			value : function(predicate) {
				var results =  [];
				if (NG_IsEmpty(predicate)) { return results; }
				for (var i = 0; i < this.length; i++) {
					if (predicate(this[i])) {
						results.push(this[i]);
					}
				}
				
				return results;
			}
		,	configurable : true
		,	writable : true
	}
);

Object.defineProperty(Array.prototype, 'NG_findAllIndex',
	{
			value : function(predicate) {
				var results =  [];
				if (NG_IsEmpty(predicate)) { return results; }
				for (var i = 0; i < this.length; i++) {
					if (predicate(this[i])) {
						results.push(i);
					}
				}
				
				return results;
			}
		,	configurable : true
		,	writable : true
	}
);

if (!Array.prototype.find) {
	Object.defineProperty(Array.prototype, 'find',
		{
				value: function(predicate) {
					if (NG_IsEmpty(this)) {
						throw new TypeError('"this" is null or not defined');
					}
					if (NG_IsEmpty(predicate)) {
						throw new TypeError('predicate is null or not defined');
					}
					var o = Object(this);
					var len = o.length >>> 0;
					if (typeof predicate !== 'function') {
						throw new TypeError('predicate must be a function');
					}
					var thisArg = arguments[1];
					var k = 0;
					while (k < len) {
						var kValue = o[k];
						if (predicate.call(thisArg, kValue, k, o)) { return kValue; }
						k++;
					}
					return undefined;
				}
			,	configurable: true
			,	writable: true
		}
	);
}

if (!Array.prototype.findIndex) {
	Object.defineProperty(Array.prototype, 'findIndex',
		{
				value: function(predicate) {
					if (NG_IsEmpty(this)) {
						throw new TypeError('"this" is null or not defined');
					}
					if (NG_IsEmpty(predicate)) {
						throw new TypeError('predicate is null or not defined');
					}
					var o = Object(this);
					var len = o.length >>> 0;
					if (typeof predicate !== 'function') {
						throw new TypeError('predicate must be a function');
					}
					var thisArg = arguments[1];
					var k = 0;
					while (k < len) {
						var kValue = o[k];
						if (predicate.call(thisArg, kValue, k, o)) { return k; }
						k++;
					}
					return -1;
				}
			,	configurable: true
			,	writable: true
		}
	);
}

if (!Array.prototype.includes) {
	Object.defineProperty(Array.prototype, 'includes',
		{
				value: function(searchElement, fromIndex) {
					if (this == null) {
						throw new TypeError('"this" is null or not defined');
					}
					
					const o = Object(this);
					// tslint:disable-next-line:no-bitwise
					const len = o.length >>> 0;
					
					if (len === 0) {
						return false;
					}
					// tslint:disable-next-line:no-bitwise
					const n = fromIndex | 0;
					var k = Math.max((n >= 0 ? n : len - Math.abs(n)), 0);
					
					while (k < len) {
						if (o[k] === searchElement) {
							return true;
						}
						k++;
					}
					return false;
				}
			,	configurable: true
			,	writable: true
		}
	);
}
//endregion

////////////////////////////
//DATE prototypes
////////////////////////////
//region . DATE prototypes

Object.defineProperty(Date.prototype, 'toNSString',
	{
			value : function(format) {
				format = format || "date";
				var dateString = "", h, ap;
				if (format === "date") {
					dateString = "{0}/{1}/{2}".NG_Format((this.getMonth() + 1).toFixed(0),this.getDate().toFixed(0),this.getFullYear().toFixed(0));
				} else if (format === "datetime") {
					h = this.getHours() > 12 ? (this.getHours() - 12).toFixed(0) : this.getHours().toFixed(0);
					ap = this.getHours() > 12 ? "pm" : "am";
					dateString = "{0}/{1}/{2} {3}:{4} {5}".NG_Format((this.getMonth() + 1).toFixed(0),this.getDate().toFixed(0),this.getFullYear().toFixed(0),h,(this.getMinutes().toFixed(0)).NG_paddingLeft("00"),ap);
				} else if (format === "datetimetz") {
					h = this.getHours() > 12 ? (this.getHours() - 12).toFixed(0) : this.getHours().toFixed(0);
					ap = this.getHours() > 12 ? "pm" : "am";
					dateString = "{0}/{1}/{2} {3}:{4}:{5} {6}".NG_Format((this.getMonth() + 1).toFixed(0),this.getDate().toFixed(0),this.getFullYear().toFixed(0),h,(this.getMinutes().toFixed(0)).NG_paddingLeft("00"),(this.getSeconds().toFixed(0)).NG_paddingLeft("00"),ap);
				} else if (format === "24H") {
					dateString = "{0}/{1}/{2} {3}:{4}".NG_Format((this.getMonth() + 1).toFixed(0),this.getDate().toFixed(0),this.getFullYear().toFixed(0),(this.getHours().toFixed(0)).NG_paddingLeft("00"),(this.getMinutes().toFixed(0)).NG_paddingLeft("00"));
				} else if (format === "24Htz") {
					dateString = "{0}/{1}/{2} {3}:{4}:{5}".NG_Format((this.getMonth() + 1).toFixed(0),this.getDate().toFixed(0),this.getFullYear().toFixed(0),this.getHours().toFixed(0).NG_paddingLeft("00"),(this.getMinutes().toFixed(0)).NG_paddingLeft("00"),(this.getSeconds().toFixed(0)).NG_paddingLeft("00"));
				}
				
				return dateString;
			}
		,	configurable : true
		,	writable : true
	}
);
//endregion

////////////////////////////
//OBJECT prototypes
////////////////////////////
//region . OBJECT prototypes

if (!Object.entries) {
	Object.entries = function(obj, keepKey) {
		var ownProps = Object.keys(obj),
			i = ownProps.length,
			resArray = new Array(i); // preallocate the Array
		keepKey = keepKey || false;
		while (i--) {
			if (keepKey) {
				resArray[i] = [ownProps[i], obj[ownProps[i]]];
			} else {
				resArray[i] = obj[ownProps[i]];
			}
		}
		
		return resArray;
	};
}
//endregion

////////////////////////////
//REGEXP prototypes
////////////////////////////
//region . REGEXP prototypes

Object.defineProperty(RegExp.prototype, 'escape',
	{
			value : function(s) {
				return s.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
			}
		,	configurable : true
		,	writable : true
	});
//endregion

////////////////////////////
//OTHER
////////////////////////////

function NG_IsEmpty(value) {
	return (value === null || value === undefined || value === "");
}
