define(['N/search', 'N/ui/serverWidget', './newgen.library.v2', './newgen.library.cs'],

function(search, widget, NG, csLib) {
	
	var S_MOVE_IN_TYPE = csLib.settings.DefaultShowMoveInDateType;
	var S_MOVE_OUT_TYPE = csLib.settings.DefaultShowMoveOutDateType;
	var E_MOVE_IN_TYPE = csLib.settings.DefaultExhibMoveInDateType;
	var E_MOVE_OUT_TYPE = csLib.settings.DefaultExhibMoveOutDateType;
	var S_DATE_TYPE = csLib.settings.DefaultShowDateType;

	var _DAY_OF_WEEK = [
		"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"
	];

	var _SHADED_HEX = "#F0F0F0";
	var _BODY_XML = '<body size="Letter" header="header" header-height="235px" footer="footer" footer-height="15px" style="margin: 0.25in; padding: 0;">\n';
	var _BODY_XML_REDUCED = '<body size="Letter" header="header" header-height="125px" footer="footer" footer-height="15px" style="margin: 0.25in; padding: 0;">\n';

	function xml_StyleSheet() {
		var xml = '';
		xml += '<style>';
		xml += 'body { font-family:Helvetica; }';
		xml += '.h_cell_a { white-space:nowrap; overflow:hidden; text-align:left; }';
		xml += '.h_cell_a p { font-size:16px; width:100%; text-align:left; color:#000000; }';
		xml += '.h_cell_b { white-space:nowrap; text-align:left; }';
		xml += '.h_cell_b p { font-size:12px; width:100%; text-align:left; color:#333333; }';
		xml += '.h_cell_c { text-align:left; }';
		xml += '.h_cell_c p { font-size:12px; width:100%; text-align:left; color:#000000; vertical-align:top; }';
		xml += '.h_cell_d { text-align:left; }';
		xml += '.h_cell_d p { font-size:12px; width:100%; text-align:left; color:#000000; vertical-align:top; }';
		xml += '.h_cell_e { white-space:nowrap; text-align:right; }';
		xml += '.h_cell_e p { font-size:12px; width:100%; text-align:right; color:#333333; }';
		xml += '.h_cell_f { white-space:nowrap; text-align:center; }';
		xml += '.h_cell_f p { font-size:12px; width:100%; text-align:center; color:#333333; }';
		xml += '.h_cell_g { white-space:nowrap; overflow:hidden; text-align:left; }';
		xml += '.h_cell_g p { font-size:14px; width:100%; text-align:left; color:#000000; }';
		xml += '.hdr_cell_a { color:#000000; background-color:#DDDDDD; padding:3px; }';
		xml += '.hdr_cell_a p { width: 100%; text-align:center; }';
		xml += '.hdr_cell_b { width:90%; color:#000000; background-color:#DDDDDD; }';
		xml += '.hdr_cell_c { width:45%; color:#000000; background-color:#DDDDDD; }';
		xml += '.hdr_cell_d { width:10%; color:#000000; background-color:#DDDDDD; }';
		xml += '.hdr_cell_d p { width: 100%; text-align:right; }';
		xml += '</style>\n';
		return xml;
	}

	function xml_PageHeader(tableData, reportTitle) {
		var s_in_DOW = !NG.tools.isEmpty(tableData['show_in']) ? _DAY_OF_WEEK[NG.time.stringToDate(tableData['show_in']).getDay()] : "";
		var s_out_DOW = !NG.tools.isEmpty(tableData['show_out']) ? _DAY_OF_WEEK[NG.time.stringToDate(tableData['show_out']).getDay()] : "";
		var e_in_DOW = !NG.tools.isEmpty(tableData['exhb_in']) ? _DAY_OF_WEEK[NG.time.stringToDate(tableData['exhb_in']).getDay()] : "";
		var e_out_DOW = !NG.tools.isEmpty(tableData['exhb_out']) ? _DAY_OF_WEEK[NG.time.stringToDate(tableData['exhb_out']).getDay()] : "";
		var s_s_DOW = !NG.tools.isEmpty(tableData['show_s']) ? _DAY_OF_WEEK[NG.time.stringToDate(tableData['show_s']).getDay()] : "";
		var s_e_DOW = !NG.tools.isEmpty(tableData['show_e']) ? _DAY_OF_WEEK[NG.time.stringToDate(tableData['show_e']).getDay()] : "";
		
		var xml = '';
		xml += '<macro id="header">';
		xml += '<div style="position:absolute;" x="0%" y="0%"><table table-layout="fixed" border="0" style="width:720px; margin:0; padding:0; overflow:hidden;" cellmargin="0" cellpadding="0">';
		if (!NG.tools.isEmpty(csLib.settings.WorkOrderImageURL)) {
			xml += '<tr><td style="overflow:hidden; height:0.93in;"><img src="{0}" width="2.5in" height="0.50in" style="margin:0; padding:0;" /></td>'.NG_Format(csLib.settings.WorkOrderImageURL); //  height:0.53in;
		} else {
			var nsLogo = "{0}/images/logos/netsuite-oracle.svg".NG_Format(csLib.settings.NSSystemDomain);
			xml += '<tr><td style="overflow:hidden; height:0.93in;"><img src="{0}" width="2.5in" height="0.50in" style="margin:0; padding:0;" /></td>'.NG_Format(nsLogo); //  height:0.53in;
		}
		xml += '<td style="width:55%;"><p style="width:100%; align:left; vertical-align:top;"><span style="font-size:24px;">{0}</span><br />'.NG_Format(reportTitle);
		xml += '{ADDITIONAL_A}';
		xml += '</p>';
		xml += '</td></tr>';
		xml += '</table></div>';
		
		xml += '<div style="background-color:#DDDDDD; border-bottom: 2px #AAAAAA; position:absolute;" x="0%" y="0.95in">';
		
		xml += '<table table-layout="fixed" border="0" style="width:720px; margin:3px; padding:0; overflow:hidden" cellmargin="0" cellpadding="0">';
		xml += '<tr><td width="100%" class="h_cell_a"><p><b>{0}</b></p></td></tr>'.NG_Format(tableData['name']);
		xml += '<tr><td class="h_cell_a"><p><b>{0}</b></p></td></tr>'.NG_Format(tableData['facility_text']);
		xml += '</table>';
		xml += '<hr style="width:100%; color:DDDDDD; background-color:#DDDDDD; border-top: 2px dashed #AAAAAA;" />';
		xml += '<table table-layout="fixed" border="0" style="width:720px; margin:3px; padding:0; overflow:hidden;" cellmargin="0" cellpadding="0">';
		
		xml += '<tr><td width="25%" class="h_cell_b"><p>Account Manager :<br />&nbsp;</p></td><td width="25%" class="h_cell_c"><p>{0}</p></td><td width="22%" class="h_cell_b"><p>Exhibitor Move-In/Out :<br />&nbsp;</p></td><td width="28%" class="h_cell_c"><p>{1} {2} - {3} {4}</p></td></tr>'.NG_Format(
				tableData['conv_srv_mgr'], e_in_DOW, tableData['exhb_in'] || "N/A", e_out_DOW, tableData['exhb_out'] || "N/A"
			);
		xml += '<tr><td class="h_cell_b"><p>Exhibitor Service Manager :<br />&nbsp;</p></td><td class="h_cell_c"><p>{0}</p></td><td class="h_cell_b"><p>Contractor Move-In/Out :<br />&nbsp;</p></td><td class="h_cell_c"><p>{1} {2} - {3} {4}</p></td></tr>'.NG_Format(
				tableData['exhb_srv_mgr'], s_in_DOW, tableData['show_in'] || "N/A", s_out_DOW, tableData['show_out'] || "N/A"
			);
		xml += '<tr><td class="h_cell_b"><p>Project Manager :<br /></p></td><td class="h_cell_c" style="white-space:nowrap; overflow:hidden;"><p style="white-space:nowrap; overflow:hidden;">{0}</p></td><td class="h_cell_b"><p>Event Dates :<br /></p></td><td class="h_cell_d"><p>{1} {2} - {3} {4}</p></td></tr>'.NG_Format(
				tableData['proj_mgr'], s_s_DOW, tableData['show_s'] || "N/A", s_e_DOW, tableData['show_e'] || "N/A"
			);
		xml += '</table>';
		
		xml += '{ADDITIONAL_B}';
		
		xml += '</div>';
		
		xml += '</macro>\n';
		return xml;
	}
	
	function xml_PageHeaderReduced(reportTitle) {
		var xml = '';
		xml += '<macro id="header">';
		xml += '<div style="position:absolute;" x="0%" y="0%"><table table-layout="fixed" border="0" style="width:720px; margin:0; padding:0; overflow:hidden;" cellmargin="0" cellpadding="0">';
		xml += '<tr><td style="overflow:hidden; height:0.93in;"><img src="{0}" width="2.5in" height="0.50in" style="margin:0; padding:0;" /></td>'.NG_Format(csLib.settings.WorkOrderImageURL);
		xml += '<td style="width:55%;"><p style="width:100%; align:left; vertical-align:top;"><span style="font-size:24px;">{0}</span><br />'.NG_Format(reportTitle);
		xml += '</p>';
		xml += '</td></tr>';
		xml += '</table>';
		xml += '{ADDITIONAL_A}';
		xml += '</div>';
		xml += '</macro>\n';
		return xml;
	}

	function xml_PageFooter(printDate) {
		var xml = '';
		xml += '<macro id="footer">';
		xml += '<table border="0" style="width: 100%; margin-left: 15px; margin-right: 15px;"><tr><td><p style="font-size:8px; width:100%; text-align:left">{0}</p></td><td><p style="font-size:8px; width:100%; text-align:right">Page <pagenumber/> of <totalpages/></p></td></tr></table>'.NG_Format(NG.time.dateToString(printDate, "datetimetz"));
		xml += '</macro>';
		return xml;
	}

	function data_GetShowDates(showTableData) {
		var showTableList = new Array();
		var showTableMap = { };
		for (var key in showTableData) {
			showTableList.push(showTableData[key]['table_id']);
			showTableMap[showTableData[key]['table_id']] = key;
		}
		
		var filt = new Array(
				["custrecord_show_number_date","anyof",showTableList]
			,	"and"
			,	["custrecord_date_type","anyof",[S_MOVE_IN_TYPE,S_MOVE_OUT_TYPE,E_MOVE_IN_TYPE,E_MOVE_OUT_TYPE,S_DATE_TYPE]]
		);
		var cols = new Array(
				search.createColumn({ name : "custrecord_show_number_date" , sum : "group" })
			,	search.createColumn({ name : "custrecord_date_type" , sum : "group" })
			,	search.createColumn({ name : "custrecord_date" , sum : "min" })
			,	search.createColumn({ name : "custrecord_date" , sum : "max" })
		);
		var results = null;
		try {
			results = NG.tools.getSearchResults("customrecord_show_date", filt, cols);
		} catch (err) {
			NG.log.logError(err, "Error encountered getting event dates");
		}
		
		if (results != null) {
			for (var i = 0; i < results.length; i++) {
				var tableId = results[i].getValue({ name : "custrecord_show_number_date" , sum : "group" });
				var dateType = results[i].getValue({ name : "custrecord_date_type" , sum : "group" });
				var minDate = results[i].getValue({ name : "custrecord_date" , sum : "min" });
				var maxDate = results[i].getValue({ name : "custrecord_date" , sum : "max" });
				var showId = showTableMap[tableId];
				
				switch (dateType) {
					case S_MOVE_IN_TYPE :
						showTableData[showId]['show_in'] = minDate;
						break;
					case S_MOVE_OUT_TYPE :
						showTableData[showId]['show_out'] = maxDate;
						break;
					case E_MOVE_IN_TYPE :
						showTableData[showId]['exhb_in'] = minDate;
						break;
					case E_MOVE_OUT_TYPE :
						showTableData[showId]['exhb_out'] = maxDate;
						break;
					case S_DATE_TYPE :
						showTableData[showId]['show_s'] = minDate;
						showTableData[showId]['show_e'] = maxDate;
						break;
				}
			}
		}
		
		return showTableData;
	}

	function data_GetSubcategories() {
		var options = new Array();
		var filt = new Array(
				["isinactive","is","F"]
		);
		var cols = new Array(
				search.createColumn({ name : "name" })
			,	search.createColumn({ name : "custrecord_ng_cs_subcat_p_cat" })
		);
		var results = null;
		try {
			results = NG.tools.getSearchResults("customrecord_subcategory", filt, cols);
		} catch (err) {
			NG.log.logError(err, "Error encountered getting subcategory values");
		}
		if (results != null) {
			for (var i = 0; i < results.length; i++) {
				options.push({
					id : results[i].id,
					value : "{0} : {1}".NG_Format(results[i].getText({ name : "custrecord_ng_cs_subcat_p_cat" }), search[i].getValue({ name : "name" }))
				});
			}
		}
		
		options = NG.tools.objectSort(options, 'value');
		options.unshift({
			id : "@NONE@",
			value : "N/A"
		});
		
		return options;
	}

	function data_GetShowTables(showTableData) {
		var showTableList = new Array();
		var showTableMap = { };
		var showIdListDone = new Array();
		for (var key in showTableData) {
			showTableList.push(showTableData[key]['table_id']);
			showTableMap[showTableData[key]['table_id']] = key;
		}
		
		var filt = new Array(
				["internalid","anyof",showTableList]
		);
		var cols = new Array(
				search.createColumn({ name : "custrecord_facility" })
			,	search.createColumn({ name : "custrecord_exh_svs_rep" })
			,	search.createColumn({ name : "custrecord_acct_exec" })
			,	search.createColumn({ name : "custrecord_project_mgr" })
			,	search.createColumn({ name : "custrecord_hall" })
			,	search.createColumn({ name : "custrecord_project_mgr" })
		);
		var results = null;
		try {
			results = nlapiSearchRecord("customrecord_show", null, filt, cols);
		} catch (err) {
			NG.log.logError(err, "Error encountered getting event data");
		}
		
		if (results != null) {
			for (var i = 0; i < results.length; i++) {
				var tableId = results[i].id;
				var showId = showTableMap[tableId];
				showTableData[showId]['exhb_srv_mgr'] = nlapiEscapeXML(results[i].getText({ name : "custrecord_acct_exec" }) || "N/A");
				showTableData[showId]['conv_srv_mgr'] = nlapiEscapeXML(results[i].getText({ name : "custrecord_project_mgr" }) || "N/A");
				showTableData[showId]['location'] = nlapiEscapeXML(results[i].getText({ name : "custrecord_exh_svs_rep" }) || "N/A");
				showTableData[showId]['proj_mgr'] = nlapiEscapeXML(results[i].getText({ name : "custrecord_project_mgr" }) || "N/A");
				showIdListDone.push(showId);
			}
		}
		
		for (var key in showTableData) {
			if (!NG.tools.isInArray(key, showIdListDone)) {
				showTableData[key]['exhb_srv_mgr'] = nlapiEscapeXML("N/A");
				showTableData[key]['conv_srv_mgr'] = nlapiEscapeXML("N/A");
				showTableData[key]['location'] = nlapiEscapeXML("N/A");
				showTableData[key]['proj_mgr'] = nlapiEscapeXML("N/A");
			}
		}
		
		return showTableData;
	}

	function data_GetOrderTypes() {
		var options = new Array();
		var filt = new Array(
				["isinactive","is","F"]
		);
		var cols = new Array(
				search.createColumn({ name : "name" })
		);
		var results = null;
		try {
			results = NG.tools.getSearchResults("customlist_ng_cs_order_type", filt, cols);
		} catch (err) {
			NG.log.logError(err, "Error encountered getting order type values");
		}
		if (results != null) {
			for (var i = 0; i < results.length; i++) {
				options.push({
					id : results[i].id,
					value : results[i].getValue({ name : "name" })
				});
			}
		}
		return options;
	}

	function BuildTimeSpanList(field) {
		field.addSelectOption("", "Choose Time Span", true);
		var d = new Date();
		for (var y = 2012; y <= d.getFullYear(); y++) {
			field.addSelectOption(y.toFixed(0), y.toFixed(0));
		}
		field.addSelectOption("A", "All");
		field.addSelectOption("F", "Future");
		field.addSelectOption("L", "Last 12 Months");
		field.addSelectOption("LN", "Last 12 Mos/Next 12 Mos");
		field.addSelectOption("N", "Next 12 Months");
	}

	function addTimeZoneOffsetHTML(form) {
		form.addField({ id : "custpage_offset" , type : widget.FieldType.TEXT , label : "tz offset" }).updateDisplayType({ displayType : widget.FieldDisplayType.HIDDEN });
		var html = '<script type="text/javascript">';
		html += 'setTimeout(function() { ';
		html += 'nlapiSetFieldValue("custpage_offset", (new Date()).getTimezoneOffset()); ';
		html += '}, 1000);';
		html += '</script>';
		form.addField({ id : "custpage_offset_html" , type : widget.FieldType.INLINEHTML , label : "tz offset script" }).defaultValue = html;
	}

	function setOffsetPrintDate(request) {
		var localOffset = new Number(request.parameters['fst']);
		var printDate = new Date();
		var offset = printDate.getTimezoneOffset();
		printDate.setMinutes(printDate.getMinutes() + offset);
		printDate.setMinutes(printDate.getMinutes() - localOffset);
		return printDate;
	}

	function objFilter(obj, func) {
		if (NG.tools.isEmpty(obj)) { return null; }
		if (NG.tools.isEmpty(func)) { return null; }
		var result = new Array();
		for (var key in obj) {
			if (func(obj[key])) {
				result.push(obj[key]);
			}
		}
		return result;
	}

	function objFind(obj, func) {
		if (NG.tools.isEmpty(obj)) { return null; }
		if (NG.tools.isEmpty(func)) { return null; }
		for (var key in obj) {
			if (func(obj[key])) {
				return this[obj];
			}
		}
		
		return null;
	}

	function itemNameDisplay(itemSku, itemName) {
		var dispOpt = csLib.settings.RptItemNameDisplay;
		var displayValue = "";
		
		switch (dispOpt) {
			case "2":
				displayValue = itemName;
				break;
			case "3":
				displayValue = "{0} : {1}".NG_Format(itemSku, itemName);
				break;
			case "4":
				displayValue = "{1} ({0})".NG_Format(itemSku, itemName);
				break;
			case "1":
			default:
				displayValue = itemSku;
				break;
		}
		
		return displayValue;
	}

	function data_GetNonBoothOrderTypes() {
		var types = new Array();
		var filt = new Array(
				["isinactive","is","F"]
			,	"and"
			,	["internalid","noneof",[csLib.settings.DefaultExhibitorOrderType]]
		);
		var cols = new Array(
				search.createColumn({ name : "name" })
		);
		var results = NG.tools.getSearchResults("customlist_ng_cs_order_type", filt, cols);
		if (!NG.tools.isEmpty(results)) {
			for (var r = 0; r < results.length; r++) {
				types.push({
						id : results[r].id
					,	text : results[r].getValue({ name : "name" })
				});
			}
		}
		
		return types;
	}
	
	function getItemCategoryData() {
		var listOut = new Array();
		var filt = new Array(
				["isinactive","is","F"]
		);
		var cols = new Array(
				search.createColumn({ name : "name" })
		);
		var results = NG.tools.getSearchResults("customlist_item_category", filt, cols);
		if (!NG.tools.isEmpty(results)) {
			for (var r = 0; r < results.length; r++) {
				listOut.push({
						id : results[r].id
					,	text : results[r].getValue({ name : "name" })
				});
			}
			
			listOut = NG.tools.objectSort(listOut, 'text');
		}
		
		return listOut;
	}
	
	function getItemSubCategoryData() {
		var listOut = new Array();
		var filt = new Array(
				["isinactive","is","F"]
		);
		var cols = new Array(
				search.createColumn({ name : "name" })
			,	search.createColumn({ name : "custrecord_ng_cs_subcat_p_cat" })
		);
		var results = NG.tools.getSearchResults("customrecord_subcategory", filt, cols);
		if (!NG.tools.isEmpty(results)) {
			for (var r = 0; r < results.length; r++) {
				listOut.push({
						id : results[r].id
					,	text : "{0}: {1}".NG_Format(results[r].getText({ name : "custrecord_ng_cs_subcat_p_cat" }), results[r].getValue({ name : "name" }))
				});
			}
			
			listOut = NG.tools.objectSort(listOut, 'text');
		}
		
		return listOut;
	}
	
	return {
			S_MOVE_IN_TYPE : S_MOVE_IN_TYPE
		,	S_MOVE_OUT_TYPE : S_MOVE_OUT_TYPE
		,	E_MOVE_IN_TYPE : E_MOVE_IN_TYPE
		,	E_MOVE_OUT_TYPE : E_MOVE_OUT_TYPE
		,	S_DATE_TYPE : S_DATE_TYPE
		,	_DAY_OF_WEEK : _DAY_OF_WEEK
		,	_SHADED_HEX : _SHADED_HEX
		,	_BODY_XML : _BODY_XML
		,	_BODY_XML_REDUCED : _BODY_XML_REDUCED
		,	xml_StyleSheet : xml_StyleSheet
		,	xml_PageHeader : xml_PageHeader
		,	xml_PageHeaderReduced : xml_PageHeaderReduced
		,	xml_PageFooter : xml_PageFooter
		,	data_GetShowDates : data_GetShowDates
		,	data_GetSubcategories : data_GetSubcategories
		,	data_GetShowTables : data_GetShowTables
		,	data_GetOrderTypes : data_GetOrderTypes
		,	BuildTimeSpanList : BuildTimeSpanList
		,	addTimeZoneOffsetHTML : addTimeZoneOffsetHTML
		,	setOffsetPrintDate : setOffsetPrintDate
		,	objFilter : objFilter
		,	objFind : objFind
		,	itemNameDisplay : itemNameDisplay
		,	data_GetNonBoothOrderTypes : data_GetNonBoothOrderTypes
		,	getItemCategoryData : getItemCategoryData
		,	getItemSubCategoryData : getItemSubCategoryData
	};
	
});
