/**
 * newgen.paytrace.lib.21.js
 * @NApiVersion 2.1
 * @NModuleScope Public
 */
define([
  "N/cache",
  "N/config",
  "N/encode",
  "N/error",
  "N/file",
  "N/format",
  "N/https",
  "N/record",
  "N/redirect",
  "N/runtime",
  "N/search",
  "N/task",
  "N/ui/serverWidget",
  "N/url",
  "N/util",
  "./newgen.library.v21",
  "./newgen.paytrace.lib.shared",
], /**
 * @param{cache} cache
 * @param{config} config
 * @param{encode} encode
 * @param{error} error
 * @param{file} file
 * @param{format} format
 * @param{https} https
 * @param{record} record
 * @param{redirect} redirect
 * @param{runtime} runtime
 * @param{search} search
 * @param{task} task
 * @param{serverWidget} serverWidget
 * @param{url} url
 * @param{util} util
 * @param{Object} NG
 * @param{Object} SHARED
 */ (
  cache,
  config,
  encode,
  error,
  file,
  format,
  https,
  record,
  redirect,
  runtime,
  search,
  task,
  serverWidget,
  url,
  util,
  NG,
  SHARED
) => {
  let _PAYTRACE_DATA,
    _PAYTRACE_INTEGRATION_ID,
    _PAYTRACE_USERNAME,
    _PAYTRACE_PASSWORD,
    _PAYTRACE_ENDPOINT,
    _CURRENT_LICENSE_KEY,
    PAYTRACE_ENDPOINT_CACHE,
    PAYTRACE_ENDPOINT_CACHED,
    _NOT_RETRIEVED = false;

  /**
   * @type _PT_SETTINGS
   */
  let _SETTINGS;

  // PayTrace API documentation can be found at https://developers.paytrace.com/support/home
  // PayTrace API endpoint domain
  const _PT_ENDPOINT = "https://api.paytrace.com",
    _PT_ENDPOINT_SB = "https://api.sandbox.paytrace.com";

  const _PROTECT_JS_ENDPOINT = "https://protect.paytrace.com/js/protect.min.js",
    _PROTECT_JS_ENDPOINT_SB =
      "https://protect.sandbox.paytrace.com/js/protect.min.js";

  //region PayTrace API endpoint paths
  const _AUTH_ENDPOINT = "/oauth/token",
    _KEYED_AUTH_ENDPOINT = "/v1/transactions/authorization/keyed",
    _KEYED_SALE_ENDPOINT = "/v1/transactions/sale/keyed",
    _KEYED_REFUND_ENDPOINT = "/v1/transactions/refund/keyed",
    _REFUND_AUTH_ENDPOINT = "/v1/transactions/refundauth/keyed",
    _REFUND_AUTH_TRANS_ENDPOINT = "/v1/transactions/refundauth/for_transaction",
    _EMAIL_RECEIPT_ENDPOINT = "/v1/transactions/email_receipt",
    _EXPORT_TRANSACTION_ENDPOINT = "/v1/transactions/export/by_id",
    _EXPORT_TRANSACTION_RANGE_ENDPOINT =
      "/v1/transactions/export/by_date_range",
    _CAPTURE_ENDPOINT = "/v1/transactions/authorization/capture",
    _TRANS_SALE_ENDPOINT = "/v1/transactions/sale/by_transaction",
    _VOID_ENDPOINT = "/v1/transactions/void",
    _FIND_BATCHES_ENDPOINT = "/v1/batches/export",
    _GET_SETTLED_ENDPOINT = "/v1/batches/transaction_list",
    _GET_CLIENT_KEY_ENDPOINT = "/v1/payment_fields/token/create?",
    _PROTECT_SALE_ENDPOINT = "/v1/transactions/sale/pt_protect",
    _ACH_SALE_ENDPOINT = "/v1/checks/sale/by_account",
    _EMV_SALE_ENDPOINT = "/v1/transactions/sale/emv",
    _EMV_AUTH_ENDPOINT = "/v1/transactions/authorization/emv";
  //endregion

  // NewGen licensing endpoint
  const _LICENSE_URL =
    "https://459955.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=423&deploy=1&compid=459955&h=396ad0814936d7dff3e1&custscript_custom_license_key={0}";
  //const _LICENSE_URL_ALT = "https://459955.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=423&deploy={1}&compid=459955&h={2}&custscript_custom_license_key={0}";

  const _PAYTRACE_CACHE_NAME = "NG_PAYTRACE_CACHE",
    _PAYTRACE_CACHE_INDEX = "NG_PAYTRACE_INDEX",
    _PRODUCT_ID = "1014",
    _LICENSE_URL_PARAMS = [
      { d: "1", h: "396ad0814936d7dff3e1" },
      { d: "2", h: "f2351aade8cf8a0b1bbc" },
      { d: "3", h: "7078d52239ce0b2284cd" },
    ];

  const _VIEW_LINK_STYLE = `
			<style>
				.ng_link { cursor: pointer; color: blue; text-decoration: underline; }
				.ng_link:hover { color: red; }
				.ng_linkp { width: 100%; text-align: center; }
			</style>
		`;
  const _VIEW_SCRIPT_HTML = `
			<script type="text/javascript">
				function ptOpenRef(url) {
					window.open(url, '_blank', 'width=1025,height=800');
				}
			</script>
		`;
  const _VIEW_LINK_HTML = `<p style="width: 100%; padding-left: 10px;"><span class="ng_link"><a onclick="ptOpenRef('{0}')">View</a></span></p>`;

  // Referential mapping to 'PayTrace Transaction Type' custom list (customlist_ng_paytrace_trans_type)
  const _TRANS_TYPE = {
    ACH: "3",
    Cap: "8",
    Card: "1",
    EMV: "2",
    Refund: "5",
    Void: "4",
    Unk: "7",
  };

  const _MAPPINGS = {
    transType: {
      ACH: "3",
      Cap: "8",
      Card: "1",
      EMV: "2",
      Refund: "5",
      Void: "4",
      Unk: "7",
    },
    recordTypes: {
      order: "salesorder",
      cashSale: "cashsale",
      invoice: "invoice",
      cashRfd: "cashrefund",
      custDep: "customerdeposit",
      custPymt: "customerpayment",
      custRnd: "customerrefund",
      creditMemo: "creditmemo",
      customer: "customer",
      pymtRef: "customrecord_ng_paytrace_ref",
      cardData: "customrecord_ng_pt_ecrypted_card",
      intProfile: "customrecord_ng_paytrace_integration",
      settings: "customrecord_ng_paytrace_settings",
      autoPayFreq: "customrecord_ng_pt_autopay_frequency",
      autoPaySched: "customrecord_ng_pt_autopay_schedule",
      convFeeDetail: "customrecord_ng_paytrace_conv_fee_detail",
    },
    lists: {
      transType: {
        id: "customlist_ng_paytrace_trans_type",
        values: {
          ACH: "3",
          Cap: "8",
          Card: "1",
          EMV: "2",
          Refund: "5",
          Void: "4",
          Unk: "7",
        },
      },
      stmtDateOpts: {
        id: "customlist_ng_pt_paynow_stmt_options",
        values: {
          CurrDate: "1",
          EndOfPrMonth: "2",
        },
      },
      autoPayStatus: {
        id: "customlist_ng_pt_autopay_status",
        values: {
          Ineligible: "1",
          Eligible: "2",
          Pending: "3",
          Success: "4",
          Failed: "5",
          ExpiredCard: "6",
        },
      },
    },
    fields: {
      pymtRef: {
        invoice: "custrecord_ng_ptr_invoice",
        payment: "custrecord_ng_ptr_payment",
        creditMemo: "custrecord_ng_ptr_creditmemo",
        order: "custrecord_ng_ptr_sales_order",
        cashSale: "custrecord_ng_ptr_cash_sale",
        deposit: "custrecord_ng_ptr_deposit",
        refund: "custrecord_ng_ptr_refund",
        ptApprvCode: "custrecord_ng_ptr_approval_code",
        ptTranId: "custrecord_ng_ptr_transaction_id",
        tranType: "custrecord_ng_ptr_trans_type",
        interactionType: "custrecord_ng_ptr_interaction_type",
        isCompleted: "custrecord_ng_ptr_completed",
        isRefund: "custrecord_ng_ptr_is_refund",
        cardType: "custrecord_ng_ptr_card_type",
        maskedCardNum: "custrecord_ng_ptr_masked_card_number",
        cardExp: "custrecord_ng_ptr_card_expiration",
        tranAmount: "custrecord_ng_ptr_transaction_amount",
        tranDateTime: "custrecord_ng_ptr_transaction_datetime",
        isAuthorized: "custrecord_ng_ptr_authorized",
        ptIntId: "custrecord_ng_ptr_integration",
        sttlmtBatch: "custrecord_ng_ptr_settlement_batch",
        sttlmtDate: "custrecord_ng_ptr_settlement_date",
        customer: "custrecord_ng_ptr_customer",
        ptReqData: "custrecord_ng_ptr_paytrace_request",
        ptRspData: "custrecord_ng_ptr_paytrace_data",
        ptRtrData: "custrecord_ng_ptr_paytrace_data_export",
      },
      cardData: {
        customer: "custrecord_ng_ptecd_customer",
        cardId: "custrecord_ng_ptecd_card_id",
        encCardNum: "custrecord_ng_ptecd_encrypted_card",
        encSecCode: "custrecord_ng_ptecd_encypted_csc",
        profile: "custrecord_ng_ptecd_applied_profile",
      },
      profile: {
        name: "name",
        ptIntegratorId: "custrecord_ng_ptid_integrator_id",
        ptUserId: "custrecord_ng_ptid_user_id",
        ptPassword: "custrecord_ng_ptid_password",
        isDefault: "custrecord_ng_ptid_is_default",
        isSandbox: "custrecord_ng_ptid_is_sandbox",
        isDisabled: "custrecord_ng_ptid_disabled",
        allowKeyedRef: "custrecord_ng_ptid_allow_keyed_refunds",
        forceKeyedRef: "custrecord_ng_ptid_force_keyed_refunds",
        defaultAccount: "custrecord_ng_ptid_default_account",
        appCardMeth: "custrecord_ng_ptid_card_methods",
        isEnabledACH: "custrecord_ng_ptid_ach_enabled",
        pubKeyFile: "custrecord_ng_ptid_pub_key_file",
        refRecInvForm: "custrecord_ng_ptid_ref_inv_form",
        refRecOrdForm: "custrecord_ng_ptid_ref_ord_form",
      },
      transaction: {
        payLinkURL: "custbody_ng_paytrace_pay_link_url",
        depositLinkURL: "custbody_ng_paytrace_deposit_link_url",
        achData: "custbody_ng_paytrace_ach_data",
        cardExp: "custbody_ng_paytrace_card_exp",
        cardType: "custbody_ng_paytrace_card_type",
        onCCHold: "custbody_ng_paytrace_cchold",
        ccHoldDetails: "custbody_ng_paytrace_ccholddetails",
        chargeAmount: "custbody_ng_paytrace_charge_amount",
        dataRefRec: "custbody_ng_paytrace_data_ref",
        maskedCardNum: "custbody_ng_paytrace_masked_number",
        ptProfile: "custbody_ng_paytrace_profile_rec",
        ptProfileId: "custbody_ng_paytrace_profile",
        sttlmtBatch: "custbody_ng_paytrace_settlement_batch",
        ptSubscr: "custbody_ng_paytrace_subscription",
        tranDate: "custbody_ng_paytrace_trans_date",
        ptTranId: "custbody_ng_paytrace_trans_id",
        amountDue: "custbody_ng_pt_amount_due",
        apCount: "custbody_ng_paytrace_ap_payment_count",
        apSched: "custbody_ng_paytrace_ap_schedule",
        apInitAmount: "custbody_ng_paytrace_ap_first_pay_amnt",
        apSchedAmount: "custbody_ng_paytrace_ap_sched_amount",
        apNextPayDate: "custbody_ng_paytrace_ap_next_pay_date",
        apStatus: "custbody_ng_paytrace_ap_status",
        apProfile: "custbody_ng_paytrace_ap_profile",
        apSignupURL: "custbody_ng_paytrace_ap_signup_lnk_url",
        apUpdateURL: "custbody_ng_paytrace_ap_update_lnk_url",
        apCardType: "custbody_ng_paytrace_ap_card_type",
        apCard: "custbody_ng_paytrace_ap_credit_card",
        apCardExp: "custbody_ng_paytrace_ap_credit_crd_exp",
        apCardMasked: "custbody_ng_paytrace_ap_masked_crd_num",
        apDontUpd: "custbody_ng_paytrace_ap_dont_upd_sched",
      },
      entity: {
        statementLinkURL: "custentity_ng_paytrace_stmt_link_url",
        quickBillSignupLinkURL: "custentity_ng_paytrace_quikbill_link_url",
        defaultProfile: "custentity_ng_pt_default_profile",
      },
      general: {
        settings: "custpage_ng_pt_settings",
        mappings: "custpage_ng_pt_mappings",
        cclist: "custpage_ccidlist",
      },
      autoPayFreq: {
        name: "name",
        isDefault: "custrecord_ng_ptapf_is_default",
        freq: "custrecord_ng_ptapf_freq_amount",
        freqIsDays: "custrecord_ng_ptapf_freq_in_days",
        freqIsMths: "\tcustrecord_ng_ptapf_freq_in_months",
        freqIsYrs: "custrecord_ng_ptapf_freq_in_years",
        descr: "custrecord_ng_ptapf_freq_descriptor",
        termDescr: "custrecord_ng_ptapf_period_descriptor",
      },
      autoPaySched: {
        invoice: "custrecord_ng_ptap_invoice",
        paySeq: "custrecord_ng_ptap_pay_seq",
        payDate: "custrecord_ng_ptap_payment_date",
        payAmount: "custrecord_ng_ptap_payment_amount",
        status: "custrecord_ng_ptap_status",
        payComplete: "custrecord_ng_ptap_payment_complete",
        actualPayDate: "custrecord_ng_ptap_actual_pay_date",
        isFinalPay: "custrecord_ng_ptap_is_final_payment",
        payRec: "custrecord_ng_ptap_payment",
      },
    },
    columns: {
      transaction: {
        ptSubscr: "custcol_ng_pt_subscription",
      },
      customerCards: {
        key: "custpage_col_rand_value",
      },
    },
    scripts: {
      getCardData: {
        id: "customscript_ng_pt_sl_get_card_data",
        deploy: "customdeploy_ng_pt_sl_get_card_data_dep",
      },
      getEMV: {
        id: "customscript_ng_pt_sl_get_emv",
        deploy: "customdeploy_ng_pt_sl_get_emv_dep",
      },
      getIntegratorData: {
        id: "customscript_ng_pt_sl_get_intgr_data",
        deploy: "customdeploy_ng_pt_sl_get_intgr_data_dep",
      },
      getKeyData: {
        id: "customscript_ng_pt_sl_get_key_data",
        deploy: "customdeploy_ng_pt_sl_get_key_data_dep",
      },
      payNowInvoice: {
        id: "customscript_ng_pt_sl_invoice_link",
        deploy: "customdeploy_ng_pt_sl_invoice_link_dep",
      },
      payNowStatement: {
        id: "customscript_ng_pt_sl_paynow_stmt",
        deploy: "customdeploy_ng_pt_sl_paynow_stmt_dep",
      },
      getCustomerProf: {
        id: "customscript_ng_pt_sl_retr_cust_prof",
        deploy: "customdeploy_ng_pt_sl_retr_cust_prof_dep",
      },
      validateCreds: {
        id: "customscript_ng_pt_sl_validate_creds",
        deploy: "customdeploy_ng_pt_sl_validate_creds_dep",
      },
      runSubscrPymt: {
        id: "customscript_ng_pt_sl_prc_subscr_pmt",
        deploy: "customdeploy_ng_pt_sl_prc_subscr_pmt_dep",
        params: {
          subscrId: "subscrId",
        },
      },
      processSubs: {
        id: "customscript_ng_pt_mr_proc_sub_pymts",
        deploy: "customdeploy_ng_pt_mr_proc_sub_pymts_man",
        params: {
          action: "custscript_ng_pt_mr_prc_sub_action",
          subscrId: "custscript_ng_pt_mr_prc_sub_ids",
        },
      },
      quickBill: {
        id: "customscript_ng_pt_sl_snd_qukbll_eml",
        deploy: "customdeploy_ng_pt_sl_snd_qukbll_eml_dep",
        params: {
          custId: "custId",
        },
      },
      quickBillES: {
        id: "customscript_ng_pt_sl_qukbll_eml_sel",
        deploy: "customdeploy_ng_pt_sl_qukbll_eml_sel_dep",
        params: {
          custId: "custId",
        },
      },
      clientUtil: {
        id: "customscript_ng_pt_sl_client_util",
        deploy: "customdeploy_ng_pt_sl_client_util_dep",
        params: {
          action: "action",
          fileId: "fileId",
        },
      },
    },
    labels: {
      subscriptions: {
        charge: {
          success: "Successful subscription billing",
          failure: "Failed subscription billing",
        },
        preExpire: {
          success: "Successful subscription expiration warning",
          failure: "Failed subscription expiration warning",
        },
        expire: {
          success: "Successful subscription expiration",
          failure: "Failed subscription expiration",
        },
      },
    },
    modals: {
      subscr: {
        oneTimeBill: "ng_pt_subscr_modal_ot_bill",
      },
      emvAuth: {
        modalId: "ng_pt_emv_pre_auth",
      },
      quickBill: {
        sendEmail: "ng_pt_quick_bill_send",
      },
    },
    paynow: {
      button: {
        1: "btn-primary",
        2: "btn-secondary",
        3: "btn-success",
        4: "btn-danger",
        5: "btn-warning",
        6: "btn-info",
        7: "btn-light",
        8: "btn-dark",
        9: "btn-primary",
        10: "btn-outline-primary",
        11: "btn-outline-secondary",
        12: "btn-outline-success",
        13: "btn-outline-danger",
        14: "btn-outline-warning",
        15: "btn-outline-info",
        16: "btn-outline-light",
        17: "btn-outline-dark",
      },
    },
  };

  const _CARD_TYPE_MAP = {
    VISA: {
      display: "Visa",
      setting: "custrecord_ng_pts_rcr_bill_visa_pay_meth",
    },
    MASTERCARD: {
      display: "Mastercard",
      setting: "custrecord_ng_pts_rcr_bill_mc_pay_meth",
    },
    AMEX: {
      display: "AmEx",
      setting: "custrecord_ng_pts_rcr_bill_amex_pay_meth",
    },
    DISCOVER: {
      display: "Discover",
      setting: "custrecord_ng_pts_rcr_bill_disc_pay_meth",
    },
  };

  const _PT_CARD_ERROR_CODES = [35, 39, 43, 44, 64, 65];

  //region STTG

  /**
   * Gets PayTrace integration settings values
   * @param {Boolean} [addURL] If true, inserts the licensing endpoint URL
   * @returns {PT_Settings}
   */
  const sttg_GetSettings = (addURL) => {
    let columns = [
        "custrecord_ng_pts_license_key",
        "custrecord_ng_pts_hide_def_proc_fields",
        "custrecord_ng_pts_other_card_pay_methods",
        "custrecord_ng_pts_card_select_label",
        "custrecord_ng_pts_profile_select_label",
        "custrecord_ng_pts_dont_save_cards",
        "custrecord_ng_pts_online_pay_meth",
        "custrecord_ng_pts_page_template",
        "custrecord_ng_pts_success_template",
        "custrecord_ng_pts_failure_template",
        "custrecord_ng_pts_system_error_template",
        "custrecord_ng_pts_paid_template",
        "custrecord_ng_pts_ach_payment_method",
        "custrecord_ng_pts_ref_inv_form",
        "custrecord_ng_pts_ref_order_form",
        "custrecord_ng_pts_ref_fail_form",
        "custrecord_ng_pts_ach_success_template",
        "custrecord_ng_pt_refund_payment_method",
        "custrecord_ng_pts_emv_payment_method",
        "custrecord_ng_pts_statement_form",
        "custrecord_ng_pts_stmt_success_template",
        "custrecord_ng_pts_stmt_ach_scss_template",
        "custrecord_ng_pts_statement_months",
        "custrecord_ng_pts_statement_date",
        "custrecord_ng_pts_enable_verbose_alerts",
        "custrecord_ng_pts_run_suretax_cs_emv",
        "custrecord_ng_pts_paynow_profile",
        "custrecord_ng_pts_invoice_print_form",
        "custrecord_ng_pts_credit_memo_print_form",
        "custrecord_ng_pts_payment_print_form",
        "custrecord_ng_pts_cc_conv_fee_pct",
        "custrecord_ng_pts_ach_conv_fee_pct",
        "custrecord_ng_pts_conv_fee_consent_msg",
        "custrecord_ng_pts_enable_conv_fees",
        "custrecord_ng_pts_auto_set_default_cc",
        "custrecord_ng_pts_rcr_bill_def_profile",
        "custrecord_ng_pts_rcr_bill_s_email_sendr",
        "custrecord_ng_pts_rcr_bill_s_email_recip",
        "custrecord_ng_pts_rcr_bill_c_email_sendr",
        "custrecord_ng_pts_rcr_bill_sucss_templt",
        "custrecord_ng_pts_rcr_bill_fail_templt",
        "custrecord_ng_pts_rcr_bill_fail_eml_bcc",
        "custrecord_ng_pts_enable_rcurrng_billing",
        "custrecord_ng_pts_rcr_bill_ce_page_tmplt",
        "custrecord_ng_pts_rcr_bill_visa_pay_meth",
        "custrecord_ng_pts_rcr_bill_mc_pay_meth",
        "custrecord_ng_pts_rcr_bill_amex_pay_meth",
        "custrecord_ng_pts_rcr_bill_disc_pay_meth",
        "custrecord_ng_pts_rcr_bill_ce_scss_tmplt",
        "custrecord_ng_pts_rcr_bill_ce_fail_tmplt",
        "custrecord_ng_pts_rcr_bill_exp_soon_tmpl",
        "custrecord_ng_pts_rcr_bill_exp_imm_tmpl",
        "custrecord_ng_pts_rcr_bill_card_exp_tmpl",
        "custrecord_ng_pts_rcr_bill_paid_templt",
        "custrecord_ng_pts_rcr_bill_signup_tmplt",
        "custrecord_ng_pts_deposit_page_template",
        "custrecord_ng_pts_dep_success_template",
        "custrecord_ng_pts_dep_ach_scss_template",
        "custrecord_ng_pts_dep_applied_template",
        "custrecord_ng_pts_rcr_bill_cu_page_tmplt",
        "custrecord_ng_pts_rcr_bill_cu_scss_tmplt",
        "custrecord_ng_pts_enable_emv_pre_auth",
        "custrecord_ng_pts_billed_template",
        "custrecord_ng_pts_closd_cancld_template",
        "custrecord_ng_pts_enable_alt_conv_fees",
        "custrecord_ng_pts_use_convfee_on_payment",
        "custrecord_ng_pts_use_convfee_on_deposit",
        "custrecord_ng_pts_invoice_conv_fees",
        "custrecord_ng_pts_conv_fee_item",
        "custrecord_ng_pts_card_toknztn_pg_tmplt",
        "custrecord_ng_pts_card_toknztn_scs_tmplt",
        "custrecord_ng_pts_card_toknztn_eml_tmplt",
        "custrecord_ng_pts_tokenizer_email_sender",
        "custrecord_ng_pts_stmnt_unapplied_amt",
      ],
      sSelCols = [
        "custrecord_ng_pts_online_pay_meth",
        "custrecord_ng_pts_ach_payment_method",
        "custrecord_ng_pts_page_template",
        "custrecord_ng_pts_success_template",
        "custrecord_ng_pts_failure_template",
        "custrecord_ng_pts_system_error_template",
        "custrecord_ng_pts_paid_template",
        "custrecord_ng_pts_ach_success_template",
        "custrecord_ng_pts_emv_payment_method",
        "custrecord_ng_pts_ach_payment_method",
        "custrecord_ng_pt_refund_payment_method",
        "custrecord_ng_pts_statement_form",
        "custrecord_ng_pts_stmt_success_template",
        "custrecord_ng_pts_stmt_ach_scss_template",
        "custrecord_ng_pts_statement_date",
        "custrecord_ng_pts_paynow_profile",
        "custrecord_ng_pts_invoice_print_form",
        "custrecord_ng_pts_credit_memo_print_form",
        "custrecord_ng_pts_payment_print_form",
        "custrecord_ng_pts_rcr_bill_def_profile",
        "custrecord_ng_pts_rcr_bill_s_email_sendr",
        "custrecord_ng_pts_rcr_bill_c_email_sendr",
        "custrecord_ng_pts_rcr_bill_sucss_templt",
        "custrecord_ng_pts_rcr_bill_fail_templt",
        "custrecord_ng_pts_rcr_bill_ce_page_tmplt",
        "custrecord_ng_pts_rcr_bill_visa_pay_meth",
        "custrecord_ng_pts_rcr_bill_mc_pay_meth",
        "custrecord_ng_pts_rcr_bill_amex_pay_meth",
        "custrecord_ng_pts_rcr_bill_disc_pay_meth",
        "custrecord_ng_pts_rcr_bill_ce_scss_tmplt",
        "custrecord_ng_pts_rcr_bill_ce_fail_tmplt",
        "custrecord_ng_pts_rcr_bill_exp_soon_tmpl",
        "custrecord_ng_pts_rcr_bill_exp_imm_tmpl",
        "custrecord_ng_pts_rcr_bill_card_exp_tmpl",
        "custrecord_ng_pts_rcr_bill_paid_templt",
        "custrecord_ng_pts_rcr_bill_signup_tmplt",
        "custrecord_ng_pts_deposit_page_template",
        "custrecord_ng_pts_dep_success_template",
        "custrecord_ng_pts_dep_ach_scss_template",
        "custrecord_ng_pts_dep_applied_template",
        "custrecord_ng_pts_rcr_bill_cu_page_tmplt",
        "custrecord_ng_pts_rcr_bill_cu_scss_tmplt",
        "custrecord_ng_pts_billed_template",
        "custrecord_ng_pts_closd_cancld_template",
        "custrecord_ng_pts_conv_fee_item",
        "custrecord_ng_pts_card_toknztn_pg_tmplt",
        "custrecord_ng_pts_card_toknztn_scs_tmplt",
        "custrecord_ng_pts_card_toknztn_eml_tmplt",
        "custrecord_ng_pts_tokenizer_email_sender",
      ],
      mSelCols = [
        "custrecord_ng_pts_other_card_pay_methods",
        "custrecord_ng_pts_rcr_bill_s_email_recip",
        "custrecord_ng_pts_rcr_bill_fail_eml_bcc",
      ],
      settings = {};

    try {
      settings = NG.tools.getLookupFields(
        "customrecord_ng_paytrace_settings",
        1,
        columns,
        sSelCols,
        mSelCols
      );
    } catch (err) {
      NG.log.logError(err, "Error encountered getting PayTrace settings");
    }

    if (addURL) {
      settings.licenseURL = _LICENSE_URL;
    }
    settings.productId = _PRODUCT_ID;
    require(["N/runtime"], (runtime) => {
      settings.accountId = runtime.accountId;
    });
    settings.licenseInfo = data_GetLicenseValidation(
      settings.custrecord_ng_pts_license_key
    );
    _SETTINGS = settings;

    return settings;
  };

  /**
   * Gets PayTrace integration settings values
   * @param {Array} A Array of all settings fields to retrieve
   * @param {Array} [M] Array of all multi-select settings fields to retrieve; Not required if not looking up multi-select fields
   * @param {Boolean} [addURL] If true, inserts the licensing endpoint URL
   * @returns {PT_Settings}
   */
  const sttg_GetSpecificSettings = (A, M, addURL) => {
    if (NG.tools.isEmpty(A) || (A || []).length === 0) {
      throw error.create({
        name: "INVALID_DATA",
        message: "No settings fields were specified for retrieval",
        notifyOff: true,
      });
    }

    let settings = {};
    A.push(SHARED.SettingsMap.licensing.key);

    try {
      let mFields = M || [],
        settingsSearch = search.lookupFields({
          type: _MAPPINGS.recordTypes.settings,
          id: 1,
          columns: A,
        });

      A.forEach((fieldId) => {
        if (mFields.includes(fieldId)) {
          settings[fieldId] = [];
          if (!NG.tools.isEmpty(settingsSearch[fieldId])) {
            settingsSearch[fieldId].forEach((value) => {
              settings[fieldId].push(value);
            });
          }
        } else {
          if (!NG.tools.isEmpty(settingsSearch[fieldId])) {
            if (util.isArray(settingsSearch[fieldId])) {
              if ((settingsSearch[fieldId] || []).length > 0) {
                settings[fieldId] = settingsSearch[fieldId][0].value;
                settings[`${fieldId}_text`] = settingsSearch[fieldId][0].text;
              } else {
                settings[fieldId] = "";
                settings[`${fieldId}_text`] = "";
              }
            } else {
              settings[fieldId] = !NG.tools.isEmpty(settingsSearch[fieldId])
                ? settingsSearch[fieldId]
                : "";
            }
          } else {
            settings[fieldId] = "";
          }
        }
      });
    } catch (err) {
      NG.log.logError(err, "Error encountered getting PayTrace settings");
    }

    if (addURL) {
      settings.licenseURL = _LICENSE_URL;
    }
    settings.productId = _PRODUCT_ID;
    require(["N/runtime"], (runtime) => {
      settings.accountId = runtime.accountId;
    });
    settings.licenseInfo = data_GetLicenseValidation(
      settings[SHARED.SettingsMap.licensing.key]
    );
    _SETTINGS = settings;

    return settings;
  };

  //endregion STTG

  //region COMM

  /**
   * Performs the initial PayTrace OAuth using the defined integration definition record internal ID and returns the generated token
   * @param {string} intId Integration definition record internal ID
   * @returns {string}
   */
  const comm_GetPayTraceAuth = (intId) => {
    data_GetPayTraceInfo(intId);
    let headers = {
        Accept: "*/*",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
      // targetURL = `${_PAYTRACE_ENDPOINT}${_AUTH_ENDPOINT}?grant_type=password&username=${NG.url.escapeURL(_PAYTRACE_USERNAME)}&password=${NG.url.escapeURL(_PAYTRACE_PASSWORD)}`,
      targetURL = `${
        _PAYTRACE_ENDPOINT || PAYTRACE_ENDPOINT_CACHED
      }${_AUTH_ENDPOINT}`,
      request;

    log.audit({
      title: "making call to PayTrace",
      details: "obtaining authentication token",
    });
    try {
      request = https.post({
        url: targetURL,
        body: {
          grant_type: "password",
          username: _PAYTRACE_USERNAME,
          password: _PAYTRACE_PASSWORD,
        },
        headers: headers,
      });
    } catch (err) {
      NG.log.logError(err, "Error encountered retrieving OAuth token");
      return "";
    }

    if (!NG.tools.isEmpty(request)) {
      if (Number(request.code) === 200) {
        let responseRAW = request.body,
          response;
        try {
          response = JSON.parse(responseRAW);
        } catch (err) {
          response = responseRAW;
        }

        let token = response.access_token;
        if (!NG.tools.isEmpty(token)) {
          return token;
        } else {
          let errA = error.create({
            name: `${response.error}`,
            message: `${response.error_description}`,
          });
          NG.log.logError(
            errA,
            "Error encountered authenticating PayTrace connection"
          );
          return "";
        }
      } else {
        let errB = error.create({
          name: "HTTP_ERROR",
          message: `The PayTrace servers returned a response code of ${request.code}`,
        });
        NG.log.logError(
          errB,
          "Error encountered authenticating PayTrace connection"
        );
        return "";
      }
    } else {
      return "";
    }
  };

  /**
   * Performs the initial PayTrace OAuth using the defined PayTrace account username and password and returns the generated token
   * @param {string} userName
   * @param {string} password
   * @param {Boolean} isSandBox
   * @returns {string|null}
   */
  const comm_GetPayTraceAuthValidate = (userName, password, isSandBox) => {
    if (runtime.envType !== runtime.EnvType.PRODUCTION && !isSandBox) {
      throw error.create({
        name: "PAYTRACE_ERROR",
        message:
          "Cannot connect to production PayTrace account from non-production NetSuite account.",
      });
    }

    _PAYTRACE_USERNAME = userName;
    _PAYTRACE_PASSWORD = password;
    _PAYTRACE_ENDPOINT = isSandBox ? _PT_ENDPOINT_SB : _PT_ENDPOINT;

    let headers = {
        Accept: "*/*",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
      // targetURL = `${_ENDPOINT}${_AUTH_ENDPOINT}?grant_type=password&username=${NG.url.escapeURL(userName)}&password=${NG.url.escapeURL(password)}`,
      targetURL = `${_PAYTRACE_ENDPOINT}${_AUTH_ENDPOINT}`,
      request;

    try {
      request = https.post({
        url: targetURL,
        body: {
          grant_type: "password",
          username: _PAYTRACE_USERNAME,
          password: _PAYTRACE_PASSWORD,
        },
        headers: headers,
      });
    } catch (err) {
      NG.log.logError(err, "Error encountered retrieving OAuth token");
      return null;
    }

    if (!NG.tools.isEmpty(request)) {
      if (Number(request.code) === 200) {
        let responseRAW = request.body,
          response;
        try {
          response = JSON.parse(responseRAW);
        } catch (err) {
          response = responseRAW;
        }

        let token = response.access_token;
        if (!NG.tools.isEmpty(token)) {
          return token;
        } else {
          let errC = error.create({
            name: `${response.error}`,
            message: `${response.error_description}`,
            notifyOff: true,
          });
          NG.log.logError(
            errC,
            "Error encountered authenticating PayTrace connection"
          );
          return null;
        }
      } else {
        let errD = error.create({
          name: "HTTP_ERROR",
          message: `The PayTrace servers returned a response code of ${request.code}`,
          notifyOff: true,
        });
        NG.log.logError(
          errD,
          "Error encountered authenticating PayTrace connection"
        );
        log.error({ title: "PayTrace Response", details: request });
        return null;
      }
    } else {
      return null;
    }
  };

  /**
   * Performs a keyed credit card authorization
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {number} post.amount Amount to be charged
   * @param {Object} post.billing_address Credit card billing address
   * @param {string} post.billing_address.name Credit card billing address addressee
   * @param {string} post.billing_address.street_address Credit card billing address street address
   * @param {string} post.billing_address.zip Credit card billing address zip code
   * @param {number} [post.tax_amount] Amount of sales tax included in charge amount; To be omitted if sales tax is zero or not available
   * @param {Object} post.credit_card Credit card data
   * @param {string} post.credit_card.number] Plaintext credit card number; Omitted if encrypted number is provided
   * @param {string} post.credit_card.encrypted_number] Credit card number encrypted by PayTrace encryption function; Omitted if plaintext number is provided
   * @param {string} post.credit_card.expiration_month Credit card expiration month
   * @param {string} post.credit_card.expiration_year Credit card expiration year
   * @param {string} [post.csc] Plaintext credit card security code
   * @param {string} [post.encrypted_csc] Credit card security code encrypted by PayTrace encryption function
   * @param {string} post.customer_reference_id Customer reference value (max 50 char)
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_AuthCardPayment = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_KEYED_AUTH_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "authorizing a card transaction"
    );
  };

  /**
   * Performs a keyed credit card payment
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {number} post.amount Amount to be charged
   * @param {Object} post.billing_address Credit card billing address
   * @param {string} post.billing_address.name Credit card billing address addressee
   * @param {string} post.billing_address.street_address Credit card billing address street address
   * @param {string} post.billing_address.zip Credit card billing address zip code
   * @param {string} post.invoice_id Reference to record being paid
   * @param {number} [post.tax_amount] Amount of sales tax included in charge amount; To be omitted if sales tax is zero or not available
   * @param {Object} post.credit_card Credit card data
   * @param {string} post.credit_card.number] Plaintext credit card number; Omitted if encrypted number is provided
   * @param {string} post.credit_card.encrypted_number] Credit card number encrypted by PayTrace encryption function; Omitted if plaintext number is provided
   * @param {string} post.credit_card.expiration_month Credit card expiration month
   * @param {string} post.credit_card.expiration_year Credit card expiration year
   * @param {string} [post.csc] Plaintext credit card security code
   * @param {string} [post.encrypted_csc] Credit card security code encrypted by PayTrace encryption function
   * @param {string} post.customer_reference_id Customer reference value (max 50 char)
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_SendCardPayment = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    PAYTRACE_ENDPOINT_CACHE = cache.getCache({
      name: "PT_ENDPOINT",
      scope: cache.Scope.PUBLIC,
    });

    PAYTRACE_ENDPOINT_CACHED = PAYTRACE_ENDPOINT_CACHE.get({
      key: "PT_TOKEN",
      loader: handleGetPaytraceEndpointLoader,
      ttl: 57600, // 16 hours
    });

    let headers = data_RequestHeader(AuthToken),
      targetURL = `${
        _PAYTRACE_ENDPOINT || PAYTRACE_ENDPOINT_CACHED
      }${_KEYED_SALE_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "sending a card transaction"
    );
  };

  /**
   * Performs a credit card payment based on a previous PayTrace transaction's information
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {number} post.amount Amount to be charged
   * @param {string|number} post.transaction_id ID of authorized PayTrace transaction being captured
   * @param {Object} post.billing_address Credit card billing address
   * @param {string} post.billing_address.name Credit card billing address addressee
   * @param {string} post.billing_address.street_address Credit card billing address street address
   * @param {string} post.billing_address.zip Credit card billing address zip code
   * @param {string} post.invoice_id Reference to record being paid
   * @param {number} [post.tax_amount] Amount of sales tax included in charge amount; To be omitted if sales tax is zero or not available
   * @param {string} post.customer_reference_id Customer reference value (max 50 char)
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_TransactionByIDPayment = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_TRANS_SALE_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "sending a sale by transaction id"
    );
  };

  /**
   * Performs a payment transaction based upon ACH information
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {number} post.amount Amount to be charged
   * @param {Object} post.billing_address Credit card billing address
   * @param {string} post.billing_address.name Credit card billing address addressee
   * @param {string} post.billing_address.street_address Credit card billing address street address
   * @param {string} post.billing_address.zip Credit card billing address zip code
   * @param {Object} post.check ACH checking account data
   * @param {string} post.check.account_number Account number
   * @param {string} post.check.routing_number Routing number
   * @param {string} post.invoice_id Reference to record being paid
   * @param {number} [post.tax_amount] Amount of sales tax included in charge amount; To be omitted if sales tax is zero or not available
   * @param {string} post.customer_reference_id Customer reference value (max 50 char)
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_SendACHPayment = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_ACH_SALE_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "sending ach payment"
    );
  };

  /**
   * Performs a swiped/chip EMV credit card authorization
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {number} post.amount Amount to be charged
   * @param {string} post.invoice_id Reference to record being paid
   * @param {string} post.emv_data Encoded card reader data
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_SendEMVPayment = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_EMV_SALE_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "sending emv payment"
    );
  };

  /**
   * Performs a swiped/chip EMV credit card authorization
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {number} post.amount Amount to be charged
   * @param {string} post.invoice_id Reference to record being paid
   * @param {string} post.emv_data Encoded card reader data
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_AuthEMVPayment = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_EMV_AUTH_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "sending emv auth"
    );
  };

  /**
   * Sends an email receipt based upon a PayTrace card transaction
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_SendReceiptEmail = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_EMAIL_RECEIPT_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "sending an email receipt"
    );
  };

  /**
   * Retrieves the data for a specified PayTrace transaction
   * @param {string} AuthToken PayTrace OAuth token
   * @param {string} tID PayTrace transaction ID
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_GetTransactionData = (AuthToken, tID) => {

    if (!_PAYTRACE_INTEGRATION_ID) {
      let ptData = data_GetIntegrationInfo();
      if (ptData) {
        _PAYTRACE_INTEGRATION_ID = ptData[1]?.custrecord_ng_ptid_integrator_id
      }
    }

    PAYTRACE_ENDPOINT_CACHE = cache.getCache({
      name: "PT_ENDPOINT",
      scope: cache.Scope.PUBLIC,
    });

    PAYTRACE_ENDPOINT_CACHED = PAYTRACE_ENDPOINT_CACHE.get({
      key: "PT_ENDPOINT",
      loader: handleGetPaytraceEndpointLoader,
      ttl: 57600, // 16 hours
    });

    let headers = data_RequestHeader(AuthToken),
      post = {
        transaction_id: tID,
        integrator_id: _PAYTRACE_INTEGRATION_ID,
      },
      targetURL = `${
        _PAYTRACE_ENDPOINT || PAYTRACE_ENDPOINT_CACHED
      }${_EXPORT_TRANSACTION_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "getting transaction data"
    );
  };

  /**
   *
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {string} integratorId PayTrace account integrator ID
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_GetTransactionDataByRange = (AuthToken, post, integratorId) => {
    post.integrator_id = integratorId || _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_EXPORT_TRANSACTION_RANGE_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "getting transaction data by range"
    );
  };

  /**
   * Performs a capture on an authorized credit card transaction
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {number} post.amount Amount to be charged
   * @param {string|number} post.transaction_id ID of authorized PayTrace transaction being captured
   * @param {string} [post.invoice_id] Reference to record being paid; Omit if no value to pass in to prevent error
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_GetPaymentCapture = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_CAPTURE_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "capturing a payment transaction"
    );
  };

  /**
   * Performs a keyed credit card refund authorization
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_SendCardRefundAuth = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_REFUND_AUTH_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "authorizing a refund transaction"
    );
  };

  /**
   * Performs a refund on a PayTrace transaction
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {number} post.amount Amount to be charged
   * @param {string} post.invoice_id Reference to record being paid
   * @param {number} [post.tax_amount] Amount of sales tax included in charge amount; To be omitted if sales tax is zero or not available
   * @param {Object} [post.billing_address] Credit card billing address; Not required if transaction ID is provided
   * @param {string} [post.billing_address.name] Credit card billing address addressee
   * @param {string} [post.billing_address.street_address] Credit card billing address street address
   * @param {string} [post.billing_address.zip] Credit card billing address zip code
   * @param {string} post.customer_reference_id Customer reference value (max 50 char)
   * @param {string|number} [post.transaction_id] ID of PayTrace transaction being refunded
   * @param {Object} [post.credit_card] Credit card data; Not required if transaction ID is provided
   * @param {string} [post.credit_card.number] Plaintext credit card number; Not required if transaction ID is provided; Omitted if encrypted number is provided
   * @param {string} [post.credit_card.encrypted_number] Credit card number encrypted by PayTrace encryption function; Not required if transaction ID is provided; Omitted if plaintext number is provided
   * @param {string} [post.credit_card.expiration_month] Credit card expiration month; Not required if transaction ID is provided
   * @param {string} [post.credit_card.expiration_year] Credit card expiration year; Not required if transaction ID is provided
   * @param {string} [post.csc] Plaintext credit card security code; Not required if transaction ID is provided; Omitted if encrypted security code is provided
   * @param {string} [post.encrypted_csc] Credit card security code encrypted by PayTrace encryption function; Not required if transaction ID is provided
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_SendCardRefundAuthTrans = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_REFUND_AUTH_TRANS_ENDPOINT}`,
      response = comm_CallPayTrace(
        headers,
        JSON.stringify(post),
        targetURL,
        "processing a refund transaction"
      ),
      processVoid = false;

    if (response.hasOwnProperty("response")) {
      if (response.response.hasOwnProperty("errors")) {
        if (
          Object.keys(response.response.errors).filter((x) => {
            return x === "817";
          }).length > 0
        ) {
          processVoid = true;
        }
      }
    } else if (response.hasOwnProperty("errors")) {
      if (
        Object.keys(response.errors).filter((x) => {
          return x === "817";
        }).length > 0
      ) {
        processVoid = true;
      }
    } else {
      let jtxt = JSON.stringify(response),
        errPos = jtxt.search(/"817":/);
      if (errPos > 0) {
        processVoid = true;
      }
    }
    if (processVoid) {
      return comm_VoidTransaction(AuthToken, post.transaction_id);
    } else {
      return response;
    }
  };

  /**
   * Voids a PayTrace transaction
   * @param {string} AuthToken PayTrace OAuth token
   * @param {string|number} tID PayTrace transaction ID
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_VoidTransaction = (AuthToken, tID) => {
    let headers = data_RequestHeader(AuthToken),
      post = {
        transaction_id: tID,
        integrator_id: _PAYTRACE_INTEGRATION_ID,
      },
      targetURL = `${_PAYTRACE_ENDPOINT}${_VOID_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "voiding a transaction"
    );
  };

  /**
   * Performs a keyed credit card refund
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {number} post.amount Amount to be charged
   * @param {string} post.invoice_id Reference to record being paid
   * @param {number} [post.tax_amount] Amount of sales tax included in charge amount; To be omitted if sales tax is zero or not available
   * @param {Object} [post.billing_address] Credit card billing address; Not required if transaction ID is provided
   * @param {string} [post.billing_address.name] Credit card billing address addressee
   * @param {string} [post.billing_address.street_address] Credit card billing address street address
   * @param {string} [post.billing_address.zip] Credit card billing address zip code
   * @param {string} post.customer_reference_id Customer reference value (max 50 char)
   * @param {string|number} [post.transaction_id] ID of PayTrace transaction being refunded
   * @param {Object} [post.credit_card] Credit card data; Not required if transaction ID is provided
   * @param {string} [post.credit_card.number] Plaintext credit card number; Not required if transaction ID is provided; Omitted if encrypted number is provided
   * @param {string} [post.credit_card.encrypted_number] Credit card number encrypted by PayTrace encryption function; Not required if transaction ID is provided
   * @param {string} [post.credit_card.expiration_month] Credit card expiration month; Not required if transaction ID is provided
   * @param {string} [post.credit_card.expiration_year] Credit card expiration year; Not required if transaction ID is provided
   * @param {string} [post.csc] Plaintext credit card security code; Not required if transaction ID is provided; Omitted if encrypted security code is provided
   * @param {string} [post.encrypted_csc] Credit card security code encrypted by PayTrace encryption function; Not required if transaction ID is provided
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_SendKeyedRefund = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_KEYED_REFUND_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "sending a keyed refund"
    );
  };

  /**
   * Retrieves PayTrace settled transaction batch information
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_GetBatches = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_FIND_BATCHES_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "getting batches"
    );
  };

  /**
   * Retrieves settled PayTrace transactions
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {string} post.batch_number PayTrace settlement batch number
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_GetSettledTransactions = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_GET_SETTLED_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "getting settled transactions"
    );
  };

  /**
   * Retrieves the client key required for client-side Protect.js card data encryption
   * @param {string} AuthToken PayTrace OAuth token
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_GetProtectClientKey = (AuthToken) => {
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_GET_CLIENT_KEY_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      null,
      targetURL,
      "getting Protect.js client key"
    );
  };

  /**
   * Submits a Protect.js payment
   * @param {string} AuthToken PayTrace OAuth token
   * @param {Object} post PayTrace request object
   * @param {number} post.amount Amount to be charged
   * @param {Object} post.billing_address Credit card billing address
   * @param {string} post.billing_address.name Credit card billing address addressee
   * @param {string} post.billing_address.street_address Credit card billing address street address, line 1
   * @param {string} post.billing_address.street_address2 Credit card billing address street address, line 2
   * @param {string} post.billing_address.city Credit card billing address city
   * @param {string} post.billing_address.state Credit card billing address state
   * @param {string} post.billing_address.zip Credit card billing address zip code
   * @param {string} post.invoice_id Reference to record being paid
   * @param {number} [post.tax_amount] Amount of sales tax included in charge amount; To be omitted if sales tax is zero or not available
   * @param {string} post.hpf_token HPF token generated by Protect.js card data encryption
   * @param {string} post.enc_key Encryption key generated by Protect.js card data encryption
   * @param {string} [post.integrator_id] PayTrace account integrator ID; Will be automatically applied
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_SendProtectPayment = (AuthToken, post) => {
    post.integrator_id = _PAYTRACE_INTEGRATION_ID;
    let headers = data_RequestHeader(AuthToken),
      targetURL = `${_PAYTRACE_ENDPOINT}${_PROTECT_SALE_ENDPOINT}`;
    return comm_CallPayTrace(
      headers,
      JSON.stringify(post),
      targetURL,
      "sending Protect.js sale"
    );
  };

  /**
   * Handles connection requests to PayTrace API endpoints
   * @param {Object} headers PayTrace request headers
   * @param {Object} body PayTrace request object
   * @param {string} url PayTrace request URL
   * @param {string} msg Text string of action being performed for logging/tracing purposes
   * @returns {*|{err: SuiteScriptError|undefined, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: *[]|string, hasErr: boolean}|{err: *, response: *, details: [*], hasErr: boolean}}
   */
  const comm_CallPayTrace = (headers, body, url, msg) => {
    log.audit({ title: "making call to PayTrace", details: msg });
    let request;
    try {
      if (!NG.tools.isEmpty(body)) {
        request = https.post({
          url: url,
          body: body,
          headers: headers,
        });
      } else {
        request = https.post({
          url: url,
          headers: headers,
        });
      }
    } catch (err) {
      NG.log.logError(err, `Error encountered connecting to PT for ${msg}`);
      return null;
    }

    if (!NG.tools.isEmpty(request)) {
      let responseRAW = request.body,
        response;

      if (Number(request.code) === 200) {
        try {
          response = JSON.parse(responseRAW);
        } catch (err) {
          response = responseRAW;
        }

        response.isRefund = msg === "processing a refund transaction";
        response.isVoid = msg === "voiding a transaction";
        if (response.success || !NG.tools.isEmpty(response.clientKey)) {
          return response;
        } else {
          if (msg === "authorizing a card transaction") {
            let errAuth = err_PayTraceError(
                response,
                `Error encountered ${msg}`,
                true
              ),
              det = err_PayTraceMessage(response, true);
            NG.log.logError(
              errAuth,
              "Error encountered authorizing credit card (PT)"
            );
            return {
              hasErr: true,
              err: errAuth,
              details: det,
              response: response,
            };
          } else {
            err_PayTraceError(response, `Error encountered ${msg}`);
            return null;
          }
        }
      } else {
        let payTraceMessage, payTraceDetails;
        try {
          try {
            response = JSON.parse(responseRAW);
          } catch (err) {
            response = responseRAW;
          }
          payTraceMessage = err_PayTraceMessage(response);
          payTraceDetails = err_PayTraceMessage(response, true);
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered building PayTrace failure message"
          );
        }

        if (
          Number(response.response_code) === 185 ||
          JSON.stringify(response.errors || {}).search(/\[185\]/) >= 0
        ) {
          let token = headers.Authorization.replace("Bearer ", ""),
            checkDate = NG.time.formatDateTime(new Date(), "MM/DD/YYYY", false),
            vPkg = {
              start_date: checkDate,
              end_date: checkDate,
              including_text: body.invoice_id,
            },
            vReq;

          try {
            vReq = comm_GetTransactionDataByRange(token, vPkg);
          } catch (err) {
            NG.log.logError(
              err,
              "Error encountered verifying transaction submission"
            );
          }

          if (!NG.tools.isEmpty(vReq)) {
            let vResRAW = vReq.body,
              vRes;
            try {
              vRes = JSON.parse(vResRAW);
            } catch (e) {
              vRes = vResRAW;
            }

            if (vRes.hasOwnProperty("transactions")) {
              if (vRes.transactions.length > 0) {
                //
              }
            }
          }
        }

        response.isRefund = msg === "processing a refund transaction";
        response.isVoid = msg === "voiding a transaction";

        let errCodeList = [],
          err;
        if (!NG.tools.isEmpty(response.response_code)) {
          errCodeList.push(Number(response.response_code));
        }
        if (response.hasOwnProperty("errors")) {
          Object.keys(response.errors).forEach((errCode) => {
            errCodeList.push(Number(errCode));
          });
        }

        if (
          Number(response.response_code) === 817 ||
          JSON.stringify(response.errors || {}).search(/\[817\]/) >= 0
        ) {
          response.errCodeList = errCodeList;
          return response;
        } else if (!NG.tools.isEmpty(payTraceMessage)) {
          err = error.create({
            name: "PAYTRACE_ERROR",
            message: `PayTrace could not process the request || ${payTraceMessage}`,
          });
          NG.log.logError(err, "Error encountered processing PayTrace request");
          return {
            hasErr: true,
            err,
            details: payTraceDetails,
            response,
            errCodeList,
          };
        } else {
          err = error.create({
            name: "HTTP_ERROR",
            message: `The PayTrace servers returned a response code of ${
              request.code
            } || ${request.getHeader("Status")}`,
          });
          NG.log.logError(err, "Error encountered processing PayTrace request");
          return {
            hasErr: true,
            err,
            details: [`${request.getCode()} || ${request.getHeader("Status")}`],
            response,
            errCodeList,
          };
        }
      }
    } else {
      return null;
    }
  };

  /**
   * Connects to NewGen licensing endpoint to retrieve licensing data
   * @param {string} key
   * @returns {Object}
   */
  const comm_ObtainLicenseInfo = (key) => {
    key = key || _CURRENT_LICENSE_KEY;
    let keyData = { license: {} },
      retrieved = false,
      targetURL = _LICENSE_URL.NG_Format(key),
      request;

    try {
      request = https.post({
        url: targetURL,
      });
    } catch (err) {
      if (err.name === "SSS_UNKNOWN_HOST") {
        log.audit({
          title: "comm_ObtainLicenseInfo",
          details:
            "A NetSuite system error prevented retrieval of license information; License has been automatically validated",
        });
        log.audit({
          title: "comm_ObtainLicenseInfo -- RESPONSE",
          details: request.body,
        });
        keyData.license = {
          key: key,
          expiration: "12/31/2051",
          valid: true,
          features: {},
        };
        retrieved = true;
      } else {
        NG.log.logError(
          err,
          "Error encountered retrieving license information"
        );
        return false;
      }
    }

    if (!NG.tools.isEmpty(request)) {
      let responseRAW = request.body,
        response;
      if (request.code === 200) {
        try {
          response = JSON.parse(responseRAW);
        } catch (err) {
          response = responseRAW;
        }

        if (!NG.tools.isEmpty(response)) {
          let loArr = response.licenseObject;
          if (!NG.tools.isEmpty(loArr) && loArr.length > 0) {
            let license =
                loArr[0].custrecord_license_key ||
                "00000000-0000-0000-0000-000000000000",
              exp = loArr[0].custrecord_renewal_expiry_date || "1/1/1970",
              feat = loArr[0].custrecord_license_key_feature_json || {},
              now = new Date(),
              dNow = new Date(
                now.getFullYear(),
                now.getMonth(),
                now.getDate(),
                0,
                0,
                0,
                0
              ),
              dExp = format.parse({ value: exp, type: "date" });

            if (util.isString(dExp)) {
              let expComps = exp.split("/");
              dExp = new Date(
                Number(expComps[2]),
                Math.round(Number(expComps[0]) - 1),
                Number(expComps[1]),
                0,
                0,
                0,
                0
              );
            }

            keyData.license = {
              key: license,
              expiration: exp,
              valid: dExp.getTime() > dNow.getTime(),
              features: feat,
            };
            retrieved = true;
          } else {
            keyData.license = {
              key: "",
              expiration: "",
              valid: false,
            };
            retrieved = true;
          }
        }
      } else if (request.code === 500) {
        log.audit({
          title: "comm_ObtainLicenseInfo",
          details:
            "A server error prevented retrieval of license information; License has been automatically validated",
        });
        log.audit({
          title: "comm_ObtainLicenseInfo -- RESPONSE",
          details: request.body,
        });
        keyData.license = {
          key: key,
          expiration: "12/31/2050",
          valid: true,
          features: {},
        };
        retrieved = true;
      }
    }

    _CURRENT_LICENSE_KEY = null;
    if (!retrieved) {
      log.audit({
        title: "comm_ObtainLicenseInfo",
        details: `license info not retrieved -- key: ${key}`,
      });
      keyData.license = { key: "", expiration: "", valid: false };
      _NOT_RETRIEVED = true;
    } else {
      _NOT_RETRIEVED = false;
    }

    require(["N/cache"], (cache) => {
      let ptCache = cache.getCache({
        name: _PAYTRACE_CACHE_NAME,
        scope: cache.Scope.PROTECTED,
      });
      ptCache.put({
        key: _PAYTRACE_CACHE_INDEX,
        value: keyData.license,
        ttl: 18 * 60 * 60,
      });
    });

    return keyData.license;
  };

  const comm_SendCustomerEmail = (options) => {
    try {
      require(["N/email", "N/render"], (email, render) => {
        let emailRender = render.mergeEmail({
          templateId: options.templateId,
          transactionId: options.mergeId,
        });

        email.send({
          author: _SETTINGS.custrecord_ng_pts_rcr_bill_c_email_sendr,
          recipients: options.recipientId,
          bcc: options.bcc || null,
          subject: emailRender.subject,
          body: emailRender.body,
          relatedRecords: {
            transactionId: parseInt(Number(options.attachId).toFixed(0)),
          },
        });
      });
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered sending AutoPay notification email to customer",
        options
      );
    }
  };

  /**
   *
   * @param {Object} options
   * @param {String} options.AuthToken
   * @param {String} options.profId
   * @param {String} options.cardName
   * @param {String} options.billStreet
   * @param {String} options.billZip
   * @param {String} options.encNumber
   * @param {String} options.encCSC
   * @param {String} options.expMonth
   * @param {String} options.expYear
   */
  const comm_PerformCardAuthAndVoid = (options) => {
    if (
      NG.tools.isEmpty(options.AuthToken) &&
      !NG.tools.isEmpty(options.profId)
    ) {
      options.AuthToken = comm_GetPayTraceAuth(options.profId);
    }

    let cardAuthRequestPost = {
      amount: 1.01,
      invoice_id: "ap card auth",
      customer_reference_id: "ap card auth",
      billing_address: {
        name: options.cardName,
        street_address: options.billStreet,
        zip: options.billZip,
      },
      credit_card: {
        encrypted_number: options.encNumber,
        expiration_month: options.expMonth,
        expiration_year: options.expYear,
      },
    };
    if (!NG.tools.isEmpty(options.encCSC)) {
      cardAuthRequestPost.encrypted_csc = options.encCSC;
    }

    let altRequestPost = data_CreateAltRequestPost(cardAuthRequestPost),
      cardAuthRequest = comm_AuthCardPayment(
        options.AuthToken,
        cardAuthRequestPost
      ),
      cardAuthErr;

    if (!NG.tools.isEmpty(cardAuthRequest)) {
      if (cardAuthRequest.hasErr) {
        try {
          data_CreateFailurePaymentRef({
            custId: options.custId,
            PayTraceProf: options.profId,
            ccRequest: cardAuthRequest,
            altRequestPost: altRequestPost,
            formId: _SETTINGS[SHARED.SettingsMap.general.refRecFailForm],
            tType: "Card",
          });

          log.audit({
            title: "AutoPay Manual Card Entry Auth Failure",
            details:
              "Card authorization failed; Reference created; Terminating process",
          });
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered creating failed PT transaction record ref"
          );
        }
      } else {
        try {
          comm_VoidTransaction(
            options.AuthToken,
            cardAuthRequest.transaction_id
          );
          log.audit({ title: "auth transaction voided", details: "" });
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered attempting to void card auth"
          );
          cardAuthErr = { name: err.name, message: err.message };
        }
      }
    }

    return {
      requestPost: altRequestPost,
      authResponse: cardAuthRequest,
      err: cardAuthErr,
    };
  };

  //endregion

  //region DATA

  /**
   * Sets connection global variables based upon the defined integration definition record internal ID
   * @param {string} intId Integration definition record internal ID
   * @returns {Object}
   */
  const data_GetPayTraceInfo = (intId) => {
    PAYTRACE_ENDPOINT_CACHE = cache.getCache({
      name: "PT_ENDPOINT",
      scope: cache.Scope.PUBLIC,
    });

    _PAYTRACE_DATA = search.lookupFields({
      type: "customrecord_ng_paytrace_integration",
      id: Number(Number(intId).toFixed(0)),
      columns: [
        "custrecord_ng_ptid_integrator_id",
        "custrecord_ng_ptid_user_id",
        "custrecord_ng_ptid_password",
        "custrecord_ng_ptid_is_sandbox",
      ],
    });

    if (
      runtime.envType !== runtime.EnvType.PRODUCTION &&
      !_PAYTRACE_DATA.custrecord_ng_ptid_is_sandbox
    ) {
      throw error.create({
        name: "PAYTRACE_ERROR",
        message:
          "Cannot connect to production PayTrace account from non-production NetSuite account.",
      });
    }

    _PAYTRACE_INTEGRATION_ID = _PAYTRACE_DATA.custrecord_ng_ptid_integrator_id;
    _PAYTRACE_USERNAME = _PAYTRACE_DATA.custrecord_ng_ptid_user_id;
    _PAYTRACE_PASSWORD = _PAYTRACE_DATA.custrecord_ng_ptid_password;
    _PAYTRACE_ENDPOINT = _PAYTRACE_DATA.custrecord_ng_ptid_is_sandbox
      ? _PT_ENDPOINT_SB
      : _PT_ENDPOINT;

    PAYTRACE_ENDPOINT_CACHED = PAYTRACE_ENDPOINT_CACHE.get({
      key: "PT_ENDPOINT",
      loader: handleGetPaytraceEndpointLoader,
      ttl: 57600, // 16 hours
    });

    return _PAYTRACE_DATA;
  };

  /**
   * Creates the required PayTrace connection headers
   * @param {string} AuthToken PayTrace OAuth token
   * @returns {Object}
   */
  const data_RequestHeader = (AuthToken) => {
    return {
      Authorization: `Bearer ${AuthToken}`,
      "Content-Type": "application/json",
      "Cache-Control": "no-cache",
    };
  };

  /**
   * Creates error object based upon received PayTrace information
   * @param {Object} response Parsed response object from PayTrace
   * @param errTitle Text to use for creating error log title
   * @param returnErr If true, returns the generated error back to the calling function; Otherwise throws the generated error
   * @returns {SuiteScriptError}
   */
  const err_PayTraceError = (response, errTitle, returnErr) => {
    let errBuild = [];
    if (Number(response.response_code) === 1) {
      Object.keys(response.errors).forEach((errCode) => {
        errBuild.push(`[${errCode}] ${response.errors[errCode][0]}`);
      });
    } else {
      errBuild.push(
        `[${response.response_code}] ${response.status_message}${
          !NG.tools.isEmpty(response.approval_message)
            ? " - {0}".NG_Format(response.approval_message)
            : ""
        }`
      );
    }
    let errPE = error.create({
      name: "PAYTRACE_ERROR",
      message: `The PayTrace request failed with the following errors: \r\n${errBuild.join(
        " \r\n"
      )}`,
    });
    NG.log.logError(errPE, errTitle);
    if (returnErr) {
      return errPE;
    } else {
      throw errPE;
    }
  };

  /**
   * Creates response message based upon received PayTrace information
   * @param {Object} response Parsed response object from PayTrace
   * @param {boolean} [rtrnArr] If true, returns an array instead of a string
   * @returns {string|*[]}
   */
  const err_PayTraceMessage = (response, rtrnArr) => {
    rtrnArr = rtrnArr || false;
    let msgBuild = [];
    if (Number(response.response_code) === 1) {
      Object.keys(response.errors).forEach((errCode) => {
        msgBuild.push(`[${errCode}] ${response.errors[errCode][0]}`);
      });
    } else {
      msgBuild.push(
        `[${response.response_code}] ${response.status_message}${
          !NG.tools.isEmpty(response.approval_message)
            ? " - {0}".NG_Format(response.approval_message)
            : ""
        }`
      );
    }
    if (rtrnArr) {
      return msgBuild;
    } else {
      return msgBuild.join(", ");
    }
  };

  /**
   * Builds an object containing data for all of the active PayTrace integration profile records
   * @returns {Object: PT_Profile}
   */
  const data_GetIntegrationInfo = () => {
    /** @type Object: PT_Profile */
    let ptidData = {};
    ptidData["EMPTY"] = {
      name: "EMPTY",
      id: "0",
      custrecord_ng_ptid_card_methods: [],
      custrecord_ng_ptid_allow_keyed_refunds: false,
      custrecord_ng_ptid_force_keyed_refunds: false,
      custrecord_ng_ptid_is_default: false,
      custrecord_ng_ptid_is_sandbox: false,
      custrecord_ng_ptid_disabled: true,
      custrecord_ng_ptid_default_account: "",
      custrecord_ng_ptid_ach_enabled: false,
      custrecord_ng_ptid_pub_key_file: {
        internalid: "0",
        url: "",
      },
    };

    let ptidFileList = [],
      ptidFilt = [["isinactive", "is", "F"]],
      ptidCols = [
        search.createColumn({ name: "name" }),
        search.createColumn({
          name: "custrecord_ng_ptid_card_methods",
        }),
        search.createColumn({
          name: "custrecord_ng_ptid_allow_keyed_refunds",
        }),
        search.createColumn({
          name: "custrecord_ng_ptid_force_keyed_refunds",
        }),
        search.createColumn({ name: "custrecord_ng_ptid_is_default" }),
        search.createColumn({ name: "custrecord_ng_ptid_is_sandbox" }),
        search.createColumn({ name: "custrecord_ng_ptid_disabled" }),
        search.createColumn({
          name: "custrecord_ng_ptid_default_account",
        }),
        search.createColumn({ name: "custrecord_ng_ptid_ach_enabled" }),
        search.createColumn({
          name: "custrecord_ng_ptid_pub_key_file",
        }),
        search.createColumn({
          name: "custrecord_ng_ptid_integrator_id",
        }),
      ],
      results;

    try {
      results = NG.tools.getSearchResults(
        "customrecord_ng_paytrace_integration",
        ptidFilt,
        ptidCols
      );
    } catch (err) {
      NG.log.logError(err, "Error encountered retrieving integration data");
    }

    if (!NG.tools.isEmpty(results)) {
      results.forEach((res) => {
        let fileIdA = res.getValue({
          name: "custrecord_ng_ptid_pub_key_file",
        });
        if (!ptidFileList.includes(fileIdA)) {
          ptidFileList.push(fileIdA);
        }
      });

      if (ptidFileList.length > 0) {
        let fileFilt = [["internalid", "anyof", ptidFileList]],
          fileCols = [search.createColumn({ name: "url" })],
          fileSearch;

        try {
          fileSearch = NG.tools.getSearchResults("file", fileFilt, fileCols);
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered searching for public key files"
          );
        }

        if (!NG.tools.isEmpty(fileSearch)) {
          results.forEach((res) => {
            let meths = res
                .getValue({
                  name: "custrecord_ng_ptid_card_methods",
                })
                .split(","),
              methsText = res
                .getText({
                  name: "custrecord_ng_ptid_card_methods",
                })
                .split(","),
              fileIdB = res.getValue({
                name: "custrecord_ng_ptid_pub_key_file",
              }),
              fileObj = {};

            for (let f = 0; f < fileSearch.length; f++) {
              if (Number(fileSearch[f].id) === Number(fileIdB)) {
                fileObj = {
                  internalid: fileSearch[f].id,
                  url: fileSearch[f].getValue({
                    name: "url",
                  }),
                };
                break;
              }
            }

            ptidData[res.id] = {
              name: res.getValue({ name: "name" }),
              id: res.id,
              custrecord_ng_ptid_card_methods: meths,
              custrecord_ng_ptid_card_methods_text: methsText,
              custrecord_ng_ptid_allow_keyed_refunds: res.getValue({
                name: "custrecord_ng_ptid_allow_keyed_refunds",
              }),
              custrecord_ng_ptid_force_keyed_refunds: res.getValue({
                name: "custrecord_ng_ptid_force_keyed_refunds",
              }),
              custrecord_ng_ptid_is_default: res.getValue({
                name: "custrecord_ng_ptid_is_default",
              }),
              custrecord_ng_ptid_is_sandbox: res.getValue({
                name: "custrecord_ng_ptid_is_sandbox",
              }),
              custrecord_ng_ptid_disabled: res.getValue({
                name: "custrecord_ng_ptid_disabled",
              }),
              custrecord_ng_ptid_default_account: res.getValue({
                name: "custrecord_ng_ptid_default_account",
              }),
              custrecord_ng_ptid_ach_enabled: res.getValue({
                name: "custrecord_ng_ptid_ach_enabled",
              }),
              custrecord_ng_ptid_integrator_id: res.getValue({
                name: "custrecord_ng_ptid_integrator_id"
              }),
              custrecord_ng_ptid_pub_key_file: fileObj,
            };
          });
        }
      }
    }

    return ptidData;
  };

  /**
   *
   * @param {Object} options
   * @param {Object} options.profiles
   * @param {string} options.profileId
   * @returns {PT_Profile}
   */
  const data_SelectProfile = (options) => {
    return options.profiles[options.profileId];
  };

  /**
   * Retrieves license information from the cache; Makes call to licensing endpoint if necessary
   * @param {string} key
   * @returns {Object}
   */
  const data_GetLicenseValidation = (key) => {
    let licenseInfo;
    _CURRENT_LICENSE_KEY = key;
    try {
      require(["N/cache"], (cache) => {
        let ptCache = cache.getCache({
          name: _PAYTRACE_CACHE_NAME,
          scope: cache.Scope.PROTECTED,
        });
        licenseInfo = ptCache.get({
          key: _PAYTRACE_CACHE_INDEX,
          loader: comm_ObtainLicenseInfo,
        });
        if (NG.tools.isEmpty(JSON.parse(licenseInfo).key) && !_NOT_RETRIEVED) {
          licenseInfo = JSON.stringify(comm_ObtainLicenseInfo(key));
        }
        _NOT_RETRIEVED = false;
      });
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered attempting to validate license key"
      );
      licenseInfo = {
        key: key,
        expiration: "12/31/2050",
        valid: true,
        features: {},
      };
    }
    return JSON.parse(licenseInfo || "{ }");
  };

  /**
   * Retrieves the customer's available credit card data based upon the indicated PayTrace integration profile record ID
   * @param cId Customer record internal ID
   * @param ptProfile PayTrace integration profile record ID
   * @returns {Object[]}
   */
  const data_GetCreditCards = (cId, ptProfile) => {
    let cardList = [],
      edFilt = [
        ["isinactive", "is", "F"],
        "and",
        ["custrecord_ng_ptecd_customer", "anyof", [cId]],
        "and",
        ["custrecord_ng_ptecd_applied_profile", "anyof", [ptProfile]],
      ],
      edCols = [
        search.createColumn({
          name: "custrecord_ng_ptecd_card_id",
          summary: search.Summary.GROUP,
        }),
      ],
      edSearch;

    try {
      edSearch = NG.tools.getSearchResults(
        "customrecord_ng_pt_ecrypted_card",
        edFilt,
        edCols
      );
    } catch (err) {
      NG.log.logError(err, "Error encountered retrieving encrypted card data");
    }

    if (!NG.tools.isEmpty(edSearch)) {
      let ccFilt = [["internalid", "anyof", [cId]]],
        ccCols = [
          search.createColumn({ name: "entityid" }),
          search.createColumn({ name: "ccnumber" }),
          search.createColumn({ name: "ccinternalid" }),
          search.createColumn({ name: "ccexpdate" }),
          search.createColumn({ name: "cctype" }),
          search.createColumn({ name: "ccdefault" }),
          search.createColumn({ name: "ccholdername" }),
          search.createColumn({ name: "cccustomercode" }),
        ],
        ccSearch;

      try {
        ccSearch = NG.tools.getSearchResults("customer", ccFilt, ccCols);
      } catch (err) {
        NG.log.logError(err, "Error encountered retrieving customer card data");
      }

      if (!NG.tools.isEmpty(ccSearch)) {
        ccSearch.forEach((cRes) => {
          edSearch.forEach((eRes) => {
            if (
              eRes.getValue({
                name: "custrecord_ng_ptecd_card_id",
                summary: search.Summary.GROUP,
              }) === cRes.getValue({ name: "ccinternalid" })
            ) {
              let ccMemo = !NG.tools.isEmpty(
                cRes.getValue({ name: "cccustomercode" })
              )
                ? ` [${cRes.getValue({
                    name: "cccustomercode",
                  })}]`
                : "";
              cardList.push({
                value: cRes.getValue({ name: "ccinternalid" }),
                text: `${cRes.getText({
                  name: "cctype",
                })} ${cRes
                  .getValue({ name: "ccnumber" })
                  .substr(-6)} (${cRes.getValue({
                  name: "ccexpdate",
                })})${ccMemo}`,
                isDefault:
                  _SETTINGS[SHARED.SettingsMap.general.autoSetDefaultCC] &&
                  cRes.getValue({ name: "ccdefault" }),
                markedDefault: cRes.getValue({
                  name: "ccdefault",
                }),
                name: cRes.getValue({ name: "ccholdername" }),
                ccExp: cRes.getValue({ name: "ccexpdate" }),
                type: cRes.getText({ name: "cctype" }),
              });
            }
          });
        });
      }
    }

    return cardList;
  };

  /**
   * Attempts to determine the default PayTrace integration profile record ID
   * @returns {string|number}
   */
  const data_getProfile = () => {
    let ptProfile;

    try {
      let ptProfFilt = [
          ["isinactive", "is", "F"],
          "and",
          ["custrecord_ng_ptid_disabled", "is", "F"],
        ],
        ptProfCols = [
          search.createColumn({ name: "internalid" }),
          search.createColumn({ name: "name" }),
          search.createColumn({
            name: "custrecord_ng_ptid_is_default",
          }),
        ],
        ptProfSearch;

      try {
        ptProfSearch = NG.tools.getSearchResults(
          "customrecord_ng_paytrace_integration",
          ptProfFilt,
          ptProfCols
        );
      } catch (err) {
        NG.log.logError(err, "Error encountered retrieving profile data");
      }

      if (!NG.tools.isEmpty(ptProfSearch)) {
        if (ptProfSearch.length === 1) {
          ptProfile = ptProfSearch[0].id;
        } else if (ptProfSearch.length > 1) {
          ptProfSearch.forEach((res) => {
            if (
              res.getValue({
                name: "custrecord_ng_ptid_is_default",
              })
            ) {
              ptProfile = res.id;
            }
          });
        }
      }
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered setting up PayTrace profile selection"
      );
    }

    return ptProfile;
  };

  /**
   * Cleans up PayTrace request object for logging; Removes/masks sensitive information
   * @param {Object} rObj PayTrace request object
   * @returns {Object}
   */
  const data_provideCleanRequestObject = (rObj) => {
    let altRequestPost = JSON.parse(JSON.stringify(rObj));
    if (!NG.tools.isEmpty(altRequestPost.credit_card)) {
      if (!NG.tools.isEmpty(altRequestPost.credit_card.number)) {
        altRequestPost.credit_card.number = altRequestPost.credit_card.number
          .substring(-4, 4)
          .NG_paddingLeft("****************");
      } else if (
        !NG.tools.isEmpty(altRequestPost.credit_card.encrypted_number)
      ) {
        altRequestPost.credit_card.encrypted_number = "************";
      }
    }
    if (!NG.tools.isEmpty(altRequestPost.csc)) {
      altRequestPost.csc = "***";
    }
    if (!NG.tools.isEmpty(altRequestPost.encrypted_csc)) {
      altRequestPost.encrypted_csc = "************";
    }
    if (!NG.tools.isEmpty(altRequestPost.emv_data)) {
      altRequestPost.emv_data = "************";
    }
    return altRequestPost;
  };

  /**
   * Decodes base64-encoded data passed along in session variable
   * @param {string} encodedSessData
   * @returns {Object}
   */
  const data_DecodeSessionData = (encodedSessData) => {
    let initDecoded = NG.tools.B64.decode(encodedSessData);
    if (initDecoded.indexOf("{") >= 0 && initDecoded.lastIndexOf("}") >= 0) {
      let finalDecoded = initDecoded.substr(
        0,
        initDecoded.lastIndexOf("}") + 1
      );
      return JSON.parse(finalDecoded);
    } else {
      return initDecoded;
    }
  };

  /**
   * Updates customer record with new card information and generates encrypted card data record
   * @param {Object} sessData Contains credit card data to be updated onto customer record
   * @param {Date} cardExpDate Credit card expiration data object
   * @returns {void}
   */
  const data_UpdateCardsOnCustomer = (sessData, cardExpDate) => {
    let cRec = record.load({
        type: "customer",
        id: sessData.h,
        isDynamic: true,
      }),
      cardIdList = [],
      lineCountA = cRec.getLineCount({ sublistId: "creditcards" });

    for (let cA = 0; cA < lineCountA; cA++) {
      cardIdList.push(
        cRec.getSublistValue({
          sublistId: "creditcards",
          fieldId: "internalid",
          line: cA,
        })
      );
    }
    cRec.selectNewLine({ sublistId: "creditcards" });
    cRec.setCurrentSublistValue({
      sublistId: "creditcards",
      fieldId: "paymentmethod",
      value: sessData.d,
    });
    cRec.setCurrentSublistValue({
      sublistId: "creditcards",
      fieldId: "ccnumber",
      value: sessData.b,
    });
    cRec.setCurrentSublistValue({
      sublistId: "creditcards",
      fieldId: "ccname",
      value: sessData.a,
    });
    if (!NG.tools.isEmpty(cardExpDate)) {
      cRec.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "ccexpiredate",
        value: cardExpDate,
      });
    } else {
      let eDateSplit = sessData.e.split(/\//);
      cardExpDate = new Date(Number(eDateSplit[1]), Number(eDateSplit[0]), 0);
      cRec.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "ccexpiredate",
        value: cardExpDate,
      });
    }
    cRec.commitLine({ sublistId: "creditcards" });
    cRec.save({ enableSourcing: true, ignoreMandatoryFields: true });

    cRec = record.load({
      type: "customer",
      id: sessData.h,
      isDynamic: true,
    });
    let cardId,
      lineCountB = cRec.getLineCount({ sublistId: "creditcards" });
    for (let cB = 0; cB < lineCountB; cB++) {
      if (
        !cardIdList.includes(
          cRec.getSublistValue({
            sublistId: "creditcards",
            fieldId: "internalid",
            line: cB,
          })
        )
      ) {
        cardId = cRec.getSublistValue({
          sublistId: "creditcards",
          fieldId: "internalid",
          line: cB,
        });
      }
    }

    if (!NG.tools.isEmpty(cardId)) {
      let cardDataRec = record.create({
        type: "customrecord_ng_pt_ecrypted_card",
      });
      cardDataRec.setValue({
        fieldId: "custrecord_ng_ptecd_customer",
        value: sessData.h,
      });
      cardDataRec.setValue({
        fieldId: "custrecord_ng_ptecd_card_id",
        value: Number(cardId).toFixed(0),
      });
      cardDataRec.setValue({
        fieldId: "custrecord_ng_ptecd_encrypted_card",
        value: sessData.f,
      });
      cardDataRec.setValue({
        fieldId: "custrecord_ng_ptecd_encypted_csc",
        value: sessData.g,
      });
      cardDataRec.setValue({
        fieldId: "custrecord_ng_ptecd_applied_profile",
        value: sessData.i,
      });
      cardDataRec.save({
        enableSourcing: true,
        ignoreMandatoryFields: true,
      });
    }
  };

  /**
   * Gets available PayTrace profile records for customer, determines default profile to use, adds available credit cards to card selection field
   * @param {Object} _PT_DATA PayTrace integration profile data object
   * @param {string|number} cId Customer record internal ID
   * @param {Field} ptCardSelectField Credit card selection field object
   * @param {Object} context Credit card selection field object
   * @returns {{custDefProfile, defProfile: string, ccIdList: *[]}}
   */
  const data_DetermineProfile = (_PT_DATA, cId, ptCardSelectField, context) => {
    let defProfile,
      custDefProfile,
      defCardId = "",
      ccIdList = [];

    if (!NG.tools.isEmpty(cId)) {
      custDefProfile = NG.tools.getLookupFields(
        "customer",
        cId,
        [SHARED.Mappings.fields.entity.defaultProfile],
        [SHARED.Mappings.fields.entity.defaultProfile],
        []
      )[SHARED.Mappings.fields.entity.defaultProfile];
      if (!NG.tools.isEmpty(custDefProfile)) {
        Object.keys(_PT_DATA).forEach((profId) => {
          if (
            Number(profId) === Number(custDefProfile) &&
            NG.tools.isEmpty(defProfile)
          ) {
            let p = _PT_DATA[profId];
            if (
              !p.custrecord_ng_ptid_disabled &&
              !NG.tools.isEmpty(p.custrecord_ng_ptid_default_account)
            ) {
              defProfile = profId;
            }
          }
        });
      }
    }

    if (NG.tools.isEmpty(defProfile)) {
      Object.keys(_PT_DATA).forEach((profId) => {
        if (profId !== "EMPTY" && NG.tools.isEmpty(defProfile)) {
          let p = _PT_DATA[profId];
          if (
            !p.custrecord_ng_ptid_disabled &&
            p.custrecord_ng_ptid_is_default
          ) {
            defProfile = profId;
          }
        }
      });
    }

    if (!NG.tools.isEmpty(cId) && !NG.tools.isEmpty(defProfile)) {
      let isRefund = ["customerrefund", "cashrefund"].includes(
          context.newRecord.type
        ),
        cardData = data_GetCreditCards(cId, defProfile),
        useDefaultCard =
          _SETTINGS[SHARED.SettingsMap.general.autoSetDefaultCC] && !isRefund;

      for (let cc = 0; cc < cardData.length; cc++) {
        let isSelected = useDefaultCard ? cardData[cc].isDefault : false;
        ptCardSelectField.addSelectOption({
          value: cardData[cc].value,
          text: cardData[cc].text,
          isSelected,
        });
        ccIdList.push(cardData[cc].value);
        if (isSelected) {
          defCardId = cardData[cc].value;
        }
      }
    }

    let ptProfSelectField = context.form.addField({
      id: SHARED.Mappings.fields.ALL.Body.ProfileSelect,
      type: serverWidget.FieldType.SELECT,
      label: _SETTINGS[SHARED.SettingsMap.general.profileSelectLabel],
      container: "payment",
    });
    ptProfSelectField.addSelectOption({
      value: "",
      text: "",
      isSelected: true,
    });
    ptProfSelectField.setHelpText({
      help: "Select a PayTrace profile to process this credit card transaction.",
    });
    Object.keys(_PT_DATA).forEach((profId) => {
      if (profId !== "EMPTY") {
        let p = _PT_DATA[profId];
        if (!p.custrecord_ng_ptid_disabled) {
          ptProfSelectField.addSelectOption({
            value: p.id,
            text: p.name,
            isSelected: profId === defProfile || false,
          });
        }
      }
    });

    return {
      defProfile,
      custDefProfile,
      ccIdList,
      ptProfSelectField,
      defCardId,
    };
  };

  const data_GetMemorizedCashSaleData = (options) => {
    let copyCardId,
      copyCardPayMeth,
      copyCardExp,
      isMemCopy = false,
      copyRecId,
      memDocId;

    try {
      if (!NG.tools.isEmpty(options.context.request)) {
        isMemCopy = !NG.tools.isEmpty(
          options.context.request.parameters["memdoc"]
        );
        memDocId = options.context.request.parameters["memdoc"];
        copyRecId = options.context.request.parameters["id"];
      } else {
        let queryText = options.context.newRecord.getValue({
          fieldId: "entryformquerystring",
        });
        if (!NG.tools.isEmpty(queryText)) {
          queryText.split("&").forEach((param) => {
            let parms = param.split("=");
            log.audit({ title: "mem cs query", details: parms });
            if (parms[0] === "memdoc" && !NG.tools.isEmpty(parms[1])) {
              isMemCopy = true;
              memDocId = parms[1];
            } else if (parms[0] === "id" && !NG.tools.isEmpty(parms[1])) {
              copyRecId = parms[1];
            }
          });
        }
      }

      if (isMemCopy && !NG.tools.isEmpty(copyRecId)) {
        let csLookupList = [SHARED.Mappings.fields.transaction.dataRefRec],
          csLookupList_S = [SHARED.Mappings.fields.transaction.dataRefRec],
          memData = NG.tools.getLookupFields(
            "transaction",
            copyRecId,
            csLookupList,
            csLookupList_S,
            []
          ),
          refRecId = (memData || {})[
            SHARED.Mappings.fields.transaction.dataRefRec
          ];

        if (!NG.tools.isEmpty(refRecId)) {
          let refLookupList = [
              SHARED.Mappings.fields.pymtRef.cardId,
              SHARED.Mappings.fields.pymtRef.cardPayMeth,
              SHARED.Mappings.fields.pymtRef.cardExp,
            ],
            refLookupList_S = [],
            refRecData = NG.tools.getLookupFields(
              SHARED.Mappings.recordTypes.pymtRef,
              refRecId,
              refLookupList,
              refLookupList_S,
              []
            );

          copyCardId = (refRecData || {})[
            SHARED.Mappings.fields.pymtRef.cardId
          ];
          copyCardPayMeth = (refRecData || {})[
            SHARED.Mappings.fields.pymtRef.cardPayMeth
          ];
          copyCardExp = (refRecData || {})[
            SHARED.Mappings.fields.pymtRef.cardExp
          ];
        }
      }
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered retrieving credit card ID from memorized cash sale"
      );
    }

    return {
      isMemCopy,
      copyRecId,
      copyCardId,
      copyCardPayMeth,
      copyCardExp,
      memDocId,
    };
  };

  /**
   * @summary Generates a payment reference record for a failed payment attempt
   *
   * @param {Object} options
   * @param {number|string} options.formId
   * @param {number|string} options.invId
   * @param {number|string} options.soId
   * @param {number|string} options.custId
   * @param {number|string} options.PayTraceProf
   * @param {number|string} options.tType
   * @param {string} [options.approvalCode]
   * @param {Object} options.ccRequest
   * @param {Object} options.altRequestPost
   * @return {void}
   */
  const data_CreateFailurePaymentRef = (options) => {
    let payCompRec = record.create({
      type: "customrecord_ng_paytrace_ref",
      isDynamic: true,
      defaultValues: { customform: options.formId },
    });

    if (!NG.tools.isEmpty(options.invId)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_invoice",
        value: options.invId,
      });
    }
    if (!NG.tools.isEmpty(options.soId)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_sales_order",
        value: options.soId,
      });
    }
    if (!NG.tools.isEmpty(options.custId)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_customer",
        value: options.custId,
      });
    }
    if (!NG.tools.isEmpty(options.approvalCode)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_approval_code",
        value: options.approvalCode,
      });
    } else {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_approval_code",
        value: "FAILURE",
      });
    }
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_transaction_id",
      value: "00000000",
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_completed",
      value: true,
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_is_refund",
      value: false,
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_authorized",
      value: false,
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_integration",
      value: options.PayTraceProf,
    });
    if (!NG.tools.isEmpty(options.tType)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_trans_type",
        value: _TRANS_TYPE[options.tType],
      });
    }
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_transaction_datetime",
      value: quickDate(new Date(), "America/Los_Angeles"),
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_paytrace_data",
      value: JSON.stringify(options.ccRequest.response, undefined, 2),
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_paytrace_request",
      value: JSON.stringify(options.altRequestPost, undefined, 2),
    });
    payCompRec.save({ ignoreMandatoryFields: true, enableSourcing: true });
  };

  /**
   * @summary Generates a payment reference record for a successful payment
   *
   * @param {Object} options
   * @param {number|string} options.formId
   * @param {number|string} options.soId
   * @param {number|string} options.invId
   * @param {number|string} options.pmtId
   * @param {number|string} options.dpstId
   * @param {number|string} options.custId
   * @param {number|string} options.PayTraceID
   * @param {number|string} options.profId
   * @param {number|string} options.amount
   * @param {string} options.tType
   * @param {string} options.cType
   * @param {string} options.ccNum
   * @param {string} options.ccExp
   * @param {boolean} options.completed
   * @param {boolean} options.refund
   * @param {boolean} options.isAuth
   * @param {Object} options.ccRequest
   * @param {Object} options.tData
   * @param {Object} options.export
   * @param {Object} options.altRequestPost
   * @param {Date} options.tDateTime
   * @param {Boolean} options.useSecs
   * @param {Boolean} options.useDTObj
   * @param {String} [options.interactionType]
   * @param {String} [options.cardId]
   * @param {String} [options.cardMethId]
   * @returns {number|string}
   */
  const data_CreatePaymentReference = (options) => {
    if (NG.tools.isEmpty(options.useSecs)) {
      options.useSecs = true;
    }
    if (NG.tools.isEmpty(options.useDTObj)) {
      options.useDTObj = false;
    }

    let payCompRec = record.create({
      type: SHARED.Mappings.recordTypes.pymtRef,
      isDynamic: true,
      defaultValues: { customform: options.formId },
    });

    if (options?.soId) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.order,
        value: options.soId,
      });
    }
    if (!NG.tools.isEmpty(options.invId)) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.invoice,
        value: options.invId,
      });
    }
    if (!NG.tools.isEmpty(options.pmtId)) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.payment,
        value: options.pmtId,
      });
    }
    if (!NG.tools.isEmpty(options.dpstId)) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.deposit,
        value: options.dpstId,
      });
    }

    if (!NG.tools.isEmpty(options.ccRequest.approval_code)) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.ptApprvCode,
        value: options.ccRequest.approval_code,
      });
    }
    payCompRec.setValue({
      fieldId: SHARED.Mappings.fields.pymtRef.customer,
      value: options.custId,
    });
    payCompRec.setValue({
      fieldId: SHARED.Mappings.fields.pymtRef.ptTranId,
      value: options.PayTraceID,
    });
    payCompRec.setValue({
      fieldId: SHARED.Mappings.fields.pymtRef.isCompleted,
      value: options.completed || false,
    });
    payCompRec.setValue({
      fieldId: SHARED.Mappings.fields.pymtRef.isRefund,
      value: options.refund || false,
    });
    payCompRec.setValue({
      fieldId: SHARED.Mappings.fields.pymtRef.ptIntId,
      value: options.profId,
    });
    if (!NG.tools.isEmpty(options.tType)) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.tranType,
        value: _TRANS_TYPE[options.tType],
      });
    }

    if (!NG.tools.isEmpty(options.interactionType)) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.interactionType,
        value: options.interactionType,
      });
    }

    if (!NG.tools.isEmpty(options.tData)) {
      let maskedNumber = options.tData.credit_card.masked_number,
        expMonth = options.tData.credit_card.expiration_month,
        expYear = options.tData.credit_card.expiration_year,
        expDate = `${expMonth}/${"20{0}".NG_Format(expYear)}`,
        chargeAmount = Number(options.tData.amount).toFixed(2),
        chargeDateTime = options.tData.created.at,
        ptDateTime = convertPayTraceDateTime(chargeDateTime),
        chargeDate = NG.time.dateToString(
          ptDateTime,
          format.Type.DATETIMETZ,
          "America/Los_Angeles"
        );

      log.audit({
        title: "paytrace tran date params",
        details: {
          useSecs: options.useSecs,
          useDTObj: options.useDTObj,
        },
      });

      if (!options.useSecs && !options.useDTObj) {
        log.audit({
          title: "converting date value to alt",
          details: "",
        });
        chargeDate = convertPayTraceDateTimeAlt({
          chargeDate: chargeDate,
        });
      }

      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.maskedCardNum,
        value: maskedNumber,
      });
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.cardExp,
        value: expDate,
      });
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.tranAmount,
        value: chargeAmount,
      });

      if (options.useDTObj) {
        log.audit({
          title: "setting tran date value to obj",
          details: ptDateTime,
        });
        payCompRec.setValue({
          fieldId: SHARED.Mappings.fields.pymtRef.tranDateTime,
          value: ptDateTime,
        });
      } else {
        log.audit({
          title: "setting tran date value to txt",
          details: chargeDate,
        });
        payCompRec.setValue({
          fieldId: SHARED.Mappings.fields.pymtRef.tranDateTime,
          value: chargeDate,
        });
      }
    } else {
      if (!NG.tools.isEmpty(options.amount)) {
        payCompRec.setValue({
          fieldId: SHARED.Mappings.fields.pymtRef.tranAmount,
          value: Number(options.amount).toFixed(2),
        });
      }
      if (!NG.tools.isEmpty(options.ccNum)) {
        payCompRec.setValue({
          fieldId: SHARED.Mappings.fields.pymtRef.maskedCardNum,
          value: options.ccNum,
        });
      }
      if (!NG.tools.isEmpty(options.ccExp)) {
        payCompRec.setValue({
          fieldId: SHARED.Mappings.fields.pymtRef.cardExp,
          value: options.ccExp,
        });
      }
      if (!NG.tools.isEmpty(options.tDateTime)) {
        payCompRec.setValue({
          fieldId: SHARED.Mappings.fields.pymtRef.tranDateTime,
          value: options.tDateTime,
        });
      }
    }
    if (!NG.tools.isEmpty(options.cType)) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.cardType,
        value: options.cType,
      });
    }
    if (options.isAuth) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.isAuthorized,
        value: true,
      });
    }

    payCompRec.setValue({
      fieldId: SHARED.Mappings.fields.pymtRef.ptReqData,
      value: JSON.stringify(options.ccRequest, undefined, 2),
    });
    payCompRec.setValue({
      fieldId: SHARED.Mappings.fields.pymtRef.ptRspData,
      value: JSON.stringify(options.altRequestPost, undefined, 2),
    });
    if (!NG.tools.isEmpty(options.export)) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.ptRtrData,
        value: JSON.stringify(options.export, undefined, 2),
      });
    }

    if (!NG.tools.isEmpty(options.cardId)) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.cardId,
        value: options.cardId,
      });
    }
    if (!NG.tools.isEmpty(options.cardMethId)) {
      payCompRec.setValue({
        fieldId: SHARED.Mappings.fields.pymtRef.cardPayMeth,
        value: options.cardMethId,
      });
    }

    return payCompRec.save({
      ignoreMandatoryFields: true,
      enableSourcing: true,
    });
  };

  /**
   * @summary Generates a payment reference record for a successful payment
   *
   * @param {Object} options
   * @param {number|string} options.formId
   * @param {number|string} options.soId
   * @param {number|string} options.invId
   * @param {number|string} options.pmtId
   * @param {number|string} options.dpstId
   * @param {number|string} options.custId
   * @param {number|string} options.PayTraceID
   * @param {number|string} options.profId
   * @param {number|string} options.amount
   * @param {string} options.tType
   * @param {string} options.cType
   * @param {string} options.ccNum
   * @param {string} options.ccExp
   * @param {boolean} options.completed
   * @param {boolean} options.refund
   * @param {boolean} options.isAuth
   * @param {Object} options.ccRequest
   * @param {Object} options.tData
   * @param {Object} options.export
   * @param {Object} options.altRequestPost
   * @param {Date} options.tDateTime
   * @returns {number|string}
   */
  const data_CreatePaymentReferenceOld = (options) => {
    let payCompRec = record.create({
      type: "customrecord_ng_paytrace_ref",
      isDynamic: true,
      defaultValues: { customform: options.formId },
    });

    if (!NG.tools.isEmpty(options.soId)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_sales_order",
        value: options.soId,
      });
    }
    if (!NG.tools.isEmpty(options.invId)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_invoice",
        value: options.invId,
      });
    }
    if (!NG.tools.isEmpty(options.pmtId)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_payment",
        value: options.pmtId,
      });
    }
    if (!NG.tools.isEmpty(options.dpstId)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_deposit",
        value: options.dpstId,
      });
    }

    if (!NG.tools.isEmpty(options.ccRequest.approval_code)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_approval_code",
        value: options.ccRequest.approval_code,
      });
    }
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_customer",
      value: options.custId,
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_transaction_id",
      value: options.PayTraceID,
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_completed",
      value: options.completed || false,
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_is_refund",
      value: options.refund || false,
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_integration",
      value: options.profId,
    });
    if (!NG.tools.isEmpty(options.tType)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_trans_type",
        value: _TRANS_TYPE[options.tType],
      });
    }

    if (!NG.tools.isEmpty(options.tData)) {
      let maskedNumber = options.tData.credit_card.masked_number,
        expMonth = options.tData.credit_card.expiration_month,
        expYear = options.tData.credit_card.expiration_year,
        expDate = "{0}/{1}".NG_Format(expMonth, "20{0}".NG_Format(expYear)),
        chargeAmount = Number(options.tData.amount).toFixed(2),
        chargeDateTime = options.tData.created.at,
        chargeDate = quickDate(
          NG.time.stringToDate(chargeDateTime, "datetimetz"),
          "America/Los_Angeles"
        );

      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_masked_card_number",
        value: maskedNumber,
      });
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_card_expiration",
        value: expDate,
      });
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_transaction_amount",
        value: chargeAmount,
      });
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_transaction_datetime",
        value: chargeDate,
      });
    } else {
      if (!NG.tools.isEmpty(options.amount)) {
        payCompRec.setValue({
          fieldId: "custrecord_ng_ptr_transaction_amount",
          value: Number(options.amount).toFixed(2),
        });
      }
      if (!NG.tools.isEmpty(options.ccNum)) {
        payCompRec.setValue({
          fieldId: "custrecord_ng_ptr_masked_card_number",
          value: options.ccNum,
        });
      }
      if (!NG.tools.isEmpty(options.ccExp)) {
        payCompRec.setValue({
          fieldId: "custrecord_ng_ptr_card_expiration",
          value: options.ccExp,
        });
      }
      if (!NG.tools.isEmpty(options.tDateTime)) {
        payCompRec.setValue({
          fieldId: "custrecord_ng_ptr_transaction_datetime",
          value: options.tDateTime,
        });
      }
    }
    if (!NG.tools.isEmpty(options.cType)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_card_type",
        value: options.cType,
      });
    }
    if (options.isAuth) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_authorized",
        value: true,
      });
    }

    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_paytrace_data",
      value: JSON.stringify(options.ccRequest, undefined, 2),
    });
    payCompRec.setValue({
      fieldId: "custrecord_ng_ptr_paytrace_request",
      value: JSON.stringify(options.altRequestPost, undefined, 2),
    });
    if (!NG.tools.isEmpty(options.export)) {
      payCompRec.setValue({
        fieldId: "custrecord_ng_ptr_paytrace_data_export",
        value: JSON.stringify(options.export, undefined, 2),
      });
    }

    return payCompRec.save({
      ignoreMandatoryFields: true,
      enableSourcing: true,
    });
  };

  /**
   * @param {Object} options Date reformatting options; One or the other of the dateString or dateObj values must be set
   * @param {string} [options.dateString] String of the date to reformat to system preferences
   * @param {Date} [options.dateObj] Date object to reformat to system preferences
   * @param {Record} [options.configRec] NetSuite config record; Will load if not passed in
   * @param {Boolean} [options.toUTC=false] Return date string in UTC format
   * @param {Boolean} [options.addSecs=false] Add the date's seconds to the date string
   * @returns {string}
   */
  const data_ReformatDateByCompPref = (options) => {
    let dateObj;
    if (!NG.tools.isEmpty(options.dateString)) {
      dateObj = dateTimeReconstructor({ dateString: options.dateString });
    } else if (!NG.tools.isEmpty(options.dateObj)) {
      dateObj = options.dateObj;
    } else {
      throw error.create({
        name: "INVALID_PARAMETERS",
        message: "One of 'dateString' or 'dateObj' options must be set",
        notifyOff: true,
      });
    }

    if (options.addSecs) {
      return NG.time.dateToString(dateObj, format.Type.DATETIMETZ);
    } else {
      return NG.time.dateToString(dateObj, format.Type.DATETIME);
    }
  };

  /**
   * @param {Object} options Options for string-to-date reconstruction
   * @param {string} options.dateString String of the date to be converted to a Date object
   * @param {string|RegExp} [options.separator=&nbsp;] String or RegEx to define where to split the string between the date and time components; Use /[A-z]/ on date strings ending in Z (Default: blank space)
   * @param {string|RegExp} [options.dateSeparator=/] String or RegEx to define where to split the date components (Default: / )
   * @param {string|RegExp} [options.timeSeparator=:] String or RegEx to define where to split the time components (Default: : )
   * @returns {Date}
   */
  const dateTimeReconstructor = (options) => {
    if (NG.tools.isEmpty(options.dateString)) {
      return null;
    }
    let partSep = options.separator || " ",
      dateSep = options.dateSeparator || "/",
      timeSep = options.timeSeparator || ":",
      dateTimePieces = options.dateString.split(partSep),
      datePieces = dateTimePieces[0].split(dateSep),
      timePieces = dateTimePieces[1].split(timeSep),
      y = Number(datePieces[2]),
      m = Math.round(Number(datePieces[0]) - 1),
      d = Number(datePieces[1]),
      h =
        dateTimePieces[2] === "PM"
          ? Math.round(Number(timePieces[0]) + 12)
          : Number(timePieces[0]),
      mm = Number(timePieces[1]),
      s = Number(timePieces[2]);
    return new Date(y, m, d, h, mm, s);
  };

  const data_AutoDetermineProfile = (custId, ptData) => {
    let p,
      defProfile,
      ptProfile = data_getProfile(),
      custDefProfile = NG.tools.getLookupFields(
        "customer",
        custId,
        ["custentity_ng_pt_default_profile"],
        ["custentity_ng_pt_default_profile"],
        []
      ).custentity_ng_pt_default_profile;

    if (!NG.tools.isEmpty(custDefProfile)) {
      Object.keys(ptData).forEach((profId) => {
        if (profId === custDefProfile && NG.tools.isEmpty(defProfile)) {
          p = ptData[profId];
          if (
            !p.custrecord_ng_ptid_disabled &&
            !NG.tools.isEmpty(p.custrecord_ng_ptid_default_account)
          ) {
            defProfile = profId;
          }
        }
      });
    }

    return defProfile || ptProfile;
  };

  /**
   * Cleans up PayTrace request object for logging; Removes/masks sensitive information
   * @param {Object} pData PayTrace request object
   * @returns {Object}
   */
  const data_CreateAltRequestPost = (pData) => {
    let altRequestPost = JSON.parse(JSON.stringify(pData));
    if (!NG.tools.isEmpty(altRequestPost.hpf_token)) {
      altRequestPost.hpf_token = "************";
    }
    if (!NG.tools.isEmpty(altRequestPost.enc_key)) {
      altRequestPost.enc_key = "************";
    }
    if (!NG.tools.isEmpty(altRequestPost.check)) {
      if (!NG.tools.isEmpty(altRequestPost.account_number)) {
        altRequestPost.account_number = "************";
      }
      if (!NG.tools.isEmpty(altRequestPost.routing_number)) {
        altRequestPost.routing_number = "************";
      }
    }
    if (!NG.tools.isEmpty(altRequestPost.csc)) {
      altRequestPost.csc = "***";
    }
    if (!NG.tools.isEmpty(altRequestPost.encrypted_csc)) {
      altRequestPost.encrypted_csc = "************";
    }
    return altRequestPost;
  };

  /**
   * Creates customer payment record based on an open invoice and returns the payment's internal ID
   * @param {Object} options
   * @returns {number|string}
   */
  const data_CreatePaymentFromInvoice = (options) => {
    log.audit({
      title: "initiating new payment record (invoice)",
      details: "",
    });
    let payRec = record.transform({
      fromType: "invoice",
      fromId: options.invId,
      toType: "customerpayment",
      isDynamic: true,
    });
    payRec.setValue({
      fieldId: "paymentmethod",
      value:
        _SETTINGS[SHARED.SettingsMap.paynow.payMeth] ||
        _SETTINGS[SHARED.SettingsMap.general.onlinePayMeth] ||
        "1",
    });

    let p = options.ptData[options.ptProfile];
    if (
      !p.custrecord_ng_ptid_disabled &&
      !NG.tools.isEmpty(p.custrecord_ng_ptid_default_account)
    ) {
      payRec.setValue({ fieldId: "undepfunds", value: "F" });
      payRec.setValue({
        fieldId: "account",
        value: p.custrecord_ng_ptid_default_account,
      });
    }
    payRec.setValue({ fieldId: "chargeit", value: false });
    payRec.setValue({ fieldId: "ccapproved", value: true });
    payRec.setValue({
      fieldId: "custbody_ng_paytrace_trans_id",
      value: options.PayTraceId,
    });
    payRec.setValue({ fieldId: "creditcardprocessor", value: "" });

    payRec.setValue({
      fieldId: "paymentmethod",
      value:
        _SETTINGS[SHARED.SettingsMap.paynow.payMeth] ||
        _SETTINGS[SHARED.SettingsMap.general.onlinePayMeth] ||
        "1",
    });

    log.audit({ title: "saving payment record (invoice)", details: "" });
    return payRec.save({
      enableSourcing: true,
      ignoreMandatoryFields: true,
    });
  };

  /**
   * @param {Object} options
   * @param {String|Number} options.custId
   * @param {String|Number} options.PayTraceId
   * @param {String|Number} options.ptProfile
   * @param {Object} options.invMap
   * @param {Object: PT_Profile} options.ptData
   * @param {Object} options.formData
   * @param {Object} options.achData
   * @param {String} options.processId
   * @param {String|Number} options.convFeeInvId
   * @param {Number} options.convFee
   * @returns {Number|String}
   */
  const data_CreatePaymentFromStatement = (options) => {
    log.audit({
      title: `initiating new payment record (statement) (${options.processId})`,
      details: "",
    });
    let payRec = record.transform({
      fromType: "customer",
      fromId: options.custId,
      toType: "customerpayment",
      isDynamic: true,
    });
    payRec.setValue({
      fieldId: "paymentmethod",
      value:
        _SETTINGS[SHARED.SettingsMap.paynow.payMeth] ||
        _SETTINGS[SHARED.SettingsMap.general.onlinePayMeth] ||
        "1",
    });
    payRec.setValue({ fieldId: "autoapply", value: false });

    if (!NG.tools.isEmpty(options.convFeeInvId)) {
      var convFeeInvLine = payRec.findSublistLineWithValue({
        sublistId: "apply",
        fieldId: "internalid",
        value: options.convFeeInvId,
      });
      if (convFeeInvLine >= 0) {
        payRec.selectLine({ sublistId: "apply", line: convFeeInvLine });
        payRec.setCurrentSublistValue({
          sublistId: "apply",
          fieldId: "apply",
          value: true,
        });
        payRec.setCurrentSublistValue({
          sublistId: "apply",
          fieldId: "amount",
          value: options.convFee,
        });
        payRec.commitLine({ sublistId: "apply" });
      } else {
        let cfInvData = NG.tools.getLookupFields(
          "invoice",
          options.convFeeInvId,
          ["tranid"],
          [],
          []
        );
        log.error({
          title: "Unable to apply conv fee invoice to payment",
          details: `Invoice ${cfInvData.tranid} (${options.convFeeInvId})`,
        });
      }
    }

    Object.keys(options.invMap).forEach((invNum) => {
      let invLine = payRec.findSublistLineWithValue({
        sublistId: "apply",
        fieldId: "internalid",
        value: options.invMap[invNum].id,
      });
      log.audit({
        title: `adding invoice to payment (${options.processId})`,
        details: `Inv#: ${invNum} (${
          options.invMap[invNum].id
        }) -- Data: ${JSON.stringify(
          options.invMap[invNum]
        )} -- Line: ${invLine}`,
      });
      if (!NG.tools.isEmpty(invLine) && invLine >= 0) {
        payRec.selectLine({
          sublistId: "apply",
          line: invLine,
        });
        payRec.setCurrentSublistValue({
          sublistId: "apply",
          fieldId: "apply",
          value: true,
        });
        payRec.setCurrentSublistValue({
          sublistId: "apply",
          fieldId: "amount",
          value: Number(options.invMap[invNum].amount),
        });
        payRec.commitLine({
          sublistId: "apply",
        });
      }
    });

    if (!NG.tools.isEmpty(options.cmMap)) {
      Object.keys(options.cmMap).forEach((cmNum) => {
        let cmLine = payRec.findSublistLineWithValue({
          sublistId: "credit",
          fieldId: "internalid",
          value: options.cmMap[cmNum].id,
        });
        log.audit({
          title: `adding credit memo to payment (${options.processId})`,
          details: `CM#: ${cmNum} (${
            options.cmMap[cmNum].id
          }) -- Data: ${JSON.stringify(
            options.cmMap[cmNum]
          )} -- Line: ${cmLine}`,
        });
        if (!NG.tools.isEmpty(cmLine) && cmLine >= 0) {
          payRec.selectLine({
            sublistId: "credit",
            line: cmLine,
          });
          payRec.setCurrentSublistValue({
            sublistId: "credit",
            fieldId: "apply",
            value: true,
          });
          payRec.setCurrentSublistValue({
            sublistId: "credit",
            fieldId: "amount",
            value: Number(options.cmMap[cmNum].amount),
          });
          payRec.commitLine({
            sublistId: "credit",
          });
        }
      });
    }

    if (!NG.tools.isEmpty(options.pyMap)) {
      Object.keys(options.pyMap).forEach((pyNum) => {
        let pyLine = payRec.findSublistLineWithValue({
          sublistId: "credit",
          fieldId: "internalid",
          value: options.pyMap[pyNum].id,
        });
        log.audit({
          title: `adding open payment to payment (${options.processId})`,
          details: `PAY#: ${pyNum} (${
            options.pyMap[pyNum].id
          }) -- Data: ${JSON.stringify(
            options.pyMap[pyNum]
          )} -- Line: ${pyLine}`,
        });
        if (!NG.tools.isEmpty(pyLine) && pyLine >= 0) {
          payRec.selectLine({
            sublistId: "credit",
            line: pyLine,
          });
          payRec.setCurrentSublistValue({
            sublistId: "credit",
            fieldId: "apply",
            value: true,
          });
          payRec.setCurrentSublistValue({
            sublistId: "credit",
            fieldId: "amount",
            value: Number(options.pyMap[pyNum].amount),
          });
          payRec.commitLine({
            sublistId: "credit",
          });
        }
      });
    }

    if (!NG.tools.isEmpty(options.jrnlMap)) {
      Object.keys(options.jrnlMap).forEach((jrnlNum) => {
        let jrnlLine = payRec.findSublistLineWithValue({
          sublistId: "credit",
          fieldId: "internalid",
          value: options.jrnlMap[jrnlNum].id,
        });
        log.audit({
          title: `adding open journal entry to payment (${options.processId})`,
          details: `JRNL#: ${jrnlNum} (${
            options.jrnlMap[jrnlNum].id
          }) -- Data: ${JSON.stringify(
            options.jrnlMap[jrnlNum]
          )} -- Line: ${jrnlLine}`,
        });
        if (!NG.tools.isEmpty(jrnlLine) && jrnlLine >= 0) {
          payRec.selectLine({
            sublistId: "credit",
            line: jrnlLine,
          });
          payRec.setCurrentSublistValue({
            sublistId: "credit",
            fieldId: "apply",
            value: true,
          });
          payRec.setCurrentSublistValue({
            sublistId: "credit",
            fieldId: "amount",
            value: Number(options.jrnlMap[jrnlNum].amount),
          });
          payRec.commitLine({
            sublistId: "credit",
          });
        }
      });
    }

    Object.keys(options.invMap).forEach((invNum) => {
      let invLine = payRec.findSublistLineWithValue({
        sublistId: "apply",
        fieldId: "internalid",
        value: options.invMap[invNum].id,
      });
      if (invLine >= 0) {
        log.audit({
          title: `validating invoice application (${options.processId})`,
          details: `Inv #: ${invNum} (${
            options.invMap[invNum].id
          }) -- Line: ${invLine} -- Amount: ${payRec.getSublistValue({
            sublistId: "apply",
            fieldId: "amount",
            line: invLine,
          })} -- Applied: ${payRec.getSublistValue({
            sublistId: "apply",
            fieldId: "apply",
            line: invLine,
          })}`,
        });
      }
    });

    if (!NG.tools.isEmpty(options.cmMap)) {
      Object.keys(options.cmMap).forEach((cmNum) => {
        let cmLine = payRec.findSublistLineWithValue({
          sublistId: "credit",
          fieldId: "internalid",
          value: options.cmMap[cmNum].id,
        });
        if (cmLine >= 0) {
          log.audit({
            title: `validating credit memo application (${options.processId})`,
            details: `CM #: ${cmNum} (${
              options.cmMap[cmNum].id
            }) -- Line: ${cmLine} -- Amount: ${payRec.getSublistValue({
              sublistId: "credit",
              fieldId: "amount",
              line: cmLine,
            })} -- Applied: ${payRec.getSublistValue({
              sublistId: "credit",
              fieldId: "apply",
              line: cmLine,
            })}`,
          });
        }
      });
    }

    if (!NG.tools.isEmpty(options.pyMap)) {
      Object.keys(options.pyMap).forEach((pyNum) => {
        let pyLine = payRec.findSublistLineWithValue({
          sublistId: "credit",
          fieldId: "internalid",
          value: options.pyMap[pyNum].id,
        });
        if (pyLine >= 0) {
          log.audit({
            title: `validating open payment application (${options.processId})`,
            details: `PAY #: ${pyNum} (${
              options.pyMap[pyNum].id
            }) -- Line: ${pyLine} -- Amount: ${payRec.getSublistValue({
              sublistId: "credit",
              fieldId: "amount",
              line: pyLine,
            })} -- Applied: ${payRec.getSublistValue({
              sublistId: "credit",
              fieldId: "apply",
              line: pyLine,
            })}`,
          });
        }
      });
    }

    let p = options.ptData[options.ptProfile];
    if (
      !p.custrecord_ng_ptid_disabled &&
      !NG.tools.isEmpty(p.custrecord_ng_ptid_default_account)
    ) {
      payRec.setValue({ fieldId: "undepfunds", value: "F" });
      payRec.setValue({
        fieldId: "account",
        value: p.custrecord_ng_ptid_default_account,
      });
    }
    payRec.setValue({
      fieldId: "payment",
      value: Number(options.formData.full_total),
    });
    payRec.setValue({ fieldId: "chargeit", value: false });
    payRec.setValue({ fieldId: "ccapproved", value: true });
    payRec.setValue({
      fieldId: "custbody_ng_paytrace_trans_id",
      value: options.PayTraceId,
    });
    payRec.setValue({ fieldId: "creditcardprocessor", value: "" });
    if (!NG.tools.isEmpty(options.achData)) {
      payRec.setValue({
        fieldId: "custbody_ng_paytrace_ach_data",
        value: options.achData,
      });
    }

    payRec.setValue({
      fieldId: "paymentmethod",
      value:
        _SETTINGS[SHARED.SettingsMap.paynow.payMeth] ||
        _SETTINGS[SHARED.SettingsMap.general.onlinePayMeth] ||
        "1",
    });

    log.audit({
      title: `saving payment record (statement) (${options.processId})`,
      details: "",
    });
    return payRec.save({
      enableSourcing: true,
      ignoreMandatoryFields: true,
    });
  };

  /**
   * Calculates the actual amount due on an invoice by taking into account unapproved payments
   * @param {string|number} invId Invoice record internal ID
   * @returns {{custbody_ng_pt_amount_due: string}}
   */
  const data_GetAmountDue = (invId) => {
    let dueUpdate,
      invData = NG.tools.getLookupFields(
        "invoice",
        invId,
        ["total", "tranid"],
        [],
        []
      ),
      appliedFilt = [["appliedtotransaction", "anyof", [invId]]],
      appliedCols = [
        NG.tools.searchColumn({ name: "tranid" }),
        NG.tools.searchColumn({ name: "type" }),
        NG.tools.searchColumn({ name: "internalid" }),
        NG.tools.searchColumn({ name: "total" }),
        NG.tools.searchColumn({ name: "trandate" }),
        NG.tools.searchColumn({ name: "appliedtolinkamount" }),
      ],
      results;

    try {
      results = NG.tools.getSearchResults(
        "transaction",
        appliedFilt,
        appliedCols
      );
    } catch (err) {
      NG.log.logError(err, "Error encountered retrieving applied transactions");
    }
    if (!NG.tools.isEmpty(results)) {
      let appliedTotal = Number(0);
      results.forEach((res) => {
        let appliedAmount = Math.abs(
          Number(res.getValue({ name: "appliedtolinkamount" }))
        );
        appliedTotal = NG.M.roundToHundredths(appliedTotal + appliedAmount);
      });
      let amountDue = NG.M.roundToHundredths(
        Number(invData.total) - appliedTotal
      );
      dueUpdate = {
        custbody_ng_pt_amount_due:
          amountDue > 0 ? amountDue.toFixed(2) : "0.00",
      };
    } else {
      dueUpdate = { custbody_ng_pt_amount_due: invData.total };
    }

    return dueUpdate;
  };

  const data_ApplyClientSettings = (context, settingsData) => {
    settingsData.custrecord_ng_pts_license_key = "**********";
    settingsData.licenseInfo.key = "**********";
    context.form
      .addField({
        id: "custpage_ng_pt_settings",
        type: "longtext",
        label: "ng pt settings",
      })
      .updateDisplayType({ displayType: "hidden" }).defaultValue =
      JSON.stringify(settingsData);
    context.form
      .addField({
        id: "custpage_ng_pt_mappings",
        type: "longtext",
        label: "ng pt mappings",
      })
      .updateDisplayType({ displayType: "hidden" }).defaultValue =
      JSON.stringify(_MAPPINGS);
  };

  const data_GetRefundableTransactions = (options) => {
    let pRefFilt = [
        [SHARED.Mappings.fields.pymtRef.isCompleted, "is", "T"],
        "and",
        [SHARED.Mappings.fields.pymtRef.isRefund, "is", "F"],
        "and",
        [SHARED.Mappings.fields.pymtRef.tranAmount, "isnotempty", ""],
        "and",
        [SHARED.Mappings.fields.pymtRef.ptApprvCode, "isnot", "FAILURE"],
      ],
      pRefCols = [
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.ptTranId,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.tranAmount,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.maskedCardNum,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.cardExp,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.cardType,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.ptIntId,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.invoice,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.cashSale,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.payment,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.creditMemo,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.pymtRef.deposit,
        }),
        search.createColumn({
          name: "created",
          sort: search.Sort.DESC,
        }),
      ],
      recIdList = [],
      rtrnData = [],
      pRefResults;

    if (!NG.tools.isEmpty(options.custId)) {
      pRefFilt.push("and", [
        SHARED.Mappings.fields.pymtRef.customer,
        "anyof",
        [options.custId],
      ]);
    }

    if (
      (options.invIdList || []).length > 0 ||
      (options.csIdList || []).length > 0 ||
      (options.tranIdList || []).length > 0
    ) {
      recIdList = [].concat(
        options.invIdList || [],
        options.csIdList || [],
        options.tranIdList || []
      );
    }

    try {
      pRefResults = NG.tools.getSearchResults(
        SHARED.Mappings.recordTypes.pymtRef,
        pRefFilt,
        pRefCols,
        null,
        15,
        false,
        true
      );
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered retrieving payment ref data results"
      );
    }

    if (!NG.tools.isEmpty(pRefResults)) {
      pRefResults.forEach((res) => {
        let transMatchChar = "";
        if (
          recIdList.includes(
            res.getValue({
              name: SHARED.Mappings.fields.pymtRef.invoice,
            })
          ) ||
          recIdList.includes(
            res.getValue({
              name: SHARED.Mappings.fields.pymtRef.cashSale,
            })
          ) ||
          recIdList.includes(
            res.getValue({
              name: SHARED.Mappings.fields.pymtRef.payment,
            })
          ) ||
          recIdList.includes(
            res.getValue({
              name: SHARED.Mappings.fields.pymtRef.creditMemo,
            })
          ) ||
          recIdList.includes(
            res.getValue({
              name: SHARED.Mappings.fields.pymtRef.deposit,
            })
          )
        ) {
          transMatchChar = "* ";
        }
        let optText = `${transMatchChar}$${Number(
          res.getValue({
            name: SHARED.Mappings.fields.pymtRef.tranAmount,
          })
        ).toFixed(2)} - ${
          res.getValue({ name: "created" }).split(" ")[0]
        } - ${res.getValue({
          name: SHARED.Mappings.fields.pymtRef.cardType,
        })} ${res.getValue({
          name: SHARED.Mappings.fields.pymtRef.maskedCardNum,
        })} (${res.getValue({
          name: SHARED.Mappings.fields.pymtRef.cardExp,
        })})`;
        rtrnData.push({
          id: Number(
            res.getValue({
              name: SHARED.Mappings.fields.pymtRef.ptTranId,
            })
          ).toFixed(0),
          profile: res.getValue({
            name: SHARED.Mappings.fields.pymtRef.ptIntId,
          }),
          text: optText,
        });
      });
    }

    return rtrnData;
  };

  /**
   *
   * @param {Object} options
   * @param {String|Number} options.invId
   * @param {String} [options.payComplete] Filter for whether payment is complete; Required to be text T/F, not boolean true/false
   * @param {String} [options.payDate]
   * @param {String} [options.onOrBeforeDate]
   * @param {String|Number} [options.status]
   * @param {Boolean} [options.sortDesc]
   * @param {Boolean} [options.returnFirst]
   * @returns {[Result]|Result|null}
   */
  const data_GetPaymentScheduleRecords = (options) => {
    let schdFilt = [["custrecord_ng_ptap_invoice", "anyof", [options.invId]]],
      schdCols = [
        search.createColumn({
          name: "custrecord_ng_ptap_pay_seq",
          sort: options.sortDesc || false ? search.Sort.DESC : search.Sort.ASC,
        }),
        search.createColumn({ name: "custrecord_ng_ptap_status" }),
        search.createColumn({
          name: "custrecord_ng_ptap_payment_complete",
        }),
        search.createColumn({
          name: "custrecord_ng_ptap_payment_date",
        }),
        search.createColumn({
          name: "custrecord_ng_ptap_payment_amount",
        }),
        search.createColumn({
          name: "custrecord_ng_ptap_is_final_payment",
        }),
        search.createColumn({
          name: "custrecord_ng_ptap_actual_pay_date",
        }),
        search.createColumn({ name: "custrecord_ng_ptap_invoice" }),
        search.createColumn({ name: "custrecord_ng_ptap_payment" }),
      ],
      schdResults;

    if (
      !NG.tools.isEmpty(options.payComplete) &&
      util.isString(options.payComplete)
    ) {
      schdFilt.push("and", [
        "custrecord_ng_ptap_payment_complete",
        "is",
        options.payComplete,
      ]);
    }

    if (!NG.tools.isEmpty(options.payDate)) {
      schdFilt.push("and", [
        "custrecord_ng_ptap_payment_date",
        "on",
        options.payDate,
      ]);
    } else if (!NG.tools.isEmpty(options.onOrBeforeDate)) {
      schdFilt.push("and", [
        "custrecord_ng_ptap_payment_date",
        "onorbefore",
        options.onOrBeforeDate,
      ]);
    }

    if (!NG.tools.isEmpty(options.status)) {
      if (util.isArray(options.status)) {
        schdFilt.push("and", [
          "custrecord_ng_ptap_status",
          "anyof",
          options.status,
        ]);
      } else {
        schdFilt.push("and", [
          "custrecord_ng_ptap_status",
          "anyof",
          [options.status],
        ]);
      }
    }

    try {
      schdResults = NG.tools.getSearchResults(
        "customrecord_ng_pt_autopay_schedule",
        schdFilt,
        schdCols
      );
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered getting unpaid billing schedule ref records"
      );
    }

    if (!NG.tools.isEmpty(options.returnFirst) && options.returnFirst) {
      if (!NG.tools.isEmpty(schdResults)) {
        return schdResults[0];
      } else {
        return null;
      }
    } else {
      return schdResults || [];
    }
  };

  /**
   *
   * @param {Object} options
   * @param {String|Number} options.invId
   * @returns {{tranId: string, autoPayStatus: string, freqIsDays: boolean, billState: string, billCountry: string, freqIsMnth: boolean, convFeeExempt: boolean, total: number, paySched: string, custId: string, payCount: number, paySchedText: string, freqIsYear: boolean, nextPayDate: string, freqCount: number, schedPayAmount: number, billAddr1: string, billAddr2: string, billZip: string, billCity: string, payProf: string, cardId: string, signupLink: string, updateLink: string, initPayAmount: number, dontUpdSched: boolean, id: string}}
   */
  const data_GetAutoPayInvoiceData = (options) => {
    let apFields = [
        "custbody_ng_paytrace_ap_payment_count",
        "custbody_ng_paytrace_ap_schedule",
        "custbody_ng_paytrace_ap_first_pay_amnt",
        "custbody_ng_paytrace_ap_sched_amount",
        "custbody_ng_paytrace_ap_profile",
        "custbody_ng_paytrace_ap_signup_lnk_url",
        "custbody_ng_paytrace_ap_status",
        "custbody_ng_paytrace_ap_next_pay_date",
        "custbody_ng_paytrace_ap_schedule.custrecord_ng_ptapf_freq_amount",
        "custbody_ng_paytrace_ap_schedule.custrecord_ng_ptapf_freq_in_days",
        "custbody_ng_paytrace_ap_schedule.custrecord_ng_ptapf_freq_in_months",
        "custbody_ng_paytrace_ap_schedule.custrecord_ng_ptapf_freq_in_years",
        "custbody_ng_paytrace_conv_fee_exempt",
        "custbody_ng_paytrace_ap_credit_card",
        "tranid",
        "total",
        "entity",
        "custbody_ng_paytrace_ap_update_lnk_url",
        "custbody_ng_paytrace_ap_dont_upd_sched",
        "custbody_ng_paytrace_ap_bill_addr_str1",
        "custbody_ng_paytrace_ap_bill_addr_str2",
        "custbody_ng_paytrace_ap_bill_addr_city",
        "custbody_ng_paytrace_ap_bill_addr_st",
        "custbody_ng_paytrace_ap_bill_addr_zip",
        "custbody_ng_paytrace_ap_bill_addr_ctry",
      ],
      apFields_S = [
        "custbody_ng_paytrace_ap_schedule",
        "custbody_ng_paytrace_ap_profile",
        "custbody_ng_paytrace_ap_status",
        "entity",
      ];
    let apData = NG.tools.getLookupFields(
      record.Type.INVOICE,
      options.invId,
      apFields,
      apFields_S,
      []
    );

    return {
      id: `${options.invId}`,
      payCount: Number(apData.custbody_ng_paytrace_ap_payment_count || "0"),
      paySched: `${apData.custbody_ng_paytrace_ap_schedule || ""}`,
      paySchedText: `${apData.custbody_ng_paytrace_ap_schedule_text || ""}`,
      payProf: `${apData.custbody_ng_paytrace_ap_profile || ""}`,
      initPayAmount: Number(
        apData.custbody_ng_paytrace_ap_first_pay_amnt || "0"
      ),
      schedPayAmount: Number(
        apData.custbody_ng_paytrace_ap_sched_amount || "0"
      ),
      signupLink: `${apData.custbody_ng_paytrace_ap_signup_lnk_url || ""}`,
      updateLink: `${apData.custbody_ng_paytrace_ap_update_lnk_url || ""}`,
      nextPayDate: `${apData.custbody_ng_paytrace_ap_next_pay_date || ""}`,
      autoPayStatus: `${apData.custbody_ng_paytrace_ap_status || ""}`,
      cardId: `${apData.custbody_ng_paytrace_ap_credit_card || ""}`,
      freqCount: Number(
        apData[
          "custbody_ng_paytrace_ap_schedule.custrecord_ng_ptapf_freq_amount"
        ] || "1"
      ),
      freqIsDays:
        apData[
          "custbody_ng_paytrace_ap_schedule.custrecord_ng_ptapf_freq_in_days"
        ],
      freqIsMnth:
        apData[
          "custbody_ng_paytrace_ap_schedule.custrecord_ng_ptapf_freq_in_months"
        ],
      freqIsYear:
        apData[
          "custbody_ng_paytrace_ap_schedule.custrecord_ng_ptapf_freq_in_years"
        ],
      convFeeExempt: apData.custbody_ng_paytrace_conv_fee_exempt,
      custId: `${apData.entity}`,
      tranId: `${apData.tranid}`,
      total: Number(apData.total || "0"),
      dontUpdSched: apData.custbody_ng_paytrace_ap_dont_upd_sched,
      billAddr1: `${apData.custbody_ng_paytrace_ap_bill_addr_str1 || ""}`,
      billAddr2: `${apData.custbody_ng_paytrace_ap_bill_addr_str2 || ""}`,
      billCity: `${apData.custbody_ng_paytrace_ap_bill_addr_city || ""}`,
      billState: `${apData.custbody_ng_paytrace_ap_bill_addr_st || ""}`,
      billZip: `${apData.custbody_ng_paytrace_ap_bill_addr_zip || ""}`,
      billCountry: `${apData.custbody_ng_paytrace_ap_bill_addr_ctry || ""}`,
    };
  };

  /**
   *
   * @param {Object} options
   * @param {String|Number} options.cardId
   * @param {String|Number} options.profId
   * @returns {{Object}|null}
   */
  const data_LookupEncryptedCard = (options) => {
    let cardFilt = [
        ["custrecord_ng_ptecd_card_id", "is", options.cardId],
        "and",
        ["custrecord_ng_ptecd_applied_profile", "is", options.profId],
      ],
      cardCols = [
        search.createColumn({
          name: "custrecord_ng_ptecd_encrypted_card",
        }),
        search.createColumn({
          name: "custrecord_ng_ptecd_encypted_csc",
        }),
      ],
      cardResults;

    try {
      cardResults = NG.tools.getSearchResults(
        "customrecord_ng_pt_ecrypted_card",
        cardFilt,
        cardCols
      );
    } catch (err) {
      NG.log.logError(err, "Error encountered retrieving encrypted card data");
    }

    if (!NG.tools.isEmpty(cardResults)) {
      return {
        encCardNum: cardResults[0].getValue({
          name: "custrecord_ng_ptecd_encrypted_card",
        }),
        encCardCSC: cardResults[0].getValue({
          name: "custrecord_ng_ptecd_encypted_csc",
        }),
      };
    } else {
      return null;
    }
  };

  /**
   *
   * @param {Object} options
   * @param {Date} options.initBillDate
   * @param {Boolean} options.freqD
   * @param {Boolean} options.freqM
   * @param {Boolean} options.freqY
   * @param {Number} options.freqQ
   * @returns {Date}
   */
  const data_GetNextBillDate = (options) => {
    let yy = options.initBillDate.getFullYear(),
      mm = options.initBillDate.getMonth(),
      dd = options.initBillDate.getDate();
    if (options.freqD) {
      dd += options.freqQ;
    } else if (options.freqM) {
      mm += options.freqQ;
    } else if (options.freqY) {
      yy += options.freqQ;
    }
    return new Date(Math.round(yy), Math.round(mm), Math.round(dd), 0, 0, 0, 0);
  };

  /**
   * Performs update on customer record to add credit card data entered in via web form
   * @param {Object} options
   * @param {Object} options.formData
   * @param {Object} options.apData
   * @param {string|number} [options.invId]
   * @param {string} [options.cardMemo]
   * @param {string} [options.addrLabel]
   * @param {Request} options.request
   * @param {Response} options.response
   * @param {Cache} options.cache
   * @param {Boolean} [options.allowSchedUpdate]
   * @param {Boolean} [options.cardIsDefault]
   * @param {string|number} [options.payMeth]
   * @param {Boolean} [options.throwErr]
   * @param {Boolean} [options.skipNewAddr]
   * @param {Boolean} [options.returnAddrId]
   * @returns {string|number|Object}
   */
  const data_UpdateCustomerCard = (options) => {
    let custRec = record.load({
        type: record.Type.CUSTOMER,
        id: options.formData.cid,
        isDynamic: true,
      }),
      cardIdList = [],
      custUpd = false,
      crd,
      apCardId,
      addrId,
      cardType,
      typeSubFilt,
      runSearch = true,
      typeResults;
    if (NG.tools.isEmpty(options.throwErr)) {
      options.throwErr = false;
    }
    if (NG.tools.isEmpty(options.skipNewAddr)) {
      options.skipNewAddr = false;
    }
    if (NG.tools.isEmpty(options.returnAddrId)) {
      options.returnAddrId = false;
    }

    if (NG.tools.isEmpty(options.payMeth)) {
      switch (options.formData.ctype) {
        case "VISA":
          typeSubFilt = [
            ["name", "contains", "Visa"],
            "or",
            ["name", "contains", "VISA"],
            "or",
            ["name", "contains", "visa"],
          ];
          break;
        case "MASTERCARD":
          typeSubFilt = [
            ["name", "contains", "Mastercard"],
            "or",
            ["name", "contains", "MasterCard"],
            "or",
            ["name", "contains", "MASTERCARD"],
            "or",
            ["name", "contains", "mastercard"],
            "or",
            ["name", "contains", "Master card"],
            "or",
            ["name", "contains", "Master Card"],
            "or",
            ["name", "contains", "MASTER CARD"],
            "or",
            ["name", "contains", "master card"],
          ];
          break;
        case "DISCOVER":
          typeSubFilt = [
            ["name", "contains", "Discover"],
            "or",
            ["name", "contains", "DISCOVER"],
            "or",
            ["name", "contains", "discover"],
          ];
          break;
        case "AMEX":
          typeSubFilt = [
            ["name", "contains", "American Express"],
            "or",
            ["name", "contains", "AMERICAN EXPRESS"],
            "or",
            ["name", "contains", "american express"],
            "or",
            ["name", "contains", "AmericanExpress"],
            "or",
            ["name", "contains", "AMERICANEXPRESS"],
            "or",
            ["name", "contains", "americanexpress"],
          ];
          break;
        default:
          runSearch = false;
      }

      if (runSearch) {
        let typeFilt = [["isinactive", "is", "F"], "and", typeSubFilt],
          typeCols = [search.createColumn({ name: "name" })];

        if (
          !NG.tools.isEmpty(
            _SETTINGS[SHARED.SettingsMap.general.nonIntPayMeths]
          ) &&
          (_SETTINGS[SHARED.SettingsMap.general.nonIntPayMeths] || []).length >
            0
        ) {
          typeFilt.push("and", [
            "internalid",
            "noneof",
            _SETTINGS[SHARED.SettingsMap.general.nonIntPayMeths],
          ]);
        }

        try {
          typeResults = NG.tools.getSearchResults(
            search.Type.PAYMENT_METHOD,
            typeFilt,
            typeCols
          );
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered finding credit card payment methods"
          );
        }
      }

      if (!NG.tools.isEmpty(typeResults)) {
        cardType = typeResults[0].id;
      } else {
        if (
          options.formData.ctype === "VISA" &&
          !NG.tools.isEmpty(_SETTINGS[SHARED.SettingsMap.general.visaPayMeth])
        ) {
          cardType = _SETTINGS[SHARED.SettingsMap.general.visaPayMeth];
        } else if (
          options.formData.ctype === "MASTERCARD" &&
          !NG.tools.isEmpty(_SETTINGS[SHARED.SettingsMap.general.mcPayMeth])
        ) {
          cardType = _SETTINGS[SHARED.SettingsMap.general.mcPayMeth];
        } else if (
          options.formData.ctype === "DISCOVER" &&
          !NG.tools.isEmpty(_SETTINGS[SHARED.SettingsMap.general.discPayMeth])
        ) {
          cardType = _SETTINGS[SHARED.SettingsMap.general.discPayMeth];
        } else if (
          options.formData.ctype === "AMEX" &&
          !NG.tools.isEmpty(_SETTINGS[SHARED.SettingsMap.general.amexPayMeth])
        ) {
          cardType = _SETTINGS[SHARED.SettingsMap.general.amexPayMeth];
        }
        ////////////////////
        else if (
          options.formData.ctype === "VISA" &&
          !NG.tools.isEmpty(_SETTINGS[SHARED.SettingsMap.autoPay.visaPayMeth])
        ) {
          cardType = _SETTINGS[SHARED.SettingsMap.autoPay.visaPayMeth];
        } else if (
          options.formData.ctype === "MASTERCARD" &&
          !NG.tools.isEmpty(_SETTINGS[SHARED.SettingsMap.autoPay.mcPayMeth])
        ) {
          cardType = _SETTINGS[SHARED.SettingsMap.autoPay.mcPayMeth];
        } else if (
          options.formData.ctype === "DISCOVER" &&
          !NG.tools.isEmpty(_SETTINGS[SHARED.SettingsMap.autoPay.discPayMeth])
        ) {
          cardType = _SETTINGS[SHARED.SettingsMap.autoPay.discPayMeth];
        } else if (
          options.formData.ctype === "AMEX" &&
          !NG.tools.isEmpty(_SETTINGS[SHARED.SettingsMap.autoPay.amexPayMeth])
        ) {
          cardType = _SETTINGS[SHARED.SettingsMap.autoPay.amexPayMeth];
        }
        ////////////////////
        else if (
          !NG.tools.isEmpty(
            _SETTINGS[SHARED.SettingsMap.general.fallbackPayMeth]
          )
        ) {
          cardType = _SETTINGS[SHARED.SettingsMap.general.fallbackPayMeth];
        }
      }
    } else {
      cardType = options.payMeth;
    }

    for (
      crd = 0;
      crd < custRec.getLineCount({ sublistId: "creditcards" });
      crd++
    ) {
      cardIdList.push(
        Number(
          custRec
            .getSublistValue({
              sublistId: "creditcards",
              fieldId: "internalid",
              line: crd,
            })
            .toFixed(0)
        )
      );
    }
    log.audit({ title: "cardIdList", details: cardIdList });
    let cardMemo = !NG.tools.isEmpty(options.cardMemo)
        ? options.cardMemo
        : `AutoPay: Invoice# ${options.apData.tranId}`,
      addrLabel = !NG.tools.isEmpty(options.addrLabel)
        ? options.addrLabel
        : `Invoice ${options.apData.tranId} AutoPay Address`;

    try {
      let currCardLine = custRec.findSublistLineWithValue({
        sublistId: "creditcards",
        fieldId: "memo",
        value: cardMemo,
      });
      let currAddrLine = custRec.findSublistLineWithValue({
        sublistId: "addressbook",
        fieldId: "label",
        value: addrLabel,
      });

      if (currCardLine >= 0) {
        custRec.removeLine({
          sublistId: "creditcards",
          line: currCardLine,
        });
      }
      if (currAddrLine >= 0) {
        custRec.removeLine({
          sublistId: "addressbook",
          line: currAddrLine,
        });
      }

      custRec.selectNewLine({ sublistId: "creditcards" });
      custRec.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "ccnumber",
        value: options.formData.cardnumber,
      });
      custRec.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "ccname",
        value: options.formData.cardname,
      });
      custRec.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "ccexpiredate",
        value: new Date(
          Number(options.formData.exp_year),
          Number(options.formData.exp_month) - 1,
          1,
          0,
          0,
          0,
          0
        ),
      });
      custRec.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "ccmemo",
        value: cardMemo,
      });
      custRec.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "customercode",
        value: cardMemo,
      });
      if (!NG.tools.isEmpty(cardType)) {
        custRec.setCurrentSublistValue({
          sublistId: "creditcards",
          fieldId: "paymentmethod",
          value: cardType,
        });
      }
      custRec.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "ccdefault",
        value: options.cardIsDefault || false,
      });
      custRec.commitLine({ sublistId: "creditcards" });

      if (!options.skipNewAddr) {
        custRec.selectNewLine({ sublistId: "addressbook" });
        custRec.setCurrentSublistValue({
          sublistId: "addressbook",
          fieldId: "defaultbilling",
          value: options.cardIsDefault || false,
        });
        custRec.setCurrentSublistValue({
          sublistId: "addressbook",
          fieldId: "defaultshipping",
          value: false,
        });
        custRec.setCurrentSublistValue({
          sublistId: "addressbook",
          fieldId: "label",
          value: addrLabel,
        });

        let apAddr = custRec.getCurrentSublistSubrecord({
          sublistId: "addressbook",
          fieldId: "addressbookaddress",
        });
        apAddr.setValue({
          fieldId: "country",
          value: options.formData.billcountry,
        });
        apAddr.setValue({
          fieldId: "addr1",
          value: options.formData.billaddress1,
        });
        if (!NG.tools.isEmpty(options.formData.billaddress2)) {
          apAddr.setValue({
            fieldId: "addr2",
            value: options.formData.billaddress2,
          });
        }
        apAddr.setValue({
          fieldId: "city",
          value: options.formData.billcity,
        });
        apAddr.setValue({
          fieldId: "state",
          value: options.formData.billstate,
        });
        apAddr.setValue({
          fieldId: "zip",
          value: options.formData.billzip,
        });
        apAddr.setValue({ fieldId: "attention", value: "" });
        apAddr.setValue({ fieldId: "addressee", value: "" });

        try {
          custRec.commitLine({ sublistId: "addressbook" });
        } catch (ex) {
          log.audit({
            title: "setting zero value phone number for customer address",
            details: "",
          });
          apAddr.setValue({
            fieldId: "addrphone",
            value: "************",
          });
          custRec.commitLine({ sublistId: "addressbook" });
        }
      }

      custRec.save({ ignoreMandatoryFields: true, enableSourcing: true });

      custUpd = true;

      if (options.returnAddrId) {
        let addrFilt = [
            ["internalid", "anyof", [options.formData.cid]],
            "and",
            ["addresslabel", "is", addrLabel],
          ],
          addrCols = [
            search.createColumn({ name: "address" }),
            search.createColumn({ name: "address1" }),
            search.createColumn({ name: "address2" }),
            search.createColumn({ name: "city" }),
            search.createColumn({ name: "state" }),
            search.createColumn({ name: "zipcode" }),
            search.createColumn({ name: "country" }),
            search.createColumn({ name: "addressinternalid" }),
          ],
          addrResults;

        try {
          addrResults = NG.tools.getSearchResults(
            search.Type.CUSTOMER,
            addrFilt,
            addrCols
          );
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered retrieving card billing address data"
          );
        }

        if (!NG.tools.isEmpty(addrResults)) {
          let addrRes = addrResults[0],
            addr2 = addrRes.getValue({ name: "address2" }),
            addr2Line = !NG.tools.isEmpty(addr2) ? `, ${addr2}` : "",
            addr = `${addrRes.getValue({
              name: "address1",
            })}${addr2Line}
							${addrRes.getValue({ name: "city" })}, ${addrRes.getValue({
              name: "state",
            })} ${addrRes.getValue({ name: "zipcode" })}
							${addrRes.getText({ name: "country" })}`;

          addrId = {
            addr: addr.replace(/\t/g, ""),
            id: addrRes.getValue({ name: "addressinternalid" }),
          };
        }
      }
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered updating customer record with AutoPay credit card"
      );
      if (!NG.tools.isEmpty(options.cache)) {
        _page.FailurePage(options.response, options.cache);
      }
      if (options.throwErr) {
        throw err;
      }
    }

    if (custUpd) {
      let custRecUpd = record.load({
          type: record.Type.CUSTOMER,
          id: options.formData.cid,
          isDynamic: true,
        }),
        updCardIdList = [];

      for (
        crd = 0;
        crd < custRecUpd.getLineCount({ sublistId: "creditcards" });
        crd++
      ) {
        updCardIdList.push(
          Number(
            custRecUpd
              .getSublistValue({
                sublistId: "creditcards",
                fieldId: "internalid",
                line: crd,
              })
              .toFixed(0)
          )
        );
      }
      log.audit({ title: "updCardIdList", details: updCardIdList });
      let diffArr = updCardIdList.NG_differential(cardIdList);
      log.audit({ title: "diffArr", details: diffArr });
      if ((diffArr || []).length === 1) {
        apCardId = diffArr[0];
        log.audit({
          title: "new card id determined",
          details: apCardId,
        });
      }

      log.audit({ title: "apCardId", details: apCardId });
      if (!NG.tools.isEmpty(apCardId)) {
        let cardDataRecId;
        let logData = JSON.parse(JSON.stringify(options.formData));
        logData.cardnumber = "******";
        logData.cardcode = "******";
        logData.customerCardId = apCardId;

        try {
          let cardDataRec = record.create({
            type: SHARED.Mappings.recordTypes.cardData,
          });
          cardDataRec.setValue({
            fieldId: SHARED.Mappings.fields.cardData.customer,
            value: options.formData["cid"],
          });
          cardDataRec.setValue({
            fieldId: SHARED.Mappings.fields.cardData.cardId,
            value: Number(apCardId).toFixed(0),
          });
          cardDataRec.setValue({
            fieldId: SHARED.Mappings.fields.cardData.encCardNum,
            value: options.formData.encnum,
          });
          cardDataRec.setValue({
            fieldId: SHARED.Mappings.fields.cardData.profile,
            value: options.apData.payProf,
          });
          if (!NG.tools.isEmpty(options.formData.enccode)) {
            cardDataRec.setValue({
              fieldId: SHARED.Mappings.fields.cardData.encSecCode,
              value: options.formData.enccode,
            });
          }
          cardDataRecId = cardDataRec.save({
            enableSourcing: true,
            ignoreMandatoryFields: true,
          });
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered creating encrypted card data record",
            JSON.stringify(logData)
          );
          if (!NG.tools.isEmpty(options.cache)) {
            _page.FailurePage(options.response, options.cache);
          }
          if (options.throwErr) {
            throw err;
          }
        }

        log.audit({ title: "cardDataRecId", details: cardDataRecId });
        if (
          !NG.tools.isEmpty(cardDataRecId) &&
          !NG.tools.isEmpty(options.invId)
        ) {
          log.audit({
            title: "Applying new card data to invoice",
            details: { invId: options.invId },
          });
          let cardExpDate = new Date(
              options.formData.exp_year,
              options.formData.exp_month,
              1,
              0,
              0,
              0,
              0
            ),
            cardDisplayNum = `*${options.formData.cardnumber.substring(-4)}`,
            cardDisplayType;

          switch (cardType) {
            case _SETTINGS[SHARED.SettingsMap.general.visaPayMeth]:
            case _SETTINGS[SHARED.SettingsMap.autoPay.visaPayMeth]:
              cardDisplayType = "Visa";
              break;
            case _SETTINGS[SHARED.SettingsMap.general.mcPayMeth]:
            case _SETTINGS[SHARED.SettingsMap.autoPay.mcPayMeth]:
              cardDisplayType = "Mastercard";
              break;
            case _SETTINGS[SHARED.SettingsMap.general.discPayMeth]:
            case _SETTINGS[SHARED.SettingsMap.autoPay.discPayMeth]:
              cardDisplayType = "Discover";
              break;
            case _SETTINGS[SHARED.SettingsMap.general.amexPayMeth]:
            case _SETTINGS[SHARED.SettingsMap.autoPay.amexPayMeth]:
              cardDisplayType = "Amex";
              break;
            default:
              cardExpDate = null;
              cardDisplayNum = "";
              cardDisplayType = "";
              break;
          }

          try {
            let submitData = {};
            submitData[SHARED.Mappings.fields.transaction.apCard] =
              Number(apCardId).toFixed(0);
            submitData[SHARED.Mappings.fields.transaction.apNextPayDate] =
              options.formData.fpaydate;
            submitData[SHARED.Mappings.fields.transaction.apStatus] =
              SHARED.Mappings.lists.autoPayStatus.values.Eligible;
            submitData[SHARED.Mappings.fields.transaction.cardExp] =
              cardExpDate;
            submitData[SHARED.Mappings.fields.transaction.maskedCardNum] =
              cardDisplayNum;
            submitData[SHARED.Mappings.fields.transaction.cardType] =
              cardDisplayType;
            submitData[SHARED.Mappings.fields.transaction.apDontUpd] = !(
              options.allowSchedUpdate || false
            );

            log.audit({
              title: "autopay invoice update data (CARD UPDATE)",
              details: submitData,
            });
            record.submitFields({
              type: record.Type.INVOICE,
              id: options.invId,
              values: submitData,
              options: {
                ignoreMandatoryFields: true,
                enableSourcing: false,
              },
            });
          } catch (err) {
            NG.log.logError(
              err,
              "Error encountered setting customer AutoPay card ID to invoice",
              JSON.stringify(logData)
            );
            if (!NG.tools.isEmpty(options.cache)) {
              _page.FailurePage(options.response, options.cache);
            }
            if (options.throwErr) {
              throw err;
            }
          }
        }
      } else {
        log.error({
          title: "New card for AutoPay could not be determined",
          details: "",
        });
        if (!NG.tools.isEmpty(options.cache)) {
          _page.FailurePage(options.response, options.cache);
        }
        if (options.throwErr) {
          throw error.create({
            name: SHARED.Mappings.errorCodes.GenAutoPay,
            message: "New card for AutoPay could not be determined",
            notifyOff: true,
          });
        }
      }
    }

    if (options.returnAddrId) {
      return { apCardId, addrId };
    } else {
      return apCardId;
    }
  };

  /**
   *
   * @param {Object} options
   * @param {Object} options.formData
   * @param {Object} options.apData
   * @param {string|number} options.invId
   * @param {Request} options.request
   * @param {Response} options.response
   * @param {Cache} options.cache
   * @param {String} options.authToken
   * @param {Object} options.ptRequestResp
   * @param {Number} options.toBeCharged
   * @param {Object} options._PT_DATA
   * @param {Object} options.altRequest
   * @param {Boolean} options.getFailed
   * @param {Boolean} [options.allowSchedUpdate]
   * @returns {Boolean}
   */
  const data_CreateCardUpdatePayment = (options) => {
    let updCardId = _data.UpdateCustomerCard({
      formData: options.formData,
      invId: options.invId,
      apData: options.apData,
      request: options.request,
      response: options.response,
      cache: options.cache,
      allowSchedUpdate: options.allowSchedUpdate || false,
    });

    let cardDataFull = _data.GetCreditCards(
        options.apData.custId,
        options.apData.payProf
      ),
      defCardIndex = !NG.tools.isEmpty(updCardId)
        ? cardDataFull.findIndex((x) => {
            return Number(x.value) === Number(updCardId);
          })
        : cardDataFull.findIndex((x) => {
            return x.markedDefault;
          }),
      cardData = cardDataFull[defCardIndex],
      cardId = updCardId || options.apData.cardId,
      ptTranId = Number(options.ptRequestResp.transaction_id).toFixed(0),
      tData = _comm.GetTransactionData(options.authToken, ptTranId),
      payProceed = true,
      isSuccess = false,
      payRecId,
      unpaidPayments = options.getFailed
        ? _data.GetPaymentScheduleRecords({
            invId: options.invId,
            payComplete: "F",
            returnFirst: false,
            status: [
              SHARED.Mappings.lists.autoPayStatus.values.Failed,
              SHARED.Mappings.lists.autoPayStatus.values.ExpiredCard,
            ],
          })
        : _data.GetPaymentScheduleRecords({
            invId: options.invId,
            payComplete: "F",
            returnFirst: false,
          }),
      autoPayRec;

    try {
      autoPayRec = record.transform({
        fromType: "invoice",
        fromId: options.invId,
        toType: "customerpayment",
        isDynamic: true,
      });
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered attempting to transform invoice into payment"
      );
      payProceed = false;
    }

    if (payProceed) {
      let p = options._PT_DATA[options.apData.payProf];
      if (
        !p.custrecord_ng_ptid_disabled &&
        !NG.tools.isEmpty(p.custrecord_ng_ptid_default_account)
      ) {
        autoPayRec.setValue({ fieldId: "undepfunds", value: "F" });
        autoPayRec.setValue({
          fieldId: "account",
          value: p.custrecord_ng_ptid_default_account,
        });
      }
      autoPayRec.setValue({
        fieldId: "payment",
        value: options.toBeCharged,
      });
      autoPayRec.setValue({ fieldId: "creditcard", value: cardId });
      autoPayRec.setValue({ fieldId: "chargeit", value: false });
      autoPayRec.setValue({ fieldId: "ccapproved", value: true });

      let invoiceLine = autoPayRec.findSublistLineWithValue({
        sublistId: "apply",
        fieldId: "internalid",
        value: options.invId,
      });
      if (invoiceLine >= 0) {
        autoPayRec.selectLine({ sublistId: "apply", line: invoiceLine });
        autoPayRec.setCurrentSublistValue({
          sublistId: "apply",
          fieldId: "amount",
          value: options.toBeCharged,
        });
        autoPayRec.commitLine({ sublistId: "apply" });
      }

      autoPayRec.setValue({
        fieldId: "payment",
        value: options.toBeCharged,
      });

      if (!NG.tools.isEmpty(tData)) {
        if ((tData.transactions || []).length > 0) {
          try {
            autoPayRec.setValue({
              fieldId: "custbody_ng_paytrace_trans_id",
              value: ptTranId,
            });

            ///////////////////
            payRecId = autoPayRec.save({
              ignoreMandatoryFields: true,
              enableSourcing: true,
            });
            ///////////////////

            let maskedCardNumber =
                tData.transactions[0].credit_card.masked_number,
              chargeAmount = Number(tData.transactions[0].amount).toFixed(2),
              chargeDate = quickDate(
                NG.time.stringToDate(
                  tData.transactions[0].created.at,
                  "datetimetz"
                ),
                "America/Los_Angeles"
              ),
              cardType = !NG.tools.isEmpty(cardData)
                ? cardData.type || "N/A"
                : "N/A",
              paymentData = NG.tools.getLookupFields(
                "customerpayment",
                payRecId,
                ["internalid", "tranid", "amount"],
                [],
                []
              );

            log.audit({
              title: "Payment Created",
              details: paymentData,
            });

            try {
              let payCompRecId = _data.CreatePaymentReference({
                formId: _SETTINGS.custrecord_ng_pts_ref_inv_form,
                soId: !NG.tools.isEmpty(options.invData.createdfrom)
                  ? options.invData.createdfrom
                  : "",
                invId: options.invId,
                custId: options.apData.custId,
                pmtId: payRecId,
                PayTraceID: ptTranId,
                profId: options.apData.payProf,
                ccRequest: options.ptRequestResp,
                altRequestPost: options.altRequest,
                tType: "Card",
                ccNum: maskedCardNumber,
                ccExp: `${options.formData.exp_month.NG_paddingLeft("00")}/${
                  options.formData.exp_year
                }`,
                amount: chargeAmount,
                cType: cardType,
                tDateTime: chargeDate,
                completed: true,
                refund: false,
                tData: tData.transactions[0],
                export: tData,
                interactionType: "Card Update Payment",
              });

              try {
                record.submitFields({
                  type: "customerpayment",
                  id: payRecId,
                  values: {
                    custbody_ng_paytrace_data_ref: payCompRecId,
                  },
                  options: {
                    ignoreMandatoryFields: true,
                    enableSourcing: false,
                  },
                });
              } catch (err) {
                NG.log.logError(
                  err,
                  "Error encountered setting pay ref ID on payment"
                );
              }
            } catch (err) {
              NG.log.logError(
                err,
                "Error encountered handling payment data ref generation"
              );
            }

            if (!NG.tools.isEmpty(unpaidPayments)) {
              let updScheds;
              if (options.getFailed) {
                updScheds = unpaidPayments.map((unp) => {
                  return unp;
                });
              } else {
                updScheds = unpaidPayments.filter((x) => {
                  return (
                    Number(
                      x.getValue({
                        name: "custrecord_ng_ptap_pay_seq",
                      })
                    ) === 1
                  );
                });
                if (updScheds.length < 1) {
                  updScheds = unpaidPayments.filter((x) => {
                    return (
                      Number(
                        x.getValue({
                          name: "custrecord_ng_ptap_pay_seq",
                        })
                      ) === 0
                    );
                  });
                }
              }

              if (updScheds.length > 0) {
                let isFinal = false;
                updScheds.forEach((upd) => {
                  try {
                    let schdUpdValues = {
                      custrecord_ng_ptap_payment_complete: true,
                      custrecord_ng_ptap_actual_pay_date: NG.time.dateToString(
                        new Date()
                      ),
                      custrecord_ng_ptap_payment: payRecId,
                      custrecord_ng_ptap_status:
                        SHARED.Mappings.lists.autoPayStatus.values.Success,
                    };
                    record.submitFields({
                      type: upd.recordType,
                      id: upd.id,
                      values: schdUpdValues,
                      options: {
                        ignoreMandatoryFields: true,
                        enableSourcing: false,
                      },
                    });

                    if (
                      upd.getValue({
                        name: "custrecord_ng_ptap_is_final_payment",
                      })
                    ) {
                      isFinal = true;
                    }
                  } catch (err) {
                    NG.log.logError(
                      err,
                      "Error encountered updating payment schedule record as paid"
                    );
                  }
                });

                let emailTemplate;
                if (isFinal) {
                  emailTemplate =
                    _SETTINGS.custrecord_ng_pts_rcr_bill_paid_templt;
                  try {
                    let invUpdate = {
                      custbody_ng_paytrace_ap_dont_upd_sched: true,
                      custbody_ng_paytrace_ap_status:
                        SHARED.Mappings.lists.autoPayStatus.values.Success,
                    };
                    log.audit({
                      title: "autopay invoice update data (MISSED PAYMENTS)",
                      details: invUpdate,
                    });
                    record.submitFields({
                      type: "invoice",
                      id: options.invId,
                      values: invUpdate,
                      options: {
                        ignoreMandatoryFields: true,
                        enableSourcing: true,
                      },
                    });
                  } catch (err) {
                    NG.log.logError(
                      err,
                      "Error encountered marking invoice AutoPay status as successful/complete"
                    );
                  }
                } else {
                  emailTemplate =
                    _SETTINGS.custrecord_ng_pts_rcr_bill_sucss_templt;
                }

                _comm.SendCustomerEmail({
                  templateId: emailTemplate,
                  mergeId: payRecId,
                  attachId: options.invId,
                  recipientId: options.apData.custId,
                });
              }
              isSuccess = true;
            }
          } catch (err) {
            NG.log.logError(
              err,
              "Error encountered during post-payment processing"
            );
            _page.FailureRedirect(
              options.request,
              options.response,
              {
                message: "Unable to charge credit card",
                cust_id: options.apData.custId,
              },
              options.cache
            );
          }
        }
      }
    } else {
      log.audit({
        title: "Process Termination",
        details: "Unable to transform invoice into payment",
      });
      _page.FailureRedirect(
        options.request,
        options.response,
        {
          message: "Unable to apply initial payment to invoice",
          cust_id: options.apData.custId,
        },
        options.cache
      );
    }

    return isSuccess;
  };

  const data_FindAltConvFee = (options) => {
    let filt = [
        ["isinactive", "is", "F"],
        "and",
        ["custrecord_ng_ptcfd_state", "is", options.state],
      ],
      cols = [
        search.createColumn({
          name: "custrecord_ng_ptcfd_cc_pct_rate",
        }),
        search.createColumn({
          name: "custrecord_ng_ptcfd_ach_pct_rate",
        }),
      ],
      results,
      rateData;

    try {
      results = NG.tools.getSearchResultsAdv({
        type: "customrecord_ng_paytrace_conv_fee_detail",
        filterExp: filt,
        columns: cols,
      });
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered searching for alternate conv fee rates"
      );
    }

    if (!NG.tools.isEmpty(results)) {
      rateData = {
        ccInit:
          results[0].getValue({
            name: "custrecord_ng_ptcfd_cc_pct_rate",
          }) || "0.00%",
        cc:
          Number(
            `${
              results[0].getValue({
                name: "custrecord_ng_ptcfd_cc_pct_rate",
              }) || "0"
            }`.replace("%", "")
          ) / 100,
        achInit:
          results[0].getValue({
            name: "custrecord_ng_ptcfd_ach_pct_rate",
          }) || "0.00%",
        ach:
          Number(
            `${
              results[0].getValue({
                name: "custrecord_ng_ptcfd_ach_pct_rate",
              }) || "0"
            }`.replace("%", "")
          ) / 100,
      };
    }

    return rateData;
  };

  const data_CreateConvFeeInvoice = (options) => {
    let cfInvId;

    try {
      let cfInv = record.transform({
        fromType: "customer",
        fromId: options.custId,
        toType: "invoice",
        isDynamic: true,
      });

      cfInv.setValue({ fieldId: "istaxable", value: false });
      cfInv.setValue({ fieldId: "taxitem", value: "-8" });
      cfInv.setValue({ fieldId: "handlingcost", value: 0 });
      cfInv.setValue({ fieldId: "shippingcost", value: 0 });

      cfInv.selectNewLine({ sublistId: "item" });
      cfInv.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "item",
        value: options.convFeeItem,
      });
      cfInv.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "price",
        value: "-1",
      });
      cfInv.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "rate",
        value: options.convFeeAmount,
      });
      cfInv.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "quantity",
        value: 1,
      });
      cfInv.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "istaxable",
        value: false,
      });
      cfInv.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "taxcode",
        value: "-8",
      });
      cfInv.commitLine({ sublistId: "item" });

      cfInvId = cfInv.save({
        ignoreMandatoryFields: true,
        enableSourcing: true,
      });
    } catch (err) {
      NG.log.logError(err, "Error encountered creating conv fee invoice");
    }

    return cfInvId;
  };

  const data_AddConvFeeItemToOrder = (options) => {
    try {
      let soRec = record.load({
        type: "salesorder",
        id: options.orderId,
        isDynamic: true,
      });
      soRec.selectNewLine({ sublistId: "item" });
      soRec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "item",
        value: options.convFeeItem,
      });
      soRec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "price",
        value: "-1",
      });
      soRec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "rate",
        value: options.convFeeAmount,
      });
      soRec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "quantity",
        value: 1,
      });
      soRec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "istaxable",
        value: false,
      });
      soRec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "taxcode",
        value: "-8",
      });
      soRec.commitLine({ sublistId: "item" });

      soRec.save({ ignoreMandatoryFields: true, enableSourcing: true });
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered updating originating order with conv fee item"
      );
    }
  };

  /**
   *
   * @param {Object} options
   * @param {String} options.refFieldId
   * @param {String|Number} [options.tranId]
   * @param {String[]|Number[]} [options.invIds]
   * @param {Record} options.currRec
   * @param {Boolean} [options.checkInv]
   * @returns {boolean}
   */
  const data_FindCompletedPayment = (options) => {
    options.checkInv = options.checkInv || false;
    let isComplete = false,
      filt = [
        [options.refFieldId, "anyof", [options.tranId]],
        "and",
        ["custrecord_ng_ptr_completed", "is", "F"],
        "and",
        ["custrecord_ng_ptr_is_refund", "is", "T"],
      ],
      cols = [
        search.createColumn({
          name: "custrecord_ng_ptr_approval_code",
        }),
        search.createColumn({
          name: "custrecord_ng_ptr_transaction_id",
        }),
      ],
      payCompSearch;

    if (!NG.tools.isEmpty(options.invIds) && options.invIds.length > 0) {
      filt[0] = [options.refFieldId, "anyof", options.invIds];
    }

    try {
      payCompSearch = NG.tools.getSearchResults(
        "customrecord_ng_paytrace_ref",
        filt,
        cols
      );
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered searching for completed refs for the transaction"
      );
    }

    if (!NG.tools.isEmpty(payCompSearch) && payCompSearch.length > 0) {
      if (options.checkInv) {
        let invIdArr = [];
        payCompSearch.forEach((res) => {
          invIdArr = invIdArr.concat(
            res.getValue({ name: "custrecord_ng_ptr_invoice" }).split(",")
          );
        });
        let finalInvIdArr = invIdArr.filter((value, index, self) => {
          return self.indexOf(value) === index;
        });
        let balanceCheck = verifyInvoiceBalances(finalInvIdArr);

        if (balanceCheck.invFullyPaid) {
          let authCode, tranId, refId;
          payCompSearch.forEach((res) => {
            let invIdList = res
              .getValue({ name: "custrecord_ng_ptr_invoice" })
              .split(",");
            if (
              NG.tools.isEmpty(authCode) &&
              invIdList.includes(balanceCheck.invId)
            ) {
              authCode = res.getValue({
                name: "custrecord_ng_ptr_approval_code",
              });
              tranId = res.getValue({
                name: "custrecord_ng_ptr_transaction_id",
              });
              refId = res.id;
            }
          });

          options.currRec.setValue({
            fieldId: "ccapproved",
            value: true,
          });
          options.currRec.setValue({
            fieldId: "authcode",
            value: authCode || "N/A",
          });
          options.currRec.setValue({
            fieldId: "pnrefnum",
            value: tranId || "N/A",
          });
          options.currRec.setValue({
            fieldId: "custbody_paytrace_tranid",
            value: tranId || "N/A",
          });
          options.currRec.setValue({
            fieldId: "custbody_ng_paytrace_data_ref",
            value: refId || "",
          });
          isComplete = true;
        }
      } else {
        options.currRec.setValue({ fieldId: "ccapproved", value: true });
        options.currRec.setValue({
          fieldId: "authcode",
          value: payCompSearch[0].getValue({
            name: "custrecord_ng_ptr_approval_code",
          }),
        });
        options.currRec.setValue({
          fieldId: "pnrefnum",
          value: payCompSearch[0].getValue({
            name: "custrecord_ng_ptr_transaction_id",
          }),
        });
        options.currRec.setValue({
          fieldId: "custbody_paytrace_tranid",
          value: payCompSearch[0].getValue({
            name: "custrecord_ng_ptr_transaction_id",
          }),
        });
        options.currRec.setValue({
          fieldId: "custbody_ng_paytrace_data_ref",
          value: payCompSearch[0].id,
        });
        isComplete = true;
      }
    }

    return isComplete;
  };

  const verifyInvoiceBalances = (invIdList) => {
    let invFilt = [
        ["internalid", "anyof", invIdList],
        "and",
        ["mainline", "is", "T"],
      ],
      invCols = [
        search.createColumn({ name: "tranid" }),
        search.createColumn({ name: "amountremaining" }),
      ],
      invResults,
      invFullyPaid = false,
      firstInvId,
      firstInvNum;

    try {
      invResults = NG.tools.getSearchResults("invoice", invFilt, invCols);
    } catch (err) {
      NG.log.logError(err, "Error encountered getting invoice balances");
    }

    if (!NG.tools.isEmpty(invResults)) {
      invResults.forEach((res) => {
        let invBalance = Number(
          res.getValue({ name: "amountremaining" }) || "0"
        );
        if (invBalance <= 0) {
          invFullyPaid = true;
          if (NG.tools.isEmpty(firstInvId)) {
            firstInvId = res.id;
          }
          if (NG.tools.isEmpty(firstInvNum)) {
            firstInvNum = res.getValue({ name: "tranid" });
          }
          log.audit({
            title: "Invoice is fully paid",
            details: `Invoice #: ${res.getValue({
              name: "tranid",
            })}`,
          });
        }
      });
    }

    return {
      invFullyPaid: invFullyPaid,
      invId: firstInvId || "",
      invNum: firstInvNum || "",
    };
  };

  /**
   *
   * @param {Object} options
   * @param {String} options.bgColorDefault Default PayNow submit button background color
   * @param {String} options.brdColorDefault Default PayNow submit button border color
   * @param {String} options.txtColorDefault PayNow submit button text color
   * @param {String} options.bgColorActive Default PayNow submit button background color when active
   * @param {String} options.brdColorActive Default PayNow submit button border color when active
   * @param {String} options.txtColorActive Default PayNow submit button text color when active
   * @returns {String}
   */
  const data_PayNowButtonColorFormat = (options) => {
    return `
            .btn-primary {
                background-color: ${options.bgColorDefault} !important;
                border-color: ${options.brdColorDefault} !important;
				color: ${options.txtColorDefault} !important;
            }
            .btn-primary:hover,
            .btn-primary:active,
            .btn-primary:visited,
            .btn-primary:focus,
            .btn-primary:active:focus {
                background-color: ${options.bgColorActive} !important;
                border-color: ${options.brdColorActive} !important;
                color: ${options.txtColorActive} !important;
            }
			`;
  };

  const data_GetPaymentFrequencyData = () => {
    let fFilt = [["isinactive", "is", "F"]],
      fCols = [
        search.createColumn({
          name: SHARED.Mappings.fields.autoPayFreq.name,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.autoPayFreq.freq,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.autoPayFreq.descr,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.autoPayFreq.termDescr,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.autoPayFreq.isDefault,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.autoPayFreq.freqIsDays,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.autoPayFreq.freqIsMths,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.autoPayFreq.freqIsYrs,
        }),
      ],
      fResults,
      fData;

    try {
      fResults = NG.tools.getSearchResults(
        SHARED.Mappings.recordTypes.autoPayFreq,
        fFilt,
        fCols
      );
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered retrieving AutoPay frequency data"
      );
    }

    if (!NG.tools.isEmpty(fResults)) {
      fData = {};
      fResults.forEach((res) => {
        fData[res.id] = {
          id: res.id,
          name: res.getValue({
            name: SHARED.Mappings.fields.autoPayFreq.name,
          }),
          freq: Number(
            res.getValue({
              name: SHARED.Mappings.fields.autoPayFreq.freq,
            })
          ),
          descr: res.getValue({
            name: SHARED.Mappings.fields.autoPayFreq.descr,
          }),
          termDesc: res.getValue({
            name: SHARED.Mappings.fields.autoPayFreq.termDescr,
          }),
          isDefault: res.getValue({
            name: SHARED.Mappings.fields.autoPayFreq.isDefault,
          }),
          isDays: res.getValue({
            name: SHARED.Mappings.fields.autoPayFreq.freqIsDays,
          }),
          isMonths: res.getValue({
            name: SHARED.Mappings.fields.autoPayFreq.freqIsMths,
          }),
          isYears: res.getValue({
            name: SHARED.Mappings.fields.autoPayFreq.freqIsYrs,
          }),
        };
      });
    }

    return fData || {};
  };

  /**
   *
   * @param {Object} options
   * @param {String|Number} options.invId
   * @param {String|Number} options.status
   * @param {Number} options.seq
   * @param {Date} options.payDate
   * @param {Number} options.payAmount
   * @param {Boolean} options.finalPayment
   * @param {Boolean} options.logError
   *
   * @returns {void}
   */
  const data_CreatePaymentSchedule = (options) => {
    if (NG.tools.isEmpty(options.logError)) {
      options.logError = true;
    }

    try {
      let paySchedRec = record.create({
        type: SHARED.Mappings.recordTypes.autoPaySched,
      });
      paySchedRec.setValue({
        fieldId: SHARED.Mappings.fields.autoPaySched.invoice,
        value: options.invId,
      });
      paySchedRec.setValue({
        fieldId: SHARED.Mappings.fields.autoPaySched.paySeq,
        value: options.seq,
      });
      paySchedRec.setValue({
        fieldId: SHARED.Mappings.fields.autoPaySched.payDate,
        value: NG.time.stringToDate(options.payDate),
      });
      paySchedRec.setValue({
        fieldId: SHARED.Mappings.fields.autoPaySched.payAmount,
        value: options.payAmount,
      });
      paySchedRec.setValue({
        fieldId: SHARED.Mappings.fields.autoPaySched.isFinalPay,
        value: options.finalPayment,
      });
      paySchedRec.setValue({
        fieldId: SHARED.Mappings.fields.autoPaySched.status,
        value: options.status || "3",
      });
      paySchedRec.save({
        ignoreMandatoryFields: true,
        enableSourcing: true,
      });
    } catch (err) {
      if (options.logError) {
        NG.log.logError(
          err,
          "Error encountered creating payment schedule record"
        );
      } else {
        throw err;
      }
    }
  };

  /**
   *
   * @param {Object} options
   * @param {String|Number} options.id
   * @param {String|Number} options.invId
   * @param {String|Number} options.status
   * @param {Number} options.seq
   * @param {Date} options.payDate
   * @param {Number} options.payAmount
   * @param {Boolean} options.finalPayment
   * @param {Boolean} options.logError
   *
   * @returns {void}
   */
  const data_UpdatePaymentSchedule = (options) => {
    if (NG.tools.isEmpty(options.logError)) {
      options.logError = true;
    }

    try {
      let updVals = {};
      if (!NG.tools.isEmpty(options.invId)) {
        updVals[SHARED.Mappings.fields.autoPaySched.invoice] = options.invId;
      }
      if (!NG.tools.isEmpty(options.seq)) {
        updVals[SHARED.Mappings.fields.autoPaySched.paySeq] = options.seq;
      }
      if (!NG.tools.isEmpty(options.payDate)) {
        updVals[SHARED.Mappings.fields.autoPaySched.payDate] =
          NG.time.dateToString(NG.time.stringToDate(options.payDate));
      }
      if (!NG.tools.isEmpty(options.payAmount)) {
        updVals[SHARED.Mappings.fields.autoPaySched.payAmount] =
          options.payAmount;
      }
      if (!NG.tools.isEmpty(options.finalPayment)) {
        updVals[SHARED.Mappings.fields.autoPaySched.isFinalPay] =
          options.finalPayment;
      }
      if (!NG.tools.isEmpty(options.status)) {
        updVals[SHARED.Mappings.fields.autoPaySched.status] = options.status;
      }

      record.submitFields({
        type: SHARED.Mappings.recordTypes.autoPaySched,
        id: options.id,
        values: updVals,
      });
    } catch (err) {
      if (options.logError) {
        NG.log.logError(
          err,
          "Error encountered creating payment schedule record"
        );
      } else {
        throw err;
      }
    }
  };

  const data_BuildBillingAddress = (billAddressData) => {
    let BillAddr2 = billAddressData.billaddress2,
      BillAddr2Line = !NG.tools.isEmpty(BillAddr2) ? `, ${BillAddr2}` : "",
      BillAddr = `${billAddressData.billaddress1}${BillAddr2Line}
			${billAddressData.billcity}, ${billAddressData.billstate} ${billAddressData.billzipcode}
			${billAddressData.billcountry_text}`;

    return BillAddr.replace(/\t/g, "");
  };

  /**
   *
   * @param {Object} options
   * @param {String|Number} options.invId
   *
   * @returns {String}
   */
  const data_BuildPendingPaymentHTML = (options) => {
    let schedFilt = [
        [SHARED.Mappings.fields.autoPaySched.invoice, "anyof", [options.invId]],
        "and",
        ["isinactive", "is", "F"],
      ],
      schedCols = [
        search.createColumn({
          name: SHARED.Mappings.fields.autoPaySched.payDate,
          sort: search.Sort.ASC,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.autoPaySched.payAmount,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.autoPaySched.status,
        }),
        search.createColumn({
          name: SHARED.Mappings.fields.autoPaySched.payComplete,
        }),
      ],
      invPrintFields = [
        "amountremaining",
        "custbody_ng_paytrace_ap_payment_count",
      ],
      invPrintFields_S = [],
      schedResults,
      invData,
      scheduleHTML = "";

    try {
      schedResults = NG.tools.getSearchResults(
        SHARED.Mappings.recordTypes.autoPaySched,
        schedFilt,
        schedCols
      );
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered retrieving AutoPay schedule data"
      );
    }

    try {
      invData = NG.tools.getLookupFields(
        "invoice",
        options.invId,
        invPrintFields,
        invPrintFields_S,
        []
      );
    } catch (err) {
      NG.log.logError(err, "Error encountered retrieving invoice AutoPay data");
    }

    scheduleHTML += scheduleDisplayStart();
    if (!NG.tools.isEmpty(schedResults) && !NG.tools.isEmpty(invData)) {
      let unpaidEntries = schedResults.filter((x) => {
        return (
          x.getValue({
            name: SHARED.Mappings.fields.autoPaySched.status,
          }) === SHARED.Mappings.lists.autoPayStatus.values.Pending &&
          !x.getValue({
            name: SHARED.Mappings.fields.autoPaySched.payComplete,
          })
        );
      });
      // ,
      // unpaidCount = unpaidEntries.length.toFixed(0),
      // nextPayment = unpaidEntries.length > 0 ? unpaidEntries[0] : null,
      // balance = Number(invData.amountremaining || "0").toFixed(2),
      // nextAmount = !NG.tools.isEmpty(nextPayment) ? Number(nextPayment.getValue({ name : SHARED.Mappings.fields.autoPaySched.payAmount })).toFixed(2) : "0",
      // scheduleHTML = "";

      unpaidEntries.forEach((res) => {
        scheduleHTML += scheduleDisplayLine({
          schedDate: res.getValue({
            name: SHARED.Mappings.fields.autoPaySched.payDate,
          }),
          schedAmount: Number(
            res.getValue({
              name: SHARED.Mappings.fields.autoPaySched.payAmount,
            })
          ).NG_formatMoney(),
        });
      });
    } else {
      scheduleHTML += scheduleDisplayLine({
        schedDate: "&nbsp;",
        schedAmount: "&nbsp;",
      });
    }
    scheduleHTML += scheduleDisplayEnd();

    return scheduleHTML;
  };

  /**
   *
   * @returns {String}
   */
  const scheduleDisplayStart = () => {
    return `<table class="schedule"><tr class="scheduleHeader"><th class="scheduleDateHeader"><p>Payment Date</p></th><th class="scheduleAmountHeader"><p>Payment Amount</p></th></tr>`;
  };

  /**
   *
   * @param {Object} options
   * @param {String} options.schedDate
   * @param {String} options.schedAmount
   *
   * @returns {String}
   */
  const scheduleDisplayLine = (options) => {
    return `<tr class="scheduleLine"><td class="scheduleDate"><p>${options.schedDate}</p></td><td class="scheduleAmount"><p>&#36;${options.schedAmount}</p></td></tr>`;
  };

  /**
   *
   * @returns {String}
   */
  const scheduleDisplayEnd = () => {
    return `</table>`;
  };

  const data_LookupNonRefundedPaymentRefs = (options) => {
    options.invIdList = options.invIdList || [];
    options.csIdList = options.csIdList || [];

    let pRefFilt = [
        ["custrecord_ng_ptr_completed", "is", "T"],
        "and",
        ["custrecord_ng_ptr_is_refund", "is", "F"],
        "and",
        ["custrecord_ng_ptr_transaction_amount", "isnotempty", null],
      ],
      pRefCols = [
        search.createColumn({
          name: "custrecord_ng_ptr_transaction_id",
        }),
        search.createColumn({
          name: "custrecord_ng_ptr_transaction_amount",
        }),
        search.createColumn({ name: "created" }),
        search.createColumn({
          name: "custrecord_ng_ptr_masked_card_number",
        }),
        search.createColumn({
          name: "custrecord_ng_ptr_card_expiration",
        }),
        search.createColumn({ name: "custrecord_ng_ptr_card_type" }),
        search.createColumn({ name: "custrecord_ng_ptr_integration" }),
      ],
      pRefSearch;

    try {
      if (options.invIdList.length > 0 && options.csIdList.length > 0) {
        pRefFilt.push("and", [
          ["custrecord_ng_ptr_invoice", "anyof", options.invIdList],
          "or",
          ["custrecord_ng_ptr_cash_sale", "anyof", options.csIdList],
        ]);
      } else if (options.invIdList.length > 0) {
        pRefFilt.push("and", [
          "custrecord_ng_ptr_invoice",
          "anyof",
          options.invIdList,
        ]);
      } else if (options.csIdList.length > 0) {
        pRefFilt.push("and", [
          "custrecord_ng_ptr_cash_sale",
          "anyof",
          options.csIdList,
        ]);
      } else if (!NG.tools.isEmpty(options.cId)) {
        pRefFilt.push("and", [
          "custrecord_ng_ptr_customer",
          "anyof",
          [options.cId],
        ]);
      }

      if (
        !NG.tools.isEmpty(
          _SETTINGS[SHARED.SettingsMap.general.rfndMonthsLookback]
        )
      ) {
        let lookBack = Number(
          _SETTINGS[SHARED.SettingsMap.general.rfndMonthsLookback] || "0"
        );
        if (lookBack > 0) {
          let today = new Date(),
            updM = today.getMonth() - lookBack,
            filterDateObj = new Date(today.getFullYear(), updM, 1, 0, 0, 0),
            filterDateTxt = NG.time.dateToString(filterDateObj);

          pRefFilt.push("and", [
            "custrecord_ng_ptr_transaction_datetime",
            "onorafter",
            filterDateTxt,
          ]);
        }
      }

      try {
        pRefSearch = NG.tools.getSearchResults(
          "customrecord_ng_paytrace_ref",
          pRefFilt,
          pRefCols
        );
      } catch (err) {
        NG.log.logError(err, "Error encountered retrieving payment ref data");
      }
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered building payment ref data search"
      );
    }

    return pRefSearch;
  };

  /**
   *
   * @param {Object} options
   * @param {String|Number} options.schedId
   * @param {String|Number} [options.payRecId]
   * @param {Number} [options.appAmount]
   * @param {Number} [options.updAmount]
   * @param {Number} [options.status]
   * @returns {void}
   */
  const data_UpdateScheduleRecord = (options) => {
    log.audit({ title: "** UpdateScheduleRecord **", details: options });
    try {
      let schedRec = record.load({
        id: options.schedId,
        type: SHARED.Mappings.recordTypes.autoPaySched,
        isDynamic: true,
      });

      if (!NG.tools.isEmpty(options.payRecId)) {
        log.audit({
          title: "current pay rec values",
          details: schedRec.getValue({
            fieldId: SHARED.Mappings.fields.autoPaySched.payRec,
          }),
        });
        let currSelect =
          schedRec.getValue({
            fieldId: SHARED.Mappings.fields.autoPaySched.payRec,
          }) || [];
        if (!currSelect.includes(options.payRecId)) {
          currSelect.push(options.payRecId);
          schedRec.setValue({
            fieldId: SHARED.Mappings.fields.autoPaySched.payRec,
            value: currSelect,
          });
        }
        log.audit({
          title: "updated pay rec values",
          details: schedRec.getValue({
            fieldId: SHARED.Mappings.fields.autoPaySched.payRec,
          }),
        });

        if (
          NG.tools.isEmpty(options.updAmount) &&
          NG.tools.isEmpty(options.appAmount)
        ) {
          schedRec.setValue({
            fieldId: SHARED.Mappings.fields.autoPaySched.payComplete,
            value: true,
          });
          schedRec.setValue({
            fieldId: SHARED.Mappings.fields.autoPaySched.actualPayDate,
            value: new Date(),
          });
        }
      }

      if (!NG.tools.isEmpty(options.updAmount) && options.updAmount > 0) {
        schedRec.setValue({
          fieldId: SHARED.Mappings.fields.autoPaySched.payAmount,
          value: Number(options.updAmount).toFixed(2),
        });
      }
      if (!NG.tools.isEmpty(options.appAmount) && options.appAmount > 0) {
        schedRec.setValue({
          fieldId: SHARED.Mappings.fields.autoPaySched.appAmount,
          value: Number(options.appAmount).toFixed(2),
        });
      }

      if (!NG.tools.isEmpty(options.status)) {
        schedRec.setValue({
          fieldId: SHARED.Mappings.fields.autoPaySched.status,
          value: options.status,
        });
      }

      try {
        schedRec.save();
      } catch (err) {
        NG.log.logError(
          err,
          "Error encountered saving updated payment schedule record",
          `Options: ${JSON.stringify(options)}`
        );
      }
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered updating payment schedule record",
        `Options: ${JSON.stringify(options)}`
      );
    }
  };

  //endregion

  //region PAGE

  /**
   * PayNow credit card payment success page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const SuccessRedirect = (request, response, data, cache) => {
    log.audit({ title: "SuccessRedirect", details: data });
    let cacheIndex = `_pt_sr_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "S",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow ACH payment success page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const SuccessACHRedirect = (request, response, data, cache) => {
    log.audit({ title: "SuccessACHRedirect", details: data });
    let cacheIndex = `_pt_sar_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "A",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow statement credit card payment success page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const SuccessStatementRedirect = (request, response, data, cache) => {
    log.audit({ title: "SuccessStatementRedirect", details: data });
    let cacheIndex = `_pt_ssr_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "M",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    log.audit({
      title: `SuccessStatementRedirect - encoded data length (${cacheIndex})`,
      details: (params.ses || "").length,
    });
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow statement ACH payment success page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const SuccessStatementACHRedirect = (request, response, data, cache) => {
    log.audit({ title: "SuccessStatementACHRedirect", details: data });
    let cacheIndex = `_pt_ssar_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "N",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * AutoPay Card Entry and Auth success page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const SuccessQuickBillCardAuthRedirect = (request, response, data, cache) => {
    log.audit({ title: "SuccessQuickBillCardAuthRedirect", details: data });
    let cacheIndex = `_pt_qba_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "QB",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * AutoPay Card Entry and Auth success page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const SuccessAutoPayCardAuthRedirect = (request, response, data, cache) => {
    log.audit({ title: "SuccessAutoPayCardAuthRedirect", details: data });
    let cacheIndex = `_pt_apca_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "APS",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * AutoPay Card Update success page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const SuccessAutoPayCardUpdateRedirect = (request, response, data, cache) => {
    log.audit({ title: "SuccessAutoPayCardUpdateRedirect", details: data });
    let cacheIndex = `_pt_apcu_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "APU",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow credit card payment success page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const SuccessDepositRedirect = (request, response, data, cache) => {
    log.audit({ title: "SuccessDepositRedirect", details: data });
    let cacheIndex = `_pt_srd_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "DS",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow ACH payment success page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const SuccessDepositACHRedirect = (request, response, data, cache) => {
    log.audit({ title: "SuccessDepositACHRedirect", details: data });
    let cacheIndex = `_pt_sard_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "DA",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow failure page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const FailureRedirect = (request, response, data, cache) => {
    log.audit({ title: "FailureRedirect", details: data });
    let cacheIndex = `_pt_fr_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "F",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow invoice already paid page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const PaidRedirect = (request, response, data, cache) => {
    log.audit({ title: "PaidRedirect", details: data });
    let cacheIndex = `_pt_pr_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "P",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow invoice already paid page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const BilledRedirect = (request, response, data, cache) => {
    log.audit({ title: "BilledRedirect", details: data });
    let cacheIndex = `_pt_br_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "B",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow invoice already paid page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const ClosedCancelledRedirect = (request, response, data, cache) => {
    log.audit({ title: "ClosedCancelledRedirect", details: data });
    let cacheIndex = `_pt_ccr_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "CC",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow invoice already paid page redirect
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {Object} data Process data
   * @param {cache} cache
   * @returns {void}
   */
  const DepositPaidRedirect = (request, response, data, cache) => {
    log.audit({ title: "DepositPaidRedirect", details: data });
    let cacheIndex = `_pt_prd_${NG.tools.generateUUID()}`;
    NG.cacheMgt.setCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: cacheIndex,
      data: encode.convert({
        string: JSON.stringify(data),
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64,
      }),
    });
    let params = {
      rd: "DP",
      ses: encode.convert({
        string: cacheIndex,
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64_URL_SAFE,
      }),
    };
    redirect.toSuitelet({
      scriptId: runtime.getCurrentScript().id,
      deploymentId: runtime.getCurrentScript().deploymentId,
      isExternal: true,
      parameters: params,
    });
  };

  /**
   * PayNow failure page creation
   * @param {ServerResponse} response
   */
  const failurePage = (response) => {
    log.audit({ title: "failurePage", details: "" });
    let pageHTML = "",
      fileID = Number(_SETTINGS[SHARED.SettingsMap.shared.systemErrorTemplate]);

    if (!NG.tools.isEmpty(fileID)) {
      let htmlFile = file.load({ id: fileID });
      pageHTML = htmlFile.getContents();
    } else {
      pageHTML =
        "<html><head><title>System Error</title></head><body><p>A problem occurred while processing your request.</p></body></html>";
    }
    response.write(pageHTML);
  };

  /**
   * PayNow page redirection handler
   * @param {ServerRequest} request
   * @param {ServerResponse} response
   * @param {cache} cache
   */
  const handleRedirect = (request, response, cache) => {
    log.audit({ title: "handleRedirect", details: "" });
    let reDir = request.parameters["rd"],
      encSessId = request.parameters["ses"],
      sessId = encode.convert({
        string: encSessId,
        inputEncoding: encode.Encoding.BASE_64_URL_SAFE,
        outputEncoding: encode.Encoding.UTF_8,
      }),
      encSessData = NG.cacheMgt.getCacheData({
        cache: cache,
        cacheName: _PAYTRACE_CACHE_NAME,
        cacheIndex: sessId,
      }),
      sessData,
      fileID;

    NG.cacheMgt.deleteCacheData({
      cache: cache,
      cacheName: _PAYTRACE_CACHE_NAME,
      cacheIndex: sessId,
    });
    if (!NG.tools.isEmpty(encSessData)) {
      try {
        sessData = data_DecodeSessionData(encSessData);
      } catch (err) {
        NG.log.logError(err, "Error encountered processing session data");
      }
    }

    switch (reDir) {
      case "S":
        fileID = Number(
          _SETTINGS[SHARED.SettingsMap.paynowInv.successTemplate]
        );
        break;
      case "A":
        fileID = Number(
          _SETTINGS[SHARED.SettingsMap.paynowInv.achSuccessTemplate]
        );
        break;
      case "M":
        fileID = Number(
          _SETTINGS[SHARED.SettingsMap.paynowStmt.successTemplate]
        );
        break;
      case "N":
        fileID = Number(
          _SETTINGS[SHARED.SettingsMap.paynowStmt.achSuccessTemplate]
        );
        break;
      case "F":
        fileID = Number(_SETTINGS[SHARED.SettingsMap.shared.failureTemplate]);
        break;
      case "P":
        fileID = Number(_SETTINGS[SHARED.SettingsMap.paynowInv.paidTemplate]);
        break;
      case "B":
        fileID = Number(_SETTINGS[SHARED.SettingsMap.paynowDep.billedTemplate]);
        break;
      case "CC":
        fileID = Number(_SETTINGS[SHARED.SettingsMap.paynowDep.closedTemplate]);
        break;
      case "APS":
        fileID = Number(_SETTINGS[SHARED.SettingsMap.autoPay.successTemplate]);
        break;
      case "APU":
        fileID = Number(
          _SETTINGS[SHARED.SettingsMap.autoPay.updateSuccessTemplate]
        );
        break;
      case "DS":
        fileID = Number(
          _SETTINGS[SHARED.SettingsMap.paynowDep.successTemplate]
        );
        break;
      case "DA":
        fileID = Number(
          _SETTINGS[SHARED.SettingsMap.paynowDep.achSuccessTemplate]
        );
        break;
      case "DP":
        fileID = Number(_SETTINGS[SHARED.SettingsMap.paynowDep.paidTemplate]);
        break;
      case "QB":
        fileID = Number(
          _SETTINGS[SHARED.SettingsMap.cardToken.successTemplate]
        );
        break;
      default:
        NG.log.logError(null, "No redirect action was set");
        failurePage(response);
        return;
    }

    if (
      !NG.tools.isEmpty(sessData) &&
      !NG.tools.isEmpty(fileID) &&
      !isNaN(fileID) &&
      fileID !== 0
    ) {
      let htmlFile = file.load({ id: fileID }),
        pageHTML = htmlFile.getContents(),
        baseURL = url.resolveScript({
          scriptId: runtime.getCurrentScript().id,
          deploymentId: runtime.getCurrentScript().deploymentId,
          returnExternalUrl: true,
        }),
        targetURL;

      log.audit({ title: "sessData", details: sessData });
      if (!NG.tools.isEmpty(sessData.inv_id)) {
        targetURL = `${baseURL}&enc=${NG.tools.B64.encode(
          sessData.inv_id
        )}&ses=${encSessData}`;
      } else if (!NG.tools.isEmpty(sessData.cust_id)) {
        targetURL = `${baseURL}&enc=${NG.tools.B64.encode(
          sessData.cust_id
        )}&ses=${encSessData}`;
      } else if (!NG.tools.isEmpty(sessData.so_id)) {
        targetURL = `${baseURL}&enc=${NG.tools.B64.encode(
          sessData.so_id
        )}&ses=${encSessData}`;
      } else {
        NG.log.logError(null, "No source record ID was provided");
        failurePage(response);
        return;
      }

      log.audit({ title: "sessData", details: sessData });

      pageHTML = pageHTML
        .replace(/\{PAY_ERR\}/g, sessData.message || "")
        .replace(/\{PAY_URL\}/g, targetURL)
        .replace(/\{INV_NUM\}/g, sessData.inv_num || "")
        .replace(/\{PAY_NUM\}/g, sessData.pay_num || "")
        .replace(/\{AMT_PYD\}/g, sessData.amt_paid || "")
        .replace(/\{CRD_NUM\}/g, sessData.masked_card || "")
        .replace(/\{ACT_NUM\}/g, sessData.masked_account || "")
        .replace(/\{RTE_NUM\}/g, sessData.masked_routing || "")
        .replace(/\{PTC_TID\}/g, sessData.pt_id || "")
        .replace(/\{DUE_DTE\}/g, sessData.due_date || "")
        .replace(/\{INV_TOT\}/g, sessData.inv_tot || "")
        .replace(/\{INV_BAL\}/g, sessData.remain_bal || "")
        .replace(/\{CST_ID\}/g, sessData.cust_id || "")
        .replace(/\{FPY_AMT\}/g, sessData.frst_pay_amt || "")
        .replace(/\{FPY_DTE\}/g, sessData.frst_pay_date || "")
        .replace(/\{SCD_AMT\}/g, sessData.sched_amt || "")
        .replace(/\{PAY_FRQ\}/g, sessData.pay_freq || "")
        .replace(/\{CRD_DTA\}/g, sessData.card_data || "")
        .replace(/\{PAY_CNT\}/g, sessData.pay_count || "")
        .replace(/\{SO_ID\}/g, sessData.so_id || "")
        .replace(/\{SO_NUM\}/g, sessData.so_num || "")
        .replace(/\{SO_TOT\}/g, sessData.so_tot || "")
        .replace(/\{DEP_NUM\}/g, sessData.dep_num || "")
        .replace(/\{DSP_DEP\}/g, sessData.dep_disp || "")
        .replace(/\{DSP_REQ\}/g, sessData.req_dep_disp || "")
        .replace(/\{DEP_TOT\}/g, sessData.req_deposit_total || "")
        .replace(/\{DEP_BAL\}/g, sessData.req_deposit_bal || "")
        .replace(/\{DEP_APP\}/g, sessData.dep_total || "")
        .replace(/\{UPD_CHG\}/g, sessData.paid_amount || "")
        .replace(/\{CUST_NAME\}/g, sessData.cust_name || "");

      response.write(pageHTML);
    } else {
      NG.log.logError(
        null,
        "Empty session data or missing template file ID",
        `Session data: ${JSON.stringify(sessData || {})} -- File ID: ${fileID}`
      );
      failurePage(response);
    }
  };

  const page_AddRefundableTransactionField = (form, options) => {
    let prevPymtSelectField = form.addField({
        id: "custpage_completed_charges",
        type: serverWidget.FieldType.SELECT,
        label: "Previous Payment Select",
        container: "payment",
      }),
      ccPymtData = data_GetRefundableTransactions(options),
      ccPymtList = ccPymtData.map((pymt) => {
        return pymt.id;
      }),
      defaultTranId;

    prevPymtSelectField.addSelectOption({
      value: "",
      text: "",
      isSelected: true,
    });
    prevPymtSelectField.setHelpText({
      help: "Select an existing card charge to refund.",
    });
    form
      .addField({
        id: "custpage_pt_trans_id",
        type: serverWidget.FieldType.TEXT,
        label: "PayTrace Transaction ID",
        container: "payment",
      })
      .setHelpText({
        help: "Enter the transaction ID/number from PayTrace for which you wish to process a refund.",
      });

    ccPymtData.forEach((data) => {
      let defVal = false;
      if (ccPymtData.length === 1) {
        defVal = true;
      } else if (
        !NG.tools.isEmpty(options.defaultTranId) &&
        data.id === options.defaultTranId
      ) {
        defVal = true;
      }
      prevPymtSelectField.addSelectOption({
        value: data.id,
        text: data.text,
        isSelected: defVal,
      });
      if (defVal) {
        defaultTranId = data.id;
      }
    });

    form
      .addField({
        id: "custpage_ccpymtlist",
        type: serverWidget.FieldType.LONGTEXT,
        label: "ccidlist",
      })
      .updateDisplayType({
        displayType: serverWidget.FieldDisplayType.HIDDEN,
      }).defaultValue = JSON.stringify(ccPymtList);

    form
      .addField({
        id: "custpage_ccpymtdata",
        type: serverWidget.FieldType.LONGTEXT,
        label: "ccpmtdat",
      })
      .updateDisplayType({
        displayType: serverWidget.FieldDisplayType.HIDDEN,
      }).defaultValue = JSON.stringify(ccPymtData);
    return defaultTranId;
  };

  const page_AddE2EE = (context) => {
    let e2ee = getFileContentsByPath("../pt-oth/paytrace-e2ee.js");

    if (!NG.tools.isEmpty(e2ee)) {
      context.form.addField({
        id: "custpage_paytrace_e2ee",
        type: serverWidget.FieldType.INLINEHTML,
        label: "e2ee",
      }).defaultValue = `<script type="text/javascript">${e2ee}</script>`;
    } else {
      log.audit({
        title: "failed to retrieve paytrace e2ee contents",
        details: "",
      });
    }
  };

  //endregion

  //region SYS

  /**
   * Inserts warning message banner into current record's form to display license expiration notice
   * @param {Form} form Current record's form object
   * @param {string|number} role Current user's role ID; Will only create banner if role indicates administrator
   */
  const sys_ExpiredLicenseNotice = (form, role) => {
    if (Number(role) === 3) {
      sys_DisplayWarningBanner(
        form,
        "Product Expiration",
        "Your license for NetSuite PayTrace Integration has expired. Please renew your license to continue using the product."
      );
    }

    NG.log.logError(
      null,
      "NewGen Product License Expiration",
      "License for NetSuite PayTrace Integration has expired."
    );
  };

  /**
   *
   * @param {Object} options
   * @param {string} options.refRecSessID
   * @param {boolean} options.paymentIsSuccess
   * @param {boolean} options.refRecUpdateSuccess
   * @param {string} options.bannerSessID
   * @param {string} options.recType
   * @param {Object} options.noticeValues
   */
  const sys_SetStatusBanner = (options) => {
    log.audit({
      title: "sys_SetStatusBanner() -- options",
      details: options,
    });
    let refRecCreated = false,
      sessObjRefRec = runtime
        .getCurrentSession()
        .get({ name: options.refRecSessID });

    if (!NG.tools.isEmpty(sessObjRefRec)) {
      let sessDataRR;
      try {
        sessDataRR = data_DecodeSessionData(sessObjRefRec);
      } catch (err) {
        NG.log.logError(
          err,
          "Error encountered processing ref rec created sess data"
        );
        runtime
          .getCurrentSession()
          .set({ name: options.refRecSessID, value: "" });
      }
      if (!NG.tools.isEmpty(sessDataRR)) {
        runtime
          .getCurrentSession()
          .set({ name: options.refRecSessID, value: "" });
        refRecCreated = sessDataRR.refRecCreated;
      }
    }

    if (refRecCreated) {
      let bannerTitle, bannerMsg, bannerType;
      if (options.paymentIsSuccess) {
        bannerTitle = `'${options.recType}' ${options.noticeValues.func1} Processed Successfully`;
        if (options.refRecUpdateSuccess) {
          bannerType = "SUCCESS";
          bannerMsg = `We successfully ${options.noticeValues.func2} ${
            options.noticeValues.method || "N/A"
          } for $${options.noticeValues.amount || "N/A"} on PT transaction ID ${
            options.noticeValues.payTraceId || "N/A"
          }`;
        } else {
          bannerType = "PARTIAL";
          bannerMsg = `We successfully ${options.noticeValues.func2} ${
            options.noticeValues.method || "N/A"
          } for $${options.noticeValues.amount || "N/A"} on PT transaction ID ${
            options.noticeValues.payTraceId || "N/A"
          }, but reference record was not updated`;
        }
      } else {
        bannerType = "ERROR";
        bannerTitle = `'${options.recType}' ${options.noticeValues.func1} Processing Failed`;
        bannerMsg = `${options.noticeValues.func1} was completed but ${options.recType} was not successfully processed`;
      }

      runtime.getCurrentSession().set({
        name: options.bannerSessID,
        value: NG.tools.B64.encode(
          JSON.stringify({
            bannerType: bannerType,
            bannerTitle: bannerTitle,
            bannerMsg: bannerMsg,
          })
        ),
      });
    }
  };

  /**
   * Adds banner message to screen
   * @param {Form} form
   * @param {string} bannerSessID
   */
  const sys_DisplayStatusBanner = (form, bannerSessID) => {
    let bannerSessObj = runtime.getCurrentSession().get({ name: bannerSessID });

    if (!NG.tools.isEmpty(bannerSessObj)) {
      let sessDataBanner;
      try {
        sessDataBanner = data_DecodeSessionData(bannerSessObj);
      } catch (err) {
        NG.log.logError(
          err,
          "Error encountered processing encoded PT banner data"
        );
        runtime.getCurrentSession().set({ name: bannerSessID, value: "" });
      }
      log.audit({
        title: "sys_DisplayStatusBanner() -- sessDataBanner",
        details: sessDataBanner,
      });
      if (!NG.tools.isEmpty(sessDataBanner)) {
        runtime.getCurrentSession().set({ name: bannerSessID, value: "" });
        if (sessDataBanner.bannerType === "SUCCESS") {
          sys_DisplaySuccessBanner(
            form,
            sessDataBanner.bannerTitle,
            sessDataBanner.bannerMsg
          );
        } else if (sessDataBanner.bannerType === "ERROR") {
          sys_DisplayErrorBanner(
            form,
            sessDataBanner.bannerTitle,
            sessDataBanner.bannerMsg
          );
        } else if (sessDataBanner.bannerType === "PARTIAL") {
          sys_DisplayConfirmationBanner(
            form,
            sessDataBanner.bannerTitle,
            sessDataBanner.bannerMsg
          );
        }
      }
    }
  };

  /**
   * Inserts error message banner into current record's form
   * @param form Current record's form object
   * @param title Banner title
   * @param message Banner message
   */
  const sys_DisplayErrorBanner = (form, title, message) => {
    let rand = NG.tools.randomString(0, 6);
    sys_DisplayBanner(
      form,
      title,
      message,
      "message.Type.ERROR",
      `custpage_ng_pt_error_${rand}`,
      `pt error ${rand}`
    );
  };

  /**
   * Inserts confirmation message banner into current record's form
   * @param form Current record's form object
   * @param title Banner title
   * @param message Banner message
   */
  const sys_DisplayConfirmationBanner = (form, title, message) => {
    let rand = NG.tools.randomString(0, 6);
    sys_DisplayBanner(
      form,
      title,
      message,
      "message.Type.INFORMATION",
      `custpage_ng_pt_conf_${rand}`,
      `pt confirmation ${rand}`
    );
  };

  /**
   * Inserts success message banner into current record's form
   * @param form Current record's form object
   * @param title Banner title
   * @param message Banner message
   */
  const sys_DisplaySuccessBanner = (form, title, message) => {
    let rand = NG.tools.randomString(0, 6);
    sys_DisplayBanner(
      form,
      title,
      message,
      "message.Type.CONFIRMATION",
      `custpage_ng_pt_success_${rand}`,
      `pt success ${rand}`
    );
  };

  /**
   * Inserts warning message banner into current record's form
   * @param form Current record's form object
   * @param title Banner title
   * @param message Banner message
   */
  const sys_DisplayWarningBanner = (form, title, message) => {
    let rand = NG.tools.randomString(0, 6);
    sys_DisplayBanner(
      form,
      title,
      message,
      "message.Type.WARNING",
      `custpage_ng_pt_warning_${rand}`,
      `pt warning ${rand}`
    );
  };

  const sys_DisplayBanner = (
    form,
    title,
    message,
    type,
    fieldId,
    fieldLabel
  ) => {
    form.addField({
      id: fieldId,
      type: serverWidget.FieldType.INLINEHTML,
      label: fieldLabel,
    }).defaultValue = `
					<script type="text/javascript">
						setTimeout(() => {
							require(['N/currentRecord', 'N/ui/message'], (currentRecord, message) => {
								let ngPTBanner = message.create({
										title : "${title}"
									,	message : "${message}"
									,	type : ${type}
								});
								ngPTBanner.show();
							});
						}, 1500);
					</script>
				`;
  };

  const sys_GetBundlePath = (relClientPath) => {
    let bundlePath,
      folderFilt = [
        ["name", "is", "Bundle 332713"],
        "or",
        ["name", "is", "Bundle 334659"],
      ],
      folderCols = [search.createColumn({ name: "name" })],
      folderResults,
      hasErr = false;

    try {
      folderResults = NG.tools.getSearchResults(
        "folder",
        folderFilt,
        folderCols
      );
    } catch (err) {
      NG.log.logError(err, "Error encountered");
      hasErr = true;
    }

    if (!NG.tools.isEmpty(folderResults)) {
      let folderName = folderResults[0].getValue({ name: "name" });
      switch (folderName) {
        case "Bundle 332713":
        case "Bundle 334659":
          bundlePath = `/SuiteBundles/${folderName}/pt-src/${relClientPath.replace(
            "../",
            ""
          )}`;
          break;
      }
    } else if (!hasErr) {
      bundlePath = `/SuiteScripts/PayTrace SS 2.0/pt-src/${relClientPath.replace(
        "../",
        ""
      )}`;
    }

    return bundlePath;
  };

  /**
   *
   * @param {Object} options
   * @param {String} options.scriptId
   * @param {String} options.deployId
   * @param {String} options.taskName
   * @param {Object} options.params
   * @returns {string}
   */
  const sys_TriggerMapReduce = (options) => {
    let scriptTask = task.create({
      taskType: task.TaskType.MAP_REDUCE,
      scriptId: options.scriptId,
      params: options.params,
    });
    if (!NG.tools.isEmpty(options.deployId)) {
      scriptTask.deploymentId = options.deployId;
    }

    let scriptTaskId = scriptTask.submit(),
      taskStatus = task.checkStatus(scriptTaskId).status;

    if (taskStatus !== "PENDING") {
      let ex = error.create({
        name: SHARED.Mappings.errorCodes.TaskQueue,
        message: `Job failed to queue. Job status: ${taskStatus}`,
      });
      NG.log.logError(ex, "Problem queuing job", `Job: ${options.taskName}`);
    } else {
      log.audit({
        title: "Job successfully queued",
        details: `Job: ${options.taskName}`,
      });
    }

    return taskStatus;
  };

  //endregion

  //region TOOLS

  /**
   * Adjusts passed in date object into defined timezone; Ideal for use with adjusting server date object into account timezone
   * @param {Date} date Date object
   * @param {string} tz Timezone
   * @returns {Date}
   */
  const quickDate = (date, tz) => {
    return format.parse({
      value: format.format({
        value: date,
        type: format.Type.DATETIMETZ,
        timezone: tz,
      }),
      type: format.Type.DATETIME,
    });
  };

  const convertPayTraceDateTime = (ptDateTime) => {
    let dMain = ptDateTime.split(" ")[0],
      tMain = ptDateTime.split(" ")[1],
      ampm = ptDateTime.split(" ")[2],
      y = Number(dMain.split("/")[2]),
      m = Math.round(Number(dMain.split("/")[0]) - 1),
      d = Number(dMain.split("/")[1]),
      h = Number(tMain.split(":")[0]),
      mm = Number(tMain.split(":")[1]),
      s = Number(tMain.split(":")[2]);

    if (ampm.toUpperCase() === "PM") {
      h = Math.round(h + 12);
    }

    return new Date(y, m, d, h, mm, s, 0);
  };

  const convertPayTraceDateTimeAlt = (options) => {
    let reg1 = /\d{1,2}:\d{1,2}:\d{1,2} /g,
      reg2 = /\d{1,2}:\d{1,2}:\d{1,2}$/g,
      reg3 = /\d{1,2}-\d{1,2}-\d{1,2} /g,
      reg4 = /\d{1,2}-\d{1,2}-\d{1,2}$/g,
      chargeDate;

    if (reg1.test(options.chargeDate)) {
      chargeDate = options.chargeDate.replace(/:\d{1,2} /, " ");
    } else if (reg2.test(options.chargeDate)) {
      chargeDate = options.chargeDate.replace(/:\d{1,2}$/, "");
    } else if (reg3.test(options.chargeDate)) {
      chargeDate = options.chargeDate.replace(/-\d{1,2} /, " ");
    } else if (reg4.test(options.chargeDate)) {
      chargeDate = options.chargeDate.replace(/-\d{1,2}$/, "");
    }

    return chargeDate;
  };

  const getFileContentsByPath = (filename) => {
    let filePath = `./${filename}`;
    return file.load({ id: filePath }).getContents();
  };

  const cleanPostingData = (value) => {
    let regX = /[^A-z0-9 ]/g;
    if (!util.isString(value)) {
      return "";
    } else {
      return value.replace(regX, "").replace("  ", " ");
    }
  };

  const createSelectButtonHideScript = (fieldIdList) => {
    let scriptHTML = `<script type="text/javascript">setTimeout(() => {\nlet buttonEl = null;\n`;
    fieldIdList.forEach((fieldId) => {
      scriptHTML += `buttonEl = document.getElementById("${fieldId}_popup_new");
				if (buttonEl !== null) { buttonEl.parentElement.style.display = "none"; }
				buttonEl = null;
				`;
    });
    scriptHTML += `}, 1000);</script>`;

    return scriptHTML;
  };

  const setFileOnline = (options) => {
    if (NG.tools.isEmpty(options.fileId)) {
      throw error.create({
        name: SHARED.Mappings.errorCodes.InvParam,
        message: "Parameter 'fileId' is not set",
        notifyOff: true,
      });
    }

    let fileObj = file.load({
      id: options.fileId,
    });
    fileObj.isOnline = true;
    fileObj.save();
  };

  /**
   * Sets connection global variables based upon the defined integration definition record internal ID
   * @returns {Object}
   */
  const handleGetPaytraceEndpointLoader = () => {
    if (
      _PAYTRACE_DATA &&
      runtime.envType !== runtime.EnvType.PRODUCTION &&
      !_PAYTRACE_DATA.custrecord_ng_ptid_is_sandbox
    ) {
      throw error.create({
        name: "PAYTRACE_CACHE_ERROR",
        message:
          "Cannot connect to production PayTrace account from non-production NetSuite account.",
      });
    }

    return (_PAYTRACE_ENDPOINT = _PAYTRACE_DATA.custrecord_ng_ptid_is_sandbox
      ? _PT_ENDPOINT_SB
      : _PT_ENDPOINT);
  };

  //endregion

  const _sttg = {
    GetSettings: sttg_GetSettings,
    GetSpecificSettings: sttg_GetSpecificSettings,
  };

  const _comm = {
    GetPayTraceAuth: comm_GetPayTraceAuth,
    GetPayTraceAuthValidate: comm_GetPayTraceAuthValidate,
    AuthCardPayment: comm_AuthCardPayment,
    SendCardPayment: comm_SendCardPayment,
    TransactionByIDPayment: comm_TransactionByIDPayment,
    SendACHPayment: comm_SendACHPayment,
    SendEMVPayment: comm_SendEMVPayment,
    AuthEMVPayment: comm_AuthEMVPayment,
    SendCardRefundAuth: comm_SendCardRefundAuth,
    SendCardRefundAuthTrans: comm_SendCardRefundAuthTrans,
    SendReceiptEmail: comm_SendReceiptEmail,
    GetTransactionData: comm_GetTransactionData,
    GetTransactionDataByRange: comm_GetTransactionDataByRange,
    GetPaymentCapture: comm_GetPaymentCapture,
    VoidTransaction: comm_VoidTransaction,
    ObtainLicenseInfo: comm_ObtainLicenseInfo,
    SendKeyedRefund: comm_SendKeyedRefund,
    GetBatches: comm_GetBatches,
    GetSettledTransactions: comm_GetSettledTransactions,
    GetProtectClientKey: comm_GetProtectClientKey,
    SendProtectPayment: comm_SendProtectPayment,
    SendCustomerEmail: comm_SendCustomerEmail,
    PerformCardAuthAndVoid: comm_PerformCardAuthAndVoid,
  };

  const _data = {
    GetIntegrationInfo: data_GetIntegrationInfo,
    GetLicenseValidation: data_GetLicenseValidation,
    GetCreditCards: data_GetCreditCards,
    GetProfile: data_getProfile,
    ProvideCleanRequestObject: data_provideCleanRequestObject,
    DecodeSessionData: data_DecodeSessionData,
    UpdateCardsOnCustomer: data_UpdateCardsOnCustomer,
    DetermineProfile: data_DetermineProfile,
    CreateFailurePaymentRef: data_CreateFailurePaymentRef,
    CreatePaymentReference: data_CreatePaymentReference,
    CreatePaymentReferenceOld: data_CreatePaymentReferenceOld,
    ReformatDateByCompPref: data_ReformatDateByCompPref,
    SelectProfile: data_SelectProfile,
    AutoDetermineProfile: data_AutoDetermineProfile,
    CreateAltRequestPost: data_CreateAltRequestPost,
    CreatePaymentFromInvoice: data_CreatePaymentFromInvoice,
    CreatePaymentFromStatement: data_CreatePaymentFromStatement,
    GetAmountDue: data_GetAmountDue,
    ApplyClientSettings: data_ApplyClientSettings,
    GetRefundableTransactions: data_GetRefundableTransactions,
    GetFileContentsByPath: getFileContentsByPath,
    GetPaymentScheduleRecords: data_GetPaymentScheduleRecords,
    GetAutoPayInvoiceData: data_GetAutoPayInvoiceData,
    LookupEncryptedCard: data_LookupEncryptedCard,
    GetNextBillDate: data_GetNextBillDate,
    UpdateCustomerCard: data_UpdateCustomerCard,
    CreateCardUpdatePayment: data_CreateCardUpdatePayment,
    FindAltConvFee: data_FindAltConvFee,
    CreateConvFeeInvoice: data_CreateConvFeeInvoice,
    AddConvFeeItemToOrder: data_AddConvFeeItemToOrder,
    FindCompletedPayment: data_FindCompletedPayment,
    PayNowButtonColorFormat: data_PayNowButtonColorFormat,
    GetPaymentFrequencyData: data_GetPaymentFrequencyData,
    CreatePaymentSchedule: data_CreatePaymentSchedule,
    UpdatePaymentSchedule: data_UpdatePaymentSchedule,
    BuildBillingAddress: data_BuildBillingAddress,
    BuildPendingPaymentHTML: data_BuildPendingPaymentHTML,
    LookupNonRefundedPaymentRefs: data_LookupNonRefundedPaymentRefs,
    UpdateScheduleRecord: data_UpdateScheduleRecord,
    GetMemorizedCashSaleData: data_GetMemorizedCashSaleData,
  };

  const _sys = {
    ExpiredLicenseNotice: sys_ExpiredLicenseNotice,
    DisplayErrorBanner: sys_DisplayErrorBanner,
    DisplaySuccessBanner: sys_DisplaySuccessBanner,
    DisplayConfirmationBanner: sys_DisplayConfirmationBanner,
    DisplayWarningBanner: sys_DisplayWarningBanner,
    SetStatusBanner: sys_SetStatusBanner,
    DisplayStatusBanner: sys_DisplayStatusBanner,
    GetBundlePath: sys_GetBundlePath,
    TriggerMapReduce: sys_TriggerMapReduce,
  };

  const _page = {
    SuccessRedirect: SuccessRedirect,
    SuccessACHRedirect: SuccessACHRedirect,
    SuccessStatementRedirect: SuccessStatementRedirect,
    SuccessStatementACHRedirect: SuccessStatementACHRedirect,
    SuccessDepositRedirect: SuccessDepositRedirect,
    SuccessDepositACHRedirect: SuccessDepositACHRedirect,
    SuccessAutoPayCardAuthRedirect: SuccessAutoPayCardAuthRedirect,
    SuccessAutoPayCardUpdateRedirect: SuccessAutoPayCardUpdateRedirect,
    SuccessQuickBillCardAuthRedirect: SuccessQuickBillCardAuthRedirect,
    FailureRedirect: FailureRedirect,
    PaidRedirect: PaidRedirect,
    BilledRedirect: BilledRedirect,
    ClosedCancelledRedirect: ClosedCancelledRedirect,
    DepositPaidRedirect: DepositPaidRedirect,
    FailurePage: failurePage,
    HandleRedirect: handleRedirect,
    AddRefundableTransactionField: page_AddRefundableTransactionField,
    AddE2EE: page_AddE2EE,
  };

  const _tools = {
    quickDate,
    convertPayTraceDateTime,
    convertPayTraceDateTimeAlt,
    getFileContentsByPath,
    cleanPostingData,
    createSelectButtonHideScript,
    setFileOnline,
  };

  /**
   * @typedef {Object} LicenseInfo
   * @property {string} key
   * @property {string} expiration
   * @property {Boolean} valid
   * @property {PT_Features} features
   */
  /**
   * @typedef {Object} PT_Settings
   * @property {string} custrecord_ng_pts_ach_payment_method
   * @property {string} custrecord_ng_pts_ach_success_template
   * @property {string} custrecord_ng_pts_card_select_label
   * @property {string} custrecord_ng_pts_emv_payment_method
   * @property {string} custrecord_ng_pts_failure_template
   * @property {string} custrecord_ng_pts_license_key
   * @property {string} custrecord_ng_pts_online_pay_meth
   * @property {string} custrecord_ng_pts_page_template
   * @property {string} custrecord_ng_pts_paid_template
   * @property {string} custrecord_ng_pts_profile_select_label
   * @property {string} custrecord_ng_pts_ref_fail_form
   * @property {string} custrecord_ng_pts_ref_inv_form
   * @property {string} custrecord_ng_pts_ref_order_form
   * @property {string} custrecord_ng_pt_refund_payment_method
   * @property {string} custrecord_ng_pts_statement_date
   * @property {string} custrecord_ng_pts_statement_form
   * @property {string} custrecord_ng_pts_statement_months
   * @property {string} custrecord_ng_pts_stmt_ach_scss_template
   * @property {string} custrecord_ng_pts_stmt_success_template
   * @property {string} custrecord_ng_pts_success_template
   * @property {string} custrecord_ng_pts_system_error_template
   * @property {string} accountId
   * @property {string} licenseURL
   * @property {string} productId
   * @property {Boolean} custrecord_ng_pts_dont_save_cards
   * @property {Boolean} custrecord_ng_pts_hide_def_proc_fields
   * @property {string[]} custrecord_ng_pts_other_card_pay_methods
   * @property {LicenseInfo} licenseInfo
   */
  /**
   * @typedef {Object} PT_Profile
   * @property {Boolean} custrecord_ng_ptid_ach_enabled
   * @property {Boolean} custrecord_ng_ptid_allow_keyed_refunds
   * @property {string[]} custrecord_ng_ptid_card_methods
   * @property {string} custrecord_ng_ptid_default_account
   * @property {Boolean} custrecord_ng_ptid_disabled
   * @property {Boolean} custrecord_ng_ptid_is_sandbox
   * @property {Boolean} custrecord_ng_ptid_force_keyed_refunds
   * @property {Boolean} custrecord_ng_ptid_is_default
   * @property {PublicKeyFile} custrecord_ng_ptid_pub_key_file
   * @property {string} custrecord_ng_ptid_ref_inv_form
   * @property {string} custrecord_ng_ptid_ref_ord_form
   * @property {string} id
   * @property {string} name
   */
  /**
   * @typedef {Object} PublicKeyFile
   * @property {string} internalid
   * @property {string} url
   */
  /**
   * @typedef {Object} PT_Features
   * @property {PT_Feature_Value} achLicensed
   * @property {PT_Feature_Value} emvLicensed
   * @property {PT_Feature_Value} payNow
   * @property {PT_Feature_Value} payNowDeposits
   * @property {PT_Feature_Value} payNowStatements
   * @property {PT_Feature_Value} profilesLicensed
   */
  /**
   * @typedef {Object} PT_Feature_Value
   * @property {string} value
   */

  return {
    quickDate: quickDate,

    settings: _SETTINGS,
    _LICENSE_URL_PARAMS: _LICENSE_URL_PARAMS,
    viewLink: {
      linkStyle: _VIEW_LINK_STYLE,
      linkHTML: _VIEW_LINK_HTML,
      linkScript: _VIEW_SCRIPT_HTML,
    },
    transTypes: _TRANS_TYPE,
    mappings: SHARED.Mappings,
    ptCacheName: _PAYTRACE_CACHE_NAME,
    cardTypeMap: _CARD_TYPE_MAP,
    settingsMap: SHARED.SettingsMap,
    PayNowBtnColors: SHARED.PayNowBtnColors,
    ProtectJS: {
      standard: _PROTECT_JS_ENDPOINT,
      sandbox: _PROTECT_JS_ENDPOINT_SB,
    },
    apiEndpoint: {
      standard: _PT_ENDPOINT,
      sandbox: _PT_ENDPOINT_SB,
    },
    ptCardCodes: _PT_CARD_ERROR_CODES,

    sttg: _sttg,
    comm: _comm,
    data: _data,
    sys: _sys,
    page: _page,
    tools: _tools,

    settingsRecId: "customrecord_ng_paytrace_settings",
  };
});
