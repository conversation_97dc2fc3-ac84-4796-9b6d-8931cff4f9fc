/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NModuleScope SameAccount
 */
define([
  "N/currentRecord",
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/ui/message",
  "N/ui/dialog",
  "N/url",
  "N/https",
] /**
 * @param{currentRecord} currentRecord
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{message} message
 * @param{dialog} dialog
 * @param{url} url
 * @param{https} https
 */, function (
  currentRecord,
  query,
  record,
  runtime,
  search,
  message,
  dialog,
  url,
  https,
) {
  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   */
  function pageInit(scriptContext) {}

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  function fieldChanged(scriptContext) {
    const { currentRecord: currRec, fieldId } = scriptContext;

    if (fieldId === "custrecord_ng_cs_stai_file") {
      const imageId = currRec.getValue({
        fieldId: "custrecord_ng_cs_stai_file",
      });

      if (imageId) {
        console.log("Image ID:", imageId);

        const isOnline = fileMarkedAvailableWithoutLogin(imageId);

        isOnline
          ? console.log('✅ File is marked as "Available without Login"')
          : console.warn('❌ File is NOT marked as "Available without Login"');

        if (isOnline) {
          const mainUrl = url.resolveDomain({
            hostType: url.HostType.APPLICATION,
          });
          const fileUrl = search.lookupFields({
            type: "file",
            id: imageId,
            columns: ["url"],
          }).url;

          const fileUrlWithHost = "https://" + mainUrl + fileUrl;

          console.log("File URL:", fileUrlWithHost);

          // Set the link url to the file
          currRec.setValue({
            fieldId: "custrecord_ng_cs_stai_link_url",
            value: fileUrlWithHost,
          });
        } else {
          const mainUrl = url.resolveDomain({
            hostType: url.HostType.APPLICATION,
          });
          const fileRecordUrl = `https://${mainUrl}/app/common/media/mediaitem.nl?id=${imageId}&e=T&o=T`;
          const fileName = search.lookupFields({
            type: "file",
            id: imageId,
            columns: ["name"],
          }).name;

          dialog
            .confirm({
              title: "⚠️ File is not available for web",
              message: `<html>
            <p>The <a target="_blank" href="${fileRecordUrl}">file</a> in not marked <b>"Available without Login"</b> for image to display in exhibitor portal. Are you sure this is the file you're trying to use?</p>
            <br>
            <p><b>${fileName}</b></p>
            </html>`,
            })
            .then((result) => {
              console.log("Result:", result);
              // Post operation. If user clicks yes
              const recordSuiteletOpsUrl = url.resolveScript({
                deploymentId: "customdeploy_ng_cses_sl_http_record_ops",
                scriptId: "customscript_ng_cses_sl_http_record_ops",
                params: {
                  type: "MARKFILEAVAILABLE",
                  file: imageId,
                },
              });

              if (result) {
                const response = https.request({
                  method: "PUT",
                  url: recordSuiteletOpsUrl,
                });

                console.log("File updated:", response);

                const mainUrl = url.resolveDomain({
                  hostType: url.HostType.APPLICATION,
                });
                const fileUrl = search.lookupFields({
                  type: "file",
                  id: imageId,
                  columns: ["url"],
                }).url;

                const fileUrlWithHost = "https://" + mainUrl + fileUrl;

                console.log("File URL:", fileUrlWithHost);

                // Set the link url to the file
                currRec.setValue({
                  fieldId: "custrecord_ng_cs_stai_link_url",
                  value: fileUrlWithHost,
                });
              } else {
                console.log("File not updated.");
              }
            });
        }
      }
    }
  }

  /**
   * Function to be executed when field is slaved.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   *
   * @since 2015.2
   */
  function postSourcing(scriptContext) {}

  /**
   * Function to be executed after sublist is inserted, removed, or edited.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function sublistChanged(scriptContext) {}

  /**
   * Function to be executed after line is selected.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function lineInit(scriptContext) {}

  /**
   * Validation function to be executed when field is changed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @returns {boolean} Return true if field is valid
   *
   * @since 2015.2
   */
  function validateField(scriptContext) {}

  /**
   * Validation function to be executed when sublist line is committed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateLine(scriptContext) {}

  /**
   * Validation function to be executed when sublist line is inserted.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateInsert(scriptContext) {}

  /**
   * Validation function to be executed when record is deleted.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateDelete(scriptContext) {}

  /**
   * Validation function to be executed when record is saved.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  function saveRecord(scriptContext) {}

  const fileMarkedAvailableWithoutLogin = (fileId) => {
    if (!fileId) return false;

    const fileLookup = query
      .runSuiteQL({
        query: `SELECT isonline FROM file WHERE id = '${fileId}'`,
      })
      .asMappedResults()[0];

    return fileLookup.isonline === "T";
  };

  return {
    pageInit: pageInit,
    fieldChanged: fieldChanged,
    postSourcing: postSourcing,
    sublistChanged: sublistChanged,
    lineInit: lineInit,
    // validateField: validateField,
    // validateLine: validateLine,
    // validateInsert: validateInsert,
    // validateDelete: validateDelete,
    // saveRecord: saveRecord,
  };
});
