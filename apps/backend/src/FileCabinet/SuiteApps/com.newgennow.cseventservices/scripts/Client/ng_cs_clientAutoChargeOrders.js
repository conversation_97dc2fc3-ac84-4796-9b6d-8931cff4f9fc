/**
 * @NApiVersion 2.x
 * @NScriptType ClientScript
 * @NModuleScope SameAccount
 */
define(["N/currentRecord", "../lib/newgen.library.v2"], function (
  currentRecord,
  NG,
) {
  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   */
  function pageInit(context) {}

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  function fieldChanged(context) {
    var currRec = context.currentRecord;

    if (context.fieldId == "custpage_show") {
      var showSel = currRec.getValue({ fieldId: "custpage_show" });
      if (showSel.length > 5) {
        window.alert(
          "The maximum selection count is 5. Please remove some of your selections.",
        );
        return;
      }

      if (currRec.getValue({ fieldId: "custpage_display_details" })) {
        currRec.setValue({
          fieldId: "custpage_display_details",
          value: false,
          ignoreFieldChange: true,
        });
      }
    }

    if (context.fieldId == "custpage_display_details") {
      var showDetails = currRec.getValue({
        fieldId: "custpage_display_details",
      });

      console.log("SHOW DETAILS", showDetails);
      if (showDetails) {
        refreshDetails();
      }
    }

    if (
      !NG.tools.isEmpty(context.sublistId) &&
      context.sublistId.search("order_list_") >= 0
    ) {
      if (context.fieldId == "selected") {
        var lineNum = !NG.tools.isEmpty(context.lineNum)
          ? context.lineNum
          : currRec.getCurrentSublistIndex({ sublistId: context.sublistId });
        if (
          currRec.getSublistValue({
            sublistId: context.sublistId,
            fieldId: "selected",
            line: lineNum,
          })
        ) {
          var lineCount = currRec.getLineCount({
            sublistId: context.sublistId,
          });
          var selectCount = 0;
          for (var l = 0; l < lineCount; l++) {
            if (
              currRec.getSublistValue({
                sublistId: context.sublistId,
                fieldId: "selected",
                line: l,
              })
            ) {
              selectCount++;
            }
          }

          if (selectCount >= 50) {
            window.alert(
              "A maximum of 50 individual orders can be selected per show for auto-charging. This line will be deselected.",
            );
            currRec.setSublistValue({
              sublistId: context.sublistId,
              fieldId: "selected",
              line: lineNum,
              value: false,
              ignoreFieldChange: true,
            });
          }
        }
      }
    }
  }

  /**
   * Function to be executed when field is slaved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   *
   * @since 2015.2
   */
  function postSourcing(context) {}

  /**
   * Function to be executed after sublist is inserted, removed, or edited.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function sublistChanged(context) {}

  /**
   * Function to be executed after line is selected.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function lineInit(context) {}

  /**
   * Validation function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @returns {boolean} Return true if field is valid
   *
   * @since 2015.2
   */
  function validateField(context) {
    return true;
  }

  /**
   * Validation function to be executed when sublist line is committed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateLine(context) {
    return true;
  }

  /**
   * Validation function to be executed when sublist line is inserted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateInsert(context) {
    return true;
  }

  /**
   * Validation function to be executed when record is deleted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateDelete(context) {
    return true;
  }

  /**
   * Validation function to be executed when record is saved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  function saveRecord(context) {
    var currRec = context.currentRecord;

    var showSel = currRec.getValue({ fieldId: "custpage_show" });
    if (showSel.length > 5) {
      window.alert(
        "The maximum selection count is 5. Please remove some of your selections.",
      );
      return false;
    }

    for (var s = 0; s < showSel.length; s++) {
      var listId = "order_list_{0}".NG_Format(showSel[s]);
      var lineCount = currRec.getLineCount({ sublistId: listId });
      var selectCount = 0;
      for (var l = 0; l < lineCount; l++) {
        if (
          currRec.getSublistValue({
            sublistId: listId,
            fieldId: "selected",
            line: l,
          })
        ) {
          selectCount++;
        }
      }

      if (selectCount >= 50) {
        var showName = NG.tools.getLookupFields(
          "customrecord_show",
          showSel[s],
          ["name"],
          [],
          [],
        ).name;
        var diff = Math.round(selectCount - 50).toFixed(0);
        window.alert(
          'A maximum of 50 individual orders can be selected per show for auto-charging. Please deselect at least {0} order(s) for show "{1}".'.NG_Format(
            diff,
            showName,
          ),
        );
      }
    }

    return true;
  }

  function refreshDetails() {
    var currRec = currentRecord.get();

    var shows = currRec.getValue({ fieldId: "custpage_show" });
    if (!NG.tools.isEmpty(shows) && shows.length > 0) {
      var ordTypes = currRec.getValue({ fieldId: "custpage_order_types" });
      if (ordTypes.length < 1) {
        ordTypes.push(csLib.settings.DefaultExhibitorOrderType);
      }
      var sp = NG.url.escapeURL(shows.join(","));
      var ot = NG.url.escapeURL(ordTypes.join(","));
      var targetURL = "{0}&shdt={1}&shsl={2}&ot={3}".NG_Format(
        currRec.getValue({ fieldId: "custpage_page_url" }),
        "T",
        sp,
        ot,
      );
      NS.form.setChanged(false);
      window.location.replace(targetURL);
    } else {
      window.alert(
        "Please select at least one event first before attempting to retrieve details.",
      );
      currRec.setValue({
        fieldId: "custpage_display_details",
        value: false,
        ignoreFieldChange: true,
      });
    }
  }
  function markAllOrders(listId) {
    console.log("⚡ Mark all contacts triggered!", listId);
    let currentRec = currentRecord.get();

    console.log("Current Record retrieved:", currentRec);

    let lineCount = currentRec.getLineCount({
      sublistId: listId,
    });

    for (var i = 0; i < lineCount; i++) {
      currentRec.selectLine({
        sublistId: listId,
        line: i,
      });

      currentRec.setCurrentSublistValue({
        sublistId: listId,
        fieldId: "selected",
        value: true,
      });

      currentRec.commitLine({
        sublistId: listId,
      });
    }
  }
  function unmarkAllOrders(listId) {
    console.log("⚡ Unmark all contacts triggered!", listId);
    let currentRec = currentRecord.get();
    let lineCount = currentRec.getLineCount({
      sublistId: listId,
    });

    for (var i = 0; i < lineCount; i++) {
      currentRec.selectLine({
        sublistId: listId,
        line: i,
      });

      currentRec.setCurrentSublistValue({
        sublistId: listId,
        fieldId: "selected",
        value: false,
      });

      currentRec.commitLine({
        sublistId: listId,
      });
    }
  }
  return {
    pageInit: pageInit,
    fieldChanged: fieldChanged,
    postSourcing: postSourcing,
    sublistChanged: sublistChanged,
    lineInit: lineInit,
    validateField: validateField,
    validateLine: validateLine,
    validateInsert: validateInsert,
    validateDelete: validateDelete,
    saveRecord: saveRecord,
    ng_refreshDetails: refreshDetails,
    markAllOrders,
    unmarkAllOrders,
  };
});

/**
 * A function to display event data.
 *
 * This function acquires the current record and sets its 'custpage_display_details' field value to true.
 * Uncommented section was responsible for checking if 'fieldId' equals 'custpage_display_details',
 * and depending on 'custpage_display_details' field result it was triggering 'refreshDetails' function.
 *
 * @function displayEventData
 * @requires "N/currentRecord"
 * @requires "N/record"
 *
 */
function displayEventData() {
  require(["N/record", "N/currentRecord"], (record, currentRecord) => {
    let currRec = currentRecord.get();

    currRec.setValue({
      fieldId: "custpage_display_details",
      value: true,
    });

    // if (context.fieldId == "custpage_display_details") {
    // 	var showDetails = currRec.getValue({ fieldId : "custpage_display_details" });
    // 	if (showDetails) {
    // 		refreshDetails();
    // 	}
    // }
  });
}
