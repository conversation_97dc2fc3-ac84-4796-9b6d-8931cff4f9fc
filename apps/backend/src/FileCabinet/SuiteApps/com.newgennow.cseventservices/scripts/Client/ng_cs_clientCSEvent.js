/**
 * @NApiVersion 2.x
 * @NScriptType ClientScript
 * @NModuleScope SameAccount
 */
define(["N/search", "../lib/newgen.library.v2", "../lib/newgen.library.cs"], /**
 * @param {search} search
 * @param {Object} NG
 * @param {Object} csLib
 */ function (search, NG, csLib) {
  var evType;
  var _BOOTH_LINE = "Booth Number __________";

  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   */
  function pageInit(context) {
    csLib.settings = csLib.trigger();
    evType = context.mode;
    var currRec = context.currentRecord;

    if (evType === "edit") {
      var name = currRec.getValue({ fieldId: "name" });
      if (NG.tools.isEmpty(name)) {
        try {
          var snum = currRec.getText({ fieldId: "custrecord_show_number" });
          if (!NG.tools.isEmpty(snum)) {
            currRec.setValue({ fieldId: "name", value: snum });
          }
        } catch (err) {
          console.log("Error encountered building name from number\n", err);
        }
      }
    }
  }

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  function fieldChanged(context) {
    var currRec = context.currentRecord;
    var facID, facData, locId;

    // >>
    if (context.fieldId === "custrecord_show_number") {
      var snum = currRec.getText({ fieldId: "custrecord_show_number" });
      if (!NG.tools.isEmpty(snum)) {
        currRec.setValue({ fieldId: "name", value: snum });
      } else {
        currRec.setValue({ fieldId: "name", value: "" });
      }
    }
    // DONE <<

    // >> Done
    if (context.fieldId === "custpage_tax_group") {
      currRec.setValue({
        fieldId: "custrecord_tax_rate",
        value: currRec.getValue({ fieldId: "custpage_tax_group" }),
        ignoreFieldChange: false,
      });
    }
    // DONE <<

    // >> Done
    if (context.fieldId === "custrecord_tax_rate") {
      var taxID = currRec.getValue({ fieldId: "custrecord_tax_rate" });
      if (!NG.tools.isEmpty(taxID)) {
        var taxRate, taxCols;
        if (csLib.settings.UseCanadianSalesTax) {
          taxCols = ["unitprice1", "unitprice2"];
        } else {
          taxCols = ["rate"];
        }
        try {
          taxRate = search.lookupFields({
            type: "taxgroup",
            id: taxID,
            columns: taxCols,
          });
        } catch (err) {
          console.log(err);
        }

        if (
          !NG.tools.isEmpty(taxRate) &&
          ((!csLib.settings.UseCanadianSalesTax &&
            !NG.tools.isEmpty(taxRate.rate)) ||
            (csLib.settings.UseCanadianSalesTax &&
              !NG.tools.isEmpty(taxRate.unitprice1)))
        ) {
          if (
            !NG.tools.isEmpty(taxRate.rate) &&
            !csLib.settings.UseCanadianSalesTax
          ) {
            currRec.setValue({
              fieldId: "custrecord_tax_percent",
              value: taxRate.rate.replace("%", ""),
            });
          } else if (
            !NG.tools.isEmpty(taxRate.unitprice1) &&
            !NG.tools.isEmpty(taxRate.unitprice2) &&
            csLib.settings.UseCanadianSalesTax
          ) {
            currRec.setValue({
              fieldId: "custrecord_ng_cs_evt_gst_pct",
              value: taxRate.unitprice1.replace("%", ""),
            });
            currRec.setValue({
              fieldId: "custrecord_ng_cs_evt_pst_pct",
              value: taxRate.unitprice2.replace("%", ""),
            });
          }
        }
      } else {
        if (csLib.settings.UseCanadianSalesTax) {
          currRec.setValue({
            fieldId: "custrecord_ng_cs_evt_gst_pct",
            value: "",
          });
          currRec.setValue({
            fieldId: "custrecord_ng_cs_evt_pst_pct",
            value: "",
          });
        } else {
          currRec.setValue({ fieldId: "custrecord_tax_percent", value: "" });
        }
      }
    }
    // DONE <<
    if (context.fieldId === "custrecord_facility") {
      // >> Done
      facID = currRec.getValue({ fieldId: "custrecord_facility" });
      if (!NG.tools.isEmpty(facID)) {
        try {
          facData = NG.tools.getLookupFields(
            "customrecord_facility",
            facID,
            [
              "custrecord_facility_address1",
              "custrecord_facility_address2",
              "custrecord_facility_city",
              "custrecord_facility_state",
              "custrecord_facility_zip",
              "custrecord_ng_cs_facility_tax_rate",
            ],
            ["custrecord_facility_state", "custrecord_ng_cs_facility_tax_rate"],
            [],
            true
          );
        } catch (err) {
          console.log("Error encountered getting facility data", err);
        }
        if (!NG.tools.isEmpty(facData)) {
          var state = !NG.tools.isEmpty(
            facData["custrecord_facility_state_text"]
          )
            ? getStateAbbrev(facData["custrecord_facility_state_text"])
            : "";
          var addy = "{0}\n{1}, {2} {3}".NG_Format(
            facData["custrecord_facility_address1"],
            facData["custrecord_facility_city"],
            state,
            facData["custrecord_facility_zip"]
          );
          currRec.setValue({
            fieldId: "custrecord_facility_address",
            value: addy,
          });
          // << Done

          UpdateFacilityAddressLabel(facData, currRec);

          currRec.setValue({
            fieldId: "custpage_tax_group",
            value: facData["custrecord_ng_cs_facility_tax_rate"],
            ignoreFieldChange: false,
          });
        }
      } else {
        currRec.setValue({
          fieldId: "custrecord_ship_to_facility_address",
          value: "",
        });
        if (!csLib.settings.UseCanadianSalesTax) {
          currRec.setValue({
            fieldId: "custpage_tax_group",
            value: "-8",
            ignoreFieldChange: false,
          });
        } else {
          currRec.setValue({
            fieldId: "custpage_tax_group",
            value: "",
            ignoreFieldChange: false,
          });
        }
      }
    }

    // >> Done
    if (context.fieldId === "custrecord_show_venue") {
      locId = currRec.getValue({ fieldId: "custrecord_show_venue" });
      if (!NG.tools.isEmpty(locId)) {
        UpdateWarehouseAddressLabel(locId, currRec);
      } else {
        currRec.setValue({
          fieldId: "custrecord_ship_to_warehouse_address",
          value: "",
        });
      }
    }
    // << Done

    // >> Done
    if (context.fieldId === "custrecord_show_customer") {
      if (
        !NG.tools.isEmpty(
          currRec.getValue({ fieldId: "custrecord_show_customer" })
        )
      ) {
        facID = currRec.getValue({ fieldId: "custrecord_facility" });
        locId = currRec.getValue({ fieldId: "custrecord_show_venue" });
        try {
          facData = search.lookupFields({
            type: "customrecord_facility",
            id: facID,
            columns: [
              "custrecord_facility_rec_contact.firstname",
              "custrecord_facility_rec_contact.lastname",
              "custrecord_facility_rec_contact.phone",
              "custrecord_facility_rec_contact.email",
              "custrecord_facility_address1",
              "custrecord_facility_address2",
              "custrecord_facility_city",
              "custrecord_facility_state",
              "custrecord_facility_zip",
            ],
          });
        } catch (err) {
          console.log("Error encountered getting facility contact info", err);
        }

        if (!NG.tools.isEmpty(facData)) {
          UpdateFacilityAddressLabel(facData, currRec);
        } else {
          currRec.setValue({
            fieldId: "custrecord_ship_to_facility_address",
            value: "",
          });
        }
        if (!NG.tools.isEmpty(locId)) {
          UpdateWarehouseAddressLabel(locId, currRec);
        } else {
          currRec.setValue({
            fieldId: "custrecord_ship_to_warehouse_address",
            value: "",
          });
        }
      } else {
        currRec.setValue({
          fieldId: "custrecord_ship_to_facility_address",
          value: "",
        });
        currRec.setValue({
          fieldId: "custrecord_ship_to_warehouse_address",
          value: "",
        });
      }
    }
    // << Done

    // >> Done
    if (context.fieldId === "custrecord_show_image") {
      var imageFileId = currRec.getValue({ fieldId: "custrecord_show_image" });
      if (!NG.tools.isEmpty(imageFileId)) {
        var fileData = NG.tools.getLookupFields(
          "file",
          imageFileId,
          ["url", "availablewithoutlogin"],
          [],
          []
        );
        if (!fileData.availablewithoutlogin) {
          window.alert("This file is not available for web use.");
          currRec.setValue({
            fieldId: "custrecord_show_image",
            value: "",
            ignoreFieldChange: true,
          });
        }
      }
    }
    //   // << Done
  }

  /**
   * Function to be executed when field is slaved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   *
   * @since 2015.2
   */
  function postSourcing(context) {}

  /**
   * Function to be executed after sublist is inserted, removed, or edited.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function sublistChanged(context) {}

  /**
   * Function to be executed after line is selected.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function lineInit(context) {}

  /**
   * Validation function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @returns {boolean} Return true if field is valid
   *
   * @since 2015.2
   */
  function validateField(context) {
    return true;
  }

  /**
   * Validation function to be executed when sublist line is committed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateLine(context) {
    return true;
  }

  /**
   * Validation function to be executed when sublist line is inserted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateInsert(context) {
    return true;
  }

  /**
   * Validation function to be executed when record is deleted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateDelete(context) {
    return true;
  }

  /**
   * Validation function to be executed when record is saved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  function saveRecord(context) {
    return true;
  }

  function UpdateFacilityAddressLabel(facData, currRec) {
    var templateData = search.lookupFields({
      type: "customrecord_ng_cs_settings",
      id: 1,
      columns: [
        "custrecord_ng_cs_fclty_addy_template",
        "custrecord_ng_cs_name_from_subsidiary",
        "custrecord_ng_cs_booth_num_line_text",
      ],
    });
    var boothLine =
      templateData.custrecord_ng_cs_booth_num_line_text || _BOOTH_LINE;
    var showName = currRec.getValue({ fieldId: "name" });
    var compName = GetCompanyName(
      templateData.custrecord_ng_cs_name_from_subsidiary,
      currRec
    );
    var custName = currRec.getValue({ fieldId: "custrecord_show_customer" });
    var stateAbbrev =
      !NG.tools.isEmpty(facData["custrecord_facility_state"]) &&
      facData["custrecord_facility_state"].length > 0
        ? getStateAbbrev(facData["custrecord_facility_state"][0].text)
        : "";

    var addressLine = "{0}{1}".NG_Format(
      facData["custrecord_facility_address1"],
      !NG.tools.isEmpty(facData["custrecord_facility_address2"])
        ? "\n{0}".NG_Format(facData["custrecord_facility_address2"])
        : ""
    );
    var addressMerge =
      templateData.custrecord_ng_cs_fclty_addy_template.NG_Format(
        showName,
        custName,
        boothLine,
        compName,
        addressLine,
        facData["custrecord_facility_city"],
        stateAbbrev,
        facData["custrecord_facility_zip"]
      );
    currRec.setValue({
      fieldId: "custrecord_ship_to_facility_address",
      value: addressMerge,
    });
  }

  function UpdateWarehouseAddressLabel(locId, currRec) {
    console.log(currRec);
    var lData = GetLocationData();
    if (!NG.tools.isEmpty(lData[locId])) {
      var templateData = search.lookupFields({
        type: "customrecord_ng_cs_settings",
        id: 1,
        columns: [
          "custrecord_ng_cs_wrhs_addy_template",
          "custrecord_ng_cs_name_from_subsidiary",
          "custrecord_ng_cs_booth_num_line_text",
        ],
      });
      var boothLine =
        templateData.custrecord_ng_cs_booth_num_line_text || _BOOTH_LINE;
      var showName = currRec.getValue({ fieldId: "name" });
      var compName = GetCompanyName(
        templateData.custrecord_ng_cs_name_from_subsidiary,
        currRec
      );
      var custName = currRec.getText({ fieldId: "custrecord_show_customer" });
      var addressLine = "{0}{1}".NG_Format(
        lData[locId]["addr1"],
        !NG.tools.isEmpty(lData[locId]["addr2"])
          ? "\n{0}".NG_Format(lData[locId]["addr2"])
          : ""
      );
      var addressMerge =
        templateData.custrecord_ng_cs_wrhs_addy_template.NG_Format(
          showName,
          custName,
          boothLine,
          compName,
          addressLine,
          lData[locId]["city"],
          lData[locId]["state"],
          lData[locId]["zip"]
        );
      currRec.setValue({
        fieldId: "custrecord_ship_to_warehouse_address",
        value: addressMerge,
      });
    } else {
      currRec.setValue({
        fieldId: "custrecord_ship_to_warehouse_address",
        value: "",
      });
    }
  }

  function getStateAbbrev(state) {
    var stateList = NG.lib.states.US;
    var stateAbbrev = "";
    for (var s = 0; s < stateList.length; s++) {
      if (stateList[s]["text"] === state) {
        stateAbbrev = stateList[s]["value"];
      }
    }

    return stateAbbrev;
  }

  function GetCompanyName(useSubsidiaryName, currRec) {
    var name = "";
    if (NG.NSFeatures.SUBSIDIARIES && useSubsidiaryName) {
      var sData = JSON.parse(
        currRec.getValue({ fieldId: "custpage_subsidiary_info" })
      );
      var subId = currRec.getValue({ fieldId: "custrecord_show_subsidiary" });
      if (!NG.tools.isEmpty(subId)) {
        if (!NG.tools.isEmpty(sData[subId])) {
          name = sData[subId]["name"];
        }
      }
    } else {
      var compData = JSON.parse(
        currRec.getValue({ fieldId: "custpage_company_info" })
      );
      name = compData["cname"];
    }

    return name;
  }

  return {
    pageInit: pageInit,
    fieldChanged: fieldChanged,
    postSourcing: postSourcing,
    sublistChanged: sublistChanged,
    lineInit: lineInit,
    validateField: validateField,
    validateLine: validateLine,
    validateInsert: validateInsert,
    validateDelete: validateDelete,
    saveRecord: saveRecord,
  };
});
