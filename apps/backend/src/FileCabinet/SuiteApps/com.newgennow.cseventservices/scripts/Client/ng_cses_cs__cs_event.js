/**
 * @Author: <PERSON>
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NModuleScope SameAccount
 * @NAmdConfig ./amdClientConfig.json
 */
define([
  "N/query",
  "N/runtime",
  "N/record",
  "N/https",
  "N/search",
  "N/url",
  "N/ui/dialog",
  "N/ui/message",
  "settings",
  "enums",
], /**
 * @param {query} query
 * @param {runtime} runtime
 * @param {record} record
 * @param {https} https
 * @param {search} search
 * @param {url} url
 * @param {dialog} dialog
 * @param {message} message
 * @param {Object} settings
 * @param {() => Settings} settings.useSettings
 * @param {ES_Enums} enums
 */ function (
  query,
  runtime,
  record,
  https,
  search,
  url,
  dialog,
  message,
  settings,
  enums,
) {
  let RECORD_MODE = "";
  let CS_SETTINGS = null;
  let COMPANY_COUNTRY = "";
  let COMPANY_INFO = "";
  let SUBSIDIARY_INFO = "";
  let TAX_GROUP_INACTIVE_MSG = null;
  const FIELDS_FOR_WAREHOUSE_ADDRESS_UPDATE = [
    "name",
    "custrecord_show_venue",
    "custrecord_show_customer",
  ];
  const FIELDS_FOR_FACILITY_ADDRESS_UPDATE = [
    "name",
    "custrecord_show_customer",
    "custrecord_show_subsidiary",
    "custrecord_facility",
  ];
  const FIELDS_FOR_ADDRESSES_UPDATE = [
    ...new Set([
      ...FIELDS_FOR_WAREHOUSE_ADDRESS_UPDATE,
      ...FIELDS_FOR_FACILITY_ADDRESS_UPDATE,
    ]),
  ];

  const BOOKING_NAME_FIELDS = enums.venueOpsMappings.BOOKING_NAME_FIELDS;
  const BK_NAME_KEYS = Object.keys(BOOKING_NAME_FIELDS);

  const FUNCTION_NAME_FIELDS = enums.venueOpsMappings.FUNCTION_NAME_FIELDS;
  const FN_NAME_KEYS = Object.keys(FUNCTION_NAME_FIELDS);

  const BOOKING_FUNCTION_MAPPING =
    enums.venueOpsMappings.BOOKING_FUNCTION_MAPPING;
  const BK_FN_KEYS = Object.keys(BOOKING_FUNCTION_MAPPING);

  // values to save post sourcing
  const POST_SOURCING = {
    paths: {
      custrecord_nges_fun_venue: "custrecord_nges_fun_venue_space",
    },
    values: {
      custrecord_nges_fun_venue_space: null,
    },
  };

  const PATH_KEYS = Object.keys(POST_SOURCING.paths);
  const VALUE_KEYS = Object.keys(POST_SOURCING.values);

  // holds the line update of a booking to copy over to linked functions
  const BOOKINGS_UPDATES = {
    id: null,
    values: {},
  };

  // to prevent a stack overflow we can toggle this variable when we
  // are in the middle of autofilling a sublist row with several values
  let AUTO_FILLING = false;
  let UPDATE_BOOKING_NAME = false;
  let UPDATE_FUNCTION_NAME = false;

  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   */
  function pageInit(scriptContext) {
    const { currentRecord, mode } = scriptContext;
    RECORD_MODE = mode;
    CS_SETTINGS = settings.useSettings();
    const companyInfo = currentRecord.getValue({
      fieldId: "custpage_company_info",
    });
    const subsidiaryInfo = currentRecord.getValue({
      fieldId: "custpage_subsidiary_info",
    });

    COMPANY_INFO = companyInfo && JSON.parse(companyInfo);
    SUBSIDIARY_INFO = subsidiaryInfo && JSON.parse(subsidiaryInfo);
    console.log(`MODE: %c${RECORD_MODE}`, "color: green; font-weight: bold;");

    switch (RECORD_MODE) {
      case "edit":
        setDefaultCwtMinimum(scriptContext, CS_SETTINGS);
        break;
      default:
        console.log("No pageInit logic for this mode.", RECORD_MODE);
    }
  }

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  function fieldChanged(scriptContext) {
    const { currentRecord, fieldId, sublistId } = scriptContext;

    handleTaxFieldsChange(scriptContext);
    handleFacilityChange(scriptContext);
    handleTaxGroupChange(scriptContext);

    // VENUE OPERATIONS
    if (CS_SETTINGS.custrecord_ng_cs_enable_venue_operations) {
      handleEventStatusSync(scriptContext);
      handleSpaceReadOnlyField(scriptContext);
      defaultDatesAndTimesFromEvent(scriptContext);
    }

    if (FIELDS_FOR_ADDRESSES_UPDATE.includes(fieldId)) {
      if (FIELDS_FOR_WAREHOUSE_ADDRESS_UPDATE.includes(fieldId)) {
        updateWarehouseAddressLabel(scriptContext);
      }

      if (FIELDS_FOR_FACILITY_ADDRESS_UPDATE.includes(fieldId)) {
        updateFacilityAddressLabel(scriptContext);
      }
    }

    if (fieldId === "custrecord_show_image") {
      const imageId = currentRecord.getValue({
        fieldId: "custrecord_show_image",
      });

      if (imageId) {
        console.log("Image ID:", imageId);

        const isOnline = fileMarkedAvailableWithoutLogin(imageId);

        isOnline
          ? console.log('✅ File is marked as "Available without Login"')
          : console.warn('❌ File is NOT marked as "Available without Login"');

        if (isOnline) {
          const mainUrl = url.resolveDomain({
            hostType: url.HostType.APPLICATION,
          });
          const fileUrl = search.lookupFields({
            type: "file",
            id: imageId,
            columns: ["url"],
          }).url;

          const fileUrlWithHost = "https://" + mainUrl + fileUrl;

          console.log("File URL:", fileUrlWithHost);
        } else {
          const mainUrl = url.resolveDomain({
            hostType: url.HostType.APPLICATION,
          });
          const fileRecordUrl = `https://${mainUrl}/app/common/media/mediaitem.nl?id=${imageId}&e=T&o=T`;
          const fileName = search.lookupFields({
            type: "file",
            id: imageId,
            columns: ["name"],
          }).name;

          dialog
            .confirm({
              title: "⚠️ File is not available for web",
              message: `<html>
            <p>The <a target="_blank" href="${fileRecordUrl}">file</a> in not marked <b>"Available without Login"</b> for image to display in exhibitor portal. Are you sure this is the file you're trying to use?</p>
            <br>
            <p><b>${fileName}</b></p>
            </html>`,
            })
            .then((result) => {
              console.log("Result:", result);
              // Post operation. If user clicks yes
              const recordSuiteletOpsUrl = url.resolveScript({
                deploymentId: "customdeploy_ng_cses_sl_http_record_ops",
                scriptId: "customscript_ng_cses_sl_http_record_ops",
                params: {
                  type: "MARKFILEAVAILABLE",
                  file: imageId,
                },
              });

              if (result) {
                const response = https.request({
                  method: "PUT",
                  url: recordSuiteletOpsUrl,
                });

                console.log("File updated:", response);
              } else {
                console.log("File not updated.");
              }
            });
        }
      }
    }

    // Set a flag to update booking name on next sublist change
    if (BK_NAME_KEYS.includes(fieldId)) {
      UPDATE_BOOKING_NAME = true;
    }

    // Set a flag to update booking name on next sublist change
    if (FN_NAME_KEYS.includes(fieldId)) {
      UPDATE_FUNCTION_NAME = true;
    }

    if (BK_FN_KEYS.includes(fieldId)) {
      BOOKINGS_UPDATES.id = currentRecord.getCurrentSublistValue({
        sublistId,
        fieldId: "id",
      });

      BOOKINGS_UPDATES.values[fieldId] = currentRecord.getCurrentSublistValue({
        sublistId,
        fieldId: fieldId,
      });
    }

    if (!AUTO_FILLING) {
      autoFillFunction(scriptContext);
    }
  }

  /**
   * Function to be executed when field is slaved.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   *
   * @since 2015.2
   */
  function postSourcing(scriptContext) {
    if (CS_SETTINGS.custrecord_ng_cs_enable_venue_operations) {
      const { fieldId } = scriptContext;

      if (PATH_KEYS.includes(fieldId)) {
        const updateFieldId = POST_SOURCING.paths[fieldId];
        const value = POST_SOURCING.values[updateFieldId];
        if (value) {
          const { currentRecord } = scriptContext;
          currentRecord.setValue({
            fieldId: updateFieldId,
            value,
          });
        }
      }
    }
  }

  /**
   * Function to be executed after sublist is inserted, removed, or edited.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function sublistChanged(scriptContext) {
    // VENUE OPERATIONS
    if (CS_SETTINGS.custrecord_ng_cs_enable_venue_operations) {
      if (AUTO_FILLING) {
        return;
      }

      updateFunctions(scriptContext);
    }
  }

  /**
   * Function to be executed after line is selected.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function lineInit(scriptContext) {}

  /**
   * Validation function to be executed when field is changed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @returns {boolean} Return true if field is valid
   *
   * @since 2015.2
   */
  function validateField(scriptContext) {}

  /**
   * Validation function to be executed when sublist line is committed.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateLine(scriptContext) {
    if (CS_SETTINGS.custrecord_ng_cs_enable_venue_operations) {
      if (UPDATE_BOOKING_NAME) {
        populateBookingName(scriptContext);
        UPDATE_BOOKING_NAME = false;
      }

      if (UPDATE_FUNCTION_NAME) {
        populateFunctionName(scriptContext);
        UPDATE_FUNCTION_NAME = false;
      }

      const bookingsDatesResult = checkBookingsDates(scriptContext);
      const functionsDatesResult = checkFunctionsDates(scriptContext);

      return bookingsDatesResult && functionsDatesResult && true;
    }

    return true;
  }

  /**
   * Validation function to be executed when sublist line is inserted.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateInsert(scriptContext) {}

  /**
   * Validation function to be executed when record is deleted.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateDelete(scriptContext) {}

  /**
   * Validation function to be executed when record is saved.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  function saveRecord(scriptContext) {}

  async function handleFacilityChange(scriptContext) {
    const { currentRecord, fieldId } = scriptContext;
    const venueId = currentRecord.getValue({ fieldId: "custrecord_facility" });
    const taxPercentField = currentRecord.getField({
      fieldId: "custrecord_tax_percent",
    });
    const taxGstField = currentRecord.getField({
      fieldId: "custrecord_ng_cs_evt_gst_pct",
    });
    const taxPstField = currentRecord.getField({
      fieldId: "custrecord_ng_cs_evt_pst_pct",
    });

    if (fieldId === "custrecord_facility") {
      if (venueId) {
        await record.load
          .promise({
            type: "customrecord_facility",
            id: venueId,
          })
          .then((venueRecord) => {
            console.log("✅ Venue record loaded.", venueRecord);
            const country =
              venueRecord.getValue({
                fieldId: "custrecord_facility_country",
              }) || "230";

            const fullAddress = venueRecord.getValue({
              fieldId: "custrecord_facility_fulladdress",
            });
            const taxGroup = venueRecord.getValue({
              fieldId: "custrecord_ng_cs_facility_tax_rate",
            });
            const taxGstPercent = venueRecord.getValue({
              fieldId: "custrecord_ng_cs_facility_gst_pct",
            });
            const taxPstPercent = venueRecord.getValue({
              fieldId: "custrecord_ng_cs_facility_pst_pct",
            });
            const taxPercent = venueRecord.getValue({
              fieldId: "custrecord_ng_cs_facility_tax_percent",
            });

            currentRecord.setValue({
              fieldId: "custrecord_facility_address",
              value: fullAddress,
            });
            // Update facility address label
            updateFacilityAddressLabel(scriptContext);

            // Update tax group
            const countryIsUs = country === "230";
            if (countryIsUs) {
              taxPercentField.isDisplay = true;
              taxPstField.isDisplay = true;
              taxPstField.displayType = "INLINE";
              taxGstField.isDisplay = false;
              taxPstField.isDisplay = false;
            } else {
              taxPercentField.isDisplay = false;
              taxGstField.displayType = "INLINE";
              taxPstField.displayType = "INLINE";
              taxGstField.isDisplay = true;
              taxPstField.isDisplay = true;
            }

            if (taxGroup) {
              if (countryIsUs) {
                currentRecord.setValue({
                  fieldId: "custpage_tax_group",
                  value: taxGroup,
                });
                currentRecord.setValue({
                  fieldId: "custrecord_tax_rate",
                  value: taxGroup,
                });
                currentRecord.setValue({
                  fieldId: "custrecord_tax_percent",
                  value: taxPercent || "0.00",
                });
                currentRecord.setValue({
                  fieldId: "custrecord_ng_tax_percent_ro",
                  value: taxPercent || "0.00",
                });
              } else {
                currentRecord.setValue({
                  fieldId: "custpage_tax_group",
                  value: taxGroup,
                });
                currentRecord.setValue({
                  fieldId: "custrecord_tax_rate",
                  value: taxGroup,
                });
                currentRecord.setValue({
                  fieldId: "custrecord_ng_cs_evt_gst_pct",
                  value: taxGstPercent || "0.00",
                });
                currentRecord.setValue({
                  fieldId: "custrecord_ng_cs_evt_gst_pct_ro",
                  value: taxGstPercent || "0.00",
                });
                currentRecord.setValue({
                  fieldId: "custrecord_ng_cs_evt_pst_pct",
                  value: taxPstPercent || "0.00",
                });
                currentRecord.setValue({
                  fieldId: "custrecord_ng_cs_evt_pst_pct_ro",
                  value: taxPstPercent || "0.00",
                });
              }
            }
          })
          .catch((error) => {
            console.error("❌ Error loading venue record.", error);
          });
      } else {
        console.log("⚠️ No venue selected.");
        if (COMPANY_COUNTRY === "US") {
          currentRecord.setValue({
            fieldId: "custpage_tax_group",
            value: "-8",
          });
          currentRecord.setValue({
            fieldId: "custrecord_tax_rate",
            value: "-8",
          });
        } else {
          currentRecord.setValue({ fieldId: "custpage_tax_group", value: "" });
        }
      }
    }
  }

  function updateWarehouseAddressLabel(scriptContext) {
    const { currentRecord } = scriptContext;

    const locationId = currentRecord.getValue({
      fieldId: "custrecord_show_venue",
    });

    if (!locationId) {
      currentRecord.setValue({
        fieldId: "custrecord_ship_to_warehouse_address",
        value: "",
      });
      return;
    }

    try {
      const locationData = csGetLocationData();
      const boothLineLabelTemplate =
        CS_SETTINGS.custrecord_ng_cs_booth_num_line_text ||
        `Booth Number __________`;
      const eventName = currentRecord.getValue({ fieldId: "name" });
      const companyName = COMPANY_INFO.companyName || "";
      const customerName =
        currentRecord.getText({
          fieldId: "custrecord_show_customer",
        }) || "";
      const addressOne = locationData[locationId].addr1;
      const addressTwo = locationData[locationId].addr2;
      const addressLine = addressOne
        ? `${addressOne}${addressTwo ? `\n${addressTwo}` : ""}`
        : "";
      const city = locationData[locationId]?.city || "";
      const state = locationData[locationId]?.state || "";
      const zip = locationData[locationId]?.zip || "";

      const addressMerge =
        CS_SETTINGS.custrecord_ng_cs_wrhs_addy_template.formatTemplating(
          eventName,
          customerName,
          boothLineLabelTemplate,
          companyName,
          addressLine,
          city,
          state,
          zip,
        );

      currentRecord.setValue({
        fieldId: "custrecord_ship_to_warehouse_address",
        value: addressMerge,
      });
    } catch (error) {
      console.error("❌ Error updating warehouse address label.", error);
    }
  }

  function updateFacilityAddressLabel(scriptContext) {
    const { currentRecord } = scriptContext;

    const venueId = currentRecord.getValue({
      fieldId: "custrecord_facility",
    });

    if (!venueId) {
      currentRecord.setValue({
        fieldId: "custrecord_ship_to_facility_address",
        value: "",
      });
      return;
    }

    const venueRecord = record.load({
      type: "customrecord_facility",
      id: venueId,
    });

    const fullAddress = venueRecord.getValue({
      fieldId: "custrecord_facility_fulladdress",
    });

    currentRecord.setValue({
      fieldId: "custrecord_facility_address",
      value: fullAddress,
    });

    // Update facility address label
    const boothLineLabelTemplate =
      CS_SETTINGS.custrecord_ng_cs_booth_num_line_text ||
      `Booth Number __________`;
    const eventName = currentRecord.getValue({ fieldId: "name" });

    // determine company name based off of if subsidiaries is in use
    let companyName = "";
    const usingSubsidiaries = runtime.isFeatureInEffect({
      feature: "SUBSIDIARIES",
    });
    if (usingSubsidiaries) {
      const subId = currentRecord.getValue({
        fieldId: "custrecord_show_subsidiary",
      });
      companyName = SUBSIDIARY_INFO[subId]["name"];
    } else {
      companyName = COMPANY_INFO.companyName;
    }

    const customerName =
      currentRecord.getText({
        fieldId: "custrecord_show_customer",
      }) || "";
    const addressOne = venueRecord.getValue({
      fieldId: "custrecord_facility_address1",
    });
    const addressTwo = venueRecord.getValue({
      fieldId: "custrecord_facility_address2",
    });
    const addressLine = addressOne
      ? `${addressOne}${addressTwo ? `\n${addressTwo}` : ""}`
      : "";
    const city =
      venueRecord.getValue({
        fieldId: "custrecord_facility_city",
      }) || "";
    const stateFullName = venueRecord.getValue({
      fieldId: "custrecord_facility_state",
    });
    const state = stateFullName ? getStateShortName(stateFullName) : "";
    const zip =
      venueRecord.getValue({
        fieldId: "custrecord_facility_zip",
      }) || "";

    const addressMerge =
      CS_SETTINGS.custrecord_ng_cs_fclty_addy_template.formatTemplating(
        eventName,
        customerName,
        boothLineLabelTemplate,
        companyName,
        addressLine,
        city,
        state,
        zip,
      );

    currentRecord.setValue({
      fieldId: "custrecord_ship_to_facility_address",
      value: addressMerge,
    });
  }

  async function handleTaxFieldsChange(scriptContext) {
    const { currentRecord, fieldId } = scriptContext;
    console.log("⚡ Running tax fields change handler...");

    if (fieldId === "custpage_tax_group") {
      const taxGroup = currentRecord.getValue({
        fieldId: "custpage_tax_group",
      });
      currentRecord.setValue({ fieldId: "custpage_tax_rate", value: taxGroup });
    }
  }

  async function handleTaxGroupChange(scriptContext) {
    const { currentRecord, fieldId } = scriptContext;

    if (fieldId === "custpage_tax_group") {
      const taxId = currentRecord.getValue({ fieldId: "custpage_tax_group" });
      let country;

      notifyUserOfInactiveTaxGroupOnEvent(currentRecord);

      const countrySearch = search.lookupFields({
        type: search.Type.TAX_GROUP,
        id: taxId,
        columns: ["country"],
      }).country;

      if (countrySearch.length !== 0) {
        country = countrySearch[0].value;
      }

      const countryIsUs = country === "US";

      const taxObject = getTaxData(taxId, countryIsUs);

      const eventTaxField = currentRecord.getField({
        fieldId: "custpage_tax_group",
      });
      const eventPctField = currentRecord.getField({
        fieldId: "custrecord_tax_percent",
      });
      const eventPctFieldRo = currentRecord.getField({
        fieldId: "custrecord_ng_tax_percent_ro",
      });
      const eventGstPctField = currentRecord.getField({
        fieldId: "custrecord_ng_cs_evt_gst_pct",
      });
      const eventGstPctFieldRo = currentRecord.getField({
        fieldId: "custrecord_ng_cs_evt_gst_pct_ro",
      });
      const eventPstPctField = currentRecord.getField({
        fieldId: "custrecord_ng_cs_evt_pst_pct",
      });
      const eventPstPctFieldRo = currentRecord.getField({
        fieldId: "custrecord_ng_cs_evt_pst_pct_ro",
      });

      if (countryIsUs) {
        eventTaxField.isDisplay = true;
        eventPctField.isDisplay = false;
        eventPctFieldRo.isDisplay = true;
        eventPctFieldRo.displayType = "INLINE";
        eventGstPctField.isDisplay = false;
        eventGstPctFieldRo.isDisplay = false;
        eventPstPctField.isDisplay = false;
        eventPstPctFieldRo.isDisplay = false;
      } else {
        eventPctField.isDisplay = false;
        eventPctFieldRo.isDisplay = false;
        eventGstPctFieldRo.displayType = "INLINE";
        eventPstPctFieldRo.displayType = "INLINE";
        eventGstPctField.isDisplay = false;
        eventGstPctFieldRo.isDisplay = true;
        eventPstPctField.isDisplay = false;
        eventPstPctFieldRo.isDisplay = true;
      }

      if (countryIsUs) {
        currentRecord.setValue({
          fieldId: "custrecord_tax_rate",
          value: taxId,
        });
        currentRecord.setValue({
          fieldId: "custrecord_tax_percent",
          value: taxObject?.rate.replace("%", "") || "0.00",
        });
        currentRecord.setValue({
          fieldId: "custrecord_ng_tax_percent_ro",
          value: taxObject?.rate.replace("%", "") || "0.00",
        });
      } else {
        currentRecord.setValue({
          fieldId: "custrecord_tax_rate",
          value: taxId,
        });
        currentRecord.setValue({
          fieldId: "custrecord_ng_cs_evt_gst_pct",
          value: taxObject?.unitprice1.replace("%", "") || "0.00",
        });
        currentRecord.setValue({
          fieldId: "custrecord_ng_cs_evt_gst_pct_ro",
          value: taxObject?.unitprice1.replace("%", "") || "0.00",
        });
        currentRecord.setValue({
          fieldId: "custrecord_ng_cs_evt_pst_pct",
          value: taxObject?.unitprice2.replace("%", "") || "0.00",
        });
        currentRecord.setValue({
          fieldId: "custrecord_ng_cs_evt_pst_pct_ro",
          value: taxObject?.unitprice2.replace("%", "") || "0.00",
        });
      }
    }
  }

  const notifyUserOfInactiveTaxGroupOnEvent = (eventRecord) => {
    if (eventRecord) {
      // Set taxitem on sales order after event has been changed
      const taxGroup = eventRecord.getValue("custpage_tax_group");
      const taxGroupText = eventRecord.getText("custpage_tax_group");

      // Check if tax group is inactive
      const taxGroupInactive =
        taxGroup &&
        search.lookupFields({
          type: "taxgroup",
          id: taxGroup,
          columns: ["isinactive"],
        }).isinactive;

      if (taxGroupInactive) {
        if (TAX_GROUP_INACTIVE_MSG) TAX_GROUP_INACTIVE_MSG.hide();

        TAX_GROUP_INACTIVE_MSG = message.create({
          title: "Event Tax Group Inactive",
          message: `<html>
                <h3>All future transactions including web orders will fail</h3>
                 <p>The tax group <a href="/app/common/item/taxgroup.nl?id=${taxGroup}" target="_blank">${taxGroupText}</a> selected is inactive. Please set a different tax group on the event or re-activate tax group in order for transactions to process.</p
            </html>`,
          type: message.Type.WARNING,
        });

        TAX_GROUP_INACTIVE_MSG.show();
      } else {
        TAX_GROUP_INACTIVE_MSG && TAX_GROUP_INACTIVE_MSG.hide();
      }
    }
  };

  const getTaxData = (taxGroup, countryIsUs) => {
    const taxData = {};

    if (taxGroup) {
      if (countryIsUs) {
        const taxGroupSearch = search.lookupFields({
          type: "taxgroup",
          id: taxGroup,
          columns: ["rate"],
        }).rate;

        if (taxGroupSearch) {
          taxData.rate = taxGroupSearch;
        }
      } else {
        const taxGroupSearch = search.lookupFields({
          type: "taxgroup",
          id: taxGroup,
          columns: ["unitprice1", "unitprice2"],
        });

        if (taxGroupSearch) {
          taxData.unitprice1 = taxGroupSearch.unitprice1;
          taxData.unitprice2 = taxGroupSearch.unitprice2;
        }
      }
    }

    console.log("Tax Data Obj", taxData);
    return taxData;
  };

  const setDefaultCwtMinimum = (sc, settings) => {
    const currRec = sc.currentRecord;

    const currCwt = currRec.getValue({
      fieldId: "custrecord_ng_cs_cwt_minimum",
    });

    if (!currCwt) {
      const csSettingsFreightMin = settings.custrecord_ng_cs_freight_minimum;

      if (csSettingsFreightMin) {
        currRec.setValue({
          fieldId: "custrecord_ng_cs_cwt_minimum",
          value: Number(csSettingsFreightMin),
        });
      }
    }
  };

  const fileMarkedAvailableWithoutLogin = (fileId) => {
    if (!fileId) return false;

    const fileLookup = query
      .runSuiteQL({
        query: `SELECT isonline FROM file WHERE id = '${fileId}'`,
      })
      .asMappedResults()[0];

    return fileLookup.isonline === "T";
  };

  const getStateShortName = (stateId) =>
    query
      .runSuiteQL(`SELECT shortname FROM state WHERE id = '${stateId}'`)
      .asMappedResults()[0].shortname;

  /**
   * When cs enable venue operations is enabled this function updates each
   * booking's status for the event to match the updated event status mapping
   * @param scriptContext
   */
  const handleEventStatusSync = (scriptContext) => {
    const { currentRecord, fieldId } = scriptContext;

    // TODO: Move this portion out to saveRecord so that we don't prompt the user mid edit
    if (fieldId === "custrecord_show_status") {
      const bookingsCount = currentRecord.getLineCount({
        sublistId: "recmachcustrecord_ng_cs_eb_event",
      });

      if (bookingsCount === 0) {
        return;
      }

      const result = confirm(
        "Do you want to automatically update the booking statuses?\n\n" +
          "This action will map the event status to all related bookings for this event.",
      );

      if (!result) {
        return;
      }

      const statusId = currentRecord.getValue({
        fieldId: "custrecord_show_status",
      });

      // load the cs event status record to find the booking status it maps to
      const eventStatus = record.load({
        type: "customrecord_cs_event_status",
        id: statusId,
      });

      const status = eventStatus.getValue({
        fieldId: "custrecord_ng_cs_event_booking_status",
      });

      for (let i = 0; i < bookingsCount; i++) {
        currentRecord.selectLine({
          sublistId: "recmachcustrecord_ng_cs_eb_event",
          line: i,
        });

        currentRecord.setCurrentSublistValue({
          sublistId: "recmachcustrecord_ng_cs_eb_event",
          fieldId: "custrecord_ng_cs_eb_status",
          value: status,
        });

        currentRecord.commitLine({
          sublistId: "recmachcustrecord_ng_cs_eb_event",
        });
      }
    }

    if (fieldId === "custpage_booking_status_bulk_update") {
      const bookingsCount = currentRecord.getLineCount({
        sublistId: "recmachcustrecord_ng_cs_eb_event",
      });

      if (bookingsCount === 0) {
        return;
      }

      const result = confirm(
        "Do you want to bulk update the booking statuses?\n\n" +
          "This action will override all bookings statuses for this event.",
      );

      if (!result) {
        return;
      }

      const status = currentRecord.getValue({
        fieldId: "custpage_booking_status_bulk_update",
      });

      for (let i = 0; i < bookingsCount; i++) {
        currentRecord.selectLine({
          sublistId: "recmachcustrecord_ng_cs_eb_event",
          line: i,
        });

        currentRecord.setCurrentSublistValue({
          sublistId: "recmachcustrecord_ng_cs_eb_event",
          fieldId: "custrecord_ng_cs_eb_status",
          value: status,
        });

        currentRecord.commitLine({
          sublistId: "recmachcustrecord_ng_cs_eb_event",
        });
      }
    }
  };

  /**
   * Returns the placeholder value if the value is considered blank.
   * Blank includes undefined, null, '', and ' '.
   * @param {string} value - Value to check
   * @param {string} placeholder - Placeholder if value is blank
   * @returns {string} - Returns the value if present or placeholder if blank
   */
  const valueOr = (value, placeholder) => {
    const blank = [undefined, null, "", " "];
    return blank.includes(value) ? placeholder : value;
  };

  /**
   * Auto populates the function name based off of function selections
   * @param scriptContext
   */
  const populateFunctionName = (scriptContext) => {
    const { currentRecord, sublistId } = scriptContext;

    const [space, title, startDate, startTime, endDate, endTime] =
      Object.entries(FUNCTION_NAME_FIELDS).map(([fieldId, defaultValue]) =>
        valueOr(
          currentRecord.getCurrentSublistText({
            sublistId,
            fieldId,
          }),
          defaultValue,
        ),
      );

    currentRecord.setCurrentSublistValue({
      sublistId,
      fieldId: "name",
      value: `${space} : ${title} : ${startDate} ${startTime} - ${endDate} ${endTime}`,
    });
  };

  /**
   * Auto populates the booking name based off of booking selections
   * @param scriptContext
   */
  const populateBookingName = (scriptContext) => {
    const { currentRecord, sublistId } = scriptContext;

    const [space, status, startDate, startTime, endDate, endTime, event] =
      Object.entries(BOOKING_NAME_FIELDS).map(([fieldId, defaultValue]) =>
        valueOr(
          currentRecord.getCurrentSublistText({
            sublistId,
            fieldId,
          }),
          defaultValue,
        ),
      );

    currentRecord.setCurrentSublistValue({
      sublistId,
      fieldId: "name",
      value: `${space} : ${status} : ${startDate} ${startTime} - ${endDate} ${endTime} : ${event}`,
    });
  };

  /**
   * Auto fills the function line with booking fields when link to booking is checked
   * @param scriptContext
   */
  const autoFillFunction = (scriptContext) => {
    const { fieldId } = scriptContext;

    if (
      fieldId === "custrecord_nges_fun_booking" ||
      fieldId === "custrecord_ng_cs_link_to_booking"
    ) {
      const { currentRecord, sublistId } = scriptContext;

      const linkToBookings = currentRecord.getCurrentSublistValue({
        sublistId,
        fieldId: "custrecord_ng_cs_link_to_booking",
      });

      if (fieldId === "custrecord_ng_cs_link_to_booking" && !linkToBookings) {
        return;
      }

      const bookingIdSelected = currentRecord.getCurrentSublistValue({
        sublistId,
        fieldId: "custrecord_nges_fun_booking",
      });

      const bookingLines = currentRecord.getLineCount({
        sublistId: "recmachcustrecord_ng_cs_eb_event",
      });

      const booking = {};
      for (let i = 0; i < bookingLines; i++) {
        const bookingId = currentRecord.getSublistValue({
          sublistId: "recmachcustrecord_ng_cs_eb_event",
          fieldId: "id",
          line: i,
        });

        if (bookingId === bookingIdSelected) {
          Object.keys(BOOKING_FUNCTION_MAPPING).forEach((fieldId) => {
            booking[fieldId] = currentRecord.getSublistValue({
              sublistId: "recmachcustrecord_ng_cs_eb_event",
              fieldId: fieldId,
              line: i,
            });
          });
        }
      }

      AUTO_FILLING = true;
      Object.entries(BOOKING_FUNCTION_MAPPING).forEach(
        ([bookingFieldId, functionFieldId]) => {
          const bookingFieldValue = booking[bookingFieldId];
          if (!bookingFieldValue) {
            return;
          }

          if (VALUE_KEYS.includes(functionFieldId)) {
            POST_SOURCING.values[functionFieldId] = bookingFieldValue;
          } else {
            currentRecord.setCurrentSublistValue({
              sublistId,
              fieldId: functionFieldId,
              value: bookingFieldValue,
            });
          }
        },
      );
      AUTO_FILLING = false;
    }
  };

  /**
   * Auto fills the function line with booking fields when link to booking is checked
   * @param scriptContext
   */
  const updateFunctions = (scriptContext) => {
    if (!BOOKINGS_UPDATES.id) {
      return;
    }

    const { currentRecord } = scriptContext;

    const functionLines = currentRecord.getLineCount({
      sublistId: "recmachcustrecord_nges_fun_event",
    });

    for (let i = 0; i < functionLines; i++) {
      const bookingId = currentRecord.getSublistValue({
        sublistId: "recmachcustrecord_nges_fun_event",
        fieldId: "custrecord_nges_fun_booking",
        line: i,
      });

      if (bookingId === BOOKINGS_UPDATES.id) {
        AUTO_FILLING = true;
        currentRecord.selectLine({
          sublistId: "recmachcustrecord_nges_fun_event",
          line: i,
        });
        Object.entries(BOOKINGS_UPDATES.values).forEach(
          ([bookingFieldId, bookingFieldValue]) => {
            const functionFieldId = BOOKING_FUNCTION_MAPPING[bookingFieldId];

            if (VALUE_KEYS.includes(functionFieldId)) {
              POST_SOURCING.values[functionFieldId] = bookingFieldValue;
            } else {
              currentRecord.setCurrentSublistValue({
                sublistId: "recmachcustrecord_nges_fun_event",
                fieldId: functionFieldId,
                value: bookingFieldValue,
              });
            }
          },
        );
        currentRecord.commitLine({
          sublistId: "recmachcustrecord_nges_fun_event",
        });
        AUTO_FILLING = false;
      }
    }

    // Reset the bookings updates object
    BOOKINGS_UPDATES.id = null;
    BOOKINGS_UPDATES.values = {};
  };

  /**
   * Updates the space read only field if the space field changed
   *
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  const handleSpaceReadOnlyField = (scriptContext) => {
    if (scriptContext.fieldId === "custrecord_ng_cs_eb_space_ui") {
      const { currentRecord } = scriptContext;
      const spaceId = currentRecord.getCurrentSublistValue({
        sublistId: "recmachcustrecord_ng_cs_eb_event",
        fieldId: "custrecord_ng_cs_eb_space_ui",
      });

      console.log("Space ID", spaceId);

      const comboSpaceIds = spaceId
        ? query
            .runSuiteQL({
              query: `
            SELECT
              custrecord_ng_cs_space_bookings_combo_ss
            FROM
              customrecord_exhibition_hall
            WHERE
              id = ${spaceId}
          `,
            })
            .asMappedResults()[0]
            ?.custrecord_ng_cs_space_bookings_combo_ss?.split(", ") || []
        : [];

      console.log("Combo Space Ids", comboSpaceIds);

      currentRecord.setCurrentSublistValue({
        sublistId: "recmachcustrecord_ng_cs_eb_event",
        fieldId: "custrecord_ng_cs_eb_space",
        value: [spaceId, ...comboSpaceIds],
      });
    }
  };

  /**
   * Ensures that bookings dates are logically valid.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  const checkBookingsDates = (scriptContext) => {
    const { sublistId } = scriptContext;

    if (sublistId === "recmachcustrecord_ng_cs_eb_event") {
      const { currentRecord } = scriptContext;

      // Get current sublist field values for the start and end dates and times
      const startDate = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_ng_cs_eb_event",
        fieldId: "custrecord_ng_cs_eb_start_date",
      });
      const startTime = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_ng_cs_eb_event",
        fieldId: "custrecord_ng_cs_eb_start_time",
      });
      const endDate = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_ng_cs_eb_event",
        fieldId: "custrecord_ng_cs_eb_end_date",
      });
      const endTime = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_ng_cs_eb_event",
        fieldId: "custrecord_ng_cs_eb_end_time",
      });

      // Only proceed if end date or end time is entered
      if (endDate || endTime) {
        if (startDate && startTime) {
          // Combine dates and times into Date objects using string literals
          const startDateTime = new Date(`${startDate} ${startTime}`);
          const endDateTime = new Date(`${endDate} ${endTime}`);

          // Check if end date/time is before start date/time
          if (endDateTime <= startDateTime) {
            alert(
              "Booking end date and time must be after start date and time.",
            );
            return false; // Prevent line save
          }
        }
      }
    }

    return true; // Allow line save
  };

  /**
   * Ensures that functions dates are logically valid.
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  const checkFunctionsDates = (scriptContext) => {
    const { sublistId } = scriptContext;

    if (sublistId === "recmachcustrecord_nges_fun_event") {
      const { currentRecord } = scriptContext;

      // Get current sublist field values for the start and end dates and times
      const startDate = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_nges_fun_event",
        fieldId: "custrecord_nges_fun_start_date",
      });
      const startTime = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_nges_fun_event",
        fieldId: "custrecord_nges_fun_start_time",
      });
      const endDate = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_nges_fun_event",
        fieldId: "custrecord_nges_fun_end_date",
      });
      const endTime = currentRecord.getCurrentSublistText({
        sublistId: "recmachcustrecord_nges_fun_event",
        fieldId: "custrecord_nges_fun_end_time",
      });

      // Only proceed if end date or end time is entered
      if (endDate || endTime) {
        if (startDate && startTime) {
          // Combine dates and times into Date objects using string literals
          const startDateTime = new Date(`${startDate} ${startTime}`);
          const endDateTime = new Date(`${endDate} ${endTime}`);

          // Check if end date/time is before start date/time
          if (endDateTime <= startDateTime) {
            alert(
              "Function end date and time must be after start date and time.",
            );
            return false; // Prevent line save
          }
        }
      }
    }

    return true; // Allow line save
  };

  /**
   * Defaults the dates and times to the selected event. To be called in the fieldChange function
   *
   * @param {Object} scriptContext
   * @param {Record} scriptContext.currentRecord - Current form record
   * @param {string} scriptContext.sublistId - Sublist name
   * @param {string} scriptContext.fieldId - Field name
   * @param {number} scriptContext.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} scriptContext.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  const defaultDatesAndTimesFromEvent = (scriptContext) => {
    const { fieldId } = scriptContext;

    if (fieldId === "custpage_booking_autofill") {
      const { currentRecord, sublistId } = scriptContext;

      const autofill = currentRecord.getCurrentSublistValue({
        sublistId,
        fieldId,
      });

      if (autofill) {
        const fieldMapping = {
          custrecord_cses_start_date: "custrecord_ng_cs_eb_start_date",
          custrecord_cses_start_time: "custrecord_ng_cs_eb_start_time",
          custrecord_cses_end_date: "custrecord_ng_cs_eb_end_date",
          custrecord_cses_end_time: "custrecord_ng_cs_eb_end_time",
        };

        Object.entries(fieldMapping).forEach(
          ([eventFieldId, bookingFieldId]) => {
            const value = currentRecord.getValue({
              fieldId: eventFieldId,
            });

            if (value) {
              currentRecord.setCurrentSublistValue({
                sublistId,
                fieldId: bookingFieldId,
                value,
              });
            }
          },
        );
      }
    }
  };

  return {
    pageInit: pageInit,
    fieldChanged: fieldChanged,
    postSourcing: postSourcing,
    sublistChanged: sublistChanged,
    // lineInit: lineInit,
    // validateField: validateField,
    validateLine: validateLine,
    // validateInsert: validateInsert,
    // validateDelete: validateDelete,
    // saveRecord: saveRecord
  };
});

String.prototype.formatTemplating = function () {
  const args = arguments;
  return this.replace(/{(\d+)}/g, function (match, number) {
    return typeof args[number] != "undefined" ? args[number] : "null"; //match;
  });
};
