/**
 * @NApiVersion 2.x
 * @NScriptType ClientScript
 * @NModuleScope SameAccount
 */
define([
  "N/search",
  "N/url",
  "N/runtime",
  "N/format",
  "N/record",
  "N/currentRecord",
  "../lib/newgen.library.v2",
  "../lib/newgen.library.cs",
], function (search, url, runtime, format, record, currentRecord, NG, csLib) {
  var _UseSubsidiaries = NG.NSFeatures.SUBSIDIARIES;
  var _UseLocations = NG.NSFeatures.LOCATIONS;
  var _UseDupDetect = NG.NSFeatures.DUPLICATES;
  //	var _UseTaxRate = csLib.settings.UseTaxCode;
  //	var _UseCnclPct = csLib.settings.UseCancellationCharge;
  //	var _NoPaymentPrompt = csLib.settings.DoNotPromptIfTerms;
  //	var _NoPromptRoles = csLib.settings.NoPromptRoles;
  //	var _PreventAdtnl = csLib.settings.PreventAdditionalOrders;
  //	var _BlockBilledOrderEditing = csLib.settings.BlockedBilledOrderEditing;
  //	var _AdminUserIDs = csLib.settings.AuthorizedBilledOrderEditors;
  //	var _UseScriptedPaymentForm = csLib.settings.UseScriptedPaymentForm;
  //	var _UseCSJobs = csLib.settings.UseCustomJob;

  var evType = null;
  var exContext = null;
  var gpl = null;
  var taxItem = null;
  var addingToLine01 = false;
  var startingLineCount = 0;
  var _CancellationPct = null;
  var _NewCancellation = null;
  var _Today = NG.time.getSimplifiedDate(new Date());
  var _CurrentRecord = null;
  var _ApplyCnclCharge = false;
  var _CurrentRole = runtime.getCurrentUser().role;
  var _ShowID = null;
  var currentShowId = null;
  var _IsPageInit = false;
  var _AdvDate = null;
  var _PreviousExhib = null;
  var _EntityChanged = false;
  var _ExistingOrder = { check: false, id: "" };
  var _totalPaid = new Number(0);
  var _ShipAddySet = false;
  var addingToLine02 = false;
  var _DATE_DATA = null;
  var _TIME_REG = /^(?:0?\d|1[012]):[0-5]\d( ?(am|pm))$/i;
  var _CALC_WORKER_COUNT = false;
  var _SHOW_DATA = null;
  var _MODAL_ITEM = null;

  var _RunGlobalBO = csLib.settings.GlobalBoothOrderScripting;
  var _ActiveFormBO = false;
  var _TriggerBO = false;
  var _RunGlobalAL = csLib.settings.GlobalAddItemScripting;
  var _ActiveFormAL = false;
  var _NonBoothFormAL = false;
  var _TriggerAL = false;
  var _RunGlobalSM = csLib.settings.GlobalShowMgtScripting;
  var _ActiveFormSM = false;
  var _TriggerSM = false;

  var _SHOW_TABLE_FIELDS = [
    "custrecord_adv_ord_date",
    "custrecord_adv_price_level",
    "custrecord_std_price_level",
    "custrecord_site_price_level",
    "custrecord_cancellation_pct",
    "custrecord_tax_rate",
    "custrecord_show_job",
    "custrecord_fin_show",
    "custrecord_facility",
    "custrecord_tax_rate",
    "custrecord_show_venue",
    "custrecord_show_subsidiary",
    "custrecord_show_mgmnt_price_lvl",
    "custrecord_tax_percent",
    "custrecord_show_type",
    "custrecord_show_type.custrecord_company",
  ];

  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   */
  function pageInit(context) {
    var csSettings = csLib.trigger();
    csLib.settings = csSettings;
    evType = context.mode;

    if (_UseSubsidiaries) {
      _SHOW_TABLE_FIELDS.push("custrecord_show_subsidiary");
    }
    if (_UseLocations) {
      _SHOW_TABLE_FIELDS.push("custrecord_show_venue");
    }

    determineTriggers(context);

    if (_TriggerBO) {
      // DONE
      boothOrderingOperations(context);
    }

    if (_TriggerAL) {
      addLineItemOperations(context);
    }

    if (_TriggerSM) {
      // DONE
      showManagementOperations(context);
    }
  }

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  function fieldChanged(context) {
    if (_TriggerBO) {
      // DONE
      boothFieldChange(context);
    }

    if (_TriggerAL) {
      CS_Field_Changed_02(context);
    }

    if (_TriggerSM) {
      // DONE
      showManagementFieldsOnChange(context);
    }
  }

  /**
   * Function to be executed when field is slaved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   *
   * @since 2015.2
   */
  function postSourcing(context) {
    if (_TriggerBO) {
      CS_Post_Sourcing_01(context);
    }

    if (_TriggerAL) {
      CS_Post_Sourcing_02(context);
    }

    if (_TriggerSM) {
      CS_Post_Sourcing_03(context);
    }
  }

  /**
   * Function to be executed after sublist is inserted, removed, or edited.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function sublistChanged(context) {
    if (_TriggerBO) {
      CS_Sublist_Changed_01(context);
    }

    if (_TriggerAL) {
      CS_Sublist_Changed_02(context);
    }

    if (_TriggerSM) {
      CS_Sublist_Changed_03(context);
    }
  }

  /**
   * Function to be executed after line is selected.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function lineInit(context) {
    if (_TriggerBO) {
      CS_Line_Init_01(context);
    }

    if (_TriggerAL) {
      CS_Line_Init_02(context);
    }

    if (_TriggerSM) {
      CS_Line_Init_03(context);
    }
  }

  /**
   * Validation function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @returns {boolean} Return true if field is valid
   *
   * @since 2015.2
   */
  function validateField(context) {
    if (_TriggerBO) {
      var goBO = CS_Validate_Field_01(context);

      if (!goBO) {
        return false;
      }
    }

    if (_TriggerAL) {
      var goAL = CS_Validate_Field_02(context);

      if (!goAL) {
        return false;
      }
    }

    if (_TriggerSM) {
      var goSM = CS_Validate_Field_03(context);

      if (!goSM) {
        return false;
      }
    }

    return true;
  }

  /**
   * Validation function to be executed when sublist line is committed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateLine(context) {
    if (_TriggerBO) {
      var goBO = CS_Validate_Line_01(context);

      if (!goBO) {
        return false;
      }
    }

    if (_TriggerAL) {
      var goAL = CS_Validate_Line_02(context);

      if (!goAL) {
        return false;
      }
    }

    if (_TriggerSM) {
      var goSM = CS_Validate_Line_03(context);

      if (!goSM) {
        return false;
      }
    }

    return true;
  }

  /**
   * Validation function to be executed when sublist line is inserted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateInsert(context) {
    if (_TriggerBO) {
      var goBO = CS_Validate_Insert_01(context);

      if (!goBO) {
        return false;
      }
    }

    if (_TriggerAL) {
      var goAL = CS_Validate_Insert_02(context);

      if (!goAL) {
        return false;
      }
    }

    if (_TriggerSM) {
      var goSM = CS_Validate_Insert_03(context);

      if (!goSM) {
        return false;
      }
    }

    return true;
  }

  /**
   * Validation function to be executed when record is deleted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function validateDelete(context) {
    if (_TriggerBO) {
      var goBO = CS_Validate_Delete_01(context);

      if (!goBO) {
        return false;
      }
    }

    if (_TriggerAL) {
      var goAL = CS_Validate_Delete_02(context);

      if (!goAL) {
        return false;
      }
    }

    if (_TriggerSM) {
      var goSM = CS_Validate_Delete_03(context);

      if (!goSM) {
        return false;
      }
    }

    return true;
  }

  /**
   * Validation function to be executed when record is saved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   *
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  function saveRecord(context) {
    if (_TriggerBO) {
      var goBO = CS_Save_Record_01(context);

      if (!goBO) {
        return false;
      }
    }

    if (_TriggerAL) {
      var goAL = CS_Save_Record_02(context);

      if (!goAL) {
        return false;
      }
    }

    if (_TriggerSM) {
      var goSM = CS_Save_Record_03(context);

      if (!goSM) {
        return false;
      }
    }

    return true;
  }

  //////////////////////////////

  function determineTriggers(context) {
    exContext = runtime.executionContext;
    var currRec = context.currentRecord; // context.getCurrentRecord();

    if (exContext == "userinterface") {
      if (!_RunGlobalBO) {
        _ActiveFormBO = NG.tools.isInArray(
          currRec.getValue({ fieldId: "customform" }),
          csLib.settings.BoothOrderFormIdListing
        );
      }
      if (_RunGlobalBO || _ActiveFormBO) {
        _TriggerBO = true;
        console.log("Booth Order triggers enabled");
      } else {
        console.log("Booth Order triggers NOT enabled");
      }

      if (!_RunGlobalAL) {
        _ActiveFormAL = NG.tools.isInArray(
          currRec.getValue({ fieldId: "customform" }),
          csLib.settings.AddItemFormIdListing
        );
        _NonBoothFormAL =
          NG.tools.isInArray(
            currRec.getValue({ fieldId: "customform" }),
            csLib.settings.AddItemFormIdListing
          ) &&
          !NG.tools.isInArray(
            currRec.getValue({ fieldId: "customform" }),
            csLib.settings.BoothOrderFormIdListing
          );
      }
      if (_RunGlobalAL || _ActiveFormAL) {
        _TriggerAL = true;
        console.log("Add Item triggers enabled");
      } else {
        console.log("Add Item triggers NOT enabled");
      }

      if (!_RunGlobalSM) {
        _ActiveFormSM = NG.tools.isInArray(
          currRec.getValue({ fieldId: "customform" }),
          csLib.settings.ShowMgtFormIdListing
        );
      }
      if (_RunGlobalSM || _ActiveFormSM) {
        _TriggerSM = true;
        console.log("Show Management triggers enabled");
      } else {
        console.log("Show Management triggers NOT enabled");
      }
    } else if (exContext == "webstore") {
      _TriggerWEB = true;
    }
  }

  function getShowData() {
    console.log("Getting show data");
    var currRec = currentRecord.get();
    var showData = null;
    currentShowId =
      currentShowId || currRec.getValue({ fieldId: "custbody_show_table" });
    if (!NG.tools.isEmpty(currentShowId)) {
      console.log("Retrieving show table data");
      try {
        showData = search.lookupFields({
          type: "customrecord_show",
          id: currentShowId,
          columns: _SHOW_TABLE_FIELDS,
        });
        console.log("Show table data retrieved");
      } catch (err) {
        console.log("Failed to retrieve show table data\n", err);
      }
    }

    //		_SHOW_DATA = showData;

    if (!NG.tools.isEmpty(showData)) {
      for (var stField in showData) {
        if (NG.obj.isArray(showData[stField])) {
          var objValue = showData[stField];
          var arr = new Array();
          if (!NG.tools.isEmpty(objValue) && !NG.tools.isEmpty(objValue[0])) {
            for (var i = 0; i < objValue.length; i++) {
              arr.push(objValue[i].value);
            }
          }
          _SHOW_DATA[stField] = arr;
        } else {
          _SHOW_DATA[stField] = showData[stField];
        }
      }
    }

    console.log("Show table data set to global variable\n", _SHOW_DATA);
  }

  //////////////////////////////////////////////////////
  //													//
  //////////////////////////////////////////////////////
  //					SECTION 001						//
  //////////////////////////////////////////////////////
  //													//
  //		BEGIN BOOTH ORDER CLIENT SCRIPTING			//
  //													//
  //////////////////////////////////////////////////////

  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   */
  function boothOrderingOperations(context) {
    var currRec = context.currentRecord; // context.getCurrentRecord();

    var exhbID = currRec.getValue({ fieldId: "entity" });
    _ShowID = currRec.getValue({ fieldId: "custbody_show" });
    currentShowId = currRec.getValue({ fieldId: "custbody_show_table" });

    if (evType != "create") {
      _PreviousExhib = exhbID;
    }

    if (!NG.tools.isEmpty(exhbID) && !NG.tools.isEmpty(currentShowId)) {
      gpl = currRec.getValue({ fieldId: "custbody_price_level" }) || null;
    }

    if (NG.tools.isEmpty(currentShowId) && csLib.settings.RetainLastShow) {
      var values = {};
      values["action"] = "get";
      values["user"] = runtime.getCurrentUser();

      csLib.func.getLastShow(values, true);
      currentShowId = lastShow;
      if (!NG.tools.isEmpty(lastShow)) {
        currRec.setValue({
          fieldId: "custbody_show_table",
          value: currentShowId,
          ignoreFieldChange: true,
        });
      }
    }

    // TO-DO
    getShowData();

    if (!NG.tools.isEmpty(_SHOW_DATA)) {
      _AdvDate = format.parse({
        value: _SHOW_DATA.custrecord_adv_ord_date,
        type: format.Type.DATE,
      });

      if (csLib.settings.UseCancellationCharge) {
        var rawPct = _SHOW_DATA["custrecord_cancellation_pct"];
        var pct =
          rawPct.search("%") >= 0
            ? new Number(rawPct.replace("%", ""))
            : new Number(rawPct);
        _CancellationPct = pct / 100;
      }

      if (csLib.settings.UseTaxCode && type != "edit") {
        /*var taxRate = _SHOW_DATA['custrecord_tax_rate'];
				if (!NG.tools.isEmpty(taxRate)) {
					nlapiSetFieldValue("istaxable", "T", false);
					nlapiSetFieldValue("taxitem", taxRate, false);
				}*/
      }
    }

    if (evType == "edit") {
      startingLineCount = currRec.getLineCount({ sublistId: "item" });

      if (csLib.settings.UseCancellationCharge) {
        //				_CurrentRecord = nlapiLoadRecord(currRec.type, currRec.id);
        _CurrentRecord = record.load({ type: currRec.type, id: currRec.id });
        if (!NG.tools.isEmpty(currentShowId)) {
          _ApplyCnclCharge = isOnOrAfterMoveInDate(currentShowId);
        }
      }

      if (
        csLib.settings.UseScriptedPaymentForm &&
        NG.tools.isEmpty(
          csLib.settings.DoNotPromptIfTerms
            ? currRec.getValue({ fieldId: "terms" })
            : null
        ) &&
        !NG.tools.isInArray(_CurrentRole, csLib.settings.NoPromptRoles)
      ) {
        triggerBackgroundExhibData(true);
      }
    }

    if (
      !NG.tools.isEmpty(currentShowId) &&
      !NG.tools.isEmpty(_SHOW_DATA) &&
      NG.tools.isEmpty(
        currRec.getValue({ fieldId: "custbody_cseg_ng_cs_job" })
      ) &&
      csLib.settings.UseCustomJob
    ) {
      currRec.setValue({
        fieldId: "custbody_cseg_ng_cs_job",
        value: _SHOW_DATA["custrecord_show_job"],
        ignoreFieldChange: true,
      });
      currRec.setValue({
        fieldId: "class",
        value: _SHOW_DATA["custrecord_fin_show"],
        ignoreFieldChange: true,
      });
    } else if (
      !NG.tools.isEmpty(currentShowId) &&
      !NG.tools.isEmpty(_SHOW_DATA)
    ) {
      currRec.setValue({
        fieldId: "class",
        value: _SHOW_DATA["custrecord_fin_show"],
        ignoreFieldChange: true,
      });
    } else if (NG.tools.isEmpty(_SHOW_DATA)) {
      console.log("_SHOW_DATA is empty");
    }

    var showField = document.forms["main_form"].elements["inpt_custbody_show"];
    if (showField != null) {
      if (isNaN(showField.length)) {
        showField.focus();
      } else {
        if (showField.length > 0) {
          showField[0].focus();
        }
      }
    }
  }

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   * // DONE ON REFACTOR
   */
  function boothFieldChange(context) {
    var currRec = context.currentRecord;

    if (context.fieldId == "custbody_show_table") {
      currentShowId = currRec.getValue({ fieldId: "custbody_show_table" });
      if (!NG.tools.isEmpty(currentShowId)) {
        getShowData();

        if (!NG.tools.isEmpty(_SHOW_DATA)) {
          if (csLib.settings.UseCustomJob) {
            currRec.setValue({
              fieldId: "custbody_cseg_ng_cs_job",
              value: _SHOW_DATA["custrecord_show_job"],
            });
            currRec.setValue({
              fieldId: "class",
              value: _SHOW_DATA["custrecord_fin_show"],
            });
          } else {
            currRec.setValue({
              fieldId: "class",
              value: _SHOW_DATA["custrecord_fin_show"],
            });
          }
          if (_UseSubsidiaries) {
            if (!NG.tools.isEmpty(_SHOW_DATA["custrecord_show_subsidiary"])) {
              currRec.setValue({
                fieldId: "subsidiary",
                value: _SHOW_DATA["custrecord_show_subsidiary"],
                ignoreFieldChange: true,
              });
            } else {
              if (
                NG.tools.isEmpty(currRec.getValue({ fieldId: "subsidiary" }))
              ) {
                currRec.setValue({
                  fieldId: "subsidiary",
                  value: "1",
                  ignoreFieldChange: true,
                });
              }
            }
          }

          if (_UseLocations) {
            currRec.setValue({
              fieldId: "location",
              value: _SHOW_DATA["custrecord_show_venue"],
              ignoreFieldChange: true,
            });
          }

          //					nlapiDisableField("entity", false);
          currRec.getField({ fieldId: "entity" }).isDisabled = false;
          //					_AdvDate = nlapiStringToDate(_SHOW_DATA['custrecord_adv_ord_date']);
          _AdvDate = format.parse({
            value: _SHOW_DATA["custrecord_adv_ord_date"],
            type: format.Type.DATE,
          });
          currRec.setValue({
            fieldId: "custbody_advanced_order_date",
            value: _SHOW_DATA["custrecord_adv_ord_date"],
            ignoreFieldChange: true,
          });

          // DEPRECATED: No longer force taxitem field on records
          if (csLib.settings.UseCancellationCharge) {
            var rawPct = _SHOW_DATA["custrecord_cancellation_pct"];
            var pct =
              rawPct.search("%") >= 0
                ? new Number(rawPct.replace("%", ""))
                : new Number(rawPct);
            _CancellationPct = pct / 100;
            _ApplyCnclCharge = isOnOrAfterMoveInDate(currentShowId);
          }
        }
      }
    }
    // DEPRECATED SHOWTYPE FUNCTIONALITY
    /*		var showType = _SHOW_DATA['custrecord_show_type'];
					var showTypeDeptList = _SHOW_DATA['custrecord_show_type.custrecord_company'];
					if (!NG.tools.isEmpty(showType)) {
						if (!NG.tools.isEmpty(showTypeDeptList)) {
			//				var showTypeDepts = NG.tools.getMultiSelect(null, null, false, showTypeDeptList);
							if (NG.tools.isInArray(csLib.settings.DefaultExhibitorDepartment, showTypeDeptList)) {
								currRec.setValue({ fieldId : "department" , value : csLib.settings.DefaultExhibitorDepartment , ignoreFieldChange : true });
								setTimeout(function() {
									currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : showType , ignoreFieldChange : true });
								}, 2500);
							} else if (NG.tools.isInArray(csLib.settings.DefaultShowMgmtDepartment, showTypeDeptList)) {
								currRec.setValue({ fieldId : "department" , value : csLib.settings.DefaultShowMgmtDepartment , ignoreFieldChange : true });
								setTimeout(function() {
									currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : showType , ignoreFieldChange : true });
								}, 2500);
							} else {
								currRec.setValue({ fieldId : "department" , value : csLib.settings.DefaultExhibitorDepartment , ignoreFieldChange : true });
								setTimeout(function() {
									currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : csLib.settings.DefaultExhibitorOrderType , ignoreFieldChange : true });
								}, 2500);
							}
						} else {
							currRec.setValue({ fieldId : "department" , value : csLib.settings.DefaultExhibitorDepartment , ignoreFieldChange : true });
							setTimeout(function() {
								currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : csLib.settings.DefaultExhibitorOrderType , ignoreFieldChange : true });
							}, 2500);
						}
					} else {
						currRec.setValue({ fieldId : "department" , value : csLib.settings.DefaultExhibitorDepartment , ignoreFieldChange : true });
						setTimeout(function() {
							currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : csLib.settings.DefaultExhibitorOrderType , ignoreFieldChange : true });
						}, 2500);
					}
				}
			} else {
				currRec.setValue({ fieldId : "class" , value : "" });
				currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : "" });
				currRec.setValue({ fieldId : "department" , value : "" });
				currRec.setValue({ fieldId : "location" , value : "" });
				if (_UseCSJobs) {
					currRec.setValue({ fieldId : "custbody_cseg_ng_cs_job" , value : "" });
				}
//				nlapiDisableField("entity", true);
				currRec.getField({ fieldId : "entity" }).isDisabled = false;
			}*/

    /*		 DEPRECATED: scriptid to url is not in DEV
			if (csLib.settings.RetainLastShow) {
				var values = { };
				values['action'] = "set";
				values['user'] = runtime.getCurrentUser().id;
				values['show'] = currentShowId;
//				_scLib.setLastShow(values);
				csLib.func.setLastShow(values);
			}
		}*/

    if (context.fieldId == "custbody_booth") {
      if (NG.tools.isEmpty(currRec.getValue({ fieldId: "custbody_booth" }))) {
        currRec.setValue({ fieldId: "entity", value: "" });
        currRec.setValue({
          fieldId: "custbody_booth_actual_exhibitor",
          value: "",
        });
      }
      // DEPRECATED - department doesn't need to be checked for anymore
      /*	if (NG.tools.isEmpty(currRec.getValue({ fieldId : "custbody_ng_cs_order_type" }))
				&& !NG.tools.isEmpty(currRec.getValue({ fieldId : "department" })) && !NG.tools.isEmpty(_SHOW_DATA)) {
				var showType = _SHOW_DATA['custrecord_show_type'];
				var showTypeDeptList = _SHOW_DATA['custrecord_show_type.custrecord_company'];
				if (!NG.tools.isEmpty(showType)) {
					if (!NG.tools.isEmpty(showTypeDeptList)) {
		//				var showTypeDepts = NG.tools.getMultiSelect(null, null, false, showTypeDeptList);
						if (NG.tools.isInArray(csLib.settings.DefaultExhibitorDepartment, showTypeDeptList)) {
							currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : showType , ignoreFieldChange : true });
						} else if (NG.tools.isInArray(csLib.settings.DefaultShowMgmtDepartment, showTypeDeptList)) {
							currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : showType , ignoreFieldChange : true });
						} else {
							currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : csLib.settings.DefaultExhibitorOrderType , ignoreFieldChange : true });
						}
					} else {
						currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : csLib.settings.DefaultExhibitorOrderType , ignoreFieldChange : true });
					}
				} else {
					currRec.setValue({ fieldId : "custbody_ng_cs_order_type" , value : csLib.settings.DefaultExhibitorOrderType , ignoreFieldChange : true });
				}
			}*/
    }

    ///////////////////////////////////
    /////////////////////////////////// START
    ///////////////////////////////////

    if (
      NG.tools.isInArray(context.fieldId, [
        "custbody_booth",
        "custbody_show_table",
        "entity",
        "custbody_booth_actual_exhibitor",
      ])
    ) {
      console.log("BIG FIELD TRIGGER");
      _EntityChanged = false;
      if (context.fieldId == "entity") {
        _EntityChanged = true;
      }
      if (NG.tools.isEmpty(currentShowId)) {
        currentShowId = currRec.getValue({ fieldId: "custbody_show_table" });
      }
      if (!NG.tools.isEmpty(currentShowId) && NG.tools.isEmpty(_SHOW_DATA)) {
        getShowData();
      } else {
        _SHOW_DATA = null;
      }

      var boothID = currRec.getValue({ fieldId: "custbody_booth" });
      var exhib_ID = currRec.getValue({ fieldId: "entity" });
      var actualExhib = currRec.getValue({
        fieldId: "custbody_booth_actual_exhibitor",
      });

      if (
        !NG.tools.isEmpty(currentShowId) &&
        (!NG.tools.isEmpty(boothID) || !NG.tools.isEmpty(exhib_ID))
      ) {
        if (csLib.settings.PreventAdditionalOrders) {
          if (!NG.tools.isEmpty(boothID)) {
            //						var boothData = nlapiLookupField("customrecord_show_booths", boothID, ["custrecord_booth_exhibitor","custrecord_booth_actual_exhibitor","name"]);
            var boothData = NG.tools.getLookupFields(
              "customrecord_show_booths",
              boothID,
              [
                "custrecord_booth_exhibitor",
                "custrecord_booth_actual_exhibitor",
                "name",
              ]
            );
            var exhbID = boothData.custrecord_booth_exhibitor[0];
            var actualExhbID = boothData.custrecord_booth_actual_exhibitor[0];

            if (NG.tools.isEmpty(exhbID)) {
              boothWithoutExhibitor();
              _EntityChanged = false;
              return;
            }

            if (
              csLib.settings.AllowMultiBillingParties &&
              NG.tools.isEmpty(exhib_ID)
            ) {
              currRec.setValue({
                fieldId: "entity",
                value: exhbID,
                ignoreFieldChange: true,
              });
              currRec.setValue({
                fieldId: "custbody_booth_actual_exhibitor",
                value: actualExhbID,
                ignoreFieldChange: true,
              });
              exhib_ID = exhbID;
              selectExhibitor(
                currentShowId,
                boothID,
                exhib_ID,
                exhbID,
                actualExhbID
              );
            } else {
              selectExhibitor(
                currentShowId,
                boothID,
                exhib_ID,
                exhbID,
                actualExhbID
              );
            }
          } else if (!NG.tools.isEmpty(exhib_ID)) {
            selectBooth(currentShowId, exhib_ID);
          }

          if (
            NG.tools.isEmpty(gpl) ||
            context.fieldId == "custbody_show_table"
          ) {
            setTimeout(function () {
              setPriceLevel(exhib_ID, currentShowId);
            }, 2000);
          }

          // DEPRECATED: Force tax set on record from event
        } else {
          if (
            !NG.tools.isEmpty(currentShowId) &&
            (!NG.tools.isEmpty(boothID) || !NG.tools.isEmpty(exhib_ID))
          ) {
            if (!_tools.isEmpty(boothID)) {
              //							var boothData = nlapiLookupField("customrecord_show_booths", boothID, ["custrecord_booth_exhibitor","custrecord_booth_actual_exhibitor","name"]);
              var boothData = NG.tools.getLookupFields(
                "customrecord_show_booths",
                boothID,
                [
                  "custrecord_booth_exhibitor",
                  "custrecord_booth_actual_exhibitor",
                  "name",
                ]
              );
              var exhbID = boothData.custrecord_booth_exhibitor[0];
              var actualExhbID = boothData.custrecord_booth_actual_exhibitor[0];
              if (
                NG.tools.isEmpty(exhbID) ||
                (NG.tools.isEmpty(actualExhbID) &&
                  csLib.settings.AllowMultiBillingParties)
              ) {
                boothWithoutExhibitor();
                _EntityChanged = false;
                return;
              } else {
                if (
                  !_EntityChanged &&
                  !csLib.settings.AllowMultiBillingParties
                ) {
                  currRec.setValue({
                    fieldId: "entity",
                    value: exhbID,
                    ignoreFieldChange: true,
                  });
                }
                currRec.setValue({
                  fieldId: "custbody_booth_actual_exhibitor",
                  value: actualExhbID,
                  ignoreFieldChange: true,
                });
              }
            } else if (!NG.tools.isEmpty(exhib_ID)) {
              selectBooth(currentShowId, exhib_ID);
            }

            if (
              NG.tools.isEmpty(gpl) ||
              context.fieldId == "custbody_show_table"
            ) {
              setTimeout(function () {
                setPriceLevel(exhib_ID, currentShowId);
              }, 2000);
            }
            // DEPRECATED: Force tax set on record from event
          }
        }

        // OPTIMIZED
        if (!NG.tools.isEmpty(exhib_ID)) {
          if (!NG.tools.isEmpty(_SHOW_DATA)) {
            if (csLib.settings.UseCustomJob) {
              currRec.setValue({
                fieldId: "custbody_cseg_ng_cs_job",
                value: _SHOW_DATA["custrecord_show_job"],
              });
              currRec.setValue({
                fieldId: "class",
                value: _SHOW_DATA["custrecord_fin_show"],
              });
            } else {
              currRec.setValue({
                fieldId: "class",
                value: _SHOW_DATA["custrecord_fin_show"],
              });
            }
          }
        }
      } else if (
        !NG.tools.isEmpty(currentShowId) &&
        context.fieldId == "custbody_booth_actual_exhibitor" &&
        !NG.tools.isEmpty(actualExhib) &&
        csLib.settings.AllowMultiBillingParties
      ) {
        // DEPRECATED - Booths have their own validation on them so not allow multiple booths to be placed on the same event.
        /*var bFilt = new Array(
						["custrecord_booth_show_table","anyof",[currentShowId]]
					,	"and"
					,	["custrecord_booth_actual_exhibitor","anyof",[actualExhib]]
				);
//				var bCols = new Array(
//						new nlobjSearchColumn("custrecord_booth_exhibitor", null, null)
//					,	new nlobjSearchColumn("name", null, null)
//				);
				var bCols = new Array(
						search.createColumn({ name : "name" })
					,	search.createColumn({ name : "custrecord_booth_exhibitor" })
				);
				var bSearch = null;
				var hasErr = false;
				try {
//					bSearch = nlapiSearchRecord("customrecord_show_booths", null, bFilt, bCols);
					bSearch = NG.tools.getSearchResults("customrecord_show_booths", bFilt, bCols, null, 1000, false, false);
				} catch (err) {
//					_log.logError(err, "Error encountered getting exhibitor booths");
					NG.log.logError(err, "Error encountered getting exhibitor booths");
					console.log("Error encountered getting exhibitor booths", err);
					hasErr = true;
				}
				
				if (bSearch != null) {
					if (bSearch.length == 1) {
						currRec.setValue({ fieldId : "custbody_booth" , value : bSearch[0].id , ignoreFieldChange : true });
						currRec.setValue({ fieldId : "entity" , value : bSearch[0].getValue({ name : "custrecord_booth_exhibitor" }) , ignoreFieldChange : false });
					} else if (bSearch.length > 1) {
						var boothList = new Array();
						for (var b = 0; b < bSearch.length; b++) {
							boothList.push(bSearch[b].getValue({ name : "name" }));
						}
						window.alert("This exhibitor has multiple booths associated with this show. Please select one of the following in the \"Booth\" field.\n\n{0}".NG_Format(boothList.join("\n")));
						return;
					}
				} else if (!hasErr) {
					window.alert("This exhibitor does not apear to have any booths associated with this show.");
					currRec.setValue({ fieldId : "custbody_booth_actual_exhibitor" , value : "" , ignoreFieldChange : true });
				}*/
      }
      _EntityChanged = false;

      if (
        !NG.tools.isEmpty(currentShowId) &&
        !NG.tools.isEmpty(boothID) &&
        (csLib.settings.AllowMultiBillingParties
          ? !NG.tools.isEmpty(exhib_ID)
          : true)
      ) {
        setTimeout(function () {
          triggerBackgroundExhibData();
        }, 4000);
      }
      if (
        !NG.tools.isEmpty(currentShowId) &&
        !NG.tools.isEmpty(boothID) &&
        !NG.tools.isEmpty(exhib_ID)
      ) {
        try {
          if (NG.tools.isEmpty(_SHOW_DATA)) {
            getShowData();
          }
          if (!NG.tools.isEmpty(_SHOW_DATA)) {
            setFacilityShipAddress(
              _SHOW_DATA["custrecord_facility"],
              _SHOW_DATA["custrecord_tax_rate"]
            );
          }
        } catch (err) {}
      }

      if (!_tools.isEmpty(_SHOW_DATA)) {
      }
    }

    ///////////////////////////////////
    /////////////////////////////////// END
    ///////////////////////////////////

    // DONE ON REFACTOR ✅ // Enforcement of minimum quantity
    if (context.sublistId == "item") {
      if (context.fieldId == "quantity") {
        addingToLine02 = addingToLine02 || false;
        if (!addingToLine01 && !addingToLine02) {
          addingToLine01 = true;
          //					var parentItem = nlapiGetCurrentLineItemValue("item", "item");
          var parentItem = currRec.getCurrentSublistValue({
            sublistId: "item",
            fieldId: "item",
          });
          if (!NG.tools.isEmpty(parentItem)) {
            //						var currQty = new Number(nlapiGetCurrentLineItemValue("item", "quantity"));
            var currQty = new Number(
              currRec.getCurrentSublistValue({
                sublistId: "item",
                fieldId: "quantity",
              })
            );
            var booth = currRec.getValue({ fieldId: "custbody_booth" });
            //						var minQ = new Number(nlapiLookupField("item", parentItem, "minimumquantity"));
            var minQ = new Number(
              NG.tools.getLookupFields("item", parentItem, ["minimumquantity"])[
                "minimumquantity"
              ]
            );
            if (isNaN(minQ)) {
              minQ = new Number(0);
            }
            if (minQ > 1 && currQty < minQ) {
              //							nlapiSetCurrentLineItemValue("item", "quantity", minQ, true);
              currRec.setCurrentSublistValue({
                sublistId: "item",
                fieldId: "quantity",
                value: minQ,
              });
            } else if (
              (NG.tools.isInArray(parentItem, csLib.settings.sqftItems) ||
                NG.tools.isInArray(parentItem, csLib.settings.sqdItems)) &&
              !NG.tools.isEmpty(booth)
            ) {
              //							var boothDims = nlapiLookupField("customrecord_show_booths", booth, ["custrecord_booth_length","custrecord_booth_width"]);
              var boothDims = NG.tools.getLookupFields(
                "customrecord_show_booths",
                booth,
                ["custrecord_booth_length", "custrecord_booth_width"]
              );
              var boothL = new Number(boothDims.custrecord_booth_length);
              var boothW = new Number(boothDims.custrecord_booth_length);
              if (isNaN(boothL) || boothL < 10) {
                boothL = new Number(10);
              }
              if (isNaN(boothW) || boothW < 10) {
                boothW = new Number(10);
              }
              minQ = boothL * boothW;
              if (currQty < minQ) {
                //								nlapiSetCurrentLineItemValue("item", "quantity", minQ, true);
                currRec.setCurrentSublistValue({
                  sublistId: "item",
                  fieldId: "quantity",
                  value: minQ,
                });
              }
            }
            addingToLine01 = false;
          }
        }
      }
    }
  }

  /**
   * Function to be executed when field is slaved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   *
   * @since 2015.2
   */
  function CS_Post_Sourcing_01(context) {
    var currRec = context.currentRecord;

    if (context.fieldId == "entity") {
      setTimeout(function () {
        if (NG.tools.isEmpty(gpl)) {
          gpl = currRec.getValue({ name: "custbody_price_level" });
        }
        // setOrderTax();
      }, 250);
    }

    if (context.sublistId == "item") {
      if (context.fieldId == "item") {
        if (!addingToLine01 && !addingToLine02) {
          if (
            !NG.tools.isEmpty(
              currRec.getValue({ name: "custbody_price_level" })
            )
          ) {
            gpl = currRec.getValue({ name: "custbody_price_level" });
          }
          if (!_tools.isEmpty(gpl)) {
            currRec.setCurrentSublistValue({
              sublistId: context.sublistId,
              fieldId: "price",
              value: gpl,
            });
          }
        }
      }
    }
  }

  /**
   * Function to be executed after sublist is inserted, removed, or edited.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   * // DONE ON REFACTOR
   */
  function CS_Sublist_Changed_01(context) {
    var currRec = context.currentRecord;

    if (context.sublistId == "item") {
      if (addingToLine01) {
        return;
      }

      if (_NewCancellation != null) {
        addingToLine01 = true;

        try {
          setTimeout(function () {
            currRec.selectNewLine({ sublistId: "item" });
            currRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "item",
              value: csLib.settings.CancellationChargeID,
            });
            setTimeout(function () {
              currRec.setCurrentSublistValue({
                sublistId: "item",
                fieldId: "quantity",
                value: "1",
                ignoreFieldChange: true,
              });
              currRec.setCurrentSublistValue({
                sublistId: "item",
                fieldId: "price",
                value: "-1",
              });
              setTimeout(function () {
                currRec.setCurrentSublistValue({
                  sublistId: "item",
                  fieldId: "rate",
                  value: new Number(_NewCancellation.rate).toFixed(2),
                });
                currRec.setCurrentSublistValue({
                  sublistId: "item",
                  fieldId: "description",
                  value: _NewCancellation.description,
                });
                currRec.setCurrentSublistValue({
                  sublistId: "item",
                  fieldId: "custcol_canc_descr",
                  value: _NewCancellation.description,
                });

                setTimeout(function () {
                  currRec.commitLine({ sublistId: "item" });
                  _NewCancellation = null;
                  addingToLine01 = false;
                }, 250);
              }, 500);
            }, 250);
          }, 500);
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered adding cancellation charge to line item"
          );
          window.alert(
            "There was a problem with adding the cancellation charge to this order"
          );
          addingToLine01 = false;
          _NewCancellation = null;
        }
      }
    }
  }

  /**
   * Function to be executed after line is selected.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function CS_Line_Init_01(context) {}

  /**
   * Validation function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @returns {boolean} Return true if field is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Field_01(context) {
    var currRec = context.currentRecord;

    // DEPRECATED REDUNDANT CHECK...
    /*if (csLib.settings.UseCancellationCharge && evType == "edit" && _ApplyCnclCharge) {
			if (context.sublistId == "item") {
				if (context.fieldId == "isclosed") {
					var isClosed = currRec.getValue({ fieldId : "isclosed" });
					if (isClosed == "T") {
						return processCancellationPct();
					}
				}
				
				if (context.fieldId == "quantity") {
					return processCancellationPct(context.lineNum);
				}
			}
		}*/

    return true;
  }

  /**
   * Validation function to be executed when sublist line is committed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Line_01(context) {
    return true;
  }

  /**
   * Validation function to be executed when sublist line is inserted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Insert_01(context) {
    return true;
  }

  /**
   * Validation function to be executed when record is deleted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   * DONE ON REFACTOR!
   */
  function CS_Validate_Delete_01(context) {
    var currRec = context.currentRecord;

    if (_UseCnclPct && evType == "edit" && _ApplyCnclCharge) {
      if (context.sublistId == "item") {
        var lineCode = currRec.getCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_linecode",
        });
        if (_tools.isEmpty(lineCode)) {
          return true;
        }
        var lineItemID = currRec.getCurrentSublistValue({
          sublistId: "item",
          fieldId: "item",
        });
        if (lineItemID == csLib.settings.CancellationChargeID) {
          return window.confirm(
            "Are you certain you want to remove this cancellation charge from the order?"
          );
        }
        return processCancellationPct();
      }
    }

    return true;
  }

  /**
   * Validation function to be executed when record is saved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   *
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  function CS_Save_Record_01(context) {
    var currRec = context.currentRecord; // context.getCurrentRecord();
    var payPrompt = false;

    if (!checkForAdditionalItems()) {
      return false;
    }

    var booth = currRec.getValue({ fieldId: "custbody_booth" });

    if (evType == "create" && !_ShipAddySet) {
      try {
        if (NG.tools.isEmpty(_SHOW_DATA)) {
          getShowData();
        }
        //setFacilityShipAddress(_SHOW_DATA['custrecord_facility'],_SHOW_DATA['custrecord_tax_rate']);
      } catch (err) {
        console.log("Error getting show data", err);
      }
    }

    // Redirect to order thats already existing
    // DEPRECATE: Estimate is not needed as of yet
    if (currRec.type != "estimate") {
      if (evType == "create" && csLib.settings.PreventAdditionalOrders) {
        if (_ExistingOrder.check) {
          var orderID = _ExistingOrder.id;
          var go = window.confirm(
            "You cannot save this order as one already exists for this booth" +
              (csLib.settings.AllowMultiBillingParties
                ? " and billing party"
                : "") +
              '. Click "OK" to redirect to the existing order, click "Cancel" to stay here and edit the order.'
          );
          if (go) {
            NG.tools.clearFormChanged();
            setTimeout(function () {
              //							window.location = nlapiResolveURL("RECORD", "salesorder", orderID, "VIEW");
              window.location = url.resolveRecord({
                recordType: "salesorder",
                recordId: orderID,
                isEditMode: false,
              });
            }, 1000);
            return false;
          } else {
            return false;
          }
        }
      }

      var totalPaid = new Number(0);
      var totalCredit = new Number(0);
      var finalAmount = new Number(0);
      /*
			DEPRECATED: Payment Form is no longer used

			var terms = csLib.settings.DoNotPromptIfTerms ? currRec.getValue({ fieldId : "terms" }) : null;
			if (csLib.settings.UseScriptedPaymentForm && NG.tools.isEmpty(terms) && !NG.tools.isInArray(_CurrentRole, csLib.settings.NoPromptRoles)) {
				totalPaid = _totalPaid;
				
				if (currRec.type == "invoice" && !NG.tools.isEmpty(currRec.id)) {
					var tFilt = new Array(
							["custbody_show_table","anyof",[currentShowId]]
						,	"and"
						,	["custbody_booth","anyof", [booth]]
						,	"and"
						,	["mainline","is","T"]
					);
					if (csLib.settings.AllowMultiBillingParties) {
						tFilt.push(
								"and"
							,	["entity","anyof",[currRec.getValue({ fieldId : "entity" })]]
						);
					}
					/!*var pCols = new Array(
							new nlobjSearchColumn("internalid", null, null)
						,	new nlobjSearchColumn("trandate", null, null)
						,	new nlobjSearchColumn("amount", null, null)
					);*!/
					/!*var cmCols = new Array(
							new nlobjSearchColumn("internalid", null, null)
						,	new nlobjSearchColumn("trandate", null, null)
						,	new nlobjSearchColumn("total", null, null)
					);*!/
					var pCols = new Array(
							search.createColumn({ name : "internalid" })
						,	search.createColumn({ name : "trandate" })
						,	search.createColumn({ name : "amount" })
					);
					var cmCols = new Array(
							search.createColumn({ name : "internalid" })
						,	search.createColumn({ name : "trandate" })
						,	search.createColumn({ name : "total" })
					);
					var pSearch = null;
					var cmSearch = null;
					if (pSearch == null) {
						var pFilt = new Array(
								["appliedtotransaction","anyof",[currRec.id]]
						);
						
						try {
//							pSearch = nlapiSearchRecord("customerpayment", null, pFilt, pCols);
							pSearch = NG.tools.getSearchResults("customerpayment", pFilt, pCols);
						} catch (err) {
							_log.logError(err, "Error encountered searching for related payments");
						}
						if (pSearch != null) {
							for (var p = 0; p < pSearch.length; p++) {
								var payAmount = new Number(pSearch[p].getValue({ name : "trandate" }));
								totalPaid += payAmount;
							}
						}
					}
					
					try {
//						cmSearch = nlapiSearchRecord("creditmemo", null, tFilt, cmCols);
						cmpSearch = NG.tools.getSearchResults("creditmemo", tFilt, cmCols);
					} catch (err) {
						_log.logError(err, "Error encountered searching for related credits");
					}
					if (cmSearch != null) {
						for (var cm = 0; cm < cmSearch.length; cm++) {
							var cmAmount = new Number(cmSearch[cm].getValue({ name : "total" }));
							totalCredit += cmAmount;
						}
					}
				}
				
				var orderVal = csLib.settings.ExemptEstimatedItems ? getOrderTotal() : new Number(currRec.getValue({ fieldId : "total" }));
				finalAmount += _M.roundToHundredths(orderVal + totalCredit);
				if (Math.abs(_M.roundToHundredths(totalPaid)) >= Math.abs(_M.roundToHundredths(finalAmount)) ||
						(csLib.settings.NoPromptUnderZero && (Math.abs(_M.roundToHundredths(finalAmount)) - Math.abs(_M.roundToHundredths(totalPaid)) <= 0))) {
					payPrompt = false;
				} else {
					payPrompt = true;
				}
				if (csLib.settings.PromptForNewLines && startingLineCount < currRec.getLineCount({ sublistId : "item" })) {
					payPrompt = true;
				}
				if (NG.tools.isInArray(_CurrentRole, csLib.settings.NoPromptRoles)) {
					payPrompt = false;
				}
			} else {
				payPrompt = false;
			}
			
			if (payPrompt) {
				var msg = "This exhibitor has an unpaid balance. Create new payment?";
				console.log("This exhibitor has an unpaid balance. Create new payment?");
				var go = window.confirm(msg);
				
				if (go) {
					currRec.setValue({ fieldId : "custbody_create_payment" , value : "T" });
				}
			}*/
    }
    /* DEPRECATION - no longer needed
		if (csLib.settings.BlockedBilledOrderEditing) {
			if (currRec.getValue({ fieldId : "status" }) == "Billed" && currRec.type == "salesorder" && !NG.tools.isInArray(runtime.getCurrentUser().id, csLib.settings.AuthorizedBilledOrderEditors)) {
				window.alert("This booth order has already been billed. No further changes can be made. If you need to make a change, please edit the invoice.");
				return false;
			}
		}*/

    return true;
  }

  function setPriceLevel(exhbID) {
    var currRec = currentRecord.get();
    if (NG.tools.isEmpty(_SHOW_DATA)) {
      getShowData();
    }
    var orderType = currRec.getValue({ fieldId: "custbody_ng_cs_order_type" });
    if (!NG.tools.isInArray(orderType, csLib.settings.ShowMgmtOrderTypes)) {
      //			var today = nlapiStringToDate(nlapiDateToString(new Date(), "date")).getTime();
      var today = NG.time.getSimplifiedDate(new Date()).getTime();
      if (!NG.tools.isEmpty(_SHOW_DATA)) {
        if (!NG.tools.isEmpty(_SHOW_DATA["custrecord_adv_ord_date"])) {
          //					_AdvDate = nlapiStringToDate(_SHOW_DATA['custrecord_adv_ord_date']);
          _AdvDate = format.parse({
            value: _SHOW_DATA["custrecord_adv_ord_date"],
            type: format.Type.DATE,
          });
          var advDate = _AdvDate.getTime();

          if (today <= advDate) {
            gpl = _SHOW_DATA["custrecord_adv_price_level"];
          } else {
            var startDate = csLib.settings.getStartDate(currentShowId);
            if (startDate != null && today >= startDate) {
              gpl =
                _SHOW_DATA["custrecord_site_price_level"] ||
                _SHOW_DATA["custrecord_std_price_level"] ||
                gpl;
            } else {
              gpl = _SHOW_DATA["custrecord_std_price_level"] || gpl;
            }
          }
        } else {
          var startDate = csLib.settings.getStartDate(currentShowId);
          if (startDate != null && today >= startDate) {
            gpl =
              _SHOW_DATA["custrecord_site_price_level"] ||
              _SHOW_DATA["custrecord_std_price_level"] ||
              gpl;
          } else {
            gpl = _SHOW_DATA["custrecord_std_price_level"] || gpl;
          }
        }
        gpl = gpl || "1";
        //				nlapiSetFieldValue("custbody_price_level", gpl);
        currRec.setValue({ fieldId: "custbody_price_level", value: gpl });
      }
    } else {
      if (!NG.tools.isEmpty(_SHOW_DATA)) {
        if (!NG.tools.isEmpty(_SHOW_DATA["custrecord_show_mgmnt_price_lvl"])) {
          //					nlapiSetFieldValue("custbody_price_level", _SHOW_DATA['custrecord_show_mgmnt_price_lvl']);
          currRec.setValue({
            fieldId: "custbody_price_level",
            value: _SHOW_DATA["custrecord_show_mgmnt_price_lvl"],
          });
          gpl = _SHOW_DATA["custrecord_show_mgmnt_price_lvl"];
        }
      }
    }
  }

  function getStartDate() {
    var currRec = currentRecord.get();
    if (NG.tools.isEmpty(currentShowId)) {
      getShowData();
    }
    var startDateT = csLib.settings.getShowDates(currentShowId, "min", false);
    var startDateD = !NG.tools.isEmpty(startDateT)
      ? format.parse({ value: startDateT, type: format.Type.DATE })
      : null;
    var startDate = !NG.tools.isEmpty(startDateD) ? startDateD.getTime() : null;

    return startDate;
  }

  function processCancellationPct(linenum) {
    var currRec = currentRecord.get();
    if (linenum == null) {
      //			var cnclCharge = _M.roundToHundredths(_CancellationPct * new Number(nlapiGetCurrentLineItemValue("item", "amount")));
      var cnclCharge = _M.roundToHundredths(
        _CancellationPct *
          new Number(
            currRec.getCurrentSublistValue({
              sublistId: "item",
              fieldId: "amount",
            })
          )
      );
      var go = window.confirm(
        "This action will incur a ${0} cancellation charge. Continue?".NG_Format(
          cnclCharge.toFixed(2)
        )
      );
      if (go) {
        //				_NewCancellation = { rate : cnclCharge , description : "Cancellation of " + nlapiGetCurrentLineItemText("item", "item") + " x " + nlapiGetCurrentLineItemValue("item", "quantity") };
        _NewCancellation = {
          rate: cnclCharge,
          description: "Cancellation of {0} x {1}".NG_Format(
            currRec.getCurrentSublistText({
              sublistId: "item",
              fieldId: "item",
            }),
            currRec.getCurrentSublistValue({
              sublistId: "item",
              fieldId: "quantity",
            })
          ),
        };
      }

      return go;
    } else {
      //			var currItem = nlapiGetCurrentLineItemValue("item", "item");
      //			var recItem = _CurrentRecord.getLineItemValue("item", "item", linenum);
      //			var currQty = new Number(nlapiGetCurrentLineItemValue("item", "quantity"));
      var currItem = currRec.getCurrentSublistValue({
        sublistId: "item",
        fieldId: "item",
      });
      var recItem = _CurrentRecord.getSublistValue({
        sublistId: "item",
        fieldId: "item",
        line: linenum,
      });
      var currQty = new Number(
        currRec.getCurrentSublistValue({
          sublistId: "item",
          fieldId: "quantity",
        })
      );
      var oldQty = 0;
      if (currItem == recItem) {
        //				oldQty = new Number(_CurrentRecord.getLineItemValue("item", "quantity", linenum));
        oldQty = new Number(
          _CurrentRecord.getSublistValue({
            sublistId: "item",
            fieldId: "quantity",
            line: linenum,
          })
        );
      } else {
        //				var lineCode = nlapiGetCurrentLineItemValue("item", "custcol_linecode");
        var lineCode = currRec.getCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_linecode",
        });
        var recLines = _CurrentRecord.getLineItemCount("item");
        for (var l = 1; l <= recLines; l++) {
          //					var recLineCode = _CurrentRecord.getLineItemValue("item", "custcol_linecode", l);
          var recLineCode = _CurrentRecord.getSublistValue({
            sublistId: "item",
            fieldId: "custcol_linecode",
            line: l,
          });
          if (recLineCode == lineCode) {
            //						oldQty = new Number(_CurrentRecord.getLineItemValue("item", "quantity", l));
            oldQty = new Number(
              _CurrentRecord.getSublistValue({
                sublistId: "item",
                fieldId: "quantity",
                line: l,
              })
            );
            break;
          }
        }
      }

      if (oldQty == 0 || (!(oldQty < 0) && !(oldQty > 0))) {
        return true;
      } else {
        if (currQty < oldQty) {
          var diff = _M.roundToHundredths(oldQty - currQty);
          //					var rate = new Number(nlapiGetCurrentLineItemValue("item", "rate"));
          var rate = new Number(
            currRec.getCurrentSublistValue({
              sublistId: "item",
              fieldId: "rate",
            })
          );
          var cnclCharge = _M.roundToHundredths(
            _CancellationPct * (diff * rate)
          );
          var go = window.confirm(
            "This action will incur a ${0} cancellation charge. Continue?".NG_Format(
              cnclCharge.toFixed(2)
            )
          );
          if (go) {
            _NewCancellation = {
              rate: cnclCharge,
              description: "Cancellation of {0} x {1}".NG_Format(
                currRec.getCurrentSublistText({
                  sublistId: "item",
                  fieldId: "item",
                }),
                diff
              ),
            };
          }

          return go;
        } else {
          return true;
        }
      }
    }
  }

  function isOnOrAfterMoveInDate(_ShowTableID) {
    var currRec = currentRecord.get();
    var threshold = csLib.settings.CancellationChargeThreshold;
    if (NG.tools.isInArray(threshold, ["2", "3", "4"])) {
      var map = [
        null,
        null,
        csLib.settings.DefaultShowDateType,
        csLib.settings.DefaultShowMoveInDateType,
        csLib.settings.DefaultExhibMoveInDateType,
      ];

      var filt = new Array(
        ["custrecord_show_number_date", "anyof", [_ShowTableID]],
        "and",
        ["custrecord_date_type", "anyof", [parseInt(map[threshold])]]
      );

      /*var cols = new Array(
					new nlobjSearchColumn("custrecord_date", null, "min")
			);*/

      var cols = new Array(search.createColumn({ name: "custrecord_date" }));

      var cSearch = null;

      try {
        // search = nlapiSearchRecord("customrecord_show_date", null, filt, cols);
        cSearch = NG.tools.getSearchResults(
          "customrecord_show_date",
          filt,
          cols
        );
      } catch (err) {
        _log.logError(err, "Error encounterd searching for show move-in dates");
      }

      if (cSearch != null) {
        var showDateRaw = cSearch[0].getValue(cols[0]);
        if (!NG.tools.isEmpty(showDateRaw)) {
          //					var showDate = nlapiStringToDate(showDateRaw);
          var showDate = format.parse({
            value: showDateRaw,
            type: format.Type.DATE,
          });
          if (_Today.getTime() >= showDate.getTime()) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      } else {
        return false;
      }
    } else {
      if (NG.tools.isEmpty(_AdvDate)) {
        return false;
      } else {
        if (_Today.getTime() <= _AdvDate.getTime()) {
          return false;
        } else {
          return true;
        }
      }
    }
  }

  function setFacilityShipAddress(facility, taxItem) {
    /*if (!NG.tools.isEmpty(facility)) {
			setTimeout(function() {
				var facAddrRef = "facAddrRef";
				if (!NG.tools.isEmpty(facAddrRef)) {
					var shipAddrValues = nlapiLookupField("customrecord_facility", facility, ["custrecord_address_ref.shipaddressee","custrecord_address_ref.shipaddress1","custrecord_address_ref.shipaddress2","custrecord_address_ref.shipcity","custrecord_address_ref.shipcountry","custrecord_address_ref.shipphone","custrecord_address_ref.shipstate","custrecord_address_ref.shipzip","custrecord_address_ref.addressinternalid"]);
					if (!NG.tools.isEmpty(shipAddrValues)) {
						nlapiSetFieldValue("shipaddresslist", "", true);
						nlapiSetFieldValue("shipoverride", "T");
						nlapiSetFieldValue("shipcountry", shipAddrValues['custrecord_address_ref.shipcountry']);
						nlapiSetFieldValue("shipaddr1", shipAddrValues['custrecord_address_ref.shipaddress1']);
						nlapiSetFieldValue("shipaddr2", shipAddrValues['custrecord_address_ref.shipaddress2']);
						nlapiSetFieldValue("shipphone", shipAddrValues['custrecord_address_ref.shipphone']);
						nlapiSetFieldValue("shipcity", shipAddrValues['custrecord_address_ref.shipcity']);
						nlapiSetFieldValue("shipstate", shipAddrValues['custrecord_address_ref.shipstate']);
						nlapiSetFieldValue("shipzip", shipAddrValues['custrecord_address_ref.shipzip']);
						nlapiSetFieldValue("shipaddress", "{0}{1}\n{2}{3}, {4} {5}{6}".NG_Format(
							!NG.tools.isEmpty(shipAddrValues['custrecord_address_ref.shipaddressee']) ? "{0}\n".NG_Format(shipAddrValues['custrecord_address_ref.shipaddressee']) : "",
							shipAddrValues['custrecord_address_ref.shipaddress1'],
							!NG.tools.isEmpty(shipAddrValues['custrecord_address_ref.shipaddress2']) ? "{0}\n".NG_Format(shipAddrValues['custrecord_address_ref.shipaddress2']) : "",
							shipAddrValues['custrecord_address_ref.shipcity'],
							shipAddrValues['custrecord_address_ref.shipstate'],
							shipAddrValues['custrecord_address_ref.shipzip'],
							!NG.tools.isEmpty(shipAddrValues['custrecord_address_ref.shipphone']) ? "\n{0}".NG_Format(shipAddrValues['custrecord_address_ref.shipphone']) : ""
						));
						_ShipAddySet = true;
						if (!NG.tools.isEmpty(taxItem)) {
							setTimeout(function() {
								nlapiSetFieldValue("taxitem", taxItem);
							}, 2000);
						}
					}
				}
			}, 1500);
		}*/
    return;
  }

  function setOrderTax(context) {
    var currRec = currentRecord.get();
    taxItem = null;
    var taxRate = null;
    try {
      taxItem = _SHOW_DATA["custrecord_tax_rate"];
      taxRate = _SHOW_DATA["custrecord_tax_percent"];
    } catch (err) {
      console.log(err);
    }
    if (!NG.tools.isEmpty(taxItem)) {
      setTimeout(function () {
        //				nlapiSetFieldValue("istaxable", "T", false);
        //				nlapiSetFieldValue("taxitem", taxItem, false);
        //				nlapiSetFieldValue("taxrate", taxRate, false);
        currRec.setValue({
          fieldId: "istaxable",
          value: "T",
          ignoreFieldChange: true,
        });
        currRec.setValue({
          fieldId: "taxitem",
          value: taxItem,
          ignoreFieldChange: true,
        });
        currRec.setValue({
          fieldId: "taxrate",
          value: taxRate,
          ignoreFieldChange: true,
        });
      }, 250);
    }
  }

  function selectExhibitor(
    _ShowTableID,
    boothID,
    exhib_ID,
    exhbID,
    actualExhbID,
    setBooth
  ) {
    var currRec = currentRecord.get();
    var orderNum = currRec.getValue({ fieldId: "tranid" });
    var filt = new Array(
      ["custbody_show_table", "anyof", [_ShowTableID]],
      "and",
      ["custbody_booth", "anyof", [boothID]]
    );
    if (csLib.settings.AllowMultiBillingParties) {
      filt.push("and", ["entity", "anyof", [exhib_ID]]);
    }

    var sSearch = null;

    try {
      //			search = nlapiSearchRecord("salesorder", null, filt, null);
      sSearch = NG.tools.getSearchResults("salesorder", filt, null);
    } catch (err) {
      console.log(err);
    }
    if (sSearch != null) {
      var soID = sSearch[0].getId();
      try {
        if (csLib.settings.PrevntBthOrderRedirectAlert) {
          NG.tools.clearFormChanged();
        }
        console.log("triggering redirect with delay (A)");
        setTimeout(function () {
          var go = window.confirm(
            "You cannot save this order as one already exists for this booth" +
              (csLib.settings.AllowMultiBillingParties
                ? " and billing party"
                : "") +
              '. Click "OK" to redirect to the existing order, click "Cancel" to stay here and edit the order.'
          );
          if (go) {
            if (csLib.settings.PrevntBthOrderRedirectAlert) {
              NG.tools.clearFormChanged();
              if (NS.form.isChanged()) NS.form.setChanged(false);
            }
            console.log(
              "initiating redirect (A) -- Is Changed: {0}".NG_Format(
                NS.form.isChanged()
              )
            );
            //						window.location = nlapiResolveURL("RECORD", "salesorder", soID, "EDIT");
            window.location = url.resolveRecord({
              recordType: "salesorder",
              recordId: soID,
              isEditMode: true,
            });
          }
        }, 1000);
      } catch (err) {
        console.log(err);
      }
    } else {
      if (NG.tools.isEmpty(orderNum) || orderNum == "To Be Generated") {
        if (setBooth) {
          //					nlapiSetFieldValue("custbody_booth", boothID, false);
          currRec.setValue({
            fieldId: "custbody_booth",
            value: boothID,
            ignoreFieldChange: true,
          });
        } else if (
          !_EntityChanged &&
          !csLib.settings.AllowMultiBillingParties
        ) {
          //					nlapiSetFieldValue("entity", exhbID, false);
          currRec.setValue({
            fieldId: "entity",
            value: exhbID,
            ignoreFieldChange: true,
          });
        }
        //				nlapiSetFieldValue("custbody_booth_actual_exhibitor", actualExhbID, false);
        currRec.setValue({
          fieldId: "custbody_booth_actual_exhibitor",
          value: actualExhbID,
          ignoreFieldChange: true,
        });
      } else {
        if (
          _EntityChanged &&
          !NG.tools.isEmpty(_PreviousExhib) &&
          _PreviousExhib != exhbID
        ) {
          if (
            window.confirm(
              "Are you changing the {0} on this order?".NG_Format(
                csLib.settings.AllowMultiBillingParties
                  ? "billing party"
                  : "exhibitor"
              )
            )
          ) {
            if (setBooth) {
              //							nlapiSetFieldValue("custbody_booth", boothID, false);
              currRec.setValue({
                fieldId: "custbody_booth",
                value: boothID,
                ignoreFieldChange: true,
              });
            } else {
              //							nlapiSetFieldValue("entity", exhib_ID, false);
              currRec.setValue({
                fieldId: "entity",
                value: exhib_ID,
                ignoreFieldChange: true,
              });
            }
            //						nlapiSetFieldValue("custbody_booth_actual_exhibitor", actualExhbID, false);
            currRec.setValue({
              fieldId: "custbody_booth_actual_exhibitor",
              value: actualExhbID,
              ignoreFieldChange: true,
            });
            return;
          }
        }

        //				var loc = nlapiResolveURL("RECORD", "salesorder", null, "EDIT");
        var loc = url.resolveRecord({
          recordType: "salesorder",
          recordId: null,
          isEditMode: true,
        });
        if (loc.search(/\?/g) >= 0) {
          loc += "&";
        } else {
          loc += "?";
        }
        loc +=
          "cf={0}&custbody_show_table={1}&custbody_booth={2}&entity={3}&custbody_booth_actual_exhibitor={4}".NG_Format(
            currRec.getValue({ fieldId: "customform" }),
            _ShowTableID,
            boothID,
            csLib.settings.AllowMultiBillingParties ? exhib_ID : exhbID,
            actualExhbID
          );
        console.log("triggering redirect with delay (B)");
        setTimeout(function () {
          if (csLib.settings.PrevntBthOrderRedirectAlert) {
            NG.tools.clearFormChanged();
            if (NS.form.isChanged()) NS.form.setChanged(false);
          }
          console.log("initiating redirect (B)");
          window.location = loc;
        }, 1000);
      }
    }
  }

  function selectBooth(_ShowTableID, exhib_ID) {
    var currRec = currentRecord.get();
    var bfilt = new Array(
      ["custrecord_booth_show_table", "anyof", [_ShowTableID]],
      "and",
      ["custrecord_booth_exhibitor", "anyof", [exhib_ID]]
    );
    /*var bcols = new Array(
				new nlobjSearchColumn("custrecord_booth_number", null, null)
			,	new nlobjSearchColumn("custrecord_booth_actual_exhibitor", null, null)
		);*/
    var bcols = new Array(
      search.createColumn({ name: "custrecord_booth_number" }),
      search.createColumn({ name: "custrecord_booth_actual_exhibitor" })
    );
    var bsearch = null;
    try {
      //			bsearch = nlapiSearchRecord("customrecord_show_booths", null, bfilt, bcols);
      bsearch = NG.tools.getSearchResults(
        "customrecord_show_booths",
        bfilt,
        bcols
      );
    } catch (err) {
      window.alert("Search error: [{0}] {1}".NG_Format(err.name, err.message));
    }
    if (bsearch != null) {
      var boothID = null;
      var actualExhbID = null;
      if (bsearch.length == 1) {
        boothID = bsearch[0].id;
        actualExhbID = bsearch[0].getValue({
          name: "custrecord_booth_actual_exhibitor",
        });
      } else if (bsearch.length > 1) {
        var booths = {};
        for (var i = 0; i < bsearch.length; i++) {
          booths[
            bsearch[i]
              .getValue({ name: "custrecord_booth_number" })
              .toUpperCase()
          ] = bsearch[i].id;
        }

        var prompt =
          "Please enter one of the following booth numbers in the field below:\n\n";
        for (var key in booths) {
          prompt += key + "\n";
        }

        var error = false;
        var errmsg = "You have entered an invalid selection.\n\n";
        while (true) {
          var message = "";
          if (error) {
            message = errmsg + prompt;
          } else {
            message = prompt;
          }

          var boothSel = window.prompt(message);
          if (NG.tools.isEmpty(boothSel)) {
            return;
          }

          if (booths[boothSel.toUpperCase()] != null) {
            boothID = booths[boothSel.toUpperCase()];
            for (var i = 0; i < bsearch.length; i++) {
              if (bsearch[i].id == boothID) {
                actualExhbID = bsearch[i].getValue({
                  name: "custrecord_booth_actual_exhibitor",
                });
                break;
              }
            }
            break;
          } else {
            error = true;
          }
        }
      }

      if (
        !NG.tools.isEmpty(boothID) &&
        csLib.settings.PreventAdditionalOrders
      ) {
        selectExhibitor(
          _ShowTableID,
          boothID,
          exhib_ID,
          exhib_ID,
          actualExhbID,
          true
        );
      } else {
        //				nlapiSetFieldValue("custbody_booth", boothID, false);
        //				nlapiSetFieldValue("custbody_booth_actual_exhibitor", actualExhbID, false);
        currRec.setValue({
          fieldId: "custbody_booth",
          value: boothID,
          ignoreFieldChange: true,
        });
        currRec.setValue({
          fieldId: "custbody_booth_actual_exhibitor",
          value: actualExhbID,
          ignoreFieldChange: true,
        });
      }
    } else {
      window.alert(
        "This exhibitor does not appear to have any booths for this show."
      );
    }
  }

  function boothWithoutExhibitor() {
    var currRec = currentRecord.get();
    window.alert("This booth does not yet have an associated exhibitor.");
    //		nlapiSetFieldValue("entity", "", false);
    //		nlapiSetFieldValue("custbody_booth_actual_exhibitor", "", false);
    currRec.setValue({ fieldId: "entity", value: "", ignoreFieldChange: true });
    currRec.setValue({
      fieldId: "custbody_booth_actual_exhibitor",
      value: "",
      ignoreFieldChange: true,
    });
  }

  function triggerBackgroundExhibData(go) {
    var currRec = currentRecord.get();
    console.log("triggerBackgroundExhibData");
    setTimeout(function () {
      if (!go) {
        console.log("triggering existing order function");
        getExistingOrders();
      } else {
        console.log("triggering payment totaling function");
        getPayments();
      }
    }, 2000);
  }

  function triggerBackgroundExhibData_ALT() {
    var currRec = currentRecord.get();
    //		var dataURL = nlapiResolveURL("SUITELET", "customscript_ng_cs_sl_bth_data_init", "customdeploy_ng_cs_sl_bth_data_init_dep");
    var dataURL = url.resolveScript({
      scriptId: "customscript_ng_cs_sl_bth_data_init",
      deploymentId: "customdeploy_ng_cs_sl_bth_data_init_dep",
    });
    var params = {
      sti: currentShowId,
      bi: currRec.getValue({ fieldId: "custbody_booth" }),
      ei: currRec.getValue({ fieldId: "entity" }),
    };
    //		nlapiRequestURL(dataURL, params, { "User-Agent-x" : "SuiteScript-Call" }, handleDataCallback, "POST");
    http.post
      .promise({
        url: dataURL,
        body: params,
        headers: { "User-Agent-x": "SuiteScript-Call" },
      })
      .then(function (response) {
        if (response.code == 200) {
          var data = JSON.parse(response.body);
          _ExistingOrder = data.existingOrder;
          _totalPaid = new Number(data.totalPaid);
        }
      });
  }

  function handleDataCallback(response) {
    if (response.code == 200) {
      var data = JSON.parse(response.body);
      _ExistingOrder = data.existingOrder;
      _totalPaid = new Number(data.totalPaid);
    }
  }

  function getExistingOrders() {
    var currRec = currentRecord.get();
    console.log("getExistingOrders");
    var booth = currRec.getValue({ fieldId: "custbody_booth" });
    var exhib = currRec.getValue({ fieldId: "entity" });

    var filt = new Array(
      ["custbody_show_table", "anyof", [currentShowId]],
      "and",
      ["custbody_booth", "anyof", [booth]],
      "and",
      ["custbody_to_be_deleted", "is", "F"],
      "and",
      ["mainline", "is", "T"]
    );
    if (csLib.settings.AllowMultiBillingParties) {
      filt.push("and", ["entity", "anyof", [exhib]]);
    }
    var sSearch = null;
    console.log("searching for existing orders");
    try {
      //			search = nlapiSearchRecord("salesorder", null, filt, null);
      sSearch = NG.tools.getSearchResults("salesorder", filt, null);
    } catch (err) {}
    if (sSearch != null) {
      _ExistingOrder.check = true;
      _ExistingOrder.id = sSearch[0].id;
    }
    console.log("search complete");
    triggerBackgroundExhibData(true);
  }

  function getPayments() {
    var currRec = currentRecord.get();
    console.log("getPayments");
    var booth = currRec.getValue({ fieldId: "custbody_booth" });
    var exhib = currRec.getValue({ fieldId: "entity" });
    var pSearch = null;
    var tFilt = new Array(
      ["custbody_show_table", "anyof", [currentShowId]],
      "and",
      ["custbody_booth", "anyof", [booth]],
      "and",
      ["mainline", "is", "T"],
      "and",
      ["type", "anyof", ["CustPymt", "CustDep"]]
    );
    if (csLib.settings.AllowMultiBillingParties) {
      tFilt.push("and", ["entity", "anyof", [exhib]]);
    }
    /*var pCols = new Array(
				new nlobjSearchColumn("internalid", null, null)
			,	new nlobjSearchColumn("trandate", null, null)
			,	new nlobjSearchColumn("amount", null, null)
		);*/
    var pCols = new Array(
      search.createColumn({ name: "internalid" }),
      search.createColumn({ name: "trandate" }),
      search.createColumn({ name: "amount" })
    );
    console.log("searching for payments");
    try {
      //			pSearch = nlapiSearchRecord("transaction", null, tFilt, pCols);
      pSearch = NG.tools.getSearchResults("transaction", tFilt, pCols);
    } catch (err) {
      _log.logError(err, "Error encountered searching for related payments");
    }
    if (pSearch != null) {
      for (var p = 0; p < pSearch.length; p++) {
        var payAmount = new Number(pSearch[p].getValue({ name: "amount" }));
        _totalPaid += payAmount;
      }
    }
    console.log("search complete");
    setTimeout(function () {
      getRefunds();
    }, 2000);
  }

  function getRefunds() {
    var currRec = currentRecord.get();
    console.log("getRefunds");
    var booth = currRec.getValue({ fieldId: "custbody_booth" });
    var exhib = currRec.getValue({ fieldId: "entity" });
    var rSearch = null;
    var tFilt = new Array(
      ["custbody_show_table", "anyof", [currentShowId]],
      "and",
      ["custbody_booth", "anyof", [booth]],
      "and",
      ["mainline", "is", "T"]
    );
    if (csLib.settings.AllowMultiBillingParties) {
      tFilt.push("and", ["entity", "anyof", [exhib]]);
    }
    /*var pCols = new Array(
				new nlobjSearchColumn("internalid", null, null)
			,	new nlobjSearchColumn("trandate", null, null)
			,	new nlobjSearchColumn("amount", null, null)
		);*/
    var pCols = new Array(
      search.createColumn({ name: "internalid" }),
      search.createColumn({ name: "trandate" }),
      search.createColumn({ name: "amount" })
    );
    console.log("searching for refunds");
    try {
      //			rSearch = nlapiSearchRecord("customerrefund", null, tFilt, pCols);
      rSearch = NG.tools.getSearchResults("customerrefund", tFilt, pCols);
    } catch (err) {
      _log.logError(err, "Error encountered searching for related refunds");
    }
    if (rSearch != null) {
      for (var r = 0; r < rSearch.length; r++) {
        var refAmount = new Number(rSearch[r].getValue({ name: "amount" }));
        _totalPaid += refAmount;
      }
    }
    console.log("search complete");
  }

  function checkForAdditionalItems() {
    var currRec = currentRecord.get();
    var itemList = new Array();
    for (var l = 1; l <= currRec.getLineCount({ sublistId: "item" }); l++) {
      //			itemList.push(nlapiGetLineItemValue("item", "item", l));
      itemList.push(
        currRec.getSublistValue({ sublistId: "item", fieldId: "item", line: l })
      );
    }
    var itemFilt = new Array(["internalid", "anyof", itemList]);
    /*var itemCols = new Array(
				new nlobjSearchColumn("internalid", "custitem_req_addtnl_items", null)
			,	new nlobjSearchColumn("itemid", "custitem_req_addtnl_items", null)
			,	new nlobjSearchColumn("itemid", null, null)
		);*/
    var itemCols = new Array(
      search.createColumn({
        name: "internalid",
        join: "custitem_req_addtnl_items",
      }),
      search.createColumn({
        name: "itemid",
        join: "custitem_req_addtnl_items",
      }),
      search.createColumn({ name: "itemid" })
    );
    var itemSearch = null;
    try {
      //			itemSearch = nlapiSearchRecord("item", null, itemFilt, itemCols);
      itemSearch = NG.tools.getSearchResults("item", itemFilt, itemCols);
    } catch (err) {
      _log.logError(err, "Error encountered finding required additional items");
    }

    if (itemSearch != null) {
      var itemMap = {};
      for (var i = 0; i < itemSearch.length; i++) {
        if (!NG.tools.isEmpty(itemSearch[i].getValue(itemCols[0]))) {
          if (NG.tools.isEmpty(itemMap[id])) {
            itemMap[itemSearch[i].id] = {};
            itemMap[itemSearch[i].id].items = new Array();
            itemMap[itemSearch[i].id].name = itemSearch[i].getValue(
              itemCols[2]
            );
          }
          itemMap[itemSearch[i].id].items.push({
            id: itemSearch[i].getValue(itemCols[0]),
            name: itemSearch[i].getValue(itemCols[1]),
          });
        }
      }

      if (NG.tools.getKeyCount(itemMap) > 0) {
        for (var key in itemMap) {
          var mainMap = itemMap[key];
          var missingList = new Array();

          for (var i = 0; i < mainMap.items.length; i++) {
            if (!NG.tools.isInArray(mainMap.items[i].id, itemList)) {
              missingList.push(mainMap.items[i].name);
            }
          }

          if (missingList.length > 0) {
            var msg =
              "The following required additional items are missing for item {0}:\n\n{1}".NG_Format(
                mainMap.name,
                missingList.join("\n")
              );
            window.alert(msg);
            return false;
          }
        }
      }
    }

    return true;
  }

  function getOrderTotal() {
    var currRec = currentRecord.get();
    var taxRate = new Number(
      currRec.getValue({ fieldId: "taxrate" }).replace("%", "")
    );
    if (isNaN(taxRate)) {
      taxRate = new Number(0);
    }
    var lines = currRec.getLineCount({ sublistId: "item" });
    var taxable = new Number(0);
    var nontaxable = new Number(0);
    var orderTaxable = currRec.getValue({ fieldId: "istaxable" }) == "T";
    for (var l = 1; l <= lines; l++) {
      //			if (nlapiGetLineItemValue("item", "custcol_cost_is_estimated", l) != "T") {
      if (
        currRec.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_cost_is_estimated",
          line: l,
        }) != "T"
      ) {
        //				if (nlapiGetLineItemValue("item", "istaxable", l) == "T" && orderTaxable) {
        if (
          currRec.getSublistValue({
            sublistId: "item",
            fieldId: "istaxable",
            line: l,
          }) == "T" &&
          orderTaxable
        ) {
          //					taxable += new Number(nlapiGetLineItemValue("item", "amount", l));
          taxable += new Number(
            currRec.getSublistValue({
              sublistId: "item",
              fieldId: "amount",
              line: l,
            })
          );
        } else {
          //					nontaxable += new Number(nlapiGetLineItemValue("item", "amount", l));
          nontaxable += new Number(
            currRec.getSublistValue({
              sublistId: "item",
              fieldId: "amount",
              line: l,
            })
          );
        }
      }
    }
    taxable = _M.roundToHundredths(taxable);
    nontaxable = _M.roundToHundredths(nontaxable);
    var salestax = _M.roundToHundredths(taxable * (taxRate / 100));
    return _M.roundToHundredths(taxable + nontaxable + salestax);
  }

  //////////////////////////////////////////////////////
  //													//
  //////////////////////////////////////////////////////
  //					SECTION 002						//
  //////////////////////////////////////////////////////
  //													//
  //		BEGIN ADD ITEM LINE CLIENT SCRIPTING		//
  //													//
  //////////////////////////////////////////////////////

  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   *
   * DONE ON REFACTOR
   */
  function addLineItemOperations(context) {
    var currRec = context.currentRecord;

    if (_tools.isEmpty(_SHOW_DATA)) {
      getShowData();
    }

    var subsidiary = null;
    if (_UseSubsidiaries) {
      subsidiary = !_tools.isEmpty(currRec.getValue({ fieldId: "subsidiary" }))
        ? currRec.getValue({ fieldId: "subsidiary" })
        : null;
    }

    var itemsListingsRAW = currRec.getValue({
      fieldId: "custpage_ng_items_list",
    });
    if (!NG.tools.isEmpty(itemsListingsRAW)) {
      var itemsListings = JSON.parse(itemsListingsRAW);
      for (var key in itemsListings) {
        csLib.settings[key] = itemsListings[key];
      }
    } else {
      csLib.func.getSelectionItems(subsidiary);
    }

    if (csLib.settings.RetainLastItemCat) {
      var itemCategory = currRec.getValue({
        fieldId: "custbody_item_category",
      });
      if (!NG.tools.isEmpty(itemCategory)) {
        ngItemCategoryDisable(false, currRec);
        currRec.setValue({ fieldId: "custbody_carpet_width", value: 0 });
        currRec.setValue({ fieldId: "custbody_carpet_length", value: 0 });
        currRec.setValue({ fieldId: "custbody_freight_weight", value: 0 });
      } else {
        setTimeout(function () {
          var values = {};
          values["action"] = "get";
          values["user"] = runtime.getCurrentUser();
          var lastItemCat = csLib.func.getLastItemCat(values);
          if (!NG.tools.isEmpty(lastItemCat)) {
            currRec.setValue({
              fieldId: "custbody_item_category",
              value: lastItemCat,
            });
          }
        }, 2000);
      }
    }

    var orderType = currRec.getValue({ fieldId: "custbody_ng_cs_order_type" });
    if (NG.tools.isInArray(orderType, csLib.settings.ShowMgmtOrderTypes)) {
      if (!NG.tools.isEmpty(currentShowId)) {
        var smpl = NG.tools.getLookupFields(
          "customrecord_show",
          currentShowId,
          "custrecord_show_mgmnt_price_lvl"
        );
        if (!NG.tools.isEmpty(smpl)) {
          currRec.setValue({ fieldId: "custbody_price_level", value: smpl });
        }
      }
    }
  }

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   */
  function CS_Field_Changed_02(context) {
    // TODO: Look through add line scripting field changes
    var currRec = context.currentRecord;

    if (context.fieldId == "custbody_item_category") {
      var itemCategory = currRec.getValue({
        fieldId: "custbody_item_category",
      });
      if (!NG.tools.isEmpty(itemCategory)) {
        ngItemCategoryDisable(false, currRec);
        currRec.setValue({ fieldId: "custbody_carpet_width", value: 0 });
        currRec.setValue({ fieldId: "custbody_carpet_length", value: 0 });
        currRec.setValue({ fieldId: "custbody_freight_weight", value: 0 });

        if (csLib.settings.RetainLastItemCat) {
          try {
            var values = {};
            values["action"] = "set";
            values["user"] = runtime.getCurrentUser();
            values["itemcat"] = itemCategory;
            csLib.func.setLastItemCat(values);
          } catch (err) {}
        }
      } else {
        ngItemCategoryDisable(true, currRec);
      }
    }

    if (context.fieldId == "custbody_item") {
      ngClearItemSelectionFields(true, currRec);
      var parentItem = currRec.getValue({ fieldId: "custbody_item" });
      if (!NG.tools.isEmpty(parentItem)) {
        ngEnableSelectionFields(parentItem, currRec);
        ngValidateMinQty(parentItem, currRec);
      } else {
        currRec.setValue({ fieldId: "custbody_carpet_width", value: 0 });
        currRec.setValue({ fieldId: "custbody_carpet_length", value: 0 });
        currRec.setValue({ fieldId: "custbody_freight_weight", value: 0 });
        var itemCategory = currRec.getValue({
          fieldId: "custbody_item_category",
        });
        if (NG.tools.isEmpty(itemCategory)) {
          ngItemCategoryDisable(true, currRec);
        } else {
          ngItemCategoryDisable(false, currRec);
        }
      }
    }

    if (context.fieldId == "custbody_quantity") {
      var parentItem = currRec.getValue({ fieldId: "custbody_item" });
      var currQty = Number(currRec.getValue({ fieldId: "custbody_quantity" }));
      if (!NG.tools.isEmpty(parentItem)) {
        ngValidateMinQty(parentItem, currQty, currRec);
      }
    }

    if (
      NG.tools.isInArray(context.fieldId, [
        "custbody_carpet_width",
        "custbody_carpet_length",
      ])
    ) {
      var parentItem = currRec.getValue({ fieldId: "custbody_item" });
      if (!NG.tools.isEmpty(parentItem)) {
        var sqftItem = NG.tools.isInArray(parentItem, csLib.settings.sqftItems);
        var sqdItem = NG.tools.isInArray(parentItem, csLib.settings.sqdItems);
        if (sqftItem || sqdItem) {
          var w = new Number(
            currRec.getValue({ fieldId: "custbody_carpet_width" })
          );
          var l = new Number(
            currRec.getValue({ fieldId: "custbody_carpet_length" })
          );
          var q = Math.ceil(w * l);
          currRec.setValue({ fieldId: "custbody_quantity", value: q });
        }
      }
    }

    if (context.fieldId == "custbody_ng_cs_order_type") {
      var orderType = currRec.getValue({
        fieldId: "custbody_ng_cs_order_type",
      });
      if (NG.tools.isInArray(orderType, csLib.settings.ShowMgmtOrderTypes)) {
        if (!NG.tools.isEmpty(currentShowId)) {
          var smpl = NG.tools.getLookupFields(
            "customrecord_show",
            currentShowId,
            ["custrecord_show_mgmnt_price_lvl"]
          );
          if (!NG.tools.isEmpty(smpl.custrecord_show_mgmnt_price_lvl)) {
            currRec.setValue({
              fieldId: "custbody_price_level",
              value: smpl.custrecord_show_mgmnt_price_lvl,
            });
          }
        }
      }
    }
  }

  /**
   * Function to be executed when field is slaved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   *
   * @since 2015.2
   */
  function CS_Post_Sourcing_02(context) {}

  /**
   * Function to be executed after sublist is inserted, removed, or edited.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function CS_Sublist_Changed_02(context) {}

  /**
   * Function to be executed after line is selected.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function CS_Line_Init_02(context) {}

  /**
   * Validation function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @returns {boolean} Return true if field is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Field_02(context) {
    return true;
  }

  /**
   * Validation function to be executed when sublist line is committed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Line_02(context) {
    var currRec = context.currentRecord;

    if (context.sublistId == "item") {
      if (!addingToLine01 && !addingToLine02) {
        var item = currRec.getCurrentSublistValue({
          sublistId: "item",
          fieldId: "item",
        });
        if (!NG.tools.isEmpty(item)) {
          var qty = new Number(
            currRec.getCurrentSublistValue({
              sublistId: "item",
              fieldId: "quantity",
            })
          );
          if (!ValidateMaxQty(item, qty)) {
            return false;
          }
        }
      }
    }

    return true;
  }

  /**
   * Validation function to be executed when sublist line is inserted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Insert_02(context) {
    return true;
  }

  /**
   * Validation function to be executed when record is deleted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Delete_02(context) {
    return true;
  }

  /**
   * Validation function to be executed when record is saved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   *
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  function CS_Save_Record_02(context) {
    return true;
  }

  function addItemToNewLine() {
    var currRec = context.currentRecord;

    addingToLine02 = true;
    try {
      if (
        !NG.tools.isEmpty(
          currRec.getCurrentSublistValue({ sublistId: "item", fieldId: "item" })
        )
      ) {
        currRec.commitLine({ sublistId: "item" });
      }
    } catch (err) {}

    if (_TriggerBO) {
      if (NG.tools.isEmpty(currentShowId) && !_NonBoothFormAL) {
        window.alert("Please select a show before adding items to the order.");
        addingToLine02 = false;
        return;
      }
      var booth = currRec.getValue({ fieldId: "custbody_booth" });
      if (NG.tools.isEmpty(booth) && !_NonBoothFormAL) {
        window.alert("Please select a booth before adding items to the order.");
        addingToLine02 = false;
        return;
      }
    }
    var exhibitor = currRec.getValue({ fieldId: "entity" });
    if (NG.tools.isEmpty(exhibitor)) {
      window.alert(
        "Please select an exhibitor before adding items to the order."
      );
      addingToLine02 = false;
      return;
    }

    var parentItem = currRec.getValue({ fieldId: "custbody_item" });
    var itemCategory = currRec.getValue({ fieldId: "custbody_item_category" });
    var color = currRec.getValue({ fieldId: "custbody_color" });
    var size = currRec.getValue({ fieldId: "custbody_size" });
    var orientation = currRec.getValue({ fieldId: "custbody_variant" });
    var cWidth = new Number(
      currRec.getValue({ fieldId: "custbody_carpet_width" })
    );
    var cLength = new Number(
      currRec.getValue({ fieldId: "custbody_carpet_length" })
    );
    var qty = new Number(currRec.getValue({ fieldId: "custbody_quantity" }));
    var fWeight = new Number(0);
    var freightWeight = new Number(
      currRec.getValue({ fieldId: "custbody_freight_weight" })
    );
    var origFreightWeight = freightWeight;
    var dayCalc = currRec.getValue({ fieldId: "custbody_days" });
    var substrate = currRec.getValue({ fieldId: "custbody_substrate" });
    var sqftItem = NG.tools.isInArray(parentItem, csLib.settings.sqftItems);
    var dItem = NG.tools.isInArray(parentItem, csLib.settings.daysItems);
    var durItem = NG.tools.isInArray(parentItem, csLib.settings.durationItems);
    var sqdItem = NG.tools.isInArray(parentItem, csLib.settings.sqdItems);
    var fItem = NG.tools.isInArray(parentItem, csLib.settings.freightItems);
    var cItem = NG.tools.isInArray(parentItem, csLib.settings.colorItems);
    var sItem = NG.tools.isInArray(parentItem, csLib.settings.sizeItems);
    var oItem = NG.tools.isInArray(parentItem, csLib.settings.orientationItems);
    var lItem = NG.tools.isInArray(parentItem, csLib.settings.laborItems);
    var gItem = NG.tools.isInArray(parentItem, csLib.settings.graphicsItems);
    var priceLevel = currRec.getValue({ fieldId: "custbody_price_level" });
    var matrixFilt = new Array();

    if (NG.tools.isEmpty(parentItem)) {
      window.alert("Please choose an item to add, first.");
      return;
    }

    if (
      !sqftItem &&
      !dItem &&
      !fItem &&
      !sqdItem &&
      !lItem &&
      !durItem &&
      (isNaN(new Number(qty)) || !(qty > 0))
    ) {
      window.alert("Please enter a quantity greater than zero.");
      return;
    }

    if (cItem) {
      if (NG.tools.isEmpty(color)) {
        window.alert("Please select a color.");
        return;
      } else {
        // "custitem27", null, "is", color, null
        matrixFilt.push(
          search.createFilter({
            name: "custitem27",
            operator: search.Operator.IS,
            values: color,
          })
        );
      }
    }

    if (sItem) {
      if (NG.tools.isEmpty(size)) {
        window.alert("Please select a size.");
        return;
      } else {
        matrixFilt.push(
          search.createFilter({
            name: "custitem28",
            operator: search.Operator.IS,
            values: size,
          })
        );
      }
    }

    if (oItem) {
      if (NG.tools.isEmpty(orientation)) {
        window.alert("Please select a variant option.");
        return;
      } else {
        // "custitem_orientation", null, "is", orientation, null

        matrixFilt.push(
          search.createFilter({
            name: "custitem_orientation",
            operator: search.Operator.IS,
            values: orientation,
          })
        );
      }
    }

    if (gItem) {
      if (NG.tools.isEmpty(substrate)) {
        window.alert("Please select a material option.");
        return;
      } else {
        // "custitem42", null, "is", substrate, null
        matrixFilt.push(
          search.createFilter({
            name: "custitem42",
            operator: search.Operator.IS,
            values: substrate,
          })
        );
      }
    }

    if (fItem) {
      if (freightWeight <= 0) {
        window.alert("Please enter the freight weight.");
        addingToLine02 = false;
        return;
      } else {
        if (freightWeight < csLib.settings.FreightMinimum) {
          freightWeight = csLib.settings.FreightMinimum;
        }
        fWeight = Math.ceil(freightWeight / 100);
      }
    }

    if (sqdItem || sqftItem) {
      if (
        parseFloat(cWidth) <= 0 ||
        NG.tools.isEmpty(cWidth) ||
        isNaN(cWidth)
      ) {
        window.alert("Please enter a value for width");
        addingToLine02 = false;
        return;
      }
      if (
        parseFloat(cLength) <= 0 ||
        NG.tools.isEmpty(cLength) ||
        isNaN(cLength)
      ) {
        window.alert("Please enter a vlaue for length");
        addingToLine02 = false;
        return;
      }
    }
    if (sqdItem || dItem || durItem) {
      if (
        parseFloat(dayCalc) <= 0 ||
        NG.tools.isEmpty(dayCalc) ||
        isNaN(dayCalc)
      ) {
        window.alert("Please enter a value for days");
        addingToLine02 = false;
        return;
      }
    }

    if (sqdItem && !durItem) {
      qty = Math.ceil(cWidth * cLength * dayCalc);
    } else if (sqftItem && itemCategory != csLib.settings.GraphicsItemCat) {
      qty = Math.ceil(cWidth * cLength);
    } else if (sqftItem && itemCategory == csLib.settings.GraphicsItemCat) {
      qty = Math.ceil((cWidth * cLength) / 144);
    } else if (sqftItem && !durItem) {
      qty = Math.ceil(cWidth * cLength);
    } else if (dItem && !durItem) {
      qty = dayCalc;
    } else if (durItem && (sqftItem || sqdItem)) {
      qty = Math.ceil(cWidth * cLength);
    }

    if (fItem) {
      // if freight item
      var rate = null;

      var maxQty = new Number(
        NG.tools.getLookupFields("item", parentItem, [
          "maximumquantity",
        ]).maximumquantity
      );
      if (
        origFreightWeight < csLib.settings.FreightMinimum &&
        Math.ceil(csLib.settings.FreightMinimum / 100) > maxQty
      ) {
        fWeight = Math.ceil(origFreightWeight / 100);
      }

      if (!ValidateMaxQty(parentItem, fWeight)) {
        return;
      }

      if (!NG.tools.isEmpty(currentShowId)) {
        var ftFilt = new Array(
          ["custrecord_show_freight", "anyof", [currentShowId]],
          "and",
          ["custrecord_freight_item", "anyof", [parentItem]]
        );
        var ftCols = new Array(
          search.createColumn({ name: "custrecord_freight_rate" }),
          search.createColumn({ name: "custrecord_pre_show_rate" }),
          search.createColumn({ name: "custrecord_inbetween_rate" }),
          search.createColumn({ name: "custrecord_on_site_rate" })
        );
        var ftSearch = null;
        try {
          ftSearch = NG.tools.getSearchResults(
            "customrecord_freight_table",
            ftFilt,
            ftCols
          );
        } catch (err) {
          NG.log.logError(err, "Error encountered getting freight table data");
        }
        if (ftSearch != null) {
          var today = NG.time.getSimplifiedDate(new Date()).getTime();
          var advDateT = null;
          try {
            advDateT = _SHOW_DATA["custrecord_adv_ord_date"];
          } catch (err) {}
          var advDateD = !NG.tools.isEmpty(advDateT)
            ? NG.time.stringToDate(advDateT)
            : null;
          var advDate = !NG.tools.isEmpty(advDateD) ? advDateD.getTime() : null;
          var startDateT = csLib.func.getShowDates(currentShowId, "min", false);
          var startDateD = !NG.tools.isEmpty(startDateT)
            ? NG.time.stringToDate(startDateT)
            : null;
          var startDate = !NG.tools.isEmpty(startDateD)
            ? startDateD.getTime()
            : null;
          if (
            advDate != null &&
            today <= advDate &&
            !NG.tools.isEmpty(
              ftSearch[0].getValue({ fieldId: "custrecord_advance_rate" })
            ) &&
            new Number(
              ftSearch[0].getValue({ fieldId: "custrecord_advance_rate" }) ||
                "0"
            ) > 0
          ) {
            priceLevel = "-1";
            rate = new Number(
              ftSearch[0].getValue({ fieldId: "custrecord_advance_rate" })
            ).toFixed(2);
          } else if (
            startDate != null &&
            today >= startDate &&
            !NG.tools.isEmpty(
              ftSearch[0].getValue({ fieldId: "custrecord_on_site_rate" })
            ) &&
            new Number(
              ftSearch[0].getValue({ fieldId: "custrecord_on_site_rate" }) ||
                "0"
            ) > 0
          ) {
            priceLevel = "-1";
            rate = new Number(
              ftSearch[0].getValue({ fieldId: "custrecord_on_site_rate" })
            ).toFixed(2);
          } else if (
            !NG.tools.isEmpty(
              ftSearch[0].getValue({ fieldId: "custrecord_inbetween_rate" })
            ) &&
            new Number(
              ftSearch[0].getValue({ fieldId: "custrecord_inbetween_rate" }) ||
                "0"
            ) > 0
          ) {
            priceLevel = "-1";
            rate = new Number(
              ftSearch[0].getValue({ fieldId: "custrecord_inbetween_rate" })
            ).toFixed(2);
          } else if (
            !NG.tools.isEmpty(
              ftSearch[0].getValue({ fieldId: "custrecord_freight_rate" })
            ) &&
            new Number(
              ftSearch[0].getValue({ fieldId: "custrecord_freight_rate" }) ||
                "0"
            ) > 0
          ) {
            priceLevel = "-1";
            rate = new Number(
              ftSearch[0].getValue({ fieldId: "custrecord_freight_rate" })
            ).toFixed(2);
          } else {
            priceLevel = currRec.getValue({ fieldId: "custbody_price_level" });
          }
        }
      }

      pushToLine(
        {
          item: parentItem,
          qty: fWeight,
          pl: priceLevel,
          fitem: fItem,
          fweight: origFreightWeight,
          rate: rate /*, foItem : foItem*/,
        },
        null,
        currRec
      );
    } else if (
      (lItem || dItem || sqdItem) &&
      !durItem &&
      !((cItem || sItem || oItem) /*|| loItem*/) &&
      _DATE_DATA != null
    ) {
      BuildDaysCalcModal({
        item: parentItem,
        qty: qty,
        pl: priceLevel,
        sqftitem: sqftItem,
        length: cLength,
        width: cWidth,
        itemcat: itemCategory,
        ditem: dItem,
        sqditem: sqdItem,
        days: dayCalc,
        litem: lItem /*, foItem : foItem*/,
      });
    } else if (durItem && _DATE_DATA != null) {
      if (!ValidateMaxQty(parentItem, qty)) {
        return;
      }

      var data = new Array();
      for (var d = 0; d < _DATE_DATA.length; d++) {
        data.push({
          item: parentItem,
          qty: qty,
          pl: priceLevel,
          sqftitem: sqftItem,
          length: cLength,
          width: cWidth,
          itemcat: itemCategory,
          ditem: dItem,
          sqditem: sqdItem,
          days: dayCalc,
          date: _DATE_DATA[d].date,
          dur: durItem /*, foItem : foItem*/,
        });
      }
      pushToLine(data, 0, currRec);
    } else if (
      (sqdItem || sqftItem || dItem || lItem) &&
      !(cItem || sItem || oItem || gItem)
    ) {
      if (!ValidateMaxQty(parentItem, qty)) {
        return;
      }

      pushToLine(
        {
          item: parentItem,
          qty: qty,
          pl: priceLevel,
          sqftitem: sqftItem,
          length: cLength,
          width: cWidth,
          itemcat: itemCategory,
          ditem: dItem,
          sqditem: sqdItem,
          days: dayCalc /*, foItem : foItem*/,
        },
        null,
        currRec
      );
    } else {
      if (parseFloat(qty) <= 0 || NG.tools.isEmpty(qty) || isNaN(qty)) {
        window.alert("Please enter a value for quantity");
        addingToLine02 = false;
        return;
      } else {
        if (cItem || sItem || oItem || gItem) {
          matrixFilt.push(
            new nlobjSearchFilter("parent", null, "is", parentItem, null)
          );
          var matrixSearch = null;
          try {
            matrixSearch = NG.tools.getSearchResults("item", matrixFilt, null);
          } catch (err) {
            NG.log.logError(
              err,
              "Error encountered searching for matrix child item"
            );
          }

          if (matrixSearch != null) {
            parentItem = matrixSearch[0].id;
          } else {
            window.alert("Could not find matrix child item with these options");
            addingToLine02 = false;
            return;
          }
        }

        if ((dItem || sqdItem) && !durItem && _DATE_DATA != null) {
          BuildDaysCalcModal({
            item: parentItem,
            qty: qty,
            pl: priceLevel,
            fitem: fItem,
            fweight: origFreightWeight,
            sqftitem: sqftItem,
            length: cLength,
            width: cWidth,
            itemcat: itemCategory,
            ditem: dItem,
            sqditem: sqdItem,
            days: dayCalc,
            litem: lItem /*, foitem : foItem*/,
          });
        } else {
          if (!ValidateMaxQty(parentItem, qty)) {
            return;
          }

          pushToLine(
            {
              item: parentItem,
              qty: qty,
              pl: priceLevel,
              fitem: fItem,
              fweight: origFreightWeight,
              sqftitem: sqftItem,
              length: cLength,
              width: cWidth,
              itemcat: itemCategory,
              ditem: dItem,
              sqditem: sqdItem,
              days: dayCalc,
              litem: lItem /*, foitem : foItem*/,
            },
            null,
            currRec
          );
        }
      }
    }
  }

  function ValidateMaxQty(item, qty) {
    if (csLib.settings.EnforceItemMaxQuantity) {
      var maxQty = new Number(
        NG.tools.getLookupFields(
          "item",
          item,
          "maximumquantity"
        ).maximumquantity
      );
      if (maxQty > 0 && qty > maxQty) {
        window.alert(
          "You have exceeded this item's maximum ordering quantity: {0}".NG_Format(
            maxQty
          )
        );
        return false;
      }
    }
    return true;
  }

  function ngItemCategoryDisable(isSet, currRec) {
    currRec.getField({ fieldId: "custbody_item" }).isDisabled = isSet;
    currRec.getField({ fieldId: "custbody_price_level" }).isDisabled = true;
    currRec.getField({ fieldId: "custbody_quantity" }).isDisabled = true;
    currRec.getField({ fieldId: "custbody_size" }).isDisabled = true;
    currRec.getField({ fieldId: "custbody_color" }).isDisabled = true;
    currRec.getField({ fieldId: "custbody_variant" }).isDisabled = true;
    currRec.getField({ fieldId: "custbody_carpet_width" }).isDisabled = true;
    currRec.getField({ fieldId: "custbody_carpet_length" }).isDisabled = true;
    currRec.getField({ fieldId: "custbody_days" }).isDisabled = true;
    currRec.getField({ fieldId: "custbody_freight_weight" }).isDisabled = true;
    currRec.getField({ fieldId: "custbody_substrate" }).isDisabled = true;
  }

  function ngItemDisable(isSet, currRec) {
    currRec.getField({ fieldId: "custbody_price_level" }).isDisabled = isSet;
    currRec.getField({ fieldId: "custbody_size" }).isDisabled = isSet;
    currRec.getField({ fieldId: "custbody_quantity" }).isDisabled = isSet;
    currRec.getField({ fieldId: "custbody_color" }).isDisabled = isSet;
    currRec.getField({ fieldId: "custbody_variant" }).isDisabled = isSet;
    currRec.getField({ fieldId: "custbody_carpet_width" }).isDisabled = isSet;
    currRec.getField({ fieldId: "custbody_carpet_length" }).isDisabled = isSet;
    currRec.getField({ fieldId: "custbody_days" }).isDisabled = isSet;
    currRec.getField({ fieldId: "custbody_freight_weight" }).isDisabled = isSet;
    currRec.getField({ fieldId: "custbody_substrate" }).isDisabled = isSet;
  }

  function ngClearItemSelectionFields(bool, currRec) {
    if (!bool) {
      currRec.setValue({ fieldId: "custbody_item_category", value: "" });
    }

    currRec.setValue({
      fieldId: "custbody_quantity",
      value: 0,
      ignoreFieldChange: true,
    });
    currRec.setValue({
      fieldId: "custbody_carpet_width",
      value: 0,
      ignoreFieldChange: true,
    });
    currRec.setValue({
      fieldId: "custbody_carpet_length",
      value: 0,
      ignoreFieldChange: true,
    });
    currRec.setValue({ fieldId: "custbody_days", value: 0 });
    currRec.setValue({ fieldId: "custbody_freight_weight", value: 0 });
    currRec.setValue({ fieldId: "custbody_timeframe", value: "" });
    _DATE_DATA = null;
  }

  function ngEnableSelectionFields(parentItem, currRec) {
    var sqftItem = NG.tools.isInArray(parentItem, csLib.settings.sqftItems);
    var dItem = NG.tools.isInArray(parentItem, csLib.settings.daysItems);
    var durItem = NG.tools.isInArray(parentItem, csLib.settings.durationItems);
    var sqdItem = NG.tools.isInArray(parentItem, csLib.settings.sqdItems);
    var fItem = NG.tools.isInArray(parentItem, csLib.settings.freightItems);
    var cItem = NG.tools.isInArray(parentItem, csLib.settings.colorItems);
    var sItem = NG.tools.isInArray(parentItem, csLib.settings.sizeItems);
    var oItem = NG.tools.isInArray(parentItem, csLib.settings.orientationItems);
    var lItem = NG.tools.isInArray(parentItem, csLib.settings.laborItems);
    var gItem = NG.tools.isInArray(parentItem, csLib.settings.graphicsItems);

    ngItemDisable(true, currRec);
    currRec.getField({ fieldId: "custbody_price_level" }).isDisabled = false;

    if (fItem) {
      currRec.getField({
        fieldId: "custbody_freight_weight",
      }).isDisabled = false;
    } else if (sqdItem) {
      currRec.getField({ fieldId: "custbody_carpet_width" }).isDisabled = false;
      currRec.getField({
        fieldId: "custbody_carpet_length",
      }).isDisabled = false;
      ngGetShowDays(csLib.settings.DCalcDateTypes, currRec);
      getBoothDims(currRec);
    } else if (sqftItem) {
      currRec.getField({ fieldId: "custbody_carpet_width" }).isDisabled = false;
      currRec.getField({
        fieldId: "custbody_carpet_length",
      }).isDisabled = false;
      getBoothDims(currRec);
      if (durItem) {
        ngGetShowDays([csLib.settings.DefaultShowDateType], currRec);
      }
    } else if (dItem || durItem) {
      currRec.getField({ fieldId: "custbody_quantity" }).isDisabled = false;
      currRec.setValue({
        fieldId: "custbody_quantity",
        value: 1,
        ignoreFieldChange: true,
      });
      if (dItem) {
        ngGetShowDays(csLib.settings.DCalcDateTypes, currRec);
      } else {
        ngGetShowDays([csLib.settings.DefaultShowDateType], currRec);
      }
    } else {
      currRec.getField({ fieldId: "custbody_quantity" }).isDisabled = false;
      currRec.setValue({
        fieldId: "custbody_quantity",
        value: 1,
        ignoreFieldChange: true,
      });
    }

    if (cItem) {
      currRec.getField({ fieldId: "custbody_color" }).isDisabled = false;
    }
    if (sItem) {
      currRec.getField({ fieldId: "custbody_size" }).isDisabled = false;
    }
    if (oItem) {
      currRec.getField({ fieldId: "custbody_variant" }).isDisabled = false;
    }
    if (lItem) {
      currRec.getField({ fieldId: "custbody_days" }).isDisabled = false;
      console.log("getting date data");
      ngGetShowDays(csLib.settings.LaborDateTypes, currRec);
    }
    if (gItem) {
      currRec.getField({ fieldId: "custbody_substrate" }).isDisabled = false;
    }
  }

  function getBoothDims(currRec) {
    var boothID = currRec.getValue({ fieldId: "custbody_booth" });
    if (!NG.tools.isEmpty(boothID)) {
      var boothData = NG.tools.getLookupFields(
        "customrecord_show_booths",
        boothID,
        ["custrecord_booth_length", "custrecord_booth_width"]
      );
      currRec.setValue({
        fieldId: "custbody_carpet_length",
        value: new Number(boothData["custrecord_booth_length"]),
      });
      currRec.setValue({
        fieldId: "custbody_carpet_width",
        value: new Number(boothData["custrecord_booth_width"]),
      });
    } else {
      currRec.setValue({ fieldId: "custbody_carpet_length", value: 0 });
      currRec.setValue({ fieldId: "custbody_carpet_width", value: 0 });
    }
  }

  function ngValidateMinQty(parentItem, currQty, currRec) {
    currQty = currQty || new Number(0);
    var minQ = new Number(
      NG.tools.getLookupFields("item", parentItem, [
        "minimumquantity",
      ]).minimumquantity
    );
    var booth = currRec.getValue({ fieldId: "custbody_booth" });
    if (isNaN(minQ)) {
      minQ = new Number(0);
    }
    if (minQ > 1 && currQty < minQ) {
      currRec.setValue({
        fieldId: "custbody_quantity",
        value: minQ,
        ignoreFieldChange: true,
      });
    } else if (
      (NG.tools.isInArray(parentItem, csLib.settings.sqftItems) ||
        NG.tools.isInArray(parentItem, csLib.settings.sqdItems)) &&
      !NG.tools.isEmpty(booth)
    ) {
      var boothDims = NG.tools.getLookupFields(
        "customrecord_show_booths",
        booth,
        ["custrecord_booth_length", "custrecord_booth_width"]
      );
      var boothL = new Number(boothDims.custrecord_booth_length);
      var boothW = new Number(boothDims.custrecord_booth_length);
      if (isNaN(boothL) || boothL < 10) {
        boothL = new Number(10);
      }
      if (isNaN(boothW) || boothW < 10) {
        boothW = new Number(10);
      }
      minQ = boothL * boothW;
      if (currQty < minQ) {
        currRec.setValue({
          fieldId: "custbody_quantity",
          value: minQ,
          ignoreFieldChange: true,
        });
      }
    }
  }

  function ngGetShowDays(type, currRec) {
    _DATE_DATA = csLib.func.getShowDates(
      currentShowId,
      null,
      true,
      type || null
    );
    currRec.setValue({
      fieldId: "custbody_days",
      value: !NG.tools.isEmpty(_DATE_DATA) ? _DATE_DATA.length : 0,
      ignoreFieldChange: true,
    });
  }

  function pushToLine(data, pos, currRec) {
    if (NG.tools.isEmpty(pos)) {
      console.log(data);
      currRec.selectNewLine({ sublistId: "item" });
      currRec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "item",
        value: data.item,
        ignoreFieldChange: false,
        forceSyncSourcing: true,
      });

      setTimeout(function () {
        var itemDescr = currRec.getCurrentSublistValue({
          sublistId: "item",
          fieldId: "description",
        });
        var adtlDescr = "";
        currRec.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "quantity",
          value: data.qty,
        });
        if (!NG.tools.isEmpty(data.pl)) {
          currRec.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "price",
            value: data.pl,
          });
        }
        if (data.fitem) {
          if (data.pl == "-1") {
            currRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "rate",
              value: data.rate,
            });
          }
        }
        if (data.fitem || data.foitem) {
          itemDescr = "{0}{1}LB".NG_Format(
            !NG.tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
            data.fweight
          );
          console.log("updating item description with freight data", itemDescr);
        }
        if ((data.ditem || data.sqditem) && !data.dur) {
          itemDescr = "{0}{1} Days".NG_Format(
            !NG.tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
            data.days
          );

          if (data.sqditem && !NG.tools.isEmpty(data.date)) {
            adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
              !NG.tools.isEmpty(adtlDescr) ? "\n" : "",
              data.width,
              data.length
            );
            itemDescr = "{0}Service Date: {1}".NG_Format(
              !NG.tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
              data.date
            );
            currRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "custcol_labor_date",
              value: data.date,
            });
          } else if (data.sqditem) {
            adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
              !NG.tools.isEmpty(adtlDescr) ? "\n" : "",
              data.width,
              data.length
            );
          } else if (!NG.tools.isEmpty(data.date)) {
            itemDescr = "{0}Service Date: {1}".NG_Format(
              !NG.tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
              data.date
            );
            currRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "custcol_labor_date",
              value: data.date,
            });
          }
        }
        if (data.dur && !NG.tools.isEmpty(data.date)) {
          itemDescr = "{0}Service Date: {1}".NG_Format(
            !NG.tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
            data.date
          );
          currRec.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "custcol_labor_date",
            value: data.date,
          });
          if (data.sqditem) {
            adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
              !NG.tools.isEmpty(adtlDescr) ? "\n" : "",
              data.width,
              data.length
            );
          }
        }
        if (
          data.sqftitem &&
          !data.sqditem &&
          data.itemcat != csLib.settings.GraphicsItemCat
        ) {
          adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
            !NG.tools.isEmpty(adtlDescr) ? "\n" : "",
            data.width,
            data.length
          );
        } else if (
          data.sqftitem &&
          !data.sqditem &&
          data.itemcat == csLib.settings.GraphicsItemCat
        ) {
          adtlDescr += '{0}Size: {1}" X {2}"'.NG_Format(
            !NG.tools.isEmpty(adtlDescr) ? "\n" : "",
            data.width,
            data.length
          );
        }
        currRec.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "description",
          value: itemDescr,
        });
        console.log("final item description", itemDescr);
        currRec.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_custom_carpet_size",
          value: adtlDescr,
        });
        setTimeout(function () {
          currRec.commitLine({ sublistId: "item" });
          addingToLine02 = false;

          setTimeout(function () {
            var itemField = document.getElementById("custbody_item_display");
            if (itemField != null) {
              if (isNaN(itemField.length)) {
                itemField.focus();
              } else {
                if (itemField.length > 0) {
                  itemField[0].focus();
                }
              }
            }
          }, 500);
        }, 500);
      }, 500);

      currRec.setValue({ fieldId: "custbody_item", value: "" });
    } else {
      currRec.selectNewLine({ sublistId: "item" });
      currRec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "item",
        value: data[pos].item,
        ignoreFieldChange: false,
        forceSyncSourcing: true,
      });

      setTimeout(function () {
        var itemDescr = currRec.getCurrentSublistValue({
          sublistId: "item",
          fieldId: "description",
        });
        var adtlDescr = "";
        currRec.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "quantity",
          value: data[pos].qty,
        });
        if (!NG.tools.isEmpty(data[pos].pl)) {
          currRec.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "price",
            value: data[pos].pl,
          });
        }
        if (data[pos].fitem) {
          if (data[pos].pl == "-1") {
            currRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "rate",
              value: data[pos].rate,
            });
          }
        }
        if (data[pos].fitem) {
          itemDescr = "{0}{1}LB".NG_Format(
            !NG.tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
            data[pos].fweight
          );
        }
        if ((data[pos].ditem || data[pos].sqditem) && !data[pos].dur) {
          itemDescr = "{0}{1} Days".NG_Format(
            !NG.tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
            data[pos].days
          );

          if (data[pos].sqditem && !NG.tools.isEmpty(data[pos].date)) {
            adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
              !NG.tools.isEmpty(adtlDescr) ? "\n" : "",
              data[pos].width,
              data[pos].length
            );
            itemDescr = "{0}Service Date: {1}".NG_Format(
              !NG.tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
              data[pos].date
            );
            currRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "custcol_labor_date",
              value: data[pos].date,
            });
          } else if (data[pos].sqditem) {
            adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
              !NG.tools.isEmpty(adtlDescr) ? "\n" : "",
              data[pos].width,
              data[pos].length
            );
          } else if (!NG.tools.isEmpty(data[pos].date)) {
            itemDescr = "{0}Service Date: {1}".NG_Format(
              !NG.tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
              data[pos].date
            );
            currRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "custcol_labor_date",
              value: data[pos].date,
            });
          }
        }
        if (data[pos].dur && !NG.tools.isEmpty(data[pos].date)) {
          itemDescr = "{0}Service Date: {1}".NG_Format(
            !NG.tools.isEmpty(itemDescr) ? "{0}\n".NG_Format(itemDescr) : "",
            data[pos].date
          );
          currRec.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "custcol_labor_date",
            value: data[pos].date,
          });
          if (data[pos].sqditem) {
            adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
              !NG.tools.isEmpty(adtlDescr) ? "\n" : "",
              data[pos].width,
              data[pos].length
            );
          }
        }
        if (
          data[pos].sqftitem &&
          !data[pos].sqditem &&
          data[pos].itemcat != csLib.settings.GraphicsItemCat
        ) {
          adtlDescr += "{0}Size: {1}' X {2}'".NG_Format(
            !NG.tools.isEmpty(adtlDescr) ? "\n" : "",
            data[pos].width,
            data[pos].length
          );
        } else if (
          data[pos].sqftitem &&
          !data[pos].sqditem &&
          data[pos].itemcat == csLib.settings.GraphicsItemCat
        ) {
          adtlDescr += '{0}Size: {1}" X {2}"'.NG_Format(
            !NG.tools.isEmpty(adtlDescr) ? "\n" : "",
            data[pos].width,
            data[pos].length
          );
        }
        currRec.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "description",
          value: itemDescr,
        });
        currRec.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_custom_carpet_size",
          value: adtlDescr,
        });
        setTimeout(function () {
          currRec.commitLine({ sublistId: "item" });
          if (Math.round(pos + 1) < data.length) {
            pushToLine(data, Math.round(pos + 1), currRec);
          } else {
            addingToLine02 = false;

            setTimeout(function () {
              var itemField = document.getElementById("custbody_item_display");
              if (itemField != null) {
                if (isNaN(itemField.length)) {
                  itemField.focus();
                } else {
                  if (itemField.length > 0) {
                    itemField[0].focus();
                  }
                }
              }
            }, 500);
          }
        }, 500);
      }, 500);

      if (pos == 0) {
        currRec.setValue({ fieldId: "custbody_item", value: "" });
      }
    }
  }

  function BuildDaysCalcModal(data) {
    /*if (nlapiGetContext().getExecutionContext() != "userinterface") {
			return;
		}*/
    if (runtime.executionContext === runtime.ContextType.USER_INTERFACE) {
      return;
    }
    var calcTable = document.getElementById("selector_table");
    if (calcTable != null) {
      _MODAL_ITEM = data.item;
      var dates = new Array();
      if (NG.tools.isEmpty(_SHOW_DATA)) {
        getShowData();
      }
      if (!NG.tools.isEmpty(currentShowId)) {
        if (data.litem) {
          dates = csLib.func.getShowDates(
            currentShowId,
            null,
            false,
            csLib.settings.LaborDateTypes
          );
        } else {
          dates = csLib.func.getShowDates(
            currentShowId,
            null,
            false,
            csLib.settings.DCalcDateTypes
          );
        }
      }

      try {
        var s = NG.tools.getSearchResults(
          "item",
          ["internalid", "anyof", data.item],
          null
        );
        var r = record.load({ type: s[0].recordType, id: data.item });
        var optCols = NG.tools.getMultiSelect("itemoptions", r, false, null);
        var hasSup = false;
        if (!NG.tools.isEmpty(optCols) && optCols.length > 0) {
          for (var o = 0; o < optCols.length; o++) {
            if (optCols[o] == "CUSTCOL_LABOR_DATE") {
              _CALC_WORKER_COUNT = true;
            } else if (optCols[o] == "CUSTCOL_LABOR_SUP_REQUIRED") {
              hasSup = true;
            }
          }
        }
        data["supitem"] = hasSup;
      } catch (err) {
        NG.log.logError(
          err,
          "Error encountered checking if item calcs for workers"
        );
        console.log(
          err,
          "Error encountered checking if item calcs for workers"
        );
      }

      /////////////////////
      // build table header
      /////////////////////
      var hr = document.createElement("tr");
      hr.id = "daysitemrow_0";

      var th1 = document.createElement("th");
      th1.style.cssText = "text-align:center; width: 2%;";
      var tp1 = document.createElement("p");
      tp1.style.cssText = "width: 100%; font-weight: bold;";
      tp1.innerHTML = "Day";
      th1.appendChild(tp1);
      hr.appendChild(th1);

      var th0 = document.createElement("th");
      th0.style.cssText = "text-align:center; width: 2%;";
      var tp0 = document.createElement("p");
      tp0.style.cssText = "width: 100%; font-weight: bold;";
      tp0.innerHTML = "Add";
      th0.appendChild(tp0);
      hr.appendChild(th0);

      var th2 = document.createElement("th");
      th2.style.cssText = "text-align:center; width: 2%;";
      var tp2 = document.createElement("p");
      tp2.style.cssText = "width: 100%; font-weight: bold;";
      tp2.innerHTML = "Service Date";
      th2.appendChild(tp2);
      hr.appendChild(th2);

      var th3 = document.createElement("th");
      th3.style.cssText = "text-align:center; width: 2%;";
      var tp3 = document.createElement("p");
      tp3.style.cssText = "width: 100%; font-weight: bold;";
      tp3.innerHTML = "Start Time";
      th3.appendChild(tp3);
      hr.appendChild(th3);

      var th4 = document.createElement("th");
      th4.style.cssText = "text-align:center; width: 2%;";
      var tp4 = document.createElement("p");
      tp4.style.cssText = "width: 100%; font-weight: bold;";
      tp4.innerHTML = "End Time";
      th4.appendChild(tp4);
      hr.appendChild(th4);

      if (_CALC_WORKER_COUNT && data.litem) {
        var th7 = document.createElement("th");
        th7.style.cssText = "text-align:center; width: 2%;";
        var tp7 = document.createElement("p");
        tp7.style.cssText = "width: 100%; font-weight: bold;";
        tp7.innerHTML = "Number of<br />Workers";
        th7.appendChild(tp7);
        hr.appendChild(th7);
      }

      var th5 = document.createElement("th");
      th5.style.cssText = "text-align:center; width: 2%;";
      var tp5 = document.createElement("p");
      tp5.style.cssText = "width: 100%; font-weight: bold;";
      tp5.innerHTML = "Duration";
      th5.appendChild(tp5);
      hr.appendChild(th5);

      var th6 = document.createElement("th");
      th6.style.cssText = "text-align:center; width: 2%;";
      var tp6 = document.createElement("p");
      tp6.style.cssText = "width: 100%; font-weight: bold;";
      tp6.innerHTML = "Supervision<br />Required?";
      th6.appendChild(tp6);
      hr.appendChild(th6);

      calcTable.appendChild(hr);
      /////////////////////
      // table header done
      /////////////////////

      for (var d = 0; d < dates.length; d++) {
        var l = Math.round(d + 1).toFixed(0);
        var tr = document.createElement("tr");
        tr.id = "daysitemrow_" + l;

        var td1 = document.createElement("td");
        td1.style.cssText = "text-align:center;";
        var p1 = document.createElement("p");
        p1.style.cssText = "width: 100%;";
        p1.innerHTML = l;
        td1.appendChild(p1);
        tr.appendChild(td1);

        var td0 = document.createElement("td");
        td0.style.cssText = "text-align:center; vertical-align: middle;";
        var selectLineInput = document.createElement("input");
        selectLineInput.type = "checkbox";
        selectLineInput.className = "daysitemselect";
        selectLineInput.name = "daysitemselect_" + l;
        selectLineInput.id = "daysitemselect_" + l;
        selectLineInput.checked = true;
        td0.appendChild(selectLineInput);
        tr.appendChild(td0);

        var td2 = document.createElement("td");
        var dateInput = document.createElement("input");
        dateInput.type = "text";
        dateInput.className = "daysitemdate";
        dateInput.name = "daysitemdate_" + l;
        dateInput.id = "daysitemdate_" + l;
        if (dates != null) {
          dateInput.value = dates[d].date;
        }
        td2.appendChild(dateInput);
        tr.appendChild(td2);

        var td3 = document.createElement("td");
        td3.style.cssText = "text-align:center;";
        if (data.litem) {
          var startTimeInput = document.createElement("input");
          startTimeInput.type = "text";
          startTimeInput.className = "daysitemtimestart";
          startTimeInput.name = "daysitemtimestart_" + l;
          startTimeInput.id = "daysitemtimestart_" + l;
          startTimeInput.addEventListener("change", onTimeChange);
          td3.appendChild(startTimeInput);
        } else {
          var p3 = document.createElement("p");
          p3.style.cssText = "width: 100%;";
          p3.innerHTML = "&nbsp;";
          td3.appendChild(p3);
        }
        tr.appendChild(td3);

        var td4 = document.createElement("td");
        td4.style.cssText = "text-align:center;";
        if (data.litem) {
          var endTimeInput = document.createElement("input");
          endTimeInput.type = "text";
          endTimeInput.className = "daysitemtimeend";
          endTimeInput.name = "daysitemtimeend_" + l;
          endTimeInput.id = "daysitemtimeend_" + l;
          endTimeInput.addEventListener("change", onTimeChange);
          td4.appendChild(endTimeInput);
        } else {
          var p4 = document.createElement("p");
          p4.style.cssText = "width: 100%;";
          p4.innerHTML = "&nbsp;";
          td4.appendChild(p4);
        }
        tr.appendChild(td4);

        if (_CALC_WORKER_COUNT && data.litem) {
          var td7 = document.createElement("td");
          td7.style.cssText = "text-align:center;";
          var workNumInput = document.createElement("input");
          workNumInput.type = "text";
          workNumInput.className = "workercount";
          workNumInput.name = "workercount_" + l;
          workNumInput.id = "workercount_" + l;
          workNumInput.value = 1;
          td7.appendChild(workNumInput);
          tr.appendChild(td7);
        }

        var td5 = document.createElement("td");
        td5.style.cssText = "text-align:center;";
        var p5 = document.createElement("p");
        p5.style.cssText = "width: 100%;";
        p5.className = "daysitemduration";
        p5.id = "daysitemduration_" + l;
        p5.innerHTML = "&nbsp;";
        td5.appendChild(p5);
        tr.appendChild(td5);

        var td6 = document.createElement("td");
        td6.style.cssText = "text-align:center; vertical-align: middle;";
        if (data.supitem) {
          var supervisorInput = document.createElement("input");
          supervisorInput.type = "checkbox";
          supervisorInput.className = "daysitemsupervisor";
          supervisorInput.name = "daysitemsupervisor_" + l;
          supervisorInput.id = "daysitemsupervisor_" + l;
          td6.appendChild(supervisorInput);
        } else {
          var p6 = document.createElement("p");
          p6.style.cssText = "width: 100%;";
          p6.innerHTML = "&nbsp;";
          td6.appendChild(p6);
        }
        tr.appendChild(td6);

        calcTable.appendChild(tr);
      }

      var overlay = document.getElementById("ng_cs_calc_popup");
      if (overlay != null) {
        overlay.className = "ng_cs_overlay ng_cs_overlay_a";
      }
    }
  }

  function cancelDaysCalc() {
    _MODAL_ITEM = null;
    closeDaysCalc();
  }

  function processDaysCalc() {
    var currRec = currentRecord.get();

    var parentItem = _MODAL_ITEM;
    _MODAL_ITEM = null;
    var length = new Number(
      currRec.getValue({ fieldId: "custbody_carpet_length" })
    );
    var width = new Number(
      currRec.getValue({ fieldId: "custbody_carpet_width" })
    );
    try {
      var pl = currRec.getValue({ fieldId: "custbody_price_level" }) || "1";
      var itemRate = new Number(0);
      try {
        if (pl == "1") {
          itemRate = new Number(
            NG.tools.getLookupFields("item", parentItem, "baseprice").baseprice
          );
        } else {
          var ratesSearch = NG.tools.getSearchResults(
            "item",
            ["internalid", "is", parentItem],
            [search.createColumn({ name: "otherprices" })]
          );
          if (ratesSearch != null) {
            itemRate = new Number(
              ratesSearch[0].getValue({ name: "price{0}".NG_Format(pl) })
            );
            if (isNaN(itemRate) || itemRate == 0) {
              itemRate = new Number(
                NG.tools.getLookupFields(
                  "item",
                  parentItem,
                  "baseprice"
                ).baseprice
              );
            }
          }
        }
      } catch (err) {}
      var calcTable = document.getElementById("selector_table");
      if (calcTable != null) {
        var lineNodes = calcTable.childNodes;
        if (lineNodes.length > 1) {
          var lineData = new Array();
          for (var d = 1; d < lineNodes.length; d++) {
            var l = Math.round(d).toFixed(0);
            if (
              document.getElementById("daysitemselect_" + l) != null &&
              document.getElementById("daysitemselect_" + l).checked
            ) {
              var dateID = "daysitemdate_{0}".NG_Format(l);
              var startID = "daysitemtimestart_{0}".NG_Format(l);
              var endID = "daysitemtimeend_{0}".NG_Format(l);
              var supervisorID = "daysitemsupervisor_{0}".NG_Format(l);
              var workCntID = "workercount_{0}".NG_Format(l);
              var dateInit = null;
              var startInit = null;
              var endInit = null;
              var workCnt = new Number(1);
              var supervisorInit = null;
              var hasSupervisor = false;
              if (document.getElementById(dateID) != null) {
                dateInit = document.getElementById(dateID).value;
                try {
                  //							dateInit = nlapiDateToString(nlapiStringToDate(dateInit), "date");
                  dataInit = NG.time.getSimplifiedDate(dateInit);
                } catch (err) {
                  window.alert(
                    "An invalid date value has been entered on line {0}. Please enter dates in the format m/d/yyyy.".NG_Format(
                      l
                    )
                  );
                  setFieldFocus(dateID);
                  return;
                }
              }
              if (document.getElementById(startID) != null) {
                startInit = document.getElementById(startID).value;
                if (
                  NG.tools.isInArray(parentItem, csLib.settings.laborItems) &&
                  NG.tools.isEmpty(startInit)
                ) {
                  window.alert(
                    "Start time entry on line {0} is blank. Time entries should be in the format HH:MM am/pm. Please verify your entry.".NG_Format(
                      l
                    )
                  );
                  setFieldFocus(startID);
                  return;
                }
                if (!_TIME_REG.test(startInit)) {
                  window.alert(
                    "Start time entry on line {0} is invalid. Time entries should be in the format HH:MM am/pm. Please verify your entry.".NG_Format(
                      l
                    )
                  );
                  setFieldFocus(startID);
                  return;
                }
                if (!NG.tools.isEmpty(startInit)) {
                  startInit = startInit.toLowerCase();
                  if (startInit.match(/am/) && !startInit.match(/ am/)) {
                    startInit = startInit.replace("am", " am");
                  }
                  if (startInit.match(/pm/) && !startInit.match(/ pm/)) {
                    startInit = startInit.replace("pm", " pm");
                  }
                }
              }
              if (document.getElementById(endID) != null) {
                endInit = document.getElementById(endID).value;
                if (
                  NG.tools.isInArray(parentItem, csLib.settings.laborItems) &&
                  NG.tools.isEmpty(endInit)
                ) {
                  window.alert(
                    "End time entry on line {0} is blank. Time entries should be in the format HH:MM am/pm. Please verify your entry.".NG_Format(
                      l
                    )
                  );
                  setFieldFocus(endID);
                  return;
                }
                if (!_TIME_REG.test(endInit)) {
                  window.alert(
                    "End time entry on line {0} is invalid. Time entries should be in the format HH:MM am/pm. Please verify your entry.".NG_Format(
                      l
                    )
                  );
                  setFieldFocus(endID);
                  return;
                }
                if (!NG.tools.isEmpty(endInit)) {
                  endInit = endInit.toLowerCase();
                  if (endInit.match(/am/) && !endInit.match(/ am/)) {
                    endInit = endInit.replace("am", " am");
                  }
                  if (endInit.match(/pm/) && !endInit.match(/ pm/)) {
                    endInit = endInit.replace("pm", " pm");
                  }
                }
              }
              if (document.getElementById(supervisorID) != null) {
                hasSupervisor = true;
                supervisorInit = document.getElementById(supervisorID).checked;
              }
              if (
                _CALC_WORKER_COUNT &&
                NG.tools.isInArray(parentItem, csLib.settings.laborItems)
              ) {
                if (document.getElementById(workCntID) != null) {
                  var workCntInit = document.getElementById(workCntID).value;

                  if (NG.tools.isEmpty(workCntInit)) {
                    window.alert(
                      "You must enter the number of workers required on line {0}.".NG_Format(
                        l
                      )
                    );
                    setFieldFocus(workCntID);
                    return;
                  }

                  workCnt = new Number(workCntInit);
                  if (isNaN(workCnt) || workCnt < 1) {
                    window.alert(
                      "The number of workers requested on line {0} is invalid. Please verify your entry.".NG_Format(
                        l
                      )
                    );
                    setFieldFocus(workCntID);
                    return;
                  }
                }
              }

              if (!NG.tools.isEmpty(dateInit)) {
                var data = {
                  itemid: parentItem,
                  cols: new Array(),
                };
                //								var date = nlapiStringToDate(dateInit);
                var date = format.parse({
                  value: dateInit,
                  type: format.Type.DATE,
                });
                var lText = "";
                var sRate = new Number(0);
                if (NG.tools.isInArray(parentItem, csLib.settings.laborItems)) {
                  var lr = processLaborRates(
                    date,
                    startInit,
                    endInit,
                    itemRate,
                    workCnt
                  );
                  if (lr == null) {
                    return;
                  }
                  data["qty"] = lr["q"];
                  data["pl"] = "-1";
                  data["rate"] = lr["r"];
                  data["amount"] = NG.M.roundToHundredths(lr["q"] * lr["r"]);
                  lText = lr["t"];
                  if (!NG.tools.isEmpty(lText)) {
                    lText += "\nSupervision Required: {0}".NG_Format(
                      supervisorInit ? "Yes" : "No"
                    );
                  }
                  sRate = lr["s"];
                } else {
                  var q = new Number(1);
                  var sqft = new Number(1);
                  if (
                    NG.tools.isInArray(parentItem, csLib.settings.sqftItems) ||
                    NG.tools.isInArray(parentItem, csLib.settings.sqdItems)
                  ) {
                    if (!NG.tools.isEmpty(width) && !NG.tools.isEmpty(length)) {
                      try {
                        sqft = NG.M.roundToHundredths(width * length);
                      } catch (e) {}
                    }
                    if (isNaN(sqft)) {
                      sqft = new Number(1);
                    }
                  } else {
                    q = new Number(
                      currRec.getValue({ fieldId: "custbody_quantity" })
                    );
                    if (isNaN(q) || q == 0) {
                      q = new Number(1);
                    }
                  }
                  var qty = NG.M.roundToHundredths(sqft * q);
                  data["qty"] = qty;
                  data["pl"] = pl;
                  if (hasSupervisor) {
                    sRate = NG.M.roundToHundredths(itemRate * qty);
                  }
                }

                var itemDescr = "";
                data.cols.push({
                  field: "custcol_labor_date",
                  value: NG.time.dateToString(date),
                });
                data.cols.push({
                  field: "custcol_labor_sup_required",
                  value: supervisorInit ? "T" : "F",
                });
                if (
                  !NG.tools.isEmpty(width) &&
                  !NG.tools.isEmpty(length) &&
                  width != 0 &&
                  length != 0
                ) {
                  data.cols.push({
                    field: "custcol_custom_carpet_size",
                    value: "Size: {0}' X {1}'".NG_Format(length, width),
                  });
                }
                if (!NG.tools.isEmpty(startInit)) {
                  if (NG.tools.isEmpty(lText))
                    itemDescr = "{0}Start Time: {1}".NG_Format(
                      !NG.tools.isEmpty(itemDescr)
                        ? "{0}\n".NG_Format(itemDescr)
                        : "",
                      startInit
                    );
                  data.cols.push({
                    field: "custcol_labor_time",
                    value: startInit,
                  });
                }
                if (!NG.tools.isEmpty(endInit)) {
                  if (NG.tools.isEmpty(lText))
                    itemDescr = "{0}End Time: {1}".NG_Format(
                      !NG.tools.isEmpty(itemDescr)
                        ? "{0}\n".NG_Format(itemDescr)
                        : "",
                      endInit
                    );
                  data.cols.push({
                    field: "custcol_labor_end_time",
                    value: endInit,
                  });
                }
                itemDescr = "{0}{1}".NG_Format(
                  !NG.tools.isEmpty(itemDescr)
                    ? "{0}\n".NG_Format(itemDescr)
                    : "",
                  lText
                );
                if (
                  !NG.tools.isEmpty(date) &&
                  itemDescr.search("Service") < 0
                ) {
                  itemDescr = "{0}Service Date: {1}".NG_Format(
                    !NG.tools.isEmpty(itemDescr)
                      ? "{0}\n".NG_Format(itemDescr)
                      : "",
                    NG.time.dateToString(date)
                  );
                }
                if (
                  !NG.tools.isInArray(parentItem, csLib.settings.laborItems) &&
                  hasSupervisor
                ) {
                  itemDescr = "{0}Supervision Required: {1}".NG_Format(
                    !NG.tools.isEmpty(itemDescr)
                      ? "{0}\n".NG_Format(itemDescr)
                      : "",
                    supervisorInit ? "Yes" : "No"
                  );
                }
                data.cols.push({ field: "description", value: itemDescr });
                lineData.push(data);
                if (_CALC_WORKER_COUNT) {
                  data.cols.push({
                    field: "custcol_labor_workers",
                    value: Math.round(workCnt).toFixed(0),
                  });
                }
                if (
                  supervisorInit &&
                  sRate > 0 &&
                  !NG.tools.isEmpty(csLib.settings.SupervisorItem)
                ) {
                  var sup = {
                    itemid: csLib.settings.SupervisorItem,
                    qty: 1,
                    pl: "-1",
                    rate: sRate,
                    amount: sRate,
                    cols: new Array(),
                  };
                  if (
                    !NG.tools.isEmpty(startInit) &&
                    !NG.tools.isEmpty(endInit)
                  ) {
                    sup.cols.push({
                      field: "description",
                      value: "For '{0}' on date {1} from {2} to {3}".NG_Format(
                        NG.tools.getLookupFields("item", parentItem, "itemid")
                          .itemid,
                        NG.time.dateToString(date),
                        startInit,
                        endInit
                      ),
                    });
                    sup.cols.push(
                      {
                        field: "custcol_labor_date",
                        value: NG.time.dateToString(date),
                      },
                      { field: "custcol_labor_time", value: startInit },
                      { field: "custcol_labor_end_time", value: endInit },
                      { field: "custcol_ng_cs_labor_item", value: parentItem }
                    );
                  } else {
                    sup.cols.push({
                      field: "description",
                      value: "For '{0}' on date {1}".NG_Format(
                        NG.tools.getLookupFields("item", parentItem, "itemid")
                          .itemid,
                        NG.time.dateToString(date)
                      ),
                    });
                  }
                  lineData.push(sup);
                }
              }
            }
          }

          if (lineData.length > 0) {
            pushDaysCalcToLine(lineData, 0);
          } else {
            if (
              window.confirm(
                "There are no dates selected for adding to the order. Is this correct?"
              )
            ) {
              console.log("no dates selected");
              closeDaysCalc();
              currRec.setValue({ fieldId: "custbody_item", value: "" });
            } else {
              return;
            }
          }
        } else {
          console.log("selector table has no lines");
          closeDaysCalc();
          currRec.setValue({ fieldId: "custbody_item", value: "" });
          return;
        }
      } else {
        console.log("selector table not found");
        closeDaysCalc();
        currRec.setValue({ fieldId: "custbody_item", value: "" });
        return;
      }
    } catch (err) {
      console.log(err);
      window.alert("There was a problem processing your request");
    }
  }

  function closeDaysCalc() {
    var overlay = document.getElementById("ng_cs_calc_popup");
    if (overlay != null) {
      overlay.className = "ng_cs_overlay ng_cs_overlay_b";
    }
    setTimeout(function () {
      var calcTable = document.getElementById("selector_table");
      if (calcTable != null) {
        while (calcTable.firstChild) {
          calcTable.removeChild(calcTable.firstChild);
        }
      }
    }, 1000);
    _CALC_WORKER_COUNT = false;
  }

  function processLaborRates(dateObj, start, end, itemRate, workerCount) {
    workerCount = workerCount || 1;
    var workerText = _CALC_WORKER_COUNT
      ? "\nWorkers Requested: {0}".NG_Format(workerCount.toFixed(0))
      : "";
    //		var date = nlapiDateToString(dateObj, "date");
    //		var st = nlapiStringToDate("{0} {1}".NG_Format(date,start)).getTime();
    //		var et = nlapiStringToDate("{0} {1}".NG_Format(date,end)).getTime();
    var date = NG.time.dateToString(dateObj);
    var st = NG.time.stringToDate("{0} {1}".NG_Format(date, start)).getTime();
    var et = NG.time.stringToDate("{0} {1}".NG_Format(date, end)).getTime();
    var filt = new Array(
      ["custrecord_ng_cs_labor_show", "anyof", [currentShowId]],
      "and",
      ["custrecord_ng_cs_labor_date", "on", date],
      "and",
      ["isinactive", "is", "F"]
    );
    //		var cols = new Array(
    //				new nlobjSearchColumn("custrecord_ng_cs_labor_start", null, null)
    //			,	new nlobjSearchColumn("custrecord_ng_cs_labor_end", null, null)
    //			,	new nlobjSearchColumn("custrecord_ng_cs_labor_type", null, null)
    //			,	new nlobjSearchColumn("custrecord_ng_cs_laborNG.Multiplier", null, null)
    //			,	new nlobjSearchColumn("custrecord_ng_cs_supervisorNG.Markup", null, null)
    //		);
    //		cols[0].setSort();
    var cols = new Array(
      search.createColumn({
        name: "custrecord_ng_cs_labor_start",
        sort: search.Sort.ASC,
      }),
      search.createColumn({ name: "custrecord_ng_cs_labor_end" }),
      search.createColumn({ name: "custrecord_ng_cs_labor_type" }),
      search.createColumn({ name: "custrecord_ng_cs_laborNG.Multiplier" }),
      search.createColumn({ name: "custrecord_ng_cs_supervisorNG.Markup" })
    );

    //		var search = nlapiSearchRecord("customrecord_ng_cs_show_labor_schedule", null, filt, cols);
    var sSearch = NG.tools.getSearchResults(
      "customrecord_ng_cs_show_labor_schedule",
      filt,
      cols
    );
    if (sSearch != null) {
      var matched = false;
      var labor = {};
      for (var i = 0; i < sSearch.length; i++) {
        //				var test_st = nlapiStringToDate("{0} {1}".NG_Format(date, search[i].getValue("custrecord_ng_cs_labor_start"))).getTime();
        //				var test_et = nlapiStringToDate("{0} {1}".NG_Format(date, search[i].getValue("custrecord_ng_cs_labor_end"))).getTime();
        var test_st = NG.time
          .stringToDate(
            "{0} {1}".NG_Format(
              date,
              sSearch[i].getValue({ name: "custrecord_ng_cs_labor_start" })
            )
          )
          .getTime();
        var test_et = NG.time
          .stringToDate(
            "{0} {1}".NG_Format(
              date,
              sSearch[i].getValue({ name: "custrecord_ng_cs_labor_end" })
            )
          )
          .getTime();

        if (st >= test_st && st <= test_et && et >= test_st && et <= test_et) {
          var lType = sSearch[i].getValue({
            name: "custrecord_ng_cs_labor_type",
          });
          var lTypeText = sSearch[i].getText({
            name: "custrecord_ng_cs_labor_type",
          });
          var mult = new Number(
            sSearch[i].getValue({ name: "custrecord_ng_cs_laborNG.Multiplier" })
          );
          var supMrkUp = NG.M.roundToHundredths(
            new Number(
              sSearch[i]
                .getValue({ name: "custrecord_ng_cs_supervisorNG.Markup" })
                .replace("%", "")
            ) / 100
          );
          var data = {
            lType: lTypeText,
            date: date,
            start: start,
            end: end,
            mult: mult,
            superV: supMrkUp,
          };

          labor[lType] = data;
          matched = true;
          break;
        }
      }
      if (!matched) {
        for (var i = 0; i < sSearch.length; i++) {
          //					var test_st = nlapiStringToDate("{0} {1}".NG_Format(date, search[i].getValue("custrecord_ng_cs_labor_start"))).getTime();
          //					var test_et = nlapiStringToDate("{0} {1}".NG_Format(date, search[i].getValue("custrecord_ng_cs_labor_end"))).getTime();
          //					var lType = search[i].getId();
          var test_st = NG.time
            .stringToDate(
              "{0} {1}".NG_Format(
                date,
                sSearch[i].getValue({ name: "custrecord_ng_cs_labor_start" })
              )
            )
            .getTime();
          var test_et = NG.time
            .stringToDate(
              "{0} {1}".NG_Format(
                date,
                sSearch[i].getValue({ name: "custrecord_ng_cs_labor_end" })
              )
            )
            .getTime();
          var lType = sSearch[i].id;

          var data = null;
          if (NG.tools.isEmpty(labor[lType])) {
            var lTypeText = sSearch[i].getText({
              name: "custrecord_ng_cs_labor_type",
            });
            var mult = new Number(
              sSearch[i].getValue({
                name: "custrecord_ng_cs_laborNG.Multiplier",
              })
            );
            var supMrkUp = NG.M.roundToHundredths(
              new Number(
                sSearch[i]
                  .getValue({ name: "custrecord_ng_cs_supervisorNG.Markup" })
                  .replace("%", "")
              ) / 100
            );
            data = {
              lType: lTypeText,
              date: date,
              mult: mult,
              superV: supMrkUp,
            };
          } else {
            data = labor[lType];
          }

          if (st < test_st && et >= test_st && et <= test_et) {
            data.start = sSearch[i].getValue({
              name: "custrecord_ng_cs_labor_start",
            });
            data.end = end;
          } else if (st >= test_st && st <= test_et && et > test_et) {
            data.start = start;
            data.end = sSearch[i].getValue({
              name: "custrecord_ng_cs_labor_end",
            });
          } else if (st < test_st && et > test_et) {
            data.start = sSearch[i].getValue({
              name: "custrecord_ng_cs_labor_start",
            });
            data.end = sSearch[i].getValue({
              name: "custrecord_ng_cs_labor_end",
            });
          } else {
            continue;
          }

          labor[lType] = data;
        }

        var tCount = new Number(0);
        var mCount = new Number(0);

        for (var key in labor) {
          tCount++;
          if (
            !NG.tools.isEmpty(labor[key].start) &&
            !NG.tools.isEmpty(labor[key].end)
          ) {
            mCount++;
          }
        }

        if (
          (tCount > 0 || mCount > 0) &&
          Math.round(tCount) == Math.round(mCount)
        ) {
          matched = true;
        }
      }

      if (!matched) {
        window.alert(
          "There do not appear to be any labor rates scheduled for this show on {0} that completely match the time frame of {1} to {2}".NG_Format(
            date,
            start,
            end
          )
        );
        return null;
      } else {
        var dur = getDuration(st, et);
        var response = {};
        if (NG.tools.getKeyCount(labor) == 1) {
          var k = null;
          for (var key in labor) {
            k = labor[key];
            break;
          }
          var rate = NG.M.roundToHundredths(k["mult"] * itemRate * workerCount);
          var text =
            "Service Date: {0}\nStart: {1} || End: {2}\nTotal Duration: {3}:{4}{9}\nLabor: {5} for {6}:{7} @ ${8}".NG_Format(
              date,
              start,
              end,
              dur["h"],
              dur["m"].toFixed(0).paddingLeft("00"),
              k["lType"],
              dur["h"],
              dur["m"].toFixed(0).paddingLeft("00"),
              rate.toFixed(2),
              workerText
            );
          response = {
            q: dur["d"],
            r: rate,
            t: text,
            s:
              k["superV"] > 0
                ? NG.M.roundToHundredths(k["superV"] * (rate * dur["d"]))
                : 0,
          };
        } else {
          var lText = "";
          var total = new Number(0);
          var superV = new Number(0);
          for (var key in labor) {
            var k = labor[key];
            var rate = NG.M.roundToHundredths(
              k["mult"] * itemRate * workerCount
            );
            //						var d = getDuration(nlapiStringToDate("{0} {1}".NG_Format(date,k['start'])).getTime(), nlapiStringToDate("{0} {1}".NG_Format(date,k['end'])).getTime());
            var d = getDuration(
              NG.time
                .stringToDate("{0} {1}".NG_Format(date, k["start"]))
                .getTime(),
              NG.time
                .stringToDate("{0} {1}".NG_Format(date, k["end"]))
                .getTime()
            );
            var amount = NG.M.roundToHundredths(rate * d["d"]);
            total += amount;
            if (amount != 0) {
              lText += "\nLabor: {0} for {1}:{2} @ ${3}".NG_Format(
                k["lType"],
                d["h"],
                d["m"].toFixed(0).paddingLeft("00"),
                rate.toFixed(2)
              );
            }
            if (k["superV"] > superV) {
              superV = k["superV"];
            }
          }
          total = NG.M.roundToHundredths(total);
          var r = NG.M.roundToHundredths(total / dur["d"]);
          var text =
            "Service Date: {0}\nStart: {1} || End: {2}\nTotal Duration: {3}:{4}{6}{5}".NG_Format(
              date,
              start,
              end,
              dur["h"],
              dur["m"].toFixed(0).paddingLeft("00"),
              lText,
              workerText
            );
          response = {
            q: dur["d"],
            r: r,
            t: text,
            s: superV > 0 ? NG.M.roundToHundredths(superV * total) : 0,
          };
        }

        return response;
      }
    } else {
      window.alert(
        "There do not appear to be any labor rates scheduled for this show on {0}. Please review the show's labor scheduling.".NG_Format(
          date
        )
      );
      return null;
    }
  }

  function getDuration(s, e) {
    var duration = NG.M.roundToHundredths((e - s) / (1000 * 60 * 60));
    var hours = Math.floor(duration);
    var minutes = (duration * 60) % 60;

    return { d: duration, h: hours, m: minutes };
  }

  function pushDaysCalcToLine(data, i) {
    var currRec = currentRecord.get();

    var line = data[i];
    var itemID = line.itemid;
    var qty = line.qty;
    var cols = line.cols;
    var pl = line.pl;
    var rate = line.rate;
    var amount = line.amount;

    //		nlapiSelectNewLineItem("item");
    //		nlapiSetCurrentLineItemValue("item", "item", itemID, true, true);

    currRec.selectNewLine({ sublistId: "item" });
    currRec.setCurrentSublistValue({
      sublistId: "item",
      fieldId: "item",
      value: itemID,
      ignoreFieldChange: false,
      forceSyncSourcing: true,
    });

    setTimeout(function () {
      currRec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "quantity",
        value: qty,
      });
      if (!NG.tools.isEmpty(pl)) {
        //				nlapiSetCurrentLineItemValue("item", "price", pl);
        currRec.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "price",
          value: pl,
        });
      }
      if (pl == "-1") {
        //				nlapiSetCurrentLineItemValue("item", "rate", rate);
        //				nlapiSetCurrentLineItemValue("item", "amount", amount);
        currRec.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "rate",
          value: rate,
        });
        currRec.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "amount",
          value: amount,
        });
      }

      if (cols != null) {
        for (var c = 0; c < cols.length; c++) {
          //					nlapiSetCurrentLineItemValue("item", cols[c].field, cols[c].value, true, true);
          currRec.setCurrentSublistValue({
            sublistId: "item",
            fieldId: cols[c].field,
            value: cols[c].value,
            ignoreFieldChange: false,
            forceSyncSourcing: true,
          });
        }
      }

      setTimeout(function () {
        //					nlapiCommitLineItem("item");
        currRec.commitLine({ sublistId: "item" });

        if (i + 1 < data.length) {
          pushDaysCalcToLine(data, i + 1);
        } else {
          addingToLine02 = false;
          closeDaysCalc();
          //						nlapiSetFieldValue("custbody_item", "");
          currRec.setValue({ fieldId: "custbody_item", value: "" });
        }

        return;
      }, 250);
    }, 250);
  }

  function matrixItem(parentItem) {
    var itemFilt = new Array(["matrix", "is", "T"], "and", [
      "isinactive",
      "is",
      "F",
    ]);
    var itemCols = new Array(
      search.createColumn({ name: csLib.settings.fields.item.color }),
      search.createColumn({ name: csLib.settings.fields.item.size })
    );
    if (csLib.settings.EnableFreightOptsOption) {
      itemCols.push(
        search.createColumn({ name: csLib.settings.fields.item.freightOpts })
      );
    }
    if (csLib.settings.EnableOrientationOption) {
      itemCols.push(
        search.createColumn({ name: csLib.settings.fields.item.orientation })
      );
    }
    if (csLib.settings.EnableGraphicOption) {
      itemCols.push(
        search.createColumn({ name: csLib.settings.fields.item.graphics })
      );
    }
    var itemSearch = null;
    try {
      itemSearch = NG.tools.getSearchResults("item", itemFilt, itemCols);
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered verifying selected item is a matrix item"
      );
    }

    return itemSearch;
  }

  function parseTime(initTime) {
    if (NG.tools.isEmpty(initTime)) {
      return null;
    }
    var d = new Date(2000, 0, 1, 0, 0, 0, 0);
    var time = initTime.match(/(\d+)(:(\d\d))?\s*(p?)/i);
    d.setHours(
      parseInt(time[1]) + (parseInt(time[1]) < 12 && time[4] ? 12 : 0)
    );
    d.setMinutes(parseInt(time[3]) || 0);
    return d;
  }

  function setDuration(line) {
    try {
      var startID = "daysitemtimestart_{0}".NG_Format(line);
      var endID = "daysitemtimeend_{0}".NG_Format(line);
      var durID = "daysitemduration_{0}".NG_Format(line);
      var start = document.getElementById(startID).value;
      var end = document.getElementById(endID).value;
      if (
        NG.tools.isInArray(end, ["12:00am", "12:00 am", "12:00AM", "12:00 AM"])
      ) {
        window.alert("End time cannot be midnight. Please change to 11:59 pm");
        setFieldFocus(endID);
        return;
      }
      if (
        NG.tools.isEmpty(start) ||
        NG.tools.isEmpty(end) ||
        !_TIME_REG.test(start) ||
        !_TIME_REG.test(end)
      ) {
        return;
      }

      var tStart = parseTime(start);
      var tEnd = parseTime(end);
      if (tEnd.getTime() < tStart.getTime()) {
        window.alert("End time cannot be before start time");
        setFieldFocus(startID);
        return;
      } else if (tStart.getTime() == tEnd.getTime()) {
        window.alert("Start time and end time cannot be the same");
        setFieldFocus(endID);
        return;
      }
      var duration = NG.M.roundToHundredths((tEnd - tStart) / (1000 * 60 * 60));
      document.getElementById(durID).innerText = duration.toFixed(2);
    } catch (err) {
      window.alert(
        "There was a problem reading the time entry. Please verify that your time entries are in the format hh:mm am/pm"
      );
      console.log(err);
    }
  }

  function onTimeChange(e) {
    var e_id = e.target.id;
    var time = document.getElementById(e_id).value;
    if (_TIME_REG.test(time)) {
      setDuration(e_id.split("_")[1]);
    } else if (!NG.tools.isEmpty(time)) {
      window.alert(
        "Time entries should be in the format HH:MM am/pm. Please verify your entry."
      );
      e.target.focus();
    }
  }

  function setFieldFocus(id) {
    var field = document.getElementById(id);
    if (field != null) {
      field.focus();
    }
  }

  //////////////////////////////////////////////////////
  //					SECTION 003						//
  //////////////////////////////////////////////////////
  //													//
  //		BEGIN SHOW MANAGEMENT CLIENT SCRIPTING		//
  //													//
  //////////////////////////////////////////////////////

  /**
   * Function to be executed after page is initialized.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.mode - The mode in which the record is being accessed (create, copy, or edit)
   *
   * @since 2015.2
   *
   * DONE ON REFACTOR
   */
  function showManagementOperations(context) {
    var currRec = context.currentRecord;

    if (!_TriggerBO) {
      var exhbID = currRec.getValue({ fieldId: "entity" });

      if (!NG.tools.isEmpty(currentShowId)) {
        setPriceLevel();
      }

      if (NG.tools.isEmpty(currentShowId) && csLib.settings.RetainLastShow) {
        var values = {};
        values["action"] = "get";
        values["user"] = runtime.getCurrentUser().id;
        var lastShow = csLib.func.getLastShow(values);
        currentShowId = lastShow;
        if (!NG.tools.isEmpty(lastShow)) {
          currRec.setValue({
            fieldId: "custbody_show_table",
            value: currentShowId,
            ignoreFieldChange: true,
          });
        }
      }

      getShowData();
      if (!NG.tools.isEmpty(_SHOW_DATA)) {
        setTimeout(function () {
          setPriceLevel(exhbID);
        }, 1000);

        if (_UseCnclPct) {
          var rawPct = _SHOW_DATA["custrecord_cancellation_pct"];
          var pct =
            rawPct.search("%") >= 0
              ? new Number(rawPct.replace("%", ""))
              : new Number(rawPct);
          _CancellationPct = pct / 100;
        }
        // DEPRECATED: Force tax set on record from event
      }

      if (evType == "edit") {
        //				startingLineCount = nlapiGetLineItemCount("item");
        startingLineCount = currRec.getLineCount({ sublistId: "item" });

        if (_UseCnclPct) {
          //					_CurrentRecord = nlapiLoadRecord(nlapiGetRecordType(), nlapiGetRecordId());
          _CurrentRecord = record.load({ type: currRec.type, id: currRec.id });
          currentShowId =
            currentShowId ||
            currRec.getValue({ fieldId: "custbody_show_table" });
          if (!NG.tools.isEmpty(currentShowId)) {
            _ApplyCnclCharge = isOnOrAfterMoveInDate(currentShowId);
          }
        }

        if (
          _UseScriptedPaymentForm &&
          NG.tools.isEmpty(
            _NoPaymentPrompt ? currRec.getValue({ fieldId: "terms" }) : null
          ) &&
          !NG.tools.isInArray(_CurrentRole, _NoPromptRoles)
        ) {
          triggerBackgroundExhibData(true);
        }
      }

      if (
        !NG.tools.isEmpty(currentShowId) &&
        !NG.tools.isEmpty(_SHOW_DATA) &&
        NG.tools.isEmpty(
          currRec.getValue({ fieldId: "custbody_cseg_ng_cs_job" })
        ) &&
        _UseCSJobs
      ) {
        currRec.setValue({
          fieldId: "custbody_cseg_ng_cs_job",
          value: _SHOW_DATA["custrecord_show_job"],
          ignoreFieldChange: true,
        });
        currRec.setValue({
          fieldId: "class",
          value: _SHOW_DATA["custrecord_fin_show"],
          ignoreFieldChange: true,
        });
      } else if (!NG.tools.isEmpty(currentShowId)) {
        currRec.setValue({
          fieldId: "class",
          value: _SHOW_DATA["custrecord_fin_show"],
          ignoreFieldChange: true,
        });
      }

      var showField =
        document.forms["main_form"].elements["inpt_custbody_show"];
      if (showField != null) {
        if (isNaN(showField.length)) {
          showField.focus();
        } else {
          if (showField.length > 0) {
            showField[0].focus();
          }
        }
      }
    }
  }

  /**
   * Function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @since 2015.2
   *
   * DONE ON REFACTOR
   */
  function showManagementFieldsOnChange(context) {
    var currRec = context.currentRecord;
    // SAME EXACT FUNCTION FROM BOOTH ORDER FIELD CHANGE
    if (!_TriggerBO) {
      if (context.fieldId == "custbody_show_table") {
        currentShowId = currRec.getValue({ fieldId: "custbody_show_table" });
        if (!NG.tools.isEmpty(currentShowId)) {
          getShowData();

          if (!NG.tools.isEmpty(_SHOW_DATA)) {
            if (_UseCSJobs) {
              currRec.setValue({
                fieldId: "custbody_cseg_ng_cs_job",
                value: _SHOW_DATA["custrecord_show_job"],
              });
              currRec.setValue({
                fieldId: "class",
                value: _SHOW_DATA["custrecord_fin_show"],
              });
            } else {
              currRec.setValue({
                fieldId: "class",
                value: _SHOW_DATA["custrecord_fin_show"],
              });
            }
            if (_UseSubsidiaries) {
              if (!NG.tools.isEmpty(_SHOW_DATA["custrecord_show_subsidiary"])) {
                currRec.setValue({
                  fieldId: "subsidiary",
                  value: _SHOW_DATA["custrecord_show_subsidiary"],
                  ignoreFieldChange: true,
                });
              } else {
                if (
                  NG.tools.isEmpty(currRec.getValue({ fieldId: "subsidiary" }))
                ) {
                  currRec.setValue({
                    fieldId: "subsidiary",
                    value: "1",
                    ignoreFieldChange: true,
                  });
                }
              }
            }

            if (_UseLocations) {
              currRec.setValue({
                fieldId: "location",
                value: _SHOW_DATA["custrecord_show_venue"],
                ignoreFieldChange: true,
              });
            }
            //						nlapiDisableField("entity", false);
            currRec.getField({ fieldId: "entity" }).isDisabled = false;
            var showTableFields = new Array(
              "custrecord_adv_ord_date",
              "custrecord_adv_price_level",
              "custrecord_std_price_level"
            );
            if (_UseTaxRate) {
              showTableFields.push("custrecord_tax_rate");
            }
            if (_UseCnclPct) {
              showTableFields.push("custrecord_cancellation_pct");
            }
            _AdvDate = NG.time.stringToDate(
              _SHOW_DATA["custrecord_adv_ord_date"]
            );
            currRec.setValue({
              fieldId: "custbody_advanced_order_date",
              value: _SHOW_DATA["custrecord_adv_ord_date"],
              ignoreFieldChange: true,
            });
            // DEPRECATED: Force tax set on record from event

            if (_UseCnclPct) {
              var rawPct = _SHOW_DATA["custrecord_cancellation_pct"];
              var pct =
                rawPct.search("%") >= 0
                  ? new Number(rawPct.replace("%", ""))
                  : new Number(rawPct);
              _CancellationPct = pct / 100;
            }

            if (_UseCnclPct) {
              _ApplyCnclCharge = isOnOrAfterMoveInDate(currentShowId);
            }

            currRec.setValue({
              fieldId: "department",
              value: csLib.settings.DefaultShowMgmtDepartment,
              ignoreFieldChange: true,
            });
            setTimeout(function () {
              currRec.setValue({
                fieldId: "custbody_ng_cs_order_type",
                value: csLib.settings.DefaultShowMgmtOrderType,
                ignoreFieldChange: true,
              });
            }, 2500);
          }

          if (csLib.settings.RetainLastShow) {
            var values = {};
            values["action"] = "set";
            values["user"] = runtime.getCurrentUser().id;
            values["show"] = _ShowID;
            csLib.func.setLastShow(values);
          }
        } else {
          currRec.setValue({ fieldId: "class", value: "" });
          if (_UseCSJobs) {
            currRec.setValue({ fieldId: "custbody_cseg_ng_cs_job", value: "" });
          }
          //					nlapiDisableField("entity", true);
          currRec.getField({ fieldId: "entity" }).isDisabled = false;
          currentShowId = null;
          _SHOW_DATA = null;
        }
      }

      if (
        _tools.isInArray(context.fieldId, [
          "custbody_show_table",
          "custbody_ng_cs_order_type",
        ])
      ) {
        setPriceLevel();
      }
    }
  }

  /**
   * Function to be executed when field is slaved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   *
   * @since 2015.2
   */
  function CS_Post_Sourcing_03(context) {
    var currRec = context.currentRecord;

    if (!_TriggerBO) {
      if (context.fieldId == "entity") {
        setTimeout(function () {
          if (NG.tools.isEmpty(gpl)) {
            gpl = currRec.getValue({ fieldId: "custbody_price_level" });
          }
          // setOrderTax();
        }, 250);
      }

      if (context.sublistId == "item") {
        if (context.fieldId == "item") {
          if (!addingToLine01 && !addingToLine02) {
            if (
              !NG.tools.isEmpty(
                currRec.getValue({ fieldId: "custbody_price_level" })
              )
            ) {
              gpl = currRec.getValue({ fieldId: "custbody_price_level" });
            }
            if (!NG.tools.isEmpty(gpl)) {
              //							nlapiSetCurrentLineItemValue(type, "price", gpl);
              currRec.setCurrentSublistValue({
                sublistId: "item",
                fieldId: "price",
                value: gpl,
              });
            }
          }
        }
      }
    }
  }

  /**
   * Function to be executed after sublist is inserted, removed, or edited.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function CS_Sublist_Changed_03(context) {}

  /**
   * Function to be executed after line is selected.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @since 2015.2
   */
  function CS_Line_Init_03(context) {}

  /**
   * Validation function to be executed when field is changed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   * @param {string} context.fieldId - Field name
   * @param {number} context.lineNum - Line number. Will be undefined if not a sublist or matrix field
   * @param {number} context.columnNum - Line number. Will be undefined if not a matrix field
   *
   * @returns {boolean} Return true if field is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Field_03(context) {
    return true;
  }

  /**
   * Validation function to be executed when sublist line is committed.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Line_03(context) {
    return true;
  }

  /**
   * Validation function to be executed when sublist line is inserted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Insert_03(context) {
    return true;
  }

  /**
   * Validation function to be executed when record is deleted.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.sublistId - Sublist name
   *
   * @returns {boolean} Return true if sublist line is valid
   *
   * @since 2015.2
   */
  function CS_Validate_Delete_03(context) {
    return true;
  }

  /**
   * Validation function to be executed when record is saved.
   *
   * @param {Object} context
   * @param {Record} context.currentRecord - Current form record
   *
   * @returns {boolean} Return true if record is valid
   *
   * @since 2015.2
   */
  function CS_Save_Record_03(context) {
    return true;
  }

  //////////////////////////////////////////////////////
  //													//
  //////////////////////////////////////////////////////
  //					SCRIPT END						//
  //////////////////////////////////////////////////////
  //													//
  //////////////////////////////////////////////////////

  return {
    pageInit: pageInit,
    fieldChanged: fieldChanged,
    postSourcing: postSourcing,
    sublistChanged: sublistChanged,
    lineInit: lineInit,
    validateField: validateField,
    validateLine: validateLine,
    validateInsert: validateInsert,
    validateDelete: validateDelete,
    saveRecord: saveRecord,
    addItemToNewLine: addItemToNewLine,
  };
});
