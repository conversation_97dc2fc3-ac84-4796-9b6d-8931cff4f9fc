/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig ./amdUserEventConfig.json
 *
 * @deprecated refer to ng_cses_ue_deposit.js
 */
define([
  "N/query",
  "N/record",
  "N/runtime",
  "N/format",
  "N/search",
  "NG/tools/two",
  "NG/client/two",
  "settings",
], /**
 * @param{format} format
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{Object} NG
 * @param{Object} csLib
 * @param{Object} settings
 * @param{() => Settings} settings.useSettings
 */ (query, record, runtime, format, search, NG, csLib, settings) => {
  var CS_SETTINGS = null;

  /**
   * Function definition to be triggered before record is loaded.
   *
   * @param {Object} context
   * @param {Record} context.newRecord - New record
   * @param {string} context.type - Trigger type
   * @param {Form} context.form - Current form
   * @Since 2015.2
   */
  function beforeLoad(context) {
    csLib.settings = csLib.trigger();
    csLib.func.hideCSJobField(context.form);
    const depositRec = context.newRecord;
    let salesOrder = depositRec.getValue("salesorder");

    switch (context.type) {
      case "create":
        addSwipeGroupField();
        runConvFeeWork();
        if (salesOrder) doSalesOrderEventWork(context, salesOrder);
        break;
      case "copy":
        addSwipeGroupField();
        runConvFeeWork();
        break;
      case "edit":
        addSwipeGroupField();
        runConvFeeWork();
        break;
      case "delete":
        break;
      default:
        log.audit({ title: "No default action available", details: "" });
        break;
    }

    function addSwipeGroupField() {
      context.form.addFieldGroup({
        id: "swipe_group",
        label: "Credit Card Swipe",
      });
      context.form.addField({
        id: "custpage_entry",
        type: "text",
        label: "CC Swipe Entry",
        container: "swipe_group",
      });
    }

    function runConvFeeWork() {
      let useConvFee = csLib.settings.UseConvenienceFee;
      let convFeeRate =
        Number(csLib.settings.ConvenienceFeeRate.replace("%", "")) / 100;
      let convFeeItem = csLib.settings.ConvenienceFeeItem;
      if (useConvFee && !NG.tools.isEmpty(convFeeItem) && convFeeRate > 0) {
        context.form
          .addField({
            id: "custpage_conv_fee",
            type: "currency",
            label: "conv fee",
          })
          .updateDisplayType({ displayType: "hidden" });
        context.form
          .addField({
            id: "custpage_base_amount",
            type: "currency",
            label: "conv fee",
          })
          .updateDisplayType({ displayType: "hidden" });
      }
    }
  }

  /**
   * Function definition to be triggered before record is loaded.
   *
   * @param {Object} context
   * @param {Record} context.newRecord - New record
   * @param {Record} context.oldRecord - Old record
   * @param {string} context.type - Trigger type
   * @Since 2015.2
   */
  function beforeSubmit(context) {
    let { newRecord: newDepositRecord, type } = context;
    csLib.settings = csLib.trigger();
    CS_SETTINGS = settings.useSettings();

    if (["create", "edit"].includes(type)) {
      let checkNum = newDepositRecord.getValue({ fieldId: "checknum" });
      if (!NG.tools.isEmpty(checkNum)) {
        context.newDepositRecord.setValue({
          fieldId: "custbody_check_num",
          value: checkNum,
        });
      }
      let soId =
        newDepositRecord.getValue({ fieldId: "salesorder" }) ||
        newDepositRecord.getValue({ fieldId: "custbody_booth_order" });
      // Run convenience fee add and consider order types for fee
      let orderType =
        soId &&
        search.lookupFields({
          type: search.Type.SALES_ORDER,
          id: soId,
          columns: ["custbody_ng_cs_order_type"],
        }).custbody_ng_cs_order_type[0].value;

      let convFeeOrderTypes =
        String(CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types).search(
          /,/g
        ) !== -1
          ? CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types
              .split(",")
              .map(Number)
          : [CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types];

      let useConvFee = csLib.settings.UseConvenienceFee;
      let convFeeRate =
        Number(csLib.settings.ConvenienceFeeRate.replace("%", "")) / 100;
      let convFeeItem = csLib.settings.ConvenienceFeeItem;

      if (
        useConvFee &&
        convFeeItem &&
        convFeeRate > 0 &&
        convFeeOrderTypes.includes(orderType)
      ) {
        let convFee = newDepositRecord.getValue({
          fieldId: "custpage_conv_fee",
        });

        if (convFee && soId) {
          let sessData = {
            convFee: Number(convFee).toFixed(2),
            soId,
            baseAmount: Number(
              newDepositRecord.getValue({ fieldId: "custpage_base_amount" })
            ).toFixed(2),
          };
          runtime.getCurrentSession().set({
            name: "_CS_DP_{0}_{1}_CONV_FEE".NG_Format(
              runtime.getCurrentUser().id,
              runtime.getCurrentUser().role
            ),
            value: "{0}".NG_Format(JSON.stringify(sessData)),
          });
        }
      }
    }
  }

  /**
   * Function definition to be triggered before record is loaded.
   *
   * @param {Object} context
   * @param {Record} context.newRecord - New record
   * @param {Record} context.oldRecord - Old record
   * @param {string} context.type - Trigger type
   * @Since 2015.2
   */
  function afterSubmit(context) {
    csLib.settings = csLib.trigger();
    CS_SETTINGS = settings.useSettings();
    let recType = "{0}".NG_Format(context.newRecord.type);
    let recId = Number(context.newRecord.id).toFixed(0);
    let currentDepositRecord = context.newRecord;
    let relatedSalesOrderId = currentDepositRecord.getValue("salesorder");

    // Run convenience fee add and consider order types for fee
    let orderType =
      relatedSalesOrderId &&
      search.lookupFields({
        type: search.Type.SALES_ORDER,
        id: relatedSalesOrderId,
        columns: ["custbody_ng_cs_order_type"],
      }).custbody_ng_cs_order_type[0].value;

    let convFeeOrderTypes =
      String(CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types).search(
        /,/g
      ) !== -1
        ? CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types
            .split(",")
            .map(Number)
        : [CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types];

    if (relatedSalesOrderId) {
      runRelatedDepositTemplateGather(relatedSalesOrderId)
        .then((r) => {
          log.audit({
            title: "Deposit record updated Sales Order related payments ✅",
            details: r,
          });
          record.submitFields({
            type: record.Type.SALES_ORDER,
            id: relatedSalesOrderId,
            values: {
              custbody_ng_cs_so_related_pymnt_rnder: r,
            },
          });
          updateBalanceAndPaid(relatedSalesOrderId).then((soRes) => {
            log.audit({
              title: "Update of balance and paid fields complete! ✅",
              details: "",
            });
          });
        })
        .catch((err) => {
          log.error({
            title: "❌ There was an error setting related deposits render...",
            details: err,
          });
        });
    }

    if (["create", "edit"].includes(context.type)) {
      let useConvFee = csLib.settings.UseConvenienceFee;
      let convFeeRate =
        Number(csLib.settings.ConvenienceFeeRate.replace("%", "")) / 100;
      let convFeeItem = csLib.settings.ConvenienceFeeItem;
      if (
        useConvFee &&
        convFeeItem &&
        convFeeRate > 0 &&
        relatedSalesOrderId &&
        convFeeOrderTypes.includes(orderType)
      ) {
        let sessionID = "_CS_DP_{0}_{1}_CONV_FEE".NG_Format(
          runtime.getCurrentUser().id,
          runtime.getCurrentUser().role
        );
        let sessObjInit = runtime.getCurrentSession().get({ name: sessionID });
        if (!NG.tools.isEmpty(sessObjInit)) {
          runtime.getCurrentSession().set({ name: sessionID, value: "" });
          let sessObj = JSON.parse(sessObjInit);
          let soId = sessObj.soId;
          let convFee = sessObj.convFee;
          let baseAmount = sessObj.baseAmount;
          let soNum = "";

          try {
            let rec = record.load({
              type: "salesorder",
              id: soId,
              isDynamic: true,
            });
            soNum = rec.getValue({ fieldId: "tranid" });
            rec.selectNewLine({ sublistId: "item" });
            rec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "item",
              value: convFeeItem,
            });
            rec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "quantity",
              value: 1,
            });
            rec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "price",
              value: "-1",
            });
            rec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "rate",
              value: convFee,
            });
            rec.commitLine({ sublistId: "item" });
            rec.save({ enableSourcing: true, ignoreMandatoryFields: true });
          } catch (err) {
            NG.log.logError(
              err,
              "Error encountered updating booth order with CC Convenience Fee",
              "Order: {0} ({1}) -- Fee Amount: {2}".NG_Format(
                soNum || "N/A",
                soId,
                convFee
              )
            );
          }

          let updPayTotal = NG.M.roundToHundredths(
            Number(baseAmount) + Number(convFee)
          );
          let values = {
            payment: updPayTotal.toFixed(2),
            ccapproved: true,
            ccprocessoraccount: "",
          };
          values.undepfunds = csLib.settings.UndepositedFunds ? "T" : "F";
          if (
            !NG.tools.isEmpty(csLib.settings.DefaultDepositAccount) &&
            csLib.settings.UndepositedFunds
          ) {
            try {
              values.account = csLib.settings.DefaultDepositAccount;
            } catch (err) {
              NG.log.logError(err, "Error encountered setting deposit account");
              values.undepfunds = "T";
              values.account = "";
            }
          } else if (
            NG.tools.isEmpty(csLib.settings.DefaultDepositAccount) &&
            !csLib.settings.UndepositedFunds
          ) {
            values.undepfunds = "T";
          }
          if (csLib.settings.UndepositedFunds) {
            values.account = "";
          }
          if (!NG.tools.isEmpty(csLib.settings.PaymentARAccount)) {
            values.aracct = csLib.settings.PaymentARAccount;
          }
          try {
            handleOrderTypeOnChange(context);
            record.submitFields({
              type: recType,
              id: recId,
              values,
              options: { enableSourcing: false, ignoreMandatoryFields: true },
            });
          } catch (err) {
            NG.log.logError(
              err,
              "Error encountered updating payment amount on deposit"
            );
          }
        }
      }

      let dpstData = NG.tools.getLookupFields(
        recType,
        recId,
        ["salesorder", "custbody_booth_order", "custbody_show_table"],
        ["salesorder", "custbody_booth_order", "custbody_show_table"],
        []
      );
      let ordId = dpstData.salesorder || dpstData.custbody_booth_order;

      if (!dpstData?.custbody_booth_order) {
        throw new Error("Booth order field is required!");
      }

      let eventData = NG.tools.getLookupFields(
        "customrecord_show",
        dpstData.custbody_booth_order,
        csLib.settings._EVENT_FIELDS,
        csLib.settings._EVENT_FIELDS_S,
        []
      );
      if (ordId) {
        let transTotal = 0;
        let ordTotal = csLib.settings.ExemptEstimatedItems
          ? csLib.func.getExemptedTotal(
              record.load({ type: "salesorder", id: ordId, isDynamic: true }),
              false,
              eventData
            )
          : Number(
              NG.tools.getLookupFields("salesorder", ordId, ["total"], [], [])
            );
        let ttlFilt = [["custbody_booth_order", "anyof", [ordId]]];
        // let ttlResults = NG.tools.getSearchResults("transaction", ttlFilt, null, csLib.settings.PaymentSearch);
        let ttlResults = NG.tools.getSearchResultsAdv({
          type: "transaction",
          filterExp: ttlFilt,
          id: csLib.settings.PaymentSearch,
        });
        if (!NG.tools.isEmpty(ttlResults)) {
          ttlResults.forEach(function (res) {
            let tAmount = Math.abs(Number(res.getValue({ name: "amount" })));
            if (
              NG.tools.isInArray(res.type, [
                "cashrefund",
                "creditmemo",
                "customerrefund",
              ])
            ) {
              transTotal = NG.M.roundToHundredths(transTotal - tAmount);
            } else {
              transTotal = NG.M.roundToHundredths(transTotal + tAmount);
            }
          });
        }

        if (NG.tools.isEmpty(dpstData.salesorder)) {
          try {
            record.submitFields({
              type: recType,
              id: recId,
              values: { salesorder: dpstData.custbody_booth_order },
              options: { ignoreMandatoryFields: true, enableSourcing: true },
            });
            handleOrderTypeOnChange(context);
          } catch (err) {
            NG.log.logError(
              err,
              "Error encountered updating sales order on deposit"
            );
          }
        }
      }
    }
  }

  const updateBalanceAndPaid = async (salesOrderId) => {
    log.audit({
      title: "⚡ Running SO Update Balance Function...",
      details: `salesorder: "${salesOrderId}"`,
    });
    let relatedDeposits = [];
    let relatedPaymentDetails = [];
    let customerdepositSearchObj = search.create({
      type: "customerdeposit",
      filters: [
        ["type", "anyof", "CustDep"],
        "AND",
        ["salesorder", "anyof", salesOrderId],
        "AND",
        ["mainline", "any", ""],
      ],
      columns: [
        search.createColumn({
          name: "ordertype",
          sort: search.Sort.ASC,
          label: "Order Type",
        }),
        search.createColumn({ name: "type", label: "Type" }),
        search.createColumn({ name: "tranid", label: "Document Number" }),
        search.createColumn({ name: "entity", label: "Name" }),
        search.createColumn({ name: "amount", label: "Amount" }),
        search.createColumn({ name: "memo", label: "Memo" }),
        search.createColumn({ name: "mainline", label: "*" }),
      ],
    });
    let searchResultCount = customerdepositSearchObj.runPaged().count;
    log.debug("customerdepositSearchObj result count", searchResultCount);
    customerdepositSearchObj.run().each(function (result) {
      // .run().each has a limit of 4,000 results
      let resultObject = {
        id: result.id,
        amount: result.getValue("amount"),
      };
      relatedDeposits.push(resultObject);
      return true;
    });

    let totalAmountPaid = 0.0;
    let relatedDepositAmounts = [];

    if (relatedDeposits.length !== 0) {
      relatedDepositAmounts = relatedDeposits.map((dep) => dep.amount);
      totalAmountPaid = relatedDepositAmounts.reduce(
        (previousValue, currentValue) =>
          Number(previousValue.amount) + Number(currentValue.amount)
      );
    }

    let salesOrder = record.load({
      id: salesOrderId,
      type: record.Type.SALES_ORDER,
    });

    let total = Number(salesOrder.getValue("total"));

    record.submitFields({
      type: record.Type.SALES_ORDER,
      id: salesOrderId,
      values: {
        custbody_total_paid: totalAmountPaid,
        custbody_balance: total - totalAmountPaid,
      },
    });
  };

  // eslint-disable-next-line consistent-return
  const runRelatedDepositTemplateGather = async (salesOrderId) => {
    log.audit({
      title: "⚡ Running Deposit Template String Construction Function...",
      details: "",
    });
    try {
      let relatedDeposits = [];
      let relatedPaymentDetails = [];
      let customerdepositSearchObj = search.create({
        type: "customerdeposit",
        filters: [
          ["type", "anyof", "CustDep"],
          "AND",
          ["salesorder", "anyof", salesOrderId],
          "AND",
          ["mainline", "any", ""],
        ],
        columns: [
          search.createColumn({
            name: "ordertype",
            sort: search.Sort.ASC,
            label: "Order Type",
          }),
          search.createColumn({ name: "type", label: "Type" }),
          search.createColumn({ name: "tranid", label: "Document Number" }),
          search.createColumn({ name: "entity", label: "Name" }),
          search.createColumn({ name: "amount", label: "Amount" }),
          search.createColumn({ name: "memo", label: "Memo" }),
          search.createColumn({ name: "mainline", label: "*" }),
        ],
      });
      let searchResultCount = customerdepositSearchObj.runPaged().count;
      log.debug("customerdepositSearchObj result count", searchResultCount);
      customerdepositSearchObj.run().each(function (result) {
        // .run().each has a limit of 4,000 results
        let resultObject = {
          id: result.id,
          amount: result.getValue("amount"),
        };
        relatedDeposits.push(resultObject);
        return true;
      });

      let templateHtml = "";

      if (searchResultCount !== 0) {
        // Load each deposit record to get more information that a search cannot
        for (const dep of relatedDeposits) {
          let relatedDepRecord = record.load({
            type: record.Type.CUSTOMER_DEPOSIT,
            id: dep.id,
          });

          relatedDepRecord &&
            log.audit({ title: "Customer Deposit Loaded ✅", details: "" });

          let paymentDetails = {
            method: relatedDepRecord.getText("paymentmethod"),
            tranType: "Deposit",
            ccNumber: relatedDepRecord.getValue("ccnumber"),
            checkNumber: relatedDepRecord.getValue("checknum"),
            expires: relatedDepRecord.getValue("ccexpiredate"),
            nameOnCard: relatedDepRecord.getValue("ccname"),
            tranDate: relatedDepRecord.getValue("trandate"),
            amount: relatedDepRecord.getValue("payment"),
          };

          relatedPaymentDetails.push(paymentDetails);

          let customerRefundSearchObject = search.create({
            type: search.Type.DEPOSIT_APPLICATION,
            filters: [
              ["type", "anyof", "DepAppl"],
              "AND",
              ["appliedtotransaction", "anyof", dep.id],
            ],
            columns: [
              search.createColumn({
                name: "ordertype",
                sort: search.Sort.ASC,
                label: "Order Type",
              }),
              search.createColumn({ name: "mainline", label: "*" }),
              search.createColumn({
                name: "paymentmethod",
                label: "Payment Method",
              }),
              search.createColumn({ name: "trandate", label: "Date" }),
              search.createColumn({ name: "asofdate", label: "As-Of Date" }),
              search.createColumn({ name: "postingperiod", label: "Period" }),
              search.createColumn({ name: "taxperiod", label: "Tax Period" }),
              search.createColumn({ name: "type", label: "Type" }),
              search.createColumn({ name: "tranid", label: "Document Number" }),
              search.createColumn({ name: "entity", label: "Name" }),
              search.createColumn({ name: "account", label: "Account" }),
              search.createColumn({ name: "memo", label: "Memo" }),
              search.createColumn({ name: "amount", label: "Amount" }),
              search.createColumn({
                name: "paymentmethod",
                label: "Payment Method",
              }),
            ],
          });

          getAllResultsFor(customerRefundSearchObject, (result) => {
            let appliedRecord = record.load({
              type: record.Type.DEPOSIT_APPLICATION,
              id: result.id,
            });
            let tranTypeLabel = "Deposit";
            let appliedTransactionsCount = appliedRecord.getLineCount("apply");

            for (let line = 0; line < appliedTransactionsCount; line++) {
              let isApplied = appliedRecord.getSublistValue({
                sublistId: "apply",
                fieldId: "apply",
                line,
              });
              let tranType = appliedRecord.getSublistValue({
                fieldId: "trantype",
                sublistId: "apply",
                line,
              });

              log.debug({ title: "Transaction Applied:", details: isApplied });
              log.debug({ title: "Transaction Type:", details: tranType });
              if (isApplied) {
                switch (tranType) {
                  case "CustRfnd":
                    tranTypeLabel = "Refund";
                    break;
                  default:
                    tranTypeLabel = "Deposit";
                }
              }
            }

            let amount = result.getValue("amount");
            let amountFormatted = "";

            if (String(amount).search("-") !== -1) {
              let currencyString = format.format({
                value: amount,
                type: format.Type.CURRENCY,
              });
              amountFormatted = `(${String(currencyString).replace("-", "")})`;
            } else {
              amountFormatted = amount;
            }

            let paymentDetails = {
              method: result.getText("paymentmethod"),
              tranType: tranTypeLabel,
              ccNumber: "",
              checkNumber: "",
              expires: "",
              nameOnCard: "",
              tranDate: result.getValue("trandate"),
              amount: amountFormatted,
            };

            relatedPaymentDetails.push(paymentDetails);
          });
        }

        log.debug({
          title: "Payment Details After:",
          details: relatedPaymentDetails,
        });

        // Build HTML for template base off the payment details.
        relatedPaymentDetails.forEach((pmt, index) => {
          let transDate = new Date(pmt.tranDate);
          let dateString = format.format({
            value: transDate,
            type: format.Type.DATE,
          });
          let currencyString = format.format({
            value: pmt.amount,
            type: format.Type.CURRENCY,
          });

          templateHtml += `
					<tr>
						<td style="align: left;">${dateString}</td>
						<td style="align: left;">${pmt.tranType}</td>
						<td style="align: left;">${pmt.method}</td>
						<td style="align: left;">${!pmt.ccNumber ? pmt.checkNumber : pmt.ccNumber}</td>
						<td style="align: right;">${currencyString}</td>
					</tr>
				`;
        });
      }

      log.audit({
        title: "🔧 Template String generated...",
        details: `Generated:\n ${templateHtml}`,
      });

      return templateHtml;
    } catch (err) {
      log.error({
        title: "Error occurred when setting template values...",
        details: err,
      });
    }
  };

  function handleOrderTypeOnChange(sc) {
    log.audit({ title: "⚡ Running Order Type On Change...", details: "" });
    let currentSalesOrder = sc.newRecord;
    let oldSalesOrder = sc.oldRecord;
    let orderType = currentSalesOrder.getValue("custbody_ng_cs_order_type");

    let orderTypeRecord = record.load({
      type: "customrecord_ng_cs_order_type",
      id: orderType,
    });

    let depositDefault = orderTypeRecord.getValue(
      "custrecord_ng_cs_order_type_deposit_def"
    );
    let overrideDepositPercent = orderTypeRecord.getValue(
      "custrecord_ng_override_dep_perc"
    );

    if (overrideDepositPercent) {
      log.audit({
        title: "⚡ Setting Deposit Percent Override...",
        details: "",
      });
      record.submitFields({
        type: record.Type.SALES_ORDER,
        id: currentSalesOrder.id,
        values: {
          requireddepositpercentage: depositDefault,
        },
      });
    } else {
      let prevDepositDefault =
        oldSalesOrder && oldSalesOrder.getValue("requireddepositpercentage");

      record.submitFields({
        type: record.Type.SALES_ORDER,
        id: currentSalesOrder.id,
        values: {
          requireddepositpercentage: prevDepositDefault || depositDefault,
        },
      });
    }
  }

  function doSalesOrderEventWork(sc, salesOrderId) {
    let currentDepositRec = sc.newRecord;
    let salesOrder = record.load({
      id: salesOrderId,
      type: record.Type.SALES_ORDER,
    });

    let boothId = salesOrder.getValue("custbody_booth");
    let csJob = salesOrder.getValue("custbody_cseg_ng_cs_job");
    let eventId = salesOrder.getValue("custbody_show_table");

    currentDepositRec.setValue({
      fieldId: "custbody_show_table",
      value: eventId,
    });

    currentDepositRec.setValue({
      fieldId: "custbody_booth",
      value: boothId,
    });

    currentDepositRec.setValue({
      fieldId: "custbody_cseg_ng_cs_job",
      value: csJob,
    });

    currentDepositRec.setValue({
      fieldId: "custbody_booth_order",
      value: salesOrderId,
    });
  }

  /**
   * Runs a search retrieving all results and into a callback function as a Search.ResultSet
   * @type Function
   * @param {Search} searchObj
   * @param {Function} callback
   * @returns void
   * */
  function getAllResultsFor(searchObj, callback) {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  }

  return {
    beforeLoad,
    beforeSubmit,
    afterSubmit,
  };
});
