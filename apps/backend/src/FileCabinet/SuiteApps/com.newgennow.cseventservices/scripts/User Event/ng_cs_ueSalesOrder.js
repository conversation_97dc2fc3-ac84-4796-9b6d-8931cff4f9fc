/* eslint-disable suitescript/module-vars */
// noinspection JSCheckFunctionSignatures,JSUnresolvedVariable,DuplicatedCode

/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @ModuleScope SameAccount
 */
define([
  "N/error",
  "N/https",
  "N/record",
  "N/runtime",
  "N/search",
  "N/ui/serverWidget",
  "N/url",
  "N/task",
  "N/encode",
  "N/query",
  "N/format",
  "../lib/newgen.library.v21",
  "../lib/newgen.library.cs.21",
], (
  error,
  https,
  record,
  runtime,
  search,
  widget,
  url,
  task,
  encode,
  query,
  format,
  NG,
  csLib
) => {
  let currTaxItem = -8; // this is the blank in the selector

  /**
   * The url to the Paytrace suitelet that runs the authorization & payment capture
   * @type {String|url} paySuiteletUrl
   * @alias _PAY_PATH
   * @version 2.0
   * @kind constant
   * */
  let paySuiteletUrl;
  /**
   * The url for authorization of the sales order auth item that begins the processing the rest of the sales order.
   * @type {String|url} authSuiteletUrl
   * @alias _AUTH_PATH
   * @version 1.0
   * @kind constant
   * */
  let authSuiteletUrl;

  /*
	*	_RunGlobalBO = csLib.settings.GlobalBoothOrderScripting;
		_ActiveFormBO = NG.tools.isInArray(formId, csLib.settings.BoothOrderFormIdListing);
		_RunGlobalAL = csLib.settings.GlobalAddItemScripting;
		_ActiveFormAL = NG.tools.isInArray(formId, csLib.settings.AddItemFormIdListing);
		_RunGlobalSM = csLib.settings.GlobalShowMGtScripting;
		_ActiveFormSM = NG.tools.isInArray(formId, csLib.settings.ShowMgtFormIdListing);
	* */

  /**
   * CS Settings Boolean that depicts to run Booth Ordering Scripts.
   * @type {boolean} _RunGlobalBO
   * @summary Pulling from CS Settings: _RunGlobalBO = csLib.settings.GlobalBoothOrderScripting;
   * */
  let _RunGlobalBO = false,
    /**
     * CS Settings Boolean that checks to see if there is a active Booth Ordering Form
     * @type {boolean} _ActiveFormBO
     * @summary Pulling from CS Settings: _ActiveFormBO = NG.tools.isInArray(formId, csLib.settings.BoothOrderFormIdListing);
     * */
    _ActiveFormBO = false,
    /**
     * CS Settings Boolean that checks to see if item scripting is enabled.
     * @type {boolean} _RunGlobalAL
     * @summary Pulling from CS Settings: _RunGlobalAL = csLib.settings.GlobalAddItemScripting;
     * */
    _RunGlobalAL = false,
    /**
     * CS Settings Boolean that checks to see if an Item Addition Form ID is set.
     * @type {boolean} _ActiveFormAL
     * @summary Pulling from CS Settings: _ActiveFormAL = NG.tools.isInArray(formId, csLib.settings.AddItemFormIdListing);
     * */
    _ActiveFormAL = false,
    /**
     * CS Settings Boolean that checks to see if an Non booth Form ID is set.
     * @type {boolean} _NonBoothFormAL
     * @deprecated This variable is not being used - will deprecate after new release
     * */
    _NonBoothFormAL = false,
    /**
     * CS Settings Boolean that checks to see if Show Management Scripting is enabled.
     * @type {boolean} _RunGlobalSM
     * @summary Pulling from CS Settings: _RunGlobalSM = csLib.settings.GlobalShowMGtScripting;
     * */
    _RunGlobalSM = false,
    /**
     * CS Settings Boolean that checks to see if Show Management Form ID has been set.
     * @type {boolean} _RunGlobalSM
     * @summary Pulling from CS Settings: _ActiveFormSM = NG.tools.isInArray(formId, csLib.settings.ShowMgtFormIdListing);
     * */
    _ActiveFormSM = false;

  /**
   * Defines the function definition that is executed before record is loaded.
   * @param {Object} context
   * @param {Record} context.newRecord - New record
   * @param {string} context.type - Trigger type; use values from the context.UserEventType enum
   * @param {Form} context.form - Current form
   * @param {ServerRequest} context.request - HTTP request information sent from the browser for a client action only.
   * @since 2015.2
   */
  const beforeLoad = (context) => {
    /**
     * Get URLs for suitelets to be able to send data later in script
     * @summary paySuiteletUrl & authSuiteletUrl get assigned url values.
     * */
    const setScriptPaths = () => {
      paySuiteletUrl = url.resolveScript({
        scriptId: "customscript_ng_cs_sl_exhib_web_pymt",
        deploymentId: "customdeploy_ng_cs_sl_exhib_web_pymt",
        returnExternalUrl: true,
        params: {},
      });

      authSuiteletUrl = url.resolveScript({
        scriptId: "customscript_auth_from_web_order",
        deploymentId: "customdeploy_auth_from_web_order_dep",
        returnExternalUrl: true,
        params: {},
      }); // THIS ISN"T BEING USED!
      // WHICH ONE OF THIS HANDLES AUTH??
    };

    log.audit({
      title: "⚡ Running script BEFORELOAD...",
      details: runtime.getCurrentScript(),
    });

    try {
      csLib.settings = csLib.trigger();
      log.debug({ title: "Settings object", details: csLib.settings });
      setScriptPaths();
      setFormFlags(context);
      let currentSalesOrder = context.newRecord;

      if (_ActiveFormBO || _ActiveFormSM || _RunGlobalBO || _RunGlobalSM) {
        csLib.func.hideCSJobField(context.form);
        /**
         * The ID of the exhibitor aka the "**entity**" that is being assigned to the order.
         * @summary The "entity" being assigned to the order. Also known as "Billing Party".
         * @type {Number} exhibitorId
         * */
        let exhibitorId,
          /**
           * The ID of the **CS Event** that the *order is related to*.
           * @type {Number} eventId
           * */
          eventId,
          /**
           * The ID of the *CS Booth* that is related to the **CS Event**.
           * @type {Number} boothId
           * */
          boothId,
          /**
           * The _ID_ of the **Booth Exhibitor** that is ordering for the particular event _(Contact assigned to the customer)_.
           * @summery The "custbody_booth_actual_exhibitor" being assigned to the order.
           * @type {Number} actualExhibitorId
           * */
          actualExhibitorId,
          /**
           * The ID of the **Booth Exhibitor Order Form** that is set for the default form coming from the __CS Settings__ record.
           * @type {Number} orderType
           * */
          orderType;

        log.debug({
          title: "Request received from source?",
          details: context?.request,
        });

        if (context?.request && Object.keys(context.request).length !== 0) {
          exhibitorId = context.request.parameters["entity"] || null;
          eventId = context.request.parameters["custbody_show_table"] || null;
          boothId = context.request.parameters["custbody_booth"] || null;
          actualExhibitorId =
            context.request.parameters["custbody_booth_actual_exhibitor"] ||
            null;
        }

        if (
          ["create", "copy"].includes(context.type) &&
          runtime.executionContext === "USERINTERFACE"
        ) {
          // Set exhibitor ID for customer
          if (exhibitorId && !isNaN(Number(exhibitorId))) {
            exhibitorId = currentSalesOrder.getValue({ fieldId: "entity" });
          } else {
            currentSalesOrder.setValue({
              fieldId: "entity",
              value: exhibitorId,
            });
          }

          // Set show/event Id on sales order
          if (eventId && !isNaN(Number(eventId))) {
            eventId = currentSalesOrder.getValue({
              fieldId: "custbody_show_table",
            });
          } else {
            currentSalesOrder.setValue({
              fieldId: "custbody_show_table",
              value: eventId,
            });
          }

          // Set booth Id for event space on order.
          if (boothId && !isNaN(Number(boothId))) {
            boothId = currentSalesOrder.getValue({ fieldId: "custbody_booth" });
          } else {
            currentSalesOrder.setValue({
              fieldId: "custbody_booth",
              value: boothId,
            });
          }

          // Set custom field booth actual exhibitor ID
          if (actualExhibitorId && !isNaN(Number(actualExhibitorId))) {
            actualExhibitorId = currentSalesOrder.getValue({
              fieldId: "custbody_booth_actual_exhibitor",
            });
          } else {
            currentSalesOrder.setValue({
              fieldId: "custbody_booth_actual_exhibitor",
              value: actualExhibitorId,
            });
          }

          // Start of deprecation
          if (
            context.type === "create" &&
            context?.request &&
            Object.keys(context.request).length !== 0
          ) {
            /**
             * CS Job Id class coming from request params.
             * @type {Number} classIdParam
             * */
            let classIdParam = context.request.parameters["jb"] || null; // Class of job
            /**
             * CS Job Id coming from request params.
             * @type {Number} jobIdParam
             * */
            let jobIdParam = context.request.parameters["csj"] || null; // CS Job ID
            /**
             * Show ID coming from request params.
             * @type {Number} eventIdParam
             * */
            let eventIdParam = context.request.parameters["sw"] || null; // Show id

            log.audit({
              title: "Checking job and class",
              details: `Class: ${classIdParam} -- Job: ${jobIdParam}`,
            });

            if (jobIdParam || classIdParam) {
              if (classIdParam && !isNaN(Number(classIdParam))) {
                log.audit({
                  title: "Class param defined!",
                  details: `Class: ${classIdParam} -- disabling field.`,
                });
                context.form
                  .getField({
                    id: "class",
                  })
                  .updateDisplayType({
                    displayType: widget.FieldDisplayType.DISABLED,
                  }).defaultValue = classIdParam || "";
              }

              if (jobIdParam && !isNaN(Number(jobIdParam))) {
                log.audit({
                  title: "Job param defined!",
                  details: `Job: ${jobIdParam} -- disabling field.`,
                });
                context.form
                  .getField({
                    id: "custbody_cseg_ng_cs_job",
                  })
                  .updateDisplayType({
                    displayType: widget.FieldDisplayType.DISABLED,
                  }).defaultValue = jobIdParam || "";
              }

              if (eventIdParam && !isNaN(Number(eventIdParam))) {
                log.audit({
                  title: "CS Event param defined!",
                  details: `CS EVENT: ${eventIdParam} -- disabling field.`,
                });
                context.form
                  .getField({
                    id: "custbody_show_table",
                  })
                  .updateDisplayType({
                    displayType: widget.FieldDisplayType.DISABLED,
                  }).defaultValue = eventIdParam || "";
              }
            }
          }

          /**
           * Will not need after upgrade.
           * * * * * * * * * * * *
           * Vegas only feature - RetainLastShow.
           * This may be deprecated in a new release.
           * @type {boolean} rememberLastShow
           * * * * * * * * * * * */
          let rememberLastShow = csLib.settings.RetainLastShow;
          if (!eventId && isNaN(Number(eventId)) && rememberLastShow) {
            let lastEvent,
              values = {
                action: "get",
                user: Number(runtime.getCurrentUser().id).toFixed(0),
              };
            try {
              lastEvent = csLib.func.getLastShow(values);
            } catch (err) {
              NG.log.logError(
                err,
                "Error encountered retrieving last set event"
              );
            }

            if (!NG.tools.isEmpty(lastEvent)) {
              eventId = lastEvent;
              currentSalesOrder.setValue({
                fieldId: "custbody_show_table",
                value: lastEvent,
              });
            }
          }
        }
        // End of deprecation

        if (["create", "copy", "edit"].includes(context.type)) {
          if (!isNaN(Number(exhibitorId)) && !isNaN(Number(eventId))) {
            setPriceLevel(exhibitorId, eventId);
          }

          // Deprecate start
          if (!isNaN(Number(eventId))) {
            // If record is NOT in the context of EDIT
            if (context.type !== "edit") {
              let fields = ["custrecord_fin_show", "custrecord_adv_ord_date"];

              if (csLib.settings.UseCustomJob) {
                fields.push("custrecord_show_job");
              }
              if (NG.NSFeatures.SUBSIDIARIES()) {
                fields.push("custrecord_show_subsidiary");
              }
              if (NG.NSFeatures.LOCATIONS()) {
                fields.push("custrecord_show_venue");
              }
              if (csLib.settings.UseTaxCode) {
                fields.push("custrecord_tax_rate");
              }

              let showData;
              try {
                showData = NG.tools.getLookupFields(
                  "customrecord_show",
                  eventId,
                  fields
                );
              } catch (err) {
                NG.log.logError(err, "Error encountered getting show data");
              }

              if (!NG.tools.isEmpty(showData)) {
                currentSalesOrder.setValue({
                  fieldId: "class",
                  value: showData.custrecord_fin_show,
                });
                currentSalesOrder.setValue({
                  fieldId: "custbody_advanced_order_date",
                  value: showData.custrecord_adv_ord_date,
                });

                if (csLib.settings.UseCustomJob) {
                  currentSalesOrder.setValue({
                    fieldId: "custbody_cseg_ng_cs_job",
                    value: showData.custrecord_show_job,
                  });
                }
                if (NG.NSFeatures.SUBSIDIARIES()) {
                  currentSalesOrder.setValue({
                    fieldId: "subsidiary",
                    value: showData.custrecord_show_subsidiary,
                  });
                }
                if (NG.NSFeatures.LOCATIONS()) {
                  currentSalesOrder.setValue({
                    fieldId: "location",
                    value: showData.custrecord_show_venue,
                  });
                }
                // DEPRECATED: HARD SET OF TAX FROM EVENT WHEN USEEVENTTAX IS ENABLED
              }
            }
          }
        }
        // End of deprecation

        // Display create work order modal for labor items
        if (context.type === "view") {
          log.audit({
            title:
              "Record in view mode - Active Show Mngt Form & Run Global Show Mngt?",
            details: `ℹ - "${_ActiveFormSM || _RunGlobalSM}"`,
          });
          if (_ActiveFormSM || _RunGlobalSM) {
            log.audit({
              title: "Record in view mode - Getting CS Area items",
              details: "",
            });
            let hasAreas = false;
            for (
              let l = 0;
              l < currentSalesOrder.getLineCount({ sublistId: "item" });
              l++
            ) {
              let itemLine = currentSalesOrder.getSublistValue({
                sublistId: "item",
                fieldId: "custcol_ng_cs_area",
                line: l,
              });

              if (!NG.tools.isEmpty(itemLine)) {
                hasAreas = true;
                break;
              }
            }

            if (hasAreas) {
              log.audit({
                title: "Record in view mode - Displaying WO Button",
                details: "",
              });
              // Deprecated start
              context.form.addButton({
                id: "custpage_create_work_order",
                label: "Work Order",
                functionName: "ngcsCreateWorkOrder()",
              });

              context.form.clientScriptModulePath =
                "../cs-client/ng_cs_clientSOPrintWorkOrderButton.js";
              let libraryPath21 = NG.tools.getFileCabinetPath({
                fileName: "newgen.library.v21.js",
              });
              log.audit({ title: "v2.1 library path", details: libraryPath21 });
              if (!NG.tools.isEmpty(libraryPath21)) {
                let woHTML = "";
                woHTML += '<script type="text/javascript">';
                woHTML += 'console.log("loading custom module..."); ';
                woHTML += `require(['${libraryPath21}/newgen.library.v21']); `;
                woHTML += "let _NG;";
                woHTML += "setTimeout(() => { ";
                woHTML += `_NG = require('${libraryPath21}/newgen.library.v21');`;
                woHTML += 'console.log("custom module has been loaded"); ';
                woHTML += "}, 1000); ";
                woHTML += "</script>";
                context.form.addField({
                  id: "custpage_wo_html",
                  type: widget.FieldType.INLINEHTML,
                  label: "wo html",
                }).defaultValue = woHTML;
              }
              // Deprecated end
            }
          }

          log.audit({
            title:
              "Record in view mode - Active Booth Ord Form & Run Global Booth Order?",
            details: `ℹ  - "${_ActiveFormSM || _RunGlobalSM}"`,
          });

          if (_ActiveFormBO || _RunGlobalBO) {
            updateBalanceAndPaid(currentSalesOrder, record, search)
              .then((r) => {
                log.audit({
                  title: "Updated paid & balance fields! ✅",
                  details: "",
                });
              })
              .catch((err) => {
                log.error({
                  title:
                    "An error occurred with setting balance and paid fields...",
                  details: err,
                });
              });

            runRelatedDepositTemplateGather(currentSalesOrder.id)
              .then((r) => {
                log.audit({
                  title: "Related Deposits Gathered...✅",
                  details: r,
                });

                record.submitFields({
                  type: record.Type.SALES_ORDER,
                  id: currentSalesOrder.id,
                  values: {
                    custbody_ng_cs_so_related_pymnt_rnder: r,
                  },
                });
              })
              .catch((err) => {
                log.error({
                  title: "❌ Error loading deposits...",
                  details: err,
                });
              });

            orderType = currentSalesOrder.getValue({
              fieldId: "custbody_ng_cs_order_type",
            });
            if (orderType === csLib.settings.DefaultExhibitorOrderType) {
              let baseURL = url.resolveScript({
                scriptId: "customscript_ng_cs_exhib_wo_rprt",
                deploymentId: "customdeploy_ng_cs_exhib_wo_rprt_dep",
                returnExternalUrl: false,
                params: {},
              });

              let targetURL = "{0}&shs={1}&bth={2}&prt=T".NG_Format(
                baseURL,
                currentSalesOrder.getValue({ fieldId: "custbody_show_table" }),
                currentSalesOrder.getValue({ fieldId: "custbody_booth" })
              );
              let exbWrkOrdHTML = "";
              exbWrkOrdHTML += '<script type="text/javascript">';
              exbWrkOrdHTML += "function ngcsPrintWorkOrder() { ";
              exbWrkOrdHTML += 'window.open("{0}"); '.NG_Format(targetURL);
              exbWrkOrdHTML += "}";
              exbWrkOrdHTML += "</script>";

              context.form.addField({
                id: "custpage_exb_wrk_ord_html",
                type: widget.FieldType.INLINEHTML,
                label: "wrk ord html",
              }).defaultValue = exbWrkOrdHTML;

              context.form.addButton({
                id: "custpage_exb_wrk_ord_btn",
                label: "Exhibitor Work Order",
                functionName: "ngcsPrintWorkOrder()",
              });
            }
          }
        }

        // Get order type from request params to set order type on record
        if (context.type === "create" && !NG.tools.isEmpty(context.request)) {
          orderType = context.request.parameters["ot"]; // Order type
          if (!NG.tools.isEmpty(orderType)) {
            currentSalesOrder.setValue({
              fieldId: "custbody_ng_cs_order_type",
              value: orderType,
            });
          }
        }

        log.audit({
          title: "Check Deposit Record Form - Show Mngt",
          details: `ℹ  - "${_ActiveFormSM || _RunGlobalSM}"`,
        });

        // Deprecated start

        // Deprecation end
      }

      log.audit({
        title: "Check Add Line",
        details: `ℹ  - "${_RunGlobalAL || _ActiveFormAL}"`,
      });

      if (_RunGlobalAL || _ActiveFormAL) {
        if (["create", "edit", "copy"].includes(context.type)) {
          if (runtime.executionContext === runtime.ContextType.USER_INTERFACE) {
            if (context.type !== "edit") {
              let buttonURL = url.resolveScript({
                scriptId: "customscript_exhib_booth_form",
                deploymentId: "customdeploy_exhib_booth_form_dep",
                returnExternalUrl: false,
                params: {},
              });

              context.form.addButton({
                id: "custpage_neb",
                label: "New Exhibitor/Booth",
                functionName: "window.location='{0}&fo=T'".NG_Format(buttonURL),
              });
            }

            let itemSublist = context.form.getSublist({ id: "item" });
            context.form.clientScriptModulePath =
              "../cs-client/ng_cs_cm_add_line_scripting.js";
            itemSublist.addButton({
              id: "custpage_add_item",
              label: "Add to Order",
              functionName: "addItemToNewLine",
            });

            let subsidiary;
            if (NG.NSFeatures.SUBSIDIARIES()) {
              subsidiary = !NG.tools.isEmpty(
                currentSalesOrder.getValue({ fieldId: "subsidiary" })
              )
                ? currentSalesOrder.getValue({ fieldId: "subsidiary" })
                : null;
            }

            csLib.func.getSelectionItems(subsidiary);

            let items = {
              sqftItems: csLib.settings.sqftItems,
              daysItems: csLib.settings.daysItems,
              sqdItems: csLib.settings.sqdItems,
              freightItems: csLib.settings.freightItems,
              colorItems: csLib.settings.colorItems,
              sizeItems: csLib.settings.sizeItems,
              orientationItems: csLib.settings.orientationItems,
              laborItems: csLib.settings.laborItems,
              durationItems: csLib.settings.durationItems,
              graphicsItems: csLib.settings.graphicsItems,
            };

            context.form
              .addField({
                id: "custpage_ng_items_list",
                type: widget.FieldType.LONGTEXT,
                label: "items list",
              })
              .updateDisplayType({
                displayType: widget.FieldDisplayType.HIDDEN,
              }).defaultValue = JSON.stringify(items);

            if (csLib.func.RetainLastItemCat) {
              let lastItemCatParams = {
                action: "get",
                user: runtime.getCurrentUser().id,
              };
              let lastItemCat = csLib.func.getLastItemCat(lastItemCatParams);
              if (!NG.tools.isEmpty(lastItemCat)) {
                currentSalesOrder.setValue({
                  fieldId: "custbody_item_category",
                  value: lastItemCat,
                });
              }
            }
          }
        }
      }
    } catch (err) {
      log.error({ title: "❌ Beforeload error occurred:", details: err });
    }
  };

  /**
   * Defines the function definition that is executed before record is submitted.
   * @param {Object} context
   * @param {Record} context.newRecord - New record
   * @param {Record} context.oldRecord - Old record
   * @param {string} context.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const beforeSubmit = async (context) => {
    const setScriptPaths = () => {
      paySuiteletUrl = url.resolveScript({
        scriptId: "customscript_ng_cs_sl_exhib_web_pymt",
        deploymentId: "customdeploy_ng_cs_sl_exhib_web_pymt",
        returnExternalUrl: true,
        params: {},
      });
      authSuiteletUrl = url.resolveScript({
        scriptId: "customscript_auth_from_web_order",
        deploymentId: "customdeploy_auth_from_web_order_dep",
        returnExternalUrl: true,
        params: {},
      });
    };

    log.audit({
      title: "⚡ Running script BEFORESUBMIT...",
      details: runtime.getCurrentScript(),
    });

    let currentSalesOrder = context.newRecord;

    // try {
    log.audit({ title: "⚡  Inside root try BeforeSubmit...", details: "⌛" });
    csLib.settings = csLib.trigger();
    setScriptPaths();
    setFormFlags(context);

    _ActiveFormBO = csLib.settings.BoothOrderFormIdListing.includes(
      currentSalesOrder.getValue({ fieldId: "customform" })
    );

    log.audit({
      title: "Execution context",
      details: runtime.executionContext,
    });
    log.audit({
      title: "Is booth order",
      details: `Run booth ordering scripts: ${_RunGlobalBO} -- Booth ordering form present: ${_ActiveFormBO}`,
    });

    let eventId, boothId, billParty;

    // Run if form includes order form or booth scripting is turned on
    if (_ActiveFormBO) {
      await runBoothOrderBeforeSubmitOperations(context, _ActiveFormBO)
        .then((res) => {
          log.audit({ title: "BeforeSUB BO ops finished.", details: res });
        })
        .catch((err) => {
          log.error({ title: "Error occured running BO ops...", details: err });
          throw err;
        });
    }

    log.audit({
      title: "tax item id (BeforeSubmit)",
      details: currentSalesOrder.getValue({ fieldId: "taxitem" }),
    });

    // OVERRIDE FORCE TAXITEM RESET
    currTaxItem = currentSalesOrder.getValue({ fieldId: "taxitem" });

    log.audit({ title: "Outside of catch! ⚡", details: "" });
    // } catch (err) {
    // 	log.error({title: '❌ Error occurred in BeforeSubmit!', details: err})
    // 	throw err
    // }
  };

  /**
   * Defines the function definition that is executed after record is submitted.
   * @param {Object} context
   * @param {Record} context.newRecord - New record
   * @param {Record} context.oldRecord - Old record
   * @param {string} context.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const afterSubmit = (context) => {
    let currentSalesOrder = context.newRecord;

    const setScriptPaths = () => {
      paySuiteletUrl = url.resolveScript({
        scriptId: "customscript_ng_cs_sl_exhib_web_pymt",
        deploymentId: "customdeploy_ng_cs_sl_exhib_web_pymt",
        returnExternalUrl: true,
        params: {},
      });
      authSuiteletUrl = url.resolveScript({
        scriptId: "customscript_auth_from_web_order",
        deploymentId: "customdeploy_auth_from_web_order_dep",
        returnExternalUrl: true,
        params: {},
      });
    };

    log.audit({
      title: "⚡ Running script AFTERSUBMIT...",
      details: runtime.getCurrentScript(),
    });

    try {
      log.audit({ title: "⚡ AfterSubmit inside root try", details: "⌛" });

      let asStart = new Date();

      csLib.settings = csLib.trigger();
      setFormFlags(context);
      setScriptPaths();

      let oldRec;

      log.audit({
        title: "🙋‍♂️ Current tax item var reads: ",
        details: currTaxItem,
      });

      const resetTaxToEvent = () => {
        log.audit({
          title: "Resetting Tax upon create!",
          details: `Context type: "${context.type}"`,
        });
        let ordTaxInfo = NG.tools.getLookupFields(
          currentSalesOrder.type,
          currentSalesOrder.id,
          [
            "tranid",
            "taxitem",
            "custbody_ng_cs_tax_temp",
            "custbody_show_table",
          ],
          ["taxitem", "custbody_ng_cs_tax_temp", "custbody_show_table"],
          []
        );
        let eventId = ordTaxInfo.custbody_show_table;
        log.debug({
          title: "Checking if event Id is valid...",
          details: eventId,
        });

        if (!isNaN(Number(eventId))) {
          eventData = getEventData(eventId);
        }

        let resetTax = false,
          targetTax;
        if (!NG.tools.isEmpty(ordTaxInfo.custbody_ng_cs_tax_temp)) {
          resetTax = true;
          targetTax = ordTaxInfo.custbody_ng_cs_tax_temp;
        } else if (
          context.type !== "create" &&
          NG.tools.isEmpty(ordTaxInfo.custbody_ng_cs_tax_temp) &&
          context.newRecord.getValue({ fieldId: "taxitem" }) !==
            context.oldRecord.getValue({ fieldId: "taxitem" })
        ) {
          resetTax = true;
          targetTax = context.oldRecord.getValue({ fieldId: "taxitem" });
        } else if (
          !NG.tools.isEmpty(eventData) &&
          context.type === "create" &&
          NG.tools.isInArray(runtime.executionContext, [
            runtime.ContextType.WEBSTORE,
            runtime.ContextType.RESTLET,
          ]) &&
          context.newRecord.getValue({ fieldId: "taxitem" }) !==
            eventData.custrecord_tax_rate
        ) {
          resetTax = true;
          targetTax = eventData.custrecord_tax_rate;
        }

        if (resetTax && !NG.tools.isEmpty(targetTax)) {
          log.audit({
            title: "updating booth order sales tax",
            details: ordTaxInfo.tranid,
          });
          let ord = record.load({
            type: currentSalesOrder.type,
            id: currentSalesOrder.id,
          });
          ord.setValue({ fieldId: "taxitem", value: targetTax });
          ord.setValue({ fieldId: "custbody_ng_cs_tax_temp", value: "" });
          ord.save({ ignoreMandatoryFields: true, enableSourcing: true });
        }
      };

      const checkAuthItem = () => {
        try {
          // find auth item first before moving further in code
          let authLine = currentSalesOrder.findSublistLineWithValue({
            sublistId: "item",
            fieldId: "item",
            line: csLib.settings.AuthItem,
          });
          if (authLine > 0) {
            log.audit({
              title: "Authorization order; Terminating script functions",
              details: "",
            });
            return;
          }
        } catch (err) {
          NG.log.logError(
            err,
            "Could not verify existence of authorization line item"
          );
        }
      };

      /*
       * Context type of record triggers will determine which logic runs in each case
       * */
      switch (context.type) {
        case "create": {
          /* * * * * * * * * * * * * * * * * * * *
           * RUN ON EVERY EXECUTION CONTEXT TYPE
           *  * * * * * * * * *  * * * * * * * * * */

          setUpExhibitorFormItemsDescriptions(currentSalesOrder, context);

          let taxitem = currentSalesOrder.getValue("taxitem");
          let eventId,
            boothId,
            billParty,
            esoId,
            soRecWeb,
            eventData,
            doSingle = false,
            soID = currentSalesOrder.id;

          const getEventData = (eventId) => {
            return NG.tools.getLookupFields(
              "customrecord_show",
              eventId,
              csLib.settings._EVENT_FIELDS,
              csLib.settings._EVENT_FIELDS_S,
              []
            );
          };

          // Check to see if auth Item is there
          checkAuthItem();

          //region Handle Sales Tax Resetting
          log.audit({ title: "Running Tax Reset Check...", details: "⌛" });
          log.debug({
            title: "Current tax item AFTERSUB before reset:",
            details: taxitem,
          });

          // END OF ALL EXEC CONTEXT LOGIC

          log.audit({
            title: `✨ Running switch case ${runtime.executionContext}`,
            details: "afterSubmit()",
          });

          switch (runtime.executionContext) {
            /* * * * * * * * * * * * * * * * * * * * *
             * RUN ON SCOPED EXECUTION CONTEXT TYPE
             *  * * * * * * * * *  * * * * * * * * * */

            case `${runtime.ContextType.USER_INTERFACE}`: {
              // Run order create logic under Netsuite UI
              log.audit({
                title: "Running context AFTSUB on USERINTER...",
                details: "⌛",
              });
              let targetOrderId = esoId || soID;

              log.audit({ title: "Running deposit update...", details: "⌛" });
              updateDeposits(targetOrderId);
              log.audit({
                title: `Deposit updating on ${targetOrderId} completed! ✅`,
                details: "",
              });

              let currBalance = Number(
                currentSalesOrder.getValue({ fieldId: "custbody_balance" })
              );

              log.audit({
                title: `Current balance on ${targetOrderId}...`,
                details: `"${currBalance}"`,
              });

              log.audit({
                title: "Run order calculations from lib...",
                details: "⌛",
              });
              // let orderTotal = csLib.func.getExemptedTotal(soRecWeb, false, eventData);
              let orderTotal = currentSalesOrder.getValue("total");

              let totalPaid = csLib.func.getPaymentTotals(
                null,
                null,
                null,
                esoId || soID
              );

              let balance = NG.M.roundToHundredths(orderTotal - totalPaid);
              let finalBalance = balance.toFixed(2);

              log.audit({
                title: "Run order calculations from lib complete! ✅",
                details: {
                  total: orderTotal,
                  totalPaid,
                  balance,
                  finalBalance,
                },
              });

              log.audit({
                title: "Order Balance Update",
                details: {
                  currBalance: currBalance,
                  balance: balance,
                  hasBalance: balance !== 0,
                },
              });

              let updVals = {
                custbody_balance: finalBalance,
                custbody_hasbalance: balance !== 0,
              };

              if (
                csLib.settings.ClearBoothOrderCCDetails &&
                !NG.tools.isEmpty(
                  currentSalesOrder.getValue({ fieldId: "paymentmethod" })
                )
              ) {
                updVals = setCardClearValues(updVals);
              }

              record.submitFields({
                type: "salesorder",
                id: targetOrderId,
                values: updVals,
                options: { ignoreMandatoryFields: true, enableSourcing: true },
              });

              if (esoId) {
                try {
                  record.submitFields({
                    type: "salesorder",
                    id: soID,
                    values: { custbody_ng_cs_originating_order: esoId },
                    options: {
                      ignoreMandatoryFields: true,
                      enableSourcing: false,
                    },
                  });
                } catch (err) {
                  NG.log.logError(
                    err,
                    "Error encountered updating balance/pay info on order"
                  );
                }
              }
              log.audit({
                title: "USERINT Order Create Complete!",
                details: "",
              });
              // ORDER CREATE VIA UI END

              break;
            }
            case `${runtime.ContextType.WEBSTORE}`: {
              resetTaxToEvent();
              handleOrderTypeOnChange(context);

              // Stage web order variables
              log.audit({
                title: "External order via WEBSTORE processing...",
                details: "⌛",
              });
              soRecWeb = record.load({
                type: currentSalesOrder.type,
                id: soID,
              });

              if (soRecWeb)
                log.audit({
                  title: "Loaded Sales order! ✅",
                  details: soRecWeb,
                });
              else
                log.error({
                  title: "Sales order load failed ❗",
                  details: soRecWeb,
                });

              eventId = soRecWeb.getValue({ fieldId: "custbody_show_table" });
              boothId = soRecWeb.getValue({ fieldId: "custbody_booth" });
              billParty = soRecWeb.getValue({ fieldId: "entity" });

              // runServerSubsidiarySet(context, record) // Run subsidiary check

              let csJob = soRecWeb.getValue({
                fieldId: "custbody_cseg_ng_cs_job",
              });
              if (NG.tools.isEmpty(eventData)) {
                eventData = getEventData(eventId);
              }

              log.audit({
                title: "tax item id (AfterSubmit) (create)",
                details: soRecWeb.getValue({ fieldId: "taxitem" }),
              });

              if (NG.tools.isEmpty(eventId) || NG.tools.isEmpty(boothId)) {
                let eVal = "";
                if (NG.tools.isEmpty(eventId)) {
                  eVal = "event";
                } else if (NG.tools.isEmpty(boothId)) {
                  eVal = "booth";
                }

                let tranid = soRecWeb.getValue({ fieldId: "tranid" });
                if (
                  !NG.tools.isEmpty(csLib.settings.WebPaymentNoticeSender) &&
                  !NG.tools.isEmpty(csLib.settings.WebPaymentFailureRecipient)
                ) {
                  sendWebOrderFailureEmail({
                    subject: "Web Order Missing Show/Booth",
                    message: `Order # ${tranid} has been submitted via the web store but does not have an associated ${eVal}. Further order processing has been halted.`,
                  });
                }

                let err = error.create({
                  name: "NG_DATA_ERROR",
                  message:
                    "Show or booth information is missing from this order. Cannot continue order processing.",
                  notifyOff: "true",
                });
                NG.log.logError(
                  err,
                  "Error encountered on new web store order"
                );
                return;
              }

              let sSearch;
              log.audit({
                title: "existing order search affecting settings",
                details: {
                  PreventAdditionalOrders:
                    csLib.settings.PreventAdditionalOrders,
                  DefaultExhibitorOrderType:
                    csLib.settings.DefaultExhibitorOrderType,
                  AllowMultiBillingParties:
                    csLib.settings.AllowMultiBillingParties,
                },
              });
              if (csLib.settings.PreventAdditionalOrders) {
                let sFilt = [
                  ["custbody_show_table", "anyof", [eventId]],
                  "and",
                  ["custbody_booth", "anyof", [boothId]],
                  "and",
                  [
                    "custbody_ng_cs_order_type",
                    "anyof",
                    [csLib.settings.DefaultExhibitorOrderType],
                  ],
                  "and",
                  ["mainline", "is", "T"],
                  "and",
                  ["custbody_to_be_deleted", "is", "F"],
                ];
                if (csLib.settings.AllowMultiBillingParties) {
                  sFilt.push("and", ["entity", "anyof", [billParty]]);
                }

                log.audit({
                  title: "running search to find existing order",
                  details: "",
                });
                sSearch = NG.tools.getSearchResults("salesorder", sFilt, []);
                log.audit({
                  title: "existing order search is complete",
                  details: "",
                });
              }

              log.audit({
                title: "existing order search results",
                details: sSearch || "EMPTY",
              });
              if (!NG.tools.isEmpty(sSearch)) {
                if (sSearch.length > 0) {
                  let filtRes = sSearch.filter(
                    (x) => Number(x.id) !== Number(soID)
                  );
                  if (filtRes.length > 0) {
                    esoId = filtRes[0].id;
                    log.audit({
                      title: "'esoId' has been defined",
                      details: "curr so id: {0} -- esoId: {1}".NG_Format(
                        soID,
                        esoId
                      ),
                    });
                  } else {
                    doSingle = true;
                    log.audit({
                      title: "'esoId' does not need to be defined",
                      details: "esoId = currRec.id ({0})".NG_Format(soID),
                    });
                  }
                } else {
                  doSingle = true;
                  log.audit({
                    title: "'esoId' could not be defined",
                    details: "",
                  });
                }
              } else {
                doSingle = true;
                log.audit({
                  title: "'esoId' could not be defined",
                  details: "",
                });
              }
              // Stage order web variables end

              /* * * * * * * * * * * * * * * * * * * * * * * * * * * * *
               * Handle webstore processing and post record operations
               * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
              log.audit({ title: "Running WEBSTORE case ❗", details: "⌛" });
              soRecWeb = record.load({
                type: currentSalesOrder.type,
                id: soID,
              });
              eventId = soRecWeb.getValue({ fieldId: "custbody_show_table" });
              boothId = soRecWeb.getValue({ fieldId: "custbody_booth" });
              billParty = soRecWeb.getValue({ fieldId: "entity" });
              csJob = soRecWeb.getValue({ fieldId: "custbody_cseg_ng_cs_job" });

              if (NG.tools.isEmpty(eventId) || NG.tools.isEmpty(boothId)) {
                let flds = [
                  "custentity_last_web_show",
                  "custentity_last_web_booth",
                ];
                let cVal = NG.tools.getLookupFields(
                  "customer",
                  soRecWeb.getValue({ fieldId: "entity" }),
                  flds,
                  flds,
                  []
                );
                eventId = !NG.tools.isEmpty(cVal.custentity_last_web_show)
                  ? cVal.custentity_last_web_show
                  : null;
                boothId = !NG.tools.isEmpty(cVal.custentity_last_web_booth)
                  ? cVal.custentity_last_web_booth
                  : null;

                if (NG.tools.isEmpty(eventId) || NG.tools.isEmpty(boothId)) {
                  let eVal = "";
                  if (NG.tools.isEmpty(eventId)) {
                    eVal = "event";
                  } else if (NG.tools.isEmpty(boothId)) {
                    eVal = "booth";
                  }

                  let tranid = soRecWeb.getValue({ fieldId: "tranid" });
                  if (
                    !NG.tools.isEmpty(csLib.settings.WebPaymentNoticeSender) &&
                    !NG.tools.isEmpty(csLib.settings.WebPaymentFailureRecipient)
                  ) {
                    sendWebOrderFailureEmail({
                      subject: "Web Order Missing Show/Booth",
                      message:
                        "Order # {0} has been submitted via the web store but does not have an associated {1}. Further order processing has been halted.".NG_Format(
                          tranid,
                          eVal
                        ),
                    });
                  }
                  let err = error.create({
                    name: "NG_DATA_ERROR",
                    message:
                      "Show or booth information is missing from this order. Cannot continue order processing.",
                    notifyOff: "true",
                  });
                  NG.log.logError(
                    err,
                    "Error encountered on new web store order"
                  );
                  return;
                }
              }

              if (NG.tools.isEmpty(eventData)) {
                eventData = getEventData(eventId);
              }

              let newTotal = calcTotals(soRecWeb, eventData);
              log.audit({
                title: "Calc'd Pay Total",
                details: !NG.tools.isEmpty(newTotal) ? newTotal : "N/A",
              });

              let cCard = {
                exp: soRecWeb.getValue({ fieldId: "ccexpiredate" }),
                name: soRecWeb.getValue({ fieldId: "ccname" }),
                number: soRecWeb.getValue({ fieldId: "ccnumber" }),
                code: soRecWeb.getValue({ fieldId: "ccsecuritycode" }),
                adr: soRecWeb.getValue({ fieldId: "ccstreet" }),
                zip: soRecWeb.getValue({ fieldId: "cczipcode" }),
                method: soRecWeb.getValue({ fieldId: "paymentmethod" }),
                id: soRecWeb.getValue({ fieldId: "creditcard" }),
                encNumber: soRecWeb.getValue({
                  fieldId: "custbody_ng_paytrace_web_enc_cc_data",
                }),
              };

              if (doSingle) {
                let updValues = {};
                updValues = setCardClearValues(updValues);
                if (!NG.tools.isEmpty(csLib.settings.DefaultBoothOrderForm)) {
                  updValues["customform"] =
                    csLib.settings.DefaultBoothOrderForm;
                }

                // try
                // {
                let lookups = [
                  "custbody_ng_cs_order_type",
                  "class",
                  "custbody_booth_actual_exhibitor",
                ];
                if (csLib.settings.UseCustomJob) {
                  lookups.push("custbody_cseg_ng_cs_job");
                }
                if (NG.NSFeatures.DEPARTMENTS()) {
                  lookups.push("department");
                }
                let orderInfo = NG.tools.getLookupFields(
                  "salesorder",
                  soID,
                  lookups,
                  lookups,
                  []
                );
                // let altUpdValues = { };
                if (
                  NG.NSFeatures.DEPARTMENTS() &&
                  NG.tools.isEmpty(orderInfo.department)
                ) {
                  updValues["department"] =
                    csLib.settings.DefaultExhibitorDepartment;
                }
                if (NG.tools.isEmpty(orderInfo.custbody_ng_cs_order_type)) {
                  updValues["custbody_ng_cs_order_type"] =
                    csLib.settings.DefaultExhibitorOrderType;
                }

                log.audit({
                  title: "Is CS Job filled on order?",
                  details: !!orderInfo.custbody_cseg_ng_cs_job
                    ? `✅ - as "${orderInfo.custbody_cseg_ng_cs_job}"`
                    : "❌ - NO CS JOB",
                });

                log.audit({
                  title: "Is Class filled on order?",
                  details: ` ${!!orderInfo["class"]}`,
                });
                if (
                  NG.tools.isEmpty(orderInfo["class"]) ||
                  NG.tools.isEmpty(orderInfo.custbody_cseg_ng_cs_job)
                ) {
                  if (
                    NG.tools.isEmpty(orderInfo["class"]) &&
                    !NG.tools.isEmpty(eventData.custrecord_fin_show)
                  ) {
                    updValues["class"] = eventData.custrecord_fin_show;
                  }
                  log.audit({
                    title: "Event CS Job set as:",
                    details: `"${eventData.custrecord_show_job}"`,
                  });
                  if (
                    /*csLib.settings.UseCustomJob && NG.tools.isEmpty(orderInfo.custbody_cseg_ng_cs_job) &&*/ !NG.tools.isEmpty(
                      eventData.custrecord_show_job
                    )
                  ) {
                    log.audit({
                      title: "Updating Sales Order CS Job body field...",
                      details: `⚡ "${eventData.custrecord_show_job}"`,
                    });
                    updValues["custbody_cseg_ng_cs_job"] =
                      eventData.custrecord_show_job;
                  }
                }
                updValues["taxitem"] = eventData.custrecord_tax_rate;
                if (
                  NG.tools.isEmpty(orderInfo.custbody_booth_actual_exhibitor) &&
                  !NG.tools.isEmpty(boothId)
                ) {
                  let actExhib = NG.tools.getLookupFields(
                    "customrecord_show_booths",
                    boothId,
                    ["custrecord_booth_actual_exhibitor"],
                    ["custrecord_booth_actual_exhibitor"],
                    []
                  );
                  if (!NG.tools.isEmpty(actExhib)) {
                    updValues["custbody_booth_actual_exhibitor"] =
                      actExhib.custrecord_booth_actual_exhibitor;
                  }
                }
                // if (Object.keys(altUpdValues).length > 0)
                // {
                // 	try
                // 	{
                // 		log.audit({ title : "Setting updated mandatory values new order" , details : "" });
                // 		record.submitFields({ type : "salesorder" , id : soID , values : altUpdValues , options : { ignoreMandatoryFields : true , enableSourcing : true } });
                // 	}
                // 	catch (err)
                // 	{
                // 		NG.log.logError(err, "Error encountered updating web order with missing mandatory booth order values");
                // 	}
                // }
                // else
                // {
                // 	log.audit({ title : "Mandatory booth order values are not empty and do not need updating" , details : "" });
                // }
                // }
                // catch (err)
                // {
                // 	NG.log.logError(err, "Error encountered validating booth order fields");
                // }

                try {
                  log.audit({
                    title: "Updating order fields",
                    details: updValues,
                  });

                  record.submitFields({
                    type: "salesorder",
                    id: soID,
                    values: updValues,
                    options: {
                      ignoreMandatoryFields: true,
                      enableSourcing: false,
                    },
                  });
                } catch (err) {
                  NG.log.logError(
                    err,
                    "Error encountered updating web order with default custom form ID"
                  );
                }
              }

              let targetOrderId = esoId || soID;
              updateDeposits(targetOrderId);

              let currBalance = Number(
                soRecWeb.getValue({ fieldId: "custbody_balance" })
              );

              let orderTotal = csLib.func.getExemptedTotal(
                soRecWeb,
                false,
                eventData
              );
              let totalPaid = csLib.func.getPaymentTotals(
                null,
                null,
                null,
                esoId || soID
              );

              let balance = NG.M.roundToHundredths(
                orderTotal.total - totalPaid
              );
              let finalBalance = balance.toFixed(2);

              log.audit({
                title: "Order Balance Update",
                details: {
                  currBalance: currBalance,
                  balance: balance,
                  hasBalance: balance !== 0,
                },
              });

              let updVals = {
                custbody_balance: finalBalance,
                custbody_hasbalance: balance !== 0,
              };
              if (
                csLib.settings.ClearBoothOrderCCDetails &&
                !NG.tools.isEmpty(
                  soRecWeb.getValue({ fieldId: "paymentmethod" })
                )
              ) {
                updVals = setCardClearValues(updVals);
              }

              record.submitFields({
                type: "salesorder",
                id: targetOrderId,
                values: updVals,
                options: { ignoreMandatoryFields: true, enableSourcing: true },
              });

              if (!NG.tools.isEmpty(esoId)) {
                record.submitFields({
                  type: "salesorder",
                  id: soID,
                  values: { custbody_ng_cs_originating_order: esoId },
                  options: {
                    ignoreMandatoryFields: true,
                    enableSourcing: false,
                  },
                });
              }

              let currRec = record.load({ type: "salesorder", id: soID });
              let totalData = calcTotals(currRec, eventData);

              let updData = {
                show: eventId,
                booth: boothId,
                billParty: billParty,
                totalData: totalData,
                eventData: eventData,
                order: currRec,
              };

              let updTotalData = totalData;

              log.audit({
                title: "CS UE Sales Order SB - AS - totalData",
                details: totalData,
              });
              log.audit({
                title: "CS UE Sales Order SB - AS - updTotalData",
                details: updTotalData,
              });

              let boothData = NG.tools.getLookupFields(
                "customrecord_show_booths",
                boothId,
                [
                  "name",
                  "custrecord_booth_show_table",
                  "custrecord_booth_exhibitor",
                ],
                ["custrecord_booth_show_table", "custrecord_booth_exhibitor"],
                [],
                true
              );
              let orderData = {
                custbody_ng_cs_order_type: currRec.getValue({
                  fieldId: "custbody_ng_cs_order_type",
                }),
                exhibitor: billParty,
                exhibitorName: boothData.custrecord_booth_exhibitor_text,
                eventName: boothData.custrecord_booth_show_table_text,
                boothName: boothData.name,
              };

              log.audit({
                title: "Scheduling processing task - WEBSTORE",
                details: "⚡",
              });
              queueScheduledTask({
                soId: soID,
                esoId: esoId || "",
                noAuth: "T",
                authData: { total: newTotal, cCard: cCard },
                od: { updTotalData: updTotalData, orderData: orderData },
              });

              break;
            }
            case `${runtime.ContextType.RESTLET}`: {
              resetTaxToEvent();
              handleOrderTypeOnChange(context);

              // Stage web order variables
              log.audit({
                title: "External order via RESTLET processing...",
                details: "⌛",
              });
              soRecWeb = record.load({
                type: currentSalesOrder.type,
                id: soID,
              });

              if (soRecWeb)
                log.audit({
                  title: "Loaded Sales order! ✅",
                  details: soRecWeb,
                });
              else
                log.error({
                  title: "Sales order load failed ❗",
                  details: soRecWeb,
                });

              eventId = soRecWeb.getValue({ fieldId: "custbody_show_table" });
              boothId = soRecWeb.getValue({ fieldId: "custbody_booth" });
              billParty = soRecWeb.getValue({ fieldId: "entity" });

              // runServerSubsidiarySet(context, record) // Run subsidiary check

              let csJob = soRecWeb.getValue({
                fieldId: "custbody_cseg_ng_cs_job",
              });
              if (NG.tools.isEmpty(eventData)) {
                eventData = getEventData(eventId);
              }

              log.audit({
                title: "tax item id (AfterSubmit) (create)",
                details: soRecWeb.getValue({ fieldId: "taxitem" }),
              });

              if (NG.tools.isEmpty(eventId) || NG.tools.isEmpty(boothId)) {
                let eVal = "";
                if (NG.tools.isEmpty(eventId)) {
                  eVal = "event";
                } else if (NG.tools.isEmpty(boothId)) {
                  eVal = "booth";
                }

                let tranid = soRecWeb.getValue({ fieldId: "tranid" });
                if (
                  !NG.tools.isEmpty(csLib.settings.WebPaymentNoticeSender) &&
                  !NG.tools.isEmpty(csLib.settings.WebPaymentFailureRecipient)
                ) {
                  sendWebOrderFailureEmail({
                    subject: "Web Order Missing Show/Booth",
                    message:
                      "Order # {0} has been submitted via the web store but does not have an associated {1}. Further order processing has been halted.".NG_Format(
                        tranid,
                        eVal
                      ),
                  });
                }

                let err = error.create({
                  name: "NG_DATA_ERROR",
                  message:
                    "Show or booth information is missing from this order. Cannot continue order processing.",
                  notifyOff: "true",
                });
                NG.log.logError(
                  err,
                  "Error encountered on new web store order"
                );
                return;
              }

              let sSearch;
              log.audit({
                title: "existing order search affecting settings",
                details: {
                  PreventAdditionalOrders:
                    csLib.settings.PreventAdditionalOrders,
                  DefaultExhibitorOrderType:
                    csLib.settings.DefaultExhibitorOrderType,
                  AllowMultiBillingParties:
                    csLib.settings.AllowMultiBillingParties,
                },
              });
              if (csLib.settings.PreventAdditionalOrders) {
                let sFilt = [
                  ["custbody_show_table", "anyof", [eventId]],
                  "and",
                  ["custbody_booth", "anyof", [boothId]],
                  "and",
                  [
                    "custbody_ng_cs_order_type",
                    "anyof",
                    [csLib.settings.DefaultExhibitorOrderType],
                  ],
                  "and",
                  ["mainline", "is", "T"],
                  "and",
                  ["custbody_to_be_deleted", "is", "F"],
                ];
                if (csLib.settings.AllowMultiBillingParties) {
                  sFilt.push("and", ["entity", "anyof", [billParty]]);
                }
                try {
                  log.audit({
                    title: "running search to find existing order",
                    details: "",
                  });
                  sSearch = NG.tools.getSearchResults("salesorder", sFilt, []);
                  log.audit({
                    title: "existing order search is complete",
                    details: "",
                  });
                } catch (err) {
                  NG.log.logError(
                    err,
                    "Error encountered searching for existing sales orders"
                  );
                }
              }

              log.audit({
                title: "existing order search results",
                details: sSearch || "EMPTY",
              });
              if (!NG.tools.isEmpty(sSearch)) {
                if (sSearch.length > 0) {
                  let filtRes = sSearch.filter(
                    (x) => Number(x.id) !== Number(soID)
                  );
                  if (filtRes.length > 0) {
                    esoId = filtRes[0].id;
                    log.audit({
                      title: "'esoId' has been defined",
                      details: "curr so id: {0} -- esoId: {1}".NG_Format(
                        soID,
                        esoId
                      ),
                    });
                  } else {
                    doSingle = true;
                    log.audit({
                      title: "'esoId' does not need to be defined",
                      details: "esoId = currRec.id ({0})".NG_Format(soID),
                    });
                  }
                } else {
                  doSingle = true;
                  log.audit({
                    title: "'esoId' could not be defined",
                    details: "",
                  });
                }
              } else {
                doSingle = true;
                log.audit({
                  title: "'esoId' could not be defined",
                  details: "",
                });
              }
              // Stage order web variables end

              // Hande react order processing and consider paytrace integration
              // TRUE: runs normal paytrace post operations for processing
              // FALSE: run webstore context as nothing else is needed prior to post operations.
              log.audit({ title: "Running RESTLET case ❗", details: "⌛" });
              log.audit({
                title: "Session Data:",
                details: runtime.getCurrentSession(),
              });

              if (csLib.settings.EnablePayTrace) {
                log.audit({
                  title: "Paytrace is enabled❗ - Running paytrace operations",
                  details: "⌛",
                });
                // Pull out of else if block
                if (isNaN(Number(eventId))) {
                  eventId = soRecWeb.getValue({
                    fieldId: "custbody_show_table",
                  });
                }
                if (
                  eventData ||
                  (typeof eventData === "object" &&
                    Object.keys(eventData).length !== 0)
                ) {
                  eventData = getEventData(eventId);
                }

                let ordLines = soRecWeb.getLineCount({ sublistId: "item" });
                if (csLib.settings.SalesTaxOnItemLines) {
                  for (let l = 0; l < ordLines; l++) {
                    let taxName = soRecWeb.getSublistText({
                      sublistId: "item",
                      fieldId: "taxcode",
                      line: l,
                    });
                    let taxId = soRecWeb.getSublistValue({
                      sublistId: "item",
                      fieldId: "taxcode",
                      line: l,
                    });
                    let taxPct1 = soRecWeb.getSublistValue({
                      sublistId: "item",
                      fieldId: "taxrate1",
                      line: l,
                    });
                    let taxPct2 = soRecWeb.getSublistValue({
                      sublistId: "item",
                      fieldId: "taxrate2",
                      line: l,
                    });
                    log.audit({
                      title: "SO -- tax on line ({0})".NG_Format(l),
                      details:
                        "Tax: {0} ({1}) -- GST/HST: {2} -- PST: {3}".NG_Format(
                          taxName,
                          taxId,
                          taxPct1,
                          taxPct2
                        ),
                    });
                  }
                }

                try {
                  let authSessionId = `_CS_WEB_ORDER_${eventId}_${boothId}_${billParty}_`;
                  const sessData = runtime
                    .getCurrentSession()
                    .get({ name: authSessionId });
                  let authData;

                  log.audit({
                    title: "Session data:",
                    details: runtime.getCurrentSession(),
                  });
                  log.audit({ title: "Session data 64d:", details: sessData });

                  try {
                    if (Object.keys(sessData).length !== 0) {
                      let initJSON = encode.convert({
                        string: sessData,
                        inputEncoding: encode.Encoding.BASE_64,
                        outputEncoding: encode.Encoding.UTF_8,
                      });
                      let trimJSON = initJSON.slice(
                        0,
                        initJSON.lastIndexOf("}") + 1
                      );
                      authData = JSON.parse(trimJSON);
                    } else {
                      log.error({
                        title: "🚧 No session data order may fail!",
                        details: sessData,
                      });
                    }
                  } catch (err) {
                    NG.log.logError(
                      err,
                      "Error encountered processing session auth data"
                    );
                  }

                  runtime
                    .getCurrentSession()
                    .set({ name: authSessionId, value: "" });

                  log.audit({
                    title: "⚡ authData being passed...",
                    details: authData,
                  });

                  let updtVals = {
                    custbody_ng_cs_rcs_conv_fee: Number(
                      csLib.settings.AutoChargeWebOrders
                        ? authData.convFee || "0"
                        : "0"
                    ).toFixed(2),
                    taxitem: eventData.custrecord_tax_rate,
                  };

                  if (!NG.tools.isEmpty(esoId)) {
                    updtVals.custbody_ng_cs_originating_order = esoId;
                  }
                  try {
                    record.submitFields({
                      type: currentSalesOrder.type,
                      id: soID,
                      values: updtVals,
                      options: {
                        ignoreMandatoryFields: true,
                        enableSourcing: true,
                      },
                    });
                  } catch (err) {
                    NG.log.logError(
                      err,
                      "Error encountered updating balance/pay info on order"
                    );
                  }

                  log.audit({
                    title: "Scheduling processing task - REST with Paytrace",
                    details: "⚡",
                  });
                  queueScheduledTask({
                    soId: soID,
                    esoId: esoId || "",
                    authData: authData,
                  });
                } catch (err) {
                  NG.log.logError(
                    err,
                    "Error encountered during restlet order AfterSubmit processing"
                  );
                }
              } else {
                log.audit({
                  title:
                    "Paytrace is disabled❗ - Running non - paytrace operations",
                  details: "⌛",
                });
                soRecWeb = record.load({
                  type: currentSalesOrder.type,
                  id: soID,
                });
                eventId = soRecWeb.getValue({ fieldId: "custbody_show_table" });
                boothId = soRecWeb.getValue({ fieldId: "custbody_booth" });
                billParty = soRecWeb.getValue({ fieldId: "entity" });
                let csJob = soRecWeb.getValue({
                  fieldId: "custbody_cseg_ng_cs_job",
                });

                if (NG.tools.isEmpty(eventId) || NG.tools.isEmpty(boothId)) {
                  try {
                    let flds = [
                      "custentity_last_web_show",
                      "custentity_last_web_booth",
                    ];
                    let cVal = NG.tools.getLookupFields(
                      "customer",
                      soRecWeb.getValue({ fieldId: "entity" }),
                      flds,
                      flds,
                      []
                    );
                    eventId = !NG.tools.isEmpty(cVal.custentity_last_web_show)
                      ? cVal.custentity_last_web_show
                      : null;
                    boothId = !NG.tools.isEmpty(cVal.custentity_last_web_booth)
                      ? cVal.custentity_last_web_booth
                      : null;
                  } catch (err) {
                    /* do nothing */
                  }
                  if (NG.tools.isEmpty(eventId) || NG.tools.isEmpty(boothId)) {
                    let eVal = "";
                    if (NG.tools.isEmpty(eventId)) {
                      eVal = "event";
                    } else if (NG.tools.isEmpty(boothId)) {
                      eVal = "booth";
                    }

                    let tranid = soRecWeb.getValue({ fieldId: "tranid" });
                    if (
                      !NG.tools.isEmpty(
                        csLib.settings.WebPaymentNoticeSender
                      ) &&
                      !NG.tools.isEmpty(
                        csLib.settings.WebPaymentFailureRecipient
                      )
                    ) {
                      sendWebOrderFailureEmail({
                        subject: "Web Order Missing Show/Booth",
                        message:
                          "Order # {0} has been submitted via the web store but does not have an associated {1}. Further order processing has been halted.".NG_Format(
                            tranid,
                            eVal
                          ),
                      });
                    }
                    let err = error.create({
                      name: "NG_DATA_ERROR",
                      message:
                        "Show or booth information is missing from this order. Cannot continue order processing.",
                      notifyOff: "true",
                    });
                    NG.log.logError(
                      err,
                      "Error encountered on new web store order"
                    );
                    return;
                  }
                }

                if (NG.tools.isEmpty(eventData)) {
                  eventData = getEventData(eventId);
                }

                let newTotal = calcTotals(soRecWeb, eventData);
                log.audit({
                  title: "Calc'd Pay Total",
                  details: !NG.tools.isEmpty(newTotal) ? newTotal : "N/A",
                });

                let cCard = {
                  exp: soRecWeb.getValue({ fieldId: "ccexpiredate" }),
                  name: soRecWeb.getValue({ fieldId: "ccname" }),
                  number: soRecWeb.getValue({ fieldId: "ccnumber" }),
                  code: soRecWeb.getValue({ fieldId: "ccsecuritycode" }),
                  adr: soRecWeb.getValue({ fieldId: "ccstreet" }),
                  zip: soRecWeb.getValue({ fieldId: "cczipcode" }),
                  method: soRecWeb.getValue({ fieldId: "paymentmethod" }),
                  id: soRecWeb.getValue({ fieldId: "creditcard" }),
                  encNumber: soRecWeb.getValue({
                    fieldId: "custbody_ng_paytrace_web_enc_cc_data",
                  }),
                };

                if (doSingle) {
                  let updValues = {};
                  updValues = setCardClearValues(updValues);
                  if (!NG.tools.isEmpty(csLib.settings.DefaultBoothOrderForm)) {
                    updValues["customform"] =
                      csLib.settings.DefaultBoothOrderForm;
                  }

                  // try
                  // {
                  let lookups = [
                    "custbody_ng_cs_order_type",
                    "class",
                    "custbody_booth_actual_exhibitor",
                  ];
                  if (csLib.settings.UseCustomJob) {
                    lookups.push("custbody_cseg_ng_cs_job");
                  }
                  if (NG.NSFeatures.DEPARTMENTS()) {
                    lookups.push("department");
                  }
                  let orderInfo = NG.tools.getLookupFields(
                    "salesorder",
                    soID,
                    lookups,
                    lookups,
                    []
                  );
                  // let altUpdValues = { };
                  if (
                    NG.NSFeatures.DEPARTMENTS() &&
                    NG.tools.isEmpty(orderInfo.department)
                  ) {
                    updValues["department"] =
                      csLib.settings.DefaultExhibitorDepartment;
                  }
                  if (NG.tools.isEmpty(orderInfo.custbody_ng_cs_order_type)) {
                    updValues["custbody_ng_cs_order_type"] =
                      csLib.settings.DefaultExhibitorOrderType;
                  }
                  if (
                    NG.tools.isEmpty(orderInfo["class"]) ||
                    NG.tools.isEmpty(orderInfo.custbody_cseg_ng_cs_job)
                  ) {
                    if (
                      NG.tools.isEmpty(orderInfo["class"]) &&
                      !NG.tools.isEmpty(eventData.custrecord_fin_show)
                    ) {
                      updValues["class"] = eventData.custrecord_fin_show;
                    }
                    if (
                      /*csLib.settings.UseCustomJob && NG.tools.isEmpty(orderInfo.custbody_cseg_ng_cs_job) &&*/ !NG.tools.isEmpty(
                        eventData.custrecord_show_job
                      )
                    ) {
                      updValues["custbody_cseg_ng_cs_job"] =
                        eventData.custrecord_show_job;
                    }
                  }
                  updValues["taxitem"] = eventData.custrecord_tax_rate;
                  if (
                    NG.tools.isEmpty(
                      orderInfo.custbody_booth_actual_exhibitor
                    ) &&
                    !NG.tools.isEmpty(boothId)
                  ) {
                    try {
                      let actExhib = NG.tools.getLookupFields(
                        "customrecord_show_booths",
                        boothId,
                        ["custrecord_booth_actual_exhibitor"],
                        ["custrecord_booth_actual_exhibitor"],
                        []
                      );
                      if (!NG.tools.isEmpty(actExhib)) {
                        updValues["custbody_booth_actual_exhibitor"] =
                          actExhib.custrecord_booth_actual_exhibitor;
                      }
                    } catch (err) {
                      NG.log.logError(
                        err,
                        'Error encountered handling missing "Actual Exhibitor" value on order'
                      );
                    }
                  }
                  // if (Object.keys(altUpdValues).length > 0)
                  // {
                  // 	try
                  // 	{
                  // 		log.audit({ title : "Setting updated mandatory values new order" , details : "" });
                  // 		record.submitFields({ type : "salesorder" , id : soID , values : altUpdValues , options : { ignoreMandatoryFields : true , enableSourcing : true } });
                  // 	}
                  // 	catch (err)
                  // 	{
                  // 		NG.log.logError(err, "Error encountered updating web order with missing mandatory booth order values");
                  // 	}
                  // }
                  // else
                  // {
                  // 	log.audit({ title : "Mandatory booth order values are not empty and do not need updating" , details : "" });
                  // }
                  // }
                  // catch (err)
                  // {
                  // 	NG.log.logError(err, "Error encountered validating booth order fields");
                  // }

                  try {
                    log.audit({
                      title: "Updating order fields",
                      details: updValues,
                    });
                    record.submitFields({
                      type: "salesorder",
                      id: soID,
                      values: updValues,
                      options: {
                        ignoreMandatoryFields: true,
                        enableSourcing: false,
                      },
                    });
                  } catch (err) {
                    NG.log.logError(
                      err,
                      "Error encountered updating web order with default custom form ID"
                    );
                  }
                }

                let targetOrderId = esoId || soID;
                updateDeposits(targetOrderId);

                let currBalance = Number(
                  soRecWeb.getValue({ fieldId: "custbody_balance" })
                );

                let orderTotal = csLib.func.getExemptedTotal(
                  soRecWeb,
                  false,
                  eventData
                );
                let totalPaid = csLib.func.getPaymentTotals(
                  null,
                  null,
                  null,
                  esoId || soID
                );

                let balance = NG.M.roundToHundredths(
                  orderTotal.total - totalPaid
                );
                let finalBalance = balance.toFixed(2);

                log.audit({
                  title: "Order Balance Update",
                  details: {
                    currBalance: currBalance,
                    balance: balance,
                    hasBalance: balance !== 0,
                  },
                });

                let updVals = {
                  custbody_balance: finalBalance,
                  custbody_hasbalance: balance !== 0,
                };
                if (
                  csLib.settings.ClearBoothOrderCCDetails &&
                  !NG.tools.isEmpty(
                    soRecWeb.getValue({ fieldId: "paymentmethod" })
                  )
                ) {
                  updVals = setCardClearValues(updVals);
                }

                try {
                  record.submitFields({
                    type: "salesorder",
                    id: targetOrderId,
                    values: updVals,
                    options: {
                      ignoreMandatoryFields: true,
                      enableSourcing: true,
                    },
                  });
                } catch (err) {
                  NG.log.logError(
                    err,
                    "Error encountered updating balance/pay info on order"
                  );
                }

                if (!NG.tools.isEmpty(esoId)) {
                  try {
                    record.submitFields({
                      type: "salesorder",
                      id: soID,
                      values: { custbody_ng_cs_originating_order: esoId },
                      options: {
                        ignoreMandatoryFields: true,
                        enableSourcing: false,
                      },
                    });
                  } catch (err) {
                    NG.log.logError(
                      err,
                      "Error encountered updating balance/pay info on order"
                    );
                  }
                }

                let currRec = record.load({ type: "salesorder", id: soID });
                let totalData = calcTotals(currRec, eventData);

                let updData = {
                  show: eventId,
                  booth: boothId,
                  billParty: billParty,
                  totalData: totalData,
                  eventData: eventData,
                  order: currRec,
                };
                let updTotalData = totalData;

                log.audit({
                  title: "CS UE Sales Order SB - AS - totalData",
                  details: totalData,
                });
                log.audit({
                  title: "CS UE Sales Order SB - AS - updTotalData",
                  details: updTotalData,
                });

                let boothData = NG.tools.getLookupFields(
                  "customrecord_show_booths",
                  boothId,
                  [
                    "name",
                    "custrecord_booth_show_table",
                    "custrecord_booth_exhibitor",
                  ],
                  ["custrecord_booth_show_table", "custrecord_booth_exhibitor"],
                  [],
                  true
                );
                let orderData = {
                  custbody_ng_cs_order_type: currRec.getValue({
                    fieldId: "custbody_ng_cs_order_type",
                  }),
                  exhibitor: billParty,
                  exhibitorName: boothData.custrecord_booth_exhibitor_text,
                  eventName: boothData.custrecord_booth_show_table_text,
                  boothName: boothData.name,
                };

                log.audit({
                  title: "Scheduling processing task - REST non PT",
                  details: "⚡",
                });
                queueScheduledTask({
                  soId: soID,
                  esoId: esoId || "",
                  noAuth: "T",
                  authData: { total: newTotal, cCard: cCard },
                  od: { updTotalData: updTotalData, orderData: orderData },
                });
              }

              break;
            }
            case `${runtime.ContextType.PRINT}`: {
              break;
            }
            default:
              log.audit({
                title: "❗ context type not supported for CREATE",
                details: `"${runtime.executionContext}"`,
              });
          }
          break;
        }
        // Difference between "edit" & "xedit"
        // XEDIT = Inline edit of record - usually shown in the list view of records
        // EDIT = The literal opening of the record in edit mode and or editing from external scripting.
        case "xedit": {
          /* * * * * * * * * * * * * * * * * * * *
           * RUN ON EVERY EXECUTION CONTEXT TYPE
           *  * * * * * * * * *  * * * * * * * * * */

          // No execution scope set for this case

          oldRec = context.oldRecord;
          break;
        }
        case "edit": {
          /* * * * * * * * * * * * * * * * * * * *
           * RUN ON EVERY EXECUTION CONTEXT TYPE
           *  * * * * * * * * *  * * * * * * * * * */

          let loadedSalesOrder = record.load({
            type: record.Type.SALES_ORDER,
            id: currentSalesOrder.id,
          });

          setUpExhibitorFormItemsDescriptions(currentSalesOrder, context);
          let taxitem = currentSalesOrder.getValue("taxitem");
          oldRec = context.oldRecord;
          let eventId,
            boothId,
            billParty,
            esoId,
            soRecWeb,
            eventData,
            doSingle = false,
            soID = currentSalesOrder.id;
          let csJob = "";
          // Check to see if auth Item is there
          checkAuthItem();

          //region Handle Sales Tax Resetting
          log.audit({ title: "Running Tax Reset Check...", details: "⌛" });
          log.debug({
            title: "Current tax item AFTERSUB before reset:",
            details: taxitem,
          });

          // END OF ALL EXEC CONTEXT LOGIC

          switch (runtime.executionContext) {
            /* * * * * * * * * * * * * * * * * * * * *
             * RUN ON SCOPED EXECUTION CONTEXT TYPE
             *  * * * * * * * * *  * * * * * * * * * */

            case `${runtime.ContextType.USER_INTERFACE}`: {
              // Allow users to change tax rate or override the tax to there own rate...

              log.audit({
                title:
                  "Skipping Tax reset - allowing user interaction via NS UI ⚡",
                details: `🙋‍♂️ Context found: "${runtime.executionContext}"`,
              });

              loadedSalesOrder.setValue({
                fieldId: "taxitem",
                value: currTaxItem,
              });

              // Run balance update in the edit context.
              log.audit({
                title: "Running context AFTSUB on USERINTER...",
                details: "⌛",
              });
              let targetOrderId = esoId || soID;

              csJob = currentSalesOrder.getValue({
                fieldId: "custbody_cseg_ng_cs_job",
              });
              eventId = currentSalesOrder.getValue({
                fieldId: "custbody_show_table",
              });
              boothId = currentSalesOrder.getValue({
                fieldId: "custbody_booth",
              });
              billParty = currentSalesOrder.getValue({ fieldId: "entity" });
              log.debug({
                title: "Current tax item AFTERSUB:",
                details: taxitem,
              });

              if (!isNaN(Number(eventId))) {
                eventData = getEventData(eventId);
              }

              let currBalance = Number(
                currentSalesOrder.getValue({ fieldId: "custbody_balance" })
              );

              log.audit({
                title: `Current balance on ${targetOrderId}...`,
                details: `"${currBalance}"`,
              });

              let eventAndSalesRecordInfo = {
                eventData: { ...eventData },
                balance: currBalance,
                eventId,
                boothId,
                billParty,
                job: csJob,
              };

              log.debug({
                title: "🌍 All event and record details:",
                details: eventAndSalesRecordInfo,
              });

              log.audit({
                title: "Run order calculations from lib...",
                details: "⌛",
              });
              // NOT NEEDED AS THIS CAUSED ISSUES WITH CALC 🙋‍
              // let orderTotal = csLib.func.getExemptedTotal(soRecWeb, false, eventData);
              let orderTotal = currentSalesOrder.getValue("total");
              let totalPaid = csLib.func.getPaymentTotals(
                null,
                null,
                null,
                esoId || soID
              );

              let balance = NG.M.roundToHundredths(orderTotal - totalPaid);
              let finalBalance = balance.toFixed(2);

              log.audit({ title: "Running deposit update...", details: "⌛" });
              updateDeposits(targetOrderId);
              log.audit({
                title: `Deposit updating on ${targetOrderId} completed! ✅`,
                details: "",
              });

              log.audit({
                title: "Run order calculations from lib complete! ✅",
                details: {
                  total: orderTotal,
                  totalPaid,
                  balance,
                  finalBalance,
                },
              });

              log.audit({
                title: "Order Balance Update",
                details: {
                  currBalance: currBalance,
                  balance: balance,
                  hasBalance: balance !== 0,
                },
              });

              let updVals = {
                custbody_balance: finalBalance,
                custbody_hasbalance: balance !== 0,
                custbody_cseg_ng_cs_job: csJob,
              };

              if (
                csLib.settings.ClearBoothOrderCCDetails &&
                !NG.tools.isEmpty(
                  currentSalesOrder.getValue({ fieldId: "paymentmethod" })
                )
              ) {
                updVals = setCardClearValues(updVals);
              }

              try {
                // Set Balance fields

                currentSalesOrder.setValue(
                  "custbody_balance",
                  updVals.custbody_balance
                );
                currentSalesOrder.setValue(
                  "custbody_hasbalance",
                  updVals.custbody_hasbalance
                );

                record.submitFields({
                  type: record.Type.SALES_ORDER,
                  id: soID,
                  values: updVals,
                  options: {
                    ignoreMandatoryFields: true,
                    enableSourcing: false,
                  },
                });

                /*
                 *	loadedSalesOrder.save({
                 *		ignoreMandatoryFields: true
                 *	})
                 */
              } catch (err) {
                NG.log.logError(
                  err,
                  "Error encountered updating balance/pay info on order"
                );
              }

              log.audit({ title: "Balance Update Complete!", details: "✅" });
              // Balance update end

              break;
            }
            case `${runtime.ContextType.WEBSTORE}`: {
              break;
            }
            case `${runtime.ContextType.RESTLET}`: {
              break;
            }
            case `${runtime.ContextType.PRINT}`: {
              break;
            }
            default:
              log.audit({
                title: "❗ context type not supported for CREATE",
                details: `"${runtime.executionContext}"`,
              });
          }
          break;
        }
        case "delete": {
          /* * * * * * * * * * * * * * * * * * * *
           * RUN ON EVERY EXECUTION CONTEXT TYPE
           *  * * * * * * * * *  * * * * * * * * * */

          // No execution scope set yet.
          return; // skip rest of script
        }
      }

      // Calculate execution time
      let asEnd = new Date();
      let asDur = NG.time.calculateDuration(asStart, asEnd);
      log.audit({
        title: "CS UE Sales Order - Exec Duration (asDur) ",
        details: asDur,
      });
    } catch (err) {
      log.error({ title: "❌ AfterSubmit error occurred!", details: err });
      throw err;
    }
  };

  async function runBoothOrderBeforeSubmitOperations(context, activeBoothForm) {
    log.audit({ title: "⚡ Running BO BeforeSub Operations...", details: "" });
    let ccErr = false;
    let authErr = {};
    // try {
    let currentSalesOrder = context.newRecord;
    let eventId = currentSalesOrder.getValue({
      fieldId: "custbody_show_table",
    });
    let boothId = currentSalesOrder.getValue({ fieldId: "custbody_booth" });
    let billParty = currentSalesOrder.getValue({ fieldId: "entity" });
    var cCard = {};
    // cCard.code = "123";
    cCard.adr = currentSalesOrder.getValue({ fieldId: "ccstreet" });
    cCard.zip = currentSalesOrder.getValue({ fieldId: "cczipcode" });
    cCard.method = currentSalesOrder.getValue({ fieldId: "paymentmethod" });
    cCard.id = currentSalesOrder.getValue({ fieldId: "creditcard" });
    cCard.encNumber = currentSalesOrder.getValue({
      fieldId: "custbody_ng_paytrace_web_enc_cc_data",
    });

    log.audit({
      title: "Card id valid? - 💳",
      details: cCard?.id
        ? `Card ID ${cCard.id} is valid! - ✅`
        : `❌ Card id "${cCard.id}" is not a valid card.`,
    });

    if (activeBoothForm) {
      switch (context.type) {
        case "create":
          /* * * * * * * * * * * * * * * * * * * * * *
           *  RUN CONTEXT TYPE TO ALL EXEC CONTEXTS  *
           * * * * * * * * * * * * * * * * * * * * * */

          // SET UP EXHIBITOR FORM ITEMS START
          // Run exhibitor order reporting

          log.audit({
            title: "Loading values for exhibitor form...",
            details: "⌛",
          });

          setUpExhibitorFormItems(context, currentSalesOrder);
          // SET UP EXHIBITOR FORM ITEMS END

          switch (runtime.executionContext) {
            /* * * * * * * * * * * * * * * * * * * * * * *
								RUN CONTEXT TYPE APPLIED TO A EXEC SCOPE
							 * * * * * * * * * * * * * * * *  * * * * * */
            case "RESTLET":
              log.audit({
                title: `Running RESTLET case 🌟 - on "${context.type}"`,
                details: "",
              });

              cCard.exp = currentSalesOrder.getValue({
                fieldId: "ccexpiredate",
              });
              cCard.name = currentSalesOrder.getValue({ fieldId: "ccname" });
              cCard.code = currentSalesOrder.getValue({
                fieldId: "ccsecuritycode",
              });
              cCard.number = currentSalesOrder.getValue({
                fieldId: "ccnumber",
              }); // field unauthorized on suitelet
              cCard.method = currentSalesOrder.getValue({
                fieldId: "paymentmethod",
              }); // field unauthorized on suitelet
              setIsWebOrder(currentSalesOrder);

              // RUN SHIPPING ADDRESS UPDATE
              // runShippingAddressUpdate(context, eventId)
              // RUN SHIPPING ADDRESS UPDATE END

              // BEGIN ORDER PROCESSING
              await runWebOrderCreateProcessing(
                context,
                eventId,
                boothId,
                billParty,
                cCard
              )
                .then((r) => {
                  log.audit({
                    title: "⚡ Web order ops finished!",
                    details: r,
                  });
                })
                .catch((err) => {
                  log.error({
                    title: "❌ Internal web processing op error!",
                    details: err,
                  });
                  if (err?.name === "CREDIT_CARD_ERROR") {
                    authErr = err;
                    ccErr = true;
                  }
                });
              // ORDER PROCESSING END
              break;
            case "WEBSTORE":
              log.audit({
                title: `Running WEBSTORE case 🌟 - on "${context.type}"`,
                details: "",
              });

              cCard.exp = currentSalesOrder.getValue({
                fieldId: "ccexpiredate",
              });
              cCard.name = currentSalesOrder.getValue({ fieldId: "ccname" });
              cCard.code = currentSalesOrder.getValue({
                fieldId: "ccsecuritycode",
              });
              cCard.number = currentSalesOrder.getValue({
                fieldId: "ccnumber",
              }); // field unauthorized on suitelet
              setIsWebOrder(currentSalesOrder);

              // RUN SHIPPING ADDRESS UPDATE
              runShippingAddressUpdate(context, eventId);
              // RUN SHIPPING ADDRESS UPDATE END

              // BEGIN ORDER PROCESSING
              await runWebOrderCreateProcessing(
                context,
                eventId,
                boothId,
                billParty,
                cCard
              )
                .then((r) => {
                  log.audit({
                    title: "⚡ Web order ops finished!",
                    details: r,
                  });
                })
                .catch((err) => {
                  log.error({
                    title: "❌ Internal web processing op error!",
                    details: err,
                  });
                  if (err?.name === "CREDIT_CARD_ERROR") {
                    authErr = err;
                    ccErr = true;
                  }
                });
              // ORDER PROCESSING END
              break;
            case "USERINTERFACE":
              log.audit({
                title: `Running USERINT case 🌟 - on "${context.type}"`,
                details: "",
              });

              cCard.exp = currentSalesOrder.getValue({
                fieldId: "ccexpiredate",
              });
              cCard.name = currentSalesOrder.getValue({ fieldId: "ccname" });
              cCard.code = currentSalesOrder.getValue({
                fieldId: "ccsecuritycode",
              });
              cCard.number = currentSalesOrder.getValue({
                fieldId: "ccnumber",
              }); // field unauthorized on suitelet

              // BEGIN USER ORDER CREATE LOGIC
              runUserOrderCreateProcessing(
                context,
                eventId,
                billParty,
                boothId
              );
              // USER ORDER CREATE LOGIC END
              break;
            default:
              log.audit({ title: "No execution matching create", details: "" });
          }
          break;
        case "edit":
          /* * * * * * * * * * * * * * * * * * * *
           * RUN CONTEXT TYPE TO ALL EXEC CONTEXTS *
           * * * * * * * * * * * * * * * * * * * * */

          // SET UP EXHIBITOR FORM ITEMS START
          // Run exhibitor order reporting
          log.audit({
            title: "Loading values for exhibitor form...",
            details: "⌛",
          });

          setUpExhibitorFormItems(context, currentSalesOrder);
          // SET UP EXHIBITOR FORM ITEMS END

          switch (runtime.executionContext) {
            case "RESTLET":
              log.audit({
                title: `Running RESTLET case 🌟 - on "${context.type}"`,
                details: "",
              });

              // RUN SHIPPING ADDRESS UPDATE
              runShippingAddressUpdate(context, eventId);
              // RUN SHIPPING ADDRESS UPDATE END

              cCard.exp = currentSalesOrder.getValue({
                fieldId: "ccexpiredate",
              });
              cCard.name = currentSalesOrder.getValue({ fieldId: "ccname" });
              cCard.code = currentSalesOrder.getValue({
                fieldId: "ccsecuritycode",
              });
              cCard.number = currentSalesOrder.getValue({
                fieldId: "ccnumber",
              }); // field unauthorized on suitelet
              break;
            case "WEBSTORE":
              log.audit({
                title: `Running WEBSTORE case 🌟 - on "${context.type}"`,
                details: "",
              });

              // RUN SHIPPING ADDRESS UPDATE
              runShippingAddressUpdate(context, eventId);
              // RUN SHIPPING ADDRESS UPDATE END

              cCard.exp = currentSalesOrder.getValue({
                fieldId: "ccexpiredate",
              });
              cCard.name = currentSalesOrder.getValue({ fieldId: "ccname" });
              cCard.code = currentSalesOrder.getValue({
                fieldId: "ccsecuritycode",
              });
              cCard.number = currentSalesOrder.getValue({
                fieldId: "ccnumber",
              }); // field unauthorized on suitelet
              break;
            case "USERINTERFACE":
              log.audit({
                title: `Running USERINT case 🌟 - on "${context.type}"`,
                details: "",
              });

              cCard.exp = currentSalesOrder.getValue({
                fieldId: "ccexpiredate",
              });
              cCard.name = currentSalesOrder.getValue({ fieldId: "ccname" });
              cCard.code = currentSalesOrder.getValue({
                fieldId: "ccsecuritycode",
              });
              cCard.number = currentSalesOrder.getValue({
                fieldId: "ccnumber",
              }); // field unauthorized on suitelet
              currTaxItem = currentSalesOrder.getValue({ fieldId: "taxitem" });
              log.audit({
                title: "Collected current tax item being set! ⚡",
                details: currTaxItem,
              });

              // Shipping address update handled client side instead
              break;
            default:
              log.audit({ title: "No execution matching edit", details: "" });
          }
          break;
        case "view":
          /* * * * * * * * * * * * * * * * * * * *
           * RUN CONTEXT TYPE TO ALL EXEC CONTEXTS *
           * * * * * * * * * * * * * * * * * * * * */

          // SET UP EXHIBITOR FORM ITEMS START
          // Run exhibitor order reporting
          log.audit({
            title: "Loading values for exhibitor form...",
            details: "⌛",
          });

          setUpExhibitorFormItems(context, currentSalesOrder);
          // SET UP EXHIBITOR FORM ITEMS END

          switch (runtime.executionContext) {
            case "RESTLET":
              log.audit({
                title: `Running RESTLET case 🌟 - on "${context.type}"`,
                details: "",
              });
              break;
            case "WEBSTORE":
              log.audit({
                title: `Running WEBSTORE case 🌟 - on "${context.type}"`,
                details: "",
              });
              break;
            case "USERINTERFACE":
              log.audit({
                title: `Running USERINT case 🌟 - on "${context.type}"`,
                details: "",
              });
              break;
            default:
              log.audit({ title: "No execution matching view", details: "" });
          }
          break;
        case "delete":
          log.audit({ title: `Running delete case 🌟 `, details: "" });
          return; // Skip

        default:
          log.audit({
            title: "Context type not found!",
            details: `⚡ - "${context.type}"`,
          });
      }
      // Testing error output and values from error
      // ONly print on non delete contexts
      if (context.type !== "delete") {
        log.audit({
          title: "CS UE Sales Order - beforeSubmit() - CC Error true?",
          details: ccErr,
        });
      }
    } else {
      log.audit({
        title: "❗ Not an active booth ordering form skipping order logic...",
        details: "",
      });
    }

    log.debug({ title: "Is there a CC Error?", details: ccErr });
    log.debug({ title: "Is there a Auth Error?", details: authErr });

    if (ccErr && authErr?.message) {
      log.audit({ title: "Auth error being thrown ❗", details: "" });
      throw authErr;
    }

    // } catch (err) {
    // 	log.error({ title: '❌ Error running Web order processing Ops..', details: err })
    // 	throw err
    // }

    async function runWebOrderOperations(context, eventId, boothId, billParty) {
      log.audit({ title: "⚡ Running Web order operations", details: "" });
      let processingStatus = "Staging";
      //  TODO; ReplyJson not returning
      await runWebOrderCreateProcessing(context, eventId, boothId, billParty)
        .then((r) => {
          log.audit({
            title: "✔ Order Processing finished",
            details: `"${r}"`,
          });
          log.audit({ title: "✔ Order Processing response", details: r });
          processingStatus = r;
        })
        .catch((err) => {
          log.error({
            title: "❌ Error occurred running web processing!",
            details: err,
          });
          processingStatus = err;
        });
      return processingStatus;
    }

    async function runWebOrderCreateProcessing(
      context,
      eventId,
      boothId,
      billParty,
      cCard
    ) {
      // Applied to CREATE of REST & WEBSTORE exec contexts only!
      // Create auth item on test order to pre-authorize card
      // Then if authorization passes status then create actual order with the exhibitor's items
      // TODO; Figure out how to get ccErr and authErr accessable from main beforeSubmit method
      let currentSalesOrder = context.newRecord;
      let authErr,
        ccErr = false;
      // try {
      log.audit({
        title: "RWOP - ⚡ Running Web order processing BEFORESUB!",
        details: "",
      });
      log.debug({
        title: "RWOP - Finding sublist line with item:",
        details: csLib.settings.AuthItem,
      });

      let authLine = currentSalesOrder.findSublistLineWithValue({
        sublistId: "item",
        fieldId: "item",
        value: csLib.settings.AuthItem,
      });

      log.debug({
        title: "RWOP - Current Auth line item...",
        details: authLine,
      });

      if (authLine > 0) {
        log.audit({
          title: "RWOP - Authorization order; Terminating script functions",
          details: "",
        });
        return;
      }

      log.audit({
        title: `RWOP - ✨ Running switch case as ${runtime.executionContext}`,
        details: "WebProcessing - beforeSubmit()",
      });

      let eventData = {};
      let processingStatus = "Running Checks";
      // Set event Id upon init
      if (
        eventId &&
        !isNaN(Number(eventId)) &&
        Object.keys(eventData).length === 0
      ) {
        eventData = getEventData(eventId);
      }

      let rplyJSON = null;
      switch (runtime.executionContext) {
        case `${runtime.ContextType.RESTLET}`:
          let restProcessed = await runRestletWebProcessingContext(
            context,
            eventId,
            boothId,
            billParty,
            authLine,
            cCard
          );

          log.debug({
            title: "Rest Processed Return...!",
            details: restProcessed,
          });

          if (restProcessed?.rplyJSON) {
            rplyJSON = restProcessed.rplyJSON;
          }

          processingStatus = restProcessed.processingStatus;
          ccErr = restProcessed.ccErr;
          authErr = restProcessed.authErr;

          currentSalesOrder.setValue({ fieldId: "getauth", value: false });
          currentSalesOrder.setValue({ fieldId: "tobeemailed", value: false });

          break;
        case `${runtime.ContextType.WEBSTORE}`:
          // Checking context of Webstore order (sitebuilder)
          log.audit({
            title: "RWOP - Running webstore logic beforeSub...",
            details: "",
          });
          processingStatus = "Processing Webstore Order";
          if (isNaN(Number(eventId)) || isNaN(Number(boothId))) {
            // Check event id and booth id for valid #s
            if (isNaN(Number(eventId)))
              NG.log.logError(null, "WEB ORDER -- NO SHOW!!!!!");
            if (isNaN(Number(boothId)))
              NG.log.logError(null, "WEB ORDER -- NO BOOTH!!!!!");
          }
          log.audit({
            title: "RWOP - Setting default sales order form...",
            details: "⚡",
          });
          currentSalesOrder.setValue({
            fieldId: "customform",
            value: csLib.settings.DefaultBoothOrderForm,
          });

          let formIdSet = currentSalesOrder.getValue({ fieldId: "customform" });

          log.audit({
            title: "RWOP - Sales Order form set!",
            details: `🧾 - ${formIdSet}`,
          });

          /*
           * TODO: Why do we need to separate if statements to validate event and booth ids when beginning block captures it? - AKA: LINE: 513 🤷‍
           * */

          if (!isNaN(Number(boothId))) {
            currentSalesOrder.setValue({
              fieldId: "custbody_booth_actual_exhibitor",
              value: NG.tools.getLookupFields(
                "customrecord_show_booths",
                boothId,
                ["custrecord_booth_actual_exhibitor"],
                ["custrecord_booth_actual_exhibitor"],
                []
              ).custrecord_booth_actual_exhibitor,
            });
          }

          if (!isNaN(Number(eventId))) {
            if (Object.keys(eventData).length === 0) {
              eventData = getEventData(eventId);
            }
            // Check if CS JOB is enabled
            if (csLib.settings.UseCustomJob) {
              currentSalesOrder.setValue({
                fieldId: "class",
                value: eventData.custrecord_fin_show,
              });
              currentSalesOrder.setValue({
                fieldId: "custbody_cseg_ng_cs_job",
                value: eventData.custrecord_show_job,
              });
              log.audit({
                title: "RWOP - Web Order Job/Class",
                details: "Job ID: ${0} - Class ID: ${1}".NG_Format(
                  currentSalesOrder.getValue({
                    fieldId: "custbody_cseg_ng_cs_job",
                  }),
                  currentSalesOrder.getValue({ fieldId: "class" })
                ),
              });
            } else {
              currentSalesOrder.setValue({
                fieldId: "class",
                value: eventData.custrecord_fin_show,
              });
              log.audit({
                title: "RWOP - Web Order Job",
                details: "Job ID: {0}".NG_Format(
                  currentSalesOrder.getValue({ fieldId: "class" })
                ),
              });
            }
          }

          // Library automatically returns NULL if the select field isn't valid removed .isEmpty function
          // TODO: Will update to deprecate lib for settings and use SuiteQL to grab everything we need instead

          currentSalesOrder.setValue({
            fieldId: "custbody_ng_cs_order_type",
            value: csLib.settings.DefaultExhibitorOrderType,
          });

          log.audit({
            title: "RWOP - Web Order Type Set!",
            details: `Order Type ID: ${currentSalesOrder.getValue({
              fieldId: "custbody_ng_cs_order_type",
            })}`,
          });

          currentSalesOrder.setValue({ fieldId: "getauth", value: false });
          currentSalesOrder.setValue({ fieldId: "tobeemailed", value: false });

          break;
        default:
          log.audit({
            title: "No execution context was ran for web order",
            details: "",
          });
      }

      return processingStatus;
      // } catch (err) {
      // 	log.error({ title: 'RWOP - Internal Error running processing!', details: err })
      // 	throw err
      // }
    }

    if (ccErr && authErr?.message) {
      throw authErr;
    }
  }

  async function runRestletWebProcessingContext(
    context,
    eventId,
    boothId,
    billParty,
    authLine,
    cCard
  ) {
    /*
     * Set isweborder to true to accomplish the following:
     * - Only on CREATE of the new sales order will this code block run
     * 1. Grab all the event information that has been set on the sales order to then be used later
     * 2. Check tax rate assigned to event if it has been set (expecting true) while tax on line item lines is true along with canadian tax is true
     * 3. Iterate through each item that is not the convienence fee item set the tax on that item from the event
     *    and if candian tax it not equipped set the taxrate1 field on the item with the float value removing the "%" to make it a decimal number instead.
     *    Else if there is canadian tax use the custom tax fields to apply that tax to each item.
     * 4. Then tax group gets set from event when group is defined and rate is defined
     *
     * */
    let currentSalesOrder = context.newRecord;
    let processingStatus = "Running Sales Order Record Value Sets";
    log.audit({ title: "RWOP - Case RESTLET running...", details: "⌛" });
    eventId =
      eventId || currentSalesOrder.getValue({ fieldId: "custbody_show_table" });
    boothId =
      boothId || currentSalesOrder.getValue({ fieldId: "custbody_booth" });
    billParty = billParty || currentSalesOrder.getValue({ fieldId: "entity" });
    let orderTax = currentSalesOrder.getValue({ fieldId: "taxitem" });
    let lineNumber = 0;
    let rplyJSON = null;
    let ccErr = false;
    let authErr = {};
    let eventData = {};
    let orderProcessingReturn = {
      rplyJSON,
      processingStatus,
      ccErr,
      authErr,
    };

    // try {
    // Set event Id upon init
    if (
      eventId &&
      !isNaN(Number(eventId)) &&
      Object.keys(eventData).length === 0
    ) {
      eventData = getEventData(eventId);
    }

    // TODO: Rewrite to include better logic in handling tax
    // Both settings fields reside on Canadian users
    if (
      eventData.custrecord_tax_rate &&
      (csLib.settings.SalesTaxOnItemLines || csLib.settings.UseCanadianSalesTax)
    ) {
      // Set each items tax code when iterating on items in the order.
      let itemLineCount = currentSalesOrder.getLineCount({ sublistId: "item" });
      for (lineNumber = 0; lineNumber < itemLineCount; lineNumber++) {
        let currentItem = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "item",
          line: lineNumber,
        });

        if (currentItem !== csLib.settings.ConvenienceFeeItem) {
          currentSalesOrder.setSublistValue({
            sublistId: "item",
            fieldId: "taxcode",
            value: eventData.custrecord_tax_rate,
            line: lineNumber,
          });
          // Check if settings holds NON Canadian Tax to then set from the CS Event
          if (!csLib.settings.UseCanadianSalesTax) {
            currentSalesOrder.setSublistValue({
              sublistId: "item",
              fieldId: "taxrate1",
              value: eventData.custrecord_tax_percent.replace("%", ""),
              line: lineNumber,
            });
          } else {
            currentSalesOrder.setSublistValue({
              sublistId: "item",
              fieldId: "taxrate1",
              value: eventData.custrecord_ng_cs_evt_gst_pct.replace("%", ""),
              line: lineNumber,
            });
            currentSalesOrder.setSublistValue({
              sublistId: "item",
              fieldId: "taxrate2",
              value: eventData.custrecord_ng_cs_evt_pst_pct.replace("%", ""),
              line: lineNumber,
            });
          }
        } else {
          currentSalesOrder.setSublistValue({
            sublistId: "item",
            fieldId: "taxcode",
            value: csLib.settings.ConvFeeTax,
            line: lineNumber,
          });
          if (!csLib.settings.UseCanadianSalesTax) {
            currentSalesOrder.setSublistValue({
              sublistId: "item",
              fieldId: "taxrate1",
              value: "0",
              line: lineNumber,
            });
          } else {
            currentSalesOrder.setSublistValue({
              sublistId: "item",
              fieldId: "taxrate1",
              value: "0",
              line: lineNumber,
            });
            currentSalesOrder.setSublistValue({
              sublistId: "item",
              fieldId: "taxrate2",
              value: "0",
              line: lineNumber,
            });
          }
        }
      }
    }

    // This will apply to US users to apply tax on the order instead.
    else if (
      eventData?.custrecord_tax_rate &&
      !isNaN(Number(eventData?.custrecord_tax_rate)) &&
      !isNaN(Number(orderTax)) &&
      eventData?.custrecord_tax_rate &&
      Number(eventData?.custrecord_tax_rate) !== Number(orderTax)
    ) {
      currentSalesOrder.setValue({
        fieldId: "taxitem",
        value: eventData.custrecord_tax_rate,
      });
    }

    // If paytrace is enabled run paytrace pre step logic in order to process transaction
    // Else run the webstore processing method fit for non-paytrace payments.
    log.audit({
      title: "RWOP - Checking if paytrace is enabled...",
      details: "⌛",
    });
    processingStatus = "Running Web Processing";
    if (csLib.settings.EnablePayTrace) {
      processingStatus = "Running Paytrace Processing";

      log.audit({
        title:
          "RWOP - Paytrace is enabled! - Running Paytrace beforeSubmit processes.",
        details: "⤵",
      });

      if (csLib.settings.AutoChargeWebOrders) {
        log.audit({
          title:
            "RWOP - AuthCharge is enabled! - Running Paytrace beforeSubmit processing steps.",
          details: "⌛",
        });

        log.audit({
          title: "RWOP - CS UE Sales Order - BS - cCard",
          details: cCard,
        });

        let totalData = calcTotals(currentSalesOrder, eventData);

        let updData = {
          show: eventId,
          booth: boothId,
          billParty: billParty,
          totalData: totalData,
          eventData: eventData,
          order: currentSalesOrder,
        };

        let updTotalData = totalData;

        log.audit({
          title: "RWOP - CS UE Sales Order - BS - totalData",
          details: totalData,
        });

        log.audit({
          title: "RWOP - CS UE Sales Order - BS - updTotalData",
          details: updTotalData,
        });

        // try {
        let boothData = NG.tools.getLookupFields(
          "customrecord_show_booths",
          boothId,
          ["name", "custrecord_booth_show_table", "custrecord_booth_exhibitor"],
          ["custrecord_booth_show_table", "custrecord_booth_exhibitor"],
          [],
          true
        );

        log.audit({
          title: "RWOP - CS UE Sales Order - BS - boothData",
          details: boothData,
        });

        let orderData = {
          custbody_ng_cs_order_type: currentSalesOrder.getValue({
            fieldId: "custbody_ng_cs_order_type",
          }),
          exhibitor: billParty,
          exhibitorName: boothData.custrecord_booth_exhibitor_text,
          eventName: boothData.custrecord_booth_show_table_text,
          boothName: boothData.name,
        };

        log.audit({
          title: "RWOP - CS UE Sales Order - BS - orderData",
          details: orderData,
        });

        await https.request
          .promise({
            method: https.Method.POST,
            url: paySuiteletUrl,
            body: {
              soid: null,
              total: updTotalData.total,
              totalInit: updTotalData.totalInit,
              tax: updTotalData.tax,
              taxInit: updTotalData.taxInit,
              card: JSON.stringify(cCard),
              ordData: JSON.stringify(orderData),
              getAuth: "T",
              subtNickel: "F",
            },
          })
          .then((payRes) => {
            log.audit({
              title: "✅ Return from POST (payRes):",
              details: payRes,
            });
            if (payRes && payRes.code === 200 && payRes?.body) {
              rplyJSON = JSON.parse(payRes.body);
              if (rplyJSON?.status && rplyJSON.status !== "OK") {
                // require(['N/error'], function(error) {
                log.audit({
                  title:
                    "RWOP - CS UE Sales Order - BS - Throwing card auth error (A)",
                  details: "",
                });

                orderProcessingReturn = {
                  rplyJSON,
                  processingStatus,
                  ccErr,
                  authErr,
                };

                throw error.create({
                  name: "CREDIT_CARD_ERROR",
                  message: rplyJSON?.message,
                  notifyOff: true,
                });
                // authErr = new Error(rplyJSON.name, { cause : rplyJSON.message });
                // log.audit({ title : "authErr (A)" , details : authErr });
                // });
              } else if (rplyJSON) {
                let authSessionId = `_CS_WEB_ORDER_${eventId}_${boothId}_${billParty}_`;
                runtime.getCurrentSession().set({
                  name: authSessionId,
                  value: encode.convert({
                    string: JSON.stringify(rplyJSON),
                    inputEncoding: encode.Encoding.UTF_8,
                    outputEncoding: encode.Encoding.BASE_64,
                  }),
                });
                log.audit({
                  title: "RWOP - CS UE Sales Order - BS - Session Data Set",
                  details: "",
                });

                orderProcessingReturn = {
                  rplyJSON,
                  processingStatus,
                  ccErr,
                  authErr,
                };
              } else {
                // require(['N/error'], function(error) {
                log.audit({
                  title:
                    "RWOP - CS UE Sales Order - BS - Throwing card auth error (B)",
                  details: "",
                });

                orderProcessingReturn = {
                  rplyJSON,
                  processingStatus,
                  ccErr,
                  authErr,
                };

                throw error.create({
                  name: "NG_CS_ERROR",
                  message: "Unable to authorize credit card",
                  notifyOff: true,
                });

                // authErr = new Error("NG_CS_ERROR", { cause : "Unable to authorize credit card" });
                // log.audit({ title : "authErr (B)" , details : authErr });
                // });
              }
            }
          })
          .catch((err) => {
            log.error({
              title: "❌ Error Authing Paytrace Post:",
              details: err,
            });
            ccErr = true;

            authErr = error.create({
              name: "CREDIT_CARD_ERROR",
              message: err?.message || err,
              notifyOff: true,
            });

            orderProcessingReturn = {
              rplyJSON,
              processingStatus,
              ccErr,
              authErr,
            };

            throw authErr;
          });

        log.audit({
          title: "RWOP - CS UE Sales Order - BS - payRes",
          details: payRes,
        });

        // } catch (err) {
        // 	// Going to leave this just in case anything else fails prior to the POST request.
        // 	orderProcessingReturn = {
        // 		rplyJSON,
        // 		processingStatus,
        // 		ccErr,
        // 		authErr
        // 	}
        // 	log.error({title: '❌ An Error occurred with payment', details: err})
        // 	NG.log.logError(err, "RWOP - Error encountered authing web order for full amount");
        // }
      } else if (
        !csLib.settings.AutoChargeWebOrders &&
        !NG.tools.isEmpty(csLib.settings.AuthItem)
      ) {
        //
      }
    } else {
      // Run order process as webstore process
      processingStatus = "Running Non PT Processing";
      log.audit({
        title: "RWOP - Running non paytrace logic beforeSub...",
        details: "",
      });

      // Run authorization for order
      log.audit({
        title: "RWOP - Running non PT Authorization...",
        details: "⌛",
      });
      log.debug({ title: "RWOP - Auth Line:", details: authLine });
      if (authLine) {
        log.audit({ title: "RWOP - Auth line exists!", details: "✅" });
        log.audit({ title: "RWOP - Running Auth POST...", details: "⌛" });
        log.debug({ title: "cCard defined as:", details: cCard });
        log.debug({
          title: "RWOP - Running Auth POST to body...",
          details: {
            body: {
              cstId: billParty,
              card: JSON.stringify(cCard),
            },
          },
        });

        await https.request
          .promise({
            method: https.Method.POST,
            url: authSuiteletUrl,
            body: {
              cstId: billParty,
              card: JSON.stringify(cCard),
            },
          })
          .then((authRes) => {
            log.audit({ title: "RWOP - POST finished...", details: authRes });
            if (authRes?.body && authRes.code === 200) {
              let status = JSON.parse(authRes.body)?.status;
              if (status === "SUCCESS") {
                log.audit({
                  title: "RWOP - POST finished successfully! ✅",
                  details: status,
                });

                if (isNaN(Number(eventId)) || isNaN(Number(boothId))) {
                  // Check event id and booth id for valid #s
                  if (isNaN(Number(eventId)))
                    NG.log.logError(null, "WEB ORDER (non PT) -- NO SHOW!!!!!");
                  if (isNaN(Number(boothId)))
                    NG.log.logError(
                      null,
                      "WEB ORDER (non PT) -- NO BOOTH!!!!!"
                    );
                }

                log.audit({
                  title: "RWOP - Setting default sales order form...",
                  details: "⚡",
                });
                currentSalesOrder.setValue({
                  fieldId: "customform",
                  value: csLib.settings.DefaultBoothOrderForm,
                });

                let formIdSet = currentSalesOrder.getValue({
                  fieldId: "customform",
                });

                log.audit({
                  title: "RWOP - Sales Order form set!",
                  details: `🧾 - ${formIdSet}`,
                });

                /*
                 * TODO: Why do we need to separate if statements to validate event and booth ids when beginning block captures it? - AKA: LINE: 513 🤷‍
                 * */

                log.debug({
                  title:
                    "RWOP - 🔎 Checking booth id and gonna set actual exhibitor",
                  details: boothId,
                });

                processingStatus = "Finalizing Event & Booth Set Up";

                if (boothId && !isNaN(Number(boothId))) {
                  processingStatus = "Setting booth on order";
                  let boothLookup = search.lookupFields({
                    type: "customrecord_show_booths",
                    id: boothId,
                    columns: ["custrecord_booth_actual_exhibitor"],
                  });
                  log.debug({
                    title: "RWOP - 🔎 Booth lookup results...",
                    details: boothLookup,
                  });
                  currentSalesOrder.setValue({
                    fieldId: "custbody_booth_actual_exhibitor",
                    value:
                      boothLookup?.custrecord_booth_actual_exhibitor[0].value,
                  });
                  log.debug({
                    title: "RWOP - 🌟 Set Booth exhibitor as:",
                    details:
                      boothLookup?.custrecord_booth_actual_exhibitor[0].value,
                  });
                }

                log.debug({
                  title:
                    "RWOP - 🔎 Checking event ID and setting job & class...",
                  details: `Event: "${eventId}"`,
                });

                if (eventId && !isNaN(Number(eventId))) {
                  let currentJob = currentSalesOrder.getValue(
                    "custbody_cseg_ng_cs_job"
                  );
                  log.debug({
                    title: "RWOP - Is event data existent?",
                    details: eventData,
                  });
                  processingStatus = "Set Booth - Setting event";
                  if (Object.keys(eventData).length === 0) {
                    eventData = getEventData(eventId);
                  }
                  // Check if CS JOB is enabled
                  if (csLib.settings.UseCustomJob) {
                    log.debug({
                      title: "RWOP - 🔗 Set CSJOB & class from event data:",
                      details: `Job: "${eventData?.custrecord_show_job}" - Class: "${eventData?.custrecord_fin_show}"`,
                    });

                    if (!currentJob) {
                      currentSalesOrder.setValue({
                        fieldId: "custbody_cseg_ng_cs_job",
                        value: eventData.custrecord_show_job,
                      });
                    }

                    // Don't have to set class but if is there mines well set it on the order
                    if (eventData?.custrecord_fin_show) {
                      currentSalesOrder.setValue({
                        fieldId: "class",
                        value: eventData.custrecord_fin_show,
                      });
                    }

                    log.audit({
                      title: "RWOP - Web Order Job/Class",
                      details: "Job ID: {0} - Class ID: {1}".NG_Format(
                        currentSalesOrder.getValue({
                          fieldId: "custbody_cseg_ng_cs_job",
                        }),
                        currentSalesOrder.getValue({ fieldId: "class" })
                      ),
                    });
                  } else {
                    currentSalesOrder.setValue({
                      fieldId: "class",
                      value: eventData.custrecord_fin_show,
                    });
                    log.audit({
                      title: "RWOP - Web Order Job",
                      details: "Job ID: {0}".NG_Format(
                        currentSalesOrder.getValue({ fieldId: "class" })
                      ),
                    });
                  }
                  processingStatus = "Event Set Moving to Order type";
                }

                // Library automatically returns NULL if the select field isn't valid removed .isEmpty function
                // TODO: Will update to deprecate lib for settings and use SuiteQL to grab everything we need instead

                currentSalesOrder.setValue({
                  fieldId: "custbody_ng_cs_order_type",
                  value: csLib.settings.DefaultExhibitorOrderType,
                  ignoreFieldChange: true,
                });

                processingStatus = "Order type set";
                log.audit({
                  title: "RWOP - Web Order Type Set!",
                  details: `Order Type ID: ${currentSalesOrder.getValue({
                    fieldId: "custbody_ng_cs_order_type",
                  })}`,
                });
              }
              processingStatus = "Processing Finished";

              orderProcessingReturn = {
                rplyJSON,
                processingStatus,
                ccErr,
                authErr,
              };
            } else {
              // Want to place a THROW here to halt order
              log.error({
                title: "RWOP - ❌ Error occurred when authing card:",
                details: authRes,
              });

              orderProcessingReturn = {
                rplyJSON,
                processingStatus,
                ccErr,
                authErr,
              };

              throw {
                name: "CREDIT_CARD_ERROR",
                message: authRes.details,
                options: {
                  code: authRes.code,
                  cause: authRes,
                },
              };
            }
          })
          .catch((err) => {
            processingStatus = "Error authing card";
            log.error({
              title: "RWOP - ❌ Internal error occurred when authing card:",
              details: err,
            });

            orderProcessingReturn = {
              rplyJSON,
              processingStatus,
              ccErr,
              authErr,
            };

            throw error.create({
              name: "CREDIT_CARD_ERROR",
              message: "Internal error occurred when authorizing card.",
            });
          });
      }
    }

    return orderProcessingReturn;

    // } catch (err) {
    // 	log.audit({title: "RWOP - try-catch-err", details: "try-catch-err"});
    // 	log.audit({title: "RWOP - err", details: {name: err.name, message: err.message}});
    // 	orderProcessingReturn = {
    // 		rplyJSON,
    // 		processingStatus,
    // 		ccErr,
    // 		authErr,
    // 		err,
    // 	}
    // 	throw err
    // }
  }

  const setUpExhibitorFormItems = (context, currentSalesOrder) => {
    // SET UP EXHIBITOR FORM ITEMS START
    let lineNumber = 0;
    log.audit({
      title: "EF - Exhibitor Form setting item values (create)...",
      details: `⌛ - In context: "${context.type}"`,
    });

    log.debug({ title: "EF - Get item count...", details: `⌛` });

    let lines = currentSalesOrder.getLineCount({ sublistId: "item" });

    lines &&
      log.audit({
        title: "EF - Get item count gathered! ✅",
        details: `🌟 - ${lines}`,
      });

    for (lineNumber = 0; lineNumber < lines; lineNumber++) {
      log.debug({
        title: "EF - Looping item line 🤏",
        details: `Item: ${lineNumber}`,
      });

      log.audit({
        title: "EF - Setting Current Line Code 🔢",
        details: `⌛ - In context: "${context.type}"`,
      });

      let currLineCode = currentSalesOrder.getSublistValue({
        sublistId: "item",
        fieldId: "custcol_linecode",
        line: lineNumber,
      });

      if (currLineCode)
        log.audit({
          title: "EF - Current Line Code Set! ✅",
          details: `Code: ${currLineCode}`,
        });
      else
        log.audit({
          title: "EF - Current Line Code Not Set❗",
          details: `Invalid Code: ${currLineCode}`,
        });

      log.audit({
        title: "EF - Setting Current Item Type...",
        details: `⌛ - In context: "${context.type}"`,
      });

      let currLineItemType = currentSalesOrder.getSublistValue({
        sublistId: "item",
        fieldId: "itemtype",
        line: lineNumber,
      });

      if (currLineItemType)
        log.audit({
          title: "Current Item Type Set! ✅",
          details: `Code: ${currLineItemType}`,
        });
      else
        log.audit({
          title: "Current Item Type Not Set❗",
          details: `❌ Invalid Type: ${currLineItemType}`,
        });

      log.audit({
        title: "Checking EndGroup and line code def...",
        details: "⌛",
      });
      if (
        typeof currLineCode === "string" &&
        currLineCode &&
        currLineItemType !== "EndGroup"
      ) {
        log.audit({
          title: "EF - Setting random string value for line code...",
          details: "⌛",
        });
        currentSalesOrder.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_linecode",
          value:
            NG.tools.randomString(6, 6) +
            currentSalesOrder.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: lineNumber,
            }) +
            lineNumber.toString(),
          line: lineNumber,
        });

        log.audit({ title: "Getting item description...", details: "⌛" });

        let currLineDesc = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "description",
          line: lineNumber,
        });

        if (currLineDesc)
          log.audit({
            title: "EF - Item description valid! ✅",
            details: `"${currLineDesc}"`,
          });
        else
          log.audit({
            title: "EF - Item description invalid! ❌",
            details: currLineDesc,
          });

        log.audit({
          title: "EF - Setting description on item...",
          details: `⌛`,
        });

        let item_desc_marked = currentSalesOrder.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_description",
          value: currLineDesc,
          line: lineNumber,
        });

        if (item_desc_marked)
          log.audit({
            title: "EF - Set description on item! ✅",
            details: `Desc: ${item_desc_marked}`,
          });
        else
          log.audit({
            title: "EF - Set description on item failed! ❌",
            details: `Desc: ${item_desc_marked}`,
          });
      }
    }
    // SET UP EXHIBITOR FORM ITEMS END

    // SET UP EXHIBITOR FORM ITEMS START
    lineNumber = 0;

    log.audit({
      title: "EF - Exhibitor Form setting item desc (create)...",
      details: `⌛ - In context: "${context.type}"`,
    });

    log.debug({ title: "EF - Get item count...", details: `⌛` });

    lines = currentSalesOrder.getLineCount({ sublistId: "item" });

    lines &&
      log.audit({
        title: "EF - Get item count gathered! ✅",
        details: `🌟 - ${lines}`,
      });

    // Set custcol_description for booth order printing form
    if (lines > 0) {
      for (lineNumber = 0; lineNumber < lines; lineNumber++) {
        log.debug({
          title: "EF - Looping item line descriptions 🤏",
          details: `Item: ${lineNumber}`,
        });

        log.audit({
          title: "EF - Setting Current Line Code 🔢",
          details: `⌛ - In context: "${context.type}"`,
        });

        let currLineCode = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_linecode",
          line: lineNumber,
        });

        if (currLineCode)
          log.audit({
            title: "EF - Current Line Code Set! ✅",
            details: `Code: ${currLineCode}`,
          });
        else
          log.audit({
            title: "EF - Current Line Code Not Set ❗",
            details: `Invalid Code: ${currLineCode}`,
          });

        log.audit({
          title: "EF - Setting Current Item Type...",
          details: `⌛ - In context: "${context.type}"`,
        });

        let currLineItemType = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "itemtype",
          line: lineNumber,
        });

        if (currLineItemType)
          log.audit({
            title: "EF - Current Item Type Set! ✅",
            details: `Code: ${currLineItemType}`,
          });
        else
          log.audit({
            title: "EF - Current Item Type Not Set❗",
            details: `❌ Invalid Type: ${currLineItemType}`,
          });

        log.audit({ title: "EF - Getting item description...", details: "⌛" });

        let currLineName = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "item_display",
          line: lineNumber,
        });

        let currLineDesc = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "description",
          line: lineNumber,
        });

        if (currLineDesc)
          log.audit({
            title: `EF - Item description valid! ✅ - "${currLineName}"`,
            details: `"${currLineDesc}"`,
          });
        else
          log.audit({
            title: `EF - Item description invalid! ❌ - "${currLineName}"`,
            details: currLineDesc,
          });

        log.audit({
          title: "EF - Setting description on item...",
          details: `⌛`,
        });

        let item_desc_marked = currentSalesOrder.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_description",
          value: currLineDesc,
          line: lineNumber,
        });

        if (item_desc_marked)
          log.audit({
            title: "EF - Set description on item! ✅",
            details: `Desc: ${item_desc_marked}`,
          });
        else
          log.audit({
            title: "EF - Set description on item failed! ❌",
            details: `Desc: ${item_desc_marked}`,
          });
      }
    }

    // SET UP EXHIBITOR FORM ITEMS END
  };

  const runShippingAddressUpdate = (context, eventId) => {
    // Check event data and detect if order is meant for web processing
    // If its not set and format shipping address fields
    // If CS Event changes then set shipping
    let currentSalesOrder = context.newRecord;
    let eventData = {};
    let addressObject = {};

    // Set event Id upon init
    if (eventId && !isNaN(Number(eventId))) {
      eventData = getEventData(eventId);
    }

    let webOrdProc =
      Boolean(runtime.executionContext === runtime.ContextType.SCHEDULED) &&
      currentSalesOrder.getValue({ fieldId: "custbody_isweborder" });

    if (eventData?.custrecord_facility && !webOrdProc) {
      let oldRec = context.oldRecord;
      if (!isNaN(Number(eventData.custrecord_facility))) {
        // Load Venus address
        let venueRecord = record.load({
          type: "CUSTOMRECORD_FACILITY",
          id: eventData.custrecord_facility,
        });

        // Set shipping address to facility location
        let state = venueRecord.getText("custrecord_facility_state");
        let zip = venueRecord.getValue("custrecord_facility_zip");
        let addressOne = venueRecord.getValue("custrecord_facility_address1");
        let addressTwo = venueRecord.getValue("custrecord_facility_address2");
        let city = venueRecord.getValue("custrecord_facility_city");
        let country = venueRecord.getValue("custrecord_facility_country");
        let phone = venueRecord.getValue("custrecord_facility_phone");

        addressObject = {
          city,
          zip,
          state,
          addressOne,
          addressTwo,
          country,
          phone,
        };

        // Check for show site address to be set
        if (venueRecord) {
          let updShipAddr = false;
          log.audit({ title: "✅ Venue loaded: ", details: venueRecord.id });

          try {
            let shpAdr = currentSalesOrder.getSubrecord({
              fieldId: "shippingaddress",
            });
            let shippingAddressName = shpAdr.getValue({ fieldId: "addr1" });
            let shippingAddressCity = shpAdr.getValue({ fieldId: "city" });
            let shippingAddressState = shpAdr.getValue({ fieldId: "state" });

            log.audit({
              title: "Shipping address subrecord is loaded for edit 🏠",
              details: `Addr1: ${shippingAddressName}\n -- City: ${shippingAddressCity}\n -- State: ${shippingAddressState}\n`,
            });

            // Validate all shipping address fields are set up with values
            if (
              shpAdr.getValue({ fieldId: "addr1" }) !==
                addressObject.addressOne ||
              shpAdr.getValue({ fieldId: "addr2" }) !==
                addressObject.addressTwo ||
              shpAdr.getValue({ fieldId: "city" }) !== addressObject.city ||
              shpAdr.getText({ fieldId: "state" }) !== addressObject.state
            ) {
              updShipAddr = true;
            }
          } catch (err) {
            log.error({
              title: "Failed to load/process shipping address subrecord",
              details: `[${err.name}] : ${err.message}`,
            });
            updShipAddr = true;
          }

          // Check if shipping address is valid to set values on record. For Advanced Warehouse Address
          log.audit({
            title: "Is New Shipping Address needing updated?",
            details: updShipAddr ? "✅" : "❌",
          });

          if (updShipAddr) {
            log.audit({
              title: "Updating shipping address...",
              details: `'shipaddresslist' value: ${currentSalesOrder.getValue({
                fieldId: "shipaddresslist",
              })}`,
            });

            currentSalesOrder.setValue({
              fieldId: "shipaddresslist",
              value: "",
            });
            currentSalesOrder.removeSubrecord({ fieldId: "shippingaddress" });

            log.debug({ title: "🚧 Address object:", details: addressObject });

            let shippingAddress = currentSalesOrder.getSubrecord({
              fieldId: "shippingaddress",
            });

            // Set country field first when script uses dynamic mode
            // Setting text expects literal name ie=United States
            // Setting value expects abbreviation ie=US

            shippingAddress.setText({
              fieldId: "country",
              value: addressObject.country || "United States",
            });

            shippingAddress.setValue({
              fieldId: "city",
              value: addressObject.city,
            });

            shippingAddress.setValue({
              fieldId: "addrphone",
              value: addressObject.phone,
            });

            shippingAddress.setValue({
              fieldId: "state",
              value: addressObject.state,
            });

            shippingAddress.setValue({
              fieldId: "zip",
              value: addressObject.zip,
            });

            shippingAddress.setValue({
              fieldId: "addr1",
              value: addressObject.addressOne,
            });

            shippingAddress.setValue({
              fieldId: "addr2",
              value: addressObject?.addressTwo,
            });

            log.audit({
              title: "Address Updated to:",
              details: shippingAddress,
            });
          }

          log.audit({
            title: "tax item id (BeforeSubmit) (post address change)",
            details: currentSalesOrder.getValue({ fieldId: "taxitem" }),
          });
        }
      }
    }

    log.audit({ title: "Update shipping completed! ✅", details: "" });
  };

  const setIsWebOrder = (currentSalesOrder) => {
    // WIll only be applied to REST & WEBSTORE on create
    currentSalesOrder.setValue({ fieldId: "custbody_isweborder", value: true });
  };

  const runUserOrderCreateProcessing = (
    context,
    eventId,
    billParty,
    boothId
  ) => {
    // APPLY to USERINTERFACE exec context of CREATE only!
    // Check if orders are to be consolidated into 1 single order
    if (
      runtime.executionContext === runtime.ContextType.USER_INTERFACE &&
      csLib.settings.PreventAdditionalOrders
    ) {
      // Make sure the event id and booth ids are present
      if (!isNaN(Number(eventId)) && !isNaN(Number(boothId))) {
        let filt = [
          ["custbody_show_table", "anyof", [eventId]],
          "and",
          ["custbody_booth", "anyof", [boothId]],
          "and",
          [
            "custbody_ng_cs_order_type",
            "anyof",
            [csLib.settings.DefaultExhibitorOrderType],
          ],
        ];

        log.audit({
          title: "Is Multi Billing Parties enabled?...",
          details: "⌛",
        });
        // Check for multi billing party to be allowed from settings and use that for our filter in the search of sales orders
        if (csLib.settings.AllowMultiBillingParties) {
          log.audit({
            title:
              "Is Multi Billing Parties enabled! - Adding entity filter on billParty",
            details: "✅",
          });
          filt.push("and", ["entity", "anyof", [billParty]]);
        } else {
          log.audit({
            title:
              "Is Multi Billing Parties disabled! - Skipping entity filter on billParty",
            details: "⤵",
          });
        }

        let salesOrderResultCount;

        log.audit({
          title: "Running sales order search...",
          details: "⌛ - 479",
        });

        let searchObj = search.create({
          type: search.Type.SALES_ORDER,
          filters: filt,
          columns: [],
        });

        salesOrderResultCount = searchObj.runPaged().count;

        log.audit({ title: "Search on sales order complete!", details: "✅" });

        // in the event that someone manages to bypass all warnings in the UI indicating the existence of a sales order for the
        // defined booth and show and block all attempts to redirect them to the existing order, an error will be thrown causing
        // the BeforeSubmit event to fail, preventing the order from being saved to the system

        log.audit({
          title: "Sales orders found 🔎",
          details: salesOrderResultCount,
        });
        if (salesOrderResultCount !== 0) {
          let boothError = error.create({
            name: "FLAGRANT SYSTEM ERROR",
            message:
              "You cannot save this order as one already exists for this booth.",
            notifyOff: "false",
          });

          log.error({
            title: "❌ Error occurred creating sales order!",
            details: boothError,
          });
          throw "You cannot save this order as one already exists for this order";
        }
      }
    }
  };

  const setUpExhibitorFormItemsDescriptions = (currentSalesOrder, context) => {
    try {
      // SET UP EXHIBITOR FORM ITEMS START
      let lineNumber = 0;

      let loadedSalesOrder = record.load({
        type: record.Type.SALES_ORDER,
        id: currentSalesOrder.id,
      });

      log.audit({
        title: "Exhibitor Form setting item values (create)...",
        details: `⌛ - In context: "${context.type}"`,
      });

      log.debug({ title: "Get item count...", details: `⌛` });

      let lines = currentSalesOrder.getLineCount({ sublistId: "item" });

      lines &&
        log.audit({
          title: "Get item count gathered! ✅",
          details: `🌟 - ${lines}`,
        });

      // Set custcol_description for booth order printing form
      if (lines > 0) {
        for (lineNumber = 0; lineNumber < lines; lineNumber++) {
          log.debug({
            title: "Looping item line descriptions 🤏",
            details: `Item: ${lineNumber}`,
          });

          log.audit({
            title: "Setting Current Line Code 🔢",
            details: `⌛ - In context: "${context.type}"`,
          });

          let currLineCode = currentSalesOrder.getSublistValue({
            sublistId: "item",
            fieldId: "custcol_linecode",
            line: lineNumber,
          });

          if (currLineCode)
            log.audit({
              title: "Current Line Code Set! ✅",
              details: `Code: ${currLineCode}`,
            });
          else
            log.audit({
              title: "Current Line Code Not Set ❗",
              details: `Invalid Code: ${currLineCode}`,
            });

          log.audit({
            title: "Setting Current Item Type...",
            details: `⌛ - In context: "${context.type}"`,
          });

          let currLineItemType = currentSalesOrder.getSublistValue({
            sublistId: "item",
            fieldId: "itemtype",
            line: lineNumber,
          });

          if (currLineItemType)
            log.audit({
              title: "Current Item Type Set! ✅",
              details: `Code: ${currLineItemType}`,
            });
          else
            log.audit({
              title: "Current Item Type Not Set❗",
              details: `❌ Invalid Type: ${currLineItemType}`,
            });

          log.audit({ title: "Getting item description...", details: "⌛" });

          let currLineName = currentSalesOrder.getSublistValue({
            sublistId: "item",
            fieldId: "item_display",
            line: lineNumber,
          });

          let currLineDesc = currentSalesOrder.getSublistValue({
            sublistId: "item",
            fieldId: "description",
            line: lineNumber,
          });

          if (currLineDesc)
            log.audit({
              title: `Item description valid! ✅ - "${currLineName}"`,
              details: `"${currLineDesc}"`,
            });
          else
            log.audit({
              title: `Item description invalid! ❌ - "${currLineName}"`,
              details: currLineDesc,
            });

          log.audit({ title: "Setting description on item...", details: `⌛` });

          let item_desc_marked = loadedSalesOrder.setSublistValue({
            sublistId: "item",
            fieldId: "custcol_description",
            value: currLineDesc,
            line: lineNumber,
          });

          if (item_desc_marked)
            log.audit({
              title: "Set description on item! ✅",
              details: `Desc: ${item_desc_marked}`,
            });
          else
            log.audit({
              title: "Set description on item failed! ❌",
              details: `Desc: ${item_desc_marked}`,
            });
        }

        loadedSalesOrder.save({
          ignoreMandatoryFields: true,
        });
      }
    } catch (err) {
      log.error({
        title: "❌ Error setting Item descriptions...",
        details: err,
      });
    }
    // SET UP EXHIBITOR FORM ITEMS END
  };

  const getEventData = (eventId) => {
    log.debug({ title: "⚡ Running EventData fetch:", details: eventId });
    try {
      return NG.tools.getLookupFields(
        "customrecord_show",
        eventId,
        csLib.settings._EVENT_FIELDS,
        csLib.settings._EVENT_FIELDS_S,
        []
      );
    } catch (err) {
      log.error({
        title: "❌ Error running event data fetch...",
        details: err,
      });
    }
  };

  const setFormFlags = (context) => {
    log.audit({ title: "⚡ Running form flag set...", details: "" });
    try {
      let formId;
      if (["create", "copy"].includes(context.type)) {
        formId = context.newRecord.getValue({ fieldId: "customform" });
      } else if (
        !NG.tools.isEmpty(context.newRecord.id) &&
        context.newRecord.id > 0
      ) {
        formId = NG.tools.getLookupFields(
          context.newRecord.type,
          context.newRecord.id,
          ["tranid", "customform"],
          ["customform"],
          []
        ).customform;
      }

      if (!NG.tools.isEmpty(formId)) {
        _RunGlobalBO = csLib.settings.GlobalBoothOrderScripting;
        _ActiveFormBO = NG.tools.isInArray(
          formId,
          csLib.settings.BoothOrderFormIdListing
        );
        _RunGlobalAL = csLib.settings.GlobalAddItemScripting;
        _ActiveFormAL = NG.tools.isInArray(
          formId,
          csLib.settings.AddItemFormIdListing
        );
        _RunGlobalSM = csLib.settings.GlobalShowMGtScripting;
        _ActiveFormSM = NG.tools.isInArray(
          formId,
          csLib.settings.ShowMgtFormIdListing
        );
      }
    } catch (err) {
      log.error({ title: "❌ Error running FormFlag set...", details: err });
    }
  };

  const setPriceLevel = (exhibId, eventId, currRec) => {
    log.audit({ title: "⚡ Running set Price Level...", details: "" });
    try {
      if (eventId) {
        let d = new Date();
        let eventData, startDate, gpl;
        let today = new Date(
          d.getFullYear(),
          d.getMonth(),
          d.getDate(),
          0,
          0,
          0,
          0
        ).getTime();
        try {
          eventData = getEventData(eventId);
        } catch (err) {
          NG.log.logError(err, "Error encountered retrieving show data");
        }

        if (!NG.tools.isEmpty(eventData)) {
          let orderType = currRec.getValue({
            fieldId: "custbody_ng_cs_order_type",
          });
          if (orderType !== csLib.settings.DefaultShowMgmtOrderType) {
            if (!NG.tools.isEmpty(eventData.custrecord_adv_ord_date)) {
              let _AdvDate = NG.time.convertStringToDate({
                dateString: eventData.custrecord_adv_ord_date,
              });
              log.audit({
                title: "Advanced Order Date (1)",
                details: eventData.custrecord_adv_ord_date,
              });
              log.audit({
                title: "Advanced Order Date (2)",
                details: _AdvDate,
              });
              let advDate = _AdvDate.getTime();

              if (today <= advDate) {
                gpl = eventData.custrecord_adv_price_level;
              } else {
                gpl = getStandardGPL({
                  eventData: eventData,
                  eventId: eventId,
                  today: today,
                });
              }
            } else {
              gpl = getStandardGPL({
                eventData: eventData,
                eventId: eventId,
                today: today,
              });
            }
          } else {
            if (!NG.tools.isEmpty(eventData.custrecord_show_mgmnt_price_lvl)) {
              currRec.setValue({
                fieldId: "custbody_price_level",
                value: eventData.custrecord_show_mgmnt_price_lvl,
              });
              gpl = eventData.custrecord_show_mgmnt_price_lvl;
            }
          }

          gpl = gpl || "1";
          currRec.setValue({ fieldId: "custbody_price_level", value: gpl });
        }
      }
    } catch (err) {
      log.error({ title: "❌ Error running price level...", details: err });
    }
  };

  const getStandardGPL = (options) => {
    log.audit({ title: "⚡ Running get GPL...", details: "" });
    try {
      let gpl,
        startDate = csLib.func.getStartDate(options.eventId);
      if (!NG.tools.isEmpty(startDate) && options.today >= startDate) {
        gpl =
          options.eventData.custrecord_site_price_level ||
          options.eventData.custrecord_std_price_level ||
          gpl;
      } else {
        gpl = options.eventData.custrecord_std_price_level || gpl;
      }
      return gpl;
    } catch (err) {
      log.error({ title: "❌ Error running GPL", details: err });
    }
  };

  const updateDeposits = (orderId) => {
    log.audit({ title: "⚡ Running Update Deposits....", details: "" });
    try {
      let depFilt = [
        ["mainline", "is", "T"],
        "and",
        ["custbody_booth_order", "anyof", [orderId]],
      ];
      let depCols = [
        search.createColumn({ name: "salesorder" }),
        search.createColumn({ name: "tranid" }),
      ];

      let depSearch = NG.tools.getSearchResults(
        "customerdeposit",
        depFilt,
        depCols
      );
      if (!NG.tools.isEmpty(depSearch)) {
        depSearch.forEach((res) => {
          if (NG.tools.isEmpty(res.getValue({ name: "salesorder" }))) {
            try {
              record.submitFields({
                type: "customerdeposit",
                id: res.id,
                values: {
                  salesorder: Number(orderId).toFixed(0),
                },
                options: {
                  ignoreMandatoryFields: true,
                  enableSourcing: true,
                },
              });
            } catch (err) {
              NG.log.logError(
                err,
                "Error encountered updating deposit with sales order",
                "Deposit: {0}".NG_Format(res.getValue({ name: "tranid" }))
              );
            }
          }
        });
      }
    } catch (err) {
      NG.log.logError(
        err,
        "Error encountered searching for deposits to update with sales order"
      );
    }
  };

  const updatePayTotal = (data) => {
    log.audit({ title: "Running updatePayTotal()...", details: "⌛" });
    let totalData = data.totalData;
    let finalTotal = {};
    try {
      log.audit({
        title:
          "UpdatePayTotal - Checking settings if consolidation is enabled ➡",
        details: csLib.settings.PreventAdditionalOrders ? "✅" : "❌",
      });
      if (csLib.settings.PreventAdditionalOrders) {
        log.audit({
          title: "UpdatePayTotal - Consolidation is enabled❗",
          details: "Building search filters to run calculation total update.",
        });
        let sFilt = [
          ["custbody_show_table", "anyof", [data.show]],
          "and",
          ["custbody_booth", "anyof", [data.booth]],
          "and",
          [
            "custbody_ng_cs_order_type",
            "anyof",
            [csLib.settings.DefaultExhibitorOrderType],
          ],
          "and",
          ["mainline", "is", "F"],
          "and",
          ["cogs", "is", "F"],
          "and",
          ["shipping", "is", "F"],
          "and",
          ["taxline", "is", "F"],
        ];
        if (csLib.settings.AllowMultiBillingParties) {
          sFilt.push("and", ["entity", "anyof", [data.billParty]]);
        }

        log.debug({
          title: "UpdatePayTotal - Filter built: ➡",
          details: sFilt,
        });
        let sCols = [
          search.createColumn({ name: "taxtotal" }),
          search.createColumn({ name: "total" }),
          search.createColumn({ name: "amount" }),
          search.createColumn({ name: "taxamount" }),
        ];

        let sSearch;
        try {
          sSearch = NG.tools.getSearchResults("salesorder", sFilt, sCols);
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered searching for existing sales orders"
          );
        }

        if (!NG.tools.isEmpty(sSearch)) {
          let eoTotal = Number(sSearch[0].getValue({ name: "total" }));
          let eoTax = Number(sSearch[0].getValue({ name: "taxtotal" }));
          let eoTotalT = Number(0);
          let eoTotalNT = Number(0);
          // let eoSubTotal = Number(0);
          sSearch.forEach((res) => {
            let lineAmount = Number(res.getValue({ name: "amount" }));
            let lineTax = Number(res.getValue({ name: "taxamount" }));
            if (lineTax > 0) {
              eoTotalT = NG.M.roundToHundredths(eoTotalT + lineAmount);
            } else {
              eoTotalNT = NG.M.roundToHundredths(eoTotalNT + lineAmount);
            }
          });
          let eoSubTotal = NG.M.roundToHundredths(eoTotalT + eoTotalNT);
          // log.audit({ title : "existing order details" , details : { eoTotal : eoTotal , eoTax : eoTax , eoTotalT : eoTotalT , eoTotalNT : eoTotalNT , eoSubTotal : eoSubTotal } });

          let fullSubTotal = NG.M.roundToHundredths(eoSubTotal + totalData.sub);
          let taxableSubTotal = NG.M.roundToHundredths(eoTotalT + totalData.t);
          let summedTax = NG.M.roundToHundredths(eoTax + totalData.tax);
          let fullTax = NG.M.roundToHundredths(
            NG.M.roundToThousandths(totalData.taxrate * taxableSubTotal)
          );
          // log.audit({ title : "calc'd order append values" , details : { fullSubTotal : fullSubTotal , taxableSubTotal : taxableSubTotal , summedTax : summedTax , fullTax : fullTax } });
          if (fullTax > summedTax) {
            log.audit({ title: "updating tax amounts", details: "" });
            let updTax = NG.M.roundToHundredths(fullTax - eoTax); // + 0.05);
            // log.audit({ title : "affected tax values" , details : "full tax: {0} -- eo tax: {1} -- upd tax: {2} -- actl tax calc: {3}".NG_Format(fullTax, eoTax, updTax, NG.M.roundToHundredths(updTax - 0.05)) });
            let updTotal = NG.M.roundToHundredths(totalData.sub + updTax);
            finalTotal = {
              sub: totalData.sub,
              tax: updTax,
              taxInit: NG.M.roundToHundredths(fullTax - eoTax),
              taxRate: totalData.taxRate,
              total: updTotal,
              totalInit: NG.M.roundToHundredths(updTotal), // - 0.05)
              t: totalData.t,
              nt: totalData.nt,
              prevTax: eoTax,
              nonRoundedTax: totalData.taxrate * totalData.t,
              taxToThous: NG.M.roundToThousandths(
                totalData.taxrate * totalData.t
              ),
              taxToThousUpd: NG.M.roundToThousandths(
                totalData.taxrate * totalData.t
              ), // + 0.05)
            };
          } else {
            log.audit({
              title: "reporting calculated tax amounts",
              details: "",
            });
            finalTotal = JSON.parse(JSON.stringify(totalData));
            finalTotal["nonRoundedTax"] = fullTax - eoTax;
            finalTotal["taxToThous"] = NG.M.roundToThousandths(fullTax - eoTax);
            finalTotal["taxToHund"] = NG.M.roundToHundredths(
              NG.M.roundToThousandths(fullTax - eoTax)
            );
            // finalTotal['nonRoundedTaxUpd'] = ((fullTax - eoTax) + 0.05);
            // finalTotal['taxToThousUpd'] = NG.M.roundToThousandths((fullTax - eoTax) + 0.05);
            finalTotal["total"] = NG.M.roundToHundredths(
              totalData.sub + (fullTax - eoTax)
            ); // + 0.05);
            finalTotal["totalInit"] = totalData.total;
            finalTotal["fullTax"] = fullTax;
            finalTotal["eoTax"] = eoTax;
            finalTotal["taxInit"] = totalData.tax;
            finalTotal["tax"] = NG.M.roundToHundredths(
              NG.M.roundToThousandths(fullTax - eoTax)
            ); // + 0.05));
            finalTotal["summedTax"] = NG.M.roundToHundredths(fullTax - eoTax);
          }
        } else {
          log.audit({ title: "not updating tax amounts", details: "" });
          finalTotal = totalData;
        }
      }
      log.audit({
        title: "UpdatePayTotal - Calc finished ✅",
        details: finalTotal,
      });
      return finalTotal;
    } catch (err) {
      log.error({ title: "UpdatePayTotal Error❗", details: err });
    }
  };

  const calcTotals = (rec, eventData) => {
    log.audit({ title: "⚡ Running Calc Totals...", details: "" });
    try {
      return csLib.settings.AutoChargeCategoryExclusions.length > 0 &&
        csLib.settings.ExemptEstimatedItems
        ? csLib.func.getExemptedTotal(rec, true, eventData)
        : csLib.settings.AutoChargeCategoryExclusions.length > 0 &&
          !csLib.settings.ExemptEstimatedItems
        ? csLib.func.calcWithoutExclCats(rec, eventData)
        : !csLib.settings.AutoChargeCategoryExclusions.length > 0 &&
          csLib.settings.ExemptEstimatedItems
        ? csLib.func.getExemptedTotal(rec, false, eventData)
        : defaultTotals(rec, eventData);
    } catch (err) {
      log.error({ title: "❌ Error running calc totals!", details: err });
    }
  };

  const defaultTotals = (currRec, eventData) => {
    try {
      let tTotal = 0,
        tTotal1 = 0,
        tTotal2 = 0,
        ntTotal = 0,
        topTax,
        taxRate,
        taxRate1,
        taxRate2,
        l;
      let lines = currRec.getLineCount({ sublistId: "item" });

      if (!csLib.settings.UseCanadianSalesTax) {
        topTax = !NG.tools.isEmpty(eventData)
          ? (eventData["custrecord_tax_percent"] || "").replace("%", "")
          : "";
        taxRate = Number(topTax || 0) / 100;
        if (csLib.settings.SalesTaxOnItemLines) {
          taxRate1 = Number(topTax || 0) / 100;
          taxRate2 = 0;
        }
      } else {
        taxRate1 =
          Number(
            !NG.tools.isEmpty(eventData)
              ? (eventData["custrecord_ng_cs_evt_gst_pct"] || "").replace(
                  "%",
                  ""
                )
              : "0"
          ) / 100;
        taxRate2 =
          Number(
            !NG.tools.isEmpty(eventData)
              ? (eventData["custrecord_ng_cs_evt_pst_pct"] || "").replace(
                  "%",
                  ""
                )
              : "0"
          ) / 100;
        tTotal1 = 0;
        tTotal2 = 0;
      }

      for (l = 0; l < lines; l++) {
        let taxable =
          currRec.getSublistValue({
            sublistId: "item",
            fieldId: "istaxable",
            line: l,
          }) || true;
        let amount = Number(
          currRec.getSublistValue({
            sublistId: "item",
            fieldId: "amount",
            line: l,
          })
        );
        if (
          !csLib.settings.SalesTaxOnItemLines &&
          !csLib.settings.UseCanadianSalesTax
        ) {
          if (taxable) {
            tTotal += amount;
          } else {
            ntTotal += amount;
          }
        } else {
          tTotal += amount;
          let taxAmount1 = NG.M.roundToHundredths(
            NG.M.roundToThousandths(taxRate1 * amount)
          );
          tTotal1 += taxAmount1;
          if (csLib.settings.UseCanadianSalesTax) {
            let taxAmount2 = NG.M.roundToHundredths(
              NG.M.roundToThousandths(taxRate2 * amount)
            );
            tTotal2 += taxAmount2;
          }
        }
      }

      tTotal = NG.M.roundToHundredths(tTotal);
      ntTotal = NG.M.roundToHundredths(ntTotal);
      tTotal1 = NG.M.roundToHundredths(tTotal1);
      tTotal2 = NG.M.roundToHundredths(tTotal2);

      if (!csLib.settings.UseCanadianSalesTax) {
        let taxTotal = !csLib.settings.SalesTaxOnItemLines
          ? NG.M.roundToHundredths(NG.M.roundToThousandths(taxRate * tTotal))
          : NG.M.roundToHundredths(tTotal1 + tTotal2);
        let billableTotal = NG.M.roundToHundredths(tTotal + ntTotal + taxTotal);

        return {
          total: billableTotal,
          tax: taxTotal,
          sub: NG.M.roundToHundredths(
            Number(currRec.getValue({ fieldId: "total" })) -
              Number(currRec.getValue({ fieldId: "taxtotal" }))
          ),
          taxrate: taxRate,
          t: tTotal,
          nt: ntTotal,
          tax1: tTotal1,
          tax2: tTotal2,
        };
      } else {
        let taxTotal = NG.M.roundToHundredths(
          Number(currRec.getValue({ fieldId: "taxtotal" })) +
            Number(currRec.getValue({ fieldId: "tax2total" }))
        );
        return {
          total: Number(currRec.getValue({ fieldId: "total" })),
          tax: taxTotal,
          sub: NG.M.roundToHundredths(
            Number(currRec.getValue({ fieldId: "total" })) - taxTotal
          ),
          taxrate: taxRate,
          t: tTotal,
          nt: ntTotal,
          tax1: tTotal1,
          tax2: tTotal2,
        };
      }
    } catch (err) {
      log.error({ title: "❌ Error running default totals", details: err });
    }
  };

  const setCardClearValues = (updValues) => {
    updValues["paymentmethod"] = "";
    updValues["ccnumber"] = "";
    updValues["ccexpiredate"] = "";
    updValues["ccname"] = "";
    updValues["ccstreet"] = "";
    updValues["cczipcode"] = "";
    return updValues;
  };

  const sendWebOrderFailureEmail = (options) => {
    require(["N/email"], (email) => {
      email.send({
        author: csLib.settings.WebPaymentNoticeSender,
        recipient: csLib.settings.WebPaymentFailureRecipient,
        subject: options.subject,
        body: options.message,
      });
    });
  };

  const queueScheduledTask = (options) => {
    log.audit({ title: "Calling processing task...", details: "⌛" });
    log.debug({ title: 'Web Processing Task "options":', details: options });
    try {
      let params = {
        custscript_ng_cs_web_proc_soid: options.soId,
        custscript_ng_cs_web_proc_esoid: options.esoId || "",
        custscript_ng_cs_web_proc_authdata: JSON.stringify(options.authData),
        custscript_ng_cs_web_proc_order_data: JSON.stringify(options.od),
        custscript_ng_cs_web_proc_noauth: options.noAuth || "F",
      };
      log.debug({ title: "Web processing task params:", details: params });
      const scriptTask = task.create({
        taskType: task.TaskType.SCHEDULED_SCRIPT,
      });
      const scriptSearch = NG.tools.getSearchResults(
        "script",
        [["scriptid", "is", "customscript_ng_cs_schd_prc_web_ord"]],
        []
      );
      scriptTask.scriptId = scriptSearch[0].id;
      scriptTask.params = params;
      const scriptTaskId = scriptTask.submit();
      log.audit({
        title: "Processing task submitted to queue ⚡...",
        details: scriptTaskId ? "✔" : "❌",
      });
      const taskStatus = task.checkStatus(scriptTaskId).status;
      log.audit({
        title: "Web order processing task status:",
        details: taskStatus,
      });
      if (taskStatus !== "PENDING") {
        const ex = error.create({
          name: "QUEUE_FAILURE",
          message:
            "Web order processing task failed to queue. Job status: {0}".NG_Format(
              taskStatus
            ),
          notifyOff: "true",
        });
        NG.log.logError(ex, "Problem queuing web order processing");
      } else {
        log.audit({
          title: "Web order processing task successfully queued",
          details: "",
        });
      }
    } catch (err) {
      log.error({ title: "❌ Internal Error Queuing Task!", details: err });
    }
  };

  const runRelatedDepositTemplateGather = async (salesOrderId) => {
    log.audit({
      title: "⚡ Running Deposit Template String Construction Function...",
      details: "",
    });
    try {
      let relatedDeposits = [];
      let relatedPaymentDetails = [];
      var customerdepositSearchObj = search.create({
        type: "customerdeposit",
        filters: [
          ["type", "anyof", "CustDep"],
          "AND",
          ["salesorder", "anyof", salesOrderId],
          "AND",
          ["mainline", "any", ""],
        ],
        columns: [
          search.createColumn({
            name: "ordertype",
            sort: search.Sort.ASC,
            label: "Order Type",
          }),
          search.createColumn({ name: "type", label: "Type" }),
          search.createColumn({ name: "tranid", label: "Document Number" }),
          search.createColumn({ name: "entity", label: "Name" }),
          search.createColumn({ name: "amount", label: "Amount" }),
          search.createColumn({ name: "memo", label: "Memo" }),
          search.createColumn({ name: "mainline", label: "*" }),
        ],
      });
      var searchResultCount = customerdepositSearchObj.runPaged().count;
      log.debug("customerdepositSearchObj result count", searchResultCount);
      customerdepositSearchObj.run().each(function (result) {
        // .run().each has a limit of 4,000 results
        let resultObject = {
          id: result.id,
          amount: result.getValue("amount"),
        };
        relatedDeposits.push(resultObject);
        return true;
      });

      let templateHtml = "";

      if (searchResultCount !== 0) {
        // Load each deposit record to get more information that a search cannot
        for (const dep of relatedDeposits) {
          let relatedDepRecord = record.load({
            type: record.Type.CUSTOMER_DEPOSIT,
            id: dep.id,
          });

          relatedDepRecord &&
            log.audit({ title: "Customer Deposit Loaded ✅", details: "" });

          let paymentDetails = {
            method: relatedDepRecord.getText("paymentmethod"),
            ccNumber: relatedDepRecord.getValue("ccnumber"),
            checkNumber: relatedDepRecord.getValue("checknum"),
            expires: relatedDepRecord.getValue("ccexpiredate"),
            nameOnCard: relatedDepRecord.getValue("ccname"),
            tranDate: relatedDepRecord.getValue("trandate"),
            amount: relatedDepRecord.getValue("payment"),
          };

          relatedPaymentDetails.push(paymentDetails);
        }

        // Build HTML for template base off the payment details.
        relatedPaymentDetails.forEach((pmt, index) => {
          let transDate = new Date(pmt.tranDate);
          let dateString = format.format({
            value: transDate,
            type: format.Type.DATE,
          });
          let currencyString = format.format({
            value: pmt.amount,
            type: format.Type.CURRENCY,
          });

          templateHtml += `
					<tr>
						<td style="align: left;">${dateString}</td>
						<td style="align: left;">${pmt.method}</td>
						<td style="align: left;">${!pmt.ccNumber ? pmt.checkNumber : pmt.ccNumber}</td>
						<td style="align: right;">${currencyString}</td>
					</tr>
				`;
        });
      }

      log.audit({
        title: "🔧 Template String generated...",
        details: `Generated:\n ${templateHtml}`,
      });

      return templateHtml;
    } catch (err) {
      log.error({
        title: "Error occurred when setting template values...",
        details: err,
      });
    }
  };

  function handleOrderTypeOnChange(sc) {
    log.audit({ title: "⚡ Running Order Type On Change...", details: "" });
    try {
      let currentSalesOrder = sc.newRecord;
      let oldSalesOrder = sc.oldRecord;
      let orderType = currentSalesOrder.getValue("custbody_ng_cs_order_type");

      let orderTypeRecord = record.load({
        type: "customrecord_ng_cs_order_type",
        id: orderType,
      });

      if (sc.mode === "edit") {
        let depositDefault = orderTypeRecord.getValue(
          "custrecord_ng_cs_order_type_deposit_def"
        );
        let prevDepositDefault =
          oldSalesOrder && oldSalesOrder.getValue("requireddepositpercentage");

        record.submitFields({
          type: record.Type.SALES_ORDER,
          id: currentSalesOrder.id,
          values: {
            requireddepositpercentage: prevDepositDefault || depositDefault,
          },
        });
      } else if (sc.mode === "create") {
        let depositDefault = orderTypeRecord.getValue(
          "custrecord_ng_cs_order_type_deposit_def"
        );

        record.submitFields({
          type: record.Type.SALES_ORDER,
          id: currentSalesOrder.id,
          values: {
            requireddepositpercentage: depositDefault,
          },
        });
      }
    } catch (err) {
      log.error({
        title: "❌ There was an error running deposit percentage set...",
        details: err,
      });
      throw err;
    }
  }

  const updateBalanceAndPaid = async (salesOrder, record, search) => {
    let salesOrderId = salesOrder.id;
    let relatedDeposits = [];

    log.audit({
      title: "⚡ Running SO Update Balance Function...",
      details: `salesorder: "${salesOrderId}"`,
    });
    var customerdepositSearchObj = search.create({
      type: "customerdeposit",
      filters: [
        ["type", "anyof", "CustDep"],
        "AND",
        ["salesorder", "anyof", salesOrderId],
        "AND",
        ["mainline", "any", ""],
      ],
      columns: [
        search.createColumn({
          name: "ordertype",
          sort: search.Sort.ASC,
          label: "Order Type",
        }),
        search.createColumn({ name: "type", label: "Type" }),
        search.createColumn({ name: "tranid", label: "Document Number" }),
        search.createColumn({ name: "entity", label: "Name" }),
        search.createColumn({ name: "amount", label: "Amount" }),
        search.createColumn({ name: "memo", label: "Memo" }),
        search.createColumn({ name: "mainline", label: "*" }),
      ],
    });
    var searchResultCount = customerdepositSearchObj.runPaged().count;
    log.debug("customerdepositSearchObj result count", searchResultCount);
    customerdepositSearchObj.run().each(function (result) {
      // .run().each has a limit of 4,000 results
      let resultObject = {
        id: result.id,
        amount: result.getValue("amount"),
      };
      relatedDeposits.push(resultObject);
      return true;
    });

    let totalAmountPaid = 0.0;
    let relatedDepositAmounts = [];

    if (relatedDeposits.length !== 0) {
      relatedDepositAmounts = relatedDeposits.map((dep) => dep.amount);
      totalAmountPaid = relatedDepositAmounts.reduce(
        (previousValue, currentValue) =>
          Number(previousValue.amount) + Number(currentValue.amount)
      );
    }

    log.audit({ title: "🔧 Total amount payable:", details: totalAmountPaid });

    let total = Number(salesOrder.getValue("total"));

    record.submitFields({
      type: record.Type.SALES_ORDER,
      id: salesOrderId,
      values: {
        custbody_total_paid: totalAmountPaid,
        custbody_balance: total - totalAmountPaid,
      },
    });

    return {
      custbody_total_paid: totalAmountPaid,
      custbody_balance: total - totalAmountPaid,
    };
  };

  const getAllResultsFor = (searchObj, callback) => {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  };

  function runServerSubsidiarySet(sc, record) {
    /* * * * * * * * * * * * * * * * * * * * * * * * * * * *
     * Run subsidiary check on customer,
     * make sure sub field is available (feature enabled)
     * and set it on the sales order
     * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
    log.audit({ title: "⚡ Running Subsidiary Check set...", details: "" });
    let currentSalesOrder = sc.newRecord;
    let salesOrderSub = currentSalesOrder.getValue("subsidiary");
    let salesOrderEntity = currentSalesOrder.getValue("entity");
    log.audit({ title: "🌟 Sales order current sub:", details: salesOrderSub });
    log.audit({
      title: "🌟 Sales order current entity:",
      details: salesOrderSub,
    });
    if (!salesOrderSub) {
      if (salesOrderEntity) {
        let customer = record.load({
          type: record.Type.CUSTOMER,
          id: salesOrderEntity,
        });

        log.audit({
          title: "Customer loaded for subsidiary set check! ✅:",
          details: "",
        });
        let customerSubsidiary = customer.getValue("subsidiary");

        if (customerSubsidiary) {
          currentSalesOrder.setValue({
            fieldId: "subsidiary",
            value: customerSubsidiary,
          });
        } else {
          console.warn(
            "Customer has no subsidiary set! Cannot default proper sub to sales order."
          );
        }
      }
    }
  }

  return {
    beforeLoad: beforeLoad,
    beforeSubmit: beforeSubmit,
    afterSubmit: afterSubmit,
  };
});
