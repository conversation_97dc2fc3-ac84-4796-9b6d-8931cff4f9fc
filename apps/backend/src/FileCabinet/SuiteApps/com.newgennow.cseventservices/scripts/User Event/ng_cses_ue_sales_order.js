/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig ./amdUserEventConfig.json
 */
define([
  "N/config",
  "N/cache",
  "N/action",
  "N/error",
  "N/format",
  "N/https",
  "N/query",
  "N/record",
  "N/runtime",
  "N/task",
  "N/transaction",
  "N/url",
  "N/search",
  "N/ui/serverWidget",
  "crypto-js",
  "settings",
  "../lib/ng_cses_checkout_hooks",
], /**
 * @param{config} config
 * @param{cache} cache
 * @param{action} action
 * @param{error} error
 * @param{format} format
 * @param{https} https
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{task} task
 * @param{transaction} transaction
 * @param{url} url
 * @param{search} search
 * @param{serverWidget} serverWidget
 * @param{CryptoJS} crypto
 * @param{Object} settings
 * @param{() => Settings} settings.useSettings
 * @param{Object} checkout
 * @param{() => Record} checkout.useEvent
 * @param{() => Record} checkout.useBooth
 */ (
  config,
  cache,
  action,
  error,
  format,
  https,
  query,
  record,
  runtime,
  task,
  transaction,
  url,
  search,
  serverWidget,
  crypto,
  settings,
  checkout
) => {
  /**
   * Defines the function definition that is executed before record is loaded.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @param {Form} scriptContext.form - Current form
   * @param {ServerRequest} scriptContext.request - HTTP request information sent from the browser for a client action only.
   * @since 2015.2
   */
  let RECORD_MODE = "";
  let CURRENT_ORDER_FORM = "";
  let PAYTRACE_ENABLED = false;
  let CS_SETTINGS = {};
  let FIELD_MAPPING = {
    tran: {
      boothId: "custbody_booth",
      eventId: "custbody_show_table",
      orderId: "custbody_booth_order",
      toBeDeleted: "",
    },
    item: {
      colorSel: "custitem27",
      sizeSel: "custitem28",
      graphicSel: "custitem42",
      sqft: "custitem_is_sqft",
      days: "custitem_is_days",
      labor: "custitem_labor_item",
      freight: "custitem_is_freight",
      orientSel: "custitem_orientation",
      showdur: "custitem_show_duration",
      size: "custitem_has_size_options",
      color: "custitem_has_color_options",
      orientation: "custitem_has_orient_options",
      graphics: "custitem_ng_cs_has_graphic_options",
    },
    itemOpts: {
      supervision: "custcol_labor_sup_required",
      labordate: "custcol_labor_date",
      laborendtime: "custcol_labor_end_time",
      laboresthours: "custcol_labor_est_hours",
      laborworkers: "custcol_labor_workers",
      labortime: "custcol_labor_time",
      laboritem: "custcol_ng_cs_labor_item",
    },
    entity: {
      lastshow: "",
      lastwebshow: "",
      lastwebbooth: "",
    },
    showTable: {
      complete: "custrecord_cs_st_show_complete",
    },
  };

  /**
   * The current booth ordering forms selected from settings
   * @type String | Array<String>
   * */
  let BOOTH_ORDER_FORMS = "";
  /**
   * The current line scripting forms selected from settings
   * @type String | Array<String>
   * */
  let LINE_ADD_FORMS = "";
  /**
   * The current rental forms selected from settings
   * @type String | Array<String>
   * */
  let RENTAL_FORMS = "";
  /**
   * The current show management ordering forms selected from settings
   * @type String | Array<String>
   * */
  let SHOW_MANAGEMENT_FORMS = "";
  let DEFAULT_BOOTH_ORDER_FORM = "";
  let DEFAULT_SHOWMNGMT_ORDER_FORM = "";
  let DEFAULT_EXHIBITOR_FORM = "";
  let SCRIPT_SESSION = null;

  /* * * *
   * Order creation steps
   * 1. Look at settings page for preventing additional orders
   * 2. Create order if none are found
   * 3. Set shipping address for web orders
   * 4. Set tax, subsidiary, venue, and web order flag from CS event
   * 5. Process order payment - if success create deposit record
   * 6. If preventing booth orders consolidation of deposit will be required after submission
   * 7. Update pdf form of fields needed for print out.
   * * * * */

  const beforeLoad = (scriptContext) => {
    // Ensure all variables are properly initialized
    let sc = scriptContext;
    SCRIPT_SESSION = runtime.getCurrentSession();
    RECORD_MODE = sc.type;
    CS_SETTINGS = settings.useSettings() || {};
    let currentSalesOrder = sc.newRecord;
    PAYTRACE_ENABLED = (CS_SETTINGS.custrecord_ng_cs_enable_paytrace === "T");
    
    // Ensure CURRENT_ORDER_FORM is initialized
    runFormsSetInit(currentSalesOrder);

    log.audit({ title: "📋 Current order form:", details: CURRENT_ORDER_FORM || "Not defined" });

    let { executionContext, getCurrentUser } = runtime;

    let runOptions = {
      create: () => {
        log.audit({
          title: "⚡ Running beforeLoad create run options:",
          details: `${getCurrentUser().name || "Unknown User"}`,
        });
        hideAreaFields(sc, CS_SETTINGS);
        switch (executionContext) {
          case `${runtime.ContextType.USER_INTERFACE}`:
            handleAddLineViewInit(sc);
            handleDefaultOrderType(sc);
            break;
          default:
            log.audit({
              title: "🔴 No case found in switch for context:",
              details: `"${executionContext}"`,
            });
        }
      },
      edit: () => {
        log.audit({
          title: "⚡ Running beforeLoad edit run options:",
          details: `${getCurrentUser().name || "Unknown User"}`,
        });
        hideAreaFields(sc, CS_SETTINGS);
        switch (executionContext) {
          case `${runtime.ContextType.USER_INTERFACE}`:
            handleAddLineViewInit(sc);
            if (sc) {
              addSurchargeFields(sc);
            }
            break;
          default:
            log.audit({
              title: "🔴 No case found in switch for context:",
              details: `"${executionContext}"`,
            });
        }
      },
      view: () => {
        log.audit({
          title: "⚡ Running beforeLoad view run options:",
          details: `${getCurrentUser().name || "Unknown User"}`,
        });

        handleShowManagementViewInit(sc, CS_SETTINGS);
        handleBoothOrderViewInit(sc, RECORD_MODE);
        hideAreaFields(sc, CS_SETTINGS);
        switch (executionContext) {
          case `${runtime.ContextType.USER_INTERFACE}`:
            handleAddLineViewInit(sc);

            /* let updateRelatedDepositsTask = task.create({
              taskType: task.TaskType.SCHEDULED_SCRIPT,
              scriptId: "customscript_ng_cses_ss_update_deposit",
              deploymentId: "customdeploy_ng_cses_ss_update_deposit",
              params: {
                custscript_ng_cses_ss_sales_order_id: currentSalesOrder.id,
              },
            });

            let taskSubmitted = updateRelatedDepositsTask.submit(); */

            // log.audit({
            //   title: "⌚ Related deposit update task submitted:",
            //   details: taskSubmitted,
            // });
            
            // Check if currentSalesOrder and its ID are defined before proceeding
            if (currentSalesOrder && currentSalesOrder.id) {
              runRelatedDepositTemplateGather(currentSalesOrder.id)
                .then((r) => {
                  log.audit({
                    title: "Related Deposits Gathering Complete...✅",
                    details: r,
                  });

                  // Compare the current value to the new value and submit only if value has changed.
                  try {
                    let relatedPaymentField = currentSalesOrder.getValue({
                      fieldId: 'custbody_ng_cs_so_related_pymnt_rnder'
                    });

                    const currentValue = relatedPaymentField ? extractValues(relatedPaymentField) : null;
                    const newValue = r ? extractValues(r) : null;

                    if (JSON.stringify(currentValue) !== JSON.stringify(newValue)) {
                      record.submitFields({
                        type: record.Type.SALES_ORDER,
                        id: currentSalesOrder.id,
                        values: {
                          custbody_ng_cs_so_related_pymnt_rnder: r,
                        },
                      });
                    }
                  } catch (valueErr) {
                    log.error({
                      title: "❌ Error processing payment field values",
                      details: valueErr,
                    });
                  }
                })
                .catch((err) => {
                  log.error({
                    title: "❌ Error loading deposits...",
                    details: err,
                  });
                });
            } else {
              log.audit({
                title: "⚠️ Cannot gather deposits",
                details: "Sales order ID is not defined"
              });
            }
            break;
          default:
            log.audit({
              title: "🔴 No case found in switch for context:",
              details: `"${executionContext}"`,
            });
        }
      },
      print: () => {
        // VENUE OPERATIONS - Add null check for CS_SETTINGS
        if (CS_SETTINGS && CS_SETTINGS.custrecord_ng_cs_enable_venue_operations === "T") {
          addPrintFields(scriptContext);
        }

        // Check if currentSalesOrder and its ID are defined before proceeding
        if (currentSalesOrder && currentSalesOrder.id) {
          runRelatedDepositTemplateGather(currentSalesOrder.id)
            .then((r) => {
              log.audit({
                title: "Related Deposits Gathering Complete...✅",
                details: r,
              });

              // Only submit if we have a valid response
              if (r) {
                record.submitFields({
                  type: record.Type.SALES_ORDER,
                  id: currentSalesOrder.id,
                  values: {
                    custbody_ng_cs_so_related_pymnt_rnder: r,
                  },
                });
              }
            })
            .catch((err) => {
              log.error({
                title: "❌ Error loading deposits...",
                details: err,
              });
            });
        } else {
          log.audit({
            title: "⚠️ Cannot gather deposits",
            details: "Sales order ID is not defined"
          });
        }
      },
      copy: () => {
        log.audit({
          title: "⚡ Running beforeLoad copy run options:",
          details: `${getCurrentUser().name || "Unknown User"}`,
        });
        hideAreaFields(sc, CS_SETTINGS);
        switch (executionContext) {
          case `${runtime.ContextType.USER_INTERFACE}`:
            handleAddLineViewInit(sc);
            break;
          default:
            log.audit({
              title: "🔴 No case found in switch for context:",
              details: `"${executionContext}"`,
            });
        }
      },
      delete: () => {
        log.audit({
          title: "⚡ Running beforeLoad delete run options:",
          details: `${getCurrentUser().name || "Unknown User"}`,
        });
        if (sc) {
          handleAddLineViewInit(sc);
        }
      },
    };

    try {
      log.audit({
        title: "⚡ Running beforeLoad run options:",
        details: `${RECORD_MODE}`,
      });

      // Check if the mode exists in runOptions before executing
      if (RECORD_MODE && runOptions[RECORD_MODE]) {
        runOptions[RECORD_MODE]();
      } else {
        log.audit({
          title: "⚠️ Unknown record mode",
          details: `Record mode "${RECORD_MODE}" has no defined run option`
        });
      }
    } catch (err) {
      log.error({ title: "⚡ Error on beforeLoad run options:", details: err });
    }
  };

  /**
   * Defines the function definition that is executed before record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const beforeSubmit = (scriptContext) => {
    let sc = scriptContext;
    let currentSalesOrder = sc.newRecord;
    let { executionContext, getCurrentUser, getCurrentSession } = runtime;

    SCRIPT_SESSION = getCurrentSession();
    RECORD_MODE = sc.type;
    CS_SETTINGS = settings.useSettings();
    PAYTRACE_ENABLED = CS_SETTINGS.custrecord_ng_cs_enable_paytrace === "T";
    runFormsSetInit(currentSalesOrder);

    let boothOrderActive = BOOTH_ORDER_FORMS.includes(CURRENT_ORDER_FORM);
    let showManagementOrderActive =
      SHOW_MANAGEMENT_FORMS.includes(CURRENT_ORDER_FORM);
    log.audit({ title: "⚡ Running BO BeforeSub Operations...", details: "" });

    let eventId = currentSalesOrder.getValue({
      fieldId: "custbody_show_table",
    });

    let boothId = currentSalesOrder.getValue({ fieldId: "custbody_booth" });
    let billParty = currentSalesOrder.getValue({ fieldId: "entity" });
    let billingAddressId = currentSalesOrder.getValue("billaddresslist");
    let csJob = getCsJob(currentSalesOrder);
    let eventData = "";
    let cCard = {};
    let processingId = currentSalesOrder.getValue({
      fieldId: "custbody_ng_cses_web_processing_id",
    });

    let runOptions = {
      create: () => {
        log.audit({
          title: "🚦 Running beforeSubmit create run options:",
          details: `${getCurrentUser().name}`,
        });
        eventData = eventId && checkout.useEvent(eventId);

        // Save surcharges selected
        saveSurchargesSelected(scriptContext);

        // Calculate the surcharges
        calculateSurcharges(scriptContext);

        if (boothOrderActive) {
          log.audit({ title: "🟢 Booth order active...", details: "" });
          setUpExhibitorFormItems(sc, currentSalesOrder);
          runPreventAdditionalOrdersCheck(sc, eventId, boothId, billParty);
        }

        if (showManagementOrderActive) {
          log.audit({
            title: "🟢 Show Management order active...",
            details: "",
          });
        }

        switch (executionContext) {
          case `${runtime.ContextType.RESTLET}`:
            log.audit({
              title: `Running RESTLET case 🌟 - on "${RECORD_MODE}"`,
              details: "",
            });

            break;
          case `${runtime.ContextType.USER_INTERFACE}`:
            log.audit({
              title: `Running USER_INTERFACE case 🌟 - on "${RECORD_MODE}"`,
              details: "",
            });

            if (showManagementOrderActive || boothOrderActive) {
              log.audit({
                title: `Running USER_INTERFACE event form logic 🌟 - on "${RECORD_MODE}"`,
                details: "",
              });

              // runShippingAddressUpdate(sc, eventId)
            }
            break;
          default:
            log.audit({
              title: "🔴 No case found in switch for context:",
              details: `"${executionContext}"`,
            });
            break;
        }

        // markLineSearchableTaxable(currentSalesOrder);
      },
      edit: () => {
        log.audit({
          title: "🚦 Running beforeSubmit edit run options:",
          details: `${getCurrentUser().name}`,
        });
        setUpExhibitorFormItems(sc, currentSalesOrder);

        // Save surcharges selected
        saveSurchargesSelected(scriptContext);

        // Calculate the surcharges
        calculateSurcharges(scriptContext);

        switch (executionContext) {
          case `${runtime.ContextType.RESTLET}`:
            log.audit({
              title: `Running RESTLET case 🌟 - on "${RECORD_MODE}"`,
              details: "",
            });

            break;
          case `${runtime.ContextType.USER_INTERFACE}`:
            log.audit({
              title: `Running USER_INTERFACE case 🌟 - on "${RECORD_MODE}"`,
              details: "",
            });

            if (showManagementOrderActive || boothOrderActive) {
              log.audit({
                title: `Running USER_INTERFACE event form logic 🌟 - on "${RECORD_MODE}"`,
                details: "",
              });

              // runShippingAddressUpdate(sc, eventId)
              // setTaxGroupToEvent(sc, eventData)
            }

            break;
        }
      },
      view: () => {
        log.audit({
          title: "🚦 Running beforeSubmit view run options:",
          details: `${getCurrentUser().name}`,
        });
      },
      copy: () => {
        log.audit({
          title: "🚦 Running beforeSubmit copy run options:",
          details: `${getCurrentUser().name}`,
        });
      },
      delete: () => {
        log.audit({
          title: "🚦 Running beforeSubmit delete run options:",
          details: `${getCurrentUser().name}`,
        });
      },
    };

    let runUserInterfaceOptions = {
      create: () => {
        log.audit({ title: "🚦 Create USERINTERFACE running...", details: "" });

        // runShippingAddressUpdate(sc, eventId)
      },
      edit: () => {
        log.audit({ title: "🚦 Edit USERINTERFACE running...", details: "" });

        // runShippingAddressUpdate(sc, eventId)
      },
      view: () => {},
      delete: () => {},
    };

    try {
      log.audit({
        title: "⚡ Running beforeSubmit run options:",
        details: `${RECORD_MODE}`,
      });

      runOptions[RECORD_MODE] && runOptions[RECORD_MODE]();

      log.debug({ title: "🔎 Event data gathered:", details: eventData });

      if (boothOrderActive) {
        log.audit({
          title: "🟢 Booth order form active running logic...",
          details: "⚡ BeforeSub..",
        });

        setUpExhibitorFormItems(sc, currentSalesOrder);

        // Only ran in EDIT and CREATE
        switch (executionContext) {
          case `${runtime.ContextType.RESTLET}`:
            // run web logic
            // cCard.code = "123";
            cCard.adr = currentSalesOrder.getValue({ fieldId: "ccstreet" });
            cCard.zip = currentSalesOrder.getValue({ fieldId: "cczipcode" });
            cCard.method = currentSalesOrder.getValue({
              fieldId: "paymentmethod",
            });
            cCard.id = currentSalesOrder.getValue({ fieldId: "creditcard" });
            cCard.encNumber = currentSalesOrder.getValue({
              fieldId: "custbody_ng_paytrace_web_enc_cc_data",
            });

            log.audit({
              title: "Card id valid? - 💳",
              details: cCard?.id
                ? `Card ID ${cCard.id} is valid! - ✅`
                : `❌ Card id "${cCard.id}" is not a valid card.`,
            });

            try {
              log.audit({
                title: "⚡ Running REST options:",
                details: RECORD_MODE,
              });

              switch (RECORD_MODE) {
                case "create":
                  log.audit({
                    title: "🚦 Create REST running...",
                    details: "",
                  });
                  cCard.exp = currentSalesOrder.getValue({
                    fieldId: "ccexpiredate",
                  });
                  cCard.name = currentSalesOrder.getValue({
                    fieldId: "ccname",
                  });
                  cCard.code = currentSalesOrder.getValue({
                    fieldId: "ccsecuritycode",
                  });
                  cCard.number = currentSalesOrder.getValue({
                    fieldId: "ccnumber",
                  }); // field unauthorized on suitelet
                  cCard.method = currentSalesOrder.getValue({
                    fieldId: "paymentmethod",
                  }); // field unauthorized on suitelet

                  // eslint-disable-next-line no-case-declarations
                  let paymentResponse = runWebOrderProcessing(
                    sc,
                    eventId,
                    boothId,
                    billParty,
                    cCard
                  );

                  log.debug({
                    title: "🟩 Payment request returned",
                    details: paymentResponse,
                  });

                  log.debug({
                    title: "❗ Type of payment promise response:",
                    details: `"${typeof paymentResponse}"`,
                  });

                  log.debug({
                    title: "📃 Payment promise response:",
                    details: paymentResponse,
                  });

                  // Now we store this key of payment to this sales order as reference to be able to create the deposit after its submitted
                  if (paymentResponse?.success) {
                    log.audit({
                      title: "🎉 Payment Successful!",
                      details: "Moving to after submit storing in session",
                    });

                    let hashedKey = crypto
                      .SHA256(
                        `CID:${cCard.id}-BILL:${billingAddressId}-PID:${processingId}-CUST:${billParty}`
                      )
                      .toString(crypto.enc.Hex);

                    log.debug({
                      title: "🗝️ Hashed key for payment response:",
                      details: hashedKey,
                    });

                    SCRIPT_SESSION.set({
                      name: hashedKey,
                      value: JSON.stringify(paymentResponse, null, 2),
                    });

                    // / Check if CSJob is set
                    if (csJob) {
                      log.audit({
                        title: "👍 CS Job is set after payment processing",
                        details: csJob,
                      });
                    } else {
                      log.audit({
                        title:
                          "👍 CS Job is not set after payment processing placing value:",
                        details: eventData.custrecord_show_job,
                      });

                      currentSalesOrder.setValue({
                        fieldId: "custbody_cseg_ng_cs_job",
                        value: eventData.custrecord_show_job,
                      });
                    }

                    log.debug({
                      title: "Script session:",
                      details: SCRIPT_SESSION,
                    });

                    log.debug({
                      title: "🟡 Session set with:",
                      details: SCRIPT_SESSION,
                    });
                  } else if (paymentResponse?.error) {
                    log.error({
                      title: "🔴 A payment error occurred!",
                      details: paymentResponse,
                    });

                    throw paymentResponse.error;
                  }

                  log.debug({
                    title: "🟡 Payment Response:",
                    details: paymentResponse,
                  });

                  break;
                case "edit":
                  log.audit({
                    title: `Running RESTLET case 🌟 - on "${RECORD_MODE}"`,
                    details: "",
                  });

                  // RUN SHIPPING ADDRESS UPDATE
                  runShippingAddressUpdate(sc, eventId);
                  // RUN SHIPPING ADDRESS UPDATE END
                  cCard.exp = currentSalesOrder.getValue({
                    fieldId: "ccexpiredate",
                  });
                  cCard.name = currentSalesOrder.getValue({
                    fieldId: "ccname",
                  });
                  cCard.code = currentSalesOrder.getValue({
                    fieldId: "ccsecuritycode",
                  });
                  cCard.number = currentSalesOrder.getValue({
                    fieldId: "ccnumber",
                  }); // field unauthorized on suitelet

                  break;
                case "view":
                  // View logic
                  break;
                case "delete":
                  // delete logic
                  break;
                default:
                  log.audit({
                    title: "🚦 No switch case defined for type:",
                    details: RECORD_MODE,
                  });
              }
            } catch (err) {
              log.error({
                title: `❌ REST option ${RECORD_MODE} had error:`,
                details: err,
              });
              throw err;
            }

            break;
          case `${runtime.ContextType.USER_INTERFACE}`:
            // run web logic
            try {
              log.audit({
                title: "⚡ Running USERINTERFACE options:",
                details: RECORD_MODE,
              });

              runUserInterfaceOptions[RECORD_MODE] &&
                runUserInterfaceOptions[RECORD_MODE]();
            } catch (err) {
              log.error({
                title: `❌ USERINTERFACE option ${RECORD_MODE} had error:`,
                details: err,
              });
              throw err;
            }
            break;
        }
      } else {
        log.audit({
          title: "🚩 Form not a booth order form skipping logic...",
          details: CURRENT_ORDER_FORM,
        });
      }
    } catch (err) {
      log.error({
        title: "❌ Error on beforeSubmit run options:",
        details: err,
      });
      throw err;
    }
  };

  /**
   * Defines the function definition that is executed after record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const afterSubmit = (scriptContext) => {
    let sc = scriptContext;
    RECORD_MODE = sc.type;
    let currentSalesOrder = sc.newRecord;
    let { executionContext, getCurrentUser, getCurrentSession } = runtime;
    SCRIPT_SESSION = getCurrentSession();
    let targetedSalesOrderId = currentSalesOrder.id;
    let eventId = currentSalesOrder.getValue({
      fieldId: "custbody_show_table",
    });
    let boothId = currentSalesOrder.getValue({ fieldId: "custbody_booth" });
    let customerId = currentSalesOrder.getValue({ fieldId: "entity" });
    log.debug("Execution Context", executionContext);

    let billingAddressId = currentSalesOrder.getValue("billaddresslist");

    let processingId = currentSalesOrder.getValue({
      fieldId: "custbody_ng_cses_web_processing_id",
    });
    let csJobInit = getCsJob(currentSalesOrder);
    let csJob = getCsJob(currentSalesOrder);
    let eventData = eventId && checkout.useEvent(eventId);

    runFormsSetInit(currentSalesOrder);

    /**
     * The updating sales order that is used for finding the lastest values for the record
     * @type {Object|Record}
     * */
    let updatedSalesOrder = {};

    let cCard = {};
    // cCard.code = "123";
    cCard.adr = currentSalesOrder.getValue({ fieldId: "ccstreet" });
    cCard.zip = currentSalesOrder.getValue({ fieldId: "cczipcode" });
    cCard.method = currentSalesOrder.getValue({ fieldId: "paymentmethod" });
    cCard.id = currentSalesOrder.getValue({ fieldId: "creditcard" });
    cCard.encNumber = currentSalesOrder.getValue({
      fieldId: "custbody_ng_paytrace_web_enc_cc_data",
    });

    log.audit({ title: "🟢 After submission tasks starting...", details: "" });
    let boothOrderActive = BOOTH_ORDER_FORMS.includes(CURRENT_ORDER_FORM);
    let showManagementOrderActive =
      SHOW_MANAGEMENT_FORMS.includes(CURRENT_ORDER_FORM);

    CURRENT_ORDER_FORM = currentSalesOrder.getValue("customform");
    CS_SETTINGS = settings.useSettings();
    // Things to gather after settings explicitly
    PAYTRACE_ENABLED = CS_SETTINGS.custrecord_ng_cs_enable_paytrace === "T";

    let hashedKey = "";
    let paymentResponse = "";
    let existingSalesOrder = "";

    if (sc.type === "create") {
      log.debug({
        title: "🗝️ Hashing session key:",
        details: `CID:${cCard.id}-BILL:${billingAddressId}-PID:${processingId}-CUST:${customerId}`,
      });

      hashedKey = crypto
        .SHA256(
          `CID:${cCard.id}-BILL:${billingAddressId}-PID:${processingId}-CUST:${customerId}`
        )
        .toString(crypto.enc.Hex);

      log.audit({
        title: "🗝️ Retrieving payment session key:",
        details: hashedKey,
      });

      paymentResponse = SCRIPT_SESSION.get({
        name: hashedKey,
      });
    }

    existingSalesOrder = SCRIPT_SESSION.get({
      name: "EXISTING-SO",
    });

    targetedSalesOrderId = existingSalesOrder || currentSalesOrder.id; // Set target ID after current record load
    log.audit({
      title: "🟡 Existing sales order found?",
      details: `"${existingSalesOrder}"`,
    });

    log.audit({
      title: "🟡 Payment order response found?",
      details: paymentResponse ? "✅" : "❌",
    });

    if (csJob) {
      log.audit({
        title: "👍 CS Job is set after submit",
        details: `"${csJob}"`,
      });
    } else {
      log.audit({
        title: "👍 CS Job is not set after submit should be value:",
        details: eventData.custrecord_show_job,
      });
    }

    let runOptions = {
      create: () => {
        log.audit({
          title: "⚡ Running afterSubmit create run options - create:",
          details: `${getCurrentUser().name}`,
        });

        setUpExhibitorFormItemsDescriptions(sc, currentSalesOrder);

        updatedSalesOrder = record.load({
          type: record.Type.SALES_ORDER,
          id: currentSalesOrder.id,
        });
        csJob = getCsJob(updatedSalesOrder);

        if (csJob) {
          log.audit({
            title: "👍 CS Job is set - on description update",
            details: csJob,
          });
        } else {
          log.audit({
            title:
              "👍 CS Job is not set on on description update should be value:",
            details: eventData.custrecord_show_job,
          });
        }

        /**
         * Updated sales order after saving of record
         * @type {Record|Object}
         * */

        switch (executionContext) {
          case `${runtime.ContextType.USER_INTERFACE}`:
            handleOrderTypeOnChange(sc, targetedSalesOrderId);
            if (showManagementOrderActive || boothOrderActive) {
              log.audit({
                title: `Running USER_INTERFACE event form logic 🌟 - on "${RECORD_MODE}"`,
                details: "",
              });

              updateBalanceAndPaid(currentSalesOrder)
                .then(() => {
                  log.audit({
                    title: "Updated paid & balance fields! ✅",
                    details: "",
                  });
                })
                .catch((err) => {
                  log.error({
                    title:
                      "An error occurred with setting balance and paid fields...",
                    details: err,
                  });
                });
              // runShippingAddressUpdate(sc, eventId, true)
              // setTaxGroupToEvent(sc, eventData, true)
            }
            // Run for auth and payment capture
            break;
          case `${runtime.ContextType.RESTLET}`:
            // Restlet handles most of processing

            if (paymentResponse) {
              paymentResponse = JSON.parse(paymentResponse);

              // Run convenience fee add and consider order types for fee
              let orderType = Number(
                currentSalesOrder.getValue("custbody_ng_cs_order_type") || -1
              );

              let convFeeOrderTypes =
                String(
                  CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types
                ).search(/,/g) !== -1
                  ? CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types
                      .split(",")
                      .map(Number)
                  : [
                      Number(
                        CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_order_types
                      ),
                    ];

              log.audit({
                title: "📥 Conv Fee Order Type Comparator:",
                details: {
                  convFeeOrderTypes,
                  orderType,
                },
              });

              if (
                CS_SETTINGS.custrecord_ng_cs_use_cc_conv_fee === "T" &&
                convFeeOrderTypes.includes(orderType)
              ) {
                updateOrderWithFee(
                  sc,
                  paymentResponse.payGen.convenienceFee,
                  eventData
                );
              } else {
                log.audit({
                  title:
                    "❗ Convenience Fees disabled or order type disallowed - Item not added❗",
                  details: `Order type: ${orderType} - Allowed types: ${convFeeOrderTypes}`,
                });
              }
            }

            if (csJob) {
              log.debug({
                title: "👍 CS Job is set - on convenience fee add",
                details: csJob,
              });
            } else {
              log.debug({
                title:
                  "👍 CS Job is not set on convenience fee add should be value:",
                details: eventData.custrecord_show_job,
              });
            }

            runOrderConsolidation(currentSalesOrder, existingSalesOrder);

            createDepositForOrder(sc, eventId, boothId);

            log.audit({
              title: "👍 CS Job is set restlet after consolidate",
              details: `"${csJob}"`,
            });

            updateDeposits(targetedSalesOrderId);

            updateBalanceAndPaid(currentSalesOrder)
              .then(() => {
                log.audit({
                  title: "Updated paid & balance fields! ✅",
                  details: "",
                });
              })
              .catch((err) => {
                log.error({
                  title:
                    "An error occurred with setting balance and paid fields...",
                  details: err,
                });
              });

            // Let save actions complete then reload the record so spot a clear on CS Job then reset it.
            // Check CS Job if set then update
            handleOrderTypeOnChange(sc, targetedSalesOrderId);

            updatedSalesOrder = record.load({
              type: record.Type.SALES_ORDER,
              id: targetedSalesOrderId,
            });

            csJob = getCsJob(updatedSalesOrder);

            if (csJob) {
              log.debug({
                title: "👍 CS Job is set restlet--",
                details: csJob,
              });
            } else {
              log.debug({
                title: "👍 CS Job is not set placing value:",
                details: eventData.custrecord_show_job,
              });

              // Set html field for pdf form
              runRelatedDepositTemplateGather(targetedSalesOrderId)
                .then((r) => {
                  log.audit({
                    title: "Related Deposits Gathering Complete...✅",
                    details: r,
                  });

                  record.submitFields({
                    type: record.Type.SALES_ORDER,
                    id: targetedSalesOrderId,
                    values: {
                      custbody_ng_cs_so_related_pymnt_rnder: r,
                    },
                  });
                })
                .catch((err) => {
                  log.error({
                    title: "❌ Error loading deposits...",
                    details: err,
                  });
                });

              record.submitFields({
                type: record.Type.SALES_ORDER,
                id: targetedSalesOrderId,
                values: {
                  custbody_cseg_ng_cs_job: String(csJobInit),
                },
                options: {
                  ignoreMandatoryFields: true,
                  enableSourcing: false,
                },
              });
            }

            log.audit({
              title:
                "🟢 After submit operations completed via REST - Saving modified current record:",
              details: updatedSalesOrder ? "✅" : "❌",
            });

            break;
          default:
            log.audit({
              title: "👀 That context wasn't defined:",
              details: `"${executionContext}"`,
            });
        }

        log.audit({
          title: "🎉 Order created Successfully!",
          details: `salesorder:${targetedSalesOrderId}`,
        });
      },
      edit: () => {
        log.audit({
          title: "⚡ Running afterSubmit edit run options:",
          details: `${getCurrentUser().name}`,
        });

        switch (executionContext) {
          case `${runtime.ContextType.PRINT}`:
          case `${runtime.ContextType.RESTLET}`:
            break;
          case `${runtime.ContextType.USER_INTERFACE}`:
            handleOrderTypeOnChange(sc, targetedSalesOrderId);
            if (showManagementOrderActive || boothOrderActive) {
              updateBalanceAndPaid(currentSalesOrder)
                .then(() => {
                  log.audit({
                    title: "Updated paid & balance fields! ✅",
                    details: "",
                  });
                })
                .catch((err) => {
                  log.error({
                    title:
                      "An error occurred with setting balance and paid fields...",
                    details: err,
                  });
                });
            }

            // runShippingAddressUpdate(sc, eventId, true)

            break;
        }
      },
      view: () => {
        log.audit({
          title: "⚡ Running afterSubmit view run options:",
          details: `${getCurrentUser().name}`,
        });
      },
      copy: () => {
        log.audit({
          title: "⚡ Running afterSubmit copy run options:",
          details: `${getCurrentUser().name}`,
        });
      },
      delete: () => {
        log.audit({
          title: "⚡ Running afterSubmit delete run options:",
          details: `${getCurrentUser().name}`,
        });
      },
    };

    try {
      log.audit({
        title: "⚡ Running afterSubmit run options:",
        details: `${RECORD_MODE}`,
      });

      runOptions[RECORD_MODE] && runOptions[RECORD_MODE]();
    } catch (err) {
      log.error({
        title: "⚡ Error on afterSubmit run options:",
        details: err,
      });
    }
  };

  /* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
   * 🌟  AUXILIARY FUNCTIONS START - End of lifecycle events 🌟*
   * * * * * * * * * * * * * * * * * * * * * * * * * * * ** * * */

  /**
   * Set up items on form to include line codes that are needed for *ADD LINE SCRIPTING* including custom description
   *
   * @param {Object} sc The scriptContext object for the current record
   * @param {Record} currentSalesOrder The current sales order record object
   * @returns void
   * */
  function setUpExhibitorFormItems(sc, currentSalesOrder) {
    // SET UP EXHIBITOR FORM ITEMS START
    let lineNumber = 0;
    log.audit({
      title: "EF - Exhibitor Form setting item values (create)...",
      details: `⌛ - In context: "${sc.type}"`,
    });

    log.debug({ title: "EF - Get item count...", details: `⌛` });

    let lines = currentSalesOrder.getLineCount({ sublistId: "item" });

    lines &&
      log.debug({
        title: "EF - Get item count gathered! ✅",
        details: `🌟 - ${lines}`,
      });

    for (lineNumber = 0; lineNumber < lines; lineNumber++) {
      log.debug({
        title: "EF - Looping item line 🤏",
        details: `Item: ${lineNumber}`,
      });

      log.audit({
        title: "EF - Setting Current Line Code 🔢",
        details: `⌛ - In context: "${sc.type}"`,
      });

      let currLineCode = currentSalesOrder.getSublistValue({
        sublistId: "item",
        fieldId: "custcol_linecode",
        line: lineNumber,
      });

      if (currLineCode)
        log.debug({
          title: "EF - Current Line Code Set! ✅",
          details: `Code: ${currLineCode}`,
        });
      else
        log.debug({
          title: "EF - Current Line Code Not Set❗",
          details: `Invalid Code: ${currLineCode}`,
        });

      log.audit({
        title: "EF - Setting Current Item Type...",
        details: `⌛ - In context: "${sc.type}"`,
      });

      let currLineItemType = currentSalesOrder.getSublistValue({
        sublistId: "item",
        fieldId: "itemtype",
        line: lineNumber,
      });

      if (currLineItemType)
        log.debug({
          title: "Current Item Type Set! ✅",
          details: `Code: ${currLineItemType}`,
        });
      else
        log.debug({
          title: "Current Item Type Not Set❗",
          details: `❌ Invalid Type: ${currLineItemType}`,
        });

      log.audit({
        title: "Checking EndGroup and line code def...",
        details: "⌛",
      });
      if (
        typeof currLineCode === "string" &&
        currLineCode &&
        currLineItemType !== "EndGroup"
      ) {
        log.audit({
          title: "EF - Setting random string value for line code...",
          details: "⌛",
        });
        currentSalesOrder.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_linecode",
          value:
            randomString(12, "#A") +
            currentSalesOrder.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: lineNumber,
            }) +
            lineNumber.toString(),
          line: lineNumber,
        });

        log.audit({ title: "Getting item description...", details: "⌛" });

        let currLineDesc = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "description",
          line: lineNumber,
        });

        if (currLineDesc)
          log.debug({
            title: "EF - Item description valid! ✅",
            details: `"${currLineDesc}"`,
          });
        else
          log.debug({
            title: "EF - Item description invalid! ❌",
            details: currLineDesc,
          });

        log.debug({
          title: "EF - Setting description on item...",
          details: `⌛`,
        });

        let item_desc_marked = currentSalesOrder.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_description",
          value: currLineDesc,
          line: lineNumber,
        });

        if (item_desc_marked)
          log.debug({
            title: "EF - Set description on item! ✅",
            details: `Desc: ${item_desc_marked}`,
          });
      }
    }
    // SET UP EXHIBITOR FORM ITEMS END

    // SET UP EXHIBITOR FORM ITEMS START
    lineNumber = 0;

    log.audit({
      title: "EF - Exhibitor Form setting item desc (create)...",
      details: `⌛ - In context: "${sc.type}"`,
    });

    log.debug({ title: "EF - Get item count...", details: `⌛` });

    lines = currentSalesOrder.getLineCount({ sublistId: "item" });

    lines &&
      log.debug({
        title: "EF - Get item count gathered! ✅",
        details: `🌟 - ${lines}`,
      });

    // Set custcol_description for booth order printing form
    if (lines > 0) {
      for (lineNumber = 0; lineNumber < lines; lineNumber++) {
        log.debug({
          title: "EF - Looping item line descriptions 🤏",
          details: `Item: ${lineNumber}`,
        });

        log.debug({
          title: "EF - Setting Current Line Code 🔢",
          details: `⌛ - In context: "${sc.type}"`,
        });

        let currLineCode = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_linecode",
          line: lineNumber,
        });

        if (currLineCode)
          log.debug({
            title: "EF - Current Line Code Set! ✅",
            details: `Code: ${currLineCode}`,
          });
        else
          log.debug({
            title: "EF - Current Line Code Not Set ❗",
            details: `Invalid Code: ${currLineCode}`,
          });

        log.debug({
          title: "EF - Setting Current Item Type...",
          details: `⌛ - In context: "${sc.type}"`,
        });

        let currLineItemType = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "itemtype",
          line: lineNumber,
        });

        if (currLineItemType)
          log.audit({
            title: "EF - Current Item Type Set! ✅",
            details: `Code: ${currLineItemType}`,
          });
        else
          log.audit({
            title: "EF - Current Item Type Not Set❗",
            details: `❌ Invalid Type: ${currLineItemType}`,
          });

        log.audit({ title: "EF - Getting item description...", details: "⌛" });

        let currLineName = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "item_display",
          line: lineNumber,
        });

        let currLineDesc = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "description",
          line: lineNumber,
        });

        if (currLineDesc)
          log.audit({
            title: `EF - Item description valid! ✅${currLineName ? ` - "${currLineName}"` : ""}`,
            details: `"${currLineDesc}"`,
          });
        else
          log.audit({
            title: `EF - Item description invalid! ❌${currLineName ? ` - "${currLineName}"` : ""}`,
            details: currLineDesc,
          });

        log.audit({
          title: "EF - Setting description on item...",
          details: `⌛`,
        });

        if (currLineDesc) {
          currentSalesOrder.setSublistValue({
            sublistId: "item",
            fieldId: "custcol_description",
            value: currLineDesc,
            line: lineNumber,
          });

          log.debug({
            title: "EF - Set description on item! ✅",
            details: `Desc: ${currLineDesc}`,
          });
        }
      }
    }

    // SET UP EXHIBITOR FORM ITEMS END
  }
  /**
   * Set up items on form to include line codes that are needed for *ADD LINE SCRIPTING* including custom description
   * >  Applies to *afterSubmit* **only**
   * @param {Object} sc The scriptContext object for the current record
   * @param {Record} currentSalesOrder The current sales order record object
   * @returns void
   * */
  const setUpExhibitorFormItemsDescriptions = (sc, currentSalesOrder) => {
    try {
      // SET UP EXHIBITOR FORM ITEMS START
      let lineNumber = 0;

      let loadedSalesOrder = record.load({
        type: record.Type.SALES_ORDER,
        id: currentSalesOrder.id,
      });

      log.audit({
        title: "Exhibitor Form setting item values (create)...",
        details: `⌛ - In context: "${sc.type}"`,
      });

      log.debug({ title: "Get item count...", details: `⌛` });

      let lines = currentSalesOrder.getLineCount({ sublistId: "item" });

      lines &&
        log.audit({
          title: "Get item count gathered! ✅",
          details: `🌟 - ${lines}`,
        });

      // Set custcol_description for booth order printing form
      if (lines > 0) {
        for (lineNumber = 0; lineNumber < lines; lineNumber++) {
          log.debug({
            title: "Looping item line descriptions 🤏",
            details: `Item: ${lineNumber}`,
          });

          log.audit({
            title: "Setting Current Line Code 🔢",
            details: `⌛ - In context: "${sc.type}"`,
          });

          let currLineCode = currentSalesOrder.getSublistValue({
            sublistId: "item",
            fieldId: "custcol_linecode",
            line: lineNumber,
          });

          if (currLineCode)
            log.debug({
              title: "Current Line Code Set! ✅",
              details: `Code: ${currLineCode}`,
            });
          else
            log.debug({
              title: "Current Line Code Not Set ❗",
              details: `Invalid Code: ${currLineCode}`,
            });

          log.audit({
            title: "Setting Current Item Type...",
            details: `⌛ - In context: "${sc.type}"`,
          });

          let currLineItemType = currentSalesOrder.getSublistValue({
            sublistId: "item",
            fieldId: "itemtype",
            line: lineNumber,
          });

          if (currLineItemType)
            log.debug({
              title: "Current Item Type Set! ✅",
              details: `Code: ${currLineItemType}`,
            });
          else
            log.debug({
              title: "Current Item Type Not Set❗",
              details: `❌ Invalid Type: ${currLineItemType}`,
            });

          log.audit({ title: "Getting item description...", details: "⌛" });

          let currLineName = currentSalesOrder.getSublistValue({
            sublistId: "item",
            fieldId: "item_display",
            line: lineNumber,
          });

          let currLineDesc = currentSalesOrder.getSublistValue({
            sublistId: "item",
            fieldId: "description",
            line: lineNumber,
          });

          if (currLineDesc) {
            log.debug({
              title: `Item description valid! ✅ - "${currLineName}"`,
              details: `"${currLineDesc}"`,
            });
          } else {
            log.debug({
              title: `Item description invalid! ❌ - "${currLineName}"`,
              details: currLineDesc,
            });
          }

          log.audit({ title: "Setting description on item...", details: `⌛` });

          if (currLineDesc) {
            loadedSalesOrder.setSublistValue({
              sublistId: "item",
              fieldId: "custcol_description",
              value: currLineDesc,
              line: lineNumber,
            });

            log.debug({
              title: "Set description on item! ✅",
              details: `Desc: ${currLineDesc}`,
            });
          }
        }

        loadedSalesOrder.save({
          enableSourcing: false,
          ignoreMandatoryFields: true,
        });
      }
    } catch (err) {
      log.error({
        title: "❌ Error setting Item descriptions...",
        details: err,
      });
    }
    // SET UP EXHIBITOR FORM ITEMS END
  };

  /**
   * Generate a random string with the followed length and type of chars to generate.
   * - *Length*: Length of string to generate.
   * - *Chars*: Sets of chars to generate: 'a', 'A', '#', '!'
   * - 'a': only lowercase
   * - 'A': only uppercase
   * - '#': only numbers
   * - '!': only symbols
   *
   * @example
   * randomString(12, 'aA#')
   *
   *  // Generates numbers, lowercase, and uppercase characters at a length of 12
   * @type Function
   * @returns {String}
   * */
  function randomString(length, chars) {
    let mask = "";
    if (chars.indexOf("a") > -1) mask += "abcdefghijklmnopqrstuvwxyz";
    if (chars.indexOf("A") > -1) mask += "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    if (chars.indexOf("#") > -1) mask += "0123456789";
    if (chars.indexOf("!") > -1) mask += "~`!@#$%^&*()_+-={}[]:\";'<>?,./|\\";
    let result = "";
    for (let i = length; i > 0; --i)
      result += mask[Math.floor(Math.random() * mask.length)];
    return result;
  }

  /**
   * Forms to be set for conditional functionality scope
   * @type Function
   * @param {Object} currentSalesOrder
   * */
  function runFormsSetInit(currentSalesOrder) {
    log.audit({ title: "⚡ Setting forms to global vars...", details: "" });
    if (CS_SETTINGS?.id) {
      log.audit({
        title: "👍 Settings looks good setting forms...",
        details: "",
      });
      try {
        try {
          CURRENT_ORDER_FORM = search.lookupFields({
            type: search.Type.SALES_ORDER,
            id: currentSalesOrder.id,
            columns: ["customform"],
          }).customform[0].value;
        } catch (err) {
          if (err.name === "SSS_MISSING_REQD_ARGUMENT") {
            log.audit({
              title: `⚠️ Warning: Looking up current form id on ${RECORD_MODE} is not available`,
              details: "pulling from record reading...",
            });
          } else {
            log.error({
              title: `❌️ Error: Looking up current form id on ${RECORD_MODE}:`,
              details: err,
            });
          }

          CURRENT_ORDER_FORM = currentSalesOrder.getValue("customform");
        }

        let formLists = search.lookupFields({
          type: "CUSTOMRECORD_NG_CS_SETTINGS",
          id: 1,
          columns: [
            "custrecord_ng_cs_booth_ord_forms",
            "custrecord_ng_cs_add_item_forms",
            "custrecord_ng_cs_rental_forms",
            "custrecord_ng_cs_show_mgt_forms",
            // These below are default forms
            "custrecord_ng_cs_dflt_booth_order_form",
            "custrecord_ng_cs_dflt_exhibtr_form",
            "custrecord_ng_cs_dflt_show_mgmt_ord_form",
          ],
        });

        for (const formKey in formLists) {
          log.debug({ title: "🚦 Going to run formKey:", details: formKey });
          switch (formKey) {
            case "custrecord_ng_cs_booth_ord_forms":
              BOOTH_ORDER_FORMS = formLists[formKey].map(
                (formId) => formId.value
              );
              break;
            case "custrecord_ng_cs_add_item_forms":
              LINE_ADD_FORMS = formLists[formKey].map((formId) => formId.value);
              break;
            case "custrecord_ng_cs_rental_forms":
              RENTAL_FORMS = formLists[formKey].map((formId) => formId.value);
              break;
            case "custrecord_ng_cs_show_mgt_forms":
              SHOW_MANAGEMENT_FORMS = formLists[formKey].map(
                (formId) => formId.value
              );
              break;
            case "custrecord_ng_cs_dflt_booth_order_form":
              DEFAULT_BOOTH_ORDER_FORM =
                formLists[formKey] &&
                formLists[formKey].map((formId) => formId.value)[0];
              break;
            case "custrecord_ng_cs_dflt_show_mgmt_ord_form":
              DEFAULT_SHOWMNGMT_ORDER_FORM =
                formLists[formKey] &&
                formLists[formKey].map((formId) => formId.value)[0];
              break;
            case "custrecord_ng_cs_dflt_exhibtr_form":
              DEFAULT_EXHIBITOR_FORM =
                formLists[formKey] &&
                formLists[formKey].map((formId) => formId.value)[0];
              break;
            default:
              log.audit({
                title: "That form key was not found:",
                details: formKey,
              });
          }
        }

        log.debug({ title: "📋 Form list:", details: formLists });
      } catch (err) {
        log.error({
          title: `❌ Error setting forms on ${RECORD_MODE}:`,
          details: err,
        });
      }

      let formsSet = {
        BOOTH_ORDER_FORMS,
        LINE_ADD_FORMS,
        RENTAL_FORMS,
        CURRENT_ORDER_FORM,
        SHOW_MANAGEMENT_FORMS,
        DEFAULT_BOOTH_ORDER_FORM,
        DEFAULT_SHOWMNGMT_ORDER_FORM,
        DEFAULT_EXHIBITOR_FORM,
      };

      log.debug({ title: "🔧 Forms set:", details: formsSet });
    } else {
      log.audit({
        title: "❌ Forms were not set - no settings...",
        details: RECORD_MODE,
      });
    }
  }
  /**
   * Runs a search retrieving all results and into a callback function as a Search.ResultSet
   * @type Function
   * @param {Search} searchObj
   * @param {Function} callback
   * @returns void
   * */
  function getAllResultsFor(searchObj, callback) {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  }
  /**
   * Update deposits for orders that are supposed to be deleted for consolidation
   * - Applies to *afterSubmit* only
   *	-- Not loaded
   *
   * @param {Number} orderId - The order ID of the current sales order
   * @returns void
   * */
  const updateDeposits = (orderId) => {
    log.audit({ title: "⚡ Running Update Deposits....", details: "" });

    try {
      // Gather deposits against sales order.
      let depositSearch = search.create({
        type: search.Type.CUSTOMER_DEPOSIT,
        filters: [
          search.createFilter({
            name: "mainline",
            operator: search.Operator.IS,
            values: true,
          }),
          search.createFilter({
            name: "custbody_booth_order",
            operator: search.Operator.ANYOF,
            values: orderId,
          }),
        ],
        columns: [
          search.createColumn({ name: "salesorder" }),
          search.createColumn({ name: "tranid" }),
        ],
      });

      let depositResults = [];
      getAllResultsFor(depositSearch, (result) => {
        let resultSetObj = {
          id: result.id,
          salesOrder: result.getValue("salesorder"),
          tranId: result.getValue("tranid"),
        };

        depositResults.push(resultSetObj);
      });

      let depSearchCount = depositSearch.runPaged().count;

      log.audit({
        title: "🔎 Deposit result count:",
        details: `<html><b>${depSearchCount}</b></html>`,
      });

      if (depSearchCount !== 0) {
        depositResults.forEach((res) => {
          if (res.salesOrder) {
            try {
              record.submitFields({
                type: "customerdeposit",
                id: res.id,
                values: {
                  salesorder: Number(orderId).toFixed(0),
                },
                options: {
                  ignoreMandatoryFields: true,
                  enableSourcing: true,
                },
              });
            } catch (err) {
              log.error({
                title: `❗ Error encountered on deposit ${res.tranId}`,
                details: err,
              });
            }
          }
        });
      }
    } catch (err) {
      log.error({
        title: "❌ Error encountered running deposit searching",
        details: err,
      });
    }
  };
  /**
   * Set current working sales order to a web order
   * - Sets *custbody_isweborder* to `true`
   *
   * @param {Record} currentSalesOrder The current sales order record being modified
   * @returns void
   * */
  const setIsWebOrder = (currentSalesOrder) => {
    // WIll only be applied to REST & WEBSTORE on create
    currentSalesOrder.setValue({ fieldId: "custbody_isweborder", value: true });
  };
  /**
   * Sets the shipping address on the sales order to the working event data via venue record.
   *
   * @param {Object} sc - The *scriptContext* object
   * @param {Number} eventId - The event ID being used to place shipping against from its venue address.
   * @param {Boolean} loadRecord - Load record for afterSubmit then save record.
   * @returns void
   * */
  const runShippingAddressUpdate = (sc, eventId, loadRecord = false) => {
    // Check event data and detect if order is meant for web processing
    // If its not set and format shipping address fields
    // If CS Event changes then set shipping
    let currentSalesOrder = loadRecord
      ? record.load({
          type: record.Type.SALES_ORDER,
          id: sc.newRecord.id,
        })
      : sc.newRecord;
    let eventData = {};
    let addressObject = {};

    // Set event Id upon init
    if (eventId && !isNaN(Number(eventId))) {
      eventData = checkout.useEvent(eventId);
    }

    let webOrdProc = Boolean(
      currentSalesOrder.getValue({ fieldId: "custbody_isweborder" })
    );

    if (eventData?.custrecord_facility && !webOrdProc) {
      if (!isNaN(Number(eventData.custrecord_facility))) {
        // Load Venus address
        let venueRecord = record.load({
          type: "CUSTOMRECORD_FACILITY",
          id: eventData.custrecord_facility,
        });

        // Set shipping address to facility location
        let state = venueRecord.getValue("custrecord_facility_state");
        let zip = venueRecord.getValue("custrecord_facility_zip");
        let addressOne = venueRecord.getValue("custrecord_facility_address1");
        let addressTwo = venueRecord.getValue("custrecord_facility_address2");
        let city = venueRecord.getValue("custrecord_facility_city");
        let country = venueRecord.getValue("custrecord_facility_country");
        let phone = venueRecord.getValue("custrecord_facility_phone");

        addressObject = {
          city,
          zip,
          state,
          addressOne,
          addressTwo,
          country,
          phone,
        };

        let shippingAddressCountryId = getCountryShortName(
          addressObject.country
        );

        let shippingAddressStateId = getStateShortName(addressObject.state);

        // Check for show site address to be set
        if (venueRecord) {
          let updShipAddr = false;
          log.audit({ title: "✅ Venue loaded: ", details: venueRecord.id });

          try {
            let shpAdr = currentSalesOrder.getSubrecord({
              fieldId: "shippingaddress",
            });
            let shippingAddressName = shpAdr.getValue({ fieldId: "addr1" });
            let shippingAddressCity = shpAdr.getValue({ fieldId: "city" });
            let shippingAddressState = shpAdr.getValue({ fieldId: "state" });

            log.audit({
              title: "Shipping address subrecord is loaded for edit 🏠",
              details: `Addr1: ${shippingAddressName}\n -- City: ${shippingAddressCity}\n -- State: ${shippingAddressState}\n`,
            });

            // Validate all shipping address fields are set up with values
            if (
              shpAdr.getValue({ fieldId: "addr1" }) !==
                addressObject.addressOne ||
              shpAdr.getValue({ fieldId: "addr2" }) !==
                addressObject.addressTwo ||
              shpAdr.getValue({ fieldId: "city" }) !== addressObject.city ||
              shpAdr.getValue({ fieldId: "state" }) !== addressObject.state ||
              shpAdr.getValue({ fieldId: "country" }) !== addressObject.country
            ) {
              updShipAddr = true;
            }
          } catch (err) {
            log.error({
              title: "Failed to load/process shipping address subrecord",
              details: err,
            });
            updShipAddr = true;
          }

          // Check if shipping address is valid to set values on record. For Advanced Warehouse Address
          log.audit({
            title: "Is New Shipping Address needing updated?",
            details: updShipAddr ? "✅" : "❌",
          });

          if (updShipAddr) {
            log.audit({
              title: "Updating shipping address...",
              details: `'shipaddresslist' value: ${currentSalesOrder.getValue({
                fieldId: "shipaddresslist",
              })}`,
            });

            currentSalesOrder.setValue({
              fieldId: "shipaddresslist",
              value: "",
              ignoreFieldChange: true,
            });
            currentSalesOrder.removeSubrecord({ fieldId: "shippingaddress" });

            log.debug({ title: "🚧 Address object:", details: addressObject });

            let shippingAddress = currentSalesOrder.getSubrecord({
              fieldId: "shippingaddress",
            });

            // Set country field first when script uses dynamic mode
            // Setting text expects literal name ie=United States
            // Setting value expects abbreviation ie=US

            shippingAddress.setValue({
              fieldId: "country",
              value: shippingAddressCountryId,
            });

            shippingAddress.setValue({
              fieldId: "city",
              value: addressObject.city,
            });

            shippingAddress.setValue({
              fieldId: "addrphone",
              value: addressObject.phone,
            });

            shippingAddress.setValue({
              fieldId: "state",
              value: shippingAddressStateId,
            });

            shippingAddress.setValue({
              fieldId: "zip",
              value: addressObject.zip,
            });

            shippingAddress.setValue({
              fieldId: "addr1",
              value: addressObject.addressOne,
            });

            shippingAddress.setValue({
              fieldId: "addr2",
              value: addressObject?.addressTwo,
            });

            log.audit({
              title: "Address Updated to:",
              details: shippingAddress,
            });
          }

          log.audit({
            title: "tax item id (BeforeSubmit) (post address change)",
            details: currentSalesOrder.getValue({ fieldId: "taxitem" }),
          });

          if (loadRecord) {
            log.audit({
              title: "Load record TRUE: running save on order...",
              details: "Shipping address saving...",
            });

            let updatedRec = currentSalesOrder.save();

            let updatedTaxItem = search.lookupFields({
              type: search.Type.SALES_ORDER,
              id: updatedRec,
              columns: ["taxitem"],
            }).taxitem[0].value;

            updatedRec &&
              log.audit({
                title: "✅ Record saved successfully",
                details: `Taxitem after save: ${updatedTaxItem}`,
              });
          } else {
            log.audit({
              title: "🔴 Record told not to load for afterSubmit.",
              details: "Shipping record no need to save",
            });
          }
        }
      }
    }

    log.audit({ title: "Update shipping completed! ✅", details: "" });
  };

  /**
   * Get the CS Job for the given sales order
   * @param {Record} currentSalesOrder
   * @returns {Number|String}
   * */
  const getCsJob = (currentSalesOrder) => {
    log.audit({ title: "⚡ Running CS Job gather...", details: "" });
    if (currentSalesOrder) {
      return currentSalesOrder.getValue("custbody_cseg_ng_cs_job");
    }
    log.error({
      title: "❌ MISSING ARG! - currentSalesOrder are required",
      details: "",
    });
  };

  /**
   * Get the current total of a sales order with its ID
   * @param {Number|String} targetSalesOrder - Sales order ID
   * @param {Object} sc - The scriptContext object
   * @returns {Number}
   * */
  const getOrderTotal = (targetSalesOrder, sc) => {
    log.audit({
      title: "⚡ Running Order Total gather...",
      details: { targetSalesOrder },
    });
    let currentSalesOrder = sc.newRecord;

    let total = 0;
    if (targetSalesOrder) {
      let salesOrderTotal = search.lookupFields({
        type: search.Type.SALES_ORDER,
        id: targetSalesOrder,
        columns: ["total"],
      }).total;

      total = salesOrderTotal;
    } else if (sc) {
      total = currentSalesOrder.getValue("total");
    } else {
      log.error({
        title: "Error getting total - ❌ MISSING ARGS!",
        details: {
          args: {
            targetSalesOrder,
            sc: !!sc,
          },
        },
      });
    }

    log.debug({ title: "🟡 Order total returned:", details: total });

    return total;
  };

  /**
   * Runs web order processing
   * - Execs: RESTLET
   *
   * 1. Run set flags for preinit: preventBooth, event data, customer data, booth data, credit card
   * 2. Look at context for block of logic to run that is either REST or WEBSTORE
   * 3. Set values:
   * --- Default booth ordering form
   * --- Run Rest processing ---
   * ---> Init values, get event data, tax line items or order, check paytrace integration, run order calulations,
   * ---
   * @param {Object} sc
   * @param {Number|String} eventId
   * @param {Number|String} boothId
   * @param {Number|String} billParty
   * @param {Object} cCard
   * @returns {Object}
   * */
  const runWebOrderProcessing = (sc, eventId, boothId, billParty, cCard) => {
    log.audit({ title: "⚡ Running web order processing:", details: "" });
    let { executionContext } = runtime;
    let currentSalesOrder = sc.newRecord;
    let eventData = eventId && checkout.useEvent(eventId);
    let boothData = boothId && checkout.useBooth(boothId);
    let paymentRequest = {};
    let orderType = currentSalesOrder.getValue({
      fieldId: "custbody_ng_cs_order_type",
    });
    let orderTax = currentSalesOrder.getValue({ fieldId: "taxitem" });
    let processingId = currentSalesOrder.getValue({
      fieldId: "custbody_ng_cses_web_processing_id",
    });
    let tax = currentSalesOrder.getValue("taxtotal");
    let taxPst = currentSalesOrder.getValue("tax2total");
    let billingAddressId = currentSalesOrder.getValue("billaddresslist");
    let tranId = currentSalesOrder.getValue("tranid");
    let csJob = currentSalesOrder.getValue("custbody_cseg_ng_cs_job");
    let csClass = currentSalesOrder.getValue("class");
    let eventVenueId = eventData.custrecord_facility;
    let eventVenueRecord = record.load({
      type: "customrecord_facility",
      id: eventVenueId,
    });
    let perLineTaxesEnabled = config
      .load({
        type: "taxpreferences",
      })
      .getValue("perlinetaxesus");

    let existingSalesOrder = SCRIPT_SESSION.get({
      name: "EXISTING-SO",
    });

    let targetSalesOrderId = existingSalesOrder || currentSalesOrder.id;
    let total = getOrderTotal(currentSalesOrder.id, sc);

    let venueCountry =
      eventVenueRecord.getValue("custrecord_facility_country") || "230";
    let venueIsUs = venueCountry === "230";

    // / Check if CSJob is set
    if (csJob) {
      log.audit({
        title: "👍 CS Job is set upon web processing",
        details: csJob,
      });
    } else {
      log.audit({
        title: "👍 CS Job is not set during processing placing value:",
        details: csJob,
      });
      currentSalesOrder.setValue({
        fieldId: "class",
        value: eventData.custrecord_fin_show,
      });

      currentSalesOrder.setValue({
        fieldId: "custbody_cseg_ng_cs_job",
        value: eventData.custrecord_show_job,
      });
    }

    log.debug({
      title: "🟡 Billing Address to pass:",
      details: billingAddressId,
    });

    let convenience_percent = 0;

    log.debug({
      title: "⚡ Using convenience fee:",
      details: CS_SETTINGS.custrecord_ng_cs_use_cc_conv_fee,
    });

    if (CS_SETTINGS.custrecord_ng_cs_use_cc_conv_fee === "T") {
      convenience_percent =
        parseFloat(
          CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_rate === ""
            ? "0"
            : CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_rate
        ) / 100;
    }

    let convenienceFee = Number(total * convenience_percent * 100);

    let grandTotal = 0;

    setIsWebOrder(currentSalesOrder);

    log.audit({ title: "🔢 Total before tax add:", details: total });

    switch (executionContext) {
      case `${runtime.ContextType.RESTLET}`:
        if (eventData?.custrecord_fin_show && eventData?.custrecord_show_job) {
          !csClass &&
            currentSalesOrder.setValue({
              fieldId: "class",
              value: eventData.custrecord_fin_show,
            });

          !csJob &&
            currentSalesOrder.setValue({
              fieldId: "custbody_cseg_ng_cs_job",
              value: eventData.custrecord_show_job,
            });
        }

        log.debug({
          title: "🟡 Setting item line tax...",
          details: eventData.custrecord_tax_rate,
        });

        // eslint-disable-next-line no-case-declarations
        let eventTax = {
          custrecord_ng_cs_evt_pst_pct: eventData.custrecord_ng_cs_evt_pst_pct,
          custrecord_ng_cs_evt_gst_pct: eventData.custrecord_ng_cs_evt_gst_pct,
          custrecord_tax_rate: eventData.custrecord_tax_rate,
        };

        log.debug({ title: "📃 Event tax loaded:", details: eventTax });

        if (eventData.custrecord_tax_rate) {
          log.audit({
            title: "🟩 Tax on lines enabled",
            details: {
              venueIsUs,
              perLineTaxesEnabled,
            },
          });
          // Set each items tax code when iterating on items in the order.
          let itemLineCount = currentSalesOrder.getLineCount({
            sublistId: "item",
          });
          log.audit({ title: "📃 Setting tax lines...", details: "" });
          for (let lineNumber = 0; lineNumber < itemLineCount; lineNumber++) {
            let currentItem = currentSalesOrder.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: lineNumber,
            });
            log.debug({
              title: "🟡 Setting item tax:",
              details: currentItem,
            });

            if (currentItem === CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_item) {
              // If the item is the convenience fee item

              if (venueCountry === "230" && perLineTaxesEnabled) {
                // If country is United States
                currentSalesOrder.setSublistValue({
                  sublistId: "item",
                  fieldId: "taxrate1",
                  value: "0",
                  line: lineNumber,
                });
              } else {
                // If country is anything but the United States
                currentSalesOrder.setSublistValue({
                  sublistId: "item",
                  fieldId: "taxrate1",
                  value: "0",
                  line: lineNumber,
                });
                currentSalesOrder.setSublistValue({
                  sublistId: "item",
                  fieldId: "taxrate2",
                  value: "0",
                  line: lineNumber,
                });
              }
            }
          }
          // This will apply to US users to apply tax on the order instead.
        } else if (
          Number(eventData?.custrecord_tax_rate) !== Number(orderTax)
        ) {
          log.audit({
            title: "📃 If tax group & tax total rate are not equal",
            details: "Setting tax group to event tax rate",
          });

          currentSalesOrder.setValue({
            fieldId: "taxitem",
            value: eventData.custrecord_tax_rate,
          });
        }

        log.audit({
          title: "RWOP - Checking if paytrace is enabled...",
          details: "⌛",
        });

        log.debug({
          title: "RWOP - Paytrace enabled?",
          details: PAYTRACE_ENABLED,
        });

        total = getOrderTotal(currentSalesOrder?.id, sc);

        log.audit({ title: "🔢 Total after tax added:", details: total });

        // eslint-disable-next-line no-case-declarations
        let estimatedItemsTotal = getEstimatedItemsTotal(currentSalesOrder);

        grandTotal = total + convenienceFee - estimatedItemsTotal;

        if (PAYTRACE_ENABLED) {
          log.audit({
            title: "✅ Paytrace is enabled running payment handler...",
            details: "",
          });

          let paymentUrl = getPaymentTransmissionUrl({
            salesOrderId: currentSalesOrder.id,
            cacheOrder: true,
            type: "PayGen-Auto",
          });

          log.audit({
            title: "🔨 Payment Transmission payload:",
            details: {
              tranId,
              taxPst,
              tax: Number(tax),
              boothData,
              eventData,
              orderType,
              card: "****MASKED FOR PROTECTION****",
              grandTotal,
              total,
              convenienceFee,
              billingAddressId,
              entity: billParty,
            },
          });

          // Send payment transmission
          paymentRequest = https.request({
            method: https.Method.POST,
            url: paymentUrl,
            body: JSON.stringify({
              tranId,
              tax: Number(tax) + Number(taxPst),
              boothData,
              eventData,
              orderType,
              card: cCard,
              grandTotal,
              total,
              convenienceFee,
              billingAddressId,
              entity: billParty,
            }),
          });

          log.audit({
            title: "✅ Request finished:",
            details: paymentRequest,
          });
          log.audit({
            title: "❗Type of payment response:",
            details: `"${typeof paymentRequest}"`,
          });

          let { body, code } = paymentRequest;

          if (code !== 500 && body) {
            if (typeof body === "string") {
              let bodyJson = JSON.parse(body);

              log.debug({
                title: "📋 Body response:",
                details: bodyJson,
              });

              if (bodyJson?.success) {
                // Return the status of the request
                log.audit({
                  title: "🟢 Payment request success:",
                  details: bodyJson,
                });
                // Store the successful payment to the session to then pull data into the afterSubmit for later usage

                log.debug({
                  title: "🗝️ Hashing session key:",
                  details: `CID:${cCard.id}-BILL:${billingAddressId}-PID:${processingId}-CUST:${billParty}`,
                });

                return bodyJson;
              } else if (bodyJson?.error) {
                log.error({
                  title: "❗ Payment request FAILED:",
                  details: bodyJson,
                });

                if (bodyJson.name === "CREDIT_CARD_ERROR") {
                  throw error.create({
                    ...bodyJson.error,
                    name: "CREDIT_CARD_ERROR",
                    message: bodyJson.message,
                  });
                } else {
                  throw bodyJson.error;
                }
              }
            }
          } else {
            log.error({
              title: "❗ Error returned from payment handler bad request:",
              details: body,
            });

            throw body;
          }

          // Logic for non paytrace payments
        } else {
          log.audit({
            title: "🟨 Paytrace is disabled running payment handler auto...",
            details: "",
          });
          let paymentUrl = getPaymentTransmissionUrl({
            salesOrderId: currentSalesOrder.id,
            cacheOrder: true,
            type: "Auto",
          });

          let payloadBody = {
            tranId,
            tax,
            boothData,
            eventData,
            orderType,
            card: cCard,
            grandTotal,
            total,
            convenienceFee,
            billingAddressId,
            entity: billParty,
          };

          log.debug({
            title: "🟡 Payload body being sent:",
            details: payloadBody,
          });

          // Send payment transmission
          paymentRequest = https.request({
            method: https.Method.POST,
            url: paymentUrl,
            body: JSON.stringify({
              tranId,
              tax: Number(tax) + Number(taxPst),
              boothData,
              eventData,
              orderType,
              card: cCard,
              grandTotal,
              total,
              convenienceFee,
              billingAddressId,
              entity: billParty,
            }),
          });

          log.audit({
            title: "✅ Non-PayGen Request finished:",
            details: paymentRequest,
          });

          let { body, code } = paymentRequest;
          if (code !== 500 && body) {
            if (typeof body === "string") {
              let bodyJson = JSON.parse(body);

              log.debug({
                title: "📋 Body response:",
                details: bodyJson,
              });

              if (bodyJson?.success) {
                // Store the successful payment to the session to then pull data into the afterSubmit for later usage
                let hashedKey = crypto
                  .SHA256(
                    `CID:${cCard.id}-BILL:${billingAddressId}-PID:${processingId}-CUST:${billParty}`
                  )
                  .toString(crypto.enc.Hex);

                // Store the successful payment to the session to then pull data into the afterSubmit for later usage
                SCRIPT_SESSION.set({
                  name: hashedKey,
                  value: JSON.stringify(bodyJson),
                });

                // Return the status of the request
                return bodyJson;
              } else if (bodyJson?.error) {
                if (bodyJson.name === "CREDIT_CARD_ERROR") {
                  throw error.create({
                    ...bodyJson.error,
                    name: "CREDIT_CARD_ERROR",
                    message: bodyJson.message,
                  });
                } else {
                  throw bodyJson;
                }
              }
            }
          } else {
            log.error({
              title: "❗ Error returned from payment handler Non-Paygen:",
              details: body,
            });
          }

          log.debug({
            title: "🟡 Request PAYMENT:",
            details: paymentRequest,
          });
        }

        currentSalesOrder.setValue({ fieldId: "getauth", value: false });
        currentSalesOrder.setValue({ fieldId: "tobeemailed", value: false });
        break;
      default:
        log.audit({
          title: "No execution context was ran for web order",
          details: "",
        });
    }
  };

  /**
   * Get the url for the payment handler suitelet in which will process payments
   * @param {Object} params params to map the URL when its generated
   * @param {Boolean} isExternal return the external version of the suitelet url deployment (Available Without login must be checked)
   * @returns {String}
   * @example
   * getPaymentTransmissionUrl({cacheOrder: true, orderId: salesOrderId}, false)
   * // Will default external as TRUE
   * getPaymentTransmissionUrl({cacheOrder: true, orderId: salesOrderId})
   * */
  function getPaymentTransmissionUrl(params, isExternal = true) {
    return url.resolveScript({
      scriptId: "customscript_ng_cses_sl_pymt_handler",
      deploymentId: "customdeploy_ng_cses_sl_pymt_handler",
      returnExternalUrl: isExternal,
      params: {
        ...params,
      },
    });
  }
  /**
   * Handles rendering of button for show management prints and area rendering.
   * Also hoists other functions to run operations needed before the page is painted.
   * - Applies to *beforeLoad* only
   *
   * @param {Object} sc - The script context object
   * @returns void
   * */
  function handleShowManagementViewInit(sc, settings) {
    // Runs in view
    let currentSalesOrder = sc.newRecord;

    let showMngmtActive = SHOW_MANAGEMENT_FORMS.includes(CURRENT_ORDER_FORM);

    let areaDetailActive = settings?.custrecord_ng_cs_use_area_detail === 'T'

    log.audit({
      title: "⚡ Running show management init...",
      details: `form: ${CURRENT_ORDER_FORM}`,
    });

    log.audit({
      title: "Record in view mode - Active Show Mngt Form?",
      details: `ℹ - "${showMngmtActive}"`,
    });

    if (showMngmtActive) {
      log.audit({
        title: "🟢 Show management form active--",
        details: "Running work order rendering",
      });
      let hasAreas = false;

      for (
        let l = 0;
        l < currentSalesOrder.getLineCount({ sublistId: "item" });
        l++
      ) {
        let itemLine;

        if (areaDetailActive) {
          itemLine = currentSalesOrder.getSublistValue({
            sublistId: "item",
            fieldId: "custcol_ng_cs_area_detail",
            line: l,
          });
        } else {
          itemLine = currentSalesOrder.getSublistValue({
            sublistId: "item",
            fieldId: "custcol_ng_cs_area",
            line: l,
          });
        }

        if (itemLine) {
          hasAreas = true;
          break;
        }
      }


      if (hasAreas) {
        sc.form.clientScriptModulePath =
          "../cs-client/ng_cses_cm_sales_order_client.js";

        sc.form.addButton({
          id: "custpage_create_work_order",
          label: "Work Order",
          functionName: "runWorkOrderGeneration",
        });
      }

      updateBalanceAndPaid(currentSalesOrder, RECORD_MODE)
        .then(() => {
          log.audit({
            title: "Updated paid & balance fields! ✅",
            details: "",
          });
        })
        .catch((err) => {
          log.error({
            title: "An error occurred with setting balance and paid fields...",
            details: err,
          });
        });

      log.audit({ title: "✅ Show management Init complete", details: "" });
    } else {
      log.audit({
        title: "🛑 Not a show management form order not running init...",
        details: "",
      });
    }
  }
  /**
   * Handles rendering of button for booth order prints and other needed logic.
   * Also hoists other functions to run operations needed before the page is painted.
   * - Applies to *beforeLoad* only
   *
   * @param {Object} sc - The script context object
   * @returns void
   * */
  function handleBoothOrderViewInit(sc, recordMode) {
    log.audit({
      title: "⚡ Running booth order view init...",
      details: `form: ${CURRENT_ORDER_FORM}`,
    });

    let currentSalesOrder = sc.newRecord;
    let boothOrderActive = BOOTH_ORDER_FORMS.includes(CURRENT_ORDER_FORM);
    let orderType = currentSalesOrder.getValue({
      fieldId: "custbody_ng_cs_order_type",
    });
    let defaultExhibitorOrderType =
      CS_SETTINGS.custrecord_ng_cs_def_exhb_ord_type;

    log.audit({
      title: "Record in view mode - Active Booth order Form?",
      details: `ℹ - "${boothOrderActive}"`,
    });

    if (boothOrderActive) {
      log.audit({ title: "🟢 booth order active running init", details: "" });
      updateBalanceAndPaid(currentSalesOrder, recordMode)
        .then(() => {
          log.audit({
            title: "Updating paid & balance fields completed! ✅",
            details: "",
          });
        })
        .catch((err) => {
          log.error({
            title: "An error occurred with setting balance and paid fields...",
            details: err,
          });
        });

      runRelatedDepositTemplateGather(currentSalesOrder.id)
        .then((r) => {
          log.audit({
            title: "Related Deposits Gathering Complete...✅",
            details: r,
          });

          // Compare the current value to the new value and submit only if value has changed.

          let relatedPaymentField = currentSalesOrder.getValue({
            fieldId: 'custbody_ng_cs_so_related_pymnt_rnder'
          });

          const currentValue = extractValues(relatedPaymentField);
          const newValue = extractValues(r);

          if (JSON.stringify(currentValue) !== JSON.stringify(newValue)) {
            record.submitFields({
              type: record.Type.SALES_ORDER,
              id: currentSalesOrder.id,
              values: {
                custbody_ng_cs_so_related_pymnt_rnder: r,
              },
            });
          }
        })
        .catch((err) => {
          log.error({ title: "❌ Error loading deposits...", details: err });
        });

      log.audit({
        title: "📋 Order type comparator",
        details: {
          orderType,
          defaultExhibitorOrderType,
        },
      });

      if (Number(orderType) === defaultExhibitorOrderType) {
        let currentEvent = currentSalesOrder.getValue({
          fieldId: "custbody_show_table",
        });
        let currentBooth = currentSalesOrder.getValue({
          fieldId: "custbody_booth",
        });

        let urlForDateEntry = url.resolveScript({
          scriptId: "customscript_ng_cs_sl_exhib_wo_add_date",
          deploymentId: "customdeploy_ng_cs_sl_exhib_wo_add_date",
          returnExternalUrl: false,
          params: {
            sales_order_id: currentSalesOrder.id,
            show_table: currentEvent,
            booth: currentBooth,
          },
        });

        sc.form.clientScriptModulePath =
          "../cs-client/ng_cses_cm_sales_order_client.js";

        log.audit({
          title: "🟡 Client script added ",
          details: sc.form.clientScriptModulePath,
        });

        sc.form.addButton({
          id: "custpage_exb_wrk_ord_btn",
          label: "Exhibitor Work Order",
          functionName: `printWorkOrder("${urlForDateEntry}")`,
        });
      }
    } else {
      log.audit({
        title: "🛑 Not a booth order form skipping logic...",
        details: "",
      });
    }
  }
  /**
   * Handles rendering of button for Add line scripting logic in item sublist.
   * Also hoists other functions to run operations needed before the page is painted.
   * - Applies to *beforeLoad* only
   *
   * @param {Object} sc - The script context object
   * @returns void
   * */
  function handleAddLineViewInit(sc) {
    log.audit({ title: "⚡ Running add line scripting init", details: "" });
    let currentSalesOrder = sc.newRecord;
    let addLineActive = LINE_ADD_FORMS.includes(CURRENT_ORDER_FORM);
    let subsidiariesEnabled = runtime.isFeatureInEffect({
      feature: "SUBSIDIARIES",
    });
    let subsidiary = "";
    // Used in copy edit and delete

    if (addLineActive) {
      log.audit({ title: "🟢 Add line active running init", details: "" });

      if (runtime.executionContext === runtime.ContextType.USER_INTERFACE) {
        let itemSublist = sc.form.getSublist({ id: "item" });

        sc.form.clientScriptModulePath =
          "../cs-client/ng_cses_cm_sales_order_client.js";

        itemSublist.addButton({
          id: "custpage_add_item",
          label: "Add to Order",
          functionName: "addItemToNewLine",
        });

        if (subsidiariesEnabled) {
          subsidiary = currentSalesOrder.getValue({ fieldId: "subsidiary" });
        }

        let items = getAddLineSelectionItems(subsidiary);

        if (items) {
          log.audit({
            title: "🟢 Items gathered! Adding to field",
            details: "",
          });
          log.debug({ title: "🟢 Items gathered!", details: items });
        }

        sc.form
          .addField({
            id: "custpage_ng_items_list",
            type: serverWidget.FieldType.LONGTEXT,
            label: "items list",
          })
          .updateDisplayType({
            displayType: serverWidget.FieldDisplayType.HIDDEN,
          }).defaultValue = JSON.stringify(items);
      }
    } else {
      log.audit({
        title: "🛑 Add line inactive on form not running init",
        details: "",
      });
    }
  }

  /**
   * THis is used in a switch controller context to handle the order type change - Create & Edit (Run Options)
   * @param {Object} sc - The script context object
   * @param {Number} targetedSalesOrderId - The sales order id modifying the existing or new sales order
   * @returns void
   * */
  function handleOrderTypeOnChange(sc, targetedSalesOrderId) {
    log.audit({ title: "⚡ Running Order Type On Change...", details: "" });
    try {
      let currentSalesOrder = sc.newRecord;
      let orderType = currentSalesOrder.getValue("custbody_ng_cs_order_type");
      let orderTypeRecord = record.load({
        type: "customrecord_ng_cs_order_type",
        id: orderType,
      });
      let depositDefault = orderTypeRecord.getValue(
        "custrecord_ng_cs_order_type_deposit_def"
      );
      let overrideDepositPercent = orderTypeRecord.getValue(
        "custrecord_ng_override_dep_perc"
      );

      log.debug({
        title: "🟡 Deposit percent default:",
        details: {
          depositDefault,
          type: typeof depositDefault,
        },
      });

      if (overrideDepositPercent) {
        log.audit({
          title: "⚡ Setting Deposit Percent Override...",
          details: "",
        });

        record.submitFields({
          type: record.Type.SALES_ORDER,
          id: targetedSalesOrderId,
          values: {
            requireddepositpercentage: depositDefault,
          },
        });
      } /*else {
        let prevDepositDefault =
          oldSalesOrder && oldSalesOrder.getValue("requireddepositpercentage");

        record.submitFields({
          type: record.Type.SALES_ORDER,
          id: targetedSalesOrderId,
          values: {
            requireddepositpercentage: prevDepositDefault || depositDefault,
          },
        });
      }*/
    } catch (err) {
      log.error({
        title: "❌ There was an error running deposit percentage set...",
        details: {
          error: err,
          context: runtime.executionContext,
          mode: RECORD_MODE,
        },
      });
      throw err;
    }
  }


  // const runRelatedDepositTemplateGather = async (salesOrderId) => {
  //   log.audit({
  //     title: "⚡ Running Deposit Template String Construction Function...",
  //     details: "",
  //   });
  //   try {
  //     let relatedDeposits = [];
  //     let relatedPaymentDetails = [];
  //     const customerdepositSearchObj = search.create({
  //       type: "customerdeposit",
  //       filters: [
  //         ["type", "anyof", "CustDep"],
  //         "AND",
  //         ["salesorder", "anyof", salesOrderId],
  //         "AND",
  //         ["mainline", "any", ""],
  //       ],
  //       columns: [
  //         search.createColumn({
  //           name: "ordertype",
  //           sort: search.Sort.ASC,
  //           label: "Order Type",
  //         }),
  //         search.createColumn({ name: "type", label: "Type" }),
  //         search.createColumn({ name: "tranid", label: "Document Number" }),
  //         search.createColumn({ name: "entity", label: "Name" }),
  //         search.createColumn({ name: "amount", label: "Amount" }),
  //         search.createColumn({ name: "memo", label: "Memo" }),
  //         search.createColumn({ name: "mainline", label: "*" }),
  //       ],
  //     });
  //     const searchResultCount = customerdepositSearchObj.runPaged().count;
  //     log.debug("customerdepositSearchObj result count", searchResultCount);
  //     customerdepositSearchObj.run().each(function (result) {
  //       // .run().each has a limit of 4,000 results
  //       let resultObject = {
  //         id: result.id,
  //         amount: result.getValue("amount"),
  //       };
  //       relatedDeposits.push(resultObject);
  //       return true;
  //     });

  //     let templateHtml = "";

  //     if (searchResultCount !== 0) {
  //       // Load each deposit record to get more information that a search cannot
  //       for (const dep of relatedDeposits) {
  //         let relatedDepRecord = record.load({
  //           type: record.Type.CUSTOMER_DEPOSIT,
  //           id: dep.id,
  //         });

  //         relatedDepRecord &&
  //           log.audit({ title: "Customer Deposit Loaded ✅", details: "" });

  //         let paymentDetails = {
  //           method: relatedDepRecord.getText("paymentmethod"),
  //           tranType: "Deposit",
  //           ccNumber: relatedDepRecord.getValue("ccnumber"),
  //           checkNumber: relatedDepRecord.getValue("checknum"),
  //           expires: relatedDepRecord.getValue("ccexpiredate"),
  //           nameOnCard: relatedDepRecord.getValue("ccname"),
  //           tranDate: relatedDepRecord.getValue("trandate"),
  //           amount: relatedDepRecord.getValue("payment"),
  //         };

  //         relatedPaymentDetails.push(paymentDetails);

  //         let customerRefundSearchObject = search.create({
  //           type: search.Type.DEPOSIT_APPLICATION,
  //           filters: [
  //             ["type", "anyof", "DepAppl"],
  //             "AND",
  //             ["appliedtotransaction", "anyof", dep.id],
  //           ],
  //           columns: [
  //             search.createColumn({
  //               name: "ordertype",
  //               sort: search.Sort.ASC,
  //               label: "Order Type",
  //             }),
  //             search.createColumn({ name: "mainline", label: "*" }),
  //             search.createColumn({
  //               name: "paymentmethod",
  //               label: "Payment Method",
  //             }),
  //             search.createColumn({ name: "trandate", label: "Date" }),
  //             search.createColumn({ name: "asofdate", label: "As-Of Date" }),
  //             search.createColumn({ name: "postingperiod", label: "Period" }),
  //             search.createColumn({ name: "taxperiod", label: "Tax Period" }),
  //             search.createColumn({ name: "type", label: "Type" }),
  //             search.createColumn({ name: "tranid", label: "Document Number" }),
  //             search.createColumn({ name: "entity", label: "Name" }),
  //             search.createColumn({ name: "account", label: "Account" }),
  //             search.createColumn({ name: "memo", label: "Memo" }),
  //             search.createColumn({ name: "amount", label: "Amount" }),
  //             search.createColumn({
  //               name: "paymentmethod",
  //               label: "Payment Method",
  //             }),
  //           ],
  //         });

  //         getAllResultsFor(customerRefundSearchObject, (result) => {
  //           let appliedRecord = record.load({
  //             type: record.Type.DEPOSIT_APPLICATION,
  //             id: result.id,
  //           });
  //           let tranTypeLabel = "Deposit";
  //           let appliedTransactionsCount = appliedRecord.getLineCount("apply");

  //           for (let line = 0; line < appliedTransactionsCount; line++) {
  //             let isApplied = appliedRecord.getSublistValue({
  //               sublistId: "apply",
  //               fieldId: "apply",
  //               line,
  //             });
  //             let tranType = appliedRecord.getSublistValue({
  //               fieldId: "trantype",
  //               sublistId: "apply",
  //               line,
  //             });
  //             let tranDate = appliedRecord.getSublistValue({
  //               fieldId: "trandate",
  //               sublistId: "apply",
  //               line,
  //             });

  //             let amountFormatted = "";
  //             let amount = appliedRecord.getValue("amount");

  //             let appliedItem = {
  //               amount: Number(amount),
  //               tranType: tranType,
  //             }


  //             log.debug({ title: "Transaction Applied:", details: isApplied });
  //             log.debug({ title: "Transaction Type:", details: tranType });
  //             if (isApplied) {
  //               switch (tranType) {
  //                 case "CustRfnd":
  //                   tranTypeLabel = "Refund";

  //                     if (appliedItem.amount > 0) {
  //                       appliedItem.amount = -Math.abs(appliedItem.amount);
  //                       log.debug({ 
  //                         title: "UDPT - Refund Amount Adjusted:", 
  //                         details: `Original amount was positive, converted to negative: ${appliedItem.amount}` 
  //                       });
  //                     }

  //                     if (String(appliedItem.amount).search("-") !== -1) {
  //                       let currencyString = format.format({
  //                         value: appliedItem.amount,
  //                         type: format.Type.CURRENCY,
  //                       });
  //                       amountFormatted = `(${String(currencyString).replace("-", "")})`;
  //                     } else {
  //                       amountFormatted = appliedItem.amount;
  //                     }
  //                   break;
  //                 default:
  //                   tranTypeLabel = "Deposit";
  //               }
  //             }

  //             // Check if card number is all digits and if so, mask it
  //             let ccNumber = result.getValue("ccnumber");

  //             if (/^\d+$/.test(ccNumber)) {
  //               ccNumber = "************" + ccNumber.slice(-4);
  //             }

  //             let paymentDetails = {
  //               method: result.getText("paymentmethod"),
  //               tranType: tranTypeLabel,
  //               ccNumber: ccNumber,
  //               checkNumber: "",
  //               expires: "",
  //               nameOnCard: "",
  //               tranDate: tranDate,
  //               amount: amountFormatted,
  //             };
  
  //             relatedPaymentDetails.push(paymentDetails);
  //           }          
  //         });
          
  //       }

  //       log.debug({
  //         title: "Payment Details After:",
  //         details: relatedPaymentDetails,
  //       });

  //       // Build HTML for template base off the payment details.
  //       relatedPaymentDetails.forEach((pmt) => {
  //         let transDate = new Date(pmt.tranDate);
  //         let dateString = format.format({
  //           value: transDate,
  //           type: format.Type.DATE,
  //         });
  //         let currencyString = format.format({
  //           value: pmt.amount,
  //           type: format.Type.CURRENCY,
  //         });

  //         templateHtml += `
	// 				<tr>
	// 					<td style="align: left;">${dateString}</td>
	// 					<td style="align: left;">${pmt.tranType}</td>
	// 					<td style="align: left;">${pmt.method}</td>
	// 					<td style="align: left;">${!pmt.ccNumber ? pmt.checkNumber : pmt.ccNumber}</td>
	// 					<td style="align: right;">${currencyString}</td>
	// 				</tr>
	// 			`;
  //       });
  //     }

  //     log.audit({
  //       title: "🔧 Template String generated...",
  //       details: `Generated:\n ${templateHtml}`,
  //     });

  //     return templateHtml;
  //   } catch (err) {
  //     log.error({
  //       title: "Error occurred when setting template values...",
  //       details: err,
  //     });
  //   }
  // };

  /**
   * Process sales order deposits to:
   * 1. Update balance, hasBalance, and amount paid fields
   * 2. Generate deposit template HTML (if requested)
   * 
   * @param {Record|Object} currentSalesOrder - The current sales order record or object with id
   * @param {String} recordMode - The current record mode (view, edit, create)
   * @param {Object} options - Additional options
   * @param {Boolean} options.generateTemplate - Whether to generate HTML template
   * @returns {Object} Object containing updated field values and template HTML if requested
   */
  const processOrderDeposits = async (currentSalesOrder, recordMode, options = {}) => {
    const { generateTemplate = false } = options;
    
    // Validate input parameters
    if (!currentSalesOrder || !currentSalesOrder.id) {
      log.error({
        title: "Invalid Sales Order",
        details: "processOrderDeposits received invalid sales order"
      });
      return null;
    }

    let relatedDeposits = [];
    let relatedPaymentDetails = [];
    let formattedAmountPaid = 0;
    const salesOrderId = currentSalesOrder.id;
    
    // Track if we have a full record or just an ID
    const hasFullRecord = typeof currentSalesOrder.getValue === 'function';

    log.audit({
      title: "🔄 processOrderDeposits",
      details: `Mode: ${recordMode}, SO ID: ${salesOrderId}, Generate Template: ${generateTemplate}, Has Full Record: ${hasFullRecord}`
    });

    // Get sales order total using search to ensure we have latest value
    let salesOrderTotal = search.lookupFields({
      type: search.Type.SALES_ORDER,
      id: salesOrderId,
      columns: ["total"],
    }).total;

    // Find related deposits
    const customerdepositSearchObj = search.create({
      type: "customerdeposit",
      filters: [
        ["type", "anyof", "CustDep"],
        "AND",
        ["salesorder", "anyof", salesOrderId],
        "AND",
        ["mainline", "any", ""],
      ],
      columns: [
        search.createColumn({
          name: "ordertype",
          sort: search.Sort.ASC,
          label: "Order Type",
        }),
        search.createColumn({ name: "type", label: "Type" }),
        search.createColumn({ name: "tranid", label: "Document Number" }),
        search.createColumn({ name: "entity", label: "Name" }),
        search.createColumn({ name: "amount", label: "Amount" }),
        search.createColumn({ name: "memo", label: "Memo" }),
        search.createColumn({ name: "mainline", label: "*" }),
        search.createColumn({
          name: "paymentmethod",
          label: "Payment Method",
        }),
        search.createColumn({
          name: "trandate",
          label: "Date",
        }),
      ],
    });

    const searchResultCount = customerdepositSearchObj.runPaged().count;
    log.debug("Customer deposit search result count", searchResultCount);

    customerdepositSearchObj.run().each(function (result) {
      // .run().each has a limit of 4,000 results
      let resultObject = {
        id: result.id,
        amount: result.getValue("amount"),
        type: result.getValue("type"),
      };      
      
      relatedDeposits.push(resultObject);
      return true;
    });


    log.debug({
      title: "Related Deposits:",
      details: relatedDeposits,
    });

    // Process related deposits for both balance calculation and template generation
    let templateHtml = "";
    
    if (searchResultCount !== 0) {
      // For each deposit, check for related refunds and deposit applications
      for (const dep of relatedDeposits) {
        // If generating template, load full deposit record for additional details
        if (generateTemplate) {
          try {
            let relatedDepRecord = record.load({
              type: record.Type.CUSTOMER_DEPOSIT,
              id: dep.id,
            });

            let payNowPaymentCard = relatedDepRecord.getValue("custbody_ng_pg_paynow_masked_cc") || '';
            let paymentMethod = relatedDepRecord.getText("paymentmethod") || '';

            // If payment method contains PayNow, use the masked card from PayNow
            let ccNumber = '';
            if (paymentMethod && paymentMethod.toLowerCase().indexOf('paynow') !== -1) {
              ccNumber = payNowPaymentCard;
            } else {
              ccNumber = relatedDepRecord.getValue("ccnumber") || '';
            }

            relatedDepRecord &&
              log.audit({ title: "Customer Deposit Loaded ✅", details: "" });

            let paymentDetails = {
              method: paymentMethod,
              tranType: "Deposit",
              ccNumber: ccNumber,
              checkNumber: relatedDepRecord.getValue("checknum"),
              expires: relatedDepRecord.getValue("ccexpiredate"),
              nameOnCard: relatedDepRecord.getValue("ccname"),
              tranDate: relatedDepRecord.getValue("trandate"),
              amount: relatedDepRecord.getValue("payment"),
            };

            relatedPaymentDetails.push(paymentDetails);
          } catch (loadErr) {
            log.error({
              title: "Error loading customer deposit",
              details: `${loadErr} (ID: ${dep.id})`
            });
            // Continue processing without this deposit's details
          }
        }

        // Search for deposit applications (common for both operations)
        let customerRefundSearchObject = search.create({
          type: search.Type.DEPOSIT_APPLICATION,
          filters: [
            ["type", "anyof", "DepAppl"],
            "AND",
            ["appliedtotransaction", "anyof", dep.id],
          ],
          columns: [
            search.createColumn({
              name: "ordertype",
              sort: search.Sort.ASC,
              label: "Order Type",
            }),
            search.createColumn({ name: "type", label: "Type" }),
            search.createColumn({ name: "mainline", label: "*" }),
            search.createColumn({
              name: "paymentmethod",
              label: "Payment Method",
            }),
            search.createColumn({ name: "trandate", label: "Date" }),
            search.createColumn({ name: "ccnumber", label: "CC Number" }),
            search.createColumn({ name: "amount", label: "Amount" }),
          ],
        });

        getAllResultsFor(customerRefundSearchObject, (result) => {
          try {
            let appliedRecord = record.load({
              type: record.Type.DEPOSIT_APPLICATION,
              id: result.id,
            });

            let tranTypeLabel = "Deposit";
            let appliedTransactionsCount = appliedRecord.getLineCount("apply");
            let paymentDetails = {};

            for (let line = 0; line < appliedTransactionsCount; line++) {
              let isApplied = appliedRecord.getSublistValue({
                sublistId: "apply",
                fieldId: "apply",
                line,
              });

              // If the transaction is not applied, skip it
              if (!isApplied) {
                continue;
              }

              let internalId = appliedRecord.getSublistValue({
                fieldId: "internalid",
                sublistId: "apply",
                line,
              });
              
              let tranType = appliedRecord.getSublistValue({
                fieldId: "trantype",
                sublistId: "apply",
                line,
              });

              let amount = appliedRecord.getSublistValue({
                fieldId: "amount",
                sublistId: "apply",
                line,
              });

              let tranDate = appliedRecord.getSublistValue({
                fieldId: "applydate",
                sublistId: "apply",
                line,
              });

              let appliedItem = {
                id: appliedRecord.id,
                internalId: internalId,
                amount: Number(amount),
                type: tranType,
                isApplied: isApplied,
              };

              if (isApplied) {
                if (tranType === "CustRfnd") {
                  tranTypeLabel = "Refund";
                  // For refunds, ensure the amount is negative
                  if (appliedItem.amount > 0) {
                    appliedItem.amount = -Math.abs(appliedItem.amount);
                    log.debug({ 
                      title: "Refund Amount Adjusted:", 
                      details: `Original amount was positive, converted to negative: ${appliedItem.amount}` 
                    });
                  }

                  if (generateTemplate) {
                    let customerRefundRecord = record.load({
                      type: record.Type.CUSTOMER_REFUND,
                      id: appliedItem.internalId,
                    });

                    let checkNumber = customerRefundRecord.getValue("tranid") || '';
                    let ccNumber = customerRefundRecord.getValue("ccnumber") || '';
                    let ccexpiredate = customerRefundRecord.getValue("ccexpiredate") || '';
                    let ccname = customerRefundRecord.getValue("ccname") || '';
                    let tranDate = customerRefundRecord.getValue("trandate") || '';
                    let paymentMethod = customerRefundRecord.getText("paymentmethod") || '';

                    paymentDetails = {
                      ...paymentDetails,
                      method: paymentMethod,
                      tranType: tranTypeLabel,
                      checkNumber: checkNumber,
                      ccNumber: ccNumber,
                      expires: ccexpiredate,
                      nameOnCard: ccname,
                      tranDate: tranDate,
                    };
                  }
                  
                  // Add to relatedDeposits for balance calculation
                  relatedDeposits.push(appliedItem);
                }
                
                // If generating template, add to payment details
                if (generateTemplate) {
                  let amountFormatted = appliedItem.amount;
                  
                  // Format negative amounts
                  if (String(appliedItem.amount).search("-") !== -1) {
                    let currencyString = format.format({
                      value: appliedItem.amount,
                      type: format.Type.CURRENCY,
                    });
                    amountFormatted = `(${String(currencyString).replace("-", "")})`;
                  }
                  
                  // Check if card number is all digits and if so, mask it
                  let ccNumber = result.getValue("ccnumber");
                  if (/^\d+$/.test(ccNumber)) {
                    ccNumber = "************" + ccNumber.slice(-4);
                  }



                  paymentDetails = {
                    ...paymentDetails,
                    tranType: tranTypeLabel,
                    tranDate: tranDate,
                    amount: amountFormatted,
                  };
    
                  relatedPaymentDetails.push(paymentDetails);
                }
              }
            }
          } catch (loadErr) {
            log.error({
              title: "Error loading deposit application",
              details: `${loadErr} (ID: ${result.id})`
            });
            // Continue processing without this application's details
          }
        });
      }

      // Generate HTML template if requested
      if (generateTemplate && relatedPaymentDetails.length > 0) {
        log.debug({
          title: "Payment Details for Template:",
          details: relatedPaymentDetails,
        });

        // Build HTML for template based on the payment details
        relatedPaymentDetails.forEach((pmt) => {
          let dateString = format.format({
            value: pmt.tranDate,
            type: format.Type.DATE,
          });
          let currencyString = format.format({
            value: pmt.amount,
            type: format.Type.CURRENCY,
          });

          templateHtml += `
          <tr>
            <td style="align: left;">${dateString}</td>
            <td style="align: left;">${pmt.tranType}</td>
            <td style="align: left;">${pmt.method}</td>
            <td style="align: left;">${!pmt.ccNumber ? pmt.checkNumber : pmt.ccNumber}</td>
            <td style="align: right;">${currencyString}</td>
          </tr>
        `;
        });
      }
    }

    // Calculate balance and total paid
    let totalAmountPaid = 0.0;
    let relatedDepositAmounts = [];

    log.debug({
      title: "Related Deposits for balance calculation:",
      details: relatedDeposits,
    });

    if (relatedDeposits.length !== 0) {
      relatedDepositAmounts = relatedDeposits
        .filter((dep) => ["CustDep", "CustRfnd"].includes(dep.type))
        .map((dep) => Number(dep.amount));

      totalAmountPaid = relatedDepositAmounts.reduce(
        (previousValue, currentValue) =>
          Number(previousValue) + Number(currentValue),
        0
      );
    }

    formattedAmountPaid = Number(totalAmountPaid);

    log.audit({
      title: "🔧 Total amount payable:",
      details: formattedAmountPaid,
    });

    let total = Number(salesOrderTotal);
    let balance = total - formattedAmountPaid;

    log.audit({ 
      title: "📂 Order values calculated", 
      details: `Total: ${total}, Paid: ${formattedAmountPaid}, Balance: ${balance}` 
    });

    // Only try to update the in-memory record if we have a full record object
    if (hasFullRecord) {
      // Update in-memory record for consistency
      currentSalesOrder.setValue({
        fieldId: "custbody_total_paid",
        value: !isNaN(formattedAmountPaid) ? formattedAmountPaid : 0,
      });

      currentSalesOrder.setValue({
        fieldId: "custbody_balance",
        value: balance,
      });

      currentSalesOrder.setValue({
        fieldId: "custbody_hasbalance",
        value: balance !== 0,
      });
    }

    // Handle different contexts based on recordMode
    if (recordMode === 'view') {
      // In view mode, only update if values have changed and if we have a full record
      if (hasFullRecord) {
        validateUpdatedValues(currentSalesOrder, formattedAmountPaid, balance);
      }
    } else {
      // In other contexts, always update the fields with submitFields
      record.submitFields({
        type: record.Type.SALES_ORDER,
        id: salesOrderId,
        values: {
          custbody_total_paid: !isNaN(formattedAmountPaid) ? formattedAmountPaid : 0,
          custbody_balance: balance,
          custbody_hasbalance: balance !== 0,
        },
        options: {
          enableSourcing: false,
        },
      });
    }

    if (generateTemplate) {
      log.audit({
        title: "Template generation complete",
        details: `Generated ${templateHtml.length} characters of HTML`
      });
    }

    return {
      custbody_total_paid: formattedAmountPaid,
      custbody_balance: balance,
      custbody_hasbalance: balance !== 0,
      templateHtml: generateTemplate ? templateHtml : null
    };
  };

  // Backwards compatibility wrapper functions
  /**
   * Update the balance, hasBalance, and amount paid fields for template to see changes
   * @param {Record} currentSalesOrder The current sales order record
   * @param {String} recordMode The record mode (view, edit, create)
   * @returns {Object} Object containing updated field values
   */
  const updateBalanceAndPaid = async (currentSalesOrder, recordMode) => {
    return processOrderDeposits(currentSalesOrder, recordMode);
  };

  /**
   * Generate HTML for deposit template
   * @param {Number|String} salesOrderId The sales order ID
   * @returns {String} HTML template string
   */
  const runRelatedDepositTemplateGather = async (salesOrderId) => {
    try {
      log.audit({
        title: "⚡ Running Deposit Template String Construction Function...",
        details: `ID: ${salesOrderId}`,
      });

      // Modified approach - don't try to load record directly
      // Instead, work only with the ID to avoid transaction type issues
      const result = await processOrderDeposits(
        { id: salesOrderId }, // Pass minimal object with just the ID
        'view', 
        { generateTemplate: true }
      );
      
      return result.templateHtml || '';
    } catch (err) {
      log.error({
        title: "Error in deposit template generation",
        details: err
      });
      return '';
    }
  };

  /**
   * Create the deposit of the sales order after it has been successfully paid and create all needed payment references for paytrace.
   *
   * @param {Object} sc The scriptContext object.
   * @param {Number|String} eventId The event ID to tie to the deposit and related event data
   * @param {Number|String} boothId The booth to find against the event and its related data to associate
   * */
  function createDepositForOrder(sc, eventId, boothId) {
    let currentSalesOrder = sc.newRecord;
    let tranId = currentSalesOrder.getValue("tranid");
    let eventData = eventId && checkout.useEvent(eventId);
    let customerId = currentSalesOrder.getValue("entity");
    let billingAddressId = currentSalesOrder.getValue("billaddresslist");
    let locationsEnabled = runtime.isFeatureInEffect({ feature: "LOCATIONS" });
    let payGenEnabled = CS_SETTINGS.custrecord_ng_cs_enable_paytrace === "T";
    let processingId = currentSalesOrder.getValue({
      fieldId: "custbody_ng_cses_web_processing_id",
    });
    let preferredPaymentProcessor = PAYTRACE_ENABLED
      ? ""
      : CS_SETTINGS.custrecord_ng_cs_prefrd_pymt_processor;
    let cCard = {};
    // cCard.code = "123";
    cCard.adr = currentSalesOrder.getValue({ fieldId: "ccstreet" });
    cCard.zip = currentSalesOrder.getValue({ fieldId: "cczipcode" });
    cCard.method = currentSalesOrder.getValue({ fieldId: "paymentmethod" });
    cCard.id = currentSalesOrder.getValue({ fieldId: "creditcard" });
    cCard.encNumber = currentSalesOrder.getValue({
      fieldId: "custbody_ng_paytrace_web_enc_cc_data",
    });

    log.audit({
      title: `⚡ Running create deposit for order ${tranId}`,
      details: `salesorder:${currentSalesOrder.id}`,
    });

    // Store of the successful payment to the session to then pull data into the afterSubmit for later usage
    log.debug({
      title: "🗝️ Hashing session key:",
      details: `CID:${cCard.id}-BILL:${billingAddressId}-PID:${processingId}-CUST:${customerId}`,
    });

    let hashedKey = crypto
      .SHA256(
        `CID:${cCard.id}-BILL:${billingAddressId}-PID:${processingId}-CUST:${customerId}`
      )
      .toString(crypto.enc.Hex);

    let paymentResponse = SCRIPT_SESSION.get({
      name: hashedKey,
    });

    log.audit({
      title: "🔎 Retrieving SO Session Data with key:",
      details: hashedKey,
    });

    // Store of the existing sales order
    let existingSalesOrder = SCRIPT_SESSION.get({
      name: `EXISTING-SO`,
    });

    log.debug({
      title: "🟡 Payment AfterSub response:",
      details: paymentResponse,
    });

    if (paymentResponse) {
      paymentResponse = JSON.parse(paymentResponse);
    }

    try {
      let depositRecord = null;
      let targetSalesOrder = existingSalesOrder || currentSalesOrder.id;
      if (paymentResponse?.status === "TRANSACTION_APPROVED") {
        log.audit({
          title: "🟢 Transaction approved for deposit creation...",
          details: "",
        });

        depositRecord = record.create({
          type: record.Type.CUSTOMER_DEPOSIT,
          salesorder: targetSalesOrder,
          defaultValues: {
            salesorder: targetSalesOrder,
          },
        });

        depositRecord
          .setValue({ fieldId: "custbody_show_table", value: eventId })
          .setValue({ fieldId: "custbody_booth", value: boothId })
          .setValue({ fieldId: "customer", value: customerId });

        // Set related event data
        if (eventData?.custrecord_show_job) {
          log.audit({
            title: "🚧 Setting Deposit CS Job",
            details: eventData?.custrecord_show_job,
          });

          depositRecord.setValue({
            fieldId: "custrecord_show_subsidiary",
            value: eventData.custrecord_show_subsidiary,
          });
          /* .setValue({
							fieldId: "custbody_cseg_ng_cs_job",
							value: eventData.custrecord_show_job
						}); */
        }

        depositRecord
          .setValue({
            fieldId: "custbody_booth_order",
            value: targetSalesOrder,
          })
          .setValue({ fieldId: "autoapply", value: false })
          .setValue({
            fieldId: "undepfunds",
            value: CS_SETTINGS.custrecord_ng_cs_use_undep_funds,
          });

        // Ensure deposit acccount and undeposited funds profiles
        if (
          CS_SETTINGS.custrecord_ng_cs_def_dep_account &&
          CS_SETTINGS.custrecord_ng_cs_use_undep_funds === "T"
        ) {
          try {
            depositRecord.setValue({
              fieldId: "account",
              value: CS_SETTINGS.custrecord_ng_cs_def_dep_account,
            });
          } catch (err) {
            log.error({ title: "❗ Deposit Error: ", details: err });
            depositRecord
              .setValue({ fieldId: "undepfunds", value: "T" })
              .setValue({ fieldId: "account", value: "" });
          }
        } else {
          depositRecord.setValue({ fieldId: "undepfunds", value: "T" });
        }

        // Ensure payment AR account id
        if (CS_SETTINGS.custrecord_ng_cs_payment_ar_account) {
          depositRecord.setValue({
            fieldId: "aracct",
            value: CS_SETTINGS.custrecord_ng_cs_payment_ar_account,
          });
        }

        /**
         * Formats the current date to the MM/DD/YYYY format using the format module.
         * @type {String}
         * */
        let transactionDate = format.format({
          type: format.Type.DATE,
          value: new Date(),
        });

        log.debug({
          title: "🟡 Date set for transaction",
          details: `"${transactionDate}"`,
        });

        depositRecord
          .setValue({
            fieldId: "payment",
            value: paymentResponse.payGen.amount,
          })
          .setValue({
            fieldId: "custbody_ng_paytrace_trans_id",
            value: paymentResponse.payGen.paymentId,
          })
          .setValue({
            fieldId: "chargeit",
            value: false,
          })
          .setValue({
            fieldId: "creditcardprocessor",
            value: "",
          })
          .setValue({
            fieldId: "creditcard",
            value: cCard.id,
          })
          .setValue({
            fieldId: "trandate",
            value: new Date(),
          });

        log.audit({ title: "Set card id on deposit! ✅", details: cCard?.id });

        log.audit({
          title: "Linking sales order to deposit - 🔗",
          details: targetSalesOrder,
        });

        depositRecord
          .setValue({ fieldId: "salesorder", value: targetSalesOrder })
          .setValue({ fieldId: "ccapproved", value: true })
          .setValue({ fieldId: "account", value: "" })
          .setValue({ fieldId: "undepfunds", value: "T" });

        log.audit({
          title: "Set misc fields: account, ccapproved, and undepfunds - ✅",
          details: {
            ccapproved: depositRecord.getValue("ccapproved"),
            account: depositRecord.getValue("account"),
            undepfunds: depositRecord.getValue("undepfunds"),
          },
        });

        let newDepositId = depositRecord.save({
          ignoreMandatoryFields: true,
          enableSourcing: true,
        });

        if (newDepositId) {
          log.audit({
            title: `🎉 Payment successfully created for order # ${tranId}`,
            details: "",
          });

          // TODO: Create paytrace payment reference with new sales order id and transaction data from paymentId

          let paymentReferenceRequest = {
            ...paymentResponse,
            deposit: {
              id: newDepositId,
              amount: depositRecord.getValue("payment"),
            },
          };

          log.debug({
            title: "🟡 Payment reference info:",
            details: paymentReferenceRequest,
          });

          let paymentReferenceRequestUrl = getPaymentTransmissionUrl({
            type: "PayGen-Ref",
            currentSalesOrder: targetSalesOrder,
            customer: customerId,
          });

          log.audit({ title: "🟡 Creating payment ref...", details: "" });

          let createPaytraceReference = https.request({
            method: https.Method.POST,
            url: paymentReferenceRequestUrl,
            body: JSON.stringify(paymentReferenceRequest),
          });

          let { body, code } = createPaytraceReference;
          if (code !== 500) {
            let resJson = "";
            if (body) {
              resJson = JSON.parse(body);
              if (resJson?.success) {
                log.audit({
                  title: "🎉 Payment reference created successfully!",
                  details: `customerdeposit:${resJson.depositId}`,
                });
              } else {
                log.audit({
                  title: "❗ Payment reference did not create!",
                  details: "Check logs in payment handler...",
                });
                log.error({
                  title: "❌ Payment reference error:",
                  details: resJson?.error,
                });
              }
            }
          } else {
            log.error({ title: "❌ Payment reference error:", details: body });
          }
        } else {
          log.audit({ title: "🟡 Error saving deposit record!", details: "" });
        }
      } else if (paymentResponse?.status === "TRANSACTION_BYPASSED") {
        log.audit({
          title: "🟢 Transaction bypassed for deposit creation...",
          details: "",
        });

        depositRecord = record.create({
          type: record.Type.CUSTOMER_DEPOSIT,
          salesorder: targetSalesOrder,
          defaultValues: {
            salesorder: targetSalesOrder,
          },
        });

        depositRecord
          .setValue({
            fieldId: "trandate",
            value: new Date(),
          })
          .setValue({ fieldId: "custbody_show_table", value: eventId })
          .setValue({ fieldId: "custbody_booth", value: boothId })
          .setValue({ fieldId: "customer", value: customerId });

        // Set related event data
        if (eventData?.custrecord_show_job) {
          log.audit({
            title: "🚧 Setting Deposit CS Job",
            details: eventData?.custrecord_show_job,
          });

          if (locationsEnabled) {
            depositRecord.setValue({
              fieldId: "location",
              value: eventData.custrecord_show_venue,
            });
          }

          depositRecord.setValue({
            fieldId: "custrecord_show_subsidiary",
            value: eventData.custrecord_show_subsidiary,
          });
          /* .setValue({
							fieldId: "custbody_cseg_ng_cs_job",
							value: eventData.custrecord_show_job
						}); */
        }

        depositRecord
          .setValue({
            fieldId: "custbody_booth_order",
            value: targetSalesOrder,
          })
          .setValue({
            fieldId: "undepfunds",
            value: CS_SETTINGS.custrecord_ng_cs_use_undep_funds,
          });

        // Ensure deposit acccount and undeposited funds profiles
        if (
          CS_SETTINGS.custrecord_ng_cs_def_dep_account &&
          CS_SETTINGS.custrecord_ng_cs_use_undep_funds === "T"
        ) {
          try {
            depositRecord.setValue({
              fieldId: "account",
              value: CS_SETTINGS.custrecord_ng_cs_def_dep_account,
            });
          } catch (err) {
            log.error({ title: "❗ Deposit Error: ", details: err });
            depositRecord
              .setValue({ fieldId: "undepfunds", value: "T" })
              .setValue({ fieldId: "account", value: "" });
          }
        } else {
          depositRecord.setValue({ fieldId: "undepfunds", value: "T" });
        }

        /**
         * Formats the current date to the MM/DD/YYYY format using the format module.
         * @type {String}
         * */
        let transactionDate = format.format({
          type: format.Type.DATE,
          value: new Date(),
        });

        log.debug({
          title: "🟡 Date set for transaction",
          details: `"${transactionDate}"`,
        });

        log.audit({
          title: "🟡 Processing with card processor:",
          details: preferredPaymentProcessor,
        });

        depositRecord.setValue({
          fieldId: "payment",
          value: paymentResponse.payGen.amount,
        });

        // eslint-disable-next-line no-unused-expressions
        payGenEnabled &&
          depositRecord.setValue({
            fieldId: "custbody_ng_paytrace_trans_id",
            value: paymentResponse.payGen.paymentId,
          });

        depositRecord
          .setValue({
            fieldId: "creditcard",
            value: cCard.id,
          })
          .setValue({
            fieldId: "creditcardprocessor",
            value: preferredPaymentProcessor,
          });

        log.audit({ title: "Set card id on deposit! ✅", details: cCard?.id });

        log.audit({
          title: "Linking sales order to deposit - 🔗",
          details: targetSalesOrder,
        });

        depositRecord.setValue({
          fieldId: "salesorder",
          value: targetSalesOrder,
        });

        log.audit({
          title: "Set misc fields: account, ccapproved, and undepfunds - ✅",
          details: {
            ccapproved: depositRecord.getValue("ccapproved"),
            account: depositRecord.getValue("account"),
            undepfunds: depositRecord.getValue("undepfunds"),
          },
        });

        let newDepositId = depositRecord.save();
        /*
        * {
                  ignoreMandatoryFields: true,
                  enableSourcing: true,
                }
        * */
        if (newDepositId) {
          log.audit({
            title: `🎉 Payment successfully created for order # ${tranId}`,
            details: "",
          });

          log.audit({
            title: "🟡 No need for payment ref from bypassed transaction...",
            details: "",
          });
        } else {
          log.audit({ title: "🟡 Error saving deposit record!", details: "" });
        }
      } else {
        log.error({
          title: "🔴 Deposit missing session key:",
          details:
            "Ensure payment succeeded. Deposit not created - Check logs for script:customscript_ng_cses_sl_pymt_handler",
        });
      }
    } catch (err) {
      log.error({
        title: "❗ Error occurred creating deposit:",
        details: {
          error: err,
          context: runtime.executionContext,
          mode: RECORD_MODE,
        },
      });
    }
  }

  function updateOrderWithFee(sc, convFee, eventData) {
    log.audit({
      title: "⚡ Running convenience fee add...",
      details: { sc, convFee, eventData },
    });

    let currentSalesOrderId = sc.newRecord.id;
    let currentSalesOrder = record.load({
      type: record.Type.SALES_ORDER,
      id: currentSalesOrderId,
      isDynamic: true,
    });
    let convenienceFeeItem = CS_SETTINGS.custrecord_ng_cs_cc_conv_fee_item;
    let eventVenueId = eventData.custrecord_facility;

    let eventVenue = record.load({
      id: eventVenueId,
      type: "customrecord_facility",
    });

    let perLineTaxesEnabled = config
      .load({
        type: "taxpreferences",
      })
      .getValue("perlinetaxesus");
    /**
     * This variable outputs an id that represents the country of the event venue. As a string or number type.
     * @type {Number|String}
     * @example
     * 'United States' = '230'
     * */
    let venueCountry = eventVenue.getValue("custrecord_facility_country");
    let isVenueInUnitedStates = venueCountry === "230";
    log.audit({ title: "🧾 Setting convenience fee as:", details: convFee });

    try {
      if (convFee !== 0) {
        !!currentSalesOrder &&
          log.audit({
            title: "✅ Loaded record for fee addition:",
            details: currentSalesOrderId,
          });

        let lineCount = currentSalesOrder.getLineCount("item");

        currentSalesOrder.selectLine({ sublistId: "item", line: lineCount }); // Line starts at zero so selection would be the end

        currentSalesOrder
          .setCurrentSublistValue({
            sublistId: "item",
            fieldId: "item",
            value: convenienceFeeItem,
          })
          .setCurrentSublistValue({
            sublistId: "item",
            fieldId: "quantity",
            value: 1,
          })
          .setCurrentSublistValue({
            sublistId: "item",
            fieldId: "price",
            value: -1,
          })
          .setCurrentSublistValue({
            sublistId: "item",
            fieldId: "rate",
            value: convFee,
          });

        if (!isVenueInUnitedStates || perLineTaxesEnabled) {
          if (isVenueInUnitedStates && perLineTaxesEnabled) {
            currentSalesOrder.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "taxrate1",
              forceSyncSourcing: true,
              value: "0",
            });
          } else if (!isVenueInUnitedStates) {
            currentSalesOrder.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "taxrate1",
              forceSyncSourcing: true,
              value: "0",
            });
            currentSalesOrder.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "taxrate2",
              forceSyncSourcing: true,
              value: "0",
            });
          }
        }

        // Not needed in standard mode
        currentSalesOrder.commitLine({ sublistId: "item" });

        if (!isVenueInUnitedStates || perLineTaxesEnabled) {
          const itemLineCount = currentSalesOrder.getLineCount({
            sublistId: "item",
          });

          for (let ln = 0; ln < itemLineCount; ln++) {
            const itemId = currentSalesOrder.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: ln,
            });

            if (itemId === convenienceFeeItem) {
              if (isVenueInUnitedStates && perLineTaxesEnabled) {
                currentSalesOrder.setSublistValue({
                  sublistId: "item",
                  fieldId: "taxrate1",
                  value: "0",
                  line: ln,
                });
              } else if (!isVenueInUnitedStates) {
                currentSalesOrder.setSublistValue({
                  sublistId: "item",
                  fieldId: "taxrate1",
                  value: "0",
                  line: ln,
                });
                currentSalesOrder.setSublistValue({
                  sublistId: "item",
                  fieldId: "taxrate2",
                  value: "0",
                  line: ln,
                });
              }
              currentSalesOrder.commitLine({ sublistId: "item" });
            }
          }
        }

        let updatedOrderId = currentSalesOrder.save();

        if (updatedOrderId) {
          log.audit({
            title: "🟢 Order saved with convenience fee successfully",
            details: "",
          });
        } else {
          log.audit({
            title: "🔴 Order saved without convenience fee...",
            details: "",
          });
        }
      } else {
        log.audit({
          title: "🛑 Convenience Fee is 0 no need to add",
          details: "",
        });
      }

      return false;
    } catch (err) {
      log.error({
        title: `❌ Error adding convenience fee to order! - ${currentSalesOrderId}`,
        details: err,
      });
    }
  }

  function runPreventAdditionalOrdersCheck(sc, eventId, boothId, entity) {
    let currentSalesOrder = sc.newRecord;
    let preventAdditionalOrderEnabled =
      CS_SETTINGS && CS_SETTINGS?.custrecord_ng_cs_prev_adtl_orders === "T";
    let boothOrderActive = BOOTH_ORDER_FORMS.includes(CURRENT_ORDER_FORM);

    log.audit({
      title: "⚡ Running additional booth order check...",
      details: "",
    });
    if (preventAdditionalOrderEnabled && boothOrderActive) {
      log.audit({
        title: "🟢 Prevent additional orders enabled!",
        details: "Looking for relevant orders...",
      });
      // Run search on all orders that have been placed more than once on an exhibitor
      let defaultOrderType = CS_SETTINGS?.custrecord_ng_cs_def_exhb_ord_type;

      let searchFilters = [
        ["custbody_show_table", "anyof", [eventId]],
        "and",
        ["custbody_booth", "anyof", [boothId]],
        "and",
        ["custbody_ng_cs_order_type", "anyof", [defaultOrderType]],
        "and",
        ["mainline", "is", "T"],
        "and",
        ["custbody_to_be_deleted", "is", "F"],
        "and",
        ["entity", "anyof", [entity]],
      ];

      let salesOrderSearchObj = search.create({
        type: search.Type.SALES_ORDER,
        filters: searchFilters,
        columns: [],
      });

      let salesOrderResultsCount = salesOrderSearchObj.runPaged().count;
      let salesOrderResults = [];

      getAllResultsFor(salesOrderSearchObj, (result) => {
        salesOrderResults.push(result.id);
      });

      if (salesOrderResultsCount !== 0) {
        let filteredResults = salesOrderResults.filter(
          (soId) => soId !== currentSalesOrder.id
        );
        if (filteredResults.length !== 0) {
          log.audit({
            title: "🧾 Existing Sales order is defined:",
            details: `Storing "${filteredResults[0].id}" in session`,
          });
          SCRIPT_SESSION.set({
            name: "EXISTING-SO",
            value: filteredResults[0],
          });
        } else {
          log.audit({
            title: "🔴 No additional orders found for customer",
            details: "",
          });
        }
      }
    } else {
      log.audit({
        title:
          "🔴 Order not a booth order or feature is disabled - Prevent booth orders",
        details: "",
      });
    }
  }

  function runOrderConsolidation(currentSalesOrder, existingSalesOrder) {
    log.audit({ title: "⚡ Running order consolidation...", details: "" });

    let soRec;
    let soNum;

    if (existingSalesOrder) {
      log.audit({
        title: "🟢 Existing Sales order - running consolidation",
        details: "",
      });

      record.submitFields({
        type: record.Type.SALES_ORDER,
        id: currentSalesOrder.id,
        values: { custbody_ng_cs_originating_order: existingSalesOrder },
        options: { ignoreMandatoryFields: true, enableSourcing: false },
      });

      /* * * * * * * * * * * *  *
       * WEB ORDER CONSOLIDATION *
       * * * * * * * * * * * * * */
      // If there is an Extra sales order id present run the order to be consolidated into the previous order. That is noted from the new soId
      log.audit({
        title: "Checking for extra sales order...",
        details: `⌛ - ${existingSalesOrder}`,
      });

      let updatedSalesOrderId;
      if (existingSalesOrder && !isNaN(Number(existingSalesOrder))) {
        log.audit({
          title: "Extra sales order present! - Running consolidation",
          details: "⌛",
        });
        log.debug({
          title: "Extra sales order present! - Running consolidation on order:",
          details: existingSalesOrder,
        });

        // - region Web order consolidation
        soRec = record.load({ type: "salesorder", id: currentSalesOrder.id });

        let eSoRec = record.load({
          type: "salesorder",
          id: existingSalesOrder,
          isDynamic: true,
        });

        // Get line count for items
        let newLines = soRec.getLineCount({ sublistId: "item" });
        soNum = soRec.getValue({ fieldId: "tranid" });
        let esoNum = eSoRec.getValue({ fieldId: "tranid" });

        log.audit({
          title: `Merging item lines from ${soNum} ➡ ${esoNum}...`,
          details: "⌛",
        });
        try {
          let colsToCheck = [
            "custcol_linecode",
            "description",
            "custcol_custom_carpet_size",
            "custcol_cost_is_estimated",
            "custcol_attached_document",
            "custcol_ng_cs_rental_loc",
            "custcol_ng_cs_rental_start_date",
            "custcol_ng_cs_rental_end_date",
          ];

          for (let itemLine = 0; itemLine < newLines; itemLine++) {
            let currItemId = soRec.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: itemLine,
            });
            let currItemQty = soRec.getSublistValue({
              sublistId: "item",
              fieldId: "quantity",
              line: itemLine,
            });
            let currPriceLevel = soRec.getSublistValue({
              sublistId: "item",
              fieldId: "price",
              line: itemLine,
            });
            let currItemMemo = soRec.getSublistValue({
              sublistId: "item",
              fieldId: "custcol_ng_cs_item_memo",
              line: itemLine,
            });
            let currItemEstimated = soRec.getSublistValue({
              sublistId: "item",
              fieldId: "custcol_cost_is_estimated",
              line: itemLine,
            });
            let currItemRate = soRec.getSublistValue({
              sublistId: "item",
              fieldId: "rate",
              line: itemLine,
            });
            let currItemAmount = soRec.getSublistValue({
              sublistId: "item",
              fieldId: "amount",
              line: itemLine,
            });
            let currOrderAttachment = soRec.getSublistValue({
              sublistId: "item",
              fieldId: "custcol_ng_cs_item_attachments",
              line: itemLine,
            });

            eSoRec.selectNewLine({ sublistId: "item" });
            eSoRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "item",
              value: currItemId,
            });
            eSoRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "quantity",
              value: currItemQty,
            });
            eSoRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "price",
              value: currPriceLevel,
            });
            eSoRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "custcol_ng_cs_item_memo",
              value: currItemMemo,
            });
            eSoRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "custcol_cost_is_estimated",
              value: currItemEstimated,
            });
            eSoRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "custcol_ng_cs_item_attachments",
              value: currOrderAttachment,
            });

            if (currPriceLevel === "-1") {
              eSoRec.setCurrentSublistValue({
                sublistId: "item",
                fieldId: "rate",
                value: currItemRate,
              });
              eSoRec.setCurrentSublistValue({
                sublistId: "item",
                fieldId: "amount",
                value: currItemAmount,
              });
            }

            eSoRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "options",
              value: soRec.getSublistValue({
                sublistId: "item",
                fieldId: "options",
                line: itemLine,
              }),
            });

            colsToCheck.forEach(function (col) {
              eSoRec.setCurrentSublistValue({
                sublistId: "item",
                fieldId: col,
                value: soRec.getSublistValue({
                  sublistId: "item",
                  fieldId: col,
                  line: itemLine,
                }),
              });
            });

            eSoRec.commitLine({ sublistId: "item" });
          }

          eSoRec.setValue({
            fieldId: "custbody_balance",
            value:
              Number(eSoRec.getValue({ fieldId: "total" })) -
              Number(eSoRec.getValue({ fieldId: "custbody_total_paid" })),
          });

          eSoRec.setValue({ fieldId: "paymentmethod", value: "" });
          eSoRec.setValue({ fieldId: "ccnumber", value: "" });
          eSoRec.setValue({ fieldId: "ccexpiredate", value: "" });
          eSoRec.setValue({ fieldId: "ccname", value: "" });
          eSoRec.setValue({ fieldId: "ccstreet", value: "" });
          eSoRec.setValue({ fieldId: "cczipcode", value: "" });

          let newSaveSuccess = false;
          try {
            updatedSalesOrderId = eSoRec.save({
              ignoreMandatoryFields: true,
              enableSourcing: true,
            });

            log.audit({
              title: "Order consolidation complete",
              details: `From: ${currentSalesOrder.id} -- To: ${updatedSalesOrderId}`,
            });

            newSaveSuccess = true;
          } catch (err) {
            log.error({
              title: "❗ An error occurred consolidating orders!",
              details: err,
            });
          }

          if (newSaveSuccess) {
            try {
              record.submitFields({
                type: "salesorder",
                id: currentSalesOrder.id,
                values: {
                  custbody_to_be_deleted: true,
                  custbody_ng_cs_rcs_conv_fee: "",
                },
                options: { ignoreMandatoryFields: true, enableSourcing: false },
              });
            } catch (err) {
              log.error({
                title: "❗ An error occurred marking order for deletion:",
                details: err,
              });
            }
          }
        } catch (err) {
          log.error({
            title:
              "🔴 Error encountered copying lines from new web order to existing order:",
            details: err,
          });
        }
        // endregion
      }
    } else {
      log.audit({
        title: "🔴 No existing SO found consolidation not running...",
        details: "",
      });
    }
  }

  /**
   * A type of object used to populate fields for usage of Add line scripting (ALS).
   * A item adding/ordering addition feature.
   *
   * @typedef {Object} lineScriptingItems
   * @property {Array<Number>} sqftItems all
   * @property {Array<Number>} daysItems
   * @property {Array<Number>} sqdItems
   * @property {Array<Number>} freightItems
   * @property {Array<Number>} colorItems
   * @property {Array<Number>} sizeItems
   * @property {Array<Number>} orientationItems
   * @property {Array<Number>} laborItems
   * @property {Array<Number>} durationItems
   * @property {Array<Number>} graphicsItems
   * */

  /**
   * Searches for items flagged for use with the item selection fields and loads the appropriate settings arrays
   * - **Only to be used in ADD LINE SCRIPTING FORMS**
   * @param {Number|String} subsidiary Internal ID of the subsidiary to filter items on
   * @returns {lineScriptingItems} Items needed to populate fields in item order adding utility
   */
  function getAddLineSelectionItems(subsidiary) {
    log.audit({
      title: "⚡ Running Add line selection items gather...",
      details: "",
    });
    let items = {
      sqftItems: [],
      daysItems: [],
      sqdItems: [],
      freightItems: [],
      colorItems: [],
      sizeItems: [],
      orientationItems: [],
      laborItems: [],
      durationItems: [],
      graphicsItems: [],
    };

    let addLineActive = LINE_ADD_FORMS.includes(CURRENT_ORDER_FORM);

    let orientationEnabled =
      CS_SETTINGS?.custrecord_ng_cs_enable_orientation_opt === "T";
    let graphicsEnabled =
      CS_SETTINGS?.custrecord_ng_cs_enable_graphics_option === "T";

    // Check if form is an line add scripting form just in case function is called not in a line scripting form

    if (addLineActive) {
      log.audit({
        title: "🟢 Add line form active gathering items...",
        details: "",
      });

      let filtersSub = [
        ["isinactive", "is", "F"],
        "and",
        [FIELD_MAPPING.item.sqft, "is", "T"],
        "or",
        [FIELD_MAPPING.item.days, "is", "T"],
        "or",
        [FIELD_MAPPING.item.showdur, "is", "T"],
        "or",
        [FIELD_MAPPING.item.freight, "is", "T"],
        "or",
        [FIELD_MAPPING.item.labor, "is", "T"],
      ];

      if (subsidiary) {
        filtersSub.push("and", ["subsidiary", "anyof", ["@NONE@", subsidiary]]);
      }

      let itemSearchObject = search.create({
        type: search.Type.ITEM,
        filters: filtersSub,
        columns: [
          search.createColumn({ name: FIELD_MAPPING.item.sqft }),
          search.createColumn({ name: FIELD_MAPPING.item.days }),
          search.createColumn({ name: FIELD_MAPPING.item.showdur }),
          search.createColumn({ name: FIELD_MAPPING.item.freight }),
          search.createColumn({ name: FIELD_MAPPING.item.labor }),
        ],
      });

      let itemResultsCount = itemSearchObject.runPaged().count;

      if (itemResultsCount !== 0) {
        getAllResultsFor(itemSearchObject, (result) => {
          let resultItemObj = {
            id: result.id,
            isSquareFt: result.getValue("custitem_is_sqft"),
            isDaysCalc: result.getValue("custitem_is_days"),
            isShowDuration: result.getValue("custitem_show_duration"),
            isFreight: result.getValue("custitem_is_freight"),
            isLabor: result.getValue("custitem_labor_item"),
          };

          if (resultItemObj.isSquareFt && resultItemObj.isDaysCalc) {
            items.sqdItems.push(resultItemObj.id); // Add square foot and days combined item
          } else if (resultItemObj.isSquareFt) {
            items.sqdItems.push(resultItemObj.id); // Add square foot only item
          } else if (resultItemObj.isDaysCalc) {
            items.daysItems.push(resultItemObj.id); // Add days calc only item
          }

          // Add show duration item
          resultItemObj.isShowDuration &&
            items.durationItems.push(resultItemObj.id);

          // Add freight item
          resultItemObj.isFreight && items.freightItems.push(resultItemObj.id);

          // Add labor item
          resultItemObj.isLabor && items.laborItems.push(resultItemObj.id);
        });
      }

      // Then run search for matrix item options
      let matrixFilters = [
        ["isinactive", "is", "F"],
        "and",
        ["matrix", "is", "T"],
        "and",
        ["matrixchild", "is", "F"],
        "and",
        [FIELD_MAPPING.item.color, "is", "T"],
        "or",
        [FIELD_MAPPING.item.size, "is", "T"],
      ];

      if (orientationEnabled) {
        matrixFilters.push("or", [FIELD_MAPPING.item.orientation, "is", "T"]);
      }

      if (graphicsEnabled) {
        matrixFilters.push("or", [FIELD_MAPPING.item.graphics, "is", "T"]);
      }

      if (subsidiary) {
        matrixFilters.push("and", [
          "subsidiary",
          "anyof",
          ["@NONE@", subsidiary],
        ]);
      }

      let matrixSearchObject = search.create({
        type: search.Type.ITEM,
        filters: matrixFilters,
        columns: [
          search.createColumn({ name: "itemid" }),
          search.createColumn({ name: FIELD_MAPPING.item.color }),
          search.createColumn({ name: FIELD_MAPPING.item.size }),
        ],
      });

      let matrixItemResultsCount = matrixSearchObject.runPaged().count;

      if (matrixItemResultsCount !== 0) {
        getAllResultsFor(matrixSearchObject, (result) => {
          let resultObject = {
            id: result.id,
            hasColors: result.getValue("custitem_has_color_options"),
            hasSizes: result.getValue("custitem_has_size_options"),
            hasOrientations: result.getValue("custitem_has_orient_options"),
            hasGraphics: result.getValue("custitem_ng_cs_has_graphic_options"),
          };
          let { hasColors, hasSizes, hasOrientations, hasGraphics, id } =
            resultObject;

          hasColors && items.colorItems.push(id);
          hasSizes && items.sizeItems.push(id);

          if (graphicsEnabled) {
            hasGraphics && items.graphicsItems.push(id);
          }

          if (orientationEnabled) {
            hasOrientations && items.orientationItems.push(id);
          }
        });
      }
    }

    return items;
  }

  /**
   * Used to init order type on create of a certain type of order with a specific type of form preloaded
   * @type {Function}
   * @param {Object} sc The scriptContext object
   * @returns void
   * */
  function handleDefaultOrderType(sc) {
    let currentSalesOrder = sc.newRecord;
    let showManagementActive =
      SHOW_MANAGEMENT_FORMS.includes(CURRENT_ORDER_FORM);
    let boothOrderActive = BOOTH_ORDER_FORMS.includes(CURRENT_ORDER_FORM);
    let defaultBoothOrderType = CS_SETTINGS.custrecord_ng_cs_def_exhb_ord_type;
    let defaultShowManagementOrderType =
      CS_SETTINGS.custrecord_ng_cs_def_show_mgmt_ord_type;

    log.audit({ title: "⚡ Running default order set...", details: "" });

    if (CS_SETTINGS) {
      log.audit({ title: "🟢 CS Settings defined", details: "" });
      if (showManagementActive) {
        log.audit({
          title: "🟢 Show management order active setting default type:",
          details: `"${defaultShowManagementOrderType}"`,
        });
        currentSalesOrder.setValue({
          fieldId: "custbody_ng_cs_order_type",
          value: defaultShowManagementOrderType,
        });
      }

      if (boothOrderActive) {
        log.audit({
          title: "🟢 Booth order active setting default type:",
          details: `"${defaultBoothOrderType}"`,
        });
        currentSalesOrder.setValue({
          fieldId: "custbody_ng_cs_order_type",
          value: defaultBoothOrderType,
        });
      }
    }
  }

  /*

  const roundOff = (num, places) => {
    const x = Math.pow(10, places);
    return Math.round(num * x) / x;
  };

  function delayScript(delay) {
    let startTime;
    let curTime;
    startTime = new Date().getTime();
    let timeDiff = 0;

    while (timeDiff < delay) {
      curTime = new Date().getTime();
      timeDiff = curTime - startTime; // in ms
    }
  }

*/
  function getStateShortName(stateId) {
    return query
      .runSuiteQL({
        query: `SELECT shortname FROM state WHERE id = '${stateId}'`,
      })
      .asMappedResults()[0].shortname;
  }

  function getCountryShortName(countryId) {
    return query
      .runSuiteQL({
        query: `SELECT id FROM COUNTRY WHERE uniquekey = '${countryId}'`,
      })
      .asMappedResults()[0].id;
  }

  function getEstimatedItemsTotal(currentSalesOrder) {
    let estimatedItemsTotal = 0;
    let lineCount = currentSalesOrder.getLineCount({ sublistId: "item" });

    for (let i = 0; i < lineCount; i++) {
      let isEstimated = currentSalesOrder.getSublistValue({
        sublistId: "item",
        fieldId: "custcol_cost_is_estimated",
        line: i,
      });

      if (isEstimated) {
        let amount = currentSalesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "amount",
          line: i,
        });

        estimatedItemsTotal += amount;
      }
    }

    return estimatedItemsTotal;
  }

  /**
   * Adds the following print fields for use within advanced pdfs:
   * - custpage_functions_list - a list of event functions
   * - custpage_space_bookings_list - a list of bookings with pricing
   * - custpage_item_schedule - a list of items grouped by the day they are scheduled
   * - custpage_items_list - a list of items with their pricing grouped by category
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @param {Form} scriptContext.form - Current form
   * @param {ServerRequest} scriptContext.request - HTTP request information sent from the browser for a client action only.
   * @since 2015.2
   */
  function addPrintFields(scriptContext) {
    const { newRecord, form } = scriptContext;

    const eventId = newRecord.getValue({
      fieldId: "custbody_show_table",
    });

    log.audit({
      title: "Adding Print Fields For Event:",
      details: eventId,
    });

    // Event Timeline - populated with functions
    const functions = [];
    if (eventId) {
      // All columns for search and later reference
      const columns = [
        "custrecord_nges_fun_agr",
        "custrecord_nges_fun_event",
        "custrecord_nges_fun_booking",
        "created",
        "custrecord_nges_fun_description",
        "custrecord_nges_fun_end_date",
        "custrecord_nges_fun_end_time",
        "custrecord_nges_fun_exp",
        "externalid",
        "custrecord_nges_fun_contact",
        "custrecord_nges_fun_type",
        "custrecord_nges_fun_gtd",
        "isinactive",
        "internalid",
        "lastmodified",
        "lastmodifiedby",
        "custrecord_ng_cs_link_to_booking",
        "name",
        "custrecord_nges_fun_room_setup",
        "scriptid",
        "custrecord_nges_fun_start_date",
        "custrecord_nges_fun_start_time",
        "custrecord_nges_fun_status",
        "custrecord_nges_fun_title",
        "custrecord_nges_fun_venue",
        "custrecord_nges_fun_venue_space",
      ];

      // Sort columns to filter out of columns arr when building the search object
      const sortColumns = [
        "custrecord_nges_fun_start_date",
        "custrecord_nges_fun_start_time",
      ];

      const eventSearch = search.create({
        type: "customrecord_ng_cs_event_function",
        filters: [["custrecord_nges_fun_event", "anyof", eventId]],
        columns: columns.map((columnName) => {
          return search.createColumn({
            name: columnName,
            ...(sortColumns.includes(columnName) && {
              sort: search.Sort.ASC,
            }),
          });
        }),
      });

      getAllResultsFor(eventSearch, (result) => {
        functions.push(
          Object.fromEntries(
            columns.map((column) => {
              return [column, result.getValue({ name: column })];
            })
          )
        );
      });
    }

    log.audit({
      title: "Adding Custpage Functions List:",
      details: functions,
    });

    // Add functions to new functions long text field
    form.addField({
      id: "custpage_functions_list",
      type: serverWidget.FieldType.LONGTEXT,
      label: "Functions List",
    }).defaultValue = JSON.stringify(functions);

    // Venue Rental / Item Charges (derived from the estimate items sublist)

    /* Steps
    Grab the item
    - get the category
    - determine if space booking or item
    - grab the remaining fields depending on the item
    - group the items by category then subcategory
    - save the space booking to its own field
    - save the items to its own field
     */

    const priceFormatter = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    });

    const itemLineCount = newRecord.getLineCount({
      sublistId: "item",
    });

    const ITEM_TYPE_MAPPING = {
      Description: record.Type.DESCRIPTION_ITEM,
      Discount: record.Type.DISCOUNT_ITEM,
      Group: record.Type.ITEM_GROUP,
      InvtPart: record.Type.INVENTORY_ITEM,
      Kit: record.Type.KIT_ITEM,
      Markup: record.Type.MARKUP_ITEM,
      NonInvtPart: record.Type.NON_INVENTORY_ITEM,
      OthCharge: record.Type.OTHER_CHARGE_ITEM,
      Service: record.Type.SERVICE_ITEM,
      // Add any additional itemType: recordType pairs as needed
    };

    log.audit({
      title: "Iterating Space Bookings:",
      details: {
        itemLineCount,
      },
    });

    const spaceBookings = [];
    const items = [];
    for (let i = 0; i < itemLineCount; i++) {
      const itemId = newRecord.getSublistValue({
        sublistId: "item",
        fieldId: "item",
        line: i,
      });

      const itemType = newRecord.getSublistValue({
        sublistId: "item",
        fieldId: "itemtype",
        line: i,
      });

      const itemRecordType = ITEM_TYPE_MAPPING[itemType];

      if (!itemId || !itemRecordType) {
        return;
      }

      const item = record.load({
        type: itemRecordType,
        id: itemId,
      });

      const custitem_item_category = item.getText({
        fieldId: "custitem_item_category",
      });

      log.audit({
        title: "Determining Line Type:",
        details: {
          custitem_item_category,
        },
      });

      if (custitem_item_category === "Space Booking") {
        /* Venue Rental Columns
        Date - from the booking
        Time - from the item line
        Room - from the booking
        Usage - from the booking
        Price - from the item line
        Total - same as price for now
         */

        const bookingId = newRecord.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_ng_cs_item_cs_event_booking",
          line: i,
        });

        const booking = record.load({
          type: "customrecord_ng_cs_event_booking",
          id: bookingId,
        });

        const custrecord_ng_cs_eb_start_date = booking.getText({
          fieldId: "custrecord_ng_cs_eb_start_date",
        });

        const custrecord_ng_cs_eb_end_date = booking.getText({
          fieldId: "custrecord_ng_cs_eb_end_date",
        });

        const custrecord_ng_cs_eb_start_time = booking.getText({
          fieldId: "custrecord_ng_cs_eb_start_time",
        });

        const custrecord_ng_cs_eb_end_time = booking.getText({
          fieldId: "custrecord_ng_cs_eb_end_time",
        });

        const custrecord_ng_cs_eb_space_ui = booking.getText({
          fieldId: "custrecord_ng_cs_eb_space_ui",
        });

        const custrecord_ng_cs_eb_book_status = booking.getText({
          fieldId: "custrecord_ng_cs_eb_book_status",
        });

        const amount = newRecord.getSublistValue({
          sublistId: "item",
          fieldId: "amount",
          line: i,
        });

        const spaceBooking = {
          custitem_item_category,
          custrecord_ng_cs_eb_start_date,
          custrecord_ng_cs_eb_end_date,
          custrecord_ng_cs_eb_start_time,
          custrecord_ng_cs_eb_end_time,
          custrecord_ng_cs_eb_space_ui,
          custrecord_ng_cs_eb_book_status,
          amount,
          amount_label: priceFormatter.format(amount),
        };

        log.audit({
          title: "Pushing Space Booking Details:",
          details: spaceBooking,
        });

        spaceBookings.push(spaceBooking);
      } else {
        /* Item Charges Columns
        Category - from the item record
        Subcategory - from the item record
        Time - from the item line
        Item / Title - from the item line
        Qty / Price - from the item line
        Subtotal - from the item line
        Total - same as price for now
         */

        const custitem_subcategory = item.getText({
          fieldId: "custitem_subcategory",
        });

        const custcol_ng_cs_item_cs_event_start_t = newRecord.getSublistText({
          sublistId: "item",
          fieldId: "custcol_ng_cs_item_cs_event_start_t",
          line: i,
        });

        const custcol_ng_cs_item_cs_event_end_t = newRecord.getSublistText({
          sublistId: "item",
          fieldId: "custcol_ng_cs_item_cs_event_end_t",
          line: i,
        });

        // load the function and get the start date
        const eventFunctionId = newRecord.getSublistValue({
          sublistId: "item",
          fieldId: "custcoll_ng_cs_item_cs_event_function",
          line: i,
        });

        let date;
        if (eventFunctionId) {
          const eventFunction = record.load({
            type: "customrecord_ng_cs_event_function",
            id: eventFunctionId,
            isDynamic: true,
          });

          const dateValue = eventFunction.getValue({
            fieldId: "custrecord_nges_fun_start_date",
          });

          if (dateValue) {
            date = new Intl.DateTimeFormat("en-US", {
              dateStyle: "full",
            }).format(dateValue);
          }
        }

        const itemName = newRecord.getSublistText({
          sublistId: "item",
          fieldId: "item",
          line: i,
        });

        const quantity = newRecord.getSublistText({
          sublistId: "item",
          fieldId: "quantity",
          line: i,
        });

        const rate = newRecord.getSublistValue({
          sublistId: "item",
          fieldId: "rate",
          line: i,
        });

        const amount = newRecord.getSublistValue({
          sublistId: "item",
          fieldId: "amount",
          line: i,
        });

        const itemCharge = {
          custitem_item_category,
          custitem_subcategory,
          custcol_ng_cs_item_cs_event_start_t,
          custcol_ng_cs_item_cs_event_end_t,
          date,
          item: itemName,
          quantity,
          rate,
          rate_label: priceFormatter.format(rate),
          amount,
          amount_label: priceFormatter.format(amount),
        };

        log.audit({
          title: "Pushing Item Charge:",
          details: itemCharge,
        });

        items.push(itemCharge);
      }
    }

    // Add the custom fields for Venue Rental Table
    const spaceBookingsTotal = spaceBookings.reduce(
      (total, spaceBooking) => total + spaceBooking.amount,
      0
    );

    const spaceBookingsList = {
      items: spaceBookings.sort((a, b) =>
        a.custrecord_ng_cs_eb_start_date.localeCompare(
          b.custrecord_ng_cs_eb_start_date
        )
      ),
      total: spaceBookingsTotal,
      total_label: priceFormatter.format(spaceBookingsTotal),
    };

    log.audit({
      title: "Adding Custpage Space Bookings List:",
      details: spaceBookingsList,
    });

    form.addField({
      id: "custpage_space_bookings_list",
      type: serverWidget.FieldType.LONGTEXT,
      label: "Space Bookings List",
    }).defaultValue = JSON.stringify(spaceBookingsList);

    /* Group the items by category then subcategory
    Goes from an array of items to grouped arrays like this:
    [{
      category: string // 'category name',
      total: number // amount total for the category
      total_label: string // amount total for the category
      items: [
        {
          custitem_item_category: string,
          custitem_subcategory: string,
          custcol_ng_cs_item_cs_event_start_t: string,
          custcol_ng_cs_item_cs_event_end_t: string,
          item: string,
          quantity: number,
          rate: number,
          rate_label: string,
          amount: number,
          amount_label: string,
        }, ...other items
      ]
    ]
    */

    const groupedItems = [];

    if (items.length > 0) {
      const categoryGroups = {};

      items.forEach((item) => {
        const category = item.custitem_item_category || "Other";
        if (categoryGroups[category]) {
          categoryGroups[category].push(item);
        } else {
          categoryGroups[category] = [item];
        }
      });

      Object.entries(categoryGroups).forEach(([category, items]) => {
        const total = items.reduce(
          (total, category) => total + category.amount,
          0
        );

        groupedItems.push({
          category,
          total,
          total_label: priceFormatter.format(total),
          items,
        });
      });
    }

    // Add the custom fields for Venue Rental Table
    const groupedItemsTotal = items.reduce(
      (total, item) => total + item.amount,
      0
    );

    const itemsList = {
      items: groupedItems.sort((a, b) => a.category.localeCompare(b.category)),
      total: groupedItemsTotal,
      total_label: priceFormatter.format(groupedItemsTotal),
    };

    log.audit({
      title: "Adding Custpage Items List:",
      details: itemsList,
    });

    // Add the custom fields for Item Charges Table
    form.addField({
      id: "custpage_items_list",
      type: serverWidget.FieldType.LONGTEXT,
      label: "Items List",
    }).defaultValue = JSON.stringify(itemsList);

    const groupedScheduleItems = [];

    if (items.length > 0) {
      const scheduleGroups = {};

      items.forEach((item) => {
        const date = item.date || "No Date Specified";
        if (scheduleGroups[date]) {
          scheduleGroups[date].push(item);
        } else {
          scheduleGroups[date] = [item];
        }
      });

      Object.entries(scheduleGroups).forEach(([date, items]) => {
        const total = items.reduce((total, item) => total + item.amount, 0);

        groupedScheduleItems.push({
          date,
          total,
          total_label: priceFormatter.format(total),
          items,
        });
      });
    }

    const itemsSchedule = {
      items: groupedScheduleItems.sort((a, b) =>
        a.category?.localeCompare(b?.category)
      ),
      total: groupedItemsTotal,
      total_label: priceFormatter.format(groupedItemsTotal),
    };

    log.audit({
      title: "Adding Custpage Items Schedule:",
      details: itemsSchedule,
    });

    // Add the custom fields for Item Schedule Table
    form.addField({
      id: "custpage_items_schedule",
      type: serverWidget.FieldType.LONGTEXT,
      label: "Items Schedule",
    }).defaultValue = JSON.stringify(itemsSchedule);
  }

  function hideAreaFields(sc, settings) {
    let currRec = sc.newRecord;
    let form = sc.form;

    let useEventAreaDetailEnabled =
      settings.custrecord_ng_cs_use_area_detail === "T" ? true : false;

    log.debug("useEventAreaDetailEnabled", useEventAreaDetailEnabled);

    if (useEventAreaDetailEnabled) {
      let sublist = form.getSublist({
        id: "item",
      });

      let areaField = sublist.getField({
        id: "custcol_ng_cs_area",
      });

      log.debug("sublist", sublist);
      log.debug("areaField", areaField);

      try {
        if (areaField) {
          areaField.updateDisplayType({
            displayType: serverWidget.FieldDisplayType.HIDDEN,
          });
        }
      } catch (e) {
        log.debug("Error Hiding Fields", e);
      }
    } else {
      let sublist = form.getSublist({
        id: "item",
      });

      let areaField = sublist.getField({
        id: "custcol_ng_cs_area_detail",
      });

      try {
        if (areaField) {
          areaField.updateDisplayType({
            displayType: serverWidget.FieldDisplayType.HIDDEN,
          });
        }
      } catch (e) {
        log.debug("Error Hiding Fields", e);
      }
    }
  }

  /**
   * Loads in a sublist for surcharges based on the event on the sales order
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @param {Form} scriptContext.form - Current form
   * @param {ServerRequest} scriptContext.request - HTTP request information sent from the browser for a client action only.
   * @since 2024.10
   */
  function addSurchargeFields(scriptContext) {
    const { newRecord, form } = scriptContext;

    const eventId = newRecord.getValue({
      fieldId: "custbody_show_table",
    });

    if (eventId) {
      // The surcharges for the event
      const surcharges = getSurchargesForEvent(eventId);
      if (surcharges.length === 0) {
        return;
      }

      let surchargesSelected = [];
      try {
        surchargesSelected = JSON.parse(
          scriptContext.newRecord.getValue({
            fieldId: "custbody_ng_cses_serv_chgs_selected",
          })
        ).selected;
      } catch (e) {
        log.error({
          title: "Error Parsing Surcharges Selected",
          details: e,
        });
      }

      log.audit({
        title: "🧾 Surcharges Selected:",
        details: surchargesSelected,
      });

      // Add surcharges group
      form.addFieldGroup({
        id: "custpage_surcharges_group",
        label: "Surcharges",
        tab: "items",
      });

      // Currency formatter
      const currencyFormatter = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      });

      // Percentage formatter
      const percentageFormatter = new Intl.NumberFormat("en-US", {
        style: "percent",
        minimumFractionDigits: 2,
      });

      // Add checkbox for each surcharge with memo as field help
      surcharges.forEach((surcharge) => {
        const { id, name, memo, charge_type, flat_charge, percentage } =
          surcharge;
        const chargeType = charge_type === 1 ? "Percent" : "Flat Rate";
        const chargeTypeValue =
          charge_type === 1
            ? percentageFormatter.format(percentage)
            : currencyFormatter.format(flat_charge);
        const field = form.addField({
          id: `custpage_surcharge_${id}`,
          type: serverWidget.FieldType.CHECKBOX,
          label: `${name} - ${chargeType}: ${chargeTypeValue}`,
          container: "custpage_surcharges_group",
        });
        field.helpText = `Recalculate fees${memo ? `- ${memo}` : ""}`;
        field.defaultValue = surchargesSelected.includes(id) ? "T" : "F";
      });
    }
  }

  /**
   * Saves the surcharges selected.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2024.10
   */
  function saveSurchargesSelected(scriptContext) {
    const { newRecord } = scriptContext;

    const eventId = newRecord.getValue({
      fieldId: "custbody_show_table",
    });

    if (eventId) {
      // The surcharges for the event
      const surcharges = getSurchargesForEvent(eventId);
      if (surcharges.length === 0) {
        return;
      }

      log.audit({
        title: "💾 Saving Surcharges Selected To Hidden Field:",
        details: eventId,
      });

      const surchargesSelected = [];
      surcharges.forEach((surcharge) => {
        const { id } = surcharge;
        const surchargeSelected = newRecord.getValue({
          fieldId: `custpage_surcharge_${id}`,
        });

        log.audit({
          title: `Surcharge Field ${id}`,
          details: surchargeSelected,
        });

        // Save the surcharge if selected or if the record is new
        if (
          surchargeSelected === "T" ||
          scriptContext.type === scriptContext.UserEventType.CREATE
        ) {
          surchargesSelected.push(id);
        }
      });

      log.audit({
        title: "🧾 Surcharge Selected:",
        details: surchargesSelected.join(","),
      });

      newRecord.setValue({
        fieldId: "custbody_ng_cses_serv_chgs_selected",
        value: JSON.stringify({
          selected: surchargesSelected,
        }),
      });
    }
  }

  /**
   * Calculate Surcharges for the sales order.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2024.10
   */
  function calculateSurcharges(scriptContext) {
    const { newRecord } = scriptContext;

    const eventId = newRecord.getValue({
      fieldId: "custbody_show_table",
    });

    if (eventId) {
      // The surcharges for the event
      const surcharges = getSurchargesForEvent(eventId);
      if (surcharges.length === 0) {
        return;
      }

      log.audit({
        title: "🧮 Calculating Surcharges For Event:",
        details: eventId,
      });

      // Filter the surcharges records for the event that are selected on the sales order
      const surchargeData = surcharges.filter((surcharge) => {
        const surchargeSelected = newRecord.getValue({
          fieldId: `custpage_surcharge_${surcharge.id}`,
        });

        return (
          surchargeSelected === "T" ||
          scriptContext.type === scriptContext.UserEventType.CREATE
        );
      });

      log.audit({
        title: "🧾 Surcharge Data:",
        details: surchargeData,
      });

      // Will keep track of the totals for each surcharge type
      // keys of surcharge ids, values of the total amount
      const surchargeTotals = {};

      // Iterate over each line item to calculate the surcharge
      const itemLineCount = newRecord.getLineCount({
        sublistId: "item",
      });

      for (let i = 0; i < itemLineCount; i++) {
        // Get the item id, the amount and the cust_col_ng_cses_cs_order_serv_chrg which indicates if the line is a surcharge line
        const itemId = newRecord.getSublistValue({
          sublistId: "item",
          fieldId: "item",
          line: i,
        });

        const amount = newRecord.getSublistValue({
          sublistId: "item",
          fieldId: "amount",
          line: i,
        });

        const isSurcharge = !!newRecord.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_ng_cses_cs_event_surcharge",
          line: i,
        });

        // loop each surcharge
        surchargeData.forEach((surcharge) => {
          const {
            applied_items,
            charge_type,
            flat_charge,
            id,
            item_id,
            item_type,
            order_importance,
            percentage,
            select_items,
          } = surcharge;

          // If the line is a surcharge line,
          // Or if we marked the id as already being applied, we can skip it
          if (isSurcharge || surchargeTotals?.[id]?.skip) {
            return;
          }

          const applicableCharge =
            select_items === "F" || applied_items.includes(itemId);
          if (applicableCharge) {
            // If the charge type is a percentage
            if (charge_type === 1) {
              const previousTotal = surchargeTotals[id]?.lineTotal || 0;
              surchargeTotals[id] = {
                // The surcharge amount is calculated later by multiplying the lineTotal by the surcharge percentage
                lineTotal: previousTotal + amount,
                orderImportance: order_importance,
                percentage,
                itemId: item_id,
                itemType: item_type,
              };
              // If the charge type is a flat rate
            } else if (charge_type === 2) {
              surchargeTotals[id] = {
                // The surcharge amount for flat charges is just the flat charge
                itemId: item_id,
                itemType: item_type,
                orderImportance: order_importance,
                skip: true,
                total: flat_charge,
              };
            }
          }
        });
      }

      log.audit({
        title: "🧾 Surcharge Totals:",
        details: surchargeTotals,
      });

      // remove serice charge lines if they are in the surcharge totals
      for (let i = itemLineCount - 1; i >= 0; i--) {
        const surcharge = newRecord.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_ng_cses_cs_event_surcharge",
          line: i,
        });

        if (surchargeTotals[surcharge]) {
          log.audit({
            title: "Removing Surcharge Line to recalculate and add back:",
            details: {
              line: i,
              surcharge,
            },
          });

          newRecord.removeLine({
            sublistId: "item",
            line: i,
          });
        }
      }

      const newItemLineCount = newRecord.getLineCount({
        sublistId: "item",
      });

      log.audit({
        title: "Recalculating surcharges and adding back lines",
        details: surchargeTotals,
      });

      // add back surcharge lines that are in the surcharge totals
      Object.keys(surchargeTotals)
        // sorts by order importance low to high with nulls last
        .sort((keyA, keyB) => {
          const sortA = surchargeTotals[keyA].orderImportance;
          const sortB = surchargeTotals[keyB].orderImportance;
          return !sortA ? 1 : !sortB ? -1 : sortA - sortB;
        })
        .forEach((surchargeId, index) => {
          const { itemId, itemType, total, lineTotal, percentage } =
            surchargeTotals[surchargeId];

          const line = newItemLineCount + index;

          newRecord.insertLine({
            sublistId: "item",
            line,
          });

          // Add the item id for the surcharge item
          newRecord.setSublistValue({
            sublistId: "item",
            fieldId: "item",
            value: itemId,
            line,
          });

          // Set the item type
          newRecord.setSublistValue({
            sublistId: "item",
            fieldId: "itemtype",
            value: itemType,
            line,
          });

          try {
            // set the quantity
            newRecord.setSublistValue({
              sublistId: "item",
              fieldId: "price",
              value: -1, // custom price level
              line,
            });
          } catch (e) {
            log.error({
              title: "Error Setting Price",
              details: e,
            });
          }

          try {
            // set the unit price
            newRecord.setSublistValue({
              sublistId: "item",
              fieldId: "rate",
              value: total || lineTotal * percentage,
              line,
            });
          } catch (e) {
            log.error({
              title: "Error Setting Unit Price",
              details: e,
            });
          }

          // set the amount
          newRecord.setSublistValue({
            sublistId: "item",
            fieldId: "amount",
            value: total || lineTotal * percentage,
            line,
          });

          // set the surcharge id cust col to indicate that this line is a surcharge line
          newRecord.setSublistValue({
            sublistId: "item",
            fieldId: "custcol_ng_cses_cs_event_surcharge",
            value: surchargeId,
            line,
          });
        });
    }
  }

  /**
   * Fetches the surcharges for the event.
   * @param {number} eventId - The event internal ID
   * @returns {Object[]} - The surcharges for the event
   * @since 2024.10
   */
  const getSurchargesForEvent = (eventId) => {
    try {
      log.audit({
        title: "🔎 Retrieving Surcharges For Event",
        details: eventId,
      });

      const surcharges = query
        .runSuiteQL({
          query: `
            SELECT
              sc.id as id
              , sc.name as name
              , sc.custrecord_ng_cses_surcharge_item as item_id
              , sc.custrecord_ng_cses_surcharge_memo as memo
              , sc.custrecord_ng_cses_order_importance as order_importance
              , sc.custrecord_ng_cses_surcharge_chg_type as charge_type
              , sc.custrecord_ng_cses_surcharge_chg_flat as flat_charge
              , sc.custrecord_ng_cses_surcharge_chg_percent as percentage
              , sc.custrecord_ng_cses_surcharge_slct_items as select_items
              , sc.custrecord_ng_cses_surcharge_appl_items as applied_items
              , itm.itemtype as item_type
            FROM
              customrecord_ng_cses_event_surcharge sc
              JOIN MAP_customrecord_ng_cses_event_surcharge_custrecord_ng_cses_surcharge_actv_evnts AS map ON
                sc.id = map.mapone
              JOIN customrecord_show AS evt ON
                map.maptwo = evt.id
              JOIN item AS itm ON
                sc.custrecord_ng_cses_surcharge_item = itm.id
            WHERE
              evt.id = ${eventId}
              AND sc.custrecord_ng_cses_surcharge_chg_type IS NOT NULL
              AND (
                sc.custrecord_ng_cses_surcharge_chg_flat IS NOT NULL
                OR sc.custrecord_ng_cses_surcharge_chg_percent IS NOT NULL
              )
            ORDER BY
              sc.custrecord_ng_cses_surcharge_default_wiz DESC
          `,
        })
        .asMappedResults();

      log.audit({
        title: "🧾 Retrieved Surcharges",
        details: surcharges,
      });

      return surcharges;
    } catch (e) {
      log.error({
        title: "Error Getting Surcharges",
        details: e,
      });
      return [];
    }
  };

  const validateUpdatedValues = (currentSalesOrder, formattedAmountPaid, balance) => {
    // Check if currentSalesOrder is valid
    if (!currentSalesOrder || !currentSalesOrder.id) {
      log.error({
        title: "Invalid Sales Order",
        details: "Cannot validate updates: missing sales order or ID"
      });
      return;
    }

    // Get current field values
    let currentFormattedAmountPaid = Number(currentSalesOrder.getValue({
      fieldId: 'custbody_total_paid'
    }) || 0);
    
    let currentBalance = Number(currentSalesOrder.getValue({
      fieldId: 'custbody_balance'
    }) || 0);
    
    let currentHasBalance = currentSalesOrder.getValue({
      fieldId: 'custbody_hasbalance'
    });
    
    // Convert to consistent number types for proper comparison
    formattedAmountPaid = Number(formattedAmountPaid || 0);
    balance = Number(balance || 0);
    const hasBalance = balance !== 0;
    
    // Track which fields need updating
    const fieldsChanged = {
      amountPaid: currentFormattedAmountPaid !== formattedAmountPaid,
      balance: currentBalance !== balance,
      hasBalance: currentHasBalance !== hasBalance
    };
    
    // Only update if at least one field value has changed
    if (fieldsChanged.amountPaid || fieldsChanged.balance || fieldsChanged.hasBalance) {
      log.audit({
        title: "Updating Balance Values",
        details: `SO: ${currentSalesOrder.id} - Changed fields: ${JSON.stringify(fieldsChanged)}`
      });
      
      record.submitFields({
        type: record.Type.SALES_ORDER,
        id: currentSalesOrder.id,
        values: {
          custbody_total_paid: formattedAmountPaid,
          custbody_balance: balance,
          custbody_hasbalance: hasBalance,
        },
        options: {
          enableSourcing: false,
        },
      });
    }
  }
  
  const extractValues = (html) => {
    const regex = /<td[^>]*>(.*?)<\/td>/g;
    let match;
    const values = [];
    while ((match = regex.exec(html)) !== null) {
      values.push(match[1].trim());
    }
    return values;
  };

  return { beforeLoad, beforeSubmit, afterSubmit };
});
