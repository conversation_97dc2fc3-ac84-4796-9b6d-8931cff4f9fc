/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 */
define(['N/search', 'N/runtime'],
    
    (search, runtime) => {
        /**
         * Defines the function definition that is executed before record is loaded.
         * @param {Object} scriptContext
         * @param {Record} scriptContext.newRecord - New record
         * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
         * @param {Form} scriptContext.form - Current form
         * @param {ServletRequest} scriptContext.request - HTTP request information sent from the browser for a client action only.
         * @since 2015.2
         */
        const beforeLoad = (scriptContext) => {  

                let newRec = scriptContext.newRecord;  

                let form = scriptContext.form;

                let daysSelectField = form.addField({
                        id: 'custpage_ng_cs_session_days',
                        label: 'Days',
                        type: 'MULTISELECT'
                });

                daysSelectField.helpText = 'Select the days that this session applies to.';
                
                let csEvent = newRec.getValue({
                        fieldId: 'custrecord_ng_cs_session_event'
                });

                log.debug({title: 'CS Event', details: csEvent});

                let csSessionDates = newRec.getValue({
                        fieldId: 'custrecord_ng_cs_session_days'
                });

                log.debug({title: 'CS Session Dates', details: csSessionDates});

                let csEventDates = [];

                if(!isEmpty(csEvent)){

                        let eventDateSearchFilters = [
                                ["custrecord_date_type","anyof","3"], 
                                "AND", 
                                ["custrecord_show_number_date","anyof",csEvent]
                        ]

                        let eventDateSearchColumns = [
                                search.createColumn({name: "internalid", label: "Internal ID"}),
                                search.createColumn({name: "custrecord_show_number_date", label: "CS Event"}),
                                search.createColumn({name: "custrecord_date_type", label: "Date Type"}),
                                search.createColumn({
                                        name: "custrecord_date",
                                        sort: search.Sort.ASC,
                                        label: "Date"
                                })    
                        ]

                        let eventDateSearchResults = getSearchResults('customrecord_show_date', eventDateSearchFilters, eventDateSearchColumns);

                        if(!isEmpty(eventDateSearchResults) && eventDateSearchResults.length > 0){

                                for(let i=0; i < eventDateSearchResults.length; i++){

                                        let eventDate =  {};

                                        eventDate.id = eventDateSearchResults[i].getValue({name: 'internalid'});

                                        eventDate.value = eventDateSearchResults[i].getValue({name: 'custrecord_date'});

                                        csEventDates.push(eventDate);

                                }

                                log.debug({title: 'CS Event Dates',details: csEventDates});

                                for(let i=0; i < csEventDates.length; i++){

                                        if(!Array.isArray(csSessionDates)){

                                                csSessionDates = [csSessionDates];

                                        }

                                        let isSelected = false;

                                        log.debug({title: 'CS Event Date', details: csEventDates[i].id});

                                        if(!isEmpty(csSessionDates) && csSessionDates.length > 0 && csSessionDates.indexOf(csEventDates[i].id) != -1){

                                                isSelected = true;

                                        }

                                        daysSelectField.addSelectOption({
                                                value: csEventDates[i].id,
                                                text: csEventDates[i].value,
                                                
                                        });

                                }

                                
                        }


                }

                form.insertField({
                     field: daysSelectField,                     
                     nextfield: 'custrecord_ng_cs_session_notes'   
                });

                daysSelectField.defaultValue = csSessionDates;

                if(scriptContext.type == 'edit' || scriptContext.type == 'create' || scriptContext.type == 'copy'){

                        let flowSublist = form.getSublist({id: 'recmachcustrecord_ng_cs_flow_event_session'});

                        flowSublist.addButton({
                                id: 'custpage_copy_previous_line',
                                label: 'Copy Previous',
                                functionName: 'copyPreviousFlowLine'
                        });

                        let equipmentSublist = form.getSublist({id: 'recmachcustrecord_ng_cs_sess_equip_sess'});

                        equipmentSublist.addButton({
                                id: 'custpage_set_equipment_line_dates',
                                label: 'Set Dates',
                                functionName: 'setEquipLineDates'
                        });

                        equipmentSublist.addButton({
                                id: 'custpage_recalc_equipment_line_pricing',
                                label: 'Recalc Pricing',
                                functionName: 'recalcEquipPricing'
                        });

                        let tasksSublist = form.getSublist({id: 'recmachcustrecord_ng_cs_sess_task_sess'});

                        tasksSublist.addButton({
                                id: 'custpage_set_tasks_line_dates',
                                label: 'Set Date',
                                functionName: 'setTaskLineDates'
                        });

                        tasksSublist.addButton({
                                id: 'custpage_recalc_tasks_line_pricing',
                                label: 'Recalc Pricing',
                                functionName: 'recalcTasksPricing'
                        });

                }else if(scriptContext.type == 'view'){

                        form.clientScriptModulePath = '../Client/ng_cs_clientCSEventSession.js'

                        let scriptObj = runtime.getCurrentScript();

                        let copySessionBtnLabel = scriptObj.getParameter({name: 'custscript_ng_cs_copy_session_btn_label'});

                        if(isEmpty(copySessionBtnLabel)){

                                copySessionBtnLabel = 'Copy Session';
                        }

                        form.addButton({
                                id: 'custpage_deep_copy_session',
                                label: copySessionBtnLabel,
                                functionName: 'deepCopySession'
                        })
                }

        }

        /**
         * Defines the function definition that is executed before record is submitted.
         * @param {Object} scriptContext
         * @param {Record} scriptContext.newRecord - New record
         * @param {Record} scriptContext.oldRecord - Old record
         * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
         * @since 2015.2
         */
        const beforeSubmit = (scriptContext) => {

        }

        /**
         * Defines the function definition that is executed after record is submitted.
         * @param {Object} scriptContext
         * @param {Record} scriptContext.newRecord - New record
         * @param {Record} scriptContext.oldRecord - Old record
         * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
         * @since 2015.2
         */
        const afterSubmit = (scriptContext) => {

        }

        function isEmpty (value) {
                if (value == null || value == undefined || value === "") {
                        return true;
                }

                return false;
        }

        function getSearchResults(recType, filt, cols, searchId, pageSize, returnIDs, firstPageOnly) {
            var results = new Array();
            pageSize = pageSize || 1000
            returnIDs = returnIDs || false;
            firstPageOnly = firstPageOnly || false;
            var searchInit = null;
            if (isEmpty(searchId)) {
                    searchInit = search.create({ type : recType , filters : filt , columns : cols });
            } else {
                    var savedSearch = null;
                    if (isEmpty(recType)) {
                            savedSearch = search.load({ id : searchId });
                    } else {
                            savedSearch = search.load({ type : recType , id : searchId });
                    }
                    var searchFilters = savedSearch.filterExpression;
                    var searchColumns = savedSearch.columns;
                    var finalFilt = new Array();
                    var finalCols = new Array();
                    if (!isEmpty(searchFilters) && Array.isArray(searchFilters)) {
                            if (!isEmpty(filt) && Array.isArray(filt)) {
                                    if (filt.length > 0) {
                                            finalFilt = searchFilters.concat(["and"], filt);
                                    } else {
                                            finalFilt = searchFilters.concat(filt);
                                    }
                            } else {
                                    finalFilt = searchFilters.concat([]);
                            }
                    } else {
                            if (!isEmpty(filt) && Array.isArray(filt)) {
                                    finalFilt = [].concat(filt);
                            } else {
                                    finalFilt = null;
                            }
                    }
                    if (!isEmpty(searchColumns) && Array.isArray(searchColumns)) {
                            if (!isEmpty(cols) && Array.isArray(cols)) {
                                    finalCols = searchColumns.concat(cols);
                            } else {
                                    finalCols = searchColumns.concat([]);
                            }
                    } else {
                            if (!isEmpty(cols) && Array.isArray(cols)) {
                                    finalCols = [].concat(cols);
                            } else {
                                    finalCols = null;
                            }
                    }
                    searchInit = search.create({ type : recType , filters : finalFilt , columns : finalCols });
            }
            var pages = searchInit.runPaged({ pageSize : pageSize });
            for (var pg = 0; pg < pages.pageRanges.length; pg++) {
                    var page = pages.fetch({ index : pages.pageRanges[pg].index });
                    if (returnIDs) {
                            for (var p = 0; p < page.data.length; p++) {
                                    results.push(page.data[p].id);
                            }
                    } else {
                            results = results.concat(page.data);
                    }

                    if (firstPageOnly) {
                            if (page.isFirst) {
                                    break;
                            }
                    }
            }

            return results.length > 0 ? results : null;
        }

        return {
                beforeLoad,
                //beforeSubmit,
                //afterSubmit
        }

    });
