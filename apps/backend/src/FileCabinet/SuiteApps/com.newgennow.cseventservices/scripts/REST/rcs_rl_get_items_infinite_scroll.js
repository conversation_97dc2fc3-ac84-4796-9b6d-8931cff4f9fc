/**
 * @NApiVersion 2.1
 * @NScriptType Restlet
 * @NAmdConfig ./amdRestConfig.json
 */
define([
  "N/record",
  "N/runtime",
  "N/search",
  "../../packages/@prices/ng_server_cm_price_hooks",
  "N/url",
  "N/query",
  "settings",
], /**
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{Object} prices
 * @param{url} url
 * @param{query} query
 * @param{Object} settings
 * @param{() => Object} settings.useSettings
 */ (record, runtime, search, prices, url, query, settings) => {
  let CS_SETTINGS = null;

  /**
   * Defines the function that is executed when a GET request is sent to a RESTlet.
   * @param {Object} requestParams - Parameters from HTTP request URL; parameters passed as an Object (for all supported
   *     content types)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const get = async (requestParams) => {
    let eventId = requestParams.event;
    let collectionId = requestParams.cid;
    let limit = requestParams.limit;
    let sortBy = requestParams.sortBy;
    let itemCollections = [];
    let items = [];
    let currentPage = (requestParams.page && requestParams.page - 1) || 0; // Always starts at 1, but we need to start at 0 for the search
    const mainUrl = url.resolveDomain({
      hostType: url.HostType.APPLICATION,
    });

    CS_SETTINGS = settings.useSettings();

    const handleNameSortBy = () => {
      let sortOption = "NONE";
      if (sortBy === "asc") {
        sortOption = search.Sort.ASC;
      } else if (sortBy === "desc") {
        sortOption = search.Sort.DESC;
      }

      return sortOption;
    };

    const handlePriceSortBy = (items) => {
      let sortedItems = [...items];
      if (sortBy === "low-high") {
        sortedItems = items.sort((a, b) => a.currentPrice - b.currentPrice);
      } else if (sortBy === "high-low") {
        sortedItems = items.sort((a, b) => b.currentPrice - a.currentPrice);
      }

      return sortedItems;
    };

    const handleManualSortBy = (items, collection) => {
      if (collection.length === 0) {
        return items;
      }

      //  Sort the items by the manual sort order from the collection field custrecord_item_sort_index_read_only (manualSortOrder) array

      let sortedItems = [...items];
      const collectionFound = collection[0];
      const collectionManualSort = collectionFound.manualSortOrder;

      log.debug({
        title: "🟡 Manual Sorting Stage:",
        details: {
          collectionFound,
          sortBy,
          collectionManualSortType: typeof collectionManualSort,
          collectionManualSort,
        },
      });

      if (!collectionFound.manualSortEnabled) {
        log.debug({
          title: "🟡 Not a manual collection",
          details: collectionFound.manualSortEnabled,
        });
        return sortedItems;
      } else if (
        sortBy === "relevance" &&
        collectionFound.manualSortEnabled &&
        collectionManualSort.length !== 0
      ) {
        log.audit({
          title: "📂 Manual Sort Detected:",
          details: {
            manualSortEnabled: collectionFound.manualSortEnabled,
            manualSortOrder: collectionFound.manualSortOrder,
            sortBy,
          },
        });

        sortedItems = sortedItems.sort((a, b) => {
          let aIndex = collectionManualSort.findIndex(
            (el) => el === String(a.id),
          );
          let bIndex = collectionManualSort.findIndex(
            (el) => el === String(b.id),
          );
          return aIndex - bIndex;
        });
      }

      return sortedItems;
    };

    try {
      // Item Collection Search
      let itemCollectionSearch = search.create({
        type: "customrecord_ng_cs_item_collection",
        filters: [
          ["custrecord_ng_cs_itemcoll_event", "anyof", eventId],
          "AND",
          ["internalid", "anyof", collectionId],
        ],
        columns: [
          search.createColumn({
            name: "id",
            sort: search.Sort.ASC,
            label: "ID",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_itemcoll_event",
            label: "CS Event",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_itemcoll_show_in_web",
            label: "Show In Web",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_itemcoll_image",
            label: "Category Image",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_itemcoll_items",
            label: "Items",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_itemcoll_display_name",
            label: "Web Display Name",
          }),
          search.createColumn({
            name: "custrecord_ng_cs_itemcoll_desc",
            label: "Web Display Description",
          }),
          search.createColumn({
            name: "custrecord_enable_manual_sort",
            label: "Manual Sort Enabled",
          }),
          search.createColumn({
            name: "custrecord_item_sort_index_read_only",
            label: "Manual Sort Index",
          }),
        ],
      });

      // Run Collection Search
      let collectionResultCount = itemCollectionSearch.runPaged().count;

      log.debug("itemCollectionSearch result count", collectionResultCount);

      itemCollectionSearch.run().each(function (result) {
        const sortOrder = result.getValue(
          "custrecord_item_sort_index_read_only",
        );
        let transformedSortOrder = sortOrder ? JSON.parse(sortOrder) : [];
        // .run().each has a limit of 4,000 results
        let imageUrl = result.getText("custrecord_ng_cs_itemcoll_image")
          ? `https://${mainUrl}${result.getText(
              "custrecord_ng_cs_itemcoll_image",
            )}`
          : null;

        // .run().each has a limit of 4,000 results
        let collectionObj = {
          id: result.id,
          name: result.getValue("custrecord_ng_cs_itemcoll_display_name"),
          event: {
            id: result.getValue("custrecord_ng_cs_itemcoll_event"),
            name: result.getText("custrecord_ng_cs_itemcoll_event"),
          },
          image: imageUrl,
          items: parseMultiSelectResult(
            result,
            "custrecord_ng_cs_itemcoll_items",
          ),
          description: result.getValue("custrecord_ng_cs_itemcoll_desc"),
          manualSortEnabled: result.getValue("custrecord_enable_manual_sort"),
          manualSortOrder: transformedSortOrder,
        };

        itemCollections.push(collectionObj);
        return true;
      });

      // Create Item Search
      let itemSearchObj = search.create({
        type: "item",
        filters: [
          ["custrecord_ng_cs_itemcoll_items.internalid", "anyof", collectionId],
          "AND",
          ["custitem_ng_cs_web_display_in_web", "is", "T"],
        ],
        columns: [
          search.createColumn({
            name: "itemid",
            label: "Name",
            sort: sortBy === "relevance" ? search.Sort.ASC : "NONE",
          }),
          search.createColumn({ name: "displayname", label: "Display Name" }),
          search.createColumn({ name: "vendorname", label: "Vendor Name" }),
          search.createColumn({
            name: "salesdescription",
            label: "Description",
          }),
          search.createColumn({ name: "type", label: "Type" }),
          search.createColumn({
            name: "baseprice",
            label: "Base Price",
          }),
          search.createColumn({
            name: "custitem_item_category",
            label: "Item Category",
          }),
          // Inventory Restrictions
          search.createColumn({
            name: "minimumquantity",
            label: "Minimum Quantity",
          }),
          search.createColumn({
            name: "enforceminqtyinternally",
            label: "Enforce Min Qty",
          }),
          search.createColumn({
            name: "maximumquantity",
            label: "Maximum Quantity",
          }),
          // Matrix Item Types
          search.createColumn({
            name: "custitem_has_color_options",
            label: "Color Options",
          }),
          search.createColumn({
            name: "custitem_has_size_options",
            label: "Size Options",
          }),
          search.createColumn({
            name: "custitem_has_orient_options",
            label: "Orientation Options",
          }),
          search.createColumn({
            name: "custitem_ng_cs_has_graphic_options",
            label: "Graphic Options",
          }),
          // Special Item Types
          search.createColumn({
            name: "custitem_is_sqft",
            label: "Square Foot Item",
          }),
          search.createColumn({
            name: "custitem_is_freight",
            label: "Freight Item",
          }),
          search.createColumn({
            name: "custitem_ng_mat_handling_sched",
            label: "Material Handling Schedule",
          }),
          search.createColumn({
            name: "custitem_is_days",
            label: "Days Calc Item",
          }),
          search.createColumn({
            name: "custitem_show_duration",
            label: "Show Duration",
          }),
          search.createColumn({
            name: "custitem_labor_item",
            label: "Labor Item",
          }),
          search.createColumn({
            name: "custitem_cost_is_estimated",
            label: "Estimated",
          }),
          search.createColumn({
            name: "custitem_cost_is_memo",
            label: "Memo Item",
          }),
          search.createColumn({
            name: "custitem_cost_is_upload",
            label: "Upload Item",
          }),
          // Matrix Item Results
          search.createColumn({ name: "custitem27", label: "Colors" }),
          search.createColumn({ name: "custitem28", label: "Size" }),
          search.createColumn({
            name: "custitem_orientation",
            label: "Orientation",
          }),
          search.createColumn({
            name: "custitem42",
            label: "Graphic Material",
          }),

          // Item Image Specs
          search.createColumn({
            name: "custitem_ng_cs_web_primary_img",
            label: "Primary Image",
          }),
          search.createColumn({
            name: "custitem_ng_cs_web_img_2",
            label: "Image 2",
          }),
          search.createColumn({
            name: "custitem_ng_cs_web_img_3",
            label: "Image 3",
          }),
          search.createColumn({
            name: "custitem_ng_cs_web_img_4",
            label: "Image 4",
          }),
          search.createColumn({
            name: "custitem_ng_cs_web_img_5",
            label: "Image 5",
          }),
          search.createColumn({
            name: "saleunit",
            label: "Sale Unit",
          }),
          // Display Bool
          search.createColumn({
            name: "custitem_ng_cs_web_display_in_web",
            label: "Is Displayed",
          }),
          // Description
          search.createColumn({
            name: "custitem_ng_cs_web_display_name",
            label: "Title",
            sort: handleNameSortBy(),
          }),
          search.createColumn({
            name: "custitem_ng_cs_web_description",
            label: "Description",
          }),
          search.createColumn({
            name: "custitem_ng_cs_item_attributes",
            label: "Item Attributes",
          }),
          // Sub Category
          search.createColumn({
            name: "custitem_subcategory",
            label: "Sub Category",
          }),
          // Related Items
          search.createColumn({
            name: "custitem_ng_cses_related_items",
            label: "Related Items",
          }),
          // Required Items
          search.createColumn({
            name: "custitem_req_addtnl_items",
            label: "Required Items",
          }),
        ],
      });

      // Run Item Search
      let itemDetailsResultCount = itemSearchObj.runPaged().count;
      log.debug("item results", itemDetailsResultCount);
      /*
                NON-PAGED Method - USE FOR TESTING ONLY
                itemSearchObj.run().each(async function (result) {
                    // .run().each has a limit of 4,000 results
                    items.push(result)
                    return true;
                });
                */

      let item_search_page_results = itemSearchObj.runPaged({
        pageSize: limit,
      });

      let item_page_ranges = item_search_page_results.pageRanges;

      log.audit({
        title: "Paged Ranges from items",
        details: item_page_ranges,
      });

      if (itemDetailsResultCount !== 0) {
        // INDEXES ALWAYS START AT 0
        let item_result_page = item_search_page_results.fetch({
          index: currentPage,
        });

        log.debug("item_result_page", item_result_page.data);

        const materialHandlingBetaEnabled =
          CS_SETTINGS.custrecord_enable_material_handling_beta === "T";

        item_result_page.data.forEach((item) => {
          let imageUrl = item.getText("custitem_ng_cs_web_primary_img")
            ? `https://${mainUrl}${item.getText(
                "custitem_ng_cs_web_primary_img",
              )}`
            : null;

          let attributeResults = item.getValue(
            "custitem_ng_cs_item_attributes",
          );

          let relatedItemsResult =
            createMultiSelectOutput(
              item.getValue("custitem_ng_cses_related_items"),
              item.getText("custitem_ng_cses_related_items"),
            ) || [];

          let requiredItemsResult =
            createMultiSelectOutput(
              item.getValue("custitem_req_addtnl_items"),
              item.getText("custitem_req_addtnl_items"),
            ) || [];

          const getRelatedItem = fetchItemDetails; // cache the function to avoid redefining it in the loop

          let relatedItems = relatedItemsResult
            ? relatedItemsResult.map((item) =>
                getRelatedItem(item.value, eventId),
              )
            : [];

          log.debug({ title: "🫗 Related Items:", details: relatedItems });

          let requiredItems = requiredItemsResult
            ? requiredItemsResult.map((item) =>
                getRelatedItem(item.value, eventId),
              )
            : [];

          log.debug({ title: "🫗 Required Items:", details: requiredItems });

          let itemObj = {
            id: item.id,
            itemAttributes: [],
            collection: collectionId,
            hasColorOptions: item.getValue("custitem_has_color_options"),
            hasSizeOptions: item.getValue("custitem_has_size_options"),
            hasOrientOptions: item.getValue("custitem_has_orient_options"),
            hasGraphicOptions: item.getValue(
              "custitem_ng_cs_has_graphic_options",
            ),
            isSquareFt: item.getValue("custitem_is_sqft"),
            isFreight: item.getValue("custitem_is_freight"),
            isDaysCalc: item.getValue("custitem_is_days"),
            isShowDuration: item.getValue("custitem_show_duration"),
            isLabor: item.getValue("custitem_labor_item"),
            isEstimated: item.getValue("custitem_cost_is_estimated"),
            isMemoItem: item.getValue("custitem_cost_is_memo"),
            isUpload: item.getValue("custitem_cost_is_upload"),
            isDisplayed: item.getValue("custitem_ng_cs_web_display_in_web"),
            price: item.getValue("baseprice"),
            colors:
              createMultiSelectOutput(
                item.getValue("custitem27"),
                item.getText("custitem27"),
              ) || [],
            sizes:
              createMultiSelectOutput(
                item.getValue("custitem28"),
                item.getText("custitem28"),
              ) || [],
            orientations:
              createMultiSelectOutput(
                item.getValue("custitem_orientation"),
                item.getText("custitem_orientation"),
              ) || [],
            materials:
              createMultiSelectOutput(
                item.getValue("custitem42"),
                item.getText("custitem42"),
              ) || [],
            image: imageUrl,
            images: [
              item.getText("custitem_ng_cs_web_primary_img")
                ? `https://${mainUrl}${item.getText(
                    "custitem_ng_cs_web_primary_img",
                  )}`
                : "",
              item.getText("custitem_ng_cs_web_img_2")
                ? `https://${mainUrl}${item.getText(
                    "custitem_ng_cs_web_img_2",
                  )}`
                : "",
              item.getText("custitem_ng_cs_web_img_3")
                ? `https://${mainUrl}${item.getText(
                    "custitem_ng_cs_web_img_3",
                  )}`
                : "",
              item.getText("custitem_ng_cs_web_img_4")
                ? `https://${mainUrl}${item.getText(
                    "custitem_ng_cs_web_img_4",
                  )}`
                : "",
              item.getText("custitem_ng_cs_web_img_5")
                ? `https://${mainUrl}${item.getText(
                    "custitem_ng_cs_web_img_5",
                  )}`
                : "",
            ].filter((img) => img),
            title: item.getValue("custitem_ng_cs_web_display_name"),
            webDescription: item.getValue("custitem_ng_cs_web_description"),
            salesDescription: item.getValue("salesdescription"),
            subCategory: item.getText("custitem_subcategory"),
            category: item.getText("custitem_item_category"),
            minimumQuantity: Number(item.getValue("minimumquantity") || 1),
            enforceMinQty: item.getValue("enforceminqtyinternally"),
            maxQuantity: Number(item.getValue("maximumquantity") || Infinity),
            additionalItems: item.getValue("custitem_req_addtnl_items"),
            saleUnit: item.getText("saleunit"),
            vendorName: item.getText("vendorname"),
            itemId: item.getValue("itemid"),
            type: item.getValue("type"),
            displayName: item.getValue("displayname"),
            relatedItems,
            requiredProducts: requiredItems,
          };

          if (attributeResults) {
            itemObj.itemAttributes = getItemAttributes(attributeResults);
          }

          // If beta features are enabled add extra data to freight items that need the material handling schedule.
          if (materialHandlingBetaEnabled && itemObj.isFreight) {
            // Look up material handling schedule that is tied to the item record
            const materialHandlingScheduleId = item.getValue(
              "custitem_ng_mat_handling_sched",
            );

            // If the material handling schedule is set on the item record then add the material handling schedule to the item object
            if (materialHandlingScheduleId) {
              const materialHandlingScheduleLookup = search.lookupFields({
                type: "customrecord_ng_mat_handling_sched",
                id: materialHandlingScheduleId,
                columns: MATERIAL_HANDLING_SCHEDULE_FIELDS,
              });

              // Format the data to be used in the front end
              itemObj.materialHandlingSchedule = {
                id: materialHandlingScheduleId,
                name: materialHandlingScheduleLookup.name,
                chargeType: getListValueFromLookup(
                  materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_charge_type,
                ),
                minWeight:
                  materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_min_weight,
                maxWeight:
                  materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_max_weight,
                firstPcPrice:
                  materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_first_pc,
                additionalPcPrice:
                  materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_addtl_pc,
                surcharge: {
                  type: getListValueFromLookup(
                    materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_surchgtype,
                  ),
                  percentage:
                    materialHandlingScheduleLookup.custrecord_surcharge_percentage,
                  flatFee:
                    materialHandlingScheduleLookup.custrecord_surcharge_flat_fee,
                },
              };

              // override frieght min and max with material schedule min and max
              itemObj.freightMinimum =
                itemObj.materialHandlingSchedule.minWeight;
              itemObj.freightMaximum =
                itemObj.materialHandlingSchedule.maxWeight;
            }
          }

          items.push(itemObj);
        });
      }

      // Add Item Prices
      let itemResult = await addItemPrices(items, eventId);

      //  Take the sorted items and sort them by manual sort order first
      let manualSortedItems = handleManualSortBy(itemResult, itemCollections);

      let sortedItems = handlePriceSortBy(manualSortedItems);

      return JSON.stringify({
        items: sortedItems,
        pages: item_page_ranges,
        page: currentPage + 1,
        totalItems: itemDetailsResultCount,
      });
    } catch (e) {
      return JSON.stringify(e);
    }
  };

  /**
   * Defines the function that is executed when a PUT request is sent to a RESTlet.
   * @param {string | Object} requestBody - The HTTP request body; request body are passed as a string when request
   *     Content-Type is 'text/plain' or parsed into an Object when request Content-Type is 'application/json' (in which case
   *     the body must be a valid JSON)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  // eslint-disable-next-line no-unused-vars
  const put = (requestBody) => {};

  /**
   * Defines the function that is executed when a POST request is sent to a RESTlet.
   * @param {string | Object} requestBody - The HTTP request body; request body is passed as a string when request
   *     Content-Type is 'text/plain' or parsed into an Object when request Content-Type is 'application/json' (in which case
   *     the body must be a valid JSON)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  // eslint-disable-next-line no-unused-vars
  const post = (requestBody) => {};

  /**
   * Defines the function that is executed when a DELETE request is sent to a RESTlet.
   * @param {Object} requestParams - Parameters from HTTP request URL; parameters are passed as an Object (for all supported
   *     content types)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  // eslint-disable-next-line no-unused-vars
  const doDelete = (requestParams) => {};

  /**
   * Output the results of a multi-select field as an array of objects
   * @type Function
   * @param {String} valueString - The value of the multi-select field
   * @param {String} textString - The text of the multi-select field
   * @returns {Array<{value: string|number, text: string}>} - An array of objects with a value and text property
   * @example
   * // returns [{value: '1', text: 'One'}, {value: '2', text: 'Two'}]
   * */
  function createMultiSelectOutput(valueString, textString) {
    let containsComma =
      (valueString.indexOf(",") && textString.indexOf(",")) !== -1;

    if (containsComma) {
      let valueArray = valueString.split(",");
      let textArray = textString.split(",");
      let returnArray = [];

      for (let i = 0; i < valueArray.length; i++) {
        returnArray.push({
          value: valueArray[i],
          text: textArray[i],
        });
      }

      return returnArray;
    } else {
      let returnArray = [];
      if (valueString && textString) {
        returnArray.push({
          value: valueString,
          text: textString,
        });
      }

      return returnArray;
    }
  }

  function calculatePercentage(currentPrice, discountPrice) {
    const percentage =
      discountPrice !== null
        ? Math.abs((currentPrice - discountPrice) / currentPrice) * 100
        : 0;

    if (percentage === 0 || Number.isNaN(percentage)) {
      return null;
    }

    return percentage.toFixed(2); // Round the percentage to 2 decimal places
  }

  /**
   * Parse mulitselect field results from a search.Result object
   * @param {search.Result} result - The search result object
   * @param {string} fieldName - The name of the field to parse
   * @returns {Array} - An array of objects with id and name properties
   * */
  function parseMultiSelectResult(result, searchColName) {
    let resultArray = result.getValue(String(searchColName)).split(","); // Split the string using comma as the delimiter
    let parsedArray = [];

    let getText = result.getText; // Cache the function

    let textArray = getText(String(searchColName)).split(",");
    parsedArray = resultArray.map(function (item, i) {
      return {
        id: item,
        name: textArray[i],
      };
    });

    return parsedArray;
  }

  async function addItemPrices(items, eventId) {
    let updatedItems = [];
    try {
      for (const item of items) {
        log.debug({ title: "⚡ Item:", details: item });

        let priceOutput = await prices.useItemPrice(eventId, item.id);

        log.audit({
          title: "💵 Price Output Check",
          details: {
            priceOutput,
            item: {
              id: item.id,
              currentPrice: item.currentPrice,
              comparePrice: item.comparePrice,
              discount: item.discount,
            },
          },
        });

        let stringedItem = JSON.stringify(item);
        let unsealedItem = JSON.parse(stringedItem);
        unsealedItem.currentPrice = priceOutput?.price;
        unsealedItem.comparePrice = priceOutput?.comparePrice;
        unsealedItem.discount = calculatePercentage(
          priceOutput?.price,
          priceOutput?.comparePrice,
        );

        log.audit({ title: "➡️ Item price set:", details: unsealedItem });
        updatedItems.push(unsealedItem);
      }

      log.audit("updated items result count", updatedItems.length);
      log.debug("updated items result ", updatedItems);

      return updatedItems;
    } catch (error) {
      log.error({
        title: "❗ Error getting price on item lists: ",
        details: error,
      });
      return new Error(error);
    }
  }

  const getItemAttributes = (attributeResults) => {
    let attributeIds = attributeResults.split(",");

    let columnString = "";

    for (let i = 0; i < attributeIds.length; i++) {
      if (i === 0) {
        columnString += `id = ${attributeIds[i]}`;
      } else {
        columnString += ` OR id = ${attributeIds[i]}`;
      }
    }

    let itemAttributeSearch = query
      .runSuiteQL({
        query: `SELECT * FROM customrecord_ng_cs_item_attribute WHERE ${columnString} ORDER BY custrecord_ng_cs_item_att_disp_order`,
      })
      .asMappedResults();

    let attributesCleaned = itemAttributeSearch.map((attribute) => {
      switch (attribute.custrecord_ng_cs_item_att_control_type) {
        case 1: {
          //Text Field
          return {
            type: "text",
            name: attribute.custrecord_ng_cs_item_att_name,
            label: attribute.custrecord_ng_cs_item_att_name,
            webDescription: attribute.custrecord_ng_cs_item_att_desc,
            value: "",
            maxNumberPicked: 0,
            validations: [
              {
                type: "string",
                message:
                  attribute.custrecord_ng_cs_item_att_is_required === "T"
                    ? `${attribute.custrecord_ng_cs_item_att_name} is required`
                    : "",
                isRequired:
                  attribute.custrecord_ng_cs_item_att_is_required === "T"
                    ? "required"
                    : "",
              },
            ],
          };
        }
        case 2: {
          //Checkbox
          let selectOptions = [];
          try {
            selectOptions =
              attribute.custrecord_ng_cs_item_att_options.split("\r\n");
          } catch {
            log.error("Error Parsing Select Options");
          }
          return {
            type: "radio-group",
            name: attribute.custrecord_ng_cs_item_att_name,
            label: attribute.custrecord_ng_cs_item_att_name,
            webDescription: attribute.custrecord_ng_cs_item_att_desc,
            value: "",
            maxNumberPicked: 0,
            options: selectOptions.map((option) => {
              return {
                value: option,
                desc: option,
              };
            }),
            validations: [
              {
                type: "string",
                message:
                  attribute.custrecord_ng_cs_item_att_is_required === "T"
                    ? `${attribute.custrecord_ng_cs_item_att_name} is required`
                    : "",
                isRequired:
                  attribute.custrecord_ng_cs_item_att_is_required === "T"
                    ? "required"
                    : "",
              },
            ],
          };
        }
        case 3: {
          //Select
          let selectOptions = [];
          try {
            selectOptions =
              attribute.custrecord_ng_cs_item_att_options.split("\r\n");
          } catch {
            log.error("Error Parsing Select Options");
          }

          return {
            type: "select",
            name: attribute.custrecord_ng_cs_item_att_name,
            label: attribute.custrecord_ng_cs_item_att_name,
            webDescription: attribute.custrecord_ng_cs_item_att_desc,
            value: "",
            maxNumberPicked: attribute.custrecord_ng_cs_item_att_num_picked,
            options: selectOptions.map((option) => {
              return {
                value: option,
                desc: option,
              };
            }),
            validations: [
              {
                type:
                  attribute.custrecord_ng_cs_item_att_num_picked &&
                  attribute.custrecord_ng_cs_item_att_num_picked > 1
                    ? "array"
                    : "string",
                message: `Select a maximum of ${attribute.custrecord_ng_cs_item_att_num_picked} for ${attribute.custrecord_ng_cs_item_att_name}.`,
                isRequired:
                  attribute.custrecord_ng_cs_item_att_is_required === "T"
                    ? "required"
                    : "",
              },
            ],
          };
        }
        default:
          log.audit("No case found");
      }
    });
    return attributesCleaned;
  };

  /**
   * Fetches item details including attributes and related items.
   * @param {number|string} itemId - The internal ID of the item to search.
   * @param {number|string} eventId - The internal ID of the event to search.
   * @returns {Object[]} Array of item objects with details.
   */
  const fetchItemDetails = (itemId, eventId) => {
    let items = [];
    const materialHandlingBetaEnabled =
      CS_SETTINGS.custrecord_enable_material_handling_beta === "T";

    const mainUrl = url.resolveDomain({
      hostType: url.HostType.APPLICATION,
    });

    let itemSearchObj = search.create({
      type: "item",
      filters: [
        // ["custrecord_ng_cs_itemcoll_items.internalid","anyof", collectionId],
        // 'AND',
        ["internalid", "anyof", itemId],
      ],
      columns: [
        search.createColumn({
          name: "itemid",
          sort: search.Sort.ASC,
          label: "Name",
        }),
        search.createColumn({ name: "displayname", label: "Display Name" }),
        search.createColumn({ name: "vendorname", label: "Vendor Name" }),
        search.createColumn({
          name: "salesdescription",
          label: "Description",
        }),
        search.createColumn({ name: "type", label: "Type" }),
        search.createColumn({ name: "baseprice", label: "Base Price" }),
        search.createColumn({
          name: "saleunit",
          join: "pricing",
          label: "Sale Unit",
        }),
        search.createColumn({
          name: "custitem_item_category",
          label: "Item Category",
        }),
        // Matrix Item Types
        // Selection Options
        search.createColumn({
          name: "custitem_has_color_options",
          label: "Color Options",
        }),
        search.createColumn({
          name: "custitem_has_size_options",
          label: "Size Options",
        }),
        search.createColumn({
          name: "custitem_has_orient_options",
          label: "Orientation Options",
        }),
        search.createColumn({
          name: "custitem_ng_cs_has_graphic_options",
          label: "Graphic Options",
        }),
        search.createColumn({
          name: "minimumquantity",
          label: "Minimum Quantity",
        }),
        search.createColumn({
          name: "enforceminqtyinternally",
          label: "Enforce Minimum Quantity",
        }),
        search.createColumn({
          name: "maximumquantity",
          label: "Max Quantity",
        }),
        // Special Item Types
        search.createColumn({
          name: "custitem_is_sqft",
          label: "Square Foot Item",
        }),
        search.createColumn({
          name: "custitem_is_freight",
          label: "Freight Item",
        }),
        search.createColumn({
          name: "custitem_ng_mat_handling_sched",
          label: "Material Handling Schedule",
        }),
        search.createColumn({
          name: "custitem_is_days",
          label: "Days Calc Item",
        }),
        search.createColumn({
          name: "custitem_show_duration",
          label: "Show Duration",
        }),
        search.createColumn({
          name: "custitem_labor_item",
          label: "Labor Item",
        }),
        search.createColumn({
          name: "custitem_cost_is_estimated",
          label: "Estimated",
        }),
        search.createColumn({
          name: "custitem_cost_is_memo",
          label: "Memo Item",
        }),
        search.createColumn({
          name: "custitem_cost_is_upload",
          label: "Upload Item",
        }),
        // Matrix Item Results
        search.createColumn({ name: "custitem27", label: "Colors" }),
        search.createColumn({ name: "custitem28", label: "Size" }),
        search.createColumn({
          name: "custitem_orientation",
          label: "Orientation",
        }),
        search.createColumn({
          name: "custitem42",
          label: "Graphic Material",
        }),

        // Item Image Specs
        search.createColumn({
          name: "custitem_ng_cs_web_primary_img",
          label: "Primary Image",
        }),
        search.createColumn({
          name: "custitem_ng_cs_web_img_2",
          label: "Image 2",
        }),
        search.createColumn({
          name: "custitem_ng_cs_web_img_3",
          label: "Image 3",
        }),
        search.createColumn({
          name: "custitem_ng_cs_web_img_4",
          label: "Image 4",
        }),
        search.createColumn({
          name: "custitem_ng_cs_web_img_5",
          label: "Image 5",
        }),
        // Display Bool
        search.createColumn({
          name: "custitem_ng_cs_web_display_in_web",
          label: "Is Displayed",
        }),
        // Description
        search.createColumn({
          name: "custitem_ng_cs_web_display_name",
          label: "Title",
        }),
        search.createColumn({
          name: "custitem_ng_cs_web_description",
          label: "Description",
        }),
        // Sub Category
        search.createColumn({
          name: "custitem_subcategory",
          label: "Sub Category",
        }),
        search.createColumn({
          name: "custitem_ng_cs_item_attributes",
          label: "Item Attributes",
        }),
        // Related Items
        search.createColumn({
          name: "custitem_ng_cses_related_items",
          label: "Related Items",
        }),
        // Required Items
        search.createColumn({
          name: "custitem_req_addtnl_items",
          label: "Required Items",
        }),
      ],
    });

    let itemDetailsResultCount = itemSearchObj.runPaged().count;
    log.debug("itemSearchObj result count", itemDetailsResultCount);
    getAllResultsFor(itemSearchObj, (item) => {
      let imageUrl = item.getText("custitem_ng_cs_web_primary_img")
        ? `https://${mainUrl}${item.getText("custitem_ng_cs_web_primary_img")}`
        : null;

      let attributeResults = item.getValue("custitem_ng_cs_item_attributes");

      let relatedItemsResult =
        createMultiSelectOutput(
          item.getValue("custitem_ng_cses_related_items"),
          item.getText("custitem_ng_cses_related_items"),
        ) || [];

      let requiredItemsResult =
        createMultiSelectOutput(
          item.getValue("custitem_req_addtnl_items"),
          item.getText("custitem_req_addtnl_items"),
        ) || [];

      const getRelatedItem = fetchItemDetails; // cache the function to avoid redefining it in the loop

      let relatedItems = relatedItemsResult
        ? relatedItemsResult.map((item) => getRelatedItem(item.value, eventId))
        : [];

      log.debug({ title: "🫗 Related Items:", details: relatedItems });

      let requiredItems = requiredItemsResult
        ? requiredItemsResult.map((item) => getRelatedItem(item.value, eventId))
        : [];

      log.debug({ title: "🫗 Required Items:", details: requiredItems });

      let itemObj = {
        id: item.id,
        hasColorOptions: item.getValue("custitem_has_color_options"),
        hasSizeOptions: item.getValue("custitem_has_size_options"),
        hasOrientOptions: item.getValue("custitem_has_orient_options"),
        hasGraphicOptions: item.getValue("custitem_ng_cs_has_graphic_options"),
        itemAttributes: "",
        isSquareFt: item.getValue("custitem_is_sqft"),
        isFreight: item.getValue("custitem_is_freight"),
        isDaysCalc: item.getValue("custitem_is_days"),
        isShowDuration: item.getValue("custitem_show_duration"),
        isLabor: item.getValue("custitem_labor_item"),
        isEstimated: item.getValue("custitem_cost_is_estimated"),
        isMemoItem: item.getValue("custitem_cost_is_memo"),
        isUpload: item.getValue("custitem_cost_is_upload"),
        isDisplayed: item.getValue("custitem_ng_cs_web_display_in_web"),
        colors:
          createMultiSelectOutput(
            item.getValue("custitem27"),
            item.getText("custitem27"),
          ) || [],
        sizes:
          createMultiSelectOutput(
            item.getValue("custitem28"),
            item.getText("custitem28"),
          ) || [],
        orientations:
          createMultiSelectOutput(
            item.getValue("custitem_orientation"),
            item.getText("custitem_orientation"),
          ) || [],
        materials:
          createMultiSelectOutput(
            item.getValue("custitem42"),
            item.getText("custitem42"),
          ) || [],
        image: imageUrl,
        images: [
          item.getText("custitem_ng_cs_web_primary_img")
            ? `https://${mainUrl}${item.getText(
                "custitem_ng_cs_web_primary_img",
              )}`
            : "",
          item.getText("custitem_ng_cs_web_img_2")
            ? `https://${mainUrl}${item.getText("custitem_ng_cs_web_img_2")}`
            : "",
          item.getText("custitem_ng_cs_web_img_3")
            ? `https://${mainUrl}${item.getText("custitem_ng_cs_web_img_3")}`
            : "",
          item.getText("custitem_ng_cs_web_img_4")
            ? `https://${mainUrl}${item.getText("custitem_ng_cs_web_img_4")}`
            : "",
          item.getText("custitem_ng_cs_web_img_5")
            ? `https://${mainUrl}${item.getText("custitem_ng_cs_web_img_5")}`
            : "",
        ].filter((img) => img),
        title: item.getValue("custitem_ng_cs_web_display_name"),
        webDescription: item.getValue("custitem_ng_cs_web_description"),
        salesDescription: item.getValue("salesdescription"),
        subCategory: item.getText("custitem_subcategory"),
        category: item.getText("custitem_item_category"),
        minimumQuantity: Number(item.getValue("minimumquantity") || 1),
        enforceMinQty: item.getValue("enforceminqtyinternally"),
        maxQuantity: Number(item.getValue("maximumquantity") || Infinity),
        saleUnit: item.getText("saleunit"),
        vendorName: item.getText("vendorname"),
        itemId: item.getValue("itemid"),
        type: item.getValue("type"),
        displayName: item.getValue("displayname"),
        relatedItems: relatedItems,
        requiredProducts: requiredItems,
      };

      let priceOutput = prices.useItemPrice(Number(eventId), Number(itemId));

      let itemPrice = priceOutput?.price ?? "";
      let itemComparePrice = priceOutput?.comparePrice ?? "";

      let discount = calculatePercentage(
        priceOutput?.price,
        priceOutput?.comparePrice,
      );

      itemObj.currentPrice = itemPrice;
      itemObj.comparePrice = itemComparePrice;
      itemObj.discount = discount;

      if (attributeResults) {
        itemObj.itemAttributes = getItemAttributes(attributeResults);
      }

      // If beta features are enabled add extra data to freight items that need the material handling schedule.
      if (materialHandlingBetaEnabled && itemObj.isFreight) {
        // Look up material handling schedule that is tied to the item record
        const materialHandlingScheduleId = item.getValue(
          "custitem_ng_mat_handling_sched",
        );

        // If the material handling schedule is set on the item record then add the material handling schedule to the item object
        if (materialHandlingScheduleId) {
          const materialHandlingScheduleLookup = search.lookupFields({
            type: "customrecord_ng_mat_handling_sched",
            id: materialHandlingScheduleId,
            columns: MATERIAL_HANDLING_SCHEDULE_FIELDS,
          });

          // Format the data to be used in the front end
          itemObj.materialHandlingSchedule = {
            id: materialHandlingScheduleId,
            name: materialHandlingScheduleLookup.name,
            chargeType: getListValueFromLookup(
              materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_charge_type,
            ),
            minWeight:
              materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_min_weight,
            maxWeight:
              materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_max_weight,
            firstPcPrice:
              materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_first_pc,
            additionalPcPrice:
              materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_addtl_pc,
            surcharge: {
              type: getListValueFromLookup(
                materialHandlingScheduleLookup.custrecord_ng_mat_handle_sch_surchgtype,
              ),
              percentage:
                materialHandlingScheduleLookup.custrecord_surcharge_percentage,
              flatFee:
                materialHandlingScheduleLookup.custrecord_surcharge_flat_fee,
            },
          };
        }
      }

      items.push(itemObj);
    });

    if (items.length === 0) {
      log.error({ title: "⚠️ No items found", details: "" });

      items = {
        requiredProducts: [],
        requireItems: [],
      };

      return items;
    } else {
      log.audit({ title: "✔ Item details", details: items });
      // return the single item object

      return items[0];
    }
  };

  const MATERIAL_HANDLING_SCHEDULE_FIELDS = [
    "name",
    "custrecord_ng_mat_handle_sch_charge_type",
    "custrecord_ng_mat_handle_sch_min_weight",
    "custrecord_ng_mat_handle_sch_max_weight",
    "custrecord_ng_mat_handle_sch_first_pc",
    "custrecord_ng_mat_handle_sch_addtl_pc",
    "custrecord_ng_mat_handle_sch_surchgtype",
    "custrecord_surcharge_percentage",
    "custrecord_surcharge_flat_fee",
  ];

  function getListValueFromLookup(array) {
    if (Array.isArray(array) && array.length === 1) {
      const element = array[0];
      if (element && element.value && element.text) {
        return {
          value: element.value,
          text: element.text,
        };
      } else {
        log.audit({
          title:
            "❗Invalid element structure. Element must have 'value' and 'text' properties.",
          details: "",
        });
        return false;
      }
    } else {
      log.audit({
        title: "❗Input array must contain exactly one element.",
        details: "",
      });
      return null;
    }
  }

  function getAllResultsFor(searchObj, callback) {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  }

  return {
    get,
    // put,
    // post,
    // delete: doDelete
  };
});
