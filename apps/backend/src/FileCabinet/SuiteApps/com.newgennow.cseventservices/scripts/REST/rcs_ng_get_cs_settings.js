/**
 * @NApiVersion 2.1
 * @NScriptType Restlet
 */
define(["N/search", "N/query", "N/config", "N/url", "N/cache"], /**
 * @param{search} search
 * @param{query} query
 * @param{config} config
 * @param{url} url
 * @param{cache} cache
 */ (search, query, config, url, cache) => {
  const CACHE_NAME = "CS_SETTINGS_CACHE";
  const CACHE_KEY = "CS_SETTINGS_DATA";
  const CACHE_TTL = 3600; // Cache for 1 hour

  const _SettingsFields = [
    "custrecord_ng_cs_gen_rand_email",
    "custrecord_ng_cs_rand_email_domain",
    "custrecord_ng_cs_rand_email_prefix",
    "custrecord_ng_cs_web_img_folder_id",
    "custrecord_ng_cs_exhb_kit_folder_id",
    "custrecord_ng_cs_use_undep_funds",
    "custrecord_ng_cs_def_dep_account",
    "custrecord_ng_cs_use_show_auditing",
    "custrecord_ng_cs_show_audit_form",
    "custrecord_ng_cs_use_pre_invoicing",
    "custrecord_ng_cs_pre_invoicing_form",
    "custrecord_ng_cs_use_alt_forms",
    "custrecord_ng_cs_prev_adtl_orders",
    "custrecord_ng_cs_allow_mult_billng_part",
    "custrecord_ng_cs_use_show_tax",
    "custrecord_ng_cs_use_cancl_charge",
    "custrecord_ng_cs_cancl_charge_item",
    "custrecord_ng_cs_def_canc_chrg_pct",
    "custrecord_ng_cs_canc_threshold",
    "custrecord_ng_cs_booth_ord_forms",
    "custrecord_ng_cs_add_item_forms",
    "custrecord_ng_cs_do_not_prompt_terms",
    "custrecord_ng_cs_prompt_exclusion_roles",
    "custrecord_ng_cs_import_log_record_id",
    "custrecord_ng_cs_import_log_search_id",
    "custrecord_ng_cs_payment_ar_account",
    "custrecord_ng_cs_cc_auth_item",
    "custrecord_ng_cs_pymnt_fail_eml_template",
    "custrecord_ng_cs_use_job_numbering",
    "custrecord_ng_cs_simple_job_numbering",
    "custrecord_ng_cs_job_num_prefix",
    "custrecord_ng_cs_custom_job_numbering",
    "custrecord_ng_cs_use_multi_cc_proc",
    "custrecord_ng_cs_no_prompt_under_zero",
    "custrecord_ng_cs_prompt_for_new_line",
    "custrecord_ng_cs_retain_last_show",
    "custrecord_ng_cs_retain_last_item_cat",
    "custrecord_ng_cs_default_show_subsidiary",
    "custrecord_ng_cs_no_billed_order_editing",
    "custrecord_ng_cs_billed_ord_edit_users",
    "custrecord_ng_cs_use_scripted_pynt_frm",
    "custrecord_ng_cs_clear_order_cc_details",
    "custrecord_ng_cs_send_invoice_fail_email",
    "custrecord_ng_cs_inv_fail_sender",
    "custrecord_ng_cs_inv_fail_recip",
    "custrecord_ng_cs_inv_fail_cc",
    "custrecord_ng_cs_def_exhb_dept",
    "custrecord_ng_cs_def_exhb_ord_type",
    "custrecord_ng_cs_send_exhib_invoice",
    "custrecord_ng_cs_exhib_invoice_sender",
    "custrecord_ng_cs_exhb_inv_email_template",
    "custrecord_ng_cs_inv_email_conditions",
    "custrecord_ng_cs_give_contacts_access",
    "custrecord_ng_cs_allow_mass_booth_delete",
    "custrecord_ng_cs_mass_booth_delete_roles",
    "custrecord_ng_cs_send_web_pymnt_email",
    "custrecord_ng_cs_web_pymnt_notice_sender",
    "custrecord_ng_cs_web_pymnt_fail_recip",
    "custrecord_ng_cs_web_pymnt_fail_cc",
    "custrecord_ng_cs_csv_import_folder_id",
    "custrecord_ng_cs_allow_show_autopay",
    "custrecord_ng_cs_pymt_rcpt_template",
    "custrecord_ng_cs_dpst_rcpt_template",
    "custrecord_ng_cs_log_time_zone",
    "custrecord_ng_cs_freight_minimum",
    "custrecord_ng_cs_prev_bo_redir_alert",
    "custrecord_ng_cs_dflt_shw_tbl_form",
    "custrecord_ng_cs_dflt_exhibtr_form",
    "custrecord_ng_cs_dflt_booth_order_form",
    "custrecord_ng_cs_activity_log_rec_id",
    "custrecord_ng_cs_activity_log_srch_id",
    "custrecord_ng_cs_auto_charge_web_orders",
    "custrecord_ng_cs_auth_non_web_orders",
    "custrecord_ng_cs_autochrg_cat_excl",
    "custrecord_ng_cs_mastercard",
    "custrecord_ng_cs_visa",
    "custrecord_ng_cs_amex",
    "custrecord_ng_cs_discover",
    "custrecord_ng_cs_default_adv_show_price",
    "custrecord_ng_cs_default_std_show_price",
    "custrecord_ng_cs_default_onst_show_price",
    "custrecord_ng_cs_payment_type",
    "custrecord_ng_cs_dflt_d_calc_date_types",
    "custrecord_ng_cs_dflt_labor_date_types",
    "custrecord_ng_cs_supervisor_item",
    "custrecord_ng_cs_exempt_estimated_items",
    "custrecord_ng_cs_auth_non_web_orders",
    "custrecord_ng_cs_default_show_mgmt_price",
    "custrecord_ng_cs_name_number_ordering",
    "custrecord_ng_cs_name_number_separator",
    "custrecord_ng_cs_use_custom_job",
    "custrecord_ng_cs_def_show_mgmt_dept",
    "custrecord_ng_cs_def_show_mgmt_ord_type",
    "custrecord_ng_cs_default_show_date",
    "custrecord_ng_cs_show_mgt_forms",
    "custrecord_ng_cs_enable_freight_opts_opt",
    "custrecord_ng_cs_enable_graphics_option",
    "custrecord_ng_cs_dflt_show_mgmt_ord_form",
    "custrecord_ng_cs_enable_orientation_opt",
    "custrecord_ng_cs_enable_labor_matrix_opt",
    "custrecord_ng_cs_enforce_item_max_qty",
    "custrecord_ng_cs_enable_paytrace",
    "custrecord_ng_cs_show_calendar_id",
    "custrecord_ng_cs_acct_domain_url",
    "custrecord_ng_cs_algolia_application_id",
    "custrecord_ng_cs_algolia_search_key",
    "custrecord_ng_cs_algolia_api_key",
    "custrecord_ng_cs_algolia_index",
    "custrecord_ng_cs_fclty_addy_template",
    "custrecord_ng_cs_wrhs_addy_template",
    "custrecord_ng_cs_name_from_subsidiary",
    "custrecord_ng_cs_booth_num_line_text",
    "custrecord_ng_cs_wo_img",
    "custrecord_ng_cs_wo_logo_img_url",
    "custrecord_ng_cs_inv_transfer_type",
    "custrecord_ng_cs_transfer_count_markup",
    "custrecord_ng_cs_trnsfr_exmpt_cats",
    "custrecord_ng_cs_default_transfer_from",
    "custrecord_ng_cs_default_to_as_st_loc",
    "custrecord_ng_cs_item_rprts_exluded_cats",
    "custrecord_ng_cs_exhb_wo_exluded_cats",
    "custrecord_ng_cs_hide_bthchklst_cnt_info",
    "custrecord_ng_cs_shade_alt_report_lines",
    "custrecord_ng_cs_report_line_shade_hex",
    "custrecord_ng_cs_report_item_display",
    "custrecord_ng_cs_graphics_item_cat",
    "custrecord_ng_cs_canonical_base_url",
    "custrecord_ng_cs_use_cc_conv_fee",
    "custrecord_ng_cs_cc_conv_fee_rate",
    "custrecord_ng_cs_cc_conv_fee_item",
    "custrecord_ng_cs_cc_conv_fee_order_types",
    "custrecord_ng_cs_csv_import_file",
    "custrecord_ng_cs_default_show_move_in",
    "custrecord_ng_cs_default_exhib_move_in",
    "custrecord_ng_cs_default_show_move_out",
    "custrecord_ng_cs_default_exhib_move_out",
    "custrecord_ng_cs_enable_rentals",
    "custrecord_ng_cs_header_logo_url",
    "custrecord_ng_cs_exhibitor_serv_phone",
    "custrecord_ng_cs_navbar_bckgrnd_color",
    "custrecord_ng_cs_accent_color",
    "custrecord_ng_cs_contact_us_url",
    "custrecord_ng_cs_event_selection_info",
    "custrecord_ng_cs_web_welcome_blurb",
    "custrecord_ng_cs_default_sprvisor_markup",
    "custrecord_ng_cs_conv_fee_web_displayed",
    "custrecord_ng_cses_web_primary_color",
    "custrecord_enable_web_eua_on_checkout",
    "custrecord_web_eua_terms_display",
    "custrecord_ng_bol_enable",
    "custrecord_ng_cs_bol_terms",
    "custrecord_convenience_fee_checkout_labe",
    "custrecord_enable_material_handling_beta",
    "custrecord_login_background_splash",
    "custrecord_footer",
  ];

  const purgeCache = () => {
    const settingsCache = cache.getCache({
      name: CACHE_NAME,
      scope: cache.Scope.PUBLIC,
    });
    settingsCache.remove(CACHE_KEY);
    log.audit("Cache Purged", "CS Settings cache has been cleared");
  };

  /**
   * Defines the function that is executed when a GET request is sent to a RESTlet.
   * @param {Object} requestParams - Parameters from HTTP request URL; parameters passed as an Object (for all supported
   *     content types)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const get = (requestParams) => {
    // Check if we need to purge the cache
    if (requestParams && requestParams.purgeCache === "true") {
      purgeCache();
    }

    // Check cache first
    const settingsCache = cache.getCache({
      name: CACHE_NAME,
      scope: cache.Scope.PUBLIC,
    });

    // let cachedSettings = settingsCache.get({
    //   key: CACHE_KEY,
    //   loader: fetchSettings,
    //   ttl: CACHE_TTL,
    // });

    return JSON.stringify(fetchSettings());
  };

  const fetchSettings = () => {
    // Modified SuiteQL query to return all necessary information
    const bolConfigQuery = `
      SELECT
        'serviceType' as type,
        id,
        name,
        scriptid
      FROM
        customlist_ng_bol_carrier_service
      UNION ALL
      SELECT
        'carrierName' as type,
        id,
        name,
        scriptid
      FROM
        customlist_ng_bol_carrier_name
      UNION ALL
      SELECT
        'deadlineOption' as type,
        id,
        name,
        scriptid
      FROM
        customlist_ng_bol_missed_deadline_opti
      UNION ALL
      SELECT
        'payorOption' as type,
        id,
        name,
        scriptid
      FROM
        customlist_ng_bol_payor
        UNION ALL
        SELECT
        'reRouteOptions' as type,
        id,
        name,
        scriptid
      FROM
        customlist_ng_cs_reroute_shipping_opt
    `;

    const bolConfigResults = query
      .runSuiteQL({ query: bolConfigQuery })
      .asMappedResults();

    log.debug("BOL Config Query Results", bolConfigResults);

    // Process the results into the desired structure
    const bolConfig = bolConfigResults.reduce((acc, item) => {
      const configItem = {
        id: item.id,
        name: item.name,
        scriptid: item.scriptid,
      };

      log.debug("Processing item", { type: item.type, item: configItem });

      switch (item.type) {
        case "serviceType":
          if (!acc.carrier) acc.carrier = {};
          if (!acc.carrier.serviceTypes) acc.carrier.serviceTypes = [];
          acc.carrier.serviceTypes.push(configItem);
          break;
        case "carrierName":
          if (!acc.carrier) acc.carrier = {};
          if (!acc.carrier.names) acc.carrier.names = [];
          acc.carrier.names.push(configItem);
          break;
        case "deadlineOption":
          if (!acc.deadlineOptions) acc.deadlineOptions = [];
          acc.deadlineOptions.push(configItem);
          break;
        case "payorOption":
          if (!acc.payorOptions) acc.payorOptions = [];
          acc.payorOptions.push(configItem);
          break;
        case "reRouteOptions":
          if (!acc.reRouteOptions) acc.reRouteOptions = []
          acc.reRouteOptions.push(configItem)
      }
      return acc;
    }, {});

    log.debug("Processed BOL Config", bolConfig);

    // Use search.lookupFields for settings
    const settings = search.lookupFields({
      type: "customrecord_ng_cs_settings",
      id: "1",
      columns: _SettingsFields,
    });

    // Parse the footer Rich Text field
    if (settings.custrecord_footer && settings.custrecord_footer.length > 0) {
      // Rich text fields come as an array with a text property
      const footerText = settings.custrecord_footer
      // Replace escaped characters with their HTML equivalents
      settings.custrecord_footer = footerText
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&amp;/g, '&');
    } else {
      settings.custrecord_footer = '';
    }

    // Use config.load for company information
    const companyRec = config.load({ type: config.Type.COMPANY_INFORMATION });
    const companyPref = config.load({ type: config.Type.COMPANY_PREFERENCES });

    const company = {
      name: companyRec.getValue("companyname"),
      preferences: {
        shortDateFormat: companyPref.getValue("DATEFORMAT") || "M/DD/YYYY",
      },
    };

    const mainUrl = url.resolveDomain({ hostType: url.HostType.APPLICATION });
    const loginBackgroundSplash = settings.custrecord_login_background_splash;
    const loginBackgroundSplashUrl =
      loginBackgroundSplash && loginBackgroundSplash.length !== 0
        ? `https://${mainUrl}${loginBackgroundSplash[0].text}`
        : null;

    return {
      ...settings,
      loginBackgroundSplashUrl,
      bolConfig,
      company,
    };
  };

  /**
   * Defines the function that is executed when a PUT request is sent to a RESTlet.
   * @param {string | Object} requestBody - The HTTP request body; request body are passed as a string when request
   *     Content-Type is 'text/plain' or parsed into an Object when request Content-Type is 'application/json' (in which case
   *     the body must be a valid JSON)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const put = (requestBody) => {};

  /**
   * Defines the function that is executed when a POST request is sent to a RESTlet.
   * @param {string | Object} requestBody - The HTTP request body; request body is passed as a string when request
   *     Content-Type is 'text/plain' or parsed into an Object when request Content-Type is 'application/json' (in which case
   *     the body must be a valid JSON)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const post = (requestBody) => {};

  /**
   * Defines the function that is executed when a DELETE request is sent to a RESTlet.
   * @param {Object} requestParams - Parameters from HTTP request URL; parameters are passed as an Object (for all supported
   *     content types)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const doDelete = (requestParams) => {};

  return { get /* put, post, delete: doDelete*/ };
});
