/**
 * @NApiVersion 2.1
 * @NScriptType Restlet
 * @NAmdConfig ./amdRestConfig.json
 */
define([
  "N/config",
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/crypto",
  "N/cache",
  "N/error",
  "hooks/locations",
  "../../packages/@prices/ng_server_cm_price_hooks",
  "../lib/ng_cses_checkout_hooks",
], /**
 * @param{config} config
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{crypto} crypto
 * @param{cache} cache
 * @param{error} error
 * @param{location} location
 * @param{Object} pricing
 * @param{Object} checkout
 */ (
  config,
  query,
  record,
  runtime,
  search,
  crypto,
  cache,
  error,
  location,
  pricing,
  checkout
) => {
  // Error Types and Messages
  const ERROR_TYPES = {
    VALIDATION: {
      MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
      INVALID_FORMAT: 'INVALID_FORMAT',
      INVALID_VALUE: 'INVALID_VALUE',
      DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',
      INVALID_CART_ITEMS: 'INVALID_CART_ITEMS'
    },
    RECORD: {
      CREATE_FAILED: 'RECORD_CREATE_FAILED',
      LOAD_FAILED: 'RECORD_LOAD_FAILED',
      UPDATE_FAILED: 'RECORD_UPDATE_FAILED',
      DELETE_FAILED: 'RECORD_DELETE_FAILED'
    },
    PAYMENT: {
      PROCESSING_FAILED: 'PAYMENT_PROCESSING_FAILED',
      INVALID_CARD: 'INVALID_CARD',
      INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
      CARD_EXPIRED: 'CARD_EXPIRED',
      CARD_DECLINED: 'CARD_DECLINED'
    },
    SYSTEM: {
      CONFIGURATION_ERROR: 'CONFIGURATION_ERROR',
      CACHE_ERROR: 'CACHE_ERROR',
      NETWORK_ERROR: 'NETWORK_ERROR',
      INTERNAL_ERROR: 'INTERNAL_ERROR'
    }
  };

  const createError = (type, message, details = {}) => {
    const errorObj = {
      name: type,
      message: message,
      details: details,
      timestamp: new Date().toISOString()
    };

    // Ensure error name includes category if it's an ERROR_TYPES constant
    // This handles cases where the error type is passed in directly from ERROR_TYPES
    // For example: ERROR_TYPES.VALIDATION.INVALID_CART_ITEMS becomes "VALIDATION.INVALID_CART_ITEMS"
    if (typeof type === 'string' && !type.includes('.')) {
      // Find which category this error belongs to
      for (const category in ERROR_TYPES) {
        if (ERROR_TYPES[category].hasOwnProperty(type)) {
          errorObj.name = `${category}.${type}`;
          break;
        }
      }
    }

    log.error({
      title: `Error: ${type}`,
      details: JSON.stringify(errorObj)
    });

    return errorObj;
  };

  const handleError = (err) => {
    // Handle NetSuite native errors
    if (err.type === 'error.SuiteScriptError') {
      log.audit({
        title: "NetSuite Native Error",
        details: JSON.stringify(err)
      });

      return createError(
        ERROR_TYPES.SYSTEM.INTERNAL_ERROR,
        err.message,
        {
          cause: err.cause,
          code: err.code,
          stack: err.stack
        }
      );
    }

    // Handle credit card errors from sales order script
    if (
      typeof err === "object" &&
      typeof err.message === "string" &&
      err.name === "CREDIT_CARD_ERROR"
    ) {
      return createError(
        ERROR_TYPES.PAYMENT.PROCESSING_FAILED,
        err.message,
        {
          ...err,
          event: err.event,
          isCreditCardError: true
        }
      );
    }

    // Handle custom errors
    if (err.name) {
      return createError(
        err.name,
        err.message,
        err.details
      );
    }

    // Handle unknown errors
    return createError(
      ERROR_TYPES.SYSTEM.INTERNAL_ERROR,
      'An unexpected error occurred',
      {
        originalError: err
      }
    );
  };

  let EVENT_CACHE_OBJ;
  let EVENT_DATA;
  let PT_CACHE_OBJ;
  let PT_PROFILE_OBJ;
  let USER_CACHE_OBJ;
  let SETTINGS_CACHE_OBJ;
  let CS_SETTINGS;
  let SCRIPT_SESSION = null;

  /**
   * Defines the function that is executed when a GET request is sent to a RESTlet.
   * @param {Object} requestParams - Parameters from HTTP request URL; parameters passed as an Object (for all supported
   *     content types)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */

  const get = (requestParams) => {
    let payraceProfile = [];
    let eventId = requestParams.ceid;
    let userId = requestParams?.uid;
    let type = requestParams.type;
    let resultsArr = [];
    let settings = {};
    let userRecord;

    SETTINGS_CACHE_OBJ = cache.getCache({
      name: "CS_SETTINGS",
    });

    CS_SETTINGS = SETTINGS_CACHE_OBJ.get({
      key: "CS_SETTINGS",
      loader: getCsSettings,
      ttl: 300,
    });

    PT_CACHE_OBJ = cache.getCache({
      name: "PT_PROFILE",
    });

    PT_PROFILE_OBJ = PT_CACHE_OBJ.get({
      key: "PT_PROFILE",
      loader: payGenLoader,
      ttl: 1800, // 30 minutes
    });

    let returnObject = {};

    try {
      settings = JSON.parse(CS_SETTINGS);
      resultsArr = eventLoader(eventId);
      log.audit({
        title: "Getting Event details",
        details: JSON.stringify(resultsArr),
      });
      payraceProfile = PT_PROFILE_OBJ;

      switch (type) {
        case "checkout":
          userRecord = record.load({
            type: "customer",
            isDynamic: true,
            id: userId,
          });

          returnObject = {
            paytraceProfile: payraceProfile,
            event: resultsArr,
            settings,
            user: userRecord,
          };
          break;
        case "orderRecover":
          let cartId = requestParams.cartRecoverId;
          let processingId = requestParams?.processingId;

          // return same object return as new order generation
          let orderReturn = getSavedOrderId({
            processingId,
            eventId,
          });

          returnObject = {
            order: orderReturn,
          };

          break;
        default:
          log.audit({
            title: "❗ No type found for checkout GET:",
            details: `"${type}"`,
          });
      }
    } catch (e) {
      log.error(`❌ An error occured with gathering info on ${type}`, e);
    }

    return JSON.stringify(returnObject);
  };

  /**
   * Defines the function that is executed when a PUT request is sent to a RESTlet.
   * @param {string | Object} requestBody - The HTTP request body; request body are passed as a string when request
   *     Content-Type is 'text/plain' or parsed into an Object when request Content-Type is 'application/json' (in which case
   *     the body must be a valid JSON)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const put = (requestBody) => {
    log.audit({ title: "Payload Received", details: requestBody });
    let type = requestBody.type;
    const authCompanyId = requestBody.user.id;

    SETTINGS_CACHE_OBJ = cache.getCache({
      name: "CS_SETTINGS",
    });

    CS_SETTINGS = SETTINGS_CACHE_OBJ.get({
      key: "CS_SETTINGS",
      loader: getCsSettings,
      ttl: 300, // 5min
    });

    switch (type) {
      case "UPDATEPAYMENTDEFAULTS": {
        try {
          let cardPayload = requestBody.rec;

          log.audit({ title: "Updating Card Initiated", details: "" });
          log.audit({ title: "Loading customer record", details: "" });

          let customer = record.load({
            type: record.Type.CUSTOMER,
            id: authCompanyId,
            isDynamic: true,
          });

          log.debug({ title: "Customer loaded", details: customer });

          log.audit({ title: "Getting Line count of cards...", details: "" });
          let card_count = customer.getLineCount({
            sublistId: "creditcards",
          });
          log.audit({ title: "Line Count Captured", details: card_count });

          log.audit({
            title: "Looping through address book",
            details: `Searching for matching id of: ${cardPayload}`,
          });
          for (let i = 0; i < card_count; i++) {
            let currentCardId = customer.getSublistValue({
              sublistId: "creditcards",
              fieldId: "internalid",
              line: i,
            });

            if (Number(currentCardId) === Number(cardPayload.internalid)) {
              customer.selectLine({
                sublistId: "creditcards",
                line: i,
              });

              if (cardPayload.ccdefault === "F") {
                log.audit({
                  title: "Loop: Card Match Id!",
                  details: cardPayload.default_shipping_address,
                });
                customer.setCurrentSublistValue({
                  sublistId: "creditcards",
                  fieldId: "ccdefault",
                  value: true,
                });
                customer.commitLine({ sublistId: "creditcards" });
              }
            }
          }

          log.audit({ title: "Out of loop", details: "Saving record changes" });

          let updated_customer = customer.save();

          card_count = customer.getLineCount({
            sublistId: "creditcards",
          });

          if (updated_customer) {
            log.audit({ title: "Save success!", details: "" });

            return JSON.stringify({
              type: "Card Default Change",
              record: customer,
              card_count,
            });
          }
          log.audit({ title: "Save Failed!", details: "" });

          return JSON.stringify("Defaulting card failed!");
        } catch (err) {
          log.error({ title: "Failed to default card!", details: err });
          return JSON.stringify({
            name: "CARD_DEFAULTING_ERROR",
            error: err,
          });
        }
      }
    }
  };

  /**
   * Defines the function that is executed when a POST request is sent to a RESTlet.
   * @param {string | Object} requestBody - The HTTP request body; request body is passed as a string when request
   *     Content-Type is 'text/plain' or parsed into an Object when request Content-Type is 'application/json' (in which case
   *     the body must be a valid JSON)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const post = async (requestBody) => {
    try {
      // Validate required fields
      if (!requestBody?.type) {
        throw createError(
          ERROR_TYPES.VALIDATION.MISSING_REQUIRED_FIELD,
          'Request type is required'
        );
      }

      if (!requestBody?.user?.id) {
        throw createError(
          ERROR_TYPES.VALIDATION.MISSING_REQUIRED_FIELD,
          'User ID is required'
        );
      }

      if (!requestBody?.cart?.length) {
        throw createError(
          ERROR_TYPES.VALIDATION.MISSING_REQUIRED_FIELD,
          'Cart cannot be empty'
        );
      }

      log.audit({ title: "Payload Received", details: requestBody });
      let type = requestBody.type;
      const authCompanyId = requestBody?.user?.id;
      const authContactId = requestBody?.user?.contactProfile.id;
      /**
       * @deprecated - Use ng_cses_rl_user_information.js for address posting
       * @todo - Remove this from the codebase
       * */
      let addressPayload =
        requestBody?.address && JSON.parse(requestBody?.address);
      let selectedAddress = requestBody?.selectedAddress;
      let selectedCard = requestBody?.selectedCard;
      /**
       * @deprecated - This is now a card ID expectation - use ng_cses_rl_user_information.js for card posting
       * @todo - Remove this from the codebase
       * */
      let creditCardPayload = requestBody?.card;
      let creditCardId = requestBody?.card;
      /**
       * @type {Array[Object]|null}
       * */
      let cart = requestBody?.cart;
      let eventPayload = requestBody?.event;
      let eventId = requestBody?.event?.id;
      
      // Validate cart items against collections and event
      if (cart && eventId) {
        // This will throw an error if any items are invalid
        validateCartItems(cart, eventId);
        log.audit({ 
          title: "Cart Items Validated Successfully", 
          details: "All items in cart exist in their respective collections and are active for the event" 
        });
      }

      const contactId = authContactId;
      const processingId = requestBody?.processingId;
      const cartId = requestBody?.cartId;
      /**
       * Booth info submitted durring an order
       * @typedef {Object} BoothSubmitted
       * @property {string} name
       * @property {string} id
       * */

      /**
       * @type {BoothSubmitted|null}
       * */
      let booth = requestBody?.booth;
      let settings;
      let { getCurrentScript } = runtime;
      SCRIPT_SESSION = getCurrentScript();

      SETTINGS_CACHE_OBJ = cache.getCache({
        name: "CS_SETTINGS",
      });

      CS_SETTINGS = SETTINGS_CACHE_OBJ.get({
        key: "CS_SETTINGS",
        loader: getCsSettings,
        ttl: 300,
      });

      settings = JSON.parse(CS_SETTINGS);

      log.debug("Settings loaded", settings);

      switch (type) {
        case "order": {
          log.debug("Request Type", "Order");

          let multiSubsidiaryEnabled = runtime.isFeatureInEffect({
            feature: "MULTISUBSIDIARYCUSTOMER",
          });

          let suitetaxEnabled = runtime.isFeatureInEffect({
            feature: "tax_overhauling",
          });

          let rec = {};
          let newOrderRec = {};
          let perLineTaxesEnabled = false;


          if (!suitetaxEnabled) {
            perLineTaxesEnabled = config
              .load({
                type: "taxpreferences",
              })
              .getValue("perlinetaxesus");
          } else {
            perLineTaxesEnabled = false;
          }

          log.debug({ title: "🔎 Event payload entry:", details: eventPayload });
          log.debug({ title: "⚙️ CS Settings: ", details: settings });

          try {
            log.audit({ title: "🚧 Event Payload:", details: eventPayload });
            log.audit({
              title: "Setting of default booth order form: ",
              details: settings.custrecord_ng_cs_dflt_booth_order_form,
            });

            // Create a sales order
            log.debug({ title: "Creating Sales Order...", details: "" });
            rec = record.create({
              type: record.Type.SALES_ORDER,
              isDynamic: true,
              defaultValues: {
                customform:
                  settings.custrecord_ng_cs_dflt_booth_order_form[0].value,
                // if localdev (auth user ID is -4) then create new addresses against Anonymous Customer
                // otherwise use actual authed company in prod
              },
            });

            rec.setValue({
              fieldId: "entity",
              value: authCompanyId === -4 ? 3 : `${authCompanyId}`,
            });

            multiSubsidiaryEnabled &&
              rec.setValue({
                fieldId: "subsidiary",
                value: eventPayload.subsidiary.id,
              });

            log.debug({ title: "Record Object Initiated", details: rec });

            log.debug({ title: "Setting Entity", details: authCompanyId });

            log.debug({
              title: "🔨 Event and staging state (Should be empty):",
              details: getBoothEventStatus(rec),
            });

            let venueRecord = record.load({
              type: "CUSTOMRECORD_FACILITY",
              id: eventPayload.venue.id,
            });

            /**
             * Sets the shipping address on the sales order according to the event venue address this will adjust the tax rate/nexus automatically
             * @see https://docs.oracle.com/en/cloud/saas/netsuite/ns-online-help/section_4704101831.html
             * */
            let addressObject = {};
            let shippingAddress = {};
            if (venueRecord) {
              log.audit({ title: "✅ Venue loaded: ", details: venueRecord.id });

              // Set shipping address to facility location
              addressObject = createAddressObject(venueRecord);

              log.audit({
                title: "🏦 Address object created!",
                details: "Setting shipping address on Sales order",
              });

              log.audit({
                title: "🔨 Address object created:",
                details: addressObject,
              });

              shippingAddress = rec.getSubrecord({
                fieldId: "shippingaddress",
              });

              // Set country field first when script uses dynamic mode
              // Setting text expects literal name ie=United States
              // Setting value expects abbreviation ie=US

              const fieldValues = {
                country: getCountryShortName(addressObject.country) || "US",
                state: getStateShortName(addressObject.state),
                city: addressObject.city,
                zip: addressObject.zip,
                addrphone: addressObject.phone,
                addr1: addressObject.addressOne,
                addr2: addressObject?.addressTwo,
              };

              Object.entries(fieldValues).forEach(([fieldId, value]) => {
                shippingAddress.setValue({ fieldId, value });
              });

              rec
                .setValue({
                  fieldId: "custbody_ng_cses_web_processing_id",
                  value: processingId,
                })
                .setValue({
                  fieldId: "custbody_show_table",
                  value: eventId,
                  ignoreFieldChange: true,
                })
                .setValue({
                  fieldId: "custbody_booth",
                  value: booth.id,
                  ignoreFieldChange: true,
                })
                .setValue({
                  fieldId: "custbody_booth_actual_exhibitor",
                  // if localdev (auth user ID is -4) then create new addresses against Anonymous Customer
                  // otherwise use actual authed company in prod
                  value: authCompanyId === -4 ? 3 : authCompanyId,
                  ignoreFieldChange: true,
                });

              log.debug({ title: "Booth Set", details: booth });
            } else {
              log.audit({ title: "❌ Venue not loaded: ", details: "" });
            }

            log.audit({
              title: "🚚 Shipping address set!",
              details: shippingAddress,
            });

            if (!suitetaxEnabled) {
              let orderTax = rec.getValue({ fieldId: "taxitem" });

              if (
                !perLineTaxesEnabled &&
                Number(eventPayload.rates.tax.value) !== Number(orderTax)
              ) {
                log.audit({
                  title: "📃 If tax group & tax total rate are not equal",
                  details: "Setting tax group to event tax rate",
                });

                if (addressObject.country === "230") {
                  rec.setValue({
                    fieldId: "taxitem",
                    value: eventPayload.rates.tax.value,
                  });
                }
              }
            }

            log.debug({
              title: "🔨 Event and staging state:",
              details: getBoothEventStatus(rec),
            });

            // Load Venus address

            log.debug({ title: "🚧 Address object:", details: addressObject });
            log.debug({
              title: "🚧 Event Being Set:",
              details: eventPayload.id,
            });

            let csJob = rec.getValue("custbody_cseg_ng_cs_job"); // custbody_cseg_ng_cs_job

            log.audit({ title: "🔎 Is CSJob set?", details: `"${csJob}"` });

            rec.setValue({
              fieldId: "custbody_cseg_ng_cs_job",
              value: `${eventPayload.job.id}`,
            });

            log.debug({
              title: "Job Set",
              details: eventPayload.job.id,
            });

            csJob = rec.getValue("custbody_cseg_ng_cs_job"); // custbody_cseg_ng_cs_job

            log.debug({ title: "csJob Value:", details: csJob });

            log.debug({
              title: "🔨 Event and staging state:",
              details: getBoothEventStatus(rec),
            });

            log.audit({ title: "Event Values Payload", details: eventPayload });

            rec.setValue({
              fieldId: "custbody_isweborder",
              value: true,
            });

            rec.setValue({
              fieldId: "custbody_ng_cses_cart_contact",
              value: authContactId || runtime.getCurrentUser()?.contact,
            });

            rec.setValue({
              fieldId: "custbody_ng_cs_order_type",
              value: `${settings.custrecord_ng_cs_def_exhb_ord_type[0].value}`,
            });

            log.debug({
              title: "Order Type Set",
              details: settings.custrecord_ng_cs_def_exhb_ord_type[0].value,
            });

            handleOrderTypeOnChange(rec, record);

            rec.setValue({
              fieldId: "memo",
              value: requestBody.memo,
            });

            log.debug({
              title: "🔨 Event and staging state:",
              details: getBoothEventStatus(rec),
            });

            rec.setValue({
              fieldId: "creditcard",
              value: creditCardId,
            });
            log.debug({
              title: "Card Value set",
              details: `ID: ${creditCardId}`,
            });

            // rec.setValue({
            //     fieldId: "custbody_ng_paytrace_web_enc_cc_data",
            //     value: selectedCard.internalid,
            // });

            // log.debug('Billing Address List Id', selectedAddress.addressid)

            rec.setValue({
              fieldId: "billaddresslist",
              value: selectedAddress.id,
            });

            log.debug({
              title: "Billing Address List Id Set",
              details: `${selectedAddress.id} at ${1190}`,
            });

            let orderAttachmentRecords = [];

            // Add the items from the cart into the order.
            let priceLevel = pricing.useItemPriceLevel(eventId);

            cart.forEach((itemData, i) => {
              // cart item id
              log.audit({
                title: "Item Line Data",
                details: JSON.stringify(itemData),
              });

              rec.selectLine({ sublistId: "item", line: i });

              log.debug({
                title: "Item Line ID",
                details: `${itemData.internalid}`,
              });

              // Basic item properties
              rec.setCurrentSublistValue({
                sublistId: "item",
                fieldId: "item",
                value: `${itemData.internalid}`, // cart item id
              });

              // rec.setCurrentSublistValue({
              //     sublistId: "item",
              //     fieldId: "item",
              //     value: `${itemData.internalid}` // cart item id
              // });

              log.debug({
                title: "Item Quantity",
                details: `${itemData.quantity}`,
              });

              rec.setCurrentSublistValue({
                sublistId: "item",
                fieldId: "quantity",
                value: itemData.quantity, // cart item quantity
              });

              log.debug({
                title: "Item Quantity Set",
                details: `${itemData.quantity}`,
              });

              rec.setCurrentSublistValue({
                sublistId: "item",
                fieldId: "price",
                value: priceLevel,
              });


              if (!suitetaxEnabled) {
                // Tax details handling
                setTaxDetails(
                  itemData,
                  rec,
                  addressObject,
                  perLineTaxesEnabled,
                  eventPayload
                );
              }

              if (itemData?.memoText) {
                rec.setCurrentSublistValue({
                  sublistId: "item",
                  fieldId: "custcol_ng_cs_item_memo",
                  value: itemData.memoText,
                });

                log.debug({
                  title: "Item Memo Set",
                  details: `${itemData?.memoText}`,
                });
              }

              //Item type specific details
              setItemTypeDetails(itemData, rec);

              // If item is estimated
              if (itemData.isEstimated) {
                rec.setCurrentSublistValue({
                  sublistId: "item",
                  fieldId: "custcol_cost_is_estimated",
                  value: true,
                });
              }

              // If it has file attachments
              if (
                itemData.isUpload &&
                Number(itemData.fileAttachments.attachmentGroup) !== -1
              ) {
                rec.setCurrentSublistValue({
                  sublistId: "item",
                  fieldId: "custcol_ng_cs_item_attachments",
                  value: itemData.fileAttachments.attachmentGroup,
                });
                orderAttachmentRecords.push(
                  itemData.fileAttachments.attachmentGroup
                );
              }

              rec.commitLine({ sublistId: "item" });
              log.debug({ title: "Line Committed", details: itemData });
            });

            // calculate and set the surcharges lines
            calculateSurcharges({ eventId, salesOrder: rec });

            log.debug({
              title: "🔨 Event and staging state:",
              details: getBoothEventStatus(rec),
            });

            // log.debug('Subsidiary', valuesParsedJson.custrecord_show_subsidiary[0].value)
            log.debug({
              title: "Subsid set",
              details: eventPayload.subsidiary.id,
            });

            log.debug({
              title: "Event ID",
              details: `${eventPayload.id} ${eventId}`,
            });

            let orderSaveId = rec.save({
              enableSourcing: true,
              ignoreMandatoryFields: true,
            });

            if (orderSaveId) {
              log.audit({
                title: "🎉 Order created successfully!",
                details: `salesorder:${orderSaveId}`,
              });
            } else {
              log.error({ title: "❌ Order failed to create!", details: "" });
            }

            newOrderRec = record.load({
              type: record.Type.SALES_ORDER,
              id: orderSaveId,
            });

            let originatingOrderId = newOrderRec.getValue(
              "custbody_ng_cs_originating_order"
            );

            let originatingTransactionId =
              originatingOrderId &&
              search.lookupFields({
                type: search.Type.SALES_ORDER,
                id: originatingOrderId,
                columns: ["tranid"],
              }).tranid;

            record.submitFields({
              type: record.Type.SALES_ORDER,
              id: orderSaveId,
              values: {
                custbody_ng_cses_web_order_processed: true,
              },
            });

            log.debug({
              title: "Job from event...",
              details: eventPayload.job.id,
            });
            csJob = newOrderRec.getValue("custbody_cseg_ng_cs_job"); // custbody_cseg_ng_cs_job

            if (csJob) {
              log.audit({
                title: "👍 CS Job is set after submit",
                details: csJob,
              });
            } else {
              log.audit({
                title: "👍 CS Job is not set after submit should be value:",
                details: eventPayload.job.id,
              });
            }

            if (originatingTransactionId) {
              log.audit({
                title: "🟢 Second Booth Order Present appending to response:",
                details: `:${originatingOrderId}`,
              });
            }

            let originOrder = {
              id: originatingOrderId,
              tranid: originatingTransactionId,
            };

            if (orderAttachmentRecords.length !== 0) {
              for (let i = 0; i < orderAttachmentRecords.length; i++) {
                record.submitFields({
                  type: "customrecord_ng_cs_order_attachments",
                  id: orderAttachmentRecords[i],
                  values: {
                    custrecord_transaction_attached:
                      originOrder.id || orderSaveId,
                  },
                });
              }
            }

            if (cartId) {
              record.submitFields({
                type: "customrecord_ng_cses_cust_abandon_cart",
                id: cartId,
                values: {
                  custrecord_ng_cses_abdn_order: originOrder.id || orderSaveId,
                  isinactive: true,
                  custrecord_ng_cses_abdn_order_placed: true,
                },
              });
            }

            return JSON.stringify({
              message: "New order created successfully.",
              customer: authCompanyId,
              order: {
                newOrderRec,
                originOrder,
              },
            });

            // Simulate refetch response
            /* return JSON.stringify({
                message: 'New order still processing...SIMULATED.',
                customer: authCompanyId,
                url: `https://tstdrv1516212.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=437&deploy=1&processingId=${processingId}&contactId=${authContactId}&uid=${authCompanyId}&cartRecoverId=${cartId}&boothId=${booth}&ceid=${eventId}&type=orderRecover`
            }) */
          } catch (error) {
            log.error({ title: "❌ Error occurred in sales order script POST:", details: error });
            return JSON.stringify({
              message: error.message,
              error: handleError(error)
            });
          }
        }
        case "address":
          return manageAddress(type, authCompanyId, requestBody, addressPayload);
        case "card":
          return manageCard(
            creditCardPayload,
            requestBody,
            addressPayload,
            selectedAddress,
            selectedCard,
            settings
          );
        default:
          return JSON.stringify({
            error: {
              name: "INVALID_REQUEST_TYPE_RECEIVED",
              message: "No type in the body was specified.",
            },
          });
      }
    } catch (err) {
      return JSON.stringify({
        error: handleError(err),
        message: error.message
      });
    }

    function handleOrderTypeOnChange(rec, record) {
      log.audit({ title: "⚡ Running Order Type On Change...", details: "" });
      try {
        let currentSalesOrder = rec;
        let orderType = currentSalesOrder.getValue("custbody_ng_cs_order_type");

        let orderTypeRecord = record.load({
          type: "customrecord_ng_cs_order_type",
          id: orderType,
        });

        let depositDefault = orderTypeRecord.getValue(
          "custrecord_ng_cs_order_type_deposit_def"
        );

        currentSalesOrder.setValue({
          fieldId: "requireddepositpercentage",
          value: depositDefault,
        });
      } catch (err) {
        log.error({
          title: "❌ There was an error running deposit percentage set...",
          details: err,
        });
        throw err;
      }
    }
  };

  /**
   * Defines the function that is executed when a DELETE request is sent to a RESTlet.
   * @param {Object} requestParams - Parameters from HTTP request URL; parameters are passed as an Object (for all supported
   *     content types)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  // eslint-disable-next-line no-unused-vars
  const doDelete = (requestParams) => {};

  function eventLoader(eventId) {
    const eventLoadingColumns = [
      search.createColumn({
        name: "name",
        sort: search.Sort.ASC,
        label: "Name",
      }),
      search.createColumn({ name: "scriptid", label: "Script ID" }),
      search.createColumn({ name: "custrecord_fin_show", label: "Class" }),
      search.createColumn({
        name: "custrecord_acct_exec",
        label: "Account Manager",
      }),
      search.createColumn({
        name: "custrecord_sales_rep",
        label: "Sales Rep",
      }),
      search.createColumn({ name: "custrecord_facility", label: "Venue" }),
      search.createColumn({ name: "custrecord_hall", label: "Venue Spaces" }),
      search.createColumn({
        name: "custrecord_facility_contact",
        label: "Venue Contact Name",
      }),
      search.createColumn({
        name: "custrecord_facility_address",
        label: "Venue Address",
      }),
      search.createColumn({
        name: "custrecord_facility_contact_email",
        label: "Venue Contact Email",
      }),
      search.createColumn({
        name: "custrecord_facility_contact_phone",
        label: "Venue Contact Phone",
      }),
      search.createColumn({
        name: "custrecord_show_type",
        label: "Event Type",
      }),
      search.createColumn({
        name: "custrecord_w_start_date",
        label: "Website Start Date",
      }),
      search.createColumn({
        name: "custrecord_w_end_date",
        label: "Website End Date",
      }),
      search.createColumn({
        name: "custrecord_adv_wh_ship_rate",
        label: "Advance Warehouse Drayage Rate",
      }),
      search.createColumn({
        name: "custrecord_inbetween_ship_rate",
        label: "In-Between Shipping Rate",
      }),
      search.createColumn({
        name: "custrecord_direct_shipping_rate",
        label: "Show Site Drayage Rate",
      }),
      search.createColumn({
        name: "custrecord_booth_size",
        label: "Booth Size",
      }),
      search.createColumn({
        name: "custrecord_show_image",
        label: "Event Portal Image",
      }),
      search.createColumn({
        name: "custrecord_show_venue",
        label: "Location",
      }),
      search.createColumn({
        name: "custrecord_show_subsidiary",
        label: "Subsidiary",
      }),
      search.createColumn({
        name: "custrecord_adv_ord_date",
        label: "Advanced Order Date",
      }),
      search.createColumn({
        name: "custrecord_wh_ship_date",
        label: "Warehouse Ship Date",
      }),
      search.createColumn({
        name: "custrecord_ship_to_facility_address",
        label: "Ship To Facility Address",
      }),
      search.createColumn({
        name: "custrecord_ship_to_warehouse_address",
        label: "Ship To Warehouse Address",
      }),
      search.createColumn({
        name: "custrecord_customer_address",
        label: "Customer Address",
      }),
      search.createColumn({
        name: "custrecord_customer_email",
        label: "Customer Email",
      }),
      search.createColumn({
        name: "custrecord_customer_phone",
        label: "Customer Phone",
      }),
      search.createColumn({ name: "custrecord_tax_rate", label: "Tax Rate" }),
      search.createColumn({
        name: "custrecord_tax_percent",
        label: "Tax Percent",
      }),
      // Canada Tax Fields \\
      search.createColumn({
        name: "custrecord_ng_cs_evt_pst_pct",
        label: "Tax Percent PST CA",
      }),
      search.createColumn({
        name: "custrecord_ng_cs_evt_gst_pct",
        label: "Tax Percent GST CA",
      }),
      // \\
      search.createColumn({
        name: "custrecord_ng_cs_event_comments",
        label: "Event Comments",
      }),
      search.createColumn({
        name: "custrecord_adv_price_level",
        label: "Advance Price Level",
      }),
      search.createColumn({
        name: "custrecord_std_price_level",
        label: "Standard Price Level",
      }),
      search.createColumn({
        name: "custrecord_site_price_level",
        label: "On Site Price Level",
      }),
      search.createColumn({
        name: "custrecord_show_mgmnt_price_lvl",
        label: "Show Management Price Level",
      }),
      search.createColumn({ name: "custrecord_show_job", label: "CS Job" }),
      search.createColumn({
        name: "custrecord_show_subsidiary",
        label: "CS Subsidiary",
      }),
    ];

    // Return the event data
    let results = [];
    let eventInfoSearch = search.create({
      type: "customrecord_show",
      filters: [["internalid", "anyof", eventId]],
      columns: eventLoadingColumns,
    });
    let eventInfoResultsCount = eventInfoSearch.runPaged().count;
    log.debug("Event search result count", eventInfoResultsCount);

    eventInfoSearch
      .run()
      .getRange({ start: 0, end: 1 })
      .forEach((e) => results.push(e));

    if (eventInfoResultsCount !== 0) {
      return results[0];
    }

    return results;
  }

  function payGenLoader() {
    let paygenProfile = [];

    let customrecord_ng_paytrace_integrationSearchObj = search.create({
      type: "customrecord_ng_paytrace_integration",
      filters: [],
      columns: [
        search.createColumn({
          name: "name",
          sort: search.Sort.ASC,
          label: "Name",
        }),
        search.createColumn({ name: "scriptid", label: "Script ID" }),
        search.createColumn({
          name: "custrecord_ng_ptid_is_default",
          label: "Is Default Integration",
        }),
        search.createColumn({
          name: "custrecord_ng_ptid_disabled",
          label: "Is Disabled",
        }),
        search.createColumn({
          name: "custrecord_ng_ptid_default_account",
          label: "Default Account",
        }),
        search.createColumn({
          name: "custrecord_ng_ptid_pub_key_file",
          label: "Public Key File",
        }),
      ],
    });
    let searchResultCount =
      customrecord_ng_paytrace_integrationSearchObj.runPaged().count;
    log.debug(
      "customrecord_ng_paytrace_integrationSearchObj result count",
      searchResultCount
    );
    customrecord_ng_paytrace_integrationSearchObj.run().each(function (result) {
      // .run().each has a limit of 4,000 results
      paygenProfile.push(result);
      return true;
    });

    return paygenProfile;
  }

  function getCsSettings() {
    let _SettingsFields = [
      "custrecord_ng_cs_gen_rand_email",
      "custrecord_ng_cs_rand_email_domain",
      "custrecord_ng_cs_rand_email_prefix",
      "custrecord_ng_cs_web_img_folder_id",
      "custrecord_ng_cs_exhb_kit_folder_id",
      "custrecord_ng_cs_use_undep_funds",
      "custrecord_ng_cs_def_dep_account",
      "custrecord_ng_cs_use_show_auditing",
      "custrecord_ng_cs_show_audit_form",
      "custrecord_ng_cs_use_pre_invoicing",
      "custrecord_ng_cs_pre_invoicing_form",
      "custrecord_ng_cs_use_alt_forms",
      "custrecord_ng_cs_prev_adtl_orders",
      "custrecord_ng_cs_allow_mult_billng_part",
      "custrecord_ng_cs_use_show_tax",
      "custrecord_ng_cs_use_cancl_charge",
      "custrecord_ng_cs_cancl_charge_item",
      "custrecord_ng_cs_def_canc_chrg_pct",
      "custrecord_ng_cs_canc_threshold",
      "custrecord_ng_cs_booth_ord_forms",
      "custrecord_ng_cs_add_item_forms",
      "custrecord_ng_cs_do_not_prompt_terms",
      "custrecord_ng_cs_prompt_exclusion_roles",
      "custrecord_ng_cs_import_log_record_id",
      "custrecord_ng_cs_import_log_search_id",
      "custrecord_ng_cs_payment_ar_account",
      "custrecord_ng_cs_cc_auth_item",
      "custrecord_ng_cs_pymnt_fail_eml_template",
      "custrecord_ng_cs_use_job_numbering",
      "custrecord_ng_cs_simple_job_numbering",
      "custrecord_ng_cs_job_num_prefix",
      "custrecord_ng_cs_custom_job_numbering",
      "custrecord_ng_cs_use_multi_cc_proc",
      "custrecord_ng_cs_no_prompt_under_zero",
      "custrecord_ng_cs_prompt_for_new_line",
      "custrecord_ng_cs_retain_last_show",
      "custrecord_ng_cs_retain_last_item_cat",
      "custrecord_ng_cs_default_show_subsidiary",
      "custrecord_ng_cs_no_billed_order_editing",
      "custrecord_ng_cs_billed_ord_edit_users",
      "custrecord_ng_cs_use_scripted_pynt_frm",
      "custrecord_ng_cs_clear_order_cc_details",
      "custrecord_ng_cs_send_invoice_fail_email",
      "custrecord_ng_cs_inv_fail_sender",
      "custrecord_ng_cs_inv_fail_recip",
      "custrecord_ng_cs_inv_fail_cc",
      "custrecord_ng_cs_def_exhb_dept",
      "custrecord_ng_cs_def_exhb_ord_type",
      "custrecord_ng_cs_send_exhib_invoice",
      "custrecord_ng_cs_exhib_invoice_sender",
      "custrecord_ng_cs_exhb_inv_email_template",
      "custrecord_ng_cs_inv_email_conditions",
      "custrecord_ng_cs_give_contacts_access",
      "custrecord_ng_cs_allow_mass_booth_delete",
      "custrecord_ng_cs_mass_booth_delete_roles",
      "custrecord_ng_cs_send_web_pymnt_email",
      "custrecord_ng_cs_web_pymnt_notice_sender",
      "custrecord_ng_cs_web_pymnt_fail_recip",
      "custrecord_ng_cs_web_pymnt_fail_cc",
      "custrecord_ng_cs_csv_import_folder_id",
      "custrecord_ng_cs_allow_show_autopay",
      "custrecord_ng_cs_pymt_rcpt_template",
      "custrecord_ng_cs_dpst_rcpt_template",
      "custrecord_ng_cs_log_time_zone",
      "custrecord_ng_cs_freight_minimum",
      "custrecord_ng_cs_prev_bo_redir_alert",
      "custrecord_ng_cs_dflt_shw_tbl_form",
      "custrecord_ng_cs_dflt_exhibtr_form",
      "custrecord_ng_cs_dflt_booth_order_form",
      "custrecord_ng_cs_activity_log_rec_id",
      "custrecord_ng_cs_activity_log_srch_id",
      "custrecord_ng_cs_auto_charge_web_orders",
      "custrecord_ng_cs_auth_non_web_orders",
      "custrecord_ng_cs_autochrg_cat_excl",
      "custrecord_ng_cs_mastercard",
      "custrecord_ng_cs_visa",
      "custrecord_ng_cs_amex",
      "custrecord_ng_cs_discover",
      "custrecord_ng_cs_default_adv_show_price",
      "custrecord_ng_cs_default_std_show_price",
      "custrecord_ng_cs_default_onst_show_price",
      "custrecord_ng_cs_payment_type",
      "custrecord_ng_cs_dflt_d_calc_date_types",
      "custrecord_ng_cs_dflt_labor_date_types",
      "custrecord_ng_cs_supervisor_item",
      "custrecord_ng_cs_exempt_estimated_items",
      "custrecord_ng_cs_auth_non_web_orders",
      "custrecord_ng_cs_default_show_mgmt_price",
      "custrecord_ng_cs_name_number_ordering",
      "custrecord_ng_cs_name_number_separator",
      "custrecord_ng_cs_use_custom_job",
      "custrecord_ng_cs_def_show_mgmt_dept",
      "custrecord_ng_cs_def_show_mgmt_ord_type",
      "custrecord_ng_cs_default_show_date",
      "custrecord_ng_cs_show_mgt_forms",
      "custrecord_ng_cs_enable_freight_opts_opt",
      "custrecord_ng_cs_enable_graphics_option",
      "custrecord_ng_cs_dflt_show_mgmt_ord_form",
      "custrecord_ng_cs_enable_orientation_opt",
      "custrecord_ng_cs_enable_labor_matrix_opt",
      "custrecord_ng_cs_enforce_item_max_qty",
      "custrecord_ng_cs_enable_paytrace",
      "custrecord_ng_cs_show_calendar_id",
      "custrecord_ng_cs_acct_domain_url",
      "custrecord_ng_cs_algolia_application_id",
      "custrecord_ng_cs_algolia_search_key",
      "custrecord_ng_cs_algolia_api_key",
      "custrecord_ng_cs_algolia_index",
      "custrecord_ng_cs_fclty_addy_template",
      "custrecord_ng_cs_wrhs_addy_template",
      "custrecord_ng_cs_name_from_subsidiary",
      "custrecord_ng_cs_booth_num_line_text",
      "custrecord_ng_cs_wo_img",
      "custrecord_ng_cs_wo_logo_img_url",
      "custrecord_ng_cs_inv_transfer_type",
      "custrecord_ng_cs_transfer_count_markup",
      "custrecord_ng_cs_trnsfr_exmpt_cats",
      "custrecord_ng_cs_default_transfer_from",
      "custrecord_ng_cs_default_to_as_st_loc",
      "custrecord_ng_cs_item_rprts_exluded_cats",
      "custrecord_ng_cs_exhb_wo_exluded_cats",
      "custrecord_ng_cs_hide_bthchklst_cnt_info",
      "custrecord_ng_cs_shade_alt_report_lines",
      "custrecord_ng_cs_report_line_shade_hex",
      "custrecord_ng_cs_report_item_display",
      "custrecord_ng_cs_graphics_item_cat",
      "custrecord_ng_cs_canonical_base_url",
      "custrecord_ng_cs_use_cc_conv_fee",
      "custrecord_ng_cs_cc_conv_fee_rate",
      "custrecord_ng_cs_cc_conv_fee_item",
      "custrecord_ng_cs_cc_conv_fee_order_types",
      "custrecord_ng_cs_csv_import_file",
      "custrecord_ng_cs_default_show_move_in",
      "custrecord_ng_cs_default_exhib_move_in",
      "custrecord_ng_cs_default_show_move_out",
      "custrecord_ng_cs_default_exhib_move_out",
      "custrecord_ng_cs_enable_rentals",
    ];

    let settings = search.lookupFields({
      type: "customrecord_ng_cs_settings",
      id: "1",
      columns: _SettingsFields,
    });

    return settings;
  }

  function getSavedOrderId({ contactId, processingId }) {
    log.audit({
      title: "🟡 Order Id search running for order recovery",
      details: {},
    });
    log.debug({
      title: "🟡 Order Id search running with params",
      details: {
        processingId,
        contactId,
      },
    });

    let existingProcessedOrders = search.create({
      type: search.Type.SALES_ORDER,
      filters: [
        ["custbody_ng_cses_web_processing_id", "is", processingId],
        "AND",
        ["mainline", "is", "T"],
      ],
      columns: ["custbody_ng_cs_originating_order", "internalid"],
    });

    let existingProcessedOrdersCount = existingProcessedOrders.runPaged().count;

    log.audit({
      title: "🛒 Order recover ran - Count administered ->",
      details: `"${existingProcessedOrdersCount}"`,
    });

    let newOrderRec = {};

    let orderResults = [];
    getAllResultsFor(existingProcessedOrders, (result) => {
      let objRes = {
        id: result.id,
        existingOrderId: result.getValue("custbody_ng_cs_originating_order"),
      };

      orderResults.push(objRes);
    });

    if (existingProcessedOrdersCount === 0) {
      log.audit({
        title: "🔎 No orders processed found - rerunning",
        details: "",
      });
      return {
        error: {
          name: "ORDER_CANNOT_FETCH",
          message:
            "Order is not existent, completely processed, or has encountered a submission error.",
        },
      };
    }
    log.audit({ title: "🔎 Processed orders found:", details: orderResults });
    if (existingProcessedOrdersCount === 1) {
      newOrderRec = record.load({
        type: record.Type.SALES_ORDER,
        id: orderResults[0].id,
      });
    }

    let originatingOrderId = orderResults[0]?.existingOrderId;

    let originatingTransactionId =
      originatingOrderId &&
      search.lookupFields({
        type: search.Type.SALES_ORDER,
        id: originatingOrderId,
        columns: ["tranid"],
      }).tranid;

    if (originatingTransactionId) {
      log.audit({
        title: "🟢 Second Booth Order Present appending to response:",
        details: `salesOrder:${originatingOrderId}`,
      });
    }

    let originOrder = {
      id: originatingOrderId,
      tranid: originatingTransactionId,
    };

    return {
      newOrderRec,
      originOrder,
    };
  }

  function manageAddress(type, authCompanyId, requestBody, addressPayload) {
    log.debug({ title: "Post type is", details: "Address addition" });
    log.debug({ title: "Payload Captured", details: requestBody });
    // Rest of the logic for address management
    // If you need to return anything, return it from here.

    // Create Address

    log.debug({
      title: "Payload object address:",
      details: addressPayload,
    });

    let customer = record.load({
      type: record.Type.CUSTOMER,
      isDynamic: true,
      id: authCompanyId,
    });

    log.debug({ title: "Record Loaded", details: customer });

    customer.selectNewLine({
      sublistId: "addressbook",
    });

    let addressSubRecord = customer.getCurrentSublistSubrecord({
      sublistId: "addressbook",
      fieldId: "addressbookaddress",
    });

    let expectedCountry = getCountryCodeOrName(
      addressPayload.billingCountry
    )[0];

    log.debug({
      title: "Running Country Code Aglo",
      details: expectedCountry,
    });
    // Set country first bc of form field changes
    addressSubRecord.setText({
      fieldId: "country",
      text: addressPayload.billingCountry,
    });
    addressSubRecord.setValue({
      fieldId: "addressee",
      value: `${addressPayload.firstName} ${addressPayload.lastName}`,
    });

    // When user enters in address without using autocomplete (clicking the address) set as the entered field
    if (addressPayload?.billingAddressName) {
      addressSubRecord.setValue({
        fieldId: "addr1",
        value: addressPayload.billingAddressName,
      });
    } else {
      addressSubRecord.setValue({
        fieldId: "addr1",
        value: addressPayload.billingAddress1,
      });
    }

    addressSubRecord.setValue({
      fieldId: "addr2",
      value: addressPayload.billingAddress2,
    });
    addressSubRecord.setValue({
      fieldId: "city",
      value: addressPayload.billingCity,
    });
    addressSubRecord.setValue({
      fieldId: "state",
      value: addressPayload.billingState,
    });
    addressSubRecord.setValue({
      fieldId: "zip",
      value: addressPayload.billingZip,
    });

    if (addressPayload?.billingAddressObject) {
      addressSubRecord.setValue({
        fieldId: "addrtext",
        value: addressPayload.billingAddressObject.description,
      });
    }
    addressSubRecord.setValue({
      fieldId: "addrphone",
      value: addressPayload.billingPhone,
    });
    addressSubRecord.setValue({ fieldId: "override", value: false });
    addressSubRecord.commit();
    customer.setCurrentSublistText({
      sublistId: "addressbook",
      fieldId: "country",
      text: addressPayload.billingCountry,
    });
    customer.setCurrentSublistValue({
      sublistId: "addressbook",
      fieldId: "isresidential",
      value: addressPayload.isResidential,
    });
    customer.setCurrentSublistValue({
      sublistId: "addressbook",
      fieldId: "defaultbilling",
      value: addressPayload.defaultBilling,
    });
    customer.setCurrentSublistValue({
      sublistId: "addressbook",
      fieldId: "defaultshipping",
      value: addressPayload.defaultShipping,
    });
    customer.setCurrentSublistText({
      sublistId: "addressbook",
      fieldId: "state",
      text: addressPayload.billingState,
    });
    customer.setCurrentSublistValue({
      sublistId: "addressbook",
      fieldId: "city",
      value: addressPayload.billingCity,
    });
    customer.setCurrentSublistValue({
      sublistId: "addressbook",
      fieldId: "phone",
      value: addressPayload.billingPhone,
    });
    customer.setCurrentSublistText({
      sublistId: "addressbook",
      fieldId: "zip",
      value: addressPayload.billingZip,
    });
    customer.setCurrentSublistValue({
      sublistId: "addressbook",
      fieldId: "addressee",
      value: `${addressPayload.firstName} ${addressPayload.lastName}`,
    });

    customer.commitLine({ sublistId: "addressbook" });

    customer.save({
      ignoreMandatoryFields: false,
    });

    let addressBookCount = customer.getLineCount({
      sublistId: "addressbook",
    });

    log.debug({ title: "Address Book Sublist", details: addressPayload });

    return JSON.stringify({
      type: "Address Addition",
      record: customer,
      address_count: addressBookCount,
    });
  }

  function manageCard(
    creditCardPayload,
    requestBody,
    addressPayload,
    selectedAddress,
    selectedCard,
    settings
  ) {
    const getCardType = (number) => {
      // visa
      let re = new RegExp("^4");
      if (number.match(re) != null)
        return settings.custrecord_ng_cs_visa[0].text;

      // Mastercard
      // Updated for Mastercard 2017 BINs expansion
      if (
        /^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$/.test(
          number
        )
      )
        return settings.custrecord_ng_cs_mastercard[0].text;

      // AMEX
      re = new RegExp("^3[47]");
      if (number.match(re) != null)
        return settings.custrecord_ng_cs_amex[0].text;

      // Discover
      re = new RegExp(
        "^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)"
      );
      if (number.match(re) != null)
        return settings.custrecord_ng_cs_discover[0].text;

      // Diners
      re = new RegExp("^36");
      if (number.match(re) != null) return "Diners";

      // Diners - Carte Blanche
      re = new RegExp("^30[0-5]");
      if (number.match(re) != null) return "Diners - Carte Blanche";

      // JCB
      re = new RegExp("^35(2[89]|[3-8][0-9])");
      if (number.match(re) != null) return "JCB";

      // Visa Electron
      re = new RegExp("^(4026|417500|4508|4844|491(3|7))");
      if (number.match(re) != null) return "Visa Electron";

      return "";
    }; // Determine card type

    log.debug({ title: "Post type is", details: "Card addition" });
    try {
      let maskedCardNumberFirstFour =
        `${creditCardPayload.paymentCardNumber}`.slice(0, 4);
      let maskedCardNumberLastFour =
        `${creditCardPayload.paymentCardNumber}`.slice(-4);

      let protectedPayload = {
        ...requestBody,
        address: addressPayload,
        selected_address: selectedAddress,
        selected_payment: selectedCard,
        card: {
          ...creditCardPayload,
          paymentCardNumber: `${maskedCardNumberFirstFour}********${maskedCardNumberLastFour}`,
        },
      };

      const authCompanyId = requestBody?.user?.id;

      log.debug({ title: "Post type is", details: "Card addition" });

      log.debug({
        title: "🔒 Payload Captured (Protected):",
        details: protectedPayload,
      });

      let cardType = getCardType(creditCardPayload.paymentCardNumber);

      log.debug({ title: "Card Type", details: cardType });

      // Add new card
      let expiryDateObj = new Date(creditCardPayload.paymentExpiry);
      log.audit({ title: "📂 Card expiration:", details: expiryDateObj });

      let yyyy = expiryDateObj.getFullYear();
      let mm = expiryDateObj.getMonth() + 1;

      expiryDateObj.setMonth(mm);
      expiryDateObj.setFullYear(yyyy);

      log.audit({
        title: "⚡ Card expiration templated:",
        details: `${mm}/${yyyy}`,
      });

      // Load Customer Record
      let customer = record.load({
        type: record.Type.CUSTOMER,
        isDynamic: true,
        id: authCompanyId,
      });

      let creditCardCount = customer.getLineCount({
        sublistId: "creditcards",
      });
      let creditCardIds = [];

      for (let i = 0; i < creditCardCount; i++) {
        let id = customer.getSublistValue({
          sublistId: "creditcards",
          fieldId: "internalid",
          line: i,
        });
        log.debug({ title: "Card ID Before", details: id });
        creditCardIds.push(id);
      }

      log.debug({ title: "Record Loaded", details: customer });

      customer.selectNewLine({
        sublistId: "creditcards",
      });

      customer.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "ccnumber",
        value: creditCardPayload.paymentCardNumber,
      });
      customer.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "ccname",
        value: creditCardPayload.paymentNameOnCard,
      });
      customer.setCurrentSublistText({
        sublistId: "creditcards",
        fieldId: "paymentmethod",
        text: cardType,
      });
      customer.setCurrentSublistText({
        sublistId: "creditcards",
        fieldId: "ccexpiredate",
        text: `${mm}/${yyyy}`,
      });
      customer.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "memo",
        value: creditCardPayload.paymentCardNote,
      });

      customer.setCurrentSublistValue({
        sublistId: "creditcards",
        fieldId: "ccdefault",
        value: creditCardPayload.defaultPayment,
      });

      customer.commitLine({ sublistId: "creditcards" });

      customer.save({
        ignoreMandatoryFields: false,
      });

      // delayScript(3000)

      let updatedCustomer = record.load({
        type: record.Type.CUSTOMER,
        isDynamic: true,
        id: authCompanyId,
      });

      let newCardIds = [];
      let updatedCreditCardCount = updatedCustomer.getLineCount({
        sublistId: "creditcards",
      }); // this isn't the problem

      for (let i = 0; i < updatedCreditCardCount; i++) {
        let id = updatedCustomer.getSublistValue({
          sublistId: "creditcards",
          fieldId: "internalid",
          line: i,
        });
        newCardIds.push(id);
      }

      let newCardId = newCardIds.filter((id) => !creditCardIds.includes(id))[0];

      log.debug({ title: "New Card ID", details: newCardId });

      // Get default paytrace profile if paytrace is enabled
      let paytraceResults = [];
      log.audit({
        title: "Checking if paytrace is enabled...",
        details: settings.custrecord_ng_cs_enable_paytrace ? "✅" : "❌",
      });

      if (settings.custrecord_ng_cs_enable_paytrace) {
        log.audit({
          title: "Paytrace is enabled running extra tasks for card encryption",
          details: "",
        });
        let paytrace_profile_search = search.create({
          type: "customrecord_ng_paytrace_integration",
          filters: [["custrecord_ng_ptid_is_default", "is", "T"]],
          columns: [
            search.createColumn({
              name: "name",
              sort: search.Sort.ASC,
              label: "Name",
            }),
            search.createColumn({
              name: "custrecord_ng_ptid_disabled",
              label: "Is Disabled",
            }),
            search.createColumn({
              name: "internalid",
              label: "Internal ID",
            }),
          ],
        });

        let paytrace_profile_search_count =
          paytrace_profile_search.runPaged().count;
        log.debug(
          "paytrace_profile_search_count result count",
          paytrace_profile_search_count
        );
        paytrace_profile_search.run().each(function (result) {
          // .run().each has a limit of 4,000 results
          paytraceResults.push(result);
          return true;
        });

        let paytraceProfileResult = JSON.stringify(paytraceResults[0]);
        let paytraceProfileResultJson = JSON.parse(paytraceProfileResult);

        log.debug({
          title: "🌍 Using profile:",
          details: paytraceProfileResultJson,
        });

        // Create paytrace card record if paytrace integration is active
        let pt_record;
        log.audit({
          title: "Check if paytrace is enabled...",
          details: "⌛",
        });
        log.audit({
          title: "Paytrace is enabled! Adding encrypted card",
          details: "✅",
        });
        pt_record = record.create({
          type: "customrecord_ng_pt_ecrypted_card",
          isDynamic: true,
        });

        log.debug("New Card Id", newCardId);

        /*
         * "custrecord_ng_ptecd_encrypted_card": creditCardPayload.paymentCardEncryptedNumber,
         * "custrecord_ng_ptecd_card_id": updatedCustomer.getSublistValue({sublistId: 'creditcards', fieldId: 'internalid', line: creditCardCount}),
         * "custrecord_ng_ptecd_customer": authCompanyId,
         * "custrecord_ng_ptecd_applied_profile": paytraceProfileResultJson.id
         * */

        pt_record.setValue(
          "custrecord_ng_ptecd_encrypted_card",
          creditCardPayload.paymentCardEncryptedNumber
        );
        pt_record.setValue("custrecord_ng_ptecd_customer", authCompanyId);
        pt_record.setValue(
          "custrecord_ng_ptecd_applied_profile",
          paytraceProfileResultJson.id
        );
        pt_record.setValue("custrecord_ng_ptecd_card_id", parseInt(newCardId));

        let newEncryptedCard = pt_record.save();

        log.audit({
          title: "Card Record created?",
          details: newEncryptedCard
            ? "Created card successfully! 💳"
            : "Card record failed to save! ❌",
        });
      }

      log.audit({
        title: "Paytrace is disabled - not adding encrypted card",
        details: "❌",
      });

      return JSON.stringify({
        type: "Card Addition",
        customer: updatedCustomer,
        card_count: updatedCreditCardCount,
        card_id: newCardId,
        paytrace_profile: settings.custrecord_ng_cs_enable_paytrace
          ? paytraceResults[0]
          : paytraceResults,
      });
    } catch (e) {
      return JSON.stringify({ name: "CARD_ADDITION_ERROR", error: e });
    }
  }

  function getAllResultsFor(searchObj, callback) {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  }

  // eslint-disable-next-line no-unused-vars
  function delayScript(delay) {
    let startTime;
    let curTime;
    startTime = new Date().getTime();
    let timeDiff = 0;

    while (timeDiff < delay) {
      curTime = new Date().getTime();
      timeDiff = curTime - startTime; // in ms
    }
  }

  function getCountryCodeOrName(inputValue) {
    let nameCountries = {
      Afghanistan: "AF",
      "Aland Islands": "AX",
      Albania: "AL",
      Algeria: "DZ",
      "American Samoa": "AS",
      Andorra: "AD",
      Angola: "AO",
      Anguilla: "AI",
      Antarctica: "AQ",
      "Antigua And Barbuda": "AG",
      Argentina: "AR",
      Armenia: "AM",
      Aruba: "AW",
      Australia: "AU",
      Austria: "AT",
      Azerbaijan: "AZ",
      Bahamas: "BS",
      Bahrain: "BH",
      Bangladesh: "BD",
      Barbados: "BB",
      Belarus: "BY",
      Belgium: "BE",
      Belize: "BZ",
      Benin: "BJ",
      Bermuda: "BM",
      Bhutan: "BT",
      Bolivia: "BO",
      "Bosnia And Herzegovina": "BA",
      Botswana: "BW",
      "Bouvet Island": "BV",
      Brazil: "BR",
      "British Indian Ocean Territory": "IO",
      "Brunei Darussalam": "BN",
      Bulgaria: "BG",
      "Burkina Faso": "BF",
      Burundi: "BI",
      Cambodia: "KH",
      Cameroon: "CM",
      Canada: "CA",
      "Cape Verde": "CV",
      "Cayman Islands": "KY",
      "Central African Republic": "CF",
      Chad: "TD",
      Chile: "CL",
      China: "CN",
      "Christmas Island": "CX",
      "Cocos (Keeling) Islands": "CC",
      Colombia: "CO",
      Comoros: "KM",
      Congo: "CG",
      "Congo, Democratic Republic": "CD",
      "Cook Islands": "CK",
      "Costa Rica": "CR",
      "Cote D'Ivoire": "CI",
      Croatia: "HR",
      Cuba: "CU",
      Cyprus: "CY",
      "Czech Republic": "CZ",
      Denmark: "DK",
      Djibouti: "DJ",
      Dominica: "DM",
      "Dominican Republic": "DO",
      Ecuador: "EC",
      Egypt: "EG",
      "El Salvador": "SV",
      "Equatorial Guinea": "GQ",
      Eritrea: "ER",
      Estonia: "EE",
      Ethiopia: "ET",
      "Falkland Islands (Malvinas)": "FK",
      "Faroe Islands": "FO",
      Fiji: "FJ",
      Finland: "FI",
      France: "FR",
      "French Guiana": "GF",
      "French Polynesia": "PF",
      "French Southern Territories": "TF",
      Gabon: "GA",
      Gambia: "GM",
      Georgia: "GE",
      Germany: "DE",
      Ghana: "GH",
      Gibraltar: "GI",
      Greece: "GR",
      Greenland: "GL",
      Grenada: "GD",
      Guadeloupe: "GP",
      Guam: "GU",
      Guatemala: "GT",
      Guernsey: "GG",
      Guinea: "GN",
      "Guinea-Bissau": "GW",
      Guyana: "GY",
      Haiti: "HT",
      "Heard Island & Mcdonald Islands": "HM",
      "Holy See (Vatican City State)": "VA",
      Honduras: "HN",
      "Hong Kong": "HK",
      Hungary: "HU",
      Iceland: "IS",
      India: "IN",
      Indonesia: "ID",
      "Iran, Islamic Republic Of": "IR",
      Iraq: "IQ",
      Ireland: "IE",
      "Isle Of Man": "IM",
      Israel: "IL",
      Italy: "IT",
      Jamaica: "JM",
      Japan: "JP",
      Jersey: "JE",
      Jordan: "JO",
      Kazakhstan: "KZ",
      Kenya: "KE",
      Kiribati: "KI",
      Korea: "KR",
      Kuwait: "KW",
      Kyrgyzstan: "KG",
      "Lao People's Democratic Republic": "LA",
      Latvia: "LV",
      Lebanon: "LB",
      Lesotho: "LS",
      Liberia: "LR",
      "Libyan Arab Jamahiriya": "LY",
      Liechtenstein: "LI",
      Lithuania: "LT",
      Luxembourg: "LU",
      Macao: "MO",
      Macedonia: "MK",
      Madagascar: "MG",
      Malawi: "MW",
      Malaysia: "MY",
      Maldives: "MV",
      Mali: "ML",
      Malta: "MT",
      "Marshall Islands": "MH",
      Martinique: "MQ",
      Mauritania: "MR",
      Mauritius: "MU",
      Mayotte: "YT",
      Mexico: "MX",
      "Micronesia, Federated States Of": "FM",
      Moldova: "MD",
      Monaco: "MC",
      Mongolia: "MN",
      Montenegro: "ME",
      Montserrat: "MS",
      Morocco: "MA",
      Mozambique: "MZ",
      Myanmar: "MM",
      Namibia: "NA",
      Nauru: "NR",
      Nepal: "NP",
      Netherlands: "NL",
      "Netherlands Antilles": "AN",
      "New Caledonia": "NC",
      "New Zealand": "NZ",
      Nicaragua: "NI",
      Niger: "NE",
      Nigeria: "NG",
      Niue: "NU",
      "Norfolk Island": "NF",
      "Northern Mariana Islands": "MP",
      Norway: "NO",
      Oman: "OM",
      Pakistan: "PK",
      Palau: "PW",
      "Palestinian Territory, Occupied": "PS",
      Panama: "PA",
      "Papua New Guinea": "PG",
      Paraguay: "PY",
      Peru: "PE",
      Philippines: "PH",
      Pitcairn: "PN",
      Poland: "PL",
      Portugal: "PT",
      "Puerto Rico": "PR",
      Qatar: "QA",
      Reunion: "RE",
      Romania: "RO",
      "Russian Federation": "RU",
      Rwanda: "RW",
      "Saint Barthelemy": "BL",
      "Saint Helena": "SH",
      "Saint Kitts And Nevis": "KN",
      "Saint Lucia": "LC",
      "Saint Martin": "MF",
      "Saint Pierre And Miquelon": "PM",
      "Saint Vincent And Grenadines": "VC",
      Samoa: "WS",
      "San Marino": "SM",
      "Sao Tome And Principe": "ST",
      "Saudi Arabia": "SA",
      Senegal: "SN",
      Serbia: "RS",
      Seychelles: "SC",
      "Sierra Leone": "SL",
      Singapore: "SG",
      Slovakia: "SK",
      Slovenia: "SI",
      "Solomon Islands": "SB",
      Somalia: "SO",
      "South Africa": "ZA",
      "South Georgia And Sandwich Isl.": "GS",
      Spain: "ES",
      "Sri Lanka": "LK",
      Sudan: "SD",
      Suriname: "SR",
      "Svalbard And Jan Mayen": "SJ",
      Swaziland: "SZ",
      Sweden: "SE",
      Switzerland: "CH",
      "Syrian Arab Republic": "SY",
      Taiwan: "TW",
      Tajikistan: "TJ",
      Tanzania: "TZ",
      Thailand: "TH",
      "Timor-Leste": "TL",
      Togo: "TG",
      Tokelau: "TK",
      Tonga: "TO",
      "Trinidad And Tobago": "TT",
      Tunisia: "TN",
      Turkey: "TR",
      Turkmenistan: "TM",
      "Turks And Caicos Islands": "TC",
      Tuvalu: "TV",
      Uganda: "UG",
      Ukraine: "UA",
      "United Arab Emirates": "AE",
      "United Kingdom": "GB",
      "United States": "USA",
      "United States Outlying Islands": "UM",
      Uruguay: "UY",
      Uzbekistan: "UZ",
      Vanuatu: "VU",
      Venezuela: "VE",
      "Viet Nam": "VN",
      "Virgin Islands, British": "VG",
      "Virgin Islands, U.S.": "VI",
      "Wallis And Futuna": "WF",
      "Western Sahara": "EH",
      Yemen: "YE",
      Zambia: "ZM",
      Zimbabwe: "ZW",
    };

    let errorCode = "";

    if (
      inputValue === undefined ||
      Boolean(inputValue.match(/^[^,'& ()-]{1}([a-zA-Z\s',&()-]){0,80}$/)) ===
        false
    ) {
      errorCode = "5555";
    }
    let inlength = inputValue.length;
    if (errorCode === "5555") {
      return [
        "ErrorCode 5555: Unsupported Data, return input value in array 1",
        inputValue,
      ];
    }

    if (inlength <= 3) {
      var outValue = inputValue.toUpperCase();
      let countryName = "";
      for (let name in nameCountries) {
        if (nameCountries[name] === outValue) {
          countryName = name;
        }
      }
      switch (true) {
        case Boolean(countryName === ""):
          return [
            "Country code does not match database record: return input value in array 1",
            inputValue,
          ];
          break;

        default:
          return [countryName, inputValue];
          break;
      }
    }

    if (inlength > 3) {
      var outValue = inputValue.toLowerCase();

      switch (true) {
        case Boolean(
          outValue.match(
            /^[Gg][Uu][Ii][Nn][Ee][Aa][-][Bb][Ii][Ss][Ss][Aa][Uu]$/
          )
        ):
          outValue = "Guinea-Bissau";
          break;

        case Boolean(
          outValue.match(
            /^[Hh][Oo][Ll][Yy][ ][Ss][Ee][Ee][ ][(][Vv][Aa][Tt][Ii][Cc][Aa][Nn][ ][Cc][Ii][Tt][Yy][ ][Ss][Tt][Aa][Tt][Ee][)]$/
          )
        ):
          outValue = "Holy See (Vatican City State)";
          break;

        case Boolean(
          outValue.match(
            /[Ff][Aa][Ll][Kk][Ll][Aa][Nn][Dd][ ][Ii][Ss][Ll][Aa][Nn][Dd][Ss][ ][(][Mm][Aa][Ll][Vv][Ii][Nn][Aa][Ss][)]$/
          )
        ):
          outValue = "Falkland Islands (Malvinas)";
          break;

        case Boolean(outValue.match(/^[Ii][Rr][Aa][Nn]$/)):
          outValue = "Iran, Islamic Republic Of";
          break;

        default:
          var arrCName = outValue.split(" ");
          var cNameJoin = "";
          for (let i in arrCName) {
            cNameJoin += `${
              arrCName[i].charAt(0).toUpperCase() + arrCName[i].slice(1)
            } `;
          }
          outValue = cNameJoin.trim();
          break;
      }

      return [
        Object.prototype.hasOwnProperty.call(nameCountries, outValue)
          ? nameCountries[outValue]
          : "Input country doesn't match database: returned input in array 1",
        inputValue,
      ];
    }
  }
  function getStateShortName(stateId) {
    return query
      .runSuiteQL({
        query: `SELECT shortname FROM state WHERE id = '${stateId}'`,
      })
      .asMappedResults()[0].shortname;
  }

  function getCountryShortName(countryId) {
    return query
      .runSuiteQL({
        query: `SELECT id FROM COUNTRY WHERE uniquekey = '${countryId}'`,
      })
      .asMappedResults()[0].id;
  }

  function getBoothEventStatus(recObj) {
    return {
      entity: recObj.getValue("entity"),
      booth: recObj.getValue("custbody_booth"),
      boothExhibitor: recObj.getValue("custbody_booth_actual_exhibitor"),
      event: recObj.getValue("custbody_show_table"),
    };
  }

  const roundOff = (num, places) => {
    const x = Math.pow(10, places);
    return Math.round(num * x) / x;
  };

  /*
   * Cart Item Functions
   * */

  /**
   * Module to set the details of each type of items
   * @param {*} itemData
   * @param {Record} rec
   */
  function setItemTypeDetails(itemData, rec) {
    // Set days calc & show duration dates in Options field for item.
    if (itemData?.attributes) {
      itemData.attributes = Object.keys(itemData.attributes).map((key) => {
        return `${key}: ${itemData.attributes[key]}\n`;
      });
    }

    if (itemData.isDaysCalc || itemData.isShowDuration)
      rec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "custcol_labor_date",
        value: `${itemData.showDate}`,
      });

    if (itemData?.attributes.length !== 0) {
      rec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "description",
        value: `Attributes:\n${itemData.attributes.join("")}`,
      });
    }

    if (itemData.isSquareFt) {
      handleSquareFtItem(itemData, rec);
    }

    if (itemData.isFreight) {
      handleFreightItem(itemData, rec);
    }

    if (itemData.isLabor) {
      handleLaborItem(itemData, rec);
    }
  }

  /**
   * Module to add the tax related properties of item in list
   * @param {Object} itemData
   * @param {Record} rec
   * @param addressObject {Object}
   * @param perLineTaxesEnabled {Boolean}
   * @param eventPayload {Object}
   */
  function setTaxDetails(
    itemData,
    rec,
    addressObject,
    perLineTaxesEnabled,
    eventPayload
  ) {
    let taxValue = eventPayload.rates.tax.value;
    if (addressObject.country === "230" && perLineTaxesEnabled) {
      // If country is United States
      handleUSTaxDetails(itemData, rec, taxValue, eventPayload);
    } else if (addressObject.country !== "230") {
      // If country is anything but the United States
      handleNonUSTaxDetails(itemData, rec, taxValue, eventPayload);
    }
  }

  function handleSquareFtItem(itemData, rec) {
    if (itemData.isSquareFt) {
      /*
       * squareFtWidth
       * squareFtLength
       * */
      log.debug({
        title: "Item Description",
        details: `${itemData.squareFtLength} x ${itemData.squareFtWidth}`,
      });
      rec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "description",
        value: `Size: ${itemData.squareFtLength} x ${itemData.squareFtWidth}
        ${itemData?.attributes.length !== 0 ? `Attributes:\n${itemData.attributes.join("")}` : ""}`, // cart item quantity
      });
      log.debug({
        title: "Item Description Set",
        details: `Size: ${itemData.squareFtLength} x ${itemData.squareFtWidth}`,
      });
    }
  }

  function handleFreightItem(itemData, rec) {
    if (itemData.isFreight) {
      /*
       * Freight weight
       * */
      log.audit({
        title: "🚚 Is Freight Item -",
        details: 'Setting price level to "Custom" (-1) ',
      });
      log.debug({
        title: "Item Description",
        details: `Weight: ${itemData.quantity * 100}\n Quantity: ${
          itemData.quantity
        }`,
      });
      rec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "description",
        value: `Nearest ${itemData.saleUnit}: ${
          itemData.quantity * 100
        }\nWeight Entered: ${itemData.recordedFreightWeight}
        ${itemData?.attributes.length !== 0 ? `Attributes:\n${itemData.attributes.join("")}` : ""}`, // cart item quantity
      });
      log.debug({
        title: "Item Description Set",
        details: `Size: ${itemData.squareFtLength} x ${itemData.squareFtWidth}`,
      });

      rec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "price",
        value: "-1", // Custom price level
      });

      log.audit({
        title: "💵 Price for freight:",
        details: `<html><b>${itemData.price}</b></html>`,
      });

      rec.setCurrentSublistValue({
        sublistId: "item",
        fieldId: "rate",
        value: itemData.price,
      });
    }
  }

  function handleLaborItem(itemData, rec) {
    if (itemData.isLabor) {
      /*
       * ---Write to Desc of order---
       * Service Date: 'selectedDate'
       * Start : {startTime} || End: {endTime}
       * Total Duration: {Number of hours}
       * Workers Requested: {numberOfWorkers}
       * Labor: {price level} for {num of hours in bucket} @ {rate}
       * Supervision Required: {supervisonBool}
       * -------------------------------
       * */
      rec
        .setCurrentSublistValue({
          sublistId: "item",
          fieldId: "price",
          value: "-1", // Custom price level
        })
        .setCurrentSublistValue({
          sublistId: "item",
          fieldId: "rate",
          value: itemData.price,
        });

      log.debug({
        title: "Item Description Of Labor",
        details: `Service Date: ${itemData.quantity * 100}\n Quantity: ${
          itemData.quantity
        }`,
      });
      let laborTableConvertedString = "Labor:";
      if (itemData?.laborTable.length !== 0) {
        itemData.laborTable.forEach((row) => {
          laborTableConvertedString += `\n- ${row.type} for ${row.hours}hrs @ ${row.rate}. Costing: ${row.totalLabor}`;
        });
      }
      // Set Item options
      rec
        .setCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_labor_date",
          value: `${itemData.laborDate?.date}`,
        })
        .setCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_labor_time",
          value: `${itemData.laborStart}`,
        })
        .setCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_labor_end_time",
          value: `${itemData.laborEnd}`,
        })
        .setCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_labor_est_hours",
          value: `${itemData.laborHours}`,
        })
        .setCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_labor_workers",
          value: `${itemData.laborWorkerCount}`,
        })
        .setCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_labor_sup_required",
          value: itemData.supervision,
        })
        .setCurrentSublistValue({
          sublistId: "item",
          fieldId: "description",
          value: `Service Date: ${itemData.laborDate?.date}
Start : ${itemData.laborStart} || End: ${itemData.laborEnd}
Total Duration: ${itemData.laborHours}
Workers Requested: ${itemData.laborWorkerCount}
${laborTableConvertedString}
Supervision Required: ${itemData.supervision}
${
  itemData.supervision
    ? `Supervision Cost: $${roundOff(itemData.supervisionCost, 2)}`
    : ""
}
${itemData?.attributes.length !== 0 ? `Attributes:\n${itemData.attributes.join("")}` : ""}
                                            `, // build Desc for labor.
        });
      log.debug({
        title: "Item Description Set",
        details: `Size: ${itemData.squareFtLength} x ${itemData.squareFtWidth}`,
      });

      if (itemData.supervisionItem) {
        // supervisionAppliedTo = title of labor item.

        rec
          .setCurrentSublistValue({
            sublistId: "item",
            fieldId: "description",
            value: `Applies to: "${itemData.supervisionAppliedTo}"    
Date: ${itemData.laborDate?.date}
Start : ${itemData.laborStart} || End: ${itemData.laborEnd}
Supervision Cost: $${roundOff(itemData.supervisionCost, 2)}
                                            `, // build Desc for labor.
          })
          .setCurrentSublistValue({
            sublistId: "item",
            fieldId: "price",
            value: "-1", // Custom price level
          })
          .setCurrentSublistValue({
            sublistId: "item",
            fieldId: "rate",
            value: itemData.price,
          });
      }
    }
  }

  function handleUSTaxDetails(itemData, rec, taxValue, eventPayload) {
    // Check if settings holds NON Canadian Tax to then set from the CS Event
    // If country is United States
    rec.setCurrentSublistValue({
      sublistId: "item",
      fieldId: "taxcode",
      value: eventPayload.rates.tax.value,
    });

    let taxRate = parseFloat(eventPayload.rates.tax.percent);

    log.audit({
      title: "📃 Tax lines submitted US:",
      details: {
        taxRate,
        taxCode: eventPayload.rates.tax.value,
      },
    });

    rec.setCurrentSublistValue({
      sublistId: "item",
      fieldId: "taxrate1",
      value: taxRate,
    });
  }

  function handleNonUSTaxDetails(itemData, rec, taxValue, eventPayload) {
    // If country is anything but the United States
    rec.setCurrentSublistValue({
      sublistId: "item",
      fieldId: "taxcode",
      value: eventPayload.rates.tax.value,
    });

    let taxRate1 = parseFloat(eventPayload.rates.tax.gst);
    let taxRate2 = parseFloat(eventPayload.rates.tax.pst);

    log.audit({
      title: "📃 Tax lines submitted:",
      details: {
        taxRate1,
        taxRate2,
        taxCode: eventPayload.rates.tax.value,
      },
    });
  }

  function createAddressObject(venueRecord) {
    const state = venueRecord.getValue("custrecord_facility_state");
    const zip = venueRecord.getValue("custrecord_facility_zip");
    const addressOne = venueRecord.getValue("custrecord_facility_address1");
    const addressTwo = venueRecord.getValue("custrecord_facility_address2");
    const city = venueRecord.getValue("custrecord_facility_city");
    const country =
      venueRecord.getValue("custrecord_facility_country") || "230";
    const phone = venueRecord.getValue("custrecord_facility_phone");
    return {
      city,
      zip,
      state,
      addressOne,
      addressTwo,
      country,
      phone,
    };
  }

  /**
   * Calculate Surcharges for the sales order.
   * @param {Object} params
   * @param {number} params.eventId - Event ID
   * @param {Record} params.salesOrder - Sales Order Record
   * @since 2024.11
   */
  function calculateSurcharges(params) {
    const { eventId, salesOrder } = params;

    if (eventId) {
      // The surcharges for the event
      const surchargesForEvent = getSurchargesForEvent(eventId);
      if (surchargesForEvent.length === 0) {
        return;
      }

      log.audit({
        title: "🧮 Calculating Surcharges For Event:",
        details: eventId,
      });

      // Will keep track of the totals for each surcharges type
      // keys of surcharges ids, values of the total amount
      const surchargesTotals = {};

      // Iterate over each line item to calculate the surcharges
      const itemLineCount = salesOrder.getLineCount({
        sublistId: "item",
      });

      for (let i = 0; i < itemLineCount; i++) {
        // Get the item id, the amount and the cust_col_ng_cses_cs_order_serv_chrg which indicates if the line is a surcharges line
        const itemId = salesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "item",
          line: i,
        });

        const amount = salesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "amount",
          line: i,
        });

        const isSurcharge = !!salesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_ng_cses_cs_event_surcharge",
          line: i,
        });

        // loop each surcharge
        surchargesForEvent.forEach((surcharge) => {
          const {
            applied_items,
            charge_type,
            flat_charge,
            id,
            item_id,
            item_type,
            order_importance,
            percentage,
            select_items,
          } = surcharge;

          // If the line is a surcharge line,
          // Or if we marked the id as already being applied, we can skip it
          if (isSurcharge || surchargesTotals?.[id]?.skip) {
            return;
          }

          const applicableCharge =
            select_items === "F" || applied_items.includes(itemId);
          if (applicableCharge) {
            // If the charge type is a percentage
            if (charge_type === 1) {
              const previousTotal = surchargesTotals[id]?.lineTotal || 0;
              surchargesTotals[id] = {
                // The surcharge amount is calculated later by multiplying the lineTotal by the surcharge percentage
                lineTotal: previousTotal + amount,
                orderImportance: order_importance,
                percentage,
                itemId: item_id,
                itemType: item_type,
              };
              // If the charge type is a flat rate
            } else if (charge_type === 2) {
              surchargesTotals[id] = {
                // The surcharge amount for flat charges is just the flat charge
                itemId: item_id,
                itemType: item_type,
                orderImportance: order_importance,
                skip: true,
                total: flat_charge,
              };
            }
          }
        });
      }

      log.audit({
        title: "🧾 Surcharge Totals:",
        details: surchargesTotals,
      });

      // remove surcharge lines if they are in the surcharge totals
      for (let i = itemLineCount - 1; i >= 0; i--) {
        const surcharge = salesOrder.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_ng_cses_cs_event_surcharge",
          line: i,
        });

        if (surchargesTotals[surcharge]) {
          log.audit({
            title: "Removing Surcharge Line to recalculate and add back:",
            details: {
              line: i,
              surcharge,
            },
          });

          salesOrder.removeLine({
            sublistId: "item",
            line: i,
          });
        }
      }

      log.audit({
        title: "Recalculating surcharges and adding back lines",
        details: surchargesTotals,
      });

      // add back surcharge lines that are in the surcharge totals
      Object.keys(surchargesTotals)
        // sorts by order importance low to high with nulls last
        .sort((keyA, keyB) => {
          const sortA = surchargesTotals[keyA].orderImportance;
          const sortB = surchargesTotals[keyB].orderImportance;
          return !sortA ? 1 : !sortB ? -1 : sortA - sortB;
        })
        .forEach((surchargeId) => {
          const { itemId, itemType, total, lineTotal, percentage } =
            surchargesTotals[surchargeId];

          salesOrder.selectNewLine({
            sublistId: "item",
          });

          // Add the item id for the surcharge item
          salesOrder.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "item",
            value: itemId,
          });

          // Set the item type
          salesOrder.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "itemtype",
            value: itemType,
          });

          try {
            // set the quantity
            salesOrder.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "price",
              value: -1, // custom price level
            });
          } catch (e) {
            log.error({
              title: "Error Setting Price",
              details: e,
            });
          }

          try {
            // set the unit price
            salesOrder.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "rate",
              value: total || lineTotal * percentage,
            });
          } catch (e) {
            log.error({
              title: "Error Setting Unit Price",
              details: e,
            });
          }

          // set the amount
          salesOrder.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "amount",
            value: total || lineTotal * percentage,
          });

          // set the surcharge id cust col to indicate that this line is a surcharge line
          salesOrder.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "custcol_ng_cses_cs_event_surcharge",
            value: surchargeId,
          });

          salesOrder.commitLine({
            sublistId: "item",
          });
        });
    }
  }

  /**
   * Fetches the surcharges for the event.
   * @param {number} eventId - The event internal ID
   * @returns {Object[]} - The surcharges for the event
   * @since 2024.10
   */
  const getSurchargesForEvent = (eventId) => {
    try {
      log.audit({
        title: "🔎 Retrieving Surcharges For Event",
        details: eventId,
      });

      const surcharges = query
        .runSuiteQL({
          query: `
            SELECT
              sc.id as id
              , sc.name as name
              , sc.custrecord_ng_cses_surcharge_item as item_id
              , sc.custrecord_ng_cses_surcharge_memo as memo
              , sc.custrecord_ng_cses_order_importance as order_importance
              , sc.custrecord_ng_cses_surcharge_chg_type as charge_type
              , sc.custrecord_ng_cses_surcharge_chg_flat as flat_charge
              , sc.custrecord_ng_cses_surcharge_chg_percent as percentage
              , sc.custrecord_ng_cses_surcharge_slct_items as select_items
              , sc.custrecord_ng_cses_surcharge_appl_items as applied_items
              , itm.itemtype as item_type
            FROM
              customrecord_ng_cses_event_surcharge sc
              JOIN MAP_customrecord_ng_cses_event_surcharge_custrecord_ng_cses_surcharge_actv_evnts AS map ON
                sc.id = map.mapone
              JOIN customrecord_show AS evt ON
                map.maptwo = evt.id
              JOIN item AS itm ON
                sc.custrecord_ng_cses_surcharge_item = itm.id
            WHERE
              evt.id = ${eventId}
              AND sc.custrecord_ng_cses_surcharge_chg_type IS NOT NULL
              AND (
                sc.custrecord_ng_cses_surcharge_chg_flat IS NOT NULL
                OR sc.custrecord_ng_cses_surcharge_chg_percent IS NOT NULL
              )
            ORDER BY
              sc.custrecord_ng_cses_surcharge_default_wiz DESC
          `,
        })
        .asMappedResults();

      log.audit({
        title: "🧾 Retrieved Surcharges",
        details: surcharges,
      });

      return surcharges;
    } catch (e) {
      log.error({
        title: "Error Getting Surcharges",
        details: e,
      });
      return [];
    }
  };

  function getFormattedImageUrl(item) {
    try {
      if (!item.image) return null;
      
      // Check if image is already a full URL
      if (typeof item.image === 'string' && (item.image.startsWith('http') || item.image.startsWith('/'))) {
        return item.image;
      }
      
      // Handle case where image might be in a different format
      if (item.imageUrl) {
        return item.imageUrl;
      }
      
      // Return null if no valid image found
      return null;
    } catch (e) {
      log.error({
        title: 'Error formatting image URL',
        details: JSON.stringify(e)
      });
      return null;
    }
  }

  function validateCartItems(cart, eventId) {
    try {
      log.audit({
        title: "Validating Cart Items",
        details: `Cart: ${JSON.stringify(cart)}, Event ID: ${eventId}`
      });

      const validItems = [];
      const invalidItems = [];
      
      // Early return if no cart items
      if (!cart || !cart.length) {
        throw createError(
          ERROR_TYPES.VALIDATION.MISSING_REQUIRED_FIELD,
          'Cart cannot be empty'
        );
      }

      // Early return if no event ID
      if (!eventId) {
        throw createError(
          ERROR_TYPES.VALIDATION.MISSING_REQUIRED_FIELD,
          'Event ID is required for cart validation'
        );
      }

      // Check each item in the cart
      for (const item of cart) {
        if (!item.collection || !item.collection.id) {
          invalidItems.push({
            item: item,
            reason: 'Missing collection information'
          });
          continue;
        }

        // Search for the collection
        const collectionSearch = search.create({
          type: 'customrecord_ng_cs_item_collection',
          filters: [
            ['internalid', 'is', item.collection.id],
            'AND',
            ['custrecord_ng_cs_itemcoll_event', 'is', eventId],
            'AND',
            ['isinactive', 'is', 'F']
          ],
          columns: [
            search.createColumn({ name: 'custrecord_ng_cs_itemcoll_items' }),
            search.createColumn({ name: 'custrecord_ng_cs_itemcoll_event' }),
            search.createColumn({ name: 'internalid' }),
            search.createColumn({ name: 'custrecord_ng_cs_itemcoll_display_name' }),
          ]
        });

        const collectionSearchResult = collectionSearch.run().getRange({ start: 0, end: 1 });

        // If collection not found or inactive
        if (!collectionSearchResult || collectionSearchResult.length === 0) {
          invalidItems.push({
            item: item,
            reason: `Collection not found or inactive for event ${eventId}`
          });
          continue;
        }

        // Get the collection's items list
        const collectionRecord = collectionSearchResult[0];
        const collectionItems = collectionRecord.getValue({ name: 'custrecord_ng_cs_itemcoll_items' });
        let parsedCollectionItems = [];

        log.debug({
          title: "Collection Items",
          details: collectionItems
        });

        if (collectionItems && typeof collectionItems === 'string') { // This handles if collectionItems has one or many item ID's
          parsedCollectionItems = collectionItems.split(',');
        }

        // Now lets exclude any special item add-ons i.e. Supervision, Item Children
        if (item.supervision) {
          log.audit({
            title: "Excluding Item",
            details: item
          });


          continue;
        }

        const parentProductId = item?.parentProductId
        const itemInternalId = item?.internalid
        const foundItem = parsedCollectionItems.find(id => Number(id) === Number(parentProductId) || Number(id) === Number(itemInternalId))

        log.debug({
          title: "Found Item",
          details: foundItem
        });

        // If collection doesn't have the item
        if (Array.isArray(parsedCollectionItems) && !foundItem) {
          invalidItems.push({
            item: item,
            reason: `Item not found in collection ${item.collection.name}`
          });
          continue;
        } else if (parsedCollectionItems && typeof parsedCollectionItems === 'string' && parsedCollectionItems.indexOf(',') === -1) {
          if (Number(parsedCollectionItems) !== Number(item.internalid) || Number(parsedCollectionItems) !== Number(item?.parentProductId)) {
            invalidItems.push({
              item: item,
              reason: `Item not found in collection ${item.collection.name}`
            });
            continue;
          }
        }

        // Item is valid
        validItems.push(item);
      }

      // If any items are invalid, throw an error
      if (invalidItems.length > 0) {
        throw createError(
          ERROR_TYPES.VALIDATION.INVALID_CART_ITEMS,
          'Some items in your cart are no longer available',
          {
            invalidItems: invalidItems.map(invalid => ({
              id: invalid.item.internalid,
              name: invalid.item.name || invalid.item.itemid,
              reason: invalid.reason,
              // Include collection information
              collection: invalid.item.collection ? {
                id: invalid.item.collection.id,
                name: invalid.item.collection.name || 'Unknown Collection'
              } : null,
              // Additional properties for enhanced UI
              quantity: invalid.item.quantity || 1,
              image: getFormattedImageUrl(invalid.item),
              memo: invalid.item.memo || null,
              squareFtLength: invalid.item.squareFtLength || null,
              squareFtWidth: invalid.item.squareFtWidth || null,
              recordedFreightWeight: invalid.item.recordedFreightWeight || null,
              isFreightItem: !!invalid.item.isFreightItem,
              isLaborItem: !!invalid.item.isLaborItem,
              isSquareFtItem: !!invalid.item.isSquareFtItem
            }))
          }
        );
      }

      // All items valid
      return {
        validItems,
        invalidItems
      };
    } catch (error) {
      log.error({
        title: 'Error validating cart items',
        details: JSON.stringify(error)
      });
      throw error;
    }
  }

  return { get, put, post, delete: doDelete };
});
