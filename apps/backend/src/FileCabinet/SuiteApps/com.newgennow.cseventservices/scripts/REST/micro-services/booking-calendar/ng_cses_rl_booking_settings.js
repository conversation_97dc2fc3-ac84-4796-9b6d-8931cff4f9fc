/**
 * @NApiVersion 2.1
 * @NScriptType Restlet
 * @NAmdConfig ../../amdRestConfig.json
 */
define([
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/url",
  "M/settings",
] /**
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{url} url
 * @param{Object} settings
 * @param{() => Object} settings.useSettings
 */, (query, record, runtime, search, url, settings) => {
  /**
   * Defines the function that is executed when a GET request is sent to a RESTlet.
   * @param {Object} requestParams - Parameters from HTTP request URL; parameters passed as an Object (for all supported
   *     content types)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const get = (requestParams) => {
    const SETTINGS = settings.useSettings();

    // Fetch the list of statuses
    const statusList = query
      .runSuiteQL({
        query: `SELECT * FROM customrecord_ng_cses_booking_status ORDER BY id ASC`,
      })
      .asMappedResults();

    // Fetch the list of events
    const eventList = query
      .runSuiteQL({
        query: `SELECT id, name FROM customrecord_show WHERE isinactive = 'F' AND custrecord_cs_st_show_complete = '2'`,
      })
      .asMappedResults();

    const csBookingRateList = query
      .runSuiteQL({
        query: `SELECT * FROM customrecord_ng_cs_space_booking_rate WHERE isinactive = 'F'`,
      })
      .asMappedResults();

    const csSpacePriceList = query
      .runSuiteQL({
        query: `SELECT * FROM customrecord_ng_cs_space_price_list WHERE isinactive = 'F'`,
      })
      .asMappedResults();

    const csRateTypesList = query
      .runSuiteQL({
        query: `SELECT * FROM customlist_ng_cs_space_rate_type WHERE isinactive = 'F'`,
      })
      .asMappedResults();

    return JSON.stringify({
      ...SETTINGS,
      status: {
        list: statusList,
      },
      event: {
        list: eventList,
      },
      pricing: {
        rates: csBookingRateList,
        list: csSpacePriceList,
      },
      rateTypes: csRateTypesList,
    });
  };

  /**
   * Defines the function that is executed when a PUT request is sent to a RESTlet.
   * @param {string | Object} requestBody - The HTTP request body; request body are passed as a string when request
   *     Content-Type is 'text/plain' or parsed into an Object when request Content-Type is 'application/json' (in which case
   *     the body must be a valid JSON)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const put = (requestBody) => {};

  /**
   * Defines the function that is executed when a POST request is sent to a RESTlet.
   * @param {string | Object} requestBody - The HTTP request body; request body is passed as a string when request
   *     Content-Type is 'text/plain' or parsed into an Object when request Content-Type is 'application/json' (in which case
   *     the body must be a valid JSON)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const post = (requestBody) => {};

  /**
   * Defines the function that is executed when a DELETE request is sent to a RESTlet.
   * @param {Object} requestParams - Parameters from HTTP request URL; parameters are passed as an Object (for all supported
   *     content types)
   * @returns {string | Object} HTTP response body; returns a string when request Content-Type is 'text/plain'; returns an
   *     Object when request Content-Type is 'application/json' or 'application/xml'
   * @since 2015.2
   */
  const doDelete = (requestParams) => {};

  return { get, put, post, delete: doDelete };
});
