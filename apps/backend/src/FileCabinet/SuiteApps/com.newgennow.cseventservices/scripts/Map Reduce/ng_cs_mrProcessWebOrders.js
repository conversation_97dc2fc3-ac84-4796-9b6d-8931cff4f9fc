/**
 * @NApiVersion 2.x
 * @NScriptType MapReduceScript
 * @NModuleScope SameAccount
 * @NAmdConfig ../amdScriptConfig.json
 */

var _COPY_COLUMNS = [
  "custcol_linecode",
  "description",
  "custcol_custom_carpet_size",
  "custcol_cost_is_estimated",
  "custcol_attached_document",
  "custcol_ng_cs_rental_loc",
  "custcol_ng_cs_rental_start_date",
  "custcol_ng_cs_rental_end_date",
];
var _EVENT_FIELDS = [
  "custrecord_ng_cs_event_rental_location",
  "custrecord_cs_st_send_inventory_date",
  "custrecord_cs_st_return_inventory_date",
];

define([
  "N/format",
  "N/https",
  "N/record",
  "../lib/newgen.library.v2",
  "../lib/newgen.library.cs",
  "../lib/newgen.paytrace.lib",
  "../lib/newgen.ri.library.v2",
], function (format, https, record, NG, csLib, PT, RENTAL) {
  /**
   * Marks the beginning of the Map/Reduce process and generates input data.
   *
   * @typedef {Object} ObjectRef
   * @property {number} id - Internal ID of the record instance
   * @property {string} type - Record type id
   *
   * @return {Array|Object|Search|RecordRef} inputSummary
   * @since 2015.1
   */
  function getInputData() {
    var filt = new Array(["mainline", "is", "T"], "and", [
      "custbody_ng_cs_process_web_order",
      "is",
      "T",
    ]);
    var cols = new Array(
      NG.tools.searchColumn({ name: "tranid" }),
      NG.tools.searchColumn({ name: "custbody_ng_cs_web_order_data" })
    );
    var results = NG.tools.getSearchResults("salesorder", filt, cols);
    var data = new Array();
    if (!NG.tools.isEmpty(results)) {
      log.audit({
        title: "There were web orders found for processing",
        details: "{0} orders found".NG_Format(results.length),
      });
      results.forEach(function (res) {
        data.push({
          id: res.id,
          num: res.getValue({ name: "tranid" }),
          encData: res.getValue({ name: "custbody_ng_cs_web_order_data" }),
        });
      });
    } else {
      log.audit({ title: "No web orders found for processing" });
    }

    return data;
  }

  /**
   * Executes when the map entry point is triggered and applies to each key/value pair.
   *
   * @param {MapSummary} context - Data collection containing the key/value pairs to process through the map stage
   * @since 2015.1
   */
  function map(context) {
    var data = JSON.parse(context.value);
    log.audit({
      title: "Processing web order",
      details: "Order #: {0}".NG_Format(data.num),
    });
    var rawDecData = NG.tools.B64.decode(data.encData);
    var webOrdData = JSON.parse(
      rawDecData.substr(0, rawDecData.lastIndexOf("}") + 1)
    );
    var authData = webOrderData.authData;
    var targetOrderId = webOrdData.esoId || data.id;

    if (!NG.tools.isEmpty(webOrdData.esoId)) {
      log.audit({ title: "Handling order consolidation" });
      var soRec = record.load({ type: "salesorder", id: data.id });
      var eSoRec = record.load({
        type: "salesorder",
        id: webOrdData.esoId,
        isDynamic: true,
      });
      var newLines = soRec.getLineCount({ sublistId: "item" });

      try {
        for (var l = 0; l < newLines; l++) {
          eSoRec.selectNewLine({ sublistId: "item" });
          eSoRec.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "item",
            value: soRec.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: l,
            }),
          });
          eSoRec.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "quantity",
            value: soRec.getSublistValue({
              sublistId: "item",
              fieldId: "quantity",
              line: l,
            }),
          });
          eSoRec.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "price",
            value: soRec.getSublistValue({
              sublistId: "item",
              fieldId: "price",
              line: l,
            }),
          });
          if (
            soRec.getSublistValue({
              sublistId: "item",
              fieldId: "price",
              line: l,
            }) == "-1"
          ) {
            eSoRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "rate",
              value: soRec.getSublistValue({
                sublistId: "item",
                fieldId: "rate",
                line: l,
              }),
            });
            eSoRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: "amount",
              value: soRec.getSublistValue({
                sublistId: "item",
                fieldId: "amount",
                line: l,
              }),
            });
          }
          eSoRec.setCurrentSublistValue({
            sublistId: "item",
            fieldId: "options",
            value: soRec.getSublistValue({
              sublistId: "item",
              fieldId: "options",
              line: l,
            }),
          });
          _COPY_COLUMNS.forEach(function (col) {
            eSoRec.setCurrentSublistValue({
              sublistId: "item",
              fieldId: col,
              value: soRec.getSublistValue({
                sublistId: "item",
                fieldId: col,
                line: l,
              }),
            });
          });
          eSoRec.commitLine({ sublistId: "item" });
          log.audit({
            title:
              "new order line {0} has been added to existing order".NG_Format(
                l
              ),
          });
        }

        eSoRec.setValue({
          fieldId: "custbody_balance",
          value:
            new Number(eSoRec.getValue({ fieldId: "total" })) -
            new Number(eSoRec.getValue({ fieldId: "custbody_total_paid" })),
        });
        eSoRec.setValue({ fieldId: "paymentmethod", value: "" });
        eSoRec.setValue({ fieldId: "ccnumber", value: "" });
        eSoRec.setValue({ fieldId: "ccexpiredate", value: "" });
        eSoRec.setValue({ fieldId: "ccname", value: "" });
        eSoRec.setValue({ fieldId: "ccstreet", value: "" });
        eSoRec.setValue({ fieldId: "cczipcode", value: "" });

        var isAltPay = !_tools.isEmpty(authData.cCard.name)
          ? authData.cCard.name.search("ACH:") >= 0 ||
            authData.cCard.name.search("Check:") >= 0 ||
            authData.cCard.name.search("Wire Transfer:") >= 0
          : false;
        if (isAltPay) {
          if (
            NG.tools.isEmpty(
              eSoRec.getValue({ fieldId: "custbody_payment_reference" })
            )
          ) {
            eSoRec.setValue({
              fieldId: "custbody_payment_reference",
              value: authData.cCard.name,
            });
          } else {
            var payRefNum = "{0}\r\n{1}".NG_Format(
              eSoRec.getValue({ fieldId: "custbody_payment_reference" }),
              authData.cCard.name
            );
            eSoRec.setValue({
              fieldId: "custbody_payment_reference",
              value: payRefNum,
            });
          }
        }
        var newSaveSuccess = false;
        try {
          eSoRec.save({ enableSourcing: true, ignoreMandatoryFields: true });
          newSaveSuccess = true;
        } catch (err) {
          NG.log.logError(err, "Error encountered updating existing order");
        }

        if (newSaveSuccess) {
          try {
            record.submitFields({
              type: "salesorder",
              id: data.id,
              values: {
                custbody_to_be_deleted: true,
                custbody_ng_cs_process_web_order: false,
                custbody_ng_cs_web_order_data: "",
              },
              options: { enableSourcing: false, ignoreMandatoryFields: true },
            });
          } catch (err) {
            NG.log.logError(
              err,
              "Error encountered flagging new web order for deletion"
            );
          }
        }
      } catch (err) {
        NG.log.logError(
          err,
          "Error encountered copying lines from new web order to existing order"
        );
      }
    } else {
      log.audit({ title: "Handling initial booth order" });
      var soRec = record.load({ type: "salesorder", id: data.id });
      var eventId = soRec.getValue({ fieldId: "custbody_show_table" });
      var boothId = soRec.getValue({ fieldId: "custbody_booth" });
      var pushData = {
        paymentmethod: "",
        ccnumber: "",
        ccexpiredate: "",
        ccname: "",
        ccstreet: "",
        cczipcode: "",
        custbody_ng_cs_process_web_order: false,
        custbody_ng_cs_web_order_data: "",
      };
      if (!NG.tools.isEmpty(csLib.settings.DefaultBoothOrderForm)) {
        pushData["customform"] = csLib.settings.DefaultBoothOrderForm;
      }
      if (isAltPay) {
        pushData["custbody_payment_reference"] = authData.cCard.name;
      }

      try {
        var lookups = [
          "custbody_ng_cs_order_type",
          "class",
          "custbody_booth_actual_exhibitor",
        ];
        if (csLib.settings.UseCustomJob) {
          lookups.push("custbody_cseg_ng_cs_job");
        }
        var orderInfo = nlapiLookupField("salesorder", data.id, lookups);
        log.audit({ title: "orderInfo", details: JSON.stringify(orderInfo) });
        if (NG.tools.isEmpty(orderInfo["custbody_ng_cs_order_type"])) {
          pushData["custbody_ng_cs_order_type"] =
            csLib.settings.DefaultExhibitorOrderType;
        }
        if (
          NG.tools.isEmpty(orderInfo["class"]) ||
          NG.tools.isEmpty(orderInfo["custbody_cseg_ng_cs_job"])
        ) {
          if (!NG.tools.isEmpty(eventId)) {
            var tData = NG.tools.getLookupFields(
              "customrecord_show",
              eventId,
              ["custrecord_fin_show", "custrecord_show_job"],
              ["custrecord_fin_show", "custrecord_show_job"],
              []
            );
            if (
              NG.tools.isEmpty(orderInfo["class"]) &&
              !NG.tools.isEmpty(tData["custrecord_fin_show"])
            ) {
              pushData["class"] = tData["custrecord_fin_show"];
            }
            if (
              csLib.settings.UseCustomJob &&
              NG.tools.isEmpty(orderInfo["custbody_cseg_ng_cs_job"]) &&
              !NG.tools.isEmpty(tData["custrecord_show_job"])
            ) {
              pushData["custbody_cseg_ng_cs_job"] =
                tData["custrecord_show_job"];
            }
          }
        }
        if (
          NG.tools.isEmpty(orderInfo["custbody_booth_actual_exhibitor"]) &&
          !NG.tools.isEmpty(boothId)
        ) {
          try {
            var boothData = NG.tools.getLookupFields(
              "customrecord_show_booths",
              boothId,
              ["custrecord_booth_actual_exhibitor"],
              ["custrecord_booth_actual_exhibitor"],
              []
            );
            if (
              !NG.tools.isEmpty(boothData.custrecord_booth_actual_exhibitor)
            ) {
              pushData["custbody_booth_actual_exhibitor"] =
                boothData.custrecord_booth_actual_exhibitor;
            }
          } catch (err) {
            NG.log.logError(
              err,
              'Error encountered handling missing "Actual Exhibitor" value on order'
            );
          }
        }
      } catch (err) {
        NG.log.logError(err, "Error encountered validating booth order fields");
      }

      try {
        record.submitFields({
          type: "salesorder",
          id: data.id,
          values: pushData,
          options: { enableSourcing: false, ignoreMandatoryFields: true },
        });
      } catch (err) {
        NG.log.logError(
          err,
          "Error encountered updating web order with default custom form ID"
        );
      }
    }

    if (csLib.settings.EnableRentals) {
      log.audit({ title: "Validating rental data" });
      var order = record.load({
        type: "salesorder",
        id: targetOrderId,
        isDynamic: true,
      });
      var lineCount = order.getLineCount({ sublistId: "item" });
      var today = new Date();
      var todayTest = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        0,
        0,
        0,
        0
      );
      var updRentals = false;

      for (var l = 0; l < lineCount; l++) {
        var rLoc = order.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_ng_cs_rental_loc",
          line: l,
        });
        var rDtS = order.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_ng_cs_rental_start_date",
          line: l,
        });
        var rDtE = order.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_ng_cs_rental_end_date",
          line: l,
        });
        var aDte = order.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_add_date",
          line: l,
        });
        if (!util.isDate({ obj: aDte })) {
          aDte = format.parse({ value: aDte, type: format.Type.DATE });
        }

        var emptyRental =
          NG.tools.isEmpty(rLoc) ||
          NG.tools.isEmpty(rDtS) ||
          NG.tools.isEmpty(rDtE);
        var addTest = new Date(
          aDte.getFullYear(),
          aDte.getMonth(),
          aDte.getDate(),
          0,
          0,
          0,
          0
        );
        var addedToday = todayTest.getTime() - addTest.getTime() == 0;

        if (emptyRental && addedToday) {
          updRentals = true;
          break;
        }
      }

      if (updRentals) {
        try {
          var eventId = order.getValue({ fieldId: "custbody_show_table" });
          var eventData = NG.tools.getLookupFields(
            "customrecord_show",
            eventId,
            _EVENT_FIELDS,
            ["custrecord_ng_cs_event_rental_location"],
            []
          );

          var items = new Array();
          for (var l = 0; l < lineCount({ sublistId: "item" }); l++) {
            var rItem = order.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: l,
            });
            var rItemType = order.getSublistValue({
              sublistId: "item",
              fieldId: "itemtype",
              line: l,
            });
            var rLoc = order.getSublistValue({
              sublistId: "item",
              fieldId: "custcol_ng_cs_rental_loc",
              line: l,
            });
            var rDtS = order.getSublistValue({
              sublistId: "item",
              fieldId: "custcol_ng_cs_rental_start_date",
              line: l,
            });
            var rDtE = order.getSublistValue({
              sublistId: "item",
              fieldId: "custcol_ng_cs_rental_end_date",
              line: l,
            });
            var aDte = order.getSublistValue({
              sublistId: "item",
              fieldId: "custcol_add_date",
              line: l,
            });
            if (!util.isDate({ obj: aDte })) {
              aDte = format.parse({ value: aDte, type: format.Type.DATE });
            }

            var emptyRental =
              NG.tools.isEmpty(rLoc) ||
              NG.tools.isEmpty(rDtS) ||
              NG.tools.isEmpty(rDtE);
            var addTest = new Date(
              aDte.getFullYear(),
              aDte.getMonth(),
              aDte.getDate(),
              0,
              0,
              0,
              0
            );
            var addedToday = todayTest.getTime() - addTest.getTime() == 0;

            if (rItemType == "NonInvtPart" && addedToday && emptyRental) {
              items.push(rItem);
            }
          }

          if (items.length > 0) {
            var avlData = RENTAL.getRentalAvailability(
              items,
              eventData["custrecord_ng_cs_event_rental_location"]
            );
            if (!NG.tools.isEmpty(avlData)) {
              for (
                var l = 0;
                l < order.getLineCount({ sublistId: "item" });
                l++
              ) {
                var rItem = order.getSublistValue({
                  sublistId: "item",
                  fieldId: "item",
                  line: l,
                });
                var rItemType = order.getSublistValue({
                  sublistId: "item",
                  fieldId: "itemtype",
                  line: l,
                });
                var rLoc = order.getSublistValue({
                  sublistId: "item",
                  fieldId: "custcol_ng_cs_rental_loc",
                  line: l,
                });
                var rDtS = order.getSublistValue({
                  sublistId: "item",
                  fieldId: "custcol_ng_cs_rental_start_date",
                  line: l,
                });
                var rDtE = order.getSublistValue({
                  sublistId: "item",
                  fieldId: "custcol_ng_cs_rental_end_date",
                  line: l,
                });
                var aDte = order.getSublistValue({
                  sublistId: "item",
                  fieldId: "custcol_add_date",
                  line: l,
                });
                if (!util.isDate({ obj: aDte })) {
                  aDte = format.parse({ value: aDte, type: format.Type.DATE });
                }

                var emptyRental =
                  NG.tools.isEmpty(rLoc) ||
                  NG.tools.isEmpty(rDtS) ||
                  NG.tools.isEmpty(rDtE);
                var addTest = new Date(
                  aDte.getFullYear(),
                  aDte.getMonth(),
                  aDte.getDate(),
                  0,
                  0,
                  0,
                  0
                );
                var addedToday = todayTest.getTime() - addTest.getTime() == 0;

                if (rItemType == "NonInvtPart" && addedToday && emptyRental) {
                  if (!NG.tools.isEmpty(avlData[rItem])) {
                    if (
                      !NG.tools.isEmpty(
                        avlData[rItem][
                          eventData["custrecord_ng_cs_event_rental_location"]
                        ]
                      )
                    ) {
                      if (
                        !NG.tools.isEmpty(
                          eventData["custrecord_cs_st_send_inventory_date"]
                        )
                      ) {
                        order.setSublistValue({
                          sublistId: "item",
                          fieldId: "custcol_ng_cs_rental_start_date",
                          value: format.parse({
                            value:
                              eventData["custrecord_cs_st_send_inventory_date"],
                            type: format.Type.DATE,
                          }),
                          line: l,
                        });
                      }
                      if (
                        !NG.tools.isEmpty(
                          eventData["custrecord_cs_st_return_inventory_date"]
                        )
                      ) {
                        order.setSublistValue({
                          sublistId: "item",
                          fieldId: "custcol_ng_cs_rental_end_date",
                          value: format.parse({
                            value:
                              eventData[
                                "custrecord_cs_st_return_inventory_date"
                              ],
                            type: format.Type.DATE,
                          }),
                          line: l,
                        });
                      }
                    }
                  }
                }
              }
            }

            order.save({ enableSourcing: true, ignoreMandatoryFields: true });
          }
        } catch (err) {
          NG.log.logError(
            err,
            "Error encountered updating rental data on web order"
          );
        }
      }
    }

    if (!NG.tools.isEmpty(authData)) {
      log.audit({ title: "Capturing authorized payment", details: authData });
      try {
        https.post({
          url: webOrdData.payPath,
          body: {
            soid: targetOrderId,
            total: authData.totalPaid,
            capture: "T",
            ptId: authData.ptTranId,
            refRecId: authData.refRecId,
            conv: authData.convFee,
            cCard: JSON.stringify(authData.cCard),
          },
        });
      } catch (err) {
        NG.log.logError(err, "Error encountered capturing payment");
      }
    }
  }

  /**
   * Executes when the reduce entry point is triggered and applies to each group.
   *
   * @param {ReduceSummary} context - Data collection containing the groups to process through the reduce stage
   * @since 2015.1
   */
  /*function reduce(context) {
		
	}*/

  /**
   * Executes when the summarize entry point is triggered and applies to the result set.
   *
   * @param {Summary} summary - Holds statistics regarding the execution of a map/reduce script
   * @since 2015.1
   */
  function summarize(summary) {}

  return {
    getInputData: getInputData,
    map: map,
    //	reduce: reduce,
    summarize: summarize,
  };
});
