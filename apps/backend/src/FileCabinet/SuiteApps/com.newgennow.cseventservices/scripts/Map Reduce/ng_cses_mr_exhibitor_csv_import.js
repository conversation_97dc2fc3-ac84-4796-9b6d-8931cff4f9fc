/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAMDConfig ./amdMapReduceConfig.json
 */
define([
  "N/crypto",
  "N/file",
  "N/record",
  "N/search",
  "N/runtime",
  "lodash",
  "settings",
  "N/email",
], /**
 * @param{crypto} crypto
 * @param{file} file
 * @param{record} record
 * @param{search} search
 * @param{runtime} runtime
 * @param{lodash} lodash
 * @param{settings} settings
 * @param{email} email
 */ (crypto, file, record, search, runtime, lodash, settings, email) => {
  /**
   * Defines the function that is executed at the beginning of the map/reduce process and generates the input data.
   * @param {Object} inputContext
   * @param {boolean} inputContext.isRestarted - Indicates whether the current invocation of this function is the first
   *     invocation (if true, the current invocation is not the first invocation and this function has been restarted)
   * @param {Object} inputContext.ObjectRef - Object that references the input data
   * @typedef {Object} ObjectRef
   * @property {string|number} ObjectRef.id - Internal ID of the record instance that contains the input data
   * @property {string} ObjectRef.type - Type of the record instance that contains the input data
   * @returns {Array|Object|Search|ObjectRef|File|Query} The input data to use in the map/reduce process
   * @since 2015.2
   */

  var countries = [
    { value: "AF", text: "Afghanistan" },
    { value: "AX", text: "Aland Islands" },
    { value: "AL", text: "Albania" },
    { value: "DZ", text: "Algeria" },
    { value: "AS", text: "American Samoa" },
    { value: "AD", text: "Andorra" },
    { value: "AO", text: "Angola" },
    { value: "AI", text: "Anguilla" },
    { value: "AQ", text: "Antarctica" },
    { value: "AG", text: "Antigua and Barbuda" },
    { value: "AR", text: "Argentina" },
    { value: "AM", text: "Armenia" },
    { value: "AW", text: "Aruba" },
    { value: "AU", text: "Australia" },
    { value: "AT", text: "Austria" },
    { value: "AZ", text: "Azerbaijan" },
    { value: "BS", text: "Bahamas" },
    { value: "BH", text: "Bahrain" },
    { value: "BD", text: "Bangladesh" },
    { value: "BB", text: "Barbados" },
    { value: "BY", text: "Belarus" },
    { value: "BE", text: "Belgium" },
    { value: "BZ", text: "Belize" },
    { value: "BJ", text: "Benin" },
    { value: "BM", text: "Bermuda" },
    { value: "BT", text: "Bhutan" },
    { value: "BO", text: "Bolivia" },
    { value: "BQ", text: "Bonaire, Saint Eustatius and Saba" },
    { value: "BA", text: "Bosnia and Herzegovina" },
    { value: "BW", text: "Botswana" },
    { value: "BV", text: "Bouvet Island" },
    { value: "BR", text: "Brazil" },
    { value: "IO", text: "British Indian Ocean Territory" },
    { value: "BN", text: "Brunei Darussalam" },
    { value: "BG", text: "Bulgaria" },
    { value: "BF", text: "Burkina Faso" },
    { value: "BI", text: "Burundi" },
    { value: "KH", text: "Cambodia" },
    { value: "CM", text: "Cameroon" },
    { value: "CA", text: "Canada" },
    { value: "IC", text: "Canary Islands" },
    { value: "CV", text: "Cape Verde" },
    { value: "KY", text: "Cayman Islands" },
    { value: "CF", text: "Central African Republic" },
    { value: "EA", text: "Ceuta and Melilla" },
    { value: "TD", text: "Chad" },
    { value: "CL", text: "Chile" },
    { value: "CN", text: "China" },
    { value: "CX", text: "Christmas Island" },
    { value: "CC", text: "Cocos (Keeling) Islands" },
    { value: "CO", text: "Colombia" },
    { value: "KM", text: "Comoros" },
    { value: "CD", text: "Congo, Democratic Republic of" },
    { value: "CG", text: "Congo, Republic of" },
    { value: "CK", text: "Cook Islands" },
    { value: "CR", text: "Costa Rica" },
    { value: "CI", text: "Cote d'Ivoire" },
    { value: "HR", text: "Croatia/Hrvatska" },
    { value: "CU", text: "Cuba" },
    { value: "CW", text: "Cura�ao" },
    { value: "CY", text: "Cyprus" },
    { value: "CZ", text: "Czech Republic" },
    { value: "DK", text: "Denmark" },
    { value: "DJ", text: "Djibouti" },
    { value: "DM", text: "Dominica" },
    { value: "DO", text: "Dominican Republic" },
    { value: "TL", text: "East Timor" },
    { value: "EC", text: "Ecuador" },
    { value: "EG", text: "Egypt" },
    { value: "SV", text: "El Salvador" },
    { value: "GQ", text: "Equatorial Guinea" },
    { value: "ER", text: "Eritrea" },
    { value: "EE", text: "Estonia" },
    { value: "ET", text: "Ethiopia" },
    { value: "FK", text: "Falkland Islands" },
    { value: "FO", text: "Faroe Islands" },
    { value: "FJ", text: "Fiji" },
    { value: "FI", text: "Finland" },
    { value: "FR", text: "France" },
    { value: "GF", text: "French Guiana" },
    { value: "PF", text: "French Polynesia" },
    { value: "TF", text: "French Southern Territories" },
    { value: "GA", text: "Gabon" },
    { value: "GM", text: "Gambia" },
    { value: "GE", text: "Georgia" },
    { value: "DE", text: "Germany" },
    { value: "GH", text: "Ghana" },
    { value: "GI", text: "Gibraltar" },
    { value: "GR", text: "Greece" },
    { value: "GL", text: "Greenland" },
    { value: "GD", text: "Grenada" },
    { value: "GP", text: "Guadeloupe" },
    { value: "GU", text: "Guam" },
    { value: "GT", text: "Guatemala" },
    { value: "GG", text: "Guernsey" },
    { value: "GN", text: "Guinea" },
    { value: "GW", text: "Guinea-Bissau" },
    { value: "GY", text: "Guyana" },
    { value: "HT", text: "Haiti" },
    { value: "HM", text: "Heard and McDonald Islands" },
    { value: "VA", text: "Holy See (City Vatican State)" },
    { value: "HN", text: "Honduras" },
    { value: "HK", text: "Hong Kong" },
    { value: "HU", text: "Hungary" },
    { value: "IS", text: "Iceland" },
    { value: "IN", text: "India" },
    { value: "ID", text: "Indonesia" },
    { value: "IR", text: "Iran (Islamic Republic of)" },
    { value: "IQ", text: "Iraq" },
    { value: "IE", text: "Ireland" },
    { value: "IM", text: "Isle of Man" },
    { value: "IL", text: "Israel" },
    { value: "IT", text: "Italy" },
    { value: "JM", text: "Jamaica" },
    { value: "JP", text: "Japan" },
    { value: "JE", text: "Jersey" },
    { value: "JO", text: "Jordan" },
    { value: "KZ", text: "Kazakhstan" },
    { value: "KE", text: "Kenya" },
    { value: "KI", text: "Kiribati" },
    { value: "KP", text: "Korea, Democratic People's Republic" },
    { value: "KR", text: "Korea, Republic of" },
    { value: "XK", text: "Kosovo" },
    { value: "KW", text: "Kuwait" },
    { value: "KG", text: "Kyrgyzstan" },
    { value: "LA", text: "Lao People's Democratic Republic" },
    { value: "LV", text: "Latvia" },
    { value: "LB", text: "Lebanon" },
    { value: "LS", text: "Lesotho" },
    { value: "LR", text: "Liberia" },
    { value: "LY", text: "Libya" },
    { value: "LI", text: "Liechtenstein" },
    { value: "LT", text: "Lithuania" },
    { value: "LU", text: "Luxembourg" },
    { value: "MO", text: "Macau" },
    { value: "MK", text: "Macedonia" },
    { value: "MG", text: "Madagascar" },
    { value: "MW", text: "Malawi" },
    { value: "MY", text: "Malaysia" },
    { value: "MV", text: "Maldives" },
    { value: "ML", text: "Mali" },
    { value: "MT", text: "Malta" },
    { value: "MH", text: "Marshall Islands" },
    { value: "MQ", text: "Martinique" },
    { value: "MR", text: "Mauritania" },
    { value: "MU", text: "Mauritius" },
    { value: "YT", text: "Mayotte" },
    { value: "MX", text: "Mexico" },
    { value: "FM", text: "Micronesia, Federal State of" },
    { value: "MD", text: "Moldova, Republic of" },
    { value: "MC", text: "Monaco" },
    { value: "MN", text: "Mongolia" },
    { value: "ME", text: "Montenegro" },
    { value: "MS", text: "Montserrat" },
    { value: "MA", text: "Morocco" },
    { value: "MZ", text: "Mozambique" },
    { value: "MM", text: "Myanmar (Burma)" },
    { value: "NA", text: "Namibia" },
    { value: "NR", text: "Nauru" },
    { value: "NP", text: "Nepal" },
    { value: "NL", text: "Netherlands" },
    { value: "AN", text: "Netherlands Antilles (Deprecated)" },
    { value: "NC", text: "New Caledonia" },
    { value: "NZ", text: "New Zealand" },
    { value: "NI", text: "Nicaragua" },
    { value: "NE", text: "Niger" },
    { value: "NG", text: "Nigeria" },
    { value: "NU", text: "Niue" },
    { value: "NF", text: "Norfolk Island" },
    { value: "MP", text: "Northern Mariana Islands" },
    { value: "NO", text: "Norway" },
    { value: "OM", text: "Oman" },
    { value: "PK", text: "Pakistan" },
    { value: "PW", text: "Palau" },
    { value: "PS", text: "Palestinian Territories" },
    { value: "PA", text: "Panama" },
    { value: "PG", text: "Papua New Guinea" },
    { value: "PY", text: "Paraguay" },
    { value: "PE", text: "Peru" },
    { value: "PH", text: "Philippines" },
    { value: "PN", text: "Pitcairn Island" },
    { value: "PL", text: "Poland" },
    { value: "PT", text: "Portugal" },
    { value: "PR", text: "Puerto Rico" },
    { value: "QA", text: "Qatar" },
    { value: "RE", text: "Reunion Island" },
    { value: "RO", text: "Romania" },
    { value: "RU", text: "Russian Federation" },
    { value: "RW", text: "Rwanda" },
    { value: "BL", text: "Saint Barth�lemy" },
    { value: "SH", text: "Saint Helena" },
    { value: "KN", text: "Saint Kitts and Nevis" },
    { value: "LC", text: "Saint Lucia" },
    { value: "MF", text: "Saint Martin" },
    { value: "VC", text: "Saint Vincent and the Grenadines" },
    { value: "WS", text: "Samoa" },
    { value: "SM", text: "San Marino" },
    { value: "ST", text: "Sao Tome and Principe" },
    { value: "SA", text: "Saudi Arabia" },
    { value: "SN", text: "Senegal" },
    { value: "RS", text: "Serbia" },
    { value: "CS", text: "Serbia and Montenegro (Deprecated)" },
    { value: "SC", text: "Seychelles" },
    { value: "SL", text: "Sierra Leone" },
    { value: "SG", text: "Singapore" },
    { value: "SX", text: "Sint Maarten" },
    { value: "SK", text: "Slovak Republic" },
    { value: "SI", text: "Slovenia" },
    { value: "SB", text: "Solomon Islands" },
    { value: "SO", text: "Somalia" },
    { value: "ZA", text: "South Africa" },
    { value: "GS", text: "South Georgia" },
    { value: "SS", text: "South Sudan" },
    { value: "ES", text: "Spain" },
    { value: "LK", text: "Sri Lanka" },
    { value: "PM", text: "St. Pierre and Miquelon" },
    { value: "SD", text: "Sudan" },
    { value: "SR", text: "Suriname" },
    { value: "SJ", text: "Svalbard and Jan Mayen Islands" },
    { value: "SZ", text: "Swaziland" },
    { value: "SE", text: "Sweden" },
    { value: "CH", text: "Switzerland" },
    { value: "SY", text: "Syrian Arab Republic" },
    { value: "TW", text: "Taiwan" },
    { value: "TJ", text: "Tajikistan" },
    { value: "TZ", text: "Tanzania" },
    { value: "TH", text: "Thailand" },
    { value: "TG", text: "Togo" },
    { value: "TK", text: "Tokelau" },
    { value: "TO", text: "Tonga" },
    { value: "TT", text: "Trinidad and Tobago" },
    { value: "TN", text: "Tunisia" },
    { value: "TR", text: "Turkey" },
    { value: "TM", text: "Turkmenistan" },
    { value: "TC", text: "Turks and Caicos Islands" },
    { value: "TV", text: "Tuvalu" },
    { value: "UG", text: "Uganda" },
    { value: "UA", text: "Ukraine" },
    { value: "AE", text: "United Arab Emirates" },
    { value: "GB", text: "United Kingdom" },
    { value: "US", text: "United States" },
    { value: "UY", text: "Uruguay" },
    { value: "UM", text: "US Minor Outlying Islands" },
    { value: "UZ", text: "Uzbekistan" },
    { value: "VU", text: "Vanuatu" },
    { value: "VE", text: "Venezuela" },
    { value: "VN", text: "Vietnam" },
    { value: "VG", text: "Virgin Islands (British)" },
    { value: "VI", text: "Virgin Islands (USA)" },
    { value: "WF", text: "Wallis and Futuna" },
    { value: "EH", text: "Western Sahara" },
    { value: "YE", text: "Yemen" },
    { value: "ZM", text: "Zambia" },
    { value: "ZW", text: "Zimbabwe" },
  ];

  const getInputData = (inputContext) => {
    let scriptObj = runtime.getCurrentScript();
    let fileId = scriptObj.getParameter({
      name: "custscript_ng_cs_impt_file_id_mr",
    });

    log.debug("fileId", fileId);

    let csvFile = file.load({
      id: fileId,
    });
    let contents = csvFile.getContents();
    let fileName = csvFile.name;

    let lines = csvToArray(contents, ",", fileName);

    log.debug("fileName", fileName);
    log.debug("contents", contents);
    log.debug("lines", lines);

    if (Array.isArray(lines)) {
      lines.shift()
    }

    return lines;

    function csvToArray(strData, strDelimiter, name) {
      strDelimiter = strDelimiter || ",";

      var objPattern = new RegExp(
          "(\\" +
          strDelimiter +
          "|\\r?\\n|\\r|^)" +
          // Quoted fields.
          '(?:"([^"]*(?:""[^"]*)*)"|' +
          // Standard fields.
          '([^"\\' +
          strDelimiter +
          "\\r\\n]*))",
          "gi"
      );

      var arrData = [[]];
      var arrMatches = null;
      var lineNum = 2;

      while ((arrMatches = objPattern.exec(strData))) {
        var strMatchedDelimiter = arrMatches[1];

        if (
            strMatchedDelimiter.length &&
            strMatchedDelimiter !== strDelimiter
        ) {
          arrData.push([lineNum, name]);
          lineNum++;
        }

        var strMatchedValue;

        if (arrMatches[2]) {
          strMatchedValue = arrMatches[2].replace(new RegExp('""', "g"), '"');
        } else {
          strMatchedValue = arrMatches[3];
        }
        arrData[arrData.length - 1].push(strMatchedValue);
      }
      return arrData;
    }
  };

  /**
   * Defines the function that is executed when the map entry point is triggered. This entry point is triggered automatically
   * when the associated getInputData stage is complete. This function is applied to each key-value pair in the provided
   * context.
   * @param {Object} mapContext - Data collection containing the key-value pairs to process in the map stage. This parameter
   *     is provided automatically based on the results of the getInputData stage.
   * @param {Iterator} mapContext.errors - Serialized errors that were thrown during previous attempts to execute the map
   *     function on the current key-value pair
   * @param {number} mapContext.executionNo - Number of times the map function has been executed on the current key-value
   *     pair
   * @param {boolean} mapContext.isRestarted - Indicates whether the current invocation of this function is the first
   *     invocation (if true, the current invocation is not the first invocation and this function has been restarted)
   * @param {string} mapContext.key - Key to be processed during the map stage
   * @param {string} mapContext.value - Value to be processed during the map stage
   * @since 2015.2
   */

  const map = (mapContext) => {
    let results = mapContext.value;
    log.debug("results", results);

    mapContext.write({
      key: results,
      value: 1,
    });
  };

  /**
   * Defines the function that is executed when the reduce entry point is triggered. This entry point is triggered
   * automatically when the associated map stage is complete. This function is applied to each group in the provided context.
   * @param {Object} reduceContext - Data collection containing the groups to process in the reduce stage. This parameter is
   *     provided automatically based on the results of the map stage.
   * @param {Iterator} reduceContext.errors - Serialized errors that were thrown during previous attempts to execute the
   *     reduce function on the current group
   * @param {number} reduceContext.executionNo - Number of times the reduce function has been executed on the current group
   * @param {boolean} reduceContext.isRestarted - Indicates whether the current invocation of this function is the first
   *     invocation (if true, the current invocation is not the first invocation and this function has been restarted)
   * @param {string} reduceContext.key - Key to be processed during the reduce stage
   * @param {List<String>} reduceContext.values - All values associated with a unique key that was passed to the reduce stage
   *     for processing
   * @since 2015.2
   */
  const reduce = (reduceContext) => {
    log.debug("Reduce Running", reduceContext);
    let scriptObj = runtime.getCurrentScript();
    let useSubsidiaries = runtime.isFeatureInEffect({
      feature: "SUBSIDIARIES",
    });
    let useLocations = runtime.isFeatureInEffect({
      feature: "LOCATIONS",
    });
    let multiSubsidiaryCustomer = runtime.isFeatureInEffect({
      feature: "MULTISUBSIDIARYCUSTOMER",
    });
    let csSettings = settings.useSettings();
    // log.debug("csSettings", csSettings);
    let fileId = scriptObj.getParameter({
      name: "custscript_ng_cs_impt_file_id_mr",
    });
    let showTableIDAndImportIDObj = scriptObj.getParameter({
      name: "custscript_ng_cs_impt_show_table_id_mr",
    });
    log.debug("showTableID", showTableIDAndImportIDObj);
    log.debug("useSubsidiaries", useSubsidiaries);

    let parsedObj = JSON.parse(showTableIDAndImportIDObj);
    log.debug("parsedObj", parsedObj);

    let importId = parsedObj.importId;
    let showTableID = parsedObj.show;
    log.debug("importId", importId);
    log.debug("showTableID", showTableID);
    // let importID = scriptObj.getParameter({
    //   name: "custscript_ng_cs_impt_import_id_mr",
    // });
    let lines = JSON.parse(reduceContext.key);

    log.debug("Reduce Lines", lines);
    // log.debug("lines[0", lines[l]);
    let line = lines[0];
    let fileName = lines[1];
    let boothNum = lines[2] && lines[2].trim();
    let companyName = lines[3] && lines[3].trim();
    let address = lines[4] && lines[4].trim();
    let address2 = lines[5] && lines[5].trim();
    let city = lines[6] && lines[6].trim();
    let state = lines[7] && lines[7].trim();
    let zip = lines[8] && lines[8].trim();
    let country = lines[9] && lines[9].trim();
    let phone = lines[10] && lines[10].trim();
    let fax = lines[11] && lines[11].trim();
    let contactName = lines[12] && lines[12].trim();
    let email = lines[13] && lines[13].trim();
    let boothLength = lines[14] && lines[14].trim();
    let boothWidth = lines[15] && lines[15].trim();
    let boothPavilion = lines[17] && lines[17].trim();
    let boothLocation = lines[18] && lines[18].trim();
    let boothType = lines[19] && lines[19].trim();
    let adtlBooths = "";
    try {
      adtlBooths = lines[16] && lines[16].trim().replace(/'/g, "");
    } catch (err) {
      log.debug("Error adding aditional booths", err);
    }

    for (let i = 0; i < countries.length; i++) {
      if (countries[i].text === country) {
        country = countries[i].value;
      }
    }

    log.debug("Result Obj", {
      line: line,
      fileName: fileName,
      boothNum: boothNum,
      companyName: companyName,
      address: address,
      address2: address2,
      city: city,
      state: state,
      zip: zip,
      country: country,
      phone: phone,
      fax: fax,
      contactName: contactName,
      email: email,
      boothLength: boothLength,
      boothWidth: boothWidth,
      boothPavilion: boothPavilion,
      boothLocation: boothLocation,
      boothType: boothType,
    });

    let msg = null;
    let status = null;
    let errCode = null;
    let errDesc = null;
    let logContact = null;
    let logCompany = null;
    let logBooth = null;

    let logObj = {
      showTableID: showTableID,
      importID: importId,
      fileName: fileName,
      line: line,
    };

    log.debug("logObj", logObj);

    let cFirstName = "";
    let cLastName = "";
    let cFullName = "";

    let commaSplit = contactName.split(/,/g);
    let spaceSplit = contactName.split(/ /g);

    if (commaSplit.length == 2) {
      cFirstName = commaSplit[1].trim();
      cLastName = commaSplit[0].trim();
      cFullName = `${cFirstName} ${cLastName}`;
    } else if (spaceSplit.length == 2) {
      cFirstName = spaceSplit[0].trim();
      cLastName = spaceSplit[1].trim();
      cFullName = `${cFirstName} ${cLastName}`;
    } else {
      cFullName = contactName;
    }

    let exhbID = null;
    let contactID = null;
    let haveContact = false;

    let validEmail = validateEmail(email);


    if (companyName) {
      if (companyName.includes("&")) {
        companyName = companyName.replace(/&/g, "and");
      }
      let exFilt = [
        ["isinactive", "is", "F"],
        "AND",
        ["companyname", "contains", companyName],
        "AND",
        ["companyname", "is", companyName],
      ];
      // if (zip) {
      //   exFilt.push("AND", ["zipcode", "is", zip]);
      // }

      log.debug("exFilt", exFilt);

      let exSearch = search.create({
        type: search.Type.CUSTOMER,
        filters: exFilt,
        columns: [
          search.createColumn({name: "internalid", label: "Internal ID"}),
        ],
      });
      let exSearchResultCount = exSearch.runPaged().count;
      log.debug("exSearchResultCount", exSearchResultCount);
      exSearch.run().each(function (result) {
        exhbID = result.getValue({name: "internalid"});
        return true;
      });
    }

    if (!exhbID) {
      log.debug("🔧 Creating Customer Record");
      try {
        let exhibitorRec = record.create({
          type: record.Type.CUSTOMER,
          isDynamic: true,
        });

        exhibitorRec.setValue({ fieldId: "isperson", value: "F" });
        exhibitorRec.setValue({ fieldId: "taxable", value: true });
        exhibitorRec.setValue({
          fieldId: "companyname",
          value: companyName.replace(/&/g, "and").toUpperCase(),
        });

        let addySkip = false;

        if (!address || address.toUpperCase().search("TBD") >= 0) {
          addySkip = true;
        }

        if (!addySkip) {
          exhibitorRec.selectNewLine({ sublistId: "addressbook" });
          exhibitorRec.setCurrentSublistValue({
            sublistId: "addressbook",
            fieldId: "defaultbilling",
            value: true,
          });
          exhibitorRec.setCurrentSublistValue({
            sublistId: "addressbook",
            fieldId: "defaultshipping",
            value: true,
          });
          let customerSubrecord = exhibitorRec.getCurrentSublistSubrecord({
            sublistId: "addressbook",
            fieldId: "addressbookaddress",
          });

          let countryField = customerSubrecord.getField({
            fieldId: "country",
          });
          let countryOptsInit = countryField.getSelectOptions();

          if (country) {
            for (let i = 0; i < countryOptsInit.length; i++) {
              if (countryOptsInit[i].value === country) {
                log.debug(
                    `✅ Setting Country to ${countryOptsInit[i].value}`
                );
                customerSubrecord.setValue({
                  fieldId: "country",
                  value: countryOptsInit[i].value,
                });
              }
            }
            let countryCheck = customerSubrecord.getValue({
              fieldId: "country",
            });
            log.debug("countryCheck", countryCheck);
            if (!countryCheck) {
              log.error({
                title:
                    "Could not validate country for new exhibitor address:",
                details: `Country: ${country}`,
              });
            }
          } else {
            log.debug("✅ Setting Country To USA");
            customerSubrecord.setValue({
              fieldId: "country",
              value: "US",
            });
          }

          customerSubrecord.setValue({
            fieldId: "addressee",
            value: companyName,
          });
          customerSubrecord.setValue({ fieldId: "addr1", value: address });
          customerSubrecord.setValue({ fieldId: "addr2", value: address2 });
          customerSubrecord.setValue({ fieldId: "city", value: city });
          customerSubrecord.setValue({ fieldId: "state", value: state });
          customerSubrecord.setValue({ fieldId: "zip", value: zip });
          customerSubrecord.setValue({ fieldId: "label", value: address });
          log.debug("✅ Address Set");
          exhibitorRec.commitLine({ sublistId: "addressbook" });
        }

        exhibitorRec.setValue({
          fieldId: "custentity_exhibit_contact",
          value: cFullName.toUpperCase() != "TBD" ? cFullName : "",
        });
        exhibitorRec.setValue({
          fieldId: "custentity_default_order_type",
          value: "1",
        });

        if (phone && phone.toUpperCase() !== "TBD" && phone.length >= 7) {
          exhibitorRec.setValue({
            fieldId: "phone",
            value: phone,
          });
        } else {
          exhibitorRec.setValue({
            fieldId: "phone",
            value: "(*************",
          });
        }

        validEmail && exhibitorRec.setValue({
          fieldId: "email",
          value: email,
        });

        if (fax && fax.toUpperCase() != "TBD" && fax.length >= 7) {
          exhibitorRec.setValue({
            fieldId: "fax",
            value: fax,
          });
        }

        log.debug("showTableID", showTableID);

        exhibitorRec.setValue({
          fieldId: "custentity_ng_cs_exhib_shows",
          value: showTableID,
        });

        let subsidiary = search.lookupFields({
          type: "customrecord_show",
          id: showTableID,
          columns: ["custrecord_show_subsidiary"],
        }).custrecord_show_subsidiary;

        if (subsidiary && useSubsidiaries) {
          exhibitorRec.setValue({
            fieldId: "subsidiary",
            value: subsidiary[0].value,
          });
        }

        if (!csSettings.custrecord_ng_cs_give_contacts_access && email) {
          let pw = importPassword();

          log.debug("701 Password", pw);

          validEmail && exhibitorRec.setValue({
            fieldId: "email",
            value: email,
          });

          exhibitorRec.setValue({
            fieldId: "giveaccess",
            value: true,
          });
          exhibitorRec.setValue({
            fieldId: "accessrole",
            value: csSettings.custrecord_ng_cs_cust_web_access_role || "14",
          });
          exhibitorRec.setValue({
            fieldId: "password",
            value: pw,
          });
          exhibitorRec.setValue({
            fieldId: "password2",
            value: pw,
          });
        }
        exhibitorRec.setValue({
          fieldId: "custentity_ng_cs_last_imported_on",
          value: new Date(),
        });
        try {
          let custRecSaved = exhibitorRec.save({
            enableSourcing: true,
            ignoreMandatoryFields: true,
          });
          exhbID = custRecSaved;
          log.debug("✅ Customer Record Saved", `Rec ID: ${custRecSaved}`);
        } catch (err) {
          log.error({
            title: "❌ Error Saving Customer Record",
            details: err,
          });
        }
      } catch (e) {
        log.error("❌ Error Creating Customer Record", e);
      }
    } else if (exhbID !== null) {
      log.audit("❎ Updating Exhibitor", `Customer Rec ID: ${exhbID}`);
      try {
        let exhibitorRec = record.load({
          type: record.Type.CUSTOMER,
          id: exhbID,
          isDynamic: true,
        });
        log.debug("✅ CUST REC LOADED", exhibitorRec);

        let addyCount = exhibitorRec.getLineCount({
          sublistId: "addressbook",
        });
        let addyMatch = false;
        let addySkip = false;
        let update = false;

        if (!address || address.toUpperCase().search("TBD") >= 0) {
          addySkip = true;
        }

        if (!addySkip) {
          for (var a = 0; a < addyCount; a++) {
            let label = exhibitorRec.getSublistValue({
              sublistId: "addressbook",
              fieldId: "label",
              line: a,
            });
            if (label == address) {
              addyMatch = true;
              break;
            }
          }
        }

        if (!addyMatch) {
          exhibitorRec.selectNewLine({sublistId: "addressbook"});
          exhibitorRec.setCurrentSublistValue({
            sublistId: "addressbook",
            fieldId: "defaultbilling",
            value: true,
          });
          exhibitorRec.setCurrentSublistValue({
            sublistId: "addressbook",
            fieldId: "defaultshipping",
            value: true,
          });
          let customerSubrecord = exhibitorRec.getCurrentSublistSubrecord({
            sublistId: "addressbook",
            fieldId: "addressbookaddress",
          });

          let countryField = customerSubrecord.getField({
            fieldId: "country",
          });
          let countryOptsInit = countryField.getSelectOptions();

          if (country) {
            for (let i = 0; i < countryOptsInit.length; i++) {
              if (countryOptsInit[i].value === country) {
                log.debug(
                    `✅ Setting Country to ${countryOptsInit[i].value}`
                );
                customerSubrecord.setValue({
                  fieldId: "country",
                  value: countryOptsInit[i].value,
                });
              }
            }
            let countryCheck = customerSubrecord.getValue({
              fieldId: "country",
            });
            log.debug("countryCheck", countryCheck);
            if (!countryCheck) {
              log.error({
                title:
                    "Could not validate country for new exhibitor address:",
                details: `Country: ${country}`,
              });
            }
          } else {
            log.debug("✅ Setting Country To USA");
            customerSubrecord.setValue({
              fieldId: "country",
              value: "US",
            });
          }
          customerSubrecord.setValue({
            fieldId: "addressee",
            value: companyName,
          });
          customerSubrecord.setValue({fieldId: "addr1", value: address});
          customerSubrecord.setValue({fieldId: "addr2", value: address2});
          customerSubrecord.setValue({fieldId: "city", value: city});
          customerSubrecord.setValue({fieldId: "state", value: state});
          customerSubrecord.setValue({fieldId: "zip", value: zip});
          customerSubrecord.setValue({fieldId: "label", value: address});
          log.debug("✅ Address Set");
          exhibitorRec.commitLine({sublistId: "addressbook"});
          update = true;
        }

        let exhbShows = exhibitorRec.getValue({
          fieldId: "custentity_ng_cs_exhib_shows",
        });
        log.debug("exhbShows", exhbShows);

        if (!exhbShows.includes(showTableID)) {
          exhbShows.push(showTableID);
        }
        exhibitorRec.setValue({
          fieldId: "custentity_ng_cs_exhib_shows",
          value: exhbShows,
        });

        if (
            cFullName !==
            exhibitorRec.getValue({
              fieldId: "custentity_exhibit_contact",
            }) &&
            cFullName &&
            cFullName.toUpperCase() !== "TBD"
        ) {
          exhibitorRec.setValue({
            fieldId: "custentity_exhibit_contact",
            value: cFullName,
          });
          update = true;
        }

        if (phone !== exhibitorRec.getValue({fieldId: "phone"})) {
          exhibitorRec.setValue({
            fieldId: "phone",
            value: phone,
          });
          update = true;
        }

        if (email !== exhibitorRec.getValue({fieldId: "email"})) {
          validEmail && exhibitorRec.setValue({
            fieldId: "email",
            value: email,
          });
        }

        if (fax !== exhibitorRec.getValue({fieldId: "fax"})) {
          exhibitorRec.setValue({
            fieldId: "fax",
            value: fax,
          });
          update = true;
        }

        if (
            exhibitorRec.getValue({fieldId: "phone"}) &&
            exhibitorRec.getValue({fieldId: "email"}) &&
            exhibitorRec.getValue({fieldId: "giveaccess"}) === true
        ) {
          update = true;
        }

        if (
            !csSettings.custrecord_ng_cs_give_contacts_access &&
            email &&
            exhibitorRec.getValue({fieldId: "giveaccess"}) !== true
        ) {
          let pw = importPassword();

          log.debug("905 Password", pw);


          validEmail && exhibitorRec.setValue({
            fieldId: "email",
            value: email,
          });


          exhibitorRec.setValue({
            fieldId: "giveaccess",
            value: true,
          });
          exhibitorRec.setValue({
            fieldId: "accessrole",
            value: csSettings.custrecord_ng_cs_cust_web_access_role || "14",
          });
          exhibitorRec.setValue({
            fieldId: "password",
            value: pw,
          });
          exhibitorRec.setValue({
            fieldId: "password2",
            value: pw,
          });
        }

        let eventSubsidiary = search.lookupFields({
          type: "customrecord_show",
          id: showTableID,
          columns: ["custrecord_show_subsidiary"],
        }).custrecord_show_subsidiary;

        if (eventSubsidiary && useSubsidiaries && multiSubsidiaryCustomer) {
          let addSubsidiary = true;

          let subsidiariesCount = exhibitorRec.getLineCount({sublistId: 'submachine'});

          for (let i = 0; i < subsidiariesCount; i++) {
            let subsidiaryId = exhibitorRec.getSublistValue({
              sublistId: 'submachine',
              fieldId: 'subsidiary',
              line: i
            });

            if (subsidiaryId === eventSubsidiary[0].value) {
              log.audit('Not Adding New Subsidiary')
              addSubsidiary = false;
              break;
            }
          }

          if (addSubsidiary) {
            log.audit('Adding Subsidiary', `Add Subsidiary ${eventSubsidiary[0].text}`)

            exhibitorRec.selectNewLine({sublistId: 'submachine'});
            exhibitorRec.setCurrentSublistValue({
              sublistId: 'submachine',
              fieldId: 'subsidiary',
              value: eventSubsidiary[0].value
            });
            exhibitorRec.commitLine({sublistId: 'submachine'});

            update = true;
          }
        }

        if (update) {
          exhibitorRec.setValue({
            fieldId: "custentity_ng_cs_last_imported_on",
            value: new Date(),
          });

          try {
            let custRecSaved = exhibitorRec.save({
              enableSourcing: true,
              ignoreMandatoryFields: true,
            });
            log.debug("✅ Customer Record Saved", `Rec ID: ${custRecSaved}`);
          } catch (err) {
            log.error({
              title: "❌ Error Saving Customer Record",
              details: err,
            });
          }
        } else {
          exhibitorRec.setValue({
            fieldId: "custentity_ng_cs_last_imported_on",
            value: new Date(),
          });
          try {
            let custRecSaved = exhibitorRec.save({
              ignoreMandatoryFields: true,
            });
            log.debug("✅ Customer Record Saved", `Rec ID: ${custRecSaved}`);
          } catch (err) {
            log.error({
              title: "❌ Error Saving Customer Record",
              details: err,
            });
          }
        }

        let subsidiary = search.lookupFields({
          type: "customrecord_show",
          id: showTableID,
          columns: ["custrecord_show_subsidiary"],
        }).custrecord_show_subsidiary;

        if (multiSubsidiaryCustomer && subsidiary) {
          log.debug("✅ Checking For Multiple Subsidiaries");
          try {
            let customersubsidiaryrelationshipSearchObj = search.create({
              type: "customersubsidiaryrelationship",
              filters: [
                ["entity", "anyof", [exhbID]],
                "AND",
                ["subsidiary", "anyof", [subsidiary[0].value]],
              ],
              columns: [
                search.createColumn({
                  name: "entity",
                  sort: search.Sort.ASC,
                  label: "Customer",
                }),
              ],
            });
            let searchResultCount =
                customersubsidiaryrelationshipSearchObj.runPaged().count;
            log.debug(
                "customersubsidiaryrelationshipSearchObj result count",
                searchResultCount
            );

            if (searchResultCount === 0) {
              let cusSubRelRec = record.create({
                type: "customersubsidiaryrelationship",
                isDynamic: true,
              });
              cusSubRelRec.setValue({fieldId: "entity", value: exhbID});
              cusSubRelRec.setValue({
                fieldId: "subsidiary",
                value: subsidiary,
              });
              cusSubRelRec.save({
                enableSourcing: true,
                ignoreMandatoryFields: true,
              });
            }
          } catch (err) {
            log.error({
              title:
                  "❌ Error encountered updating exhibitor subsidiary relationships",
              details: err,
            });
          }
        }
      } catch (e) {
        log.error({title: " Error Updating Customer Record", details: e});
      }
    }

    if (exhbID !== null) {
      let subsidiary = search.lookupFields({
        type: "customrecord_show",
        id: showTableID,
        columns: ["custrecord_show_subsidiary"],
      }).custrecord_show_subsidiary;

      if ((cFirstName && cLastName) || cFullName) {
        haveContact = true;
      }

      if (haveContact) {
        let filt = [];

        if (email) {
          filt.push(["email", "is", email]);
        } else if (cFirstName && cLastName) {
          filt.push("and", ["firstname", "is", cFirstName], "and", [
            "lastName",
            "is",
            cLastName,
          ]);
        } else if (cFullName) {
          filt.push("and", ["entityid", "contains", cFullName]);
        }

        let contactSearch = search.create({
          type: search.Type.CONTACT,
          filters: [filt],
          columns: [
            search.createColumn({name: "internalid", label: "Internal ID"}),
          ],
        });
        contactSearch.run().each(function (result) {
          contactID = result.getValue({name: "internalid"});
          return true;
        });

        if (contactID) {
          log.debug("✅ Existing Contact Found");
          let companyId = search.lookupFields({
            type: search.Type.CONTACT,
            id: contactID,
            columns: ["company"],
          }).company;
          log.debug("THE COMPANY ID", companyId);

          if (companyId?.length && Number(companyId[0].value) !== Number(exhbID)) {
            log.debug({
              title:
                  "✅ Contact found but is already linked to a different exhibitor",
              details: "Attaching Contact to New Exhibitor",
            });

            // Attach contact to new Customer Record
            try {
              record.attach({
                record: {
                  type: 'contact',
                  id: contactID
                },
                to: {
                  type: 'customer',
                  id: exhbID
                }
              });
            } catch (e) {
              log.error('Error attaching contact to customer', e)
            }

            // Set Access on previous customer record to False
            const companyFound = Number(companyId[0].value)
            
            try {
              let prevCustRec = record.load({
                type: record.Type.CUSTOMER,
                id: companyFound,
                isDynamic: true
              });

              let cLines = prevCustRec.getLineCount({sublistId: "contactroles"});
              let contact = {};

              if (cLines >= 1) {
                for (let i = 0; i < cLines; i++) {
                  let cl = prevCustRec.getSublistValue({
                    sublistId: "contactroles",
                    fieldId: "contact",
                    line: i,
                  });

                  if (cl == contactID) {
                    contact.cl = cl;
                    contact.line = i;
                  }
                }

                log.debug("Contact Found", contact);

                if (contact) {
                  try {

                    prevCustRec.selectLine({
                      sublistId: "contactroles",
                      line: contact.line,
                    });

                    prevCustRec.setCurrentSublistValue({
                      sublistId: "contactroles",
                      fieldId: "giveaccess",
                      value: false,
                    });

                    prevCustRec.commitLine({sublistId: "contactroles"});

                    let prevCustRecSaved = prevCustRec.save({
                      enableSourcing: true,
                      ignoreMandatoryFields: true,
                    });

                    log.audit({
                      title: "✅ Cust Record Saved",
                      details: `RecId ${prevCustRecSaved}`,
                    });

                  } catch (e) {
                    log.error({
                      title: "❌ Error encountered giving contact login access",
                      detail: e,
                    });
                  }
                }
              }
            } catch (e) {
              log.error('Error Revoking Access From Prev Customer Rec', e)
            }


          } else if (companyId) {
            if (multiSubsidiaryCustomer && subsidiary) {
              try {
                let customersubsidiaryrelationshipSearchObj = search.create({
                  type: "customersubsidiaryrelationship",
                  filters: [
                    ["entity", "anyof", [exhbID]],
                    "AND",
                    ["subsidiary", "anyof", [subsidiary[0].value]],
                  ],
                  columns: [
                    search.createColumn({
                      name: "entity",
                      sort: search.Sort.ASC,
                      label: "Customer",
                    }),
                  ],
                });
                let searchResultCount =
                    customersubsidiaryrelationshipSearchObj.runPaged().count;

                if (searchResultCount === 0) {
                  let cusSubRelRec = record.create({
                    type: "customersubsidiaryrelationship",
                    isDynamic: true,
                  });
                  cusSubRelRec.setValue({fieldId: "entity", value: exhbID});
                  cusSubRelRec.setValue({
                    fieldId: "subsidiary",
                    value: subsidiary,
                  });
                  cusSubRelRec.save({
                    enableSourcing: true,
                    ignoreMandatoryFields: true,
                  });
                }
              } catch (err) {
                log.error({
                  title:
                      "❌ Error encountered updating contact subsidiary relationships Line: 1157",
                  details: err,
                });
              }
            }
            try {
              record.submitFields({
                type: record.Type.CONTACT,
                id: contactID,
                values: {
                  custentity_ng_cs_last_imported_on: new Date(),
                },
              });
            } catch (err) {
              log.error({
                title:
                    "❌ Error encountered setting 'last imported on' value on customer",
                details: err,
              });
            }
          }
        } else {
          log.debug("🔧 Creating New Contact");

          let contact = record.create({
            type: record.Type.CONTACT,
            isDynamic: true,
          });

          if (cFirstName && cLastName) {
            contact.setValue({
              fieldId: "firstname",
              value: cFirstName,
            });
            contact.setValue({
              fieldId: "lastname",
              value: cLastName,
            });
          } else if (cFullName) {
            contact.setValue({
              fieldId: "entityid",
              value: cFullName,
            });
          }
          (email && validEmail) && contact.setValue({
            fieldId: "email",
            value: email,
          });

          phone && contact.setValue({
            fieldId: "phone",
            value: phone,
          });

          contact.setValue({
            fieldId: "company",
            value: exhbID,
          });

          useSubsidiaries && contact.setValue({
            fieldId: "subsidiary",
            value: subsidiary[0].value,
          });

          contact.setValue({
            fieldId: "custentity_ng_cs_last_imported_on",
            value: new Date(),
          });

          try {
            let savedContact = contact.save({
              enableSourcing: true,
              ignoreMandatoryFields: true,
            });
            logObj.status = "SUCCESS";
            logObj.desc = "Successfully created new contact";
            logObj.contact = cFullName;
            logObj.company = companyName;
            logImport(logObj);
            logObj = cleanLogObj(logObj);
            log.debug({
              title: "✅ Contact Saved 1199",
              details: `Rec ID: ${savedContact}`,
            });
            contactID = savedContact;
          } catch (err) {
            if (err.name === "CONTACT_ALREADY_EXISTS") {
              log.debug("Contact Exists, Updating Existing Contact");
              let filt = [];

              filt.push(["company", "anyof", [exhbID]]);
              if (email) {
                filt.push("and", ["email", "is", email]);
              } else if (cFirstName && cLastName) {
                filt.push("and", ["firstname", "is", cFirstName], "and", [
                  "lastName",
                  "is",
                  cLastName,
                ]);
              } else if (cFullName) {
                filt.push("and", ["entityid", "contains", cFullName]);
              }

              let contactSearch = search.create({
                type: search.Type.CONTACT,
                filters: [filt],
                columns: [
                  search.createColumn({
                    name: "internalid",
                    label: "Internal ID",
                  }),
                ],
              });
              contactSearch.run().each(function (result) {
                contactID = result.getValue({name: "internalid"});
                return true;
              });

              if (contactID !== null) {
                try {
                  let contact = record.load({
                    type: record.Type.CONTACT,
                    id: contactID,
                    isDynamic: true,
                  });

                  (email && validEmail) && contact.setValue({
                    fieldId: "email",
                    value: email,
                  });

                  phone && contact.setValue({
                    fieldId: "phone",
                    value: phone,
                  });

                  contact.setValue({
                    fieldId: "custentity_ng_cs_last_imported_on",
                    value: new Date(),
                  });
                  contact.save({
                    enableSourcing: true,
                    ignoreMandatoryFields: true,
                  });

                  if (multiSubsidiaryCustomer && subsidiary) {
                    try {
                      let customersubsidiaryrelationshipSearchObj =
                          search.create({
                            type: "customersubsidiaryrelationship",
                            filters: [
                              ["entity", "anyof", [contactID]],
                              "AND",
                              ["subsidiary", "anyof", [subsidiary[0].value]],
                            ],
                            columns: [
                              search.createColumn({
                                name: "entity",
                                sort: search.Sort.ASC,
                                label: "Customer",
                              }),
                            ],
                          });
                      let searchResultCount =
                          customersubsidiaryrelationshipSearchObj.runPaged()
                              .count;

                      if (searchResultCount === 0) {
                        let cusSubRelRec = record.create({
                          type: "customersubsidiaryrelationship",
                          isDynamic: true,
                        });
                        cusSubRelRec.setValue({
                          fieldId: "entity",
                          value: contactID,
                        });
                        cusSubRelRec.setValue({
                          fieldId: "subsidiary",
                          value: subsidiary,
                        });
                        cusSubRelRec.save({
                          enableSourcing: true,
                          ignoreMandatoryFields: true,
                        });
                      }
                    } catch (err) {
                      log.error({
                        title:
                            "❌ Error encountered updating contact subsidiary relationships Line: 1334",
                        details: err,
                      });
                    }
                  }
                } catch (err2) {
                  logObj.status = "FAILURE";
                  logObj.desc = `Failed to update contact record for exhibitor:${JSON.stringify(
                      {
                        email: email,
                        firstName: cFirstName,
                        lastName: cLastName,
                        fullName: cFullName,
                      }
                  )}`;
                  logObj.contact = cFullName;
                  logObj.company = companyName;
                  logObj.booth = boothNum;
                  logObj.errCode = "UNEXPECTED_ERROR";
                  logObj.errDesc =
                      "Could not locate existing contact record for updating";
                  logImport(logObj);
                  logObj = cleanLogObj(logObj);
                  log.error({
                    title:
                        "❌ Error encountered updating contact record Line: 1342",
                    details: err,
                  });
                }
              } else {
                logObj.status = "FAILURE";
                logObj.desc = `Failed to update contact record for exhibitor:${JSON.stringify(
                    {
                      email: email,
                      firstName: cFirstName,
                      lastName: cLastName,
                      fullName: cFullName,
                    }
                )}`;
                logObj.contact = cFullName;
                logObj.company = companyName;
                logObj.booth = boothNum;
                logObj.errCode = err.name;
                logObj.errDesc = err.message;
                logImport(logObj);
                logObj = cleanLogObj(logObj);
                log.error({
                  title:
                      "❌ Error encountered updating contact record Line: 1349",
                  details: err,
                });
              }
            } else {
              logObj.status = "FAILURE";
              logObj.desc = `Failed to update contact record for exhibitor:${JSON.stringify(
                  {
                    email: email,
                    firstName: cFirstName,
                    lastName: cLastName,
                    fullName: cFullName,
                  }
              )}`;
              logObj.contact = cFullName;
              logObj.company = companyName;
              logObj.booth = boothNum;
              logObj.errCode = err.name;
              logObj.errDesc = err.message;
              logImport(logObj);
              logObj = cleanLogObj(logObj);
              log.error({
                title:
                    "❌ Error encountered updating contact record Line: 1356",
                details: err,
              });
            }
          }
        }
      } else {
        logObj.status = "FAILURE";
        logObj.desc = "Cannot create contact record due to missing info";
        logObj.company = companyName;
        logObj.booth = boothNum;
        logObj.line = Number(line) + 1;
        logImport(logObj);
        logObj = cleanLogObj(logObj);
        log.error({
          title: "❌ Cannot create contact record due to missing info",
          details: "",
        });
      }
    }

    if (exhbID) {
      if (!boothNum || boothNum.toUpperCase() == "TBD") {
        log.error("❌ Missing Booth Info");
      } else {
        log.debug("Checking for existing booths");
        let customrecord_show_boothsSearchObj = search.create({
          type: "customrecord_show_booths",
          filters: [
            ["custrecord_booth_number", "is", boothNum],
            "AND",
            ["custrecord_booth_show_table", "anyof", showTableID],
          ],
          columns: [
            search.createColumn({
              name: "name",
              sort: search.Sort.ASC,
              label: "Name",
            }),
          ],
        });
        let searchResultCount =
            customrecord_show_boothsSearchObj.runPaged().count;
        log.debug(
            "customrecord_show_boothsSearchObj result count",
            searchResultCount
        );

        if (searchResultCount > 0) {
          log.debug("❎ Booth Already Exists");
        }
        if (searchResultCount === 0) {
          log.debug("🔧 Creating New Booth Record");
          let boothRec = record.create({
            type: "customrecord_show_booths",
            isDynamic: true,
          });

          boothRec.setValue({
            fieldId: "custrecord_booth_number",
            value: boothNum,
          });
          boothRec.setValue({
            fieldId: "custrecord_booth_show_table",
            value: showTableID,
          });
          boothRec.setValue({
            fieldId: "custrecord_booth_exhibitor",
            value: exhbID,
          });
          boothRec.setValue({
            fieldId: "custrecord_booth_actual_exhibitor",
            value: exhbID,
          });
          boothRec.setValue({
            fieldId: "custrecord_addl_booths",
            value: adtlBooths,
          });
          let exhb = boothRec.getText({
            fieldId: "custrecord_booth_exhibitor",
          });
          boothRec.setValue({
            fieldId: "name",
            value: `${boothNum} (${exhb})`,
          });

          // if (useLocations) {
          //   boothRec.setValue({
          //     fieldId: "custrecord_booth_venue",
          //     value: venue,
          //   });
          // }

          contactID && boothRec.setValue({
            fieldId: "custrecord_booth_contact",
            value: contactID,
          });

          if (
              boothLength &&
              Number(boothLength) !== 0 &&
              boothWidth &&
              Number(boothWidth) !== 0
          ) {
            let boothSize = null;

            if (
                Number(boothLength) === 10 &&
                ((Number(boothWidth) <= 80 &&
                        Number(boothWidth) >= 10 &&
                        Number(boothWidth) % 10 === 0) ||
                    Number(boothWidth) === 15)
            ) {
              boothSize = `${boothLength} X ${boothWidth}`;
            } else {
              boothSize = "Island";
            }
            boothRec.setValue({
              fieldId: "custrecord_booth_boothsize",
              value: boothSize,
            });
            boothRec.setValue({
              fieldId: "custrecord_booth_width",
              value: Number(boothWidth),
            });
            boothRec.setValue({
              fieldId: "custrecord_booth_length",
              value: Number(boothLength),
            });
          }

          try {
            boothPavilion && boothRec.setValue({
              fieldId: "custrecord_booth_pavilion",
              value: boothPavilion,
            });

            boothLocation && boothRec.setValue({
              fieldId: "custrecord_booth_location",
              value: boothLocation,
            });

            if (boothType) {
              let boothTypeField = boothRec.getField({
                fieldId: 'custrecord_booth_type'
              });

              let bothTypeOptions = boothTypeField.getSelectOptions();

              let matchingBooth = bothTypeOptions.find(option => option.text === boothType);

              let boothTypeValue = matchingBooth ? matchingBooth.value : '';

              boothRec.setValue({
                fieldId: "custrecord_booth_type",
                value: boothTypeValue,
              });
            }

          } catch (e) {
            log.error('Error setting Pavilion, Location, Booth Type', e)
          }

          try {
            let boothRecSaved = boothRec.save({
              enableSourcing: true,
              ignoreMandatoryFields: true,
            });

            logObj.status = "SUCCESS";
            logObj.desc = "Successfully Created Booth Record";
            logObj.booth = boothNum;
            logObj.contact = cFullName;
            logObj.company = companyName;
            logImport(logObj);
            logObj = cleanLogObj(logObj);

            log.debug({
              title: "✅ Booth Record Saved",
              details: boothRecSaved,
            });

            reduceContext.write({
              key: line,
              value: boothNum,
            });
          } catch (e) {
            logObj.status = "FAILURE";
            logObj.desc = "Could not submit booth record";
            logObj.booth = boothNum;
            logObj.contact = cFullName;
            logObj.company = companyName;
            logObj.errCode = e.name;
            logObj.errDesc = e.message;
            logImport(logObj);
            logObj = cleanLogObj(logObj);
            log.error({
              title: "❌ Error submitting booth record",
              details: e,
            });
          }
        }

        if (contactID && csSettings.custrecord_ng_cs_give_contacts_access) {
          let exhibRec = record.load({
            type: record.Type.CUSTOMER,
            id: exhbID,
            isDynamic: true,
          });
          let cLines = exhibRec.getLineCount({sublistId: "contactroles"});
          let sendMail = false;
          let contact = {};

          if (cLines >= 1) {
            for (let i = 0; i < cLines; i++) {
              let cl = exhibRec.getSublistValue({
                sublistId: "contactroles",
                fieldId: "contact",
                line: i,
              });
              log.debug("cl", cl + " " + typeof cl);
              log.debug("Line", i);
              log.debug("contactID", contactID + " " + typeof contactID);

              if (cl == contactID) {
                contact.cl = cl;
                contact.line = i;
              }
            }

            log.debug("First contact", contact);

            if (contact) {
              try {
                let pw = importPassword();

                log.debug("1579 Password", pw);
                log.debug("contact Line", contact);

                exhibRec.selectLine({
                  sublistId: "contactroles",
                  line: contact.line,
                });
                exhibRec.setCurrentSublistValue({
                  sublistId: "contactroles",
                  fieldId: "giveaccess",
                  value: true,
                });
                exhibRec.setCurrentSublistValue({
                  sublistId: "contactroles",
                  fieldId: "role",
                  value:
                      csSettings.custrecord_ng_cs_cust_web_access_role || "14",
                });
                exhibRec.setCurrentSublistValue({
                  sublistId: "contactroles",
                  fieldId: "fillpassword",
                  value: true,
                });
                exhibRec.setCurrentSublistValue({
                  sublistId: "contactroles",
                  fieldId: "password",
                  value: pw,
                });
                exhibRec.setCurrentSublistValue({
                  sublistId: "contactroles",
                  fieldId: "password1",
                  value: pw,
                });
                exhibRec.setCurrentSublistValue({
                  sublistId: "contactroles",
                  fieldId: "passwordconfirm",
                  value: pw,
                });
                exhibRec.commitLine({sublistId: "contactroles"});

                let exhibRecSaved = exhibRec.save({
                  enableSourcing: true,
                  ignoreMandatoryFields: true,
                });

                log.audit({
                  title: "✅ Cust Record Saved",
                  details: `RecId ${exhibRecSaved}`,
                });

                sendMail = true;
              } catch (e) {
                log.error({
                  title: "❌ Error encountered giving contact login access",
                  detail: e,
                });
              }
            }
          }
        }
      }
    }

  };

  /**
   * Defines the function that is executed when the summarize entry point is triggered. This entry point is triggered
   * automatically when the associated reduce stage is complete. This function is applied to the entire result set.
   * @param {Object} summaryContext - Statistics about the execution of a map/reduce script
   * @param {number} summaryContext.concurrency - Maximum concurrency number when executing parallel tasks for the map/reduce
   *     script
   * @param {Date} summaryContext.dateCreated - The date and time when the map/reduce script began running
   * @param {boolean} summaryContext.isRestarted - Indicates whether the current invocation of this function is the first
   *     invocation (if true, the current invocation is not the first invocation and this function has been restarted)
   * @param {Iterator} summaryContext.output - Serialized keys and values that were saved as output during the reduce stage
   * @param {number} summaryContext.seconds - Total seconds elapsed when running the map/reduce script
   * @param {number} summaryContext.usage - Total number of governance usage units consumed when running the map/reduce
   *     script
   * @param {number} summaryContext.yields - Total number of yields when running the map/reduce script
   * @param {Object} summaryContext.inputSummary - Statistics about the input stage
   * @param {Object} summaryContext.mapSummary - Statistics about the map stage
   * @param {Object} summaryContext.reduceSummary - Statistics about the reduce stage
   * @since 2015.2
   */
  const summarize = (summaryContext) => {
    log.debug("Summary", summaryContext);
    log.debug("Summary Value", summaryContext.value);

    let scriptObj = runtime.getCurrentScript();
    let sendToUser = scriptObj.getParameter({
      name: "custscript_ng_cs_impt_send_to_user_mr",
    });
    let sendToGroup = scriptObj.getParameter({
      name: "custscript_ng_cs_impt_send_to_group_mr",
    });
    let user = scriptObj.getParameter({
      name: "custscript_ng_cs_impt_user_id_mr",
    });

    let showTableIDAndImportIDObj = scriptObj.getParameter({
      name: "custscript_ng_cs_impt_show_table_id_mr",
    });
    log.debug("showTableID", showTableIDAndImportIDObj);

    let parsedObj = JSON.parse(showTableIDAndImportIDObj);
    log.debug("parsedObj", parsedObj);

    let importId = parsedObj.importId;
    log.debug("importId", importId);

    // let importId = scriptObj.getParameter({
    //   name: "custscript_ng_cs_impt_import_id_mr",
    // });

    log.debug("sendToUser", sendToUser);
    log.debug("sendToGroup", sendToGroup);
    log.debug("user", user);
    log.debug("importId", importId);

    let recip = null;

    if (sendToUser) {
      recip = user;
    } else {
      recip = sendToGroup;
    }

    try {
      email.send({
        author: Number(user),
        subject: `Exhibitor Import Complete", "Exhibitor import with ID of ${importId} has completed.`,
        body: "Batch successfully imported",
        recipients: recip,
      });
    } catch (e) {
      log.debug("❌ Error Sending Email", e);
    }
  };

  function validateEmail(email) {
    return email.match(
        /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    );
  }

  function importPassword() {
    var pw = "";
    while (!pw || pw.length < 10) {
      pw = randomString(0, 0, 20, true);
    }

    return pw;
  }

  function logImport(data) {
    try {
      let importLog = record.create({
        type: "customrecord_exhb_import_log",
        isDynamic: true,
      });

      importLog.setValue({
        fieldId: "custrecord_exhb_log_datetime",
        value: new Date(),
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_show_table",
        value: data.showTableID,
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_importid",
        value: data.importID,
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_status",
        value: data.status != null ? data.status : "UNKNOWN",
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_descr",
        value: data.desc != null ? data.desc.substr(0, 300) : "N/A",
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_company",
        value: data.company != null ? data.company : "",
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_contact",
        value: data.contact != null ? data.contact : "",
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_booth",
        value: data.booth != null ? data.booth : "",
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_errcode",
        value: data.errCode != null ? data.errCode : "",
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_errdescr",
        value: data.errDesc != null ? data.errDesc.substr(0, 300) : "",
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_file_name",
        value: data.fileName,
      });
      importLog.setValue({
        fieldId: "custrecord_exhb_log_file_line",
        value: data.line,
      });

      importLog.save({
        ignoreMandatoryFields: true,
      });
    } catch (err) {
      log.error({
        title: `Error encountered writing import log", "Log data:`,
        details: JSON.stringify(data),
      });
    }
  }

  function cleanLogObj(logObj) {
    logObj.status = null;
    logObj.descr = null;
    logObj.company = null;
    logObj.contact = null;
    logObj.booth = null;
    logObj.errCode = null;
    logObj.errDesc = null;

    return logObj;
  }

  function randomString(llen, nlen, rlen, isPW) {
    let letters = [
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "I",
      "J",
      "K",
      "L",
      "M",
      "N",
      "O",
      "P",
      "Q",
      "R",
      "S",
      "T",
      "U",
      "V",
      "W",
      "X",
      "Y",
      "Z",
    ];
    let lower = [
      "a",
      "b",
      "c",
      "d",
      "e",
      "f",
      "g",
      "h",
      "i",
      "j",
      "k",
      "l",
      "m",
      "n",
      "o",
      "p",
      "q",
      "r",
      "s",
      "t",
      "u",
      "v",
      "w",
      "x",
      "y",
      "z",
    ];
    let numbers = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
    let symbols = ["!", "@", "#", "$", "%", "*", "="];
    let rcode = "";
    let ll = letters.length;
    let nl = numbers.length;
    let pwTest = /[ IilO01]/g;
    isPW = isPW || false;

    if (!rlen || isNaN(Number(rlen)) || Number(rlen) === 0) {
      for (let l = 0; l < llen; l++) {
        let pos = Math.floor(Math.random() * ll);
        rcode += letters[pos];
      }

      for (let n = 0; n < nlen; n++) {
        let pos = Math.floor(Math.random() * nl);
        rcode += numbers[pos];
      }
    } else {
      let full = [];
      if (isPW) {
        full = letters.concat(lower, numbers, symbols);
      } else {
        full = letters.concat(numbers);
      }
      let rl = full.length;

      for (let r = 0; r < rlen; r++) {
        if (isPW) {
          let char = " ";
          do {
            let pos = Math.floor(Math.random() * rl);
            char = full[pos];
          } while (pwTest.test(char));
          rcode += char;
        } else {
          let pos = Math.floor(Math.random() * rl);
          rcode += full[pos];
        }
      }
    }

    return rcode;
  }

  return { getInputData, map, reduce, summarize };
});
