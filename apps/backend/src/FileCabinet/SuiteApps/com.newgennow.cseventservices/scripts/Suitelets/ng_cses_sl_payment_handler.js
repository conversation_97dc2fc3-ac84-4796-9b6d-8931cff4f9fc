/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 */
define([
  "N/cache",
  "N/config",
  "N/crypto",
  "N/currency",
  "N/encode",
  "N/file",
  "N/format",
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/task",
  "N/url",
  "N/error",
  "../lib/newgen.paytrace.lib.21",
], /**
 * @param {cache} cache
 * @param {config} config
 * @param {crypto} crypto
 * @param {currency} currency
 * @param {encode} encode
 * @param {file} file
 * @param {format} format
 * @param {query} query
 * @param {record} record
 * @param {runtime} runtime
 * @param {search} search
 * @param {task} task
 * @param {url} url
 * @param {error} error
 * @param {Object} PT
 */ (
  cache,
  config,
  crypto,
  currency,
  encode,
  file,
  format,
  query,
  record,
  runtime,
  search,
  task,
  url,
  error,
  PT,
) => {
  /**
   * Defines the Suitelet script trigger point.
   * @param {Object} scriptContext
   * @param {ServerRequest} scriptContext.request - Incoming request
   * @param {ServerResponse} scriptContext.response - Suitelet response
   * @since 2015.2
   */

  let CS_SETTINGS = {};
  let PAYTRACE_DATA;
  let PAYTRACE_PROFILE = null;
  let PAYTRACE_TOKEN = null;
  let PT_PROFILE_CACHE = null;
  let PT_TOKEN_CACHE = null;

  const onRequest = (scriptContext) => {
    let sc = scriptContext;
    let req = sc.request;
    let res = sc.response;
    let { method, parameters, body, clientIpAddress, headers } = req;
    let { write, setHeader } = res;

    PT_PROFILE_CACHE = cache.getCache({
      name: "PT_PROFILE",
      scope: cache.Scope.PUBLIC,
    });

    PT_TOKEN_CACHE = cache.getCache({
      name: "PT_TOKEN",
      scope: cache.Scope.PROTECTED,
    });

    log.debug({ title: "", details: "" });

    log.debug({
      title: "🔧 Payload received:",
      details: {
        parameters,
        body,
      },
    });

    let runOptions = {
      GET: () => {
        log.audit({ title: "⚡ Running GET...", details: "" });
      },
      POST: () => {
        log.audit({ title: "⚡ Running POST...", details: "" });

        log.debug({
          title: "🚧 Data passed:",
          details: {
            parameters,
            body,
          },
        });

        // Get Paytrace profile info
        CS_SETTINGS = getSettings();
        runPayGenInit(); // Has to be ran after settings is defined

        setHeader({
          name: "Content-Type",
          value: "application/json",
        });

        if (body && parameters.type) {
          let payload = JSON.parse(body);
          switch (parameters.type) {
            case "PayGen-Auto":
              // Run paytrace payment with auto capture
              runPayGenPayment(req, payload, parameters, true)
                .then((payRes) => {
                  log.audit({
                    title: "✅ Payment run completed",
                    details: payRes,
                  });

                  if (payRes?.success) {
                    write({
                      output: JSON.stringify(payRes),
                    });
                  } else {
                    write({
                      output: JSON.stringify(payRes),
                    });
                  }
                })
                .catch((err) => {
                  log.error({
                    title: "❌ Error running PayGen payment:",
                    details: err,
                  });
                  if (err.name === "PAYTRACE_ERROR") {
                    let reducedErrorMessage = "";
                    // Paytrace error messages come with their company name in them.
                    // We only want the message after that which contains "| |" to spilt - displaying the error correctly.

                    if (err.message.search(/ \|\| /) !== -1) {
                      reducedErrorMessage =
                        err.message.split(/ \|\| /)[1] ||
                        "Unknown card processor error";
                    } else {
                      reducedErrorMessage = err.message;
                    }

                    write({
                      output: JSON.stringify({
                        error: {
                          ...err,
                        },
                        name: "CREDIT_CARD_ERROR",
                        message: reducedErrorMessage,
                      }),
                    });
                  } else if (err.name === "CREDIT_CARD_ERROR") {
                    write({
                      output: JSON.stringify({
                        error: err,
                        name: "CREDIT_CARD_ERROR",
                        message: err.message,
                      }),
                    });
                  } else {
                    write({
                      output: JSON.stringify({
                        error: err,
                      }),
                    });
                  }
                });
              break;
            case "PayGen-Manual":
              // Run paytrace payment with manual capture. User would have to capture payment in the UI
              runPayGenPayment(req, payload, parameters, false)
                .then((payRes) => {
                  log.audit({
                    title: "✅ Payment run completed",
                    details: payRes,
                  });
                  if (payRes?.success) {
                    write({
                      output: JSON.stringify(payRes),
                    });
                  } else {
                    write({
                      output: JSON.stringify(payRes),
                    });
                  }
                })
                .catch((err) => {
                  log.error({
                    title: "❌ Error running PayGen payment:",
                    details: err,
                  });
                  if (err.name === "PAYTRACE_ERROR") {
                    let reducedErrorMessage = "";

                    // Paytrace error messages come with their company name in them.
                    // We only want the message after that which contains "| |" to spilt - displaying the error correctly.

                    if (err.message.search(/ \|\| /) !== -1) {
                      reducedErrorMessage =
                        err.message.split(/ \|\| /)[1] ||
                        "Unknown card processor error";
                    } else {
                      reducedErrorMessage = err.message;
                    }

                    write({
                      output: JSON.stringify({
                        error: {
                          ...err,
                        },
                        name: "CREDIT_CARD_ERROR",
                        message: reducedErrorMessage,
                      }),
                    });
                  } else {
                    write({
                      output: JSON.stringify({
                        error: err,
                      }),
                    });
                  }
                });
              break;
            case "PayGen-Ref":
              // Run to create the paytrace ref needed for payment reference on a deposit

              try {
                if (PAYTRACE_TOKEN && PAYTRACE_PROFILE) {
                  log.audit({
                    title: "🟢 Paytrace reference init...",
                    details: "",
                  });

                  log.debug({
                    title: "🟡 Payment ref payload received:",
                    details: payload,
                  });

                  log.audit({
                    title: "🚧 Getting transaction data with:",
                    details: {
                      trasnactionId: payload.payGen?.paymentId,
                      profile: PAYTRACE_PROFILE,
                    },
                  });

                  PT.comm.GetPayTraceAuth(PAYTRACE_PROFILE);
                  let transactionData = PT.comm.GetTransactionData(
                    PAYTRACE_TOKEN,
                    payload.payGen.paymentId,
                  );

                  log.debug({
                    title: "🧾 Transaction data:",
                    details: transactionData,
                  });

                  let payCompRecId = PT.data.CreatePaymentReferenceOld({
                    formId: PT.settings.custrecord_ng_pts_ref_order_form,
                    soId: parameters.currentSalesOrder,
                    custId: parameters.customer,
                    PayTraceID: payload.payGen.paymentId,
                    profId: PAYTRACE_PROFILE,
                    ccRequest: payload.payGen.httpReq,
                    // altRequestPost: altRequestPost,
                    tType: "Card",
                    completed: true,
                    refund: false,
                    tData: transactionData.transactions[0],
                    export: transactionData,
                    cType: payload.payGen.cardPaymentMethod,
                    dpstId: payload.deposit.id,
                  });

                  let updatedDep = record.submitFields({
                    type: record.Type.CUSTOMER_DEPOSIT,
                    id: payload.deposit.id,
                    values: { custbody_ng_paytrace_data_ref: payCompRecId },
                    options: {
                      ignoreMandatoryFields: true,
                      enableSourcing: false,
                    },
                  });

                  log.audit({
                    title: "👍 Payment Reference submitted...",
                    details: "",
                  });
                  if (updatedDep) {
                    log.audit({
                      title: "✅ Payment Reference submitted success!...",
                      details: "",
                    });

                    write({
                      output: JSON.stringify({
                        status: "SUBMITTED_REF_OK",
                        success: true,
                        depositId: updatedDep,
                        message: `Deposit record updated with payment reference: ${payCompRecId}`,
                      }),
                    });
                  } else {
                    log.audit({
                      title: "❌ Payment Reference submit failed!...",
                      details: "",
                    });

                    write({
                      output: JSON.stringify({
                        success: false,
                        status: "SUBMITTED_REF_ERR",
                        message: `Deposit record not updated with payment record - submission failed`,
                      }),
                    });
                  }
                }
              } catch (err) {
                log.error({
                  title: "❌ Error occurred submitting paygen reference:",
                  details: err,
                });

                write({
                  output: JSON.stringify({
                    success: false,
                    status: "ERROR_PAYMENT_REF_SUBMISSION",
                    ...err,
                  }),
                });
              }

              break;
            case "Auto":
              // Run non paytrace capture with settings provided by payment integration provider.
              runManualPayment(req, payload, parameters, true)
                .then((payRes) => {
                  log.audit({
                    title: "✅ Payment run completed",
                    details: payRes,
                  });
                  if (payRes?.success) {
                    write({
                      output: JSON.stringify(payRes),
                    });
                  } else {
                    write({
                      output: JSON.stringify(payRes),
                    });
                  }
                })
                .catch((err) => {
                  log.error({
                    title: "❌ Error running PayGen payment:",
                    details: err,
                  });
                  if (err.name === "PAYTRACE_ERROR") {
                    let reducedErrorMessage = "";

                    // Paytrace error messages come with their company name in them.
                    // We only want the message after that which contains "| |" to spilt - displaying the error correctly.

                    if (err.message.search(/ \|\| /) !== -1) {
                      reducedErrorMessage =
                        err.message.split(/ \|\| /)[1] ||
                        "Unknown card processor error";
                    } else {
                      reducedErrorMessage = err.message;
                    }

                    write({
                      output: JSON.stringify({
                        error: {
                          ...err,
                        },
                        name: "CREDIT_CARD_ERROR",
                        message: reducedErrorMessage,
                      }),
                    });
                  } else {
                    write({
                      output: JSON.stringify({
                        error: err,
                      }),
                    });
                  }
                });
              break;
            default:
              log.error({
                title: '❌ No payment "type" of capture specified:',
                details: `Provide one of the following: PayGen-Auto, PayGen-Manual, Auto.\nThis will determine payment logic.`,
              });
          }
        }
      },
      PATCH: () => {}, // Might have to push for a PATCH request if body cannot be sent
      DELETE: () => {},
    };

    try {
      log.audit({ title: "🚦 Running options...", details: `🔧 ${method}` });

      runOptions[method] && runOptions[method]();
    } catch (err) {
      log.error({ title: "❌ Error occurred during operation:", details: err });
      throw err;
    }
  };

  function orderCacheLoader() {
    let data = "hello world"; // Just get ACTUAL data using this function.
    return data;
  }

  function handlePaytraceProfileCacheLoader() {
    let debugMode = true;
    let paygenProfile = null;
    let payGenEnabled = CS_SETTINGS?.custrecord_ng_cs_enable_paytrace === "T";

    try {
      if (payGenEnabled) {
        if (!PT.settings.licenseInfo.valid) {
          log.error({
            title: "❗ PayGen license is invalid",
            details: "Please check license issuer",
          });
          return {
            status: "Paygen Processing: Invalid license",
            error: {
              name: "PAYGEN_LICENSE_INVALID",
              message: "Please provide a valid license for usage.",
            },
          };
        }

        paygenProfile = PT.settings.licenseInfo.valid && PT.data.GetProfile();

        if (debugMode) {
          log.debug({
            title: "🚧 Debug: Paytrace profile:",
            details: paygenProfile,
          });
        }

        // If profile is valid run token authentication to api and then incoming sales-order payment
        if (paygenProfile) {
          return paygenProfile;
        } else {
          log.error({
            title: "❗ Failed to fetch PayGen profile...",
            details: "Couldn't determine card processor",
          });

          if (debugMode) {
            log.debug({
              title: "🚧 Debug: Paytrace profile:",
              details: paygenProfile,
            });
          }

          return {
            status: "Paygen Processing: Invalid profile",
            error: {
              name: "PAYGEN_PROFILE_INVALID",
              message:
                "A valid PayGen profile is needed to proceed with processing...",
            },
          };
        }
      } else {
        log.audit({
          title: "🟡 Paygen disabled setting profile cache to false",
          details: "",
        });
        return false;
      }
    } catch (err) {
      log.error({
        title: "❌ Internal error occured processing payment:",
        details: err,
      });
      throw err;
    }
  }

  function handlePaytraceTokenCacheLoader() {
    let debugMode = true;
    let paygenProfile = null;
    let paygenToken = "";
    let payGenEnabled = CS_SETTINGS?.custrecord_ng_cs_enable_paytrace === "T";

    try {
      if (payGenEnabled) {
        if (!PT.settings.licenseInfo.valid) {
          log.error({
            title: "❗ PayGen license is invalid",
            details: "Please check license issuer",
          });
          return {
            status: "Paygen Processing: Invalid license",
            error: {
              name: "PAYGEN_LICENSE_INVALID",
              message: "Please provide a valid license for usage.",
            },
          };
        }

        paygenProfile = PT.settings.licenseInfo.valid && PT.data.GetProfile();

        if (debugMode) {
          log.debug({
            title: "🚧 Debug: Paytrace profile:",
            details: paygenProfile,
          });
        }

        // If profile is valid run token authentication to api and then incoming sales-order payment
        if (paygenProfile) {
          paygenToken = PT.comm.GetPayTraceAuth(paygenProfile);

          return paygenToken;
        } else {
          log.error({
            title: "❗ Failed to fetch PayGen profile...",
            details: "Couldn't determine card processor",
          });

          if (debugMode) {
            log.debug({
              title: "🚧 Debug: Paytrace profile:",
              details: paygenProfile,
            });
          }

          return {
            status: "Paygen Processing: Invalid profile",
            error: {
              name: "PAYGEN_PROFILE_INVALID",
              message:
                "A valid PayGen profile is needed to proceed with processing...",
            },
          };
        }
      } else {
        log.audit({
          title: "🟡 Paygen disabled setting token cache to false",
          details: "",
        });
        return false;
      }
    } catch (err) {
      log.error({
        title: "❌ Internal error occured processing payment:",
        details: err,
      });
      throw err;
    }
  }
  /**
   * The GET of the CS Settings fields and all its values
   * Not all multi select fields will show in results may have to use a search.lookup to get those values
   *
   * @type Function
   * @returns {Object} settings
   * */
  function getSettings() {
    log.audit({ title: "⚡ Running get settings...", details: "" });
    let settings = null;

    try {
      settings = query
        .runSuiteQL({
          query: `SELECT * FROM CUSTOMRECORD_NG_CS_SETTINGS WHERE ID = '1'`,
        })
        .asMappedResults()[0];

      settings
        ? log.audit({ title: "✅ Settings gathered!", details: settings })
        : log.error({ title: "❌ Error getting settings!", details: "" });
    } catch (err) {
      log.error({ title: "❌ Error occurred getting settings:", details: err });
    }

    return settings;
  }

  function runPayGenInit() {
    let payGenEnabled = CS_SETTINGS?.custrecord_ng_cs_enable_paytrace === "T";

    if (payGenEnabled) {
      PT.settings = PT.sttg.GetSettings(true);
      PAYTRACE_DATA = PT.data.GetIntegrationInfo();
    }

    PAYTRACE_PROFILE = PT_PROFILE_CACHE.get({
      key: "PT_PROFILE",
      loader: handlePaytraceProfileCacheLoader,
      ttl: 57600, // 16 hours
    });

    PAYTRACE_TOKEN = PT_TOKEN_CACHE.get({
      key: "PT_TOKEN",
      loader: handlePaytraceTokenCacheLoader,
      ttl: 57600, // 16 hours
    });
  }

  /**
   * Runs PayGen (Paytrace) payment to gateway and returns response from payment capture.
   * @param {Object} req
   * @param {Object} body
   * @param {Object} params
   * @param {Boolean} autoCapture
   * @returns {Object} - HTTP response body from paytrace or constructed error
   * */
  async function runPayGenPayment(req, body, params, autoCapture) {
    let debugMode = true;
    let paygenProfile = null;
    let paygenToken = "";
    let { clientIpAddress } = req;

    if (debugMode) {
      log.debug({
        title: "🟢 Debug mode active- CARD VALUES MAY BE DISPLAYED!",
        details: "Remember to turn off in a production setting!",
      });

      log.debug({ title: "🔎 Payload captured body:", details: body });
      log.debug({ title: "🔎 Payload captured params:", details: params });
    }

    try {
      if (!PT.settings.licenseInfo.valid) {
        log.error({
          title: "❗ PayGen license is invalid",
          details: "Please check license issuer",
        });
        return {
          status: "Paygen Processing: Invalid license",
          error: {
            name: "PAYGEN_LICENSE_INVALID",
            message: "Please provide a valid license for usage.",
          },
        };
      }

      if (debugMode) {
        log.debug({
          title: "🚧 Debug: Paytrace profile:",
          details: PAYTRACE_PROFILE,
        });
        log.debug({
          title: "🚧 Debug: Paytrace token :",
          details: PAYTRACE_TOKEN,
        });
      }

      // If profile is valid run token authentication to api and then incoming sales-order payment
      if (PAYTRACE_PROFILE && PAYTRACE_TOKEN) {
        if (autoCapture) {
          log.audit({
            title: "🟢 Auto payment capture enabled",
            details: "Running payment capture...",
          });
          if (body?.card?.id && body?.entity) {
            let cardId = body.card.id;
            let customerRecord = record.load({
              type: record.Type.CUSTOMER,
              id: body.entity,
            });

            let creditCardLine = customerRecord.findSublistLineWithValue({
              sublistId: "creditcards",
              fieldId: "internalid",
              value: cardId,
            });

            let billingAddressLine = customerRecord.findSublistLineWithValue({
              sublistId: "addressbook",
              fieldId: "internalid",
              value: body?.billingAddressId,
            });

            log.debug({
              title: "🟡 Billing address defined:",
              details: billingAddressLine,
            });

            let billingAddress = {};
            let billAddressSubrec = null;
            if (billingAddressLine !== -1) {
              billAddressSubrec = customerRecord.getSublistSubrecord({
                sublistId: "addressbook",
                fieldId: "addressbookaddress",
                line: billingAddressLine,
              });

              billingAddress.id = customerRecord.getSublistValue({
                sublistId: "addressbook",
                fieldId: "addressid",
                line: billingAddressLine,
              });
              billingAddress.label = customerRecord.getSublistValue({
                sublistId: "addressbook",
                fieldId: "label",
                line: billingAddressLine,
              });
              billingAddress.name = billAddressSubrec.getValue({
                fieldId: "addressee",
              });
              billingAddress.addressOne = billAddressSubrec.getValue({
                fieldId: "addr1",
              });
              billingAddress.phone = billAddressSubrec.getValue({
                fieldId: "phone",
              });
              billingAddress.country = billAddressSubrec.getValue({
                fieldId: "country",
              });
              billingAddress.state = billAddressSubrec.getValue({
                fieldId: "state",
              });
              billingAddress.city = billAddressSubrec.getValue({
                fieldId: "city",
              });
              billingAddress.zip = billAddressSubrec.getValue({
                fieldId: "zip",
              });

              log.debug({
                title: "🟡 Billing address made:",
                details: billingAddress,
              });
            }

            if (creditCardLine !== -1) {
              log.audit({
                title: "✅ Card is defined- running lookup",
                details: creditCardLine,
              });
              let cardNumber = customerRecord.getSublistValue({
                sublistId: "creditcards",
                fieldId: "ccnumber",
                line: creditCardLine,
              });
              let cardExpireDate = customerRecord.getSublistValue({
                sublistId: "creditcards",
                fieldId: "ccexpiredate",
                line: creditCardLine,
              });
              let cardPaymentMethod = customerRecord.getSublistValue({
                sublistId: "creditcards",
                fieldId: "paymentmethod",
                line: creditCardLine,
              });
              let cardName = customerRecord.getSublistValue({
                sublistId: "creditcards",
                fieldId: "ccname",
                line: creditCardLine,
              });

              let paymentCard = {
                cardNumber,
                cardExpireDate,
                cardPaymentMethod,
                cardName,
              };

              if (debugMode) {
                log.debug({
                  title: "🟡 Card payment object:",
                  details: paymentCard,
                });
              }

              log.audit({
                title: "Running paytrace enc card lookup...⌛",
                details: "",
              });

              let paytraceEncryptionCardSearch = search.create({
                type: "customrecord_ng_pt_ecrypted_card",
                filters: [
                  ["custrecord_ng_ptecd_customer", "anyof", body.entity],
                  "AND",
                  ["custrecord_ng_ptecd_card_id", "is", `${cardId}`],
                ],
                columns: [
                  search.createColumn({
                    name: "custrecord_ng_ptecd_encrypted_card",
                  }),
                  search.createColumn({
                    name: "custrecord_ng_ptecd_encypted_csc",
                  }),
                ],
              });
              let encryptedCards = [];
              let paytraceEncCardCount =
                paytraceEncryptionCardSearch.runPaged().count;

              getAllResultsFor(paytraceEncryptionCardSearch, (result) => {
                let encCardObj = {
                  encryptedCardNumber: result.getValue(
                    "custrecord_ng_ptecd_encrypted_card",
                  ),
                  encryptedCSC: result.getValue(
                    "custrecord_ng_ptecd_encypted_csc",
                  ),
                };

                encryptedCards.push(encCardObj);
              });

              log.audit({
                title: "🔎 Paytrace enc card results",
                details: paytraceEncCardCount !== 0 ? "✅" : "❌",
              });

              if (debugMode) {
                log.debug({
                  title: "🟡 Paytrace search results:",
                  details: encryptedCards,
                });
              }

              // Check if card is expired & encrypted card is available
              log.audit({
                title: "🔎 Checking if card is encrypted...",
                details: "",
              });

              if (paytraceEncCardCount !== 0) {
                log.audit({ title: "🟢 Card is encrypted...", details: "" });

                paymentCard.encryptedNumber =
                  encryptedCards[0].encryptedCardNumber;
                paymentCard.encryptedCSC = encryptedCards[0].encryptedCSC;

                if (paymentCard.cardExpireDate) {
                  log.audit({
                    title: "📅 Checking if card is expired...",
                    details: "",
                  });

                  let expirationParsed = new Date(paymentCard.cardExpireDate);
                  let expirationMonth = expirationParsed.getMonth() + 1;
                  let expirationYear = expirationParsed.getFullYear();
                  let today = new Date();
                  today.setHours(0, 0, 0, 0);
                  today.setDate(1);

                  log.debug({
                    title: "🟡 Payment expiration details:",
                    details: {
                      expirationParsed,
                      today,
                    },
                  });

                  if (today < expirationParsed) {
                    log.audit({
                      title: "✅ Card expiration is good!",
                      details: "Continuing with payment process...",
                    });
                    // Continue to run payment after expiration validated
                    /**
                     * Request needed for payment transmission
                     * */
                    let payGenCardRequest = {
                      amount: body.grandTotal,
                      tax_amount: body.tax,
                      credit_card: {
                        encrypted_number: paymentCard.encryptedNumber,
                        expiration_month: expirationMonth,
                        expiration_year: expirationYear,
                      },
                      billing_address: {
                        name: billingAddress.name,
                        street_address: billingAddress.addressOne,
                        zip: billingAddress.zip,
                      },
                      invoice_id: params.salesOrderId,
                      customer_reference_id: `CS Web Order - ${
                        body?.tranId
                      } - ${new Date().toLocaleDateString()}`,
                    };

                    // Send payment to paytrace
                    log.audit({
                      title: "Sending payment request...⌛",
                      details: "",
                    });

                    if (debugMode) {
                      log.audit({
                        title: "🟡 Payment request details",
                        details: payGenCardRequest,
                      });
                    }

                    PT.comm.GetPayTraceAuth(PAYTRACE_PROFILE);
                    let paymentRequest = PT.comm.SendCardPayment(
                      PAYTRACE_TOKEN,
                      payGenCardRequest,
                    );

                    log.audit({
                      title: "🟡 Payment response:",
                      details: paymentRequest,
                    });

                    if (paymentRequest?.success) {
                      // Payment successfully captured making return to operations to deliver message
                      log.audit({
                        title: "🟢 Payment captured successfully!",
                        details: "",
                      });
                      return {
                        status: "TRANSACTION_APPROVED",
                        success: paymentRequest.success,
                        message: paymentRequest.status_message,
                        payGen: {
                          httpReq: paymentRequest,
                          cardId: body.card.id,
                          cardMethod: paymentCard.cardPaymentMethod,
                          card: paymentRequest.masked_card_number,
                          approvalCode: paymentRequest.approval_code,
                          paymentId: paymentRequest.transaction_id,
                          billingAddressId: body.billingAddressId,
                          convenienceFee: body.convenienceFee,
                          amount: body.grandTotal,
                          entity: body.entity,
                          clientIp: clientIpAddress,
                        },
                      };
                    } else {
                      // Payment did not capture successfully will throw error to operations to deliver message
                      log.error({
                        title: "❗ Paygen capture error!",
                        details: paymentRequest.err,
                      });
                      throw error.create({
                        ...paymentRequest.err,
                      });
                    }
                    // End of payment capture and response
                  } else {
                    log.audit({
                      title: "🔴 Card is expired!",
                      details: "Terminating payment process...",
                    });
                    throw error.create({
                      name: "CREDIT_CARD_ERROR",
                      cause: `Card: "${paymentCard.cardName}" - ${paymentCard.cardNumber} is expired as of (${expirationMonth}/${expirationYear})`,
                      message:
                        "Expired Card: The form of payment used is currently expired please choose another form of payment",
                      notifyOff: false,
                    });
                  }
                }
              } else {
                // Log error as there needs to be a card encryption number to send to Paytrace in order to process the payment.
                log.error({
                  title: "❌ No Paygen encrypted card!",
                  details:
                    "Terminating payment as the card encryption record is needed in order to proceed!",
                });

                throw error.create({
                  name: "CREDIT_CARD_ERROR",
                  success: false,
                  cause: `Card: "${paymentCard.cardName}" - ${paymentCard.cardNumber} is not available for processing`,
                  message:
                    "No PayGen encrypted card: The form of payment used is currently not available for processing please choose another form of payment or retry adding card.",
                  notifyOff: false,
                });
              }
            } // Card line end
          } // Entity and body end
        } else {
          log.audit({
            title: "🟡 Manual payment capture enabled",
            details: "Running auth but no capture...",
          });

          if (body?.card?.id && body?.entity) {
            let cardId = body.card.id;
            let customerRecord = record.load({
              type: record.Type.CUSTOMER,
              id: body.entity,
            });

            let creditCardLine = customerRecord.findSublistLineWithValue({
              sublistId: "creditcards",
              fieldId: "internalid",
              value: cardId,
            });

            let billingAddressLine = customerRecord.findSublistLineWithValue({
              sublistId: "addressbook",
              fieldId: "internalid",
              value: body?.billingAddressId,
            });

            log.debug({
              title: "🟡 Billing address defined:",
              details: billingAddressLine,
            });

            let billingAddress = {};
            let billAddressSubrec = null;
            if (billingAddressLine !== -1) {
              billAddressSubrec = customerRecord.getSublistSubrecord({
                sublistId: "addressbook",
                fieldId: "addressbookaddress",
                line: billingAddressLine,
              });

              billingAddress.id = customerRecord.getSublistValue({
                sublistId: "addressbook",
                fieldId: "addressid",
                line: billingAddressLine,
              });
              billingAddress.label = customerRecord.getSublistValue({
                sublistId: "addressbook",
                fieldId: "label",
                line: billingAddressLine,
              });
              billingAddress.name = billAddressSubrec.getValue({
                fieldId: "addressee",
              });
              billingAddress.addressOne = billAddressSubrec.getValue({
                fieldId: "addr1",
              });
              billingAddress.phone = billAddressSubrec.getValue({
                fieldId: "phone",
              });
              billingAddress.country = billAddressSubrec.getValue({
                fieldId: "country",
              });
              billingAddress.state = billAddressSubrec.getValue({
                fieldId: "state",
              });
              billingAddress.city = billAddressSubrec.getValue({
                fieldId: "city",
              });
              billingAddress.zip = billAddressSubrec.getValue({
                fieldId: "zip",
              });

              log.debug({
                title: "🟡 Billing address made:",
                details: billingAddress,
              });
            }

            if (creditCardLine !== -1) {
              log.audit({
                title: "✅ Card is defined- running lookup",
                details: creditCardLine,
              });
              let cardNumber = customerRecord.getSublistValue({
                sublistId: "creditcards",
                fieldId: "ccnumber",
                line: creditCardLine,
              });
              let cardExpireDate = customerRecord.getSublistValue({
                sublistId: "creditcards",
                fieldId: "ccexpiredate",
                line: creditCardLine,
              });
              let cardPaymentMethod = customerRecord.getSublistValue({
                sublistId: "creditcards",
                fieldId: "paymentmethod",
                line: creditCardLine,
              });
              let cardName = customerRecord.getSublistValue({
                sublistId: "creditcards",
                fieldId: "ccname",
                line: creditCardLine,
              });

              let paymentCard = {
                cardNumber,
                cardExpireDate,
                cardPaymentMethod,
                cardName,
              };

              if (debugMode) {
                log.debug({
                  title: "🟡 Card payment object:",
                  details: paymentCard,
                });
              }

              log.audit({
                title: "Running paytrace enc card lookup...⌛",
                details: "",
              });

              let paytraceEncryptionCardSearch = search.create({
                type: "customrecord_ng_pt_ecrypted_card",
                filters: [
                  ["custrecord_ng_ptecd_customer", "anyof", [body.entity]],
                  "and",
                  ["custrecord_ng_ptecd_card_id", "is", `${cardId}`],
                ],
                columns: [
                  search.createColumn({
                    name: "custrecord_ng_ptecd_encrypted_card",
                  }),
                  search.createColumn({
                    name: "custrecord_ng_ptecd_encypted_csc",
                  }),
                ],
              });
              let encryptedCards = [];
              let paytraceEncCardCount =
                paytraceEncryptionCardSearch.runPaged().count;

              getAllResultsFor(paytraceEncryptionCardSearch, (result) => {
                let encCardObj = {
                  encryptedCardNumber: result.getValue(
                    "custrecord_ng_ptecd_encrypted_card",
                  ),
                  encryptedCSC: result.getValue(
                    "custrecord_ng_ptecd_encypted_csc",
                  ),
                };

                encryptedCards.push(encCardObj);
              });

              log.audit({
                title: "🔎 Paytrace enc card results",
                details: paytraceEncCardCount !== 0 ? "✅" : "❌",
              });

              if (debugMode) {
                log.debug({
                  title: "🟡 Paytrace search results:",
                  details: encryptedCards,
                });
              }

              // TODO check enc card and run payment
              // Check if card is expired & encrypted card is available
              log.audit({
                title: "🔎 Checking if card is encrypted...",
                details: "",
              });

              if (paytraceEncCardCount !== 0) {
                log.audit({ title: "🟢 Card is encrypted...", details: "" });

                paymentCard.encryptedNumber =
                  encryptedCards[0].encryptedCardNumber;
                paymentCard.encryptedCSC = encryptedCards[0].encryptedCSC;

                if (paymentCard.cardExpireDate) {
                  log.audit({
                    title: "📅 Checking if card is expired...",
                    details: "",
                  });

                  let expirationParsed = new Date(paymentCard.cardExpireDate);
                  let expirationMonth = expirationParsed.getMonth() + 1;
                  let expirationYear = expirationParsed.getFullYear();
                  let today = new Date();
                  today.setHours(0, 0, 0, 0);
                  today.setDate(1);

                  log.debug({
                    title: "🟡 Payment expiration details:",
                    details: {
                      expirationParsed,
                      today,
                    },
                  });

                  if (today < expirationParsed) {
                    log.audit({
                      title: "✅ Card expiration is good!",
                      details: "Continuing with payment process...",
                    });
                    // Continue to run payment after expiration validated
                    /**
                     * Request needed for payment transmission
                     * */
                    let payGenCardRequest = {
                      amount: body.grandTotal,
                      tax_amount: body.tax,
                      credit_card: {
                        encrypted_number: paymentCard.encryptedNumber,
                        expiration_month: expirationMonth,
                        expiration_year: expirationYear,
                      },
                      billing_address: {
                        name: billingAddress.name,
                        street_address: billingAddress.addressOne,
                        zip: billingAddress.zip,
                      },
                      invoice_id: params.salesOrderId,
                      customer_reference_id: `CS Web Order - ${
                        body?.tranId
                      } - ${new Date().toLocaleDateString()}`,
                    };

                    // Send payment to paytrace
                    log.audit({
                      title: "Sending payment request...⌛",
                      details: "",
                    });

                    if (debugMode) {
                      log.audit({
                        title: "🟡 Payment request details",
                        details: payGenCardRequest,
                      });
                    }

                    PT.comm.GetPayTraceAuth(PAYTRACE_PROFILE);
                    let paymentRequest = PT.comm.AuthCardPayment(
                      PAYTRACE_TOKEN,
                      payGenCardRequest,
                    );

                    log.audit({
                      title: "🟡 Payment response:",
                      details: paymentRequest,
                    });

                    if (paymentRequest?.success) {
                      // Payment successfully captured making return to operations to deliver message
                      log.audit({
                        title: "🟢 Payment captured successfully!",
                        details: "",
                      });
                      return {
                        status: "TRANSACTION_APPROVED",
                        success: paymentRequest.success,
                        message: paymentRequest.status_message,
                        payGen: {
                          httpReq: paymentRequest,
                          cardId: body.card.id,
                          cardMethod: paymentCard.cardPaymentMethod,
                          card: paymentRequest.masked_card_number,
                          approvalCode: paymentRequest.approval_code,
                          paymentId: paymentRequest.transaction_id,
                          billingAddressId: body.billingAddressId,
                          convenienceFee: body.convenienceFee,
                          amount: body.grandTotal,
                          entity: body.entity,
                          clientIp: clientIpAddress,
                        },
                      };
                    } else {
                      // Payment did not capture successfully will throw error to operations to deliver message
                      log.error({
                        title: "❗ Paygen capture error!",
                        details: paymentRequest.err,
                      });
                      throw error.create({
                        ...paymentRequest.err,
                      });
                    }
                    // End of payment capture and response
                  } else {
                    log.audit({
                      title: "🔴 Card is expired!",
                      details: "Terminating payment process...",
                    });
                    throw error.create({
                      name: "CREDIT_CARD_ERROR",
                      cause: `Card: "${paymentCard.cardName}" - ${paymentCard.cardNumber} is expired as of (${expirationMonth}/${expirationYear})`,
                      message:
                        "Expired Card: The form of payment used is currently expired please choose another form of payment",
                      notifyOff: false,
                    });
                  }
                }
              } else {
                // Log error as there needs to be a card encryption number to send to Paytrace in order to process the payment.
                log.error({
                  title: "❌ No Paygen encrypted card!",
                  details:
                    "Terminating payment as the card encryption record is needed in order to proceed!",
                });
              }
            } // Card line end
          } // Entity and body end
        }
      } else {
        log.error({
          title: "❗ Failed to fetch PayGen profile...",
          details: "Couldn't determine card processor",
        });

        if (debugMode) {
          log.debug({
            title: "🚧 Debug: Paytrace profile:",
            details: paygenProfile,
          });
        }

        return {
          status: "Paygen Processing: Invalid profile",
          error: {
            name: "PAYGEN_PROFILE_INVALID",
            message:
              "A valid PayGen profile is needed to proceed with processing...",
          },
        };
      }
    } catch (err) {
      log.error({
        title: "❌ Internal error occurred processing payment:",
        details: err,
      });
      throw err;
    }
  }

  /**
   * Runs PayGen (Paytrace) payment to gateway and returns response from payment capture.
   * @param {Object} req
   * @param {Object} body
   * @param {Object} params
   * @param {Boolean} autoCapture
   * @returns {Object} - HTTP response body from paytrace or constructed error
   * */
  async function runManualPayment(req, body, params, autoCapture) {
    let debugMode = true;
    let { clientIpAddress } = req;

    if (debugMode) {
      log.debug({
        title: "🟢 Debug mode active- CARD VALUES MAY BE DISPLAYED!",
        details: "Remember to turn off in a production setting!",
      });

      log.debug({ title: "🔎 Payload captured body:", details: body });
      log.debug({ title: "🔎 Payload captured params:", details: params });
    }

    try {
      if (debugMode) {
        log.debug({
          title: "🚧 Debug: Paytrace profile:",
          details: PAYTRACE_PROFILE,
        });
        log.debug({
          title: "🚧 Debug: Paytrace token :",
          details: PAYTRACE_TOKEN,
        });
      }

      // If profile is valid run token authentication to api and then incoming sales-order payment
      if (autoCapture) {
        log.audit({
          title: "🟢 Auto payment capture enabled",
          details: "Running payment capture...",
        });
        if (body?.card?.id && body?.entity) {
          let cardId = body.card.id;
          let customerRecord = record.load({
            type: record.Type.CUSTOMER,
            id: body.entity,
          });

          let creditCardLine = customerRecord.findSublistLineWithValue({
            sublistId: "creditcards",
            fieldId: "internalid",
            value: cardId,
          });

          let billingAddressLine = customerRecord.findSublistLineWithValue({
            sublistId: "addressbook",
            fieldId: "internalid",
            value: body?.billingAddressId,
          });

          log.debug({
            title: "🟡 Billing address defined:",
            details: billingAddressLine,
          });

          let billingAddress = {};
          let billAddressSubrec = null;
          if (billingAddressLine !== -1) {
            billAddressSubrec = customerRecord.getSublistSubrecord({
              sublistId: "addressbook",
              fieldId: "addressbookaddress",
              line: billingAddressLine,
            });

            billingAddress.id = customerRecord.getSublistValue({
              sublistId: "addressbook",
              fieldId: "addressid",
              line: billingAddressLine,
            });
            billingAddress.label = customerRecord.getSublistValue({
              sublistId: "addressbook",
              fieldId: "label",
              line: billingAddressLine,
            });
            billingAddress.name = billAddressSubrec.getValue({
              fieldId: "addressee",
            });
            billingAddress.addressOne = billAddressSubrec.getValue({
              fieldId: "addr1",
            });
            billingAddress.phone = billAddressSubrec.getValue({
              fieldId: "phone",
            });
            billingAddress.country = billAddressSubrec.getValue({
              fieldId: "country",
            });
            billingAddress.state = billAddressSubrec.getValue({
              fieldId: "state",
            });
            billingAddress.city = billAddressSubrec.getValue({
              fieldId: "city",
            });
            billingAddress.zip = billAddressSubrec.getValue({
              fieldId: "zip",
            });

            log.debug({
              title: "🟡 Billing address made:",
              details: billingAddress,
            });
          }

          if (creditCardLine !== -1) {
            log.audit({
              title: "✅ Card is defined- running lookup",
              details: creditCardLine,
            });
            let cardNumber = customerRecord.getSublistValue({
              sublistId: "creditcards",
              fieldId: "ccnumber",
              line: creditCardLine,
            });
            let cardExpireDate = customerRecord.getSublistValue({
              sublistId: "creditcards",
              fieldId: "ccexpiredate",
              line: creditCardLine,
            });
            let cardPaymentMethod = customerRecord.getSublistValue({
              sublistId: "creditcards",
              fieldId: "paymentmethod",
              line: creditCardLine,
            });
            let cardName = customerRecord.getSublistValue({
              sublistId: "creditcards",
              fieldId: "ccname",
              line: creditCardLine,
            });

            let paymentCard = {
              cardNumber,
              cardExpireDate,
              cardPaymentMethod,
              cardName,
            };

            if (debugMode) {
              log.debug({
                title: "🟡 Card payment object:",
                details: paymentCard,
              });
            }

            log.audit({
              title: "Running paytrace enc card lookup...⌛",
              details: "",
            });

            // TODO check enc card and run payment
            // Check if card is expired & encrypted card is available

            if (paymentCard.cardExpireDate) {
              log.audit({
                title: "📅 Checking if card is expired...",
                details: "",
              });

              let expirationParsed = new Date(paymentCard.cardExpireDate);
              let expirationMonth = expirationParsed.getMonth() + 1;
              let expirationYear = expirationParsed.getFullYear();
              let today = new Date();
              today.setHours(0, 0, 0, 0);
              today.setDate(1);

              log.debug({
                title: "🟡 Payment expiration details:",
                details: {
                  expirationParsed,
                  today,
                },
              });

              if (today < expirationParsed) {
                log.audit({
                  title: "✅ Card expiration is good!",
                  details: "Continuing with payment process...",
                });
                // Continue to run payment after expiration validated

                /** * * * * * * * * * * * * * * * * * * * * *
                 * Request needed for payment transmission  *
                 * * * * * * * * * * * * * * * * * * * * * **/
                let payGenCardRequest = {
                  amount: body.grandTotal,
                  tax_amount: body.tax,
                  credit_card: {
                    encrypted_number: paymentCard.encryptedNumber,
                    expiration_month: expirationMonth,
                    expiration_year: expirationYear,
                  },
                  billing_address: {
                    name: billingAddress.name,
                    street_address: billingAddress.addressOne,
                    zip: billingAddress.zip,
                  },
                  invoice_id: params.salesOrderId,
                  customer_reference_id: `CS Web Order - ${
                    body?.tranId
                  } - ${new Date().toLocaleDateString()}`,
                };

                // Send payment to paytrace
                log.audit({
                  title: "Sending payment request...⌛",
                  details: "",
                });

                if (debugMode) {
                  log.audit({
                    title: "🟡 Payment request details",
                    details: payGenCardRequest,
                  });
                }

                // Payment successfully captured making return to operations to deliver message
                log.audit({
                  title: "🟢 Payment captured successfully!",
                  details: "",
                });
                return {
                  status: "TRANSACTION_BYPASSED",
                  success: true,
                  message: "Auto non paygen transaction bypassing",
                  payGen: {
                    httpReq: null,
                    cardId: body.card.id,
                    cardMethod: paymentCard.cardPaymentMethod,
                    card: "Card Masked",
                    billingAddressId: body.billingAddressId,
                    convenienceFee: body.convenienceFee,
                    amount: body.grandTotal,
                    entity: body.entity,
                    clientIp: clientIpAddress,
                  },
                };
                // End of payment capture and response
              } else {
                log.audit({
                  title: "🔴 Card is expired!",
                  details: "Terminating payment process...",
                });

                throw error.create({
                  name: "CREDIT_CARD_ERROR",
                  cause: `Card: "${paymentCard.cardName}" - ${paymentCard.cardNumber} is expired as of (${expirationMonth}/${expirationYear})`,
                  message:
                    "Expired Card: The form of payment used is currently expired please choose another form of payment",
                  notifyOff: false,
                });
              }
            }
          } // Card line end
        } // Entity and body end
      } else {
        log.audit({
          title: "🟡 Manual payment capture enabled",
          details: "Running auth but no capture...",
        });
      }
    } catch (err) {
      log.error({
        title: "❌ Internal error occurred processing payment:",
        details: err,
      });
      throw err;
    }
  }
  /**
   * Runs a search retrieving all results and into a callback function as a Search.ResultSet
   * @type Function
   * @param {Search} searchObj
   * @param {Function} callback
   * @returns void
   * */
  function getAllResultsFor(searchObj, callback) {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  }

  return { onRequest };
});
