/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 */
define(['N/record', 'N/render', 'N/search', 'N/file','N/redirect', 'N/ui/serverWidget','N/runtime','../lib/newgen.library.v21'],
    /**
     * @param{record} record
     * @param{render} render
     * @param{search} search
     * @param(file) file
     */
    (record, render, search,file,redirect,serverWidget,runtime, NG) => {
        /**
         * Defines the Suitelet script trigger point.
         * @param {Object} scriptContext
         * @param {ServerRequest} scriptContext.request - Incoming request
         * @param {ServerResponse} scriptContext.response - Suitelet response
         * @since 2015.2
         */
        const onRequest = (scriptContext) => {

            if(scriptContext.request.method == 'GET'){

                displayForm(scriptContext);

            }else if(scriptContext.request.method == 'POST'){

                doWork(scriptContext);

            }

        }

        function displayForm(scriptContext){

            let scriptObj = runtime.getCurrentScript();

            let formTitle = scriptObj.getParameter({name: 'custscript_ng_cs_gen_est_form_title'}) || 'Generate Estimate';

            let form = serverWidget.createForm({
                title: formTitle
            });

            form.addSubmitButton({
                label: 'Submit'
            });

            form.addButton({
                id: 'custpage_reset_form',
                label: 'Reset',
                functionName: 'resetForm'
            });

            let params = scriptContext.request.parameters;

            log.debug({title: 'Params', details: JSON.stringify(params)});

            let eventID = params.eventID;

            log.debug({title: 'Event ID', details: eventID});

            form.clientScriptModulePath = '../Client/ng_cs_clientCSEventGenEstimate.js';

            var scriptID = scriptObj.id;

            var scriptDeploymentID = scriptObj.deploymentId;

            var scriptIDField = form.addField({
                id: 'custpage_script_id',
                type: 'text',
                label: 'Script ID'
            }).updateDisplayType({
                displayType: 'Hidden'
            });

            scriptIDField.defaultValue = scriptID;

            var scriptDeploymentIDField = form.addField({
                id: 'custpage_script_deployment_id',
                type: 'text',
                label: 'Script Deployment ID'
            }).updateDisplayType({
                displayType: 'Hidden'
            });

            scriptDeploymentIDField.defaultValue = scriptDeploymentID;

            //Search for Event Sessions

            let sessionsFilters = [
                ["custrecord_ng_cs_session_event","anyof",eventID],
                "AND",
                ["isinactive","is","F"]
            ]

            if(!NG.tools.isEmpty(params.vers)){
                sessionsFilters.push("AND");
                sessionsFilters.push(["custrecord_ng_cs_session_version","contains",params.vers]);
            }

            let sessionsColumns = [
                search.createColumn({name: 'internalid', label: 'Internal ID'}),
                search.createColumn({
                    name: "name",
                    sort: search.Sort.ASC,
                    label: "Name"
                }),
                search.createColumn({name: "custrecord_ng_cs_session_title", label: "Title"}),
                search.createColumn({name: "custrecord_ng_cs_session_version", label: "Version"}),
                search.createColumn({name: "custrecord_ng_cs_session_type", label: "Session Type"}),
                search.createColumn({name: "custrecord_ng_cs_session_venue", label: "Venue"}),
                search.createColumn({
                    name: "custrecord_ng_cs_space_name",
                    join: "CUSTRECORD_NG_CS_SESSION_VENUE_SPACE",
                    label: "Sales Name"
                }),
                search.createColumn({name: "custrecord_ng_cs_session_start_date", label: "Start Date"}),
                search.createColumn({name: "custrecord_ng_cs_session_start_time", label: "Start Time"}),
                search.createColumn({name: "custrecord_ng_cs_session_end_date", label: "End Date"}),
                search.createColumn({name: "custrecord_ng_cs_session_end_time", label: "End Time"})
            ]

            let sessionsResults = NG.tools.getSearchResultsAdv({
                type: 'customrecord_ng_cs_event_function',
                filters: sessionsFilters,
                columns: sessionsColumns
            });

            let sessionVersions = [];

            if(!NG.tools.isEmpty(sessionsResults) && sessionsResults.length > 0){

                for(let i=0; i < sessionsResults.length; i++){

                    let sessionVersion = sessionsResults[i].getValue({name: 'custrecord_ng_cs_session_version'});

                    if(!NG.tools.isEmpty(sessionVersion) && sessionVersions.indexOf(sessionVersion) == -1){

                        sessionVersions.push(sessionVersion);

                    }
                }
            }
            let eventInfoGroup = form.addFieldGroup({
                id: 'custpage_event_info',
                label: 'Event Information'
            });

            let eventField = form.addField({
                type: 'select',
                label: 'Event',
                id: 'custpage_event',
                source: 'customrecord_show',
                container: 'custpage_event_info'
            })

            eventField.defaultValue = eventID;

            eventField.updateDisplayType({
                displayType: 'inline'
            });

            let filtersGroup = form.addFieldGroup({
                id: 'custpage_session_filters',
                label: 'Filters'
            });

            let versionFilterField = form.addField({
                id: 'custpage_session_version',
                label: 'Version',
                type: 'select',
                container: 'custpage_session_filters'
            });

            versionFilterField.setHelpText({help: 'Select Version to Filter Sessions List'});

            versionFilterField.addSelectOption({
                value: '',
                text: ''
            });

            for(let i=0; i < sessionVersions.length; i++){

                versionFilterField.addSelectOption({
                    value: sessionVersions[i],
                    text: sessionVersions[i]
                });

            }

            if(!NG.tools.isEmpty(params.vers)){

                versionFilterField.defaultValue = params.vers;

                if(!NG.tools.isEmpty(sessionsResults) && sessionsResults.length > 0){

                    sessionsResults = sessionsResults.filter(function(result){return result.getValue({name: 'custrecord_ng_cs_session_version'}) == params.vers});

                }

            }

            let sessionsList = form.addSublist({
                id: 'custpage_sessions_list',
                label: 'Sessions',
                type: 'LIST'
            });

            let markAllButtons = sessionsList.addMarkAllButtons();

            let selectAllButton = markAllButtons[0].label="Select All";

            let selectedField = sessionsList.addField({
                id: 'selected',
                label: 'Select',
                type: 'checkbox'
            });

            let sessionIDField = sessionsList.addField({
                id: 'custpage_session_id',
                label: 'Internal ID',
                type: 'integer'
            }).updateDisplayType({
                displayType: 'HIDDEN'
            });

            let sessionNameField = sessionsList.addField({
                id: 'custpage_session_name',
                label: 'Name',
                type: 'text'
            });

            let sessionTitleField = sessionsList.addField({
                id: 'custpage_session_title',
                label: 'Title',
                type: 'text'
            });

            let sessionVersionField = sessionsList.addField({
                id: 'custpage_session_version',
                label: 'Version',
                type: 'text'
            });

            let sessionTypeField = sessionsList.addField({
                id: 'custpage_session_type',
                label: 'Type',
                type: 'select',
                source: 'customlist_ng_cs_session_type'
            }).updateDisplayType({
                displayType: 'inline'
            });

            let sessionVenueField = sessionsList.addField({
                id: 'custpage_session_venue',
                label: 'Venue',
                type: 'select',
                source: 'customrecord_facility'
            }).updateDisplayType({
                displayType: 'inline'
            });

            let sessionVenueSpace = sessionsList.addField({
                id: 'custpage_session_venue_space',
                label: 'Room',
                type: 'text'
            });

            let sessionStartDateField = sessionsList.addField({
                id: 'custpage_session_start_date',
                label: 'Start Date',
                type: 'date'
            });

            let sessionStartTimeField = sessionsList.addField({
                id: 'custpage_session_start_time',
                label: 'Start Time',
                type: 'TIMEOFDAY'
            });

            let sessionEndDateField = sessionsList.addField({
                id: 'custpage_session_end_date',
                label: 'End Date',
                type: 'date'
            });

            let sessionEndTimeField = sessionsList.addField({
                id: 'custpage_session_end_time',
                label: 'End Time',
                type: 'TIMEOFDAY'
            });

            if(!NG.tools.isEmpty(sessionsResults) && sessionsResults.length > 0){

                for(let i=0; i < sessionsResults.length; i++){

                    let sessionData = {};

                    sessionData.id = sessionsResults[i].getValue({name: 'internalid'});

                    sessionData.name = sessionsResults[i].getValue({name: 'name'});

                    sessionData.title = sessionsResults[i].getValue({name: 'custrecord_ng_cs_session_title'});

                    sessionData.version = sessionsResults[i].getValue({name: 'custrecord_ng_cs_session_version'});

                    sessionData.type = sessionsResults[i].getValue({name: 'custrecord_ng_cs_session_type'});

                    sessionData.venue = sessionsResults[i].getValue({name: 'custrecord_ng_cs_session_venue'});

                    sessionData.venueSpace = sessionsResults[i].getValue({name: 'custrecord_ng_cs_space_name', join: 'CUSTRECORD_NG_CS_SESSION_VENUE_SPACE'});

                    sessionData.startDate = sessionsResults[i].getValue({name: 'custrecord_ng_cs_session_start_date'});

                    sessionData.startTime = sessionsResults[i].getValue({name: 'custrecord_ng_cs_session_start_time'});

                    sessionData.endDate = sessionsResults[i].getValue({name: 'custrecord_ng_cs_session_end_date'});

                    sessionData.endTime = sessionsResults[i].getValue({name: 'custrecord_ng_cs_session_end_time'});

                    sessionData.id && sessionsList.setSublistValue({
                        id: 'custpage_session_id',
                        value: sessionData.id,
                        line: i
                    });

                    sessionData.name && sessionsList.setSublistValue({
                        id: 'custpage_session_name',
                        value: sessionData.name,
                        line: i
                    });

                    sessionData.title && sessionsList.setSublistValue({
                        id: 'custpage_session_title',
                        value: sessionData.title,
                        line: i
                    });

                    sessionData.version && sessionsList.setSublistValue({
                        id: 'custpage_session_version',
                        value: sessionData.version,
                        line: i
                    });

                    sessionData.type && sessionsList.setSublistValue({
                        id: 'custpage_session_type',
                        value: sessionData.type,
                        line: i
                    });

                    sessionData.venue && sessionsList.setSublistValue({
                        id: 'custpage_session_venue',
                        value: sessionData.venue,
                        line: i
                    });

                    sessionData.venueSpace && sessionsList.setSublistValue({
                        id: 'custpage_session_venue_space',
                        value: sessionData.venueSpace,
                        line: i
                    });

                    sessionData.startDate && sessionsList.setSublistValue({
                        id: 'custpage_session_start_date',
                        value: sessionData.startDate,
                        line: i
                    });

                    sessionData.startTime && sessionsList.setSublistValue({
                        id: 'custpage_session_start_time',
                        value: sessionData.startTime,
                        line: i
                    });

                    sessionData.endDate && sessionsList.setSublistValue({
                        id: 'custpage_session_end_date',
                        value: sessionData.endDate,
                        line: i
                    });

                    sessionData.endTime && sessionsList.setSublistValue({
                        id: 'custpage_session_end_time',
                        value: sessionData.endTime,
                        line: i
                    });
                }

            }



            scriptContext.response.writePage({
                pageObject: form
            });

        }

        function doWork(scriptContext){

            let responseObj = {};

            let request = scriptContext.request;

            var params = scriptContext.request.parameters;

            log.debug({title: 'Params', details: JSON.stringify(params)});

            var eventID = params.custpage_event;

            let lineCount = scriptContext.request.getLineCount('custpage_sessions_list');

            log.debug({title: 'Sessions Line Count', details: lineCount});

            let selectedSessionIDs = [];

            for(let i=0; i < lineCount; i++){

                let selected = request.getSublistValue({
                    group: 'custpage_sessions_list',
                    name: 'selected',
                    line: i
                });

                log.debug({title: 'Line ' + i + ' Selected', details: selected});

                if(selected == "T" || (!NG.tools.isEmpty(selected) && selected == "true")){

                    let sessionID = request.getSublistValue({
                        group: 'custpage_sessions_list',
                        name: 'custpage_session_id',
                        line: i
                    });



                    if(!NG.tools.isEmpty(sessionID) && selectedSessionIDs.indexOf(sessionID) == -1){

                        selectedSessionIDs.push(sessionID);

                    }

                }

            }

            log.debug({title: 'Selected Session IDs', details: selectedSessionIDs});

            if(!isEmpty(eventID)){

                var eventRec = record.load({
                    type: 'customrecord_show',
                    id: eventID,
                    isDynamic: true
                });

                var eventPaymentTerms = eventRec.getValue({fieldId: 'custrecord_terms'});

                var versionCount = eventRec.getValue({
                    fieldId: 'custrecord_ng_cs_event_est_vers_count'
                });

                versionCount = versionCount+1;

                eventRec.setValue({
                    fieldId: 'custrecord_ng_cs_event_est_vers_count',
                    value: versionCount
                });

                var venueID = eventRec.getValue({fieldId: 'custrecord_facility'});

                var venueRec = record.load({
                    type: 'customrecord_facility',
                    id: venueID,
                    isDynamic: true
                });

                var venueCurrency = venueRec.getValue({fieldId: 'custrecord_ng_cs_venue_currency'}) || 1;

                var locationID = venueRec.getValue({
                    fieldId: 'custrecord_ng_cs_facility_assoc_location'
                });

                var locationRec = record.load({
                    type: 'location',
                    id: locationID,
                    isDynamic: true
                });

                var locationAddressMainKey = locationRec.getValue({fieldId: 'mainaddress_key'});


                var locationAddressFilters = null;
                var locationAddressColumns = null;
                var locationAddressResults = null;

                locationAddressFilters = [
                    ["internalid","anyof",locationAddressMainKey]
                ];

                locationAddressColumns = [
                    search.createColumn({
                        name: "internalid",
                        label: "Internal ID"
                    }),
                    search.createColumn({
                        name: "addressee",
                        label: "Addressee"
                    }),
                    search.createColumn({
                        name: "attention",
                        label: "Attention"
                    }),
                    search.createColumn({
                        name: "address1",
                        label: "Address1"
                    }),
                    search.createColumn({
                        name: "address2",
                        label: "Address2"
                    }),
                    search.createColumn({
                        name: "address3",
                        label: "Address3"
                    }),
                    search.createColumn({
                        name: "city",
                        label: "City"
                    }),
                    search.createColumn({
                        name: "state",
                        label: "State"
                    }),
                    search.createColumn({
                        name: "zip",
                        label: "Zip"
                    }),
                    search.createColumn({
                        name: "country",
                        label: "Country"
                    }),
                    search.createColumn({
                        name: "countrycode",
                        label: "Country Code"
                    }),
                    search.createColumn({
                        name: 'phone',
                        label: 'Phone'
                    })
                ]

                locationAddressResults = NG.tools.getSearchResults('address',locationAddressFilters,locationAddressColumns);

                if(!NG.tools.isEmpty(locationAddressResults) && locationAddressResults.length > 0){

                    var venueAddress1 = locationAddressResults[0].getValue({name: 'address1'});

                    var venueAddress2 = locationAddressResults[0].getValue({name: 'address2'});

                    var venueCity = locationAddressResults[0].getValue({name: 'city'});

                    var venueStateName = locationAddressResults[0].getValue({name: 'state'});

                    var venueZip = locationAddressResults[0].getValue({name: 'zip'});

                    var venueCountryCode = locationAddressResults[0].getValue({name: 'countrycode'});

                    var venueCountryID = locationAddressResults[0].getValue({name: 'country'});

                    var venueCountryName = locationAddressResults[0].getValue({name: 'country'});


                }



                var taxGroupID = eventRec.getValue({
                    fieldId: 'custrecord_tax_rate'
                });

                var serviceFeeItem = venueRec.getValue({
                    fieldId: 'custrecord_ng_cs_facility_serv_fee_item'
                });

                var serviceFeeRate = venueRec.getValue({
                    fieldId: 'custrecord_ng_cs_facility_service_fee'
                })

                //Search Event Venue Discounts
                let itemDiscountsFilters = [
                    ["custrecord_ng_cs_event_discount_event","anyof",eventID],
                    "AND",
                    ["custrecord_ng_cs_event_discount_venues","anyof",venueID]
                ]

                let itemDiscountsColumns = [
                    search.createColumn({name: "internalid", label: "Internal ID"}),
                    search.createColumn({
                        name: "name",
                        sort: search.Sort.ASC,
                        label: "Name"
                    }),
                    search.createColumn({name: "custrecord_ng_cs_event_discount_item", label: "Discount Item"}),
                    search.createColumn({name: "custrecord_ng_cs_event_discount_tiers", label: "Service Tiers"}),
                    search.createColumn({name: "custrecord_ng_cs_event_discount_venues", label: "Venues"}),
                    search.createColumn({
                        name: "baseprice",
                        join: "CUSTRECORD_NG_CS_EVENT_DISCOUNT_ITEM",
                        label: "Base Price"
                    }),
                    search.createColumn({name: "custrecord_ng_discount_rate", label: "Discount Rate"})
                ]

                let itemDiscountsResults = getSearchResults('customrecord_ng_cs_event_discount', itemDiscountsFilters, itemDiscountsColumns);

                //Search For Event Sessions

                let eventSessionsSearchFilters = [
                    ["isinactive","is","F"],
                    "AND",
                    ["custrecord_ng_cs_session_event","anyof",[eventID]]
                ]

                let eventSessionsSearchColumns = [
                    search.createColumn({name: 'internalid',label: 'Internal ID'}),
                    search.createColumn({
                        name: "name",
                        sort: search.Sort.ASC,
                        label: "Name"
                    }),
                    search.createColumn({name: "custrecord_ng_cs_session_title", label: "Title"}),
                    search.createColumn({name: "custrecord_ng_cs_session_event", label: "CS Event"}),
                    search.createColumn({name: "custrecord_ng_cs_session_booking", label: "CS Event Booking"}),
                    search.createColumn({name: "custrecord_ng_cs_session_type", label: "Session Type"}),
                    search.createColumn({name: "custrecord_ng_cs_session_venue", label: "Venue"}),
                    search.createColumn({name: "custrecord_ng_cs_session_venue_space", label: "Venue Space"}),
                    search.createColumn({
                        name: "custrecord_ng_cs_space_name",
                        join: "CUSTRECORD_NG_CS_SESSION_VENUE_SPACE",
                        label: "Sales Name"
                    }),
                    search.createColumn({name: "custrecord_ng_cs_session_start_date", label: "Start Date"}),
                    search.createColumn({name: "custrecord_ng_cs_session_start_time", label: "Start Time"}),
                    search.createColumn({name: "custrecord_ng_cs_session_end_date", label: "End Date"}),
                    search.createColumn({name: "custrecord_ng_cs_session_end_time", label: "End Time"}),
                    search.createColumn({name: "custrecord_ng_cs_session_description", label: "Description"}),
                    search.createColumn({name: "custrecord_ng_cs_session_contact", label: "Session Contact"}),
                    search.createColumn({name: "custrecord_ng_cs_session_status", label: "Status"})
                ]

                let eventSessionsSearchResults = getSearchResults('customrecord_ng_cs_event_function',eventSessionsSearchFilters,eventSessionsSearchColumns);

                let eventSessionIDs = [];

                if(!isEmpty(eventSessionsSearchResults) && eventSessionsSearchResults.length > 0){

                    for(let i=0; i < eventSessionsSearchResults.length; i++){

                        var sessionID = eventSessionsSearchResults[i].getValue({name: 'internalid'});

                        if(eventSessionIDs.indexOf(sessionID) == -1  && selectedSessionIDs.indexOf(sessionID) != -1){

                            eventSessionIDs.push(sessionID);

                        }

                    }

                    if(!isEmpty(eventSessionIDs) && eventSessionIDs.length > 0){

                        //Search for Event Session Equipment Records

                        let eventSessionEquipmentFilters = [
                            ["isinactive","is","F"],
                            "AND",
                            ["custrecord_ng_cs_sess_equip_sess","anyof",eventSessionIDs]
                        ]

                        let eventSessionEquipmentColumns = [
                            search.createColumn({name: "internalid", label: "Internal ID"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_sess", label: "Event Session"}),
                            search.createColumn({
                                name: "custrecord_ng_cs_session_title",
                                join: "CUSTRECORD_NG_CS_SESS_EQUIP_SESS",
                                label: "Title"
                            }),
                            search.createColumn({
                                name: "custrecord_ng_cs_session_start_date",
                                join: "CUSTRECORD_NG_CS_SESS_EQUIP_SESS",
                                label: "Start Date"
                            }),
                            search.createColumn({
                                name: "custrecord_ng_cs_session_start_time",
                                join: "CUSTRECORD_NG_CS_SESS_EQUIP_SESS",
                                label: "Start Time"
                            }),
                            search.createColumn({
                                name: "custrecord_ng_cs_session_end_date",
                                join: "CUSTRECORD_NG_CS_SESS_EQUIP_SESS",
                                label: "End Date"
                            }),
                            search.createColumn({
                                name: "custrecord_ng_cs_session_end_time",
                                join: "CUSTRECORD_NG_CS_SESS_EQUIP_SESS",
                                label: "End Time"
                            }),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_item", label: "Rental Item Code"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_salesdesc", label: "Sales Description"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_pricelvl", label: "Price Level"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_service_tier", label: "Service Tier"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_itemcat", label: "Item Category"}),
                            search.createColumn({
                                name: "custitem_ng_cs_item_sales_type",
                                join: "CUSTRECORD_NG_CS_SESS_EQUIP_ITEM",
                                label: "Sales Type"
                            }),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_qty", label: "Quantity"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_sess_days", label: "# of Days"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_default_rate", label: "Default Rate"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_rate", label: "Rate"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_day_price", label: "Daily Price"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_price", label: "Price"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_service_fee", label: "Service Fee"}),
                            search.createColumn({name: "custrecord_ng_equip_day_serv_fee_amt", label: "Daily Service Fee Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_serv_fee_amt", label: "Service Fee Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_discount", label: "Discount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_flat_discnt", label: "Flat Discount"}),
                            search.createColumn({name: "custrecord_ng_cs_equip_day_discnt_amt", label: "Daily Discount Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_discount_amt", label: "Discount Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_euip_daily_sub_total", label: "Daily Sub Total"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_subtotal", label: "Sub Total"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_is_taxable", label: 'Taxable'}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_taxrate", label: "Tax Rate"}),
                            search.createColumn({name: "custrecord_ng_cs_equip_day_tax_amt", label: "Daily Tax Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_tax_amt", label: "Tax Amount"}),
                            search.createColumn({name: "custrecord_ng_equip_daily_amount", label: "Daily Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_amount", label: "Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_is_subrental", label: "Sub-rental"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_subrent_cost", label: "Sub-rental Cost"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_com_rate", label: "Commission Rate"}),
                            search.createColumn({name: "custrecord_ng_equip_daily_comm_amount", label: "Daily Commission Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_com_amount", label: "Commission Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_session_equip_days", label: "Days"}),
                            search.createColumn({name: "custrecord_ng_cs_session_equip_days_inl", label: "Equipment Days"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_int_notes", label: "Internal Notes"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_notes", label: "Notes"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_ser_com_rate", label: "Service Fee Commission Rate"}),
                            search.createColumn({name: "custrecord_ng_equip_day_serv_comm_amount", label: "Daily Service Fee Commission Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_ser_com_amt", label: "Service Fee Commission Amount"}),
                            search.createColumn({name: "custrecord_ng_equip_day_comm_total", label: "Daily Commission Total"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_com_total", label: "Commission Total"}),
                            search.createColumn({name: "custrecord_ng_amount_deci", label: "Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_com_amt_deci", label: "Commission Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_com_total_deci", label: "Commission Total Decimal"}),
                            search.createColumn({name: "custrecord_ng_day_amt_deci", label: "Daily Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_day_com_amt_deci", label: "Daily Commission Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_day_com_total_deci", label: "Daily Commission Total Decimal"}),
                            search.createColumn({name: "custrecord_ng_day_disc_amt_deci", label: "Daily Discount Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_cs_day_serv_fee_amt_deci", label: "Daily Service Fee Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_day_serv_fee_com_amt_deci", label: "Daily Service Fee Commission Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_day_sub_total_deci", label: "Daily Sub Total Decimal"}),
                            search.createColumn({name: "custrecord_ng_day_tax_amt_deci", label: "Daily Tax Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_disc_amt_deci", label: "Discount Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_serv_fee_amt_deci", label: "Service Fee Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_serv_fee_com_amt_deci", label: "Service Fee Commission Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_sub_total_deci", label: "Sub Total Decimal"}),
                            search.createColumn({name: "custrecord_ng_tax_amt_deci", label: "Tax Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_tax_amt_pst", label: "PST Amount"}),
                            search.createColumn({name: "custrecord_ng_tax_amt_pst_deci", label: "PST Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_equip_taxrate_pst", label: "PST Rate"}),
                            search.createColumn({name: "custrecord_ng_cs_equip_day_tax_amt_pst", label: "Daily PST Amount"}),
                            search.createColumn({name: "custrecord_ng_day_tax_amt_pst_deci", label: "Daily PST Amount Decimal"})
                        ]


                        let eventSessionEquipmentResults = getSearchResults('customrecord_ng_cs_sess_equip', eventSessionEquipmentFilters,eventSessionEquipmentColumns);

                        //Search for Event Session Task Record

                        let eventSessionTasksFilters = [
                            ["isinactive","is","F"],
                            "AND",
                            ["custrecord_ng_cs_sess_task_sess","anyof",eventSessionIDs]
                        ]

                        let eventSessionTasksColumns = [
                            search.createColumn({name: "internalid", label: "Internal ID"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_sess", label: "Event Session"}),
                            search.createColumn({
                                name: "custrecord_ng_cs_sess_task_item",
                                sort: search.Sort.ASC,
                                label: "Service Item Code"
                            }),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_salesdesc", label: "Sales Description"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_pricelvl", label: "Price Level"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_service_tier", label: "Service Tier"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_itemcat", label: "Item Category"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_qty", label: "Quantity"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_sess_days", label: "# of Days"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_rate", label: "Rate"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_hours", label: "Hours"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_hours_ot", label: "Overtime Hours"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_hours_dt", label: "Double Time Hours"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_price", label: "Price"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_discount", label: "Discount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_flat_discount" ,label: "Flat Discount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_discount_amt", label: "Discount Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_subtotal", label: "Sub Total"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_is_taxable", label: "Taxable"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_taxrate", label: "Tax Rate"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_tax_amt", label: "Tax Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_amount", label: "Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_com_rate", label: "Commission Rate"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_com_amount", label: "Commission Amount"}),
                            search.createColumn({name: "custrecord_ng_cs_session_task_days", label: "Days"}),
                            search.createColumn({name: "custrecord_ng_cs_session_task_days_inl", label: "Task Days"}),
                            search.createColumn({name: "custrecord_ng_cs_session_task_start_time", label: "Start Time"}),
                            search.createColumn({name: "custrecord_ng_cs_session_task_end_time", label: "End Time"}),
                            search.createColumn({name: "custrecord_ng_cs_session_task_int_notes", label: "Internal Notes"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_note", label: "Notes"}),
                            search.createColumn({
                                name: "custrecord_ng_cs_session_start_date",
                                join: "CUSTRECORD_NG_CS_SESS_TASK_SESS",
                                label: "Start Date"
                            }),
                            search.createColumn({
                                name: "custrecord_ng_cs_session_start_time",
                                join: "CUSTRECORD_NG_CS_SESS_TASK_SESS",
                                label: "Start Time"
                            }),
                            search.createColumn({
                                name: "custrecord_ng_cs_session_end_date",
                                join: "CUSTRECORD_NG_CS_SESS_TASK_SESS",
                                label: "End Date"
                            }),
                            search.createColumn({
                                name: "custrecord_ng_cs_session_end_time",
                                join: "CUSTRECORD_NG_CS_SESS_TASK_SESS",
                                label: "End Time"
                            }),
                            search.createColumn({
                                name: "custrecord_ng_cs_session_title",
                                join: "CUSTRECORD_NG_CS_SESS_TASK_SESS",
                                label: "Title"
                            }),
                            search.createColumn({
                                name: "custitem_ng_cs_item_sales_type",
                                join: "CUSTRECORD_NG_CS_SESS_TASK_ITEM",
                                label: "Sales Type"
                            }),
                            search.createColumn({name: "custrecord_ng_task_amt_deci", label: "Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_comm_amt_deci", label: "Commission Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_task_disc_amt_deci", label: "Discount Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_price_deci", label: "Price Decimal"}),
                            search.createColumn({name: "custrecord_ng_task_sub_total_deci", label: "Sub Total Decimal"}),
                            search.createColumn({name: "custrecord_ng_task_tax_amt_deci", label: "Tax Amount Decimal"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_tax_amt_pst", label: "PST Amount"}),
                            search.createColumn({name: "custrecord_ng_task_tax_amt_pst_deci", label: "PST Amount  Decimal"}),
                            search.createColumn({name: "custrecord_ng_cs_sess_task_taxrate_pst", label: "PST Rate"})
                        ]

                        let eventSessionTasksResults = getSearchResults('customrecord_ng_cs_sess_task', eventSessionTasksFilters,eventSessionTasksColumns);

                        if((!isEmpty(eventSessionEquipmentResults) && eventSessionEquipmentResults.length > 0) || (!isEmpty(eventSessionTasksResults) && eventSessionTasksResults.length > 0)){


                            let eventData = {};

                            eventData.eventID = eventID;

                            eventData.paymentTerms = eventPaymentTerms;

                            eventData.eventName = eventRec.getValue({fieldId: 'name'});

                            eventData.jobID = eventRec.getValue({fieldId: 'custrecord_show_job'});

                            eventData.venueID = eventRec.getValue({fieldId: 'custrecord_facility'});

                            eventData.venueName = eventRec.getText({fieldId: 'custrecord_facility'});

                            eventData.venueCurrency = venueCurrency;

                            eventData.venueAddress1 = venueAddress1;

                            eventData.venueAddress2 = venueAddress2;

                            eventData.venueCity = venueCity;

                            eventData.venueStateName = venueStateName;

                            eventData.venueZip = venueZip;

                            eventData.venueCountryCode = venueCountryCode;

                            eventData.venueCountryName = venueCountryName;

                            eventData.venueCountryID = venueCountryID;

                            eventData.locationID = locationID;

                            eventData.customerID = eventRec.getValue({fieldId: 'custrecord_show_customer'});

                            eventData.customerName = eventRec.getText({fieldId: 'custrecord_show_customer'});

                            eventData.salesRepID = eventRec.getValue({fieldId: 'custrecord_sales_rep'});

                            eventData.salesRepName = eventRec.getText({fieldId: 'custrecord_sales_rep'});

                            eventData.taxGroupID = taxGroupID;

                            eventData.sessionsData = [];

                            for(let i=0; i < eventSessionIDs.length; i++){

                                let sessionData = {};

                                sessionData.sessionID = eventSessionIDs[i];

                                let filteredSessionRecord = eventSessionsSearchResults.filter(function(result){return result.getValue({name: 'internalid'}) == sessionData.sessionID});

                                if(!isEmpty(filteredSessionRecord) && filteredSessionRecord.length >0){

                                    let sessionRecResult = filteredSessionRecord[0];

                                    sessionData.sessionName = sessionRecResult.getValue({name: 'custrecord_ng_cs_session_title'});

                                    sessionData.venueID = sessionRecResult.getValue({name: 'custrecord_ng_cs_session_venue'});

                                    sessionData.venueSpaceID = sessionRecResult.getValue({name: 'custrecord_ng_cs_session_venue_space'});

                                    sessionData.venueSpaceName = sessionRecResult.getValue({name: 'custrecord_ng_cs_space_name', join: 'custrecord_ng_cs_session_venue_space'});

                                    //Get Filtered Equipment Records for Session

                                    sessionData.sessionEquipment = [];

                                    if(!isEmpty(eventSessionEquipmentResults) && eventSessionEquipmentResults.length > 0){

                                        let filteredSessionEquipment = eventSessionEquipmentResults.filter(function(result){return result.getValue({name: 'custrecord_ng_cs_sess_equip_sess'}) == sessionData.sessionID});

                                        if(!isEmpty(filteredSessionEquipment) && filteredSessionEquipment.length > 0){

                                            for(let i=0; i < filteredSessionEquipment.length; i++){

                                                let equipment = {};

                                                equipment.internalID = filteredSessionEquipment[i].getValue({name: 'internalid'});

                                                equipment.eventSessionID = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_sess'});

                                                equipment.eventSessionName = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_session_title', join: 'CUSTRECORD_NG_CS_SESS_EQUIP_SESS'});

                                                equipment.eventSessionStartDate = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_session_start_date', join: 'CUSTRECORD_NG_CS_SESS_EQUIP_SESS'});

                                                equipment.eventSessionStartTime = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_session_start_time', join: 'CUSTRECORD_NG_CS_SESS_EQUIP_SESS'});

                                                equipment.eventSessionEndDate = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_session_end_date', join: 'CUSTRECORD_NG_CS_SESS_EQUIP_SESS'});

                                                equipment.eventSessionEndTime = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_session_end_time', join: 'CUSTRECORD_NG_CS_SESS_EQUIP_SESS'});

                                                equipment.itemID = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_item'});

                                                equipment.itemName = filteredSessionEquipment[i].getText({name: 'custrecord_ng_cs_sess_equip_item'});

                                                equipment.salesDescription = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_salesdesc'});

                                                equipment.priceLevelID = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_pricelvl'});

                                                equipment.priceLevelText = filteredSessionEquipment[i].getText({name: 'custrecord_ng_cs_sess_equip_pricelvl'});

                                                equipment.itemServiceTierID = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_service_tier'});

                                                equipment.itemServiceTierText = filteredSessionEquipment[i].getText({name: 'custrecord_ng_cs_sess_equip_service_tier'});

                                                equipment.itemCategoryID = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_itemcat'});

                                                equipment.itemCategoryName = filteredSessionEquipment[i].getText({name: 'custrecord_ng_cs_sess_equip_itemcat'});

                                                equipment.itemSalesType = filteredSessionEquipment[i].getValue({name: 'custitem_ng_cs_item_sales_type',join: 'CUSTRECORD_NG_CS_SESS_EQUIP_ITEM'});

                                                equipment.itemSalesTypeName = filteredSessionEquipment[i].getText({name: 'custitem_ng_cs_item_sales_type',join: 'CUSTRECORD_NG_CS_SESS_EQUIP_ITEM'});

                                                equipment.itemQty = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_qty'});

                                                equipment.sessionDays = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_sess_days'});

                                                equipment.defaultRate = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_default_rate'});

                                                equipment.taxable = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_is_taxable'});

                                                equipment.itemRate = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_rate'});

                                                equipment.itemDayPrice = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_day_price'});

                                                equipment.itemPrice = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_price'});

                                                equipment.itemServiceFee = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_service_fee'});

                                                equipment.serviceFeeDayAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_day_serv_fee_amt_deci'});

                                                equipment.serviceFeeAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_serv_fee_amt_deci'});

                                                equipment.itemDiscount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_discount'});

                                                equipment.itemDayDiscount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_day_disc_amt_deci'});

                                                equipment.flatDiscountAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_flat_discnt'});

                                                equipment.discountAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_disc_amt_deci'});

                                                equipment.itemDaySubTotal = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_day_sub_total_deci'});

                                                equipment.itemSubtotal = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_sub_total_deci'});

                                                equipment.itemTaxRate = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_taxrate'});

                                                equipment.itemTaxRatePST = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_taxrate_pst'});

                                                equipment.itemDayTaxAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_day_tax_amt_deci'});

                                                equipment.itemDayTaxAmountPST = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_day_tax_amt_pst_deci'});

                                                equipment.taxAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_tax_amt_deci'});

                                                equipment.taxAmountPST = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_tax_amt_pst_deci'})

                                                equipment.itemDayAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_day_amt_deci'});

                                                equipment.itemAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_amount_deci'});

                                                equipment.isSubrental = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_is_subrental'});

                                                equipment.subrentalCost = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_subrent_cost'});

                                                equipment.itemCommissionRate = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_com_rate'});

                                                equipment.itemDayCommissionAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_day_com_amt_deci'});

                                                equipment.itemCommissionAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_com_amt_deci'});

                                                equipment.daysIDs = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_session_equip_days'});

                                                equipment.daysText = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_session_equip_days_inl'});

                                                equipment.itemInternalNotes = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_int_notes'});

                                                equipment.itemNotes = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_notes'});

                                                equipment.serviceFeeCommissionRate = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_cs_sess_equip_ser_com_rate'});

                                                equipment.serviceFeeDayCommissionAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_day_serv_fee_com_amt_deci'});

                                                equipment.serviceFeeCommissionAmount = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_serv_fee_com_amt_deci'});

                                                equipment.dayCommissionTotal = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_day_com_total_deci'});

                                                equipment.commissionTotal = filteredSessionEquipment[i].getValue({name: 'custrecord_ng_com_total_deci'});

                                                equipment.itemDiscounts = [];

                                                if(!isEmpty(itemDiscountsResults) && itemDiscountsResults.length > 0){

                                                    let filteredDiscounts = itemDiscountsResults.filter(function(result){
                                                        let resultValues = result.getAllValues();
                                                        let discountServiceTiers = resultValues.custrecord_ng_cs_event_discount_tiers;

                                                        if(!isEmpty(discountServiceTiers) && Array.isArray(discountServiceTiers) && discountServiceTiers.length > 0){

                                                            for(let i=0; i < discountServiceTiers.length; i++){

                                                                if(discountServiceTiers[i].value == equipment.itemServiceTierID){

                                                                    return true;

                                                                }else{

                                                                    continue;

                                                                }
                                                            }
                                                        }
                                                    });

                                                    if(!isEmpty(filteredDiscounts) && filteredDiscounts.length > 0){

                                                        for(let i=0; i < filteredDiscounts.length; i++){

                                                            let itemDiscount = filteredDiscounts[i].getAllValues();

                                                            equipment.itemDiscounts.push(itemDiscount);

                                                        }
                                                    }

                                                }

                                                sessionData.sessionEquipment.push(equipment);


                                            }

                                        }

                                    }



                                    //Get Filtered Task Records for Session

                                    sessionData.sessionTasks = [];

                                    if(!isEmpty(eventSessionTasksResults) && eventSessionTasksResults.length > 0){

                                        let filteredSessionTasks = eventSessionTasksResults.filter(function(result){return result.getValue({name: 'custrecord_ng_cs_sess_task_sess'}) == sessionData.sessionID});

                                        if(!isEmpty(filteredSessionTasks) && filteredSessionTasks.length > 0){

                                            for(let i=0; i < filteredSessionTasks.length; i++){

                                                let task = {};

                                                task.internalID = filteredSessionTasks[i].getValue({name: 'internalid'});

                                                task.eventSessionID = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_sess'});

                                                task.eventSessionName = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_session_title', join: 'CUSTRECORD_NG_CS_SESS_TASK_SESS'});

                                                task.eventSessionStartDate = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_session_start_date', join: 'CUSTRECORD_NG_CS_SESS_TASK_SESS'});

                                                task.eventSessionStartTime = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_session_start_time', join: 'CUSTRECORD_NG_CS_SESS_TASK_SESS'});

                                                task.eventSessionEndDate = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_session_end_date', join: 'CUSTRECORD_NG_CS_SESS_TASK_SESS'});

                                                task.eventSessionEndTime = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_session_end_time', join: 'CUSTRECORD_NG_CS_SESS_TASK_SESS'});

                                                task.itemID = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_item'});

                                                task.itemName = filteredSessionTasks[i].getText({name: 'custrecord_ng_cs_sess_task_item'});

                                                task.salesDescription = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_salesdesc'});

                                                task.priceLevelID = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_pricelvl'});

                                                task.priceLevelText = filteredSessionTasks[i].getText({name: 'custrecord_ng_cs_sess_task_pricelvl'});

                                                task.itemServiceTierID = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_service_tier'});

                                                task.itemServiceTierText = filteredSessionTasks[i].getText({name: 'custrecord_ng_cs_sess_task_service_tier'});

                                                task.itemCategoryID = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_itemcat'});

                                                task.itemCategoryName = filteredSessionTasks[i].getText({name: 'custrecord_ng_cs_sess_task_itemcat'});

                                                task.itemSalesType = filteredSessionTasks[i].getValue({name: 'custitem_ng_cs_item_sales_type', join: 'CUSTRECORD_NG_CS_SESS_TASK_ITEM'});

                                                task.itemSalesTypeName = filteredSessionTasks[i].getText({name: 'custitem_ng_cs_item_sales_type', join: 'CUSTRECORD_NG_CS_SESS_TASK_ITEM'});

                                                task.itemQty = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_qty'});

                                                task.sessionDays = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_sess_days'});

                                                task.itemRate = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_rate'});

                                                task.itemHours = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_hours'});

                                                task.itemOTHours = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_hours_ot'});

                                                task.itemDTHours = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_hours_dt'});

                                                task.itemPrice = filteredSessionTasks[i].getValue({name: 'custrecord_ng_price_deci'});

                                                task.itemDiscount = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_discount'});

                                                task.itemFlatDiscount = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_flat_discount'});

                                                task.itemDiscountAmount = filteredSessionTasks[i].getValue({name: 'custrecord_ng_task_disc_amt_deci'});

                                                task.itemSubtotal = filteredSessionTasks[i].getValue({name: 'custrecord_ng_task_sub_total_deci'});

                                                task.taxable = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_is_taxable'});

                                                task.itemTaxRate = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_taxrate'});

                                                task.itemTaxRatePST = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_taxrate_pst'});

                                                task.taxAmount = filteredSessionTasks[i].getValue({name: 'custrecord_ng_task_tax_amt_deci'});

                                                task.taxAmountPST = filteredSessionTasks[i].getValue({name: 'custrecord_ng_task_tax_amt_pst_deci'});

                                                task.itemAmount = filteredSessionTasks[i].getValue({name: 'custrecord_ng_task_amt_deci'});

                                                task.itemCommissionRate = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_com_rate'});

                                                task.itemCommissionAmount = filteredSessionTasks[i].getValue({name: 'custrecord_ng_comm_amt_deci'});

                                                task.daysIDs = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_session_task_days'});

                                                task.daysText = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_session_task_days_inl'});

                                                task.startTime = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_session_task_start_time'});

                                                task.endTime = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_session_task_end_time'});

                                                task.itemInternalNotes = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_session_task_int_notes'});

                                                task.itemNotes = filteredSessionTasks[i].getValue({name: 'custrecord_ng_cs_sess_task_note'});

                                                task.itemDiscounts = [];

                                                if(!isEmpty(itemDiscountsResults) && itemDiscountsResults.length > 0){

                                                    let filteredDiscounts = itemDiscountsResults.filter(function(result){
                                                        let resultValues = result.getAllValues();
                                                        let discountServiceTiers = resultValues.custrecord_ng_cs_event_discount_tiers;

                                                        if(!isEmpty(discountServiceTiers) && Array.isArray(discountServiceTiers) && discountServiceTiers.length > 0){

                                                            for(let i=0; i < discountServiceTiers.length; i++){

                                                                if(discountServiceTiers[i].value == task.itemServiceTierID){

                                                                    return true;

                                                                }else{

                                                                    continue;

                                                                }
                                                            }
                                                        }
                                                    });

                                                    if(!isEmpty(filteredDiscounts) && filteredDiscounts.length > 0){

                                                        for(let i=0; i < filteredDiscounts.length; i++){

                                                            let itemDiscount = filteredDiscounts[i].getAllValues();

                                                            task.itemDiscounts.push(itemDiscount);

                                                        }
                                                    }

                                                }

                                                sessionData.sessionTasks.push(task);

                                            }

                                        }

                                    }


                                    eventData.sessionsData.push(sessionData);


                                }


                            }

                            log.debug({title: 'Event Data', details: JSON.stringify(eventData)});

                            responseObj.eventData = eventData;

                            let oppDiscounts = [];

                            let serviceFeeAmount = 0;

                            let estRec = record.create({
                                type: 'estimate',
                                isDynamic: true,
                                defaultValues: {
                                    entity: eventData.customerID
                                }
                            });

                            estRec.setValue({
                                fieldId: 'shipaddresslist',
                                value: -2
                            });

                            let shipAddressRec = estRec.getSubrecord({fieldId: 'shippingaddress'});

                            shipAddressRec.setValue({
                                fieldId: 'country',
                                value: eventData.venueCountryCode
                            });

                            shipAddressRec.setValue({
                                fieldId: 'addr1',
                                value: eventData.venueAddress1
                            })

                            shipAddressRec.setValue({
                                fieldId: 'addr2',
                                value: eventData.venueAddress2
                            })

                            shipAddressRec.setValue({
                                fieldId: 'city',
                                value: eventData.venueCity
                            });

                            shipAddressRec.setValue({
                                fieldId: 'state',
                                value: eventData.venueStateName
                            });

                            shipAddressRec.setValue({
                                fieldId: 'zip',
                                value: eventData.venueZip
                            })

                            shipAddressRec.commit();

                            estRec.setValue({
                                fieldId: 'currency',
                                value: eventData.venueCurrency
                            });

                            estRec.setValue({
                                fieldId: 'custbody_ng_cs_event_est_version_num',
                                value: versionCount
                            });

                            estRec.setValue({
                                fieldId: 'title',
                                value: eventData.eventName + ' (' + eventData.venueName + ')'
                            });

                            estRec.setValue({
                                fieldId: 'custbody_show_table',
                                value: eventData.eventID
                            });

                            estRec.setValue({
                                fieldId: 'entitystatus',
                                value: '10'
                            });

                            estRec.setValue({
                                fieldId: 'custbody_cseg_ng_cs_job',
                                value: eventData.jobID
                            });

                            estRec.setValue({
                                fieldId: 'custbody_ng_cs_event_venue',
                                value: eventData.venueID
                            });

                            estRec.setValue({
                                fieldId: 'location',
                                value: eventData.locationID
                            });

                            estRec.setValue({
                                fieldId: 'terms',
                                value: eventData.paymentTerms
                            });

                            /*
                            estRec.setValue({
                                fieldId: 'istaxable',
                                value: true
                            });

                            estRec.setValue({
                                fieldId: 'taxitem',
                                value: eventData.taxGroupID
                            });
                            */
                            for(let i=0; i < eventData.sessionsData.length; i++){

                                let sessionData = eventData.sessionsData[i];

                                if(sessionData.sessionEquipment.length > 0){

                                    for(let i=0; i < sessionData.sessionEquipment.length; i++){

                                        let equipmentData = sessionData.sessionEquipment[i];

                                        log.debug({title: 'Equipment Item Data', details: JSON.stringify(equipmentData)});

                                        if(!isEmpty(equipmentData.serviceFeeAmount) && parseFloat(equipmentData.serviceFeeAmount) > 0){

                                            serviceFeeAmount = serviceFeeAmount + parseFloat(equipmentData.serviceFeeAmount);

                                        }

                                        if(!isEmpty(equipmentData.itemDiscounts) && equipmentData.itemDiscounts.length > 0){

                                            for(let d=0; d < equipmentData.itemDiscounts.length; d++){

                                                let discountID = equipmentData.itemDiscounts[d]["custrecord_ng_cs_event_discount_item"][0].value;

                                                let discountRate = equipmentData.itemDiscounts[d]["custrecord_ng_discount_rate"];

                                                let isFlatRate = false;

                                                if(discountRate.indexOf('%') != -1){

                                                    discountRate = parseFloat(discountRate) * -1;

                                                }else{

                                                    isFlatRate = true;

                                                }

                                                let discountIndex = oppDiscounts.findIndex(function(discObj){return discObj.discountID == discountID})

                                                if(discountIndex == -1){

                                                    let discountObj = {};
                                                    discountObj.discountID = discountID;
                                                    discountObj.discountRate = discountRate;
                                                    discountObj.isFlatRate = isFlatRate;

                                                    if(!isFlatRate){

                                                        let itemQty = Number(equipmentData.itemQty);

                                                        let itemRate = Number(equipmentData.itemRate);

                                                        let itemDays = Number(equipmentData.sessionDays);

                                                        let itemDayPrice = (itemQty * itemRate);

                                                        let discountAmount = (discountRate/100) * itemDayPrice;

                                                        discountAmount = discountAmount * itemDays;

                                                        discountObj.discountAmount = discountAmount;

                                                        oppDiscounts.push(discountObj);

                                                    }else{
                                                        /*
                                                        let discountAmount = discountRate;

                                                        discountObj.discountAmount = discountAmount;

                                                        oppDiscounts.push(discountObj);
                                                        */
                                                    }



                                                }else{

                                                    let discountObj = oppDiscounts[discountIndex];

                                                    if(!discountObj.isFlatRate){

                                                        let discountRate = discountObj.discountRate;

                                                        let itemQty = Number(equipmentData.itemQty);

                                                        let itemRate = Number(equipmentData.itemRate);

                                                        let itemDays = Number(equipmentData.sessionDays);

                                                        let itemDayPrice = itemQty * itemRate;

                                                        let discountAmount = (discountRate/100) * itemDayPrice;

                                                        discountAmount = discountAmount * itemDays;

                                                        discountObj.discountAmount = discountObj.discountAmount + discountAmount;


                                                    }else{
                                                        /*
                                                        let discountAmount = discountRate;

                                                        discountObj.discountAmount = discountObj.discountAmount + discountAmount;
                                                        */
                                                    }



                                                }

                                            }

                                        }

                                        estRec.selectNewLine({
                                            sublistId: 'item'
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'item',
                                            value: equipmentData.itemID
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_category',
                                            value: equipmentData.itemCategoryID
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'quantity',
                                            value: Number(equipmentData.itemQty)
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'description',
                                            value: equipmentData.salesDescription
                                        });

                                        if(equipmentData.defaultRate == equipmentData.itemRate){

                                            if(!isEmpty(equipmentData.priceLevelID)){

                                                estRec.setCurrentSublistValue({
                                                    sublistId: 'item',
                                                    fieldId: 'price',
                                                    value: equipmentData.priceLevelID
                                                });

                                            }else{

                                                estRec.setCurrentSublistValue({
                                                    sublistId: 'item',
                                                    fieldId: 'price',
                                                    value: -1
                                                });

                                                estRec.setCurrentSublistValue({
                                                    sublistId: 'item',
                                                    fieldId: 'rate',
                                                    value: equipmentData.itemRate
                                                });

                                            }

                                        }else{

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'price',
                                                value: -1
                                            });

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'rate',
                                                value: equipmentData.itemRate
                                            });

                                        }

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'amount',
                                            value: equipmentData.itemPrice
                                        })
                                        if(equipmentData.taxable){

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'taxcode',
                                                value: eventData.taxGroupID
                                            });

                                        }else{

                                            if(eventData.venueCountryCode == 'CA'){

                                                estRec.setCurrentSublistText({
                                                    sublistId: 'item',
                                                    fieldId: 'taxcode',
                                                    text: 'CA-Zero'
                                                });

                                            }else{

                                                estRec.setCurrentSublistValue({
                                                    sublistId: 'item',
                                                    fieldId: 'taxcode',
                                                    value: -8
                                                });

                                            }



                                        }


                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_rental_loc',
                                            value: eventData.locationID
                                        });

                                        log.debug({title: 'Equipment Days', details: equipmentData.daysText});

                                        if(!NG.tools.isEmpty(equipmentData.daysText)){

                                            log.debug({title: 'Equipment Days Text', details: equipmentData.daysText});

                                            let equipmentLineDays = equipmentData.daysText.split('\n');

                                            log.debug({title: 'Equipment Days Text Array', details: JSON.stringify(equipmentLineDays)});

                                            log.debug({title: 'Rental Start Date', details: equipmentLineDays[0]});

                                            estRec.setCurrentSublistText({
                                                sublistId: 'item',
                                                fieldId: 'custcol_ng_cs_rental_start_date',
                                                text: equipmentLineDays[0]
                                            });

                                            log.debug({title: 'Rental End Date', details: equipmentLineDays[equipmentLineDays.length-1]});

                                            estRec.setCurrentSublistText({
                                                sublistId: 'item',
                                                fieldId: 'custcol_ng_cs_rental_end_date',
                                                text: equipmentLineDays[equipmentLineDays.length-1]
                                            });

                                        }

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_session_line',
                                            value: equipmentData.eventSessionID
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_session_title',
                                            value: equipmentData.eventSessionName
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_session_equip_rec',
                                            value: equipmentData.internalID
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_days_number',
                                            value: equipmentData.sessionDays
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_discount_amt',
                                            value: equipmentData.discountAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_serv_fee_amt',
                                            value: equipmentData.serviceFeeAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_service_tier',
                                            value: equipmentData.itemServiceTierID
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_show_dates',
                                            value: equipmentData.daysIDs
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_session_days',
                                            value: equipmentData.daysText
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_internal_notes',
                                            value: equipmentData.itemInternalNotes
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_notes',
                                            value: equipmentData.itemNotes
                                        });
                                        /*
                                        if (!NG.tools.isEmpty(equipmentData.eventSessionStartDate)){

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'custcol_ng_cs_event_sess_start_date',
                                                value: new Date(equipmentData.eventSessionStartDate)
                                            });

                                        }

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_sess_start_time',
                                            value: equipmentData.eventSessionStartTime
                                        });

                                        if(!NG.tools.isEmpty(equipmentData.eventSessionEndDate)){

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'custcol_ng_cs_event_sess_end_date',
                                                value: equipmentData.eventSessionEndDate
                                            });

                                        }

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_sess_end_time',
                                            value: equipmentData.eventSessionEndTime
                                        });
                                        */


                                        estRec.setCurrentSublistText({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_tax_rate',
                                            text: equipmentData.itemTaxRate
                                        });

                                        estRec.setCurrentSublistText({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_tax_rate_pst',
                                            text: equipmentData.itemTaxRatePST
                                        });

                                        log.debug({title: 'Item Commission Rate', details: equipmentData.itemCommissionRate});

                                        estRec.setCurrentSublistText({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_commission_rate',
                                            text: equipmentData.itemCommissionRate
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_commission_amount',
                                            value: equipmentData.itemCommissionAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_tax_amt',
                                            value: equipmentData.taxAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_tax_amount_pst',
                                            value: equipmentData.taxAmountPST
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_sess_venue_space',
                                            value: sessionData.venueSpaceID
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_sess_ven_sp_name',
                                            value: sessionData.venueSpaceName
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_sales_type',
                                            value: equipmentData.itemSalesType
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_session_title',
                                            value: equipmentData.eventSessionName
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_default_rate',
                                            value: equipmentData.defaultRate
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_flat_discount',
                                            value: equipmentData.flatDiscountAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_is_subrental',
                                            value: equipmentData.isSubrental
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_subrental_cost',
                                            value: equipmentData.subrentalCost
                                        });

                                        log.debug({title: 'Service Fee Commission Rate', details: equipmentData.serviceFeeCommissionRate});

                                        estRec.setCurrentSublistText({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_serv_fee_comm_rate',
                                            text: equipmentData.serviceFeeCommissionRate
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_serv_fee_comm_amount',
                                            value: equipmentData.serviceFeeCommissionAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_commission_total',
                                            value: equipmentData.commissionTotal
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_daily_price',
                                            value: equipmentData.itemDayPrice
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_serv_fee_amount',
                                            value: equipmentData.serviceFeeDayAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_discount_amount',
                                            value: equipmentData.itemDayDiscount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_sub_total',
                                            value: equipmentData.itemDaySubTotal
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_tax_amount',
                                            value: equipmentData.itemDayTaxAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_tax_amount_pst',
                                            value: equipmentData.itemDayTaxAmountPST
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_amount',
                                            value: equipmentData.itemDayAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_commission_amount',
                                            value: equipmentData.itemDayCommissionAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_serv_comm_amount',
                                            value: equipmentData.serviceFeeDayCommissionAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_commission_total',
                                            value: equipmentData.dayCommissionTotal
                                        });

                                        estRec.commitLine({
                                            sublistId: 'item'
                                        });


                                    }


                                }

                                if(sessionData.sessionTasks.length > 0){

                                    for(let i=0; i < sessionData.sessionTasks.length; i++){

                                        let taskData = sessionData.sessionTasks[i];

                                        log.debug({title: 'Labor Item Data', details: JSON.stringify(taskData)});

                                        if(!isEmpty(taskData.itemDiscounts) && taskData.itemDiscounts.length > 0){

                                            for(let d=0; d < taskData.itemDiscounts.length; d++){

                                                let discountID = taskData.itemDiscounts[d]["custrecord_ng_cs_event_discount_item"][0].value;

                                                let discountRate = taskData.itemDiscounts[d]["custrecord_ng_discount_rate"];

                                                let isFlatRate = false;

                                                if(discountRate.indexOf('%') != -1){

                                                    discountRate = parseFloat(discountRate) * -1;

                                                }else{

                                                    isFlatRate = true;

                                                }

                                                let discountIndex = oppDiscounts.findIndex(function(discObj){return discObj.discountID == discountID})

                                                if(discountIndex == -1){

                                                    let discountObj = {};
                                                    discountObj.discountID = discountID;
                                                    discountObj.discountRate = discountRate;
                                                    discountObj.isFlatRate = isFlatRate;

                                                    if(!isFlatRate){

                                                        let itemQty = Number(taskData.itemQty);

                                                        let itemRate = Number(taskData.itemRate);

                                                        let itemDays = Number(taskData.sessionDays);

                                                        let itemDayPrice = (itemQty * itemRate);

                                                        let discountAmount = (discountRate/100) * itemDayPrice;

                                                        discountAmount = discountAmount * itemDays;

                                                        discountObj.discountAmount = discountAmount;

                                                        oppDiscounts.push(discountObj);

                                                    }else{
                                                        /*
                                                        let discountAmount = discountRate;

                                                        discountObj.discountAmount = discountAmount;

                                                        oppDiscounts.push(discountObj);
                                                        */
                                                    }



                                                }else{

                                                    let discountObj = oppDiscounts[discountIndex];

                                                    if(!discountObj.isFlatRate){

                                                        let discountRate = discountObj.discountRate;

                                                        let itemQty = Number(taskData.itemQty);

                                                        let itemRate = Number(taskData.itemRate);

                                                        let itemDays = Number(taskData.sessionDays);

                                                        let itemDayPrice = itemQty * itemRate;

                                                        let discountAmount = (discountRate/100) * itemDayPrice;

                                                        discountAmount = discountAmount * itemDays;

                                                        discountObj.discountAmount = discountObj.discountAmount + discountAmount;


                                                    }else{
                                                        /*
                                                        let discountAmount = discountRate;

                                                        discountObj.discountAmount = discountObj.discountAmount + discountAmount;
                                                        */
                                                    }



                                                }

                                            }

                                        }

                                        estRec.selectNewLine({
                                            sublistId: 'item'
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'item',
                                            value: taskData.itemID
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'quantity',
                                            value: Number(taskData.itemQty) * (Number(taskData.itemHours)+Number(taskData.itemOTHours)+Number(taskData.itemDTHours))
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_days_number',
                                            value: Number(taskData.sessionDays)
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_task_hours',
                                            value: Number(taskData.itemHours)
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_task_hours_ot',
                                            value: Number(taskData.itemOTHours)
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_task_hours_dt',
                                            value: Number(taskData.itemDTHours)
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'description',
                                            value: taskData.salesDescription
                                        });

                                        if(taskData.taxable){

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'taxcode',
                                                value: eventData.taxGroupID
                                            });

                                        }else{

                                            if(eventData.venueCountryCode == 'CA'){

                                                estRec.setCurrentSublistText({
                                                    sublistId: 'item',
                                                    fieldId: 'taxcode',
                                                    text: 'CA-Zero'
                                                });


                                            }else{

                                                estRec.setCurrentSublistValue({
                                                    sublistId: 'item',
                                                    fieldId: 'taxcode',
                                                    value: -8
                                                });

                                            }



                                        }

                                        if(!isEmpty(taskData.priceLevelID)){

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'price',
                                                value: taskData.priceLevelID
                                            });

                                        }else{

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'price',
                                                value: -1
                                            });

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'rate',
                                                value: taskData.itemRate
                                            });

                                        }

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'amount',
                                            value: taskData.itemAmount
                                        })

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_service_tier',
                                            value: taskData.itemServiceTierID
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_show_dates',
                                            value: taskData.daysIDs
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_session_days',
                                            value: taskData.daysText
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_internal_notes',
                                            value: taskData.itemInternalNotes
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_notes',
                                            value: taskData.itemNotes
                                        });

                                        estRec.setCurrentSublistText({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_tax_rate',
                                            text: taskData.itemTaxRate
                                        });

                                        estRec.setCurrentSublistText({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_tax_rate_pst',
                                            text: taskData.itemTaxRatePST
                                        });

                                        estRec.setCurrentSublistText({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_commission_rate',
                                            text: taskData.itemCommissionRate
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_item_commission_amount',
                                            value: taskData.itemCommissionAmount
                                        });

                                        if(!NG.tools.isEmpty(taskData.startTime)){

                                            estRec.setCurrentSublistText({
                                                sublistId: 'item',
                                                fieldId: 'custcol_ng_cs_start_time',
                                                text: taskData.startTime
                                            });

                                        }

                                        if(!NG.tools.isEmpty(taskData.endTime)){

                                            estRec.setCurrentSublistText({
                                                sublistId: 'item',
                                                fieldId: 'custcol_ng_cs_end_time',
                                                text: taskData.endTime
                                            });

                                        }


                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_session_line',
                                            value: taskData.eventSessionID
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_session_title',
                                            value: taskData.eventSessionName
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_session_task_rec',
                                            value: taskData.internalID
                                        });

                                        /*
                                        if(!NG.tools.isEmpty(taskData.eventSessionStartDate)){

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'custcol_ng_cs_event_sess_start_date',
                                                value: taskData.eventSessionStartDate
                                            });

                                        }

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_sess_start_time',
                                            value: taskData.eventSessionStartTime
                                        });

                                        if(!NG.tools.isEmpty(taskData.eventSessionEndDate)){

                                            estRec.setCurrentSublistValue({
                                                sublistId: 'item',
                                                fieldId: 'custcol_ng_cs_event_sess_end_date',
                                                value: taskData.eventSessionEndDate
                                            });

                                        }

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_sess_end_time',
                                            value: taskData.eventSessionEndTime
                                        });
                                        */

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_discount_amt',
                                            value: taskData.discountAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_serv_fee_amt',
                                            value: taskData.serviceFeeAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_tax_amt',
                                            value: taskData.taxAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_tax_amt_pst',
                                            value: taskData.taxAmountPST
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_sess_venue_space',
                                            value: sessionData.venueSpaceID
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_sess_ven_sp_name',
                                            value: sessionData.venueSpaceName
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_event_session_title',
                                            value: taskData.eventSessionName
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_commission_total',
                                            value: taskData.itemCommissionAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_daily_price',
                                            value: taskData.itemAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_discount_amount',
                                            value: taskData.discountAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_sub_total',
                                            value: taskData.itemSubtotal
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_amount',
                                            value: taskData.itemSubtotal
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_commission_amount',
                                            value: taskData.itemCommissionAmount
                                        });

                                        estRec.setCurrentSublistValue({
                                            sublistId: 'item',
                                            fieldId: 'custcol_ng_cs_day_commission_total',
                                            value: taskData.itemCommissionAmount
                                        });

                                        estRec.commitLine({
                                            sublistId: 'item'
                                        });


                                    }


                                }
                            }



                            if(!isEmpty(serviceFeeAmount) && serviceFeeAmount > 0){

                                estRec.selectNewLine({
                                    sublistId: 'item'
                                });

                                estRec.setCurrentSublistValue({
                                    sublistId: 'item',
                                    fieldId: 'item',
                                    value: -2
                                });

                                estRec.commitLine({
                                    sublistId: 'item'
                                })

                                estRec.selectNewLine({
                                    sublistId: 'item'
                                });

                                estRec.setCurrentSublistValue({
                                    sublistId: 'item',
                                    fieldId: 'item',
                                    value: serviceFeeItem
                                });

                                estRec.setCurrentSublistValue({
                                    sublistId: 'item',
                                    fieldId: 'price',
                                    value: -1
                                });

                                estRec.setCurrentSublistValue({
                                    sublistId: 'item',
                                    fieldId: 'amount',
                                    value: serviceFeeAmount
                                });

                                estRec.setCurrentSublistValue({
                                    sublistId: 'item',
                                    fieldId: 'taxcode',
                                    value: eventData.taxGroupID
                                });

                                estRec.commitLine({
                                    sublistId: 'item'
                                });

                            }

                            if(!isEmpty(oppDiscounts) && oppDiscounts.length > 0){

                                for(let i =0; i < oppDiscounts.length; i++){

                                    estRec.selectNewLine({
                                        sublistId: 'item'
                                    });

                                    estRec.setCurrentSublistValue({
                                        sublistId: 'item',
                                        fieldId: 'item',
                                        value: -2
                                    });

                                    estRec.commitLine({
                                        sublistId: 'item'
                                    })

                                    let discountID = oppDiscounts[i].discountID;

                                    let discountAmount = oppDiscounts[i].discountAmount;

                                    estRec.selectNewLine({
                                        sublistId: 'item'
                                    });

                                    estRec.setCurrentSublistValue({
                                        sublistId: 'item',
                                        fieldId: 'item',
                                        value: discountID
                                    });

                                    estRec.setCurrentSublistValue({
                                        sublistId: 'item',
                                        fieldId: 'price',
                                        value: -1
                                    });

                                    estRec.setCurrentSublistValue({
                                        sublistId: 'item',
                                        fieldId: 'amount',
                                        value: discountAmount
                                    });

                                    estRec.setCurrentSublistValue({
                                        sublistId: 'item',
                                        fieldId: 'taxcode',
                                        value: eventData.taxGroupID
                                    })

                                    estRec.commitLine({
                                        sublistId: 'item'
                                    });

                                }
                            }

                            estRec.selectNewLine({
                                sublistId: 'item'
                            });

                            estRec.setCurrentSublistValue({
                                sublistId: 'item',
                                fieldId: 'item',
                                value: -2
                            });

                            estRec.commitLine({
                                sublistId: 'item'
                            })

                            let estRecID = estRec.save({
                                enableSourcing: true,
                                ignoreMandatoryFields: true
                            });

                            responseObj.success = true;
                            responseObj.estRecID = estRecID;

                            log.debug({title: 'Response Object', details: JSON.stringify(responseObj)});

                            eventRec.save({
                                enableSourcing: true,
                                ignoreMandatoryFields: true
                            });

                            let scriptParams = {};

                            scriptParams['estRecID'] = estRecID;

                            redirect.toSuitelet({
                                scriptId: 'customscript_ng_cs_sl_print_estimate',
                                deploymentId: 'customdeploy_ng_cs_sl_print_estimate',
                                parameters: scriptParams
                            });

                            /*
                            let pdfFile = render.transaction({
                                entityId: estRecID
                            })

                            scriptContext.response.writeFile({
                                file: pdfFile,
                                isInline: true
                            });
                            */

                        }else{

                            log.error({title: 'Error Gathering Even Data', details: "No Equipment or Task Records Found"});

                            let err = {message: 'No Equipment or Task Records Found'};

                            throw err;
                        }


                    }


                }else{

                    log.error({title: 'Error Gathering Even Data', details: "No Session Records Found for Event"});

                    let err = {message: 'No Equipment or Task Records Found for Event'};

                    throw err;


                }

            }else{

                log.error({title: 'Error Generating Opportunity', details: 'No Event ID Found'});

                let err = {message: 'No Event ID Found'};

                throw err;
            }


            //scriptContext.response.write(JSON.stringify(responseObj));

        }

        function getSearchResults(recType, filt, cols, searchId, pageSize, returnIDs, firstPageOnly) {
            var results = new Array();
            pageSize = pageSize || 1000
            returnIDs = returnIDs || false;
            firstPageOnly = firstPageOnly || false;
            var searchInit = null;
            if (isEmpty(searchId)) {
                searchInit = search.create({ type : recType , filters : filt , columns : cols });
            } else {
                var savedSearch = null;
                if (isEmpty(recType)) {
                    savedSearch = search.load({ id : searchId });
                } else {
                    savedSearch = search.load({ type : recType , id : searchId });
                }
                var searchFilters = savedSearch.filterExpression;
                var searchColumns = savedSearch.columns;
                var finalFilt = new Array();
                var finalCols = new Array();
                if (!isEmpty(searchFilters) && Array.isArray(searchFilters)) {
                    if (!isEmpty(filt) && Array.isArray(filt)) {
                        if (filt.length > 0) {
                            finalFilt = searchFilters.concat(["and"], filt);
                        } else {
                            finalFilt = searchFilters.concat(filt);
                        }
                    } else {
                        finalFilt = searchFilters.concat([]);
                    }
                } else {
                    if (!isEmpty(filt) && Array.isArray(filt)) {
                        finalFilt = [].concat(filt);
                    } else {
                        finalFilt = null;
                    }
                }
                if (!isEmpty(searchColumns) && Array.isArray(searchColumns)) {
                    if (!isEmpty(cols) && Array.isArray(cols)) {
                        finalCols = searchColumns.concat(cols);
                    } else {
                        finalCols = searchColumns.concat([]);
                    }
                } else {
                    if (!isEmpty(cols) && Array.isArray(cols)) {
                        finalCols = [].concat(cols);
                    } else {
                        finalCols = null;
                    }
                }
                searchInit = search.create({ type : recType , filters : finalFilt , columns : finalCols });
            }
            var pages = searchInit.runPaged({ pageSize : pageSize });
            for (var pg = 0; pg < pages.pageRanges.length; pg++) {
                var page = pages.fetch({ index : pages.pageRanges[pg].index });
                if (returnIDs) {
                    for (var p = 0; p < page.data.length; p++) {
                        results.push(page.data[p].id);
                    }
                } else {
                    results = results.concat(page.data);
                }

                if (firstPageOnly) {
                    if (page.isFirst) {
                        break;
                    }
                }
            }

            return results.length > 0 ? results : null;
        }



        function isEmpty (value) {
            if (value == null || value == undefined || value === "") {
                return true;
            }

            return false;
        }

        return {onRequest}

    });
