/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig ./amdSuiteletConfig.json
 */
define([
  "N/cache",
  "N/config",
  "N/file",
  "N/format",
  "N/query",
  "N/record",
  "N/redirect",
  "N/runtime",
  "N/search",
  "N/ui/serverWidget",
  "N/url",
  "settings",
  "enums",
  "moment",
  "crypto-js",
], /**
 * @param{cache} cache
 * @param{config} config
 * @param{file} file
 * @param{format} format
 * @param{query} query
 * @param{record} record
 * @param{redirect} redirect
 * @param{runtime} runtime
 * @param{search} search
 * @param{serverWidget} serverWidget
 * @param{url} url
 * @param{Object} settings
 * @param{() => Object} settings.useSettings
 * @param{ES_Enums} enums
 * @param{moment} moment
 * @param{CryptoJS} crypto
 */ (
  cache,
  config,
  file,
  format,
  query,
  record,
  redirect,
  runtime,
  search,
  serverWidget,
  url,
  settings,
  enums,
  moment,
  crypto,
) => {
  let CS_SETTINGS = null;
  let COMPANY_PREFERENCES = {};
  let COMPANY_INFO = {};
  let BOOTH_LINE = "Booth Number __________";
  let EVENT_GENERATED_COMMENTS =
    "Auto-generated by Event Creation Wizard for event ";
  const messageTypes = {
    CONFIRMATION: 0,
    INFORMATION: 1,
    WARNING: 2,
    ERROR: 3,
  };

  /**
   * Defines the Suitelet script trigger point.
   * @param {Object} scriptContext
   * @param {ServerRequest} scriptContext.request - Incoming request
   * @param {ServerResponse} scriptContext.response - Suitelet response
   * @since 2015.2
   */
  const onRequest = (scriptContext) => {
    const { request, response } = scriptContext;
    const { parameters: requestParams, method } = request;
    CS_SETTINGS = settings.useSettings();

    COMPANY_PREFERENCES = config.load({
      type: config.Type.COMPANY_PREFERENCES,
      isDynamic: true,
    });

    COMPANY_INFO = config.load({
      type: config.Type.COMPANY_INFORMATION,
      isDynamic: true,
    });

    switch (method) {
      case "GET":
        // Write function for display page
        renderForm(request, response);
        break;
      case "POST":
        // Write function for processing form submission
        submitForm(request, response);
        break;
      default:
        log.audit({ title: "🌕 HTTP METHOD INVALID:", details: method });
    }
  };

  /**
   * Render custom event form wizard
   * @param {ServerRequest} req
   * @param {ServerResponse} res
   * @returns {void}
   * */
  const renderForm = (req, res) => {
    const { writePage } = res;
    const { parameters: requestParams } = req;
    // Params from request
    const {
      jb: job,
      csj: csJobParam,
      snbr: eventNumber,
      snm: eventName,
      stype: eventType,
      sdy: subsidiary,
      loc,
      cid: customerId,
      sid: sessionId,
      // venue ops params
      indt: inDate,
      intm: inTime,
      stdt: startDate,
      sttm: startTime,
      endt: endDate,
      entm: endTime,
      oudt: outDate,
      outm: outTime,
      tpla: totalPlannedAttendance,
      tata: totalActualAttendance,
      // bookings params start with the following and end with numbers 1-10 to determine the booking
      // sd, st, ed, et, sp, bs, bu, bd
    } = requestParams;
    const CS_SETTINGS = settings.useSettings();
    let status = "";

    // Get the current user
    if (sessionId) {
      let sessData = runtime.getCurrentSession().get({ name: sessionId });
      if (sessData) {
        sessData = JSON.parse(sessData);

        log.audit({
          title: "🟢 Session Status Data Defined:",
          details: sessData,
        });

        status = sessData;
      } else {
        runtime.getCurrentSession().set({ name: sessionId, value: "" });
      }
    }

    const autoJobInheritanceEnabled =
      CS_SETTINGS.custrecord_enable_auto_job_inheritance;

    const materialHandlingBetaEnabled =
      CS_SETTINGS.custrecord_enable_material_handling_beta === "T";

    // Construct the form
    let form = serverWidget.createForm({
      title: "Create Event Wizard",
      hideNavBar: false,
    });
    let fieldLinkRef;
    let genBoothsRef;

    // Check status
    if (status) {
      let statusTitle;
      let statusMsg;
      let statusType;
      let statusMessageArr = [];

      // After event is created
      if (status?.eventRecId) {
        let eventLink = url.resolveRecord({
          recordType: "customrecord_show",
          recordId: status.eventRecId,
          isEditMode: false,
        });
        const eventDetails = query
          .runSuiteQL({
            query: `SELECT name FROM CUSTOMRECORD_SHOW WHERE ID = ${status.eventRecId}`,
          })
          .asMappedResults()[0];

        fieldLinkRef = `
            <a href="${eventLink}" target="_blank">${eventDetails.name}</a>
        `;

        let createBoothsPath = url.resolveScript({
          scriptId: "customscript_booth_generator",
          deploymentId: "customdeploy_booth_generator_dep",
          returnExternalUrl: false,
        });

        genBoothsRef = `
         <a href="${createBoothsPath}&showid=${status.eventRecId}" target="_blank">Generate Booths</a>
        `;

        statusTitle = "Event Created Successfully";
        statusMsg = `Event ${status.eventRecId} has been created.`;
        statusType = messageTypes.CONFIRMATION;
        statusMessageArr.push(statusMsg);
      }

      // Render event creation status with errors
      if (
        status?.eventRecId &&
        (status?.fail ||
          (status?.errList && status.errList.length !== 0) ||
          status.failureSubject)
      ) {
        statusTitle = "Event Creation Succeeded with Errors";
        statusType = messageTypes.WARNING;
        statusMessageArr.push(`${fieldLinkRef} | ${genBoothsRef} <br/>`);
      } else if (status?.eventRecId) {
        statusTitle = "Event Created Successfully";
        statusType = messageTypes.CONFIRMATION;
        statusMessageArr.push(`${fieldLinkRef} | ${genBoothsRef} <br/>`);
      } else {
        statusTitle = "Event Creation Failed";
        statusType = messageTypes.ERROR; // Err
      }

      // Render error messages

      /* 
         failureSubject
         failureErrName
         failureErrDesc
      */
      if (status?.fail && status?.errList && status.errList.length !== 0) {
        statusMessageArr.push("Errors Encountered: <br/><ul>");
        status.errList.forEach((err) => {
          statusMessageArr.push(
            `<li>${err?.failureSubject} - ${err.failureErrName}: ${err.failureErrDesc}</li>`,
          );
        });

        statusMessageArr.push(
          `<li>${status.failureSubject} - ${status.failureErrName}: ${status.failureErrDesc}</li>`,
        );
        statusMessageArr.push("</ul>");
      }
      statusMsg = statusMessageArr.join("");

      form.addPageInitMessage({
        title: statusTitle,
        message: statusMsg,
        type: statusType,
      });
    }

    // Add the form fields
    form.addFieldGroup({ id: "group1", label: "Primary Event Information" });
    form.addFieldGroup({ id: "group2", label: "Event Financial Information" });
    form.addFieldGroup({ id: "group3", label: "Event Vendors" });
    form.addFieldGroup({ id: "group4", label: "Event Floor Details" });
    form.addFieldGroup({ id: "group5", label: "Group 5" });
    form.addFieldGroup({ id: "misc", label: "Misc" });

    form.addTab({ id: "datesmain_tab", label: "Dates" });
    form.addTab({ id: "freight_tab", label: "Freight Table" });
    form.addTab({ id: "web_blurb_tab", label: "Web Blurbs" });
    form.addTab({ id: "adtnlinfo_tab", label: "Additional Info Links" });
    form.addTab({ id: "item_coll_tab", label: "Item Collections" });
    form.addTab({ id: "web_tab", label: "Web" });
    form.addTab({ id: "displayforms_tab", label: "Web Categories" });

    form.clientScriptModulePath =
      "../cs-client/ng_cses_slsc_event_wizard.js";

    // ========= Primary Event Information =========
    let subsidiariesEnabled = runtime.isFeatureInEffect({
      feature: "SUBSIDIARIES",
    });
    let locationsEnabled = runtime.isFeatureInEffect({ feature: "LOCATIONS" });
    let cancellationEnabled =
      CS_SETTINGS.custrecord_ng_cs_use_cancl_charge === "T";

    let eventNameField = form.addField({
      id: "custpage_showname",
      type: "text",
      label: "Event Name",
      container: "group1",
    });
    let eventNumberField = form.addField({
      id: "custpage_shownumber",
      type: "text",
      label: "Event Number",
      container: "group1",
    });
    let customerField = form.addField({
      id: "custpage_customer",
      type: "select",
      label: "Customer",
      source: "customer",
      container: "group1",
    });
    customerField.defaultValue = customerId || "";

    customerId &&
      customerField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.DISABLED,
      });

    let subsidiaryField;
    if (subsidiariesEnabled) {
      subsidiaryField = form.addField({
        id: "custpage_subsidiary",
        type: "select",
        label: "Subsidiary",
        source: "subsidiary",
        container: "group1",
      });
    }
    let eventTypeField = form.addField({
      id: "custpage_showtype",
      type: "select",
      label: "Event Type",
      source: "customrecord_order_type",
      container: "group1",
    });
    form.addField({
      id: "custpage_show_status",
      type: "select",
      label: "Event Status",
      source: "customrecord_cs_event_status",
      container: "group1",
    });
    let venueField = form
      .addField({
        id: "custpage_venue",
        type: "select",
        label: "Event Venue",
        source: "customrecord_facility",
        container: "group1",
      })
      .updateBreakType({ breakType: serverWidget.FieldBreakType.STARTCOL });
    venueField.isMandatory = true;

    if (locationsEnabled) {
      let locationField = form.addField({
        id: "custpage_location",
        type: "select",
        label: "Advanced Warehouse Location",
        source: "location",
        container: "group1",
      });
      if (loc) {
        locationField.defaultValue = loc;
      }
    }

    // ========= Add Shoelace CSS Hidden Field =========
    let shoelaceHiddenField = form.addField({
      id: "custpage_shoelace_hidden",
      type: serverWidget.FieldType.INLINEHTML,
      label: "Shoelace CSS Hidden Field",
    });

    shoelaceHiddenField.updateDisplayType({
      displayType: serverWidget.FieldDisplayType.NODISPLAY,
    });

    shoelaceHiddenField.defaultValue = `<html>
    <style>

      .card-image-container {
        padding: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .card-image {
        width: 176px;
        height: 176px;
        object-fit: contain;
      }

      .card-image::part(body) {
        display: none;
      }

      .card-image::part(base) {
        box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
        border-radius: 4px;
      }

    </style>
     <head>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.19.1/cdn/themes/light.css" />
        <script type="module" src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.19.1/cdn/shoelace-autoloader.js"></script>
     </head>
    </html>`;

    // ========= Event Financial Information =========
    let csJobField;
    let classField;
    let enableInheritEventForJobField = form.addField({
      id: "custpage_inherit_event_for_job",
      type: "checkbox",
      label: "Inherit Event for Job Creation",
      container: "group2",
    });

    enableInheritEventForJobField.defaultValue = autoJobInheritanceEnabled;
    enableInheritEventForJobField.helpText = `✔️ If checked, the event will be inherited for the job. Automatically creating the CS Job from the name of the current event. <br/><br/> ✖️ If unchecked, the event will not be inherited for the job. Thus, a CS Job must be created manually for this event.`;

    csJobField = form.addField({
      id: "custpage_cs_job",
      type: "select",
      label: "CS Job",
      source: "customrecord_cseg_ng_cs_job",
      container: "group2",
    });
    const csJobMandatory = autoJobInheritanceEnabled === "F";

    csJobField.isMandatory = csJobMandatory; // Set the field to mandatory if auto job inheritance is disabled
    csJobField.updateDisplayType({
      displayType: autoJobInheritanceEnabled === "T" ? "disabled" : "normal",
    });
    csJobField.helpText = `Select the CS Job to associate with this event. This will act as the financial reporting instrument. <br/><br/> If the CS Job is not listed, please create a new CS Job.`;

    classField = form.addField({
      id: "custpage_financialshow",
      type: "select",
      label: "Class",
      source: "classification",
      container: "group2",
    });

    // Freight Price Levels
    let awShipRateField = form.addField({
      id: "custpage_awshiprate",
      type: "currency",
      label: "Advanced Warehouse Drayage Rate",
      container: "group2",
    });
    let ibShipRateField = form.addField({
      id: "custpage_ibshiprate",
      type: "currency",
      label: "In Between Drayage Rate",
      container: "group2",
    });
    let dShipRateField = form.addField({
      id: "custpage_dshiprate",
      type: "currency",
      label: "Event Site Drayage Rate",
      container: "group2",
    });

    if (materialHandlingBetaEnabled) {
      awShipRateField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.HIDDEN,
      });
      ibShipRateField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.HIDDEN,
      });
      dShipRateField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.HIDDEN,
      });
    }

    let advPLField = form
      .addField({
        id: "custpage_advpricelevel",
        type: "select",
        label: "Advance Price Level",
        source: "pricelevel",
        container: "group2",
      })
      .updateBreakType({ breakType: serverWidget.FieldBreakType.STARTCOL });
    let stdPLField = form.addField({
      id: "custpage_stdpricelevel",
      type: "select",
      label: "Standard Price Level",
      source: "pricelevel",
      container: "group2",
    });
    let onSitePLField = form.addField({
      id: "custpage_showpricelevel",
      type: "select",
      label: "Event Site Price Level",
      source: "pricelevel",
      container: "group2",
    });
    let showMgmtPLField = form.addField({
      id: "custpage_showmgmtpricelevel",
      type: "select",
      label: "Show Management Price Level",
      source: "pricelevel",
      container: "group2",
    });

    let taxField;
    taxField = form
      .addField({
        id: "custpage_taxrate",
        type: "select",
        label: "Tax Group",
        container: "group2",
      })
      .updateBreakType({ breakType: serverWidget.FieldBreakType.STARTCOL })
      .updateDisplayType({ displayType: serverWidget.FieldDisplayType.NORMAL });
    form
      .addField({
        id: "custpage_gst_percent",
        type: "text",
        label: "GST/HST",
        container: "group2",
      })
      .updateDisplayType({ displayType: serverWidget.FieldDisplayType.INLINE });
    form
      .addField({
        id: "custpage_pst_percent",
        type: "text",
        label: "PST",
        container: "group2",
      })
      .updateDisplayType({ displayType: serverWidget.FieldDisplayType.INLINE });
    form
      .addField({
        id: "custpage_tax_percent",
        type: "text",
        label: "Tax Rate",
        container: "group2",
      })
      .updateDisplayType({ displayType: serverWidget.FieldDisplayType.INLINE });

    let freightMinimumField = form.addField({
      id: "custpage_cwt_minimum",
      type: "integer",
      label: "CWT Minimum",
      container: "group2",
    });

    freightMinimumField.defaultValue = Number(
      CS_SETTINGS.custrecord_ng_cs_freight_minimum,
    );

    freightMinimumField.updateDisplayType({
      displayType: materialHandlingBetaEnabled
        ? serverWidget.FieldDisplayType.NODISPLAY
        : serverWidget.FieldDisplayType.NORMAL,
    });

    if (cancellationEnabled) {
      form
        .addField({
          id: "custpage_cnclpct",
          type: "percent",
          label: "Cancellation Charge %",
          container: "group2",
        })
        .updateBreakType({
          breakType: serverWidget.FieldBreakType.NONE,
        })
        .updateDisplayType({
          displayType: serverWidget.FieldDisplayType.NORMAL,
        }).defaultValue = CS_SETTINGS.custrecord_ng_cs_def_canc_chrg_pct;
    }

    try {
      if (taxField) {
        taxField.helpText = "Select the tax code for the show.";
        taxField.updateBreakType({ breakType: "startcol" });
        let taxGroupFilter = [["isinactive", "is", "F"]];
        let taxGroupColumns = [
          search.createColumn({ name: "itemid", sort: "ASC" }),
        ];

        let taxGroupSearch = search.create({
          type: "taxgroup",
          filters: taxGroupFilter,
          columns: taxGroupColumns,
        });

        let results = [];
        getAllResultsFor(taxGroupSearch, (result) => {
          results.push(result);
        });

        if (results.length !== 0) {
          taxField.addSelectOption({
            value: "",
            text: "",
            isSelected: true,
          });
          results.forEach((res) => {
            taxField.addSelectOption({
              value: res.id,
              text: res.getValue({ name: "itemid" }),
            });
          });
        }
      }
    } catch (e) {
      log.error('SuiteTax Enabled', e)
    }

    form.addField({
      id: "custpage_advorderdate",
      type: "date",
      label: "Last Date for Advance Order Pricing",
      container: "datesmain_tab",
    });
    form.addField({
      id: "custpage_whseshipdate",
      type: "date",
      label: "Warehouse Ship Date",
      container: "datesmain_tab",
    });
    form.addField({
      id: "custpage_webstartdate",
      type: "date",
      label: "Website Start Date",
      container: "datesmain_tab",
    });
    form.addField({
      id: "custpage_webenddate",
      type: "date",
      label: "Website End Date",
      container: "datesmain_tab",
    });

    // ========= Surcharges =========
    form.addTab({
      id: "custpage_tab_surcharges",
      label: "Surcharges",
    });
    const surchargeSublist = form.addSublist({
      id: "custpage_sublist_surcharges",
      type: serverWidget.SublistType.INLINEEDITOR,
      label: "Surcharges",
      tab: "custpage_tab_surcharges",
    });

    surchargeSublist.addField({
      id: "selected",
      type: serverWidget.FieldType.CHECKBOX,
      label: "Select",
    });
    // add hidden surcharge id field
    surchargeSublist
      .addField({
        id: "custpage_surcharge_id",
        type: serverWidget.FieldType.TEXT,
        label: "Surcharge ID",
      })
      .updateDisplayType({
        displayType: serverWidget.FieldDisplayType.HIDDEN,
      });
    surchargeSublist
      .addField({
        id: "custpage_surcharge_name",
        type: serverWidget.FieldType.TEXT,
        label: "Name",
      })
      .updateDisplayType({
        displayType: serverWidget.FieldDisplayType.DISABLED,
      });
    surchargeSublist
      .addField({
        id: "custpage_surcharge_memo",
        type: serverWidget.FieldType.TEXT,
        label: "Memo",
      })
      .updateDisplayType({
        displayType: serverWidget.FieldDisplayType.DISABLED,
      });
    surchargeSublist
      .addField({
        id: "custpage_surcharge_select_items",
        type: serverWidget.FieldType.CHECKBOX,
        label: "Applied To Individual Items",
        source: "item",
      })
      .updateDisplayType({
        displayType: serverWidget.FieldDisplayType.DISABLED,
      });
    surchargeSublist
      .addField({
        id: "custpage_surcharge_appl_items",
        type: serverWidget.FieldType.TEXT,
        label: "Applied To Items",
      })
      .updateDisplayType({
        displayType: serverWidget.FieldDisplayType.DISABLED,
      });
    surchargeSublist
      .addField({
        id: "custpage_surcharge_type",
        type: serverWidget.FieldType.SELECT,
        label: "Type",
        source: "customlist_ng_cses_surcharge_chg_type",
      })
      .updateDisplayType({
        displayType: serverWidget.FieldDisplayType.DISABLED,
      });
    surchargeSublist
      .addField({
        id: "custpage_surcharge_flat",
        type: serverWidget.FieldType.CURRENCY,
        label: "Charge Flat",
      })
      .updateDisplayType({
        displayType: serverWidget.FieldDisplayType.DISABLED,
      });
    surchargeSublist
      .addField({
        id: "custpage_surcharge_percent",
        type: serverWidget.FieldType.PERCENT,
        label: "Charge Percent",
      })
      .updateDisplayType({
        displayType: serverWidget.FieldDisplayType.DISABLED,
      });

    // Query result mapped to sublist
    const queryResults = query
      .runSuiteQL({
        query: `
        SELECT
          id,
          name,
          custrecord_ng_cses_surcharge_default_wiz,
          custrecord_ng_cses_surcharge_memo,
          custrecord_ng_cses_surcharge_slct_items,
          custrecord_ng_cses_surcharge_appl_items,
          custrecord_ng_cses_surcharge_chg_type,
          custrecord_ng_cses_surcharge_chg_flat,
          custrecord_ng_cses_surcharge_chg_percent,
        FROM
          customrecord_ng_cses_event_surcharge
        ORDER BY
          custrecord_ng_cses_surcharge_default_wiz DESC
      `,
      })
      .asMappedResults();

    // Map query result to sublist
    queryResults.forEach((result, index) => {
      surchargeSublist.setSublistValue({
        id: "custpage_surcharge_id",
        line: index,
        value: result.id,
      });
      if (result.custrecord_ng_cses_surcharge_default_wiz === "T") {
        surchargeSublist.setSublistValue({
          id: "selected",
          line: index,
          value: "T",
        });
      }
      if (result.name) {
        surchargeSublist.setSublistValue({
          id: "custpage_surcharge_name",
          line: index,
          value: result.name,
        });
      }
      if (result.custrecord_ng_cses_surcharge_memo) {
        surchargeSublist.setSublistValue({
          id: "custpage_surcharge_memo",
          line: index,
          value: result.custrecord_ng_cses_surcharge_memo,
        });
      }
      if (result.custrecord_ng_cses_surcharge_slct_items === "T") {
        surchargeSublist.setSublistValue({
          id: "custpage_surcharge_select_items",
          line: index,
          value: "T",
        });
      }
      if (result.custrecord_ng_cses_surcharge_appl_items) {
        surchargeSublist.setSublistValue({
          id: "custpage_surcharge_appl_items",
          line: index,
          value: result.custrecord_ng_cses_surcharge_appl_items,
        });
      }
      if (result.custrecord_ng_cses_surcharge_chg_type) {
        surchargeSublist.setSublistValue({
          id: "custpage_surcharge_type",
          line: index,
          value: result.custrecord_ng_cses_surcharge_chg_type,
        });
      }
      if (result.custrecord_ng_cses_surcharge_chg_flat) {
        surchargeSublist.setSublistValue({
          id: "custpage_surcharge_flat",
          line: index,
          value: result.custrecord_ng_cses_surcharge_chg_flat,
        });
      }
      if (result.custrecord_ng_cses_surcharge_chg_percent) {
        surchargeSublist.setSublistValue({
          id: "custpage_surcharge_percent",
          line: index,
          value: result.custrecord_ng_cses_surcharge_chg_percent,
        });
      }
    });

    // ========= Venue Operations =========
    if (CS_SETTINGS.custrecord_ng_cs_enable_venue_operations === "T") {
      // Venue Operation Body Fields
      form.addFieldGroup({
        id: "custpage_group_venue_ops",
        label: "Venue Operations",
      });

      form.addField({
        id: "custpage_in_date",
        type: serverWidget.FieldType.DATE,
        label: "In Date",
        container: "custpage_group_venue_ops",
      }).defaultValue = inDate ? moment(inDate).toDate() : "";
      form.addField({
        id: "custpage_start_date",
        type: serverWidget.FieldType.DATE,
        label: "Start Date",
        container: "custpage_group_venue_ops",
      }).defaultValue = startDate ? moment(startDate).toDate() : "";
      form.addField({
        id: "custpage_end_date",
        type: serverWidget.FieldType.DATE,
        label: "End Date",
        container: "custpage_group_venue_ops",
      }).defaultValue = endDate ? moment(endDate).toDate() : "";
      form.addField({
        id: "custpage_out_date",
        type: serverWidget.FieldType.DATE,
        label: "Out Date",
        container: "custpage_group_venue_ops",
      }).defaultValue = outDate ? moment(outDate).toDate() : "";
      form.addField({
        id: "custpage_planned_attendance",
        type: serverWidget.FieldType.INTEGER,
        label: "Total Planned Attendance",
        container: "custpage_group_venue_ops",
      }).defaultValue = totalPlannedAttendance || "";
      form.addField({
        id: "custpage_actual_attendance",
        type: serverWidget.FieldType.INTEGER,
        label: "Total Actual Attendance",
        container: "custpage_group_venue_ops",
      }).defaultValue = totalActualAttendance || "";
      form
        .addField({
          id: "custpage_in_time",
          type: serverWidget.FieldType.TIMEOFDAY,
          label: "In Time",
          container: "custpage_group_venue_ops",
        })
        .updateBreakType({
          breakType: serverWidget.FieldBreakType.STARTCOL,
        }).defaultValue = inTime || "";
      form.addField({
        id: "custpage_start_time",
        type: serverWidget.FieldType.TIMEOFDAY,
        label: "Start Time",
        container: "custpage_group_venue_ops",
      }).defaultValue = startTime || "";
      form.addField({
        id: "custpage_end_time",
        type: serverWidget.FieldType.TIMEOFDAY,
        label: "End Time",
        container: "custpage_group_venue_ops",
      }).defaultValue = endTime || "";
      form.addField({
        id: "custpage_out_time",
        type: serverWidget.FieldType.TIMEOFDAY,
        label: "Out Time",
        container: "custpage_group_venue_ops",
      }).defaultValue = outTime || "";

      // Bookings Sublist
      form.addTab({ id: "custpage_tab_bookings", label: "Bookings" });

      const bookingsSublist = form.addSublist({
        id: "custpage_sublist_bookings",
        type: serverWidget.SublistType.INLINEEDITOR,
        label: "Bookings",
        tab: "custpage_tab_bookings",
      });
      bookingsSublist.addField({
        id: "custcol_bk_space",
        type: serverWidget.FieldType.SELECT,
        label: "Space",
        source: "61",
      });
      bookingsSublist.addField({
        id: "custcol_bk_start_date",
        type: serverWidget.FieldType.DATE,
        label: "Start Date",
      });
      bookingsSublist.addField({
        id: "custcol_bk_start_time",
        type: serverWidget.FieldType.TIMEOFDAY,
        label: "Start Time",
      });
      bookingsSublist.addField({
        id: "custcol_bk_end_date",
        type: serverWidget.FieldType.DATE,
        label: "End Date",
      });
      bookingsSublist.addField({
        id: "custcol_bk_end_time",
        type: serverWidget.FieldType.TIMEOFDAY,
        label: "End Time",
      });
      bookingsSublist.addField({
        id: "custcol_bk_booking_usage",
        type: serverWidget.FieldType.SELECT,
        label: "Booking Usage",
        source: "225",
      });
      bookingsSublist.addField({
        id: "custcol_bk_booking_rate",
        type: serverWidget.FieldType.SELECT,
        label: "Booking Rate",
        source: "334",
      });
      bookingsSublist.addField({
        id: "custcol_bk_status",
        type: serverWidget.FieldType.SELECT,
        label: "Status",
        source: "127",
      });
      bookingsSublist.addField({
        id: "custcol_bk_description",
        type: serverWidget.FieldType.TEXT,
        label: "Description",
      });

      // Maps the request param prefix to the appropriate bookings sublist field id
      const bookingsParamsMapping = {
        sd: "custcol_bk_start_date",
        st: "custcol_bk_start_time",
        ed: "custcol_bk_end_date",
        et: "custcol_bk_end_time",
        sp: "custcol_bk_space",
        bs: "custcol_bk_status",
        bu: "custcol_bk_booking_usage",
        br: "custcol_bk_booking_rate",
        bd: "custcol_bk_description",
      };

      // Set the sublist field values
      Array.from({ length: 10 }, (_, i) => i + 1).forEach((num) => {
        Object.entries(bookingsParamsMapping).forEach(
          ([queryParam, sublistFieldId]) => {
            const value = requestParams[`${queryParam}${num}`];
            if (value) {
              bookingsSublist.setSublistValue({
                id: sublistFieldId,
                line: num - 1,
                value,
              });
            }
          },
        );
      });
    }

    // ========= Event Sublists =========
    let timeList = getShowTimes();
    form = addDateSubLists(form, timeList);

    // ========= Event Freight =========
    let freightSublist = null;

    if (!materialHandlingBetaEnabled) {
      freightSublist = form.addSublist({
        id: "custpage_freightlist",
        type: serverWidget.SublistType.LIST,
        label: "Freight Table",
        tab: "freight_tab",
      });

      // == Construct Freight Sublist ==
      let freightItems = getFreightItems();

      freightSublist.addMarkAllButtons();
      freightSublist.addField({
        id: "selected",
        type: serverWidget.FieldType.CHECKBOX,
        label: "Add to Event",
      });
      freightSublist.addField({
        id: "custcol_name",
        type: serverWidget.FieldType.TEXT,
        label: "Freight Item",
      });
      freightSublist.addField({
        id: "custcol_altname",
        type: serverWidget.FieldType.TEXT,
        label: "Alt Name",
      });
      freightSublist.addField({
        id: "custcol_description",
        type: serverWidget.FieldType.TEXT,
        label: "Description",
      });
      freightSublist
        .addField({
          id: "custcol_internalid",
          type: serverWidget.FieldType.TEXT,
          label: "id",
        })
        .updateDisplayType({
          displayType: serverWidget.FieldDisplayType.HIDDEN,
        });
      freightSublist
        .addField({
          id: "custcol_baseprice",
          type: serverWidget.FieldType.TEXT,
          label: "base price",
        })
        .updateDisplayType({
          displayType: serverWidget.FieldDisplayType.HIDDEN,
        });

      // == Populate Freight Sublist ==
      let freightLineCount = 0;

      freightItems.forEach((freightItem) => {
        log.debug({
          title: "🌕 Data from freight item:",
          details: freightItem,
        });

        if (!materialHandlingBetaEnabled) {
          freightSublist.setSublistValue({
            id: "selected",
            value: "T",
            line: freightLineCount,
          });

          freightItem.name &&
            freightSublist.setSublistValue({
              id: "custcol_name",
              line: freightLineCount,
              value: freightItem.name,
            });
          freightItem.displayName &&
            freightSublist.setSublistValue({
              id: "custcol_altname",
              value: freightItem.displayName,
              line: freightLineCount,
            });
          freightItem.description &&
            freightSublist.setSublistValue({
              id: "custcol_description",
              value: freightItem.description,
              line: freightLineCount,
            });
          freightItem.id &&
            freightSublist.setSublistValue({
              id: "custcol_internalid",
              value: freightItem.id,
              line: freightLineCount,
            });
          freightItem.price &&
            freightSublist.setSublistValue({
              id: "custcol_baseprice",
              value: freightItem.price,
              line: freightLineCount,
            });

          freightLineCount++;
        }
      });
    }

    let additionalInfoList = form.addSublist({
      id: "custpage_adtnl_info",
      type: serverWidget.SublistType.LIST,
      label: "Additional Info Links",
      tab: "adtnlinfo_tab",
    });

    // ========= Event Portal Definitions =========
    populateSublist({
      sublist: additionalInfoList,
      data: getAdditionalInfoLinkTemplates(),
      options: {
        addMarkAllButtons: true,
        checkboxes: true,
        columnMapping: additionalInfoMap,
      },
    });

    // ========= Event Web Blurbs =========
    let webBlurbList = form.addSublist({
      id: "custpage_web_blurbs",
      type: serverWidget.SublistType.LIST,
      label: "Web Blurbs",
      tab: "web_blurb_tab",
    });
    populateSublist({
      sublist: webBlurbList,
      data: getWebBlurbTemplates(),
      options: {
        addMarkAllButtons: true,
        checkboxes: true,
        columnMapping: webBlurbMap,
      },
    });

    // ========= Event Item Collections =========
    let itemCollectionList = form.addSublist({
      id: "custpage_item_coll",
      type: serverWidget.SublistType.LIST,
      label: "Item Collections",
      tab: "item_coll_tab",
    });

    populateSublist({
      sublist: itemCollectionList,
      data: getCollectionTemplates(form),
      options: {
        actions: true,
        checkboxes: true,
        addMarkAllButtons: true,
        columnMapping: itemCollectionMap,
      },
    });

    eventNameField.isMandatory = true;
    const useJobNumbering =
      CS_SETTINGS.custrecord_ng_cs_use_job_numbering === "T";

    eventNumberField.isMandatory = useJobNumbering;

    eventNumberField.updateDisplayType({
      displayType: useJobNumbering
        ? serverWidget.FieldDisplayType.NORMAL
        : serverWidget.FieldDisplayType.DISABLED,
    });

    if (csJobParam) {
      csJobField.defaultValue = csJobParam;
      csJobField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.DISABLED,
      });
    }

    let classMandatory = COMPANY_PREFERENCES.getValue({
      fieldId: "classmandatory",
    });

    if (classMandatory) {
      classField.isMandatory = true;
    }

    if (job || csJobParam) {
      if (eventName) {
        eventNameField.defaultValue = eventName;
        eventNameField.updateDisplayType({
          displayType: serverWidget.FieldDisplayType.DISABLED,
        });
      }
      if (useJobNumbering && eventNumber) {
        eventNumberField.updateDisplayType({
          displayType: serverWidget.FieldDisplayType.DISABLED,
        }).defaultValue = eventNumber;
      }
    }

    if (subsidiariesEnabled) {
      subsidiaryField.updateBreakType({
        breakType: serverWidget.FieldBreakType.STARTCOL,
      });
      subsidiaryField.isMandatory = true;
      subsidiaryField.defaultValue =
        (subsidiaryField.defaultValue = subsidiary) ||
        CS_SETTINGS.custrecord_ng_cs_default_show_subsidiary;
    }

    awShipRateField.defaultValue = "0.00";
    ibShipRateField.defaultValue = "0.00";
    dShipRateField.defaultValue = "0.00";

    let defaultPriceLevels = {
      advancedPriceLevel: CS_SETTINGS.custrecord_ng_cs_default_adv_show_price,
      standardPriceLevel: CS_SETTINGS.custrecord_ng_cs_default_std_show_price,
      onSitePriceLevel: CS_SETTINGS.custrecord_ng_cs_default_onst_show_price,
      showMgmtPriceLevel: CS_SETTINGS.custrecord_ng_cs_default_show_mgmt_price,
    };

    if (defaultPriceLevels.advancedPriceLevel) {
      advPLField.defaultValue = defaultPriceLevels.advancedPriceLevel;
    }
    if (defaultPriceLevels.standardPriceLevel) {
      stdPLField.defaultValue = defaultPriceLevels.standardPriceLevel;
    }
    if (defaultPriceLevels.onSitePriceLevel) {
      onSitePLField.defaultValue = defaultPriceLevels.onSitePriceLevel;
    }
    if (defaultPriceLevels.showMgmtPriceLevel) {
      showMgmtPLField.defaultValue = defaultPriceLevels.showMgmtPriceLevel;
    }

    awShipRateField.updateBreakType({
      breakType: serverWidget.FieldBreakType.STARTCOL,
    });
    eventTypeField.isMandatory = true;

    if (eventType) {
      eventTypeField.defaultValue = eventType;
    }

    form.addSubmitButton({
      label: "Submit",
    });

    writePage({
      pageObject: form,
    });
  };

  /**
   * Runs the submission of the form to create the event
   * @param {ServerRequest} req
   * @param {ServerResponse} res
   * @returns {void}
   * */
  const submitForm = (req, res) => {
    const { parameters: requestParams } = req;
    const {
      custpage_showname: eventName,
      custpage_shownumber: eventNumber,
      custpage_customer: customer,
      custpage_subsidiary: subsidiary,
      custpage_showtype: showType,
      custpage_location: location,
      custpage_venue: eventVenue,
      custpage_financialshow: financialShow,
      custpage_awshiprate: advWhseShipRate,
      custpage_ibshiprate: ibWhseShipRate,
      custpage_dshiprate: directShipRate,
      custpage_stdpricelevel: stdPriceLevel,
      custpage_advpricelevel: advPriceLevel,
      custpage_showpricelevel: sitePriceLevel,
      custpage_showmgmtpricelevel: siteMgmtPriceLevel,
      custpage_taxrate: eventTax,
      custpage_cnclpct: cnclPct,
      custpage_advorderdate: advOrderDate,
      custpage_whseshipdate: whseShipDate,
      custpage_webstartdate: webStartDate,
      custpage_webenddate: webEndDate,
      custpage_show_status: showStatus,
      custpage_cwt_minimum: cwtMinimum,
      custpage_inherit_event_for_job: inheritJob,
      // Venue Operation Fields
      custpage_in_date: inDate,
      custpage_in_time: inTime,
      custpage_start_date: startDate,
      custpage_start_time: startTime,
      custpage_end_date: endDate,
      custpage_end_time: endTime,
      custpage_out_date: outDate,
      custpage_out_time: outTime,
      custpage_planned_attendance: plannedAttendance,
      custpage_actual_attendance: actualAttendance,
    } = requestParams;

    // Gets modified in the function of inheritance
    let csJob = requestParams.custpage_cs_job;

    const subsidiaryEnabled = runtime.isFeatureInEffect({
      feature: "SUBSIDIARIES",
    });
    const locationsEnabled = runtime.isFeatureInEffect({
      feature: "LOCATIONS",
    });
    const useJobNumbering =
      CS_SETTINGS.custrecord_ng_cs_use_job_numbering === "T";
    const defaultEventForm =
      CS_SETTINGS.custrecord_ng_cs_dflt_shw_tbl_form || null;
    const useCancellationCharge =
      CS_SETTINGS.custrecord_ng_cs_use_cancl_charge === "T";
    const locationEnabled = runtime.isFeatureInEffect({
      feature: "LOCATIONS",
    });

    const separators = {
      1: " ",
      2: " - ",
      3: " -- ",
      4: " | ",
      5: " || ",
      6: " : ",
      7: " :: ",
      8: " <> ",
    };

    const separator =
      separators[CS_SETTINGS.custrecord_ng_cs_name_number_separator] || " ";
    const nameNumberOrder = CS_SETTINGS.custrecord_ng_cs_name_number_ordering;
    // ========= Error Creation =========
    let failure = false;
    let failureSubject = "";
    let failureErrName = "";
    let failureErrDesc = "";
    let errList = [];

    // ========= Event Creation =========
    let eventNameComplete = "";
    let eventRecId = -1;
    let templateData = null;
    let boothLine = -1;
    let compName = "";
    let custName = "";
    let stateAbbrev = "";
    let addressLine = "";
    let addressMerge = "";

    // First construct the base event name
    if (useJobNumbering) {
      if (nameNumberOrder === "1") {
        eventNameComplete = `${eventNumber}${separator}${eventName}`;
      } else {
        eventNameComplete = `${eventName}${separator}${eventNumber}`;
      }
    } else {
      eventNameComplete = eventName;
    }

    // Search for existing events with similar names
    let nameSearch = search.create({
      type: "customrecord_show",
      filters: [
        ["isinactive", "is", "F"],
        "AND",
        ["name", "startswith", eventNameComplete]
      ],
      columns: ["name"]
    });

    let existingNames = [];
    nameSearch.run().each(function(result) {
      existingNames.push(result.getValue("name"));
      return true;
    });

    // If name exists, append incrementing number
    if (existingNames.length > 0) {
      let counter = 1;
      let tempName = eventNameComplete;
      while (existingNames.includes(tempName)) {
        tempName = `${eventNameComplete} (${counter})`;
        counter++;
      }
      eventNameComplete = tempName;
    }

    let eventRecord = record.create({
      type: "customrecord_show",
      isDynamic: true,
      defaultValues: {
        customform: defaultEventForm,
      },
    });

    if (subsidiaryEnabled) {
      eventRecord.setValue({
        fieldId: "custrecord_show_subsidiary",
        value: subsidiary,
      });
    }

    eventRecord
      .setValue({
        fieldId: "custrecord_show_type",
        value: showType,
      })
      .setValue({
        fieldId: "name",
        value: eventNameComplete,
      })
      .setValue({
        fieldId: "custrecord_show_customer",
        value: customer,
      });

    log.audit({ title: "Inherit CS Job?", details: inheritJob });

    if (inheritJob === "T") {
      log.audit({ title: "🟢 Inherit CS Job Enabled", details: inheritJob });

      // Create new CS Job from event name
      const csJobRecord = record.create({
        type: "customrecord_cseg_ng_cs_job",
        isDynamic: true,
      });

      let csJobSearch = search.create({
        type: "customrecord_cseg_ng_cs_job",
        filters: [
          ["isinactive", "is", "F"],
          "AND",
          ["name", "is", eventNameComplete],
        ],
        columns: ["name"],
      });

      let allCSJobs = [];
      getAllResultsFor(csJobSearch, (result) => {
        let csJobObj = {
          name: result.getValue({ name: "name" }),
          id: result.id,
        };

        allCSJobs.push(csJobObj);
      });

      if (allCSJobs.length !== 0) {
        log.audit({
          title: "🟢 CS Job Found:",
          details: allCSJobs[0],
        });

        eventNameComplete = `${eventNameComplete} (${allCSJobs.length + 1})`;
      }

      // Required fields on CS Event wizard
      csJobRecord.setValue({
        fieldId: "name",
        value: eventNameComplete,
      });

      // Non-required fields on CS Event wizard
      location &&
        csJobRecord.setValue({
          fieldId: "custrecord_ng_cs_job_location",
          value: location,
        });
      customer &&
        csJobRecord.setValue({
          fieldId: "custrecord_ng_cs_job_client",
          value: customer,
        });
      eventNumber &&
        csJobRecord.setValue({
          fieldId: "custrecord_ng_cs_job_number",
          value: eventNumber,
        });
      subsidiary &&
        csJobRecord.setValue({
          fieldId: "custrecord_ng_cs_job_subsidiaries",
          value: subsidiary,
        });

      csJob = csJobRecord.save({
        enableSourcing: true,
        ignoreMandatoryFields: true,
      });
    }

    csJob &&
      eventRecord
        .setValue({
          fieldId: "custrecord_show_job",
          value: csJob,
        })
        .setValue({
          fieldId: "custrecord_58_cseg_ng_cs_job",
          value: csJob,
        });

    financialShow &&
      eventRecord.setValue({
        fieldId: "custrecord_fin_show",
        value: financialShow,
      });

    eventRecord
      .setValue({
        fieldId: "custrecord_show_status",
        value: showStatus,
      })
      .setValue({
        fieldId: "custrecord_adv_wh_ship_rate",
        value: advWhseShipRate,
      })
      .setValue({
        fieldId: "custrecord_inbetween_ship_rate",
        value: ibWhseShipRate,
      })
      .setValue({
        fieldId: "custrecord_direct_shipping_rate",
        value: directShipRate,
      })
      .setValue({
        fieldId: "custrecord_std_price_level",
        value: stdPriceLevel,
      })
      .setValue({
        fieldId: "custrecord_adv_price_level",
        value: advPriceLevel,
      })
      .setValue({
        fieldId: "custrecord_site_price_level",
        value: sitePriceLevel,
      })
      .setValue({
        fieldId: "custrecord_show_mgmnt_price_lvl",
        value: siteMgmtPriceLevel,
      })
      .setValue({
        fieldId: "custrecord_ng_cs_cwt_minimum",
        value: cwtMinimum,
      });
    let venueData = {};

    if (eventVenue) {
      log.audit({
        title: "🟢 Venue found loading record: ",
        details: eventVenue,
      });

      const venueRecord = record.load({
        type: "customrecord_facility",
        id: eventVenue,
      });

      venueData = {
        address1: venueRecord.getValue({
          fieldId: "custrecord_facility_address1",
        }),
        address2: venueRecord.getValue({
          fieldId: "custrecord_facility_address2",
        }),
        city: venueRecord.getValue({ fieldId: "custrecord_facility_city" }),
        state: venueRecord.getValue({ fieldId: "custrecord_facility_state" }),
        zip: venueRecord.getValue({ fieldId: "custrecord_facility_zip" }),
        country: venueRecord.getValue({
          fieldId: "custrecord_facility_country",
        }),
        rates: {
          tax: {
            gst: venueRecord.getValue({
              fieldId: "custrecord_ng_cs_facility_gst_pct",
            }),
            pst: venueRecord.getValue({
              fieldId: "custrecord_ng_cs_facility_pst_pct",
            }),
            percent: venueRecord.getValue({
              fieldId: "custrecord_ng_cs_facility_tax_percent",
            }),
            value: venueRecord.getValue({
              fieldId: "custrecord_ng_cs_facility_tax_rate",
            }),
          },
        },
      };

      log.audit({ title: "🏚️ Venue Data loaded:", details: venueData });

      eventRecord.setValue({
        fieldId: "custrecord_facility",
        value: eventVenue,
      });

      templateData = {
        addressTemplate: CS_SETTINGS.custrecord_ng_cs_fclty_addy_template,
        useSubsidiaryName:
          CS_SETTINGS.custrecord_ng_cs_name_from_subsidiary === "T",
        boothLine: CS_SETTINGS.custrecord_ng_cs_booth_num_line_text,
      };

      const subsidiaryName =
        subsidiary &&
        subsidiaryEnabled &&
        templateData.useSubsidiaryName &&
        search.lookupFields({
          type: "subsidiary",
          id: subsidiary,
          columns: ["name"],
        }).name;

      boothLine = templateData.boothLine || BOOTH_LINE;
      compName = subsidiaryName || COMPANY_INFO.getValue("companyname");
      custName = customer
        ? search.lookupFields({
            type: "customer",
            id: customer,
            columns: ["entityid"],
          }).entityid
        : "";
      stateAbbrev = venueData.state ? getStateShortName(venueData.state) : "";
      addressLine = `${venueData.address1} ${
        venueData.address2 ? "\n" + venueData.address2 : ""
      }`;

      addressMerge = replacePlaceholders(
        templateData.addressTemplate,
        eventName,
        custName,
        boothLine,
        compName,
        addressLine,
        venueData.city,
        stateAbbrev,
        venueData.zip,
      );

      eventRecord.setValue({
        fieldId: "custrecord_ship_to_facility_address",
        value: addressMerge,
      });
      const venueAddressLine = addressLine;
      const venueAddress = `${venueAddressLine}\n${venueData.city} ${stateAbbrev} ${venueData.zip}`;

      eventRecord.setValue({
        fieldId: "custrecord_facility_address",
        value: venueAddress,
      });

      const countryIsUs = venueData.country === "230";
      const countryIsCanada = venueData.country === "37";
      // Tax fields
      const taxGstPercent = venueData.rates.tax.gst;
      const taxPstPercent = venueData.rates.tax.pst;
      const taxPercent = venueData.rates.tax.percent;
      const taxValue = venueData.rates.tax.value;

      if (eventTax) {
        eventRecord.setValue({
          fieldId: "custrecord_tax_rate",
          value: eventTax,
        });
        if (countryIsUs) {
          eventRecord.setValue({
            fieldId: "custrecord_tax_percent",
            value: taxPercent || "0.00",
          });
        } else {
          eventRecord.setValue({
            fieldId: "custrecord_ng_cs_evt_gst_pct",
            value: taxGstPercent || "0.00",
          });
          eventRecord.setValue({
            fieldId: "custrecord_ng_cs_evt_pst_pct",
            value: taxPstPercent || "0.00",
          });
        }
      }

      if (useCancellationCharge) {
        eventRecord.setValue({
          fieldId: "custrecord_cancellation_pct",
          value: cnclPct.replace("%", "") || "0.00",
        });
      }
      let locationData = {};
      if (locationsEnabled) {
        eventRecord.setValue({
          fieldId: "custrecord_show_venue",
          value: location,
        });
        if (location) {
          const locationRecord = record.load({
            type: "location",
            id: location,
          });

          let addressSubrecord = locationRecord.getSubrecord({
            fieldId: "mainaddress",
          });

          locationData = {
            address1: addressSubrecord.getValue({
              fieldId: "address1",
            }),
            address2: addressSubrecord.getValue({
              fieldId: "address2",
            }),
            city: addressSubrecord.getValue({ fieldId: "city" }),
            state: addressSubrecord.getValue({ fieldId: "state" }),
            zip: addressSubrecord.getValue({ fieldId: "zip" }),
            country: addressSubrecord.getValue({
              fieldId: "country",
            }),
          };

          boothLine = templateData.boothLine || BOOTH_LINE;
          compName = subsidiaryName || COMPANY_INFO.getValue("companyname");

          const customerNameLookup = customer
            ? search.lookupFields({
                type: "customer",
                id: customer,
                columns: ["entityid"],
              }).entityid
            : "";

          custName = customer ? customerNameLookup : "";
          addressLine = `${locationData.address1} ${
            locationData.address2 ? "\n" + locationData.address2 : ""
          }`;

          addressMerge = replacePlaceholders(
            templateData.addressTemplate,
            eventName,
            custName,
            boothLine,
            compName,
            addressLine,
            locationData.city,
            locationData.state,
            locationData.zip,
          );

          eventRecord.setValue({
            fieldId: "custrecord_ship_to_warehouse_address",
            value: addressMerge,
          });
        }
      }
    } else {
      log.audit({
        title: "🔴 No venue found on submission:",
        details: eventVenue,
      });
    }

    if (advOrderDate) {
      eventRecord.setValue({
        fieldId: "custrecord_adv_ord_date",
        value: moment(advOrderDate).toDate(),
      });
    }

    if (whseShipDate) {
      eventRecord.setValue({
        fieldId: "custrecord_wh_ship_date",
        value: moment(whseShipDate).toDate(),
      });
    }

    if (webStartDate) {
      eventRecord.setValue({
        fieldId: "custrecord_w_start_date",
        value: moment(webStartDate).toDate(),
      });
    }

    if (webEndDate) {
      eventRecord.setValue({
        fieldId: "custrecord_w_end_date",
        value: moment(webEndDate).toDate(),
      });
    }

    // ========= Venue Operation Fields =========
    if (CS_SETTINGS.custrecord_ng_cs_enable_venue_operations === "T") {
      const venueOperationFields = [
        {
          fieldId: "custrecord_cses_in_date",
          value: inDate,
          type: "date",
        },
        { fieldId: "custrecord_cses_in_time", value: inTime, type: "time" },
        {
          fieldId: "custrecord_cses_start_date",
          value: startDate,
          type: "date",
        },
        {
          fieldId: "custrecord_cses_start_time",
          value: startTime,
          type: "time",
        },
        {
          fieldId: "custrecord_cses_end_date",
          value: endDate,
          type: "date",
        },
        { fieldId: "custrecord_cses_end_time", value: endTime, type: "time" },
        {
          fieldId: "custrecord_cses_out_date",
          value: outDate,
          type: "date",
        },
        { fieldId: "custrecord_cses_out_time", value: outTime, type: "time" },
        {
          fieldId: "custrecord_cses_total_planned_attnd",
          value: plannedAttendance,
          type: "number",
        },
        {
          fieldId: "custrecord_cses_total_actual_attnd",
          value: actualAttendance,
          type: "number",
        },
      ];

      log.audit({
        title: "Venue Operations",
        details: venueOperationFields,
      });

      venueOperationFields.forEach(({ fieldId, value: rawValue, type }) => {
        if (rawValue) {
          const value = type === "date" ? moment(rawValue).toDate() : rawValue;
          type === "time"
            ? eventRecord.setText({
                fieldId,
                text: value,
              })
            : eventRecord.setValue({
                fieldId,
                value,
              });
        }
      });
    }

    try {
      eventRecId = eventRecord.save({
        ignoreMandatoryFields: true,
        enableSourcing: true,
      });
    } catch (err) {
      log.error({ title: "🔴 Error occurred creating event: ", details: err });
      failure = true;
      failureSubject = "Error encountered creating event record";
      failureErrName = err.name;
      failureErrDesc = err.message;
    }

    if (eventRecId !== -1) {
      log.audit({
        title: "🟢 Event record created successfully: ",
        details: eventRecId,
      });

      // ========= Event Entity Group Creation & Contact Search =========
      let fields = [];
      let values = [];
      let entityGroupRecId = -1;
      let campaignRecId = -1;
      let savedContactSearchId = -1;

      try {
        let contactSearch = search.create({
          type: "contact",
          filters: [
            ["isinactive", "is", "F"],
            "AND",
            [
              "custrecord_booth_contact.custrecord_booth_show_table",
              "anyof",
              eventRecId,
            ],
          ],
          columns: [
            search.createColumn({ name: "entityid" }),
            search.createColumn({ name: "email" }),
            search.createColumn({ name: "phone" }),
            search.createColumn({ name: "altphone" }),
            search.createColumn({ name: "fax" }),
            search.createColumn({ name: "company" }),
            search.createColumn({ name: "altemail" }),
            search.createColumn({ name: "custentity_ng_cs_last_imported_on" }),
            search.createColumn({
              name: "custrecord_booth_number",
              join: "custrecord_booth_contact",
            }),
            search.createColumn({
              name: "custrecord_booth_exhibitor",
              join: "custrecord_booth_contact",
            }),
            search.createColumn({
              name: "custrecord_booth_contact",
              join: "custrecord_booth_contact",
            }),
            search.createColumn({
              name: "internalid",
              join: "custrecord_booth_contact",
            }),
          ],
        });

        // Look for existing contact search
        let contactSearchResults = [];

        let existingContactSearch = search.create({
          type: "savedsearch",
          filters: [
            ["isinactive", "is", "F"],
            "AND",
            ["titletext", "startswith", `Contact Search Event: ${eventRecId}`],
          ],
          columns: [search.createColumn({ name: "title" })],
        });

        getAllResultsFor(existingContactSearch, (result) => {
          let savedSearchResult = {
            id: result.id,
            title: result.getValue({ name: "title" }),
          };

          contactSearchResults.push(savedSearchResult);
        });

        if (contactSearchResults.length !== 0) {
          log.audit({
            title: "🟢 Saved Searches Found:",
            details: contactSearchResults,
          });

          let contactSavedSearchTitles = contactSearchResults.map(
            (ss) => ss.title,
          );
          let matchesTitleClosest = matchesStringClosest(
            `Contact Search Event: ${eventRecId}`,
            contactSavedSearchTitles,
          );

          log.audit({
            title: "✅ Closest Search Match confirmed:",
            details: matchesTitleClosest,
          });

          let contactSearchMatchId = contactSearchResults.find(
            (ss) => ss.title === matchesTitleClosest,
          )?.id;
          if (contactSearchMatchId) {
            log.audit({
              title: "🟢 100% match found, deleting search:",
              details: matchesTitleClosest,
            });

            // Load saved search record and modify it then save...??

            search.delete({
              id: contactSearchMatchId,
            }); // May not delete due to dependencies

            contactSearchMatchId = -1;
          }
        }

        // ========== Save Search Into Database ==========
        contactSearch.title = `Contact Search Event: ${eventRecId}`;
        contactSearch.description = `Contact Search for ${eventName}`;
        contactSearch.id = `customsearch_evt_contact_srch_${eventRecId}`;
        contactSearch.isPublic = true;
        savedContactSearchId = contactSearch.save();

        fields.push("custrecord_cs_st_contact_search");
        values.push(savedContactSearchId);
      } catch (err) {
        log.error({ title: "🔴 Error creating contact search:", details: err });
      }

      // == Run Contact Search & Create Entity Group ==
      try {
        if (savedContactSearchId !== -1) {
          let entityGroupName = `Contact Group Event: ${eventRecId}`;

          if (entityGroupName.length > 51) {
            entityGroupName = `Contact Group Event: ${eventRecId}`;
          }

          // Find Similar Entity Groups
          const entityGroupSearch = search.create({
            type: "entitygroup",
            filters: [
              ["isinactive", "is", "F"],
              "AND",
              ["grouptype", "is", "Contact"],
              "AND",
              [
                ["groupname", "contains", entityGroupName],
                "OR",
                ["groupname", "is", entityGroupName],
                "OR",
                ["groupname", "startswith", entityGroupName],
              ],
            ],
            columns: [search.createColumn({ name: "groupname" })],
          });

          let entityGroupResults = [];
          getAllResultsFor(entityGroupSearch, (result) => {
            let entityGroupObj = {
              id: result.id,
              name: result.getValue({ name: "groupname" }),
            };

            entityGroupResults.push(entityGroupObj);
          });

          log.audit({
            title: "🔎 Found similar groups:",
            details: entityGroupResults,
          });

          // Which one of these matches the closest?
          let entityGroupNames = entityGroupResults.map((group) => group.name);
          let entityGroupMatch = matchesStringClosest(
            entityGroupName,
            entityGroupNames,
          );

          log.audit({
            title: "✅ Closest Match confirmed:",
            details: entityGroupMatch,
          });

          let entityGroupMatchId = entityGroupResults.find(
            (group) => group.name === entityGroupMatch,
          )?.id;

          // If entity group match is 100% match, delete that group and create a new one
          if (entityGroupMatchId) {
            log.audit({
              title: "🟢 100% match found, deleting group:",
              details: entityGroupMatch,
            });

            record.delete({
              type: "entitygroup",
              id: entityGroupMatchId,
            });

            entityGroupMatchId = -1;
          }

          // == Create Entity Group ==
          let entityGroupRecord = record.create({
            type: "entitygroup",
            isDynamic: true,
            defaultValues: {
              grouptype: "Contact",
              dynamic: "T",
            },
          });

          entityGroupRecord
            .setValue({
              fieldId: "groupname",
              value: entityGroupName,
            })
            .setValue({
              fieldId: "groupowner",
              value: runtime.getCurrentUser().id,
            })
            .setValue({
              fieldId: "restrictedoownner",
              value: true,
            })
            .setValue({
              fieldId: "comments",
              value: `${EVENT_GENERATED_COMMENTS} ${eventName}`,
            })
            .setValue({
              fieldId: "savedsearch",
              value: savedContactSearchId,
            });

          try {
            entityGroupRecId = entityGroupRecord.save({
              ignoreMandatoryFields: true,
              enableSourcing: true,
            });

            fields.push("custrecord_cs_st_contact_group");
            values.push(entityGroupRecId);
          } catch (err) {
            log.error({
              title: "🔴 Error creating entity group:",
              details: err,
            });
          }
        }

        // ========= Campaign Creation =========
        let campaignName = `Event Campaign: ${eventName}`;
        const campaignRecord = record.create({
          type: "campaign",
          isDynamic: true,
        });

        campaignRecord
          .setValue({
            fieldId: "message",
            value: `${EVENT_GENERATED_COMMENTS}${eventName}`,
          })
          .setValue({
            fieldId: "title",
            value: campaignName,
          })
          .setValue({
            fieldId: "owner",
            value: runtime.getCurrentUser().id,
          });

        try {
          campaignRecId = campaignRecord.save({
            ignoreMandatoryFields: true,
            enableSourcing: true,
          });

          fields.push("custrecord_ng_cs_st_show_campaign");
          values.push(campaignRecId);
        } catch (err) {
          log.error({ title: "🔴 Error creating campaign:", details: err });
        }

        // ========= Event Entity Group Creation & Contact Search Value Summary =========

        if (entityGroupRecId !== -1) {
          let submitOptions = {};

          fields.forEach(
            (field, index) => (submitOptions[field] = values[index]),
          );

          record.submitFields({
            type: "customrecord_show",
            id: eventRecId,
            values: submitOptions,
            options: {
              enableSourcing: true,
              ignoreMandatoryFields: true,
            },
          });
        }
      } catch (err) {
        log.error({ title: "🔴 Error creating event:", details: err });
      }

      // ========= Event Dates Creation =========
      const timeList = getShowTimes();
      const showDates = createShowDates(eventRecId, timeList, req);
      const freightTables = createFreightTables(
        eventRecId,
        {
          advance: advWhseShipRate,
          inbetween: ibWhseShipRate,
          onsite: directShipRate,
        },
        req,
      );

      log.audit({ title: "🟢 Event Date Data:", details: showDates });

      if (showDates) {
        if (showDates.failure) {
          if (showDates.errorList.length !== 0) {
            errList.concat(showDates.errorList);
          } else {
            errList.push({
              failureSubject: showDates.errorSummary.failureSubject,
              failureErrName: showDates.errorSummary.failureErrName,
              failureErrDesc: showDates.errorSummary.failureErrDesc,
            });
          }
        } else if (showDates?.ids && showDates.ids.length !== 0) {
          log.audit({
            title: "🟢 Event Dates created successfully:",
            details: showDates.ids,
          });

          if (showDates?.errorList && showDates.errorList.length !== 0) {
            errList.concat(showDates.errorList);
          }
        }
      }

      if (freightTables) {
        if (freightTables.failure) {
          if (freightTables.errorList.length !== 0) {
            errList.concat(freightTables.errorList);
          } else {
            errList.push({
              failureSubject: freightTables.errorSummary.failureSubject,
              failureErrName: freightTables.errorSummary.failureErrName,
              failureErrDesc: freightTables.errorSummary.failureErrDesc,
            });
          }
        } else if (freightTables?.ids && freightTables.ids.length !== 0) {
          log.audit({
            title: "🟢 Freight Tables created successfully:",
            details: freightTables.ids,
          });
          if (
            freightTables?.errorList &&
            freightTables.errorList.length !== 0
          ) {
            errList.concat(freightTables.errorList);
          }
        }
      }

      // ========= Event Display Forms =========
      // Deprecated used to be used for sitebuilder product pages

      // const displayFormCount = req.getLineCount({
      //   group: "custpage_displayformlist",
      // });
      //
      // if (displayFormCount !== 0) {
      //   for (let line = 0; line < displayFormCount; line++) {
      //     const itemSelected = req.getSublistValue({
      //       group: "custpage_displayformlist",
      //       fieldId: "selected",
      //       line,
      //     });
      //     const displayFormId = req.getSublistValue({
      //       group: "custpage_displayformlist",
      //       fieldId: "custcol_internalid",
      //       line,
      //     });
      //     const displayFormRec = record.create({
      //       type: "customrecord_show_display_form",
      //       isDynamic: true,
      //     });
      //
      //     const groupId = req.getSublistValue({
      //       group: "custpage_displayformlist",
      //       fieldId: "custcol_groupid",
      //       line,
      //     });
      //
      //     try {
      //       const displayFormRecId = displayFormRec.save({
      //         ignoreMandatoryFields: true,
      //       });
      //
      //       log.audit({
      //         title: "🟢 Display Form created successfully:",
      //         details: displayFormRecId,
      //       });
      //     } catch (err) {
      //       log.error({
      //         title: "🔴 Error creating display form:",
      //         details: err,
      //       });
      //     }
      //
      //     const formSelected = Boolean(
      //       (typeof itemSelected === "boolean" && itemSelected) ||
      //         itemSelected === "T"
      //     );
      //
      //     displayFormRec
      //       .setValue({
      //         fieldId: "custrecord_sdf_wshow",
      //         value: formSelected,
      //       })
      //       .setValue({
      //         fieldId: "custrecord_sdf_show_table",
      //         value: eventRecId,
      //       })
      //       .setValue({
      //         fieldId: "custrecord_sdf_display_form",
      //         value: displayFormId,
      //       })
      //       .setValue({
      //         fieldId: "custrecord_sdf_display_form",
      //       })
      //       .setValue({
      //         fieldId: "custrecord_sdf_group",
      //         value: groupId,
      //       });
      //
      //     try {
      //       const displayFormRecId = displayFormRec.save({
      //         ignoreMandatoryFields: true,
      //       });
      //
      //       log.audit({
      //         title: "🟢 Display Form created successfully:",
      //         details: displayFormRecId,
      //       });
      //     } catch (err) {
      //       log.error({
      //         title: "🔴 Error creating display form:",
      //         details: err,
      //       });
      //
      //       errList.push({
      //         failureSubject: `Error encountered creating event web category record: ${eventRecId}`,
      //         failureErrName: err.name,
      //         failureErrDesc: err.message,
      //       });
      //     }
      //   }
      // }

      // ========= Create Event Web Items =========
      const webItemsResults = {
        additionalInfo: createAdditionalInfoItems(req, eventRecId),
        webBlurbs: createWebBlurbs(req, eventRecId),
        itemCollections: createItemCollections(req, eventRecId),
        bookings: createBookings(req, eventRecId),
        surcharges: createSurcharges(req, eventRecId)
      };

      log.audit({
        title: "🏁 Event Web Items Creation Results",
        details: webItemsResults
      });

      // Process any errors from web items creation
      Object.entries(webItemsResults).forEach(([itemType, result]) => {
        if (result?.errors && result.errors.length > 0) {
          result.errors.forEach(error => {
            errList.push({
              failureSubject: `Error in ${itemType}: ${error.subject || 'Error processing line ' + error.line}`,
              failureErrName: error.name,
              failureErrDesc: error.message || error.error
            });
          });
        }
      });

      let status = {};
      status.fail = failure || errList.length > 0;
      status.failureSubject = failureSubject;
      status.failureErrName = failureErrName;
      status.failureErrDesc = failureErrDesc;
      status.errList = errList;
      status.eventRecId = eventRecId;
      status.webItemsResults = webItemsResults;

      let sessionId = crypto
        .SHA256(`Event-${eventRecId}-created-${moment().format("MM/DD/YYYY")}`)
        .toString();

      log.audit({ title: "🟢 Session ID:", details: sessionId });

      runtime
        .getCurrentSession()
        .set({ name: sessionId, value: JSON.stringify(status) });

      redirect.toSuitelet({
        scriptId: runtime.getCurrentScript().id,
        deploymentId: runtime.getCurrentScript().deploymentId,
        parameters: {
          sid: sessionId,
        },
      });
    }
  };

  /**
   * Create the item collections for the event to display on the website
   * @param {ServerRequest} req
   * @param {Number} eventId
   * @returns {void}
   * */
  const createItemCollections = (req, eventId) => {
    const summary = {
      success: [],
      errors: [],
      childCollections: []
    };

    log.audit({
      title: "🟢 Starting Item Collections Creation",
      details: { eventId }
    });

    try {
      const group = "custpage_item_coll";
      const itemCollectionCount = Number(req.getLineCount({ group }));

      log.debug({
        title: "📊 Item Collection Count",
        details: { count: itemCollectionCount }
      });

      if (itemCollectionCount === 0) {
        log.audit({
          title: "ℹ️ No Item Collections to Process",
          details: "Skipping item collections creation"
        });
        return summary;
      }

      for (let line = 0; line < itemCollectionCount; line++) {
        try {
          const itemSelected = req.getSublistValue({
            group,
            name: "selected",
            line,
          });

          const targetTemplateId = req.getSublistValue({
            group,
            name: "custpage_template_id",
            line,
          });

          log.debug({
            title: "🔍 Processing Collection Line",
            details: { line, itemSelected, targetTemplateId }
          });

          const selected = Boolean(
            (typeof itemSelected === "boolean" && itemSelected) ||
            itemSelected === "T"
          );

          // Skip processing this collection if it wasn't selected by the user
          // Child collections should always be included, so we only skip if:
          // 1. The item is not selected AND
          // 2. The item is not a child collection
          const isChildCollection = req.getSublistValue({
            group,
            name: "parent",
            line
          });

          if (!selected && !isChildCollection) {
            log.debug({
              title: "⏩ Skipping Unselected Parent Collection",
              details: { line, targetTemplateId }
            });
            continue;
          }

          const itemRec = record.copy({
            type: "customrecord_ng_cs_item_collection",
            isDynamic: false,
            id: targetTemplateId,
          });

          itemRec
            .setValue({
              fieldId: "custrecord_ng_cs_itemcoll_event",
              value: eventId,
            })
            .setValue({
              fieldId: "custrecord_ng_cs_itemcoll_template",
              value: false,
            });

          const itemRecId = itemRec.save({
            ignoreMandatoryFields: true,
          });

          log.audit({
            title: "✅ Collection Created Successfully",
            details: { itemRecId, targetTemplateId }
          });

          summary.success.push({
            id: itemRecId,
            templateId: targetTemplateId
          });

          // Handle child collections
          const updatedCollection = record.load({
            type: "customrecord_ng_cs_item_collection",
            id: targetTemplateId,
          });

          const collectionId = updatedCollection.id;
          const parentId = updatedCollection.getValue({ fieldId: "parent" });

          log.debug({
            title: "👨‍👦 Checking for Child Collections",
            details: { collectionId, parentId }
          });

          if (!parentId) {
            const childCollections = getChildCollections(collectionId);
            
            if (childCollections.length > 0) {
              log.debug({
                title: "👨‍👦 Child Collections Found",
                details: { count: childCollections.length, children: childCollections }
              });

              const createdCollections = createChildCollections(
                childCollections,
                itemRecId,
                eventId
              );

              summary.childCollections.push(...createdCollections);

              log.audit({
                title: "✅ Child Collections Created",
                details: { createdCollections }
              });
            }
          }

        } catch (err) {
          log.error({
            title: "❌ Error Processing Collection Line",
            details: { line, error: err }
          });
          summary.errors.push({
            line,
            error: err.message,
            name: err.name
          });
        }
      }
    } catch (err) {
      log.error({
        title: "❌ Fatal Error in Item Collections Creation",
        details: err
      });
      summary.errors.push({
        error: err.message,
        name: err.name,
        isFatal: true
      });
    }

    log.audit({
      title: "🏁 Item Collections Creation Complete",
      details: summary
    });

    return summary;
  };

  /**
   * Create the web blurbs for the event to display on the website
   * @param {ServerRequest} req
   * @param {Number} eventId
   * @returns {void}
   * */
  const createWebBlurbs = (req, eventId) => {
    const summary = {
      success: [],
      errors: []
    };

    log.audit({
      title: "🟢 Starting Web Blurbs Creation",
      details: { eventId }
    });

    try {
      const group = "custpage_web_blurbs";
      const blurbCount = Number(req.getLineCount({ group }));

      log.debug({
        title: "📊 Web Blurb Count",
        details: { count: blurbCount }
      });

      if (blurbCount === 0) {
        log.audit({
          title: "ℹ️ No Web Blurbs to Process",
          details: "Skipping web blurbs creation"
        });
        return summary;
      }

      for (let line = 0; line < blurbCount; line++) {
        try {
          const itemSelected = req.getSublistValue({
            group,
            name: "selected",
            line,
          });

          const targetTemplateId = req.getSublistValue({
            group,
            name: "custpage_template_id",
            line,
          });

          log.debug({
            title: "🔍 Processing Web Blurb Line",
            details: { line, itemSelected, targetTemplateId }
          });

          const selected = Boolean(
            (typeof itemSelected === "boolean" && itemSelected) ||
            itemSelected === "T"
          );

          if (!selected) {
            log.debug({
              title: "⏩ Skipping Unselected Web Blurb",
              details: { line, targetTemplateId }
            });
            continue;
          }

          const itemRec = record.copy({
            type: "customrecord_ng_cs_show_table_web_blurbs",
            isDynamic: false,
            id: targetTemplateId,
          });

          itemRec
            .setValue({
              fieldId: "custrecord_ng_cs_stwb_show_table",
              value: eventId,
            })
            .setValue({
              fieldId: "custrecord_ng_cs_stwb_template",
              value: false,
            });

          const itemRecId = itemRec.save({
            ignoreMandatoryFields: true,
          });

          log.audit({
            title: "✅ Web Blurb Created Successfully",
            details: { itemRecId, targetTemplateId }
          });

          summary.success.push({
            id: itemRecId,
            templateId: targetTemplateId
          });

        } catch (err) {
          log.error({
            title: "❌ Error Processing Web Blurb Line",
            details: { line, error: err }
          });
          summary.errors.push({
            line,
            error: err.message,
            name: err.name
          });
        }
      }
    } catch (err) {
      log.error({
        title: "❌ Fatal Error in Web Blurbs Creation",
        details: err
      });
      summary.errors.push({
        error: err.message,
        name: err.name,
        isFatal: true
      });
    }

    log.audit({
      title: "🏁 Web Blurbs Creation Complete",
      details: summary
    });

    return summary;
  };

  /**
   * Creates additional info link items for the event to display on the website
   * @param {ServerRequest} req
   * @param {Number} eventId
   * @returns {void}
   * */
  const createAdditionalInfoItems = (req, eventId) => {
    const summary = {
      success: [],
      errors: []
    };

    log.audit({
      title: "🟢 Starting Additional Info Items Creation",
      details: { eventId }
    });

    try {
      const group = "custpage_adtnl_info";
      const additionalInfoCount = Number(req.getLineCount({ group }));

      log.debug({
        title: "📊 Additional Info Count",
        details: { count: additionalInfoCount }
      });

      if (additionalInfoCount === 0) {
        log.audit({
          title: "ℹ️ No Additional Info Items to Process",
          details: "Skipping additional info items creation"
        });
        return summary;
      }

      for (let line = 0; line < additionalInfoCount; line++) {
        try {
          const itemSelected = req.getSublistValue({
            group,
            name: "selected",
            line,
          });

          const targetTemplateId = req.getSublistValue({
            group,
            name: "custpage_template_id",
            line,
          });

          log.debug({
            title: "🔍 Processing Additional Info Line",
            details: { line, itemSelected, targetTemplateId }
          });

          const selected = Boolean(
            (typeof itemSelected === "boolean" && itemSelected) ||
            itemSelected === "T"
          );

          if (!selected) {
            log.debug({
              title: "⏩ Skipping Unselected Additional Info Item",
              details: { line, targetTemplateId }
            });
            continue;
          }

          const itemRec = record.copy({
            type: "customrecord_ng_cs_show_table_adtnl_info",
            isDynamic: false,
            id: targetTemplateId,
          });

          itemRec
            .setValue({
              fieldId: "custrecord_ng_cs_stai_show_table",
              value: eventId,
            })
            .setValue({
              fieldId: "custrecord_ng_cs_stai_template",
              value: false,
            });

          const itemRecId = itemRec.save({
            ignoreMandatoryFields: true,
          });

          log.audit({
            title: "✅ Additional Info Item Created Successfully",
            details: { itemRecId, targetTemplateId }
          });

          summary.success.push({
            id: itemRecId,
            templateId: targetTemplateId
          });

        } catch (err) {
          log.error({
            title: "❌ Error Processing Additional Info Line",
            details: { line, error: err }
          });
          summary.errors.push({
            line,
            error: err.message,
            name: err.name
          });
        }
      }
    } catch (err) {
      log.error({
        title: "❌ Fatal Error in Additional Info Items Creation",
        details: err
      });
      summary.errors.push({
        error: err.message,
        name: err.name,
        isFatal: true
      });
    }

    log.audit({
      title: "🏁 Additional Info Items Creation Complete",
      details: summary
    });

    return summary;
  };

  /**
   * Create the show date records from the time list for a specific event
   * @param {Number} eventRecId
   * @param {Object[]} timeList
   * @param {ServerRequest} req
   * @returns {Object}
   * */
  const createShowDates = (eventRecId, timeList, req) => {
    let summary = {
      failure: false,
      failureSubject: "",
      failureErrName: "",
      failureErrDesc: "",
      errorList: [],
      dateIds: [],
    };

    let CS_SETTINGS = settings.useSettings();

    try {
      let dateTypeSearch = getEventDateTypes();
      const laborDateTypes = parseMultiSelectQueryValues(
        CS_SETTINGS.custrecord_ng_cs_dflt_labor_date_types,
      );
      const sublistId = "custpage_event_dates";
      const dateTypeCount = req.getLineCount({ group: sublistId });

      log.audit({ title: "🔢 Timelist for eventDates:", details: timeList });

      for (let line = 0; line < dateTypeCount; line++) {
        const startTime = req.getSublistValue({
          group: sublistId,
          name: "custcol_starttime",
          line,
        });
        const endTime = req.getSublistValue({
          group: sublistId,
          name: "custcol_endtime",
          line,
        });
        log.audit({
          title: "🔎 Date start-end:",
          details: {
            startTime,
            endTime,
          },
        });

        const dateStartValue = timeList.find((time) => time.id === startTime);
        const dateEndValue = timeList.find((time) => time.id === endTime);

        log.audit({
          title: "✅ Date start-end:",
          details: {
            dateStartValue,
            dateEndValue,
          },
        });

        const dateTypeId = req.getSublistValue({
          group: sublistId,
          name: "custcol_type",
          line,
        });

        const dateTypeName = dateTypeSearch.find(
          (type) => type.id === dateTypeId,
        )?.name;

        const eventDate = req.getSublistValue({
          group: sublistId,
          name: "custcol_date",
          line,
        });

        const altShowDate = moment(eventDate).toDate();
        const initialStartDate = `${eventDate} ${dateStartValue.name}`;
        const initialEndDate = `${eventDate} ${dateEndValue.name}`;
        const altStart = moment(initialStartDate).toDate();
        const altEnd = moment(initialEndDate).toDate();
        const dateRec = record.create({ type: "customrecord_show_date" });

        dateRec
          .setValue({
            fieldId: "custrecord_show_number_date",
            value: eventRecId,
          })
          .setValue({
            fieldId: "custrecord_date_type",
            value: dateTypeId,
          })
          .setValue({
            fieldId: "custrecord_date",
            value: altShowDate,
          })
          .setValue({
            fieldId: "custrecord_start_time",
            value: altStart,
          })
          .setValue({
            fieldId: "custrecord_end_time",
            value: altEnd,
          });

        try {
          const dateRecId = dateRec.save({
            ignoreMandatoryFields: true,
          });

          if (laborDateTypes.includes(dateTypeId)) {
            log.audit({
              title: "🟢 Labor type found:",
              details: "Creating labor schedules...",
            });
            createLaborSchedules(eventRecId, altShowDate);
          }

          summary.dateIds.push(dateRecId);
        } catch (err) {
          summary.failure = true;
          const errorObject = {
            failureSubject: `Error encountered creating ${dateTypeName} date for record ${eventDate}`,
            failureErrName: err.name,
            failureErrDesc: err.message,
          };

          summary.errorList.push(errorObject);
        }
      }

      if (summary.failure) {
        log.error({
          title: "🔴 Error creating show dates:",
          details: summary.errorList,
        });

        return {
          failure: true,
          errorSummary: summary,
          errorList: summary.errorList,
        };
      }

      return {
        failure: false,
        ids: summary.dateIds,
        errorList: summary.errorList,
        errorSummary: summary,
      };
    } catch (err) {
      log.error({ title: "🔴 Error creating show dates:", details: err });
      summary.failure = true;
      summary.failureSubject = "Error encountered creating show dates";
      summary.failureErrName = err.name;
      summary.failureErrDesc = err.message;

      return {
        failure: true,
        errorList: summary.errorList,
        errorSummary: summary,
      };
    }
  };

  /**
   * Create the bookings records from the bookings list for a specific event
   * @param {ServerRequest} request
   * @param {Number} eventId
   * @returns {Object}
   * */
  const createBookings = (request, eventId) => {
    const summary = {
      bookingIds: [],
      errors: [],
    };

    try {
      const sublistId = "custpage_sublist_bookings";
      const lineCount = request.getLineCount({ group: sublistId });

      if (lineCount < 1) {
        return;
      }

      // fields to derive the booking name
      const booking_name_fields = {
        custrecord_ng_cs_eb_space_ui: "Space",
        custrecord_ng_cs_eb_status: "Status",
        custrecord_ng_cs_eb_start_date: "Start Date",
        custrecord_ng_cs_eb_start_time: "Time",
        custrecord_ng_cs_eb_end_date: "End Date",
        custrecord_ng_cs_eb_end_time: "Time",
        custrecord_ng_cs_eb_event: "Event",
      };

      // function to replace empty values in name
      const valueOr = (value, placeholder) => {
        const blank = [undefined, null, "", " "];
        return blank.includes(value) ? placeholder : value;
      };

      const sublistFields = [
        {
          sublistFieldId: "custcol_bk_space",
          fieldId: "custrecord_ng_cs_eb_space_ui",
          type: "id",
        },
        {
          sublistFieldId: "custcol_bk_start_date",
          fieldId: "custrecord_ng_cs_eb_start_date",
          type: "date",
        },
        {
          sublistFieldId: "custcol_bk_start_time",
          fieldId: "custrecord_ng_cs_eb_start_time",
          type: "time",
        },
        {
          sublistFieldId: "custcol_bk_end_date",
          fieldId: "custrecord_ng_cs_eb_end_date",
          type: "date",
        },
        {
          sublistFieldId: "custcol_bk_end_time",
          fieldId: "custrecord_ng_cs_eb_end_time",
          type: "time",
        },
        {
          sublistFieldId: "custcol_bk_booking_usage",
          fieldId: "custrecord_ng_cs_eb_book_status",
          type: "id",
        },
        {
          sublistFieldId: "custcol_bk_booking_rate",
          fieldId: "custrecord_ng_cs_eb_space_rate",
          type: "id",
        },
        {
          sublistFieldId: "custcol_bk_status",
          fieldId: "custrecord_ng_cs_eb_status",
          type: "id",
        },
        {
          sublistFieldId: "custcol_bk_description",
          fieldId: "custrecord_ng_cs_eb_description",
          type: "text",
        },
      ];

      for (let line = 0; line < lineCount; line++) {
        const booking = record.create({
          type: "customrecord_ng_cs_event_booking",
          isDynamic: true,
        });

        booking.setValue({
          fieldId: "custrecord_ng_cs_eb_event",
          value: eventId,
        });

        booking.setValue({
          fieldId: "custrecord_ng_cs_eb_venue",
          value: request.parameters.custpage_venue,
        });

        let lineValues = {};
        sublistFields.forEach(({ sublistFieldId, fieldId, type }) => {
          const rawValue = request.getSublistValue({
            group: "custpage_sublist_bookings",
            name: sublistFieldId,
            line,
          });

          if (rawValue) {
            const value =
              type === "date" ? moment(rawValue).toDate() : rawValue;
            lineValues[fieldId] = value;
            switch (type) {
              case "time":
                booking.setText({ fieldId, text: value });
                return;
              default:
                booking.setValue({ fieldId, value });
                return;
            }
          }
        });

        const [space, status, startDate, startTime, endDate, endTime, event] =
          Object.entries(booking_name_fields).map(([fieldId, defaultValue]) =>
            valueOr(booking.getText({ fieldId }), defaultValue),
          );

        booking.setValue({
          fieldId: "name",
          value: `${space} : ${status} : ${startDate} ${startTime} - ${endDate} ${endTime} : ${event}`,
        });

        try {
          const bookingId = booking.save({ ignoreMandatoryFields: true });
          summary.bookingIds.push(bookingId);
        } catch (err) {
          summary.errors.push({
            name: err.name,
            message: err.message,
            subject: `Error on line num: ${line}`,
            lineValues,
          });
        }
      }
    } catch (err) {
      summary.errors.push({
        name: err.name,
        message: err.message,
        subject: "Unexpected error occurred",
      });
    }

    if (summary.errors.length > 0) {
      log.error({
        title: "🔴 One or more errors encountered while creating bookings",
        details: summary,
      });
    } else {
      log.audit({
        title: "🟢 Successfully created all bookings",
        details: summary.bookingIds,
      });
    }
  };

  /**
   * Attaches surcharges to the event
   * @param {ServerRequest} request
   * @param {Number} eventId
   * @returns {Object}
   * */
  const createSurcharges = (request, eventId) => {
    const summary = {
      surchargeRecordsAttached: [],
      errors: [],
    };

    try {
      const sublistId = "custpage_sublist_surcharges";
      const lineCount = request.getLineCount({ group: sublistId });

      if (lineCount < 1) {
        return;
      }

      for (let line = 0; line < lineCount; line++) {
        const attach = request.getSublistValue({
          group: sublistId,
          name: "selected",
          line,
        });

        if (attach === "T") {
          const surchargeId = request.getSublistValue({
            group: sublistId,
            name: "custpage_surcharge_id",
            line,
          });

          // Load the Surcharge record
          const surchargeRecord = record.load({
            type: "customrecord_ng_cses_event_surcharge",
            id: surchargeId,
          });

          // Get the current list of events in the multiselect field
          const existingEvents =
            surchargeRecord.getValue({
              fieldId: "custrecord_ng_cses_surcharge_actv_evnts",
            }) || [];

          // Add the new event ID to the array if it's not already included
          if (!existingEvents.includes(eventId)) {
            existingEvents.push(eventId);
          }

          // Update the multiselect field with the new list
          surchargeRecord.setValue({
            fieldId: "custrecord_ng_cses_surcharge_actv_evnts",
            value: existingEvents,
          });

          // Save the updated Surcharge record
          surchargeRecord.save();
          summary.surchargeRecordsAttached.push(surchargeId);
        }
      }
    } catch (err) {
      summary.errors.push({
        name: err.name,
        message: err.message,
        subject: "Unexpected error occurred",
      });
    }

    if (summary.errors.length > 0) {
      log.error({
        title: "🔴 One or more errors encountered while attaching surcharges",
        details: summary,
      });
    } else {
      log.audit({
        title: "🟢 Successfully attached all surcharges",
        details: summary.bookingIds,
      });
    }
  };

  const createFreightTables = (eventRecId, rates, req) => {
    const group = "custpage_freightlist";
    let summary = {
      failure: false,
      failureSubject: "",
      failureErrName: "",
      failureErrDesc: "",
      errorList: [],
      freightIds: [],
    };

    const materialHandlingBetaEnabled =
      CS_SETTINGS.custrecord_enable_material_handling_beta === "T";

    try {
      if (!materialHandlingBetaEnabled) {
        log.audit({
          title: "🟢 Creating freight tables.",
          details: "Material handling beta is disabled.",
        });
        const freightCount = req.getLineCount({ group });
        if (freightCount !== 0) {
          for (let line = 0; line < freightCount; line++) {
            const itemSelected = req.getSublistValue({
              group,
              name: "selected",
              line,
            });
            const freightName = req.getSublistValue({
              group,
              name: "custpage_freight_name",
              line,
            });
            const freightRate = Number(
              req.getSublistValue({
                group,
                name: "custcol_baseprice",
                line,
              }),
            );

            if (
              (typeof itemSelected === "boolean" && itemSelected) ||
              itemSelected === "T"
            ) {
              const freightRec = record.create({
                type: "customrecord_freight_table",
                isDynamic: true,
              });

              const frieghtId = req.getSublistValue({
                group,
                name: "custcol_internalid",
                line,
              });

              freightRec
                .setValue({
                  fieldId: "custrecord_show_freight",
                  value: eventRecId,
                })
                .setValue({
                  fieldId: "custrecord_freight_item",
                  value: frieghtId,
                })
                .setValue({
                  fieldId: "custrecord_freight_rate",
                  value: freightRate,
                })
                .setValue({
                  fieldId: "custrecord_pre_show_rate",
                  value: rates.advance,
                })
                .setValue({
                  fieldId: "custrecord_inbetween_rate",
                  value: rates.inbetween,
                })
                .setValue({
                  fieldId: "custrecord_on_site_rate",
                  value: rates.onsite,
                });

              try {
                const freightRecId = freightRec.save({
                  ignoreMandatoryFields: true,
                });

                summary.freightIds.push(freightRecId);
              } catch (err) {
                summary.failure = true;
                const errorObject = {
                  failureSubject: `Error encountered creating freight table for record ${freightName}`,
                  failureErrName: err.name,
                  failureErrDesc: err.message,
                };

                summary.errorList.push(errorObject);
              }
            }
          }
        }

        if (summary.failure) {
          log.error({
            title: "🔴 Error creating freight tables:",
            details: summary.errorList,
          });

          return {
            ...summary,
            failure: true,
            errorSummary: summary,
          };
        }
      } else {
        log.audit({
          title: "🤫 Beta features for material handling enabled.",
          details: "Removing freight table creation.",
        });
      }

      return {
        ...summary,
        ids: summary.freightIds,
        errorList: summary.errorList,
        errorSummary: summary,
      };
    } catch (err) {
      log.error({
        title: "🔴 Error creating freight table records:",
        details: err,
      });
    }

    return summary;
  };

  /**
   * Get times of day from customrecord_time
   * @returns {Object[]} resultList
   * */
  const getShowTimes = () => {
    let resultList = [];
    let timeSearch = search.create({
      type: "customrecord_time",
      filters: [["isinactive", "is", "F"]],
      columns: [
        search.createColumn({ name: "name" }),
        search.createColumn({ name: "internalid", sort: "ASC" }),
      ],
    });

    getAllResultsFor(timeSearch, (result) => {
      let timeObj = {
        id: result.id,
        name: result.getValue({ name: "name" }),
      };

      resultList.push(timeObj);
    });

    return resultList;
  };

  /**
   * Get event date types from customrecord_ng_cs_date_types
   * @returns {Object[]|[]}
   * */
  const getEventDateTypes = () => {
    let resultList = [];
    let eventDateTypeSearch = search.create({
      type: "customrecord_ng_cs_date_types",
      filters: [["isinactive", "is", "F"]],
      columns: [
        search.createColumn({ name: "name" }),
        search.createColumn({ name: "custrecord_ng_dt_sequence", sort: "ASC" }),
      ],
    });

    getAllResultsFor(eventDateTypeSearch, (result) => {
      let eventDateTypeObj = {
        id: result.id,
        name: result.getValue({ name: "name" }),
        sequence: result.getValue({ name: "custrecord_ng_dt_sequence" }),
      };

      resultList.push(eventDateTypeObj);
    });

    log.audit({ title: "🟢 Event date types:", details: resultList });

    return resultList;
  };

  /**
   * Adds date sublists to the form including the date type, date, and time
   * @param {Form} form
   * @param {Object} timeList
   * @returns {Form}
   */
  const addDateSubLists = (form, timeList) => {
    let eventDateTypes = getEventDateTypes();

    try {
      if (eventDateTypes.length !== 0) {
        let dateSublist = form.addSublist({
          id: "custpage_event_dates",
          type: serverWidget.SublistType.INLINEEDITOR,
          label: "Event Dates",
          tab: "datesmain_tab",
        });

        dateSublist.addButton({
          id: "cust_btn_copy",
          label: "Copy Last Line",
          functionName: "CopyLastDateLine()",
        });

        let typeColumn = dateSublist.addField({
          id: "custcol_type",
          type: serverWidget.FieldType.SELECT,
          label: "Date Type",
        });
        typeColumn.isMandatory = true;
        typeColumn.addSelectOption({
          value: "",
          text: "",
          isSelected: true,
        });

        eventDateTypes.forEach((dateType) => {
          typeColumn.addSelectOption({
            text: dateType.name,
            value: dateType.id,
          });
        });

        dateSublist.addField({
          id: "custcol_date",
          type: serverWidget.FieldType.DATE,
          label: "Date",
        }).isMandatory = true;

        // Start Time
        let startTimeColumn = dateSublist.addField({
          id: "custcol_starttime",
          type: serverWidget.FieldType.SELECT,
          label: "Start Time",
        });
        startTimeColumn.isMandatory = true;

        startTimeColumn.addSelectOption({
          value: "",
          text: "",
          isSelected: true,
        });

        timeList.forEach((time) => {
          startTimeColumn.addSelectOption({
            text: time.name,
            value: time.id,
          });
        });
        // End Time
        let endTimeColumn = dateSublist.addField({
          id: "custcol_endtime",
          type: serverWidget.FieldType.SELECT,
          label: "End Time",
        });
        endTimeColumn.isMandatory = true;

        endTimeColumn.addSelectOption({
          value: "",
          text: "",
          isSelected: true,
        });

        timeList.forEach((time) => {
          endTimeColumn.addSelectOption({
            text: time.name,
            value: time.id,
          });
        });
      } else {
        log.audit({
          title: "🔴 No date types found to render date sublists:",
          details: eventDateTypes,
        });
        let dateSublist = form.addSublist({
          id: "custpage_dates_sublist",
          type: serverWidget.SublistType.LIST,
          label: "Event Dates",
          tab: "datesmain_tab",
        });

        dateSublist.addField({
          id: "custcol_type",
          type: serverWidget.FieldType.TEXT,
          label: "Type",
        });
        dateSublist.addField({
          id: "custcol_date",
          type: serverWidget.FieldType.TEXT,
          label: "Date",
        });
        dateSublist.addField({
          id: "custcol_starttime",
          type: serverWidget.FieldType.TEXT,
          label: "Start Time",
        });
        dateSublist.addField({
          id: "custcol_endtime",
          type: serverWidget.FieldType.TEXT,
          label: "End Time",
        });
        dateSublist.setSublistValue({
          id: "custcol_type",
          line: 0,
          value: "Date Types Not Available",
        });
      }

      return form;
    } catch (err) {
      log.error({ title: "Error Adding Date Sublist", details: err });
    }

    return form;
  };

  /**
   * Gets list of display forms from customrecord_display_forms
   * @returns {Object[]} resultList
   * */
  const getDisplayForms = () => {
    let resultList = [];
    let displayFormSearch = search.create({
      type: "customrecord_display_forms",
      filters: [["isinactive", "is", "F"]],
      columns: [
        search.createColumn({ name: "custrecord_form_name" }),
        search.createColumn({ name: "custrecord_form_group" }),
        search.createColumn({ name: "custrecord_form_active" }),
      ],
    });

    getAllResultsFor(displayFormSearch, (result) => {
      let displayFormObj = {
        id: result.id,
        name: result.getValue({ name: "custrecord_form_name" }),
        group: {
          id: result.getValue({ name: "custrecord_form_group" }),
          name: result.getText({ name: "custrecord_form_group" }),
        },
        active: result.getValue({ name: "custrecord_form_active" }),
      };

      let found = resultList.find((res) => res.id === displayFormObj.id);
      if (found) {
        // If the displayFormObj is found in the resultList, add it to the existing group
        found.group.push(displayFormObj);
      } else {
        // If the displayFormObj is not found in the resultList, create a new group
        const group = [displayFormObj];
        resultList.push({ id: displayFormObj.id, group });
      }
    });

    return resultList;
  };

  /**
   * Get all freight items from item records
   * @returns {Object[]} freightItems
   * */
  const getFreightItems = () => {
    let freightItems = [];
    let freightItemSearch = search.create({
      type: "item",
      filters: [
        ["isinactive", "is", "F"],
        "AND",
        [enums.item.Type.FREIGHT, "is", "T"],
      ],
      columns: [
        search.createColumn({ name: "itemid" }),
        search.createColumn({ name: "displayname" }),
        search.createColumn({ name: "salesdescription" }),
        search.createColumn({ name: "baseprice" }),
      ],
    });

    getAllResultsFor(freightItemSearch, (result) => {
      let freightItemObj = {
        selected: "T", // Default to selected
        id: result.id,
        name: result.getValue({ name: "itemid" }),
        displayName: result.getValue({ name: "displayname" }),
        description: result.getValue({ name: "salesdescription" }),
        price: result.getValue({ name: "baseprice" }),
      };

      freightItems.push(freightItemObj);
    });

    return freightItems;
  };

  /**
   * Get Templated Web Blurbs
   * @returns {ResultSet[]} webBlurbs
   * */
  const getWebBlurbTemplates = () => {
    let webBlurbs = [];
    const webBlurbSearch = search.create({
      type: "customrecord_ng_cs_show_table_web_blurbs",
      filters: [
        ["isinactive", "is", "F"],
        "AND",
        ["custrecord_ng_cs_stwb_template", "is", "T"],
      ],
      columns: [
        search.createColumn({ name: "custrecord_ng_cs_stwb_title" }),
        search.createColumn({ name: "custrecord_rcs_blurb_subtitle" }),
        search.createColumn({ name: "custrecord_ng_cs_stwb_display_title" }),
        search.createColumn({ name: "custrecord_ng_cs_stwb_display_order" }),
        search.createColumn({ name: "custrecord_ng_cs_stwb_web_blurb" }),
        search.createColumn({ name: "custrecord_automated_content_enabled" }),
      ],
    });

    getAllResultsFor(webBlurbSearch, (result) => {
      webBlurbs.push(result);
    });

    return webBlurbs;
  };

  /**
   * Grab additional info links from customrecord_ng_cs_show_table_adtnl_info
   * @returns {ResultSet[]} additionalInfoLinks
   * */
  const getAdditionalInfoLinkTemplates = () => {
    let additionalInfoLinks = [];
    let additionalInfoLinkSearch = search.create({
      type: "customrecord_ng_cs_show_table_adtnl_info",
      filters: [
        ["isinactive", "is", "F"],
        "AND",
        ["custrecord_ng_cs_stai_template", "is", "T"],
      ],
      columns: [
        search.createColumn({ name: "custrecord_ng_cs_stai_link_text" }),
        search.createColumn({ name: "custrecord_ng_cs_stai_link_url" }),
        search.createColumn({ name: "custrecord_ng_cs_stai_file" }),
        search.createColumn({
          name: "custrecord_ng_cs_stai_link_display_order",
        }),
      ],
    });

    getAllResultsFor(additionalInfoLinkSearch, (result) => {
      additionalInfoLinks.push(result);
    });

    // Run display order sort
    additionalInfoLinks = additionalInfoLinks.sort(
      (a, b) =>
        Number(
          a.getValue({
            name: "custrecord_ng_cs_stai_link_display_order",
          }),
        ) -
        Number(
          b.getValue({
            name: "custrecord_ng_cs_stai_link_display_order",
          }),
        ),
    );

    return additionalInfoLinks;
  };

  /**
   * Get collection templates with hierarchy
   * @returns {ResultSet[]} collectionTemplates
   * */
  const getCollectionTemplates = (form) => {
    let collectionTemplates = [];
    let hierarchyMap = {};
    
    // First get all templates
    let collectionTemplateSearch = search.create({
      type: "customrecord_ng_cs_item_collection",
      filters: [
        ["isinactive", "is", "F"],
        "AND",
        ["custrecord_ng_cs_itemcoll_template", "is", "T"],
      ],
      columns: [
        search.createColumn({ name: "custrecord_ng_cs_itemcoll_event" }),
        search.createColumn({ name: "custrecord_ng_cs_itemcoll_show_in_web" }),
        search.createColumn({ name: "custrecord_ng_cs_itemcoll_image" }),
        search.createColumn({ name: "custrecord_ng_cs_itemcoll_items" }),
        search.createColumn({ name: "custrecord_ng_cs_itemcoll_display_name" }),
        search.createColumn({ name: "parent" }),
        search.createColumn({ name: "internalid" }),
      ],
    });

    // First pass: Build hierarchy map
    getAllResultsFor(collectionTemplateSearch, (result) => {
      const id = result.id
      const parentId = result.getValue({ name: "parent" });
      const displayName = result.getText({ name: "custrecord_ng_cs_itemcoll_display_name" });
      
      hierarchyMap[id] = {
        result,
        parent: parentId,
        children: [],
        level: 0,
        displayName
      };
    });

    // Second pass: Calculate levels and populate children
    Object.keys(hierarchyMap).forEach((id) => {
      const entry = hierarchyMap[id];
      if (entry.parent && hierarchyMap[entry.parent]) {
        hierarchyMap[entry.parent].children.push(id);
        let level = 1;
        let parent = hierarchyMap[entry.parent];
        while (parent.parent && hierarchyMap[parent.parent]) {
          level++;
          parent = hierarchyMap[parent.parent];
        }
        entry.level = level;
      }
    });

    // Third pass: Format display names and create final array
    Object.keys(hierarchyMap).forEach((id) => {
      const entry = hierarchyMap[id];
      const prefix = entry.level > 0 ? "└".repeat(entry.level) + "─ " : "";
      
      // Update the display name in the result object
      const formattedResult = entry.result;
      formattedResult.custrecord_ng_cs_itemcoll_display_name = prefix + entry.displayName;
      
      collectionTemplates.push(formattedResult);
    });

    // Store hierarchy map in cache for client-side use
    const hierarchyData = {};
    Object.keys(hierarchyMap).forEach((id) => {
      hierarchyData[id] = {
        parent: hierarchyMap[id].parent,
        children: hierarchyMap[id].children,
        level: hierarchyMap[id].level
      };
    });

    log.debug({ title: "🟢 Collection Hierarchy Data:", details: hierarchyData });

    // Add hierarchy data as a hidden field to the form
    form.addField({
      id: 'custpage_collection_hierarchy',
      type: serverWidget.FieldType.LONGTEXT,
      label: 'Collection Hierarchy Data'
    }).defaultValue = JSON.stringify(hierarchyData);
    form.getField('custpage_collection_hierarchy').updateDisplayType({
      displayType: serverWidget.FieldDisplayType.HIDDEN
    });

    return collectionTemplates;
  };

  /**
   * Populate a sublist with data and format values with column mapping options
   * @param {Object} props
   * @param {Sublist} props.sublist - Sublist to populate
   * @param {Result[]} props.data - Data provided by a search
   * @param {Object} props.options - Options for formatting the sublist
   * @param {boolean} props.options.addMarkAllButtons - Adds mark all buttons to the sublist
   * @param {boolean} props.options.checkboxes - Adds checkboxes to the sublist
   * @param {Object} props.options.columnMapping - Maps the sublist columns to the data with options on output
   * @param {string} props.options.columnMapping.id - The id of the field to add to the sublist
   * @param {string} props.options.columnMapping.label - The label of the field to add to the sublist
   * @param {string} props.options.columnMapping.type - The type of field to add to the sublist
   * @param {boolean} props.options.columnMapping.getMulti - Gets multiple values from a field and joins them with a newline
   * @param {boolean} props.options.columnMapping.isBool - Checks if the value is a boolean and outputs Yes or No
   * @param {boolean} props.options.columnMapping.getText - Gets the text value of a field
   * @param {boolean} props.options.columnMapping.image - Checks if the value is an image and outputs the image
   * @param {boolean} props.options.columnMapping.isHTML - Checks if the value is HTML and outputs the HTML
   * @param {boolean} props.options.columnMapping.previewUrl - Adds a preview link to the sublist
   * @returns {void}
   * */
  const populateSublist = ({ sublist, data, options }) => {
    options?.addMarkAllButtons && sublist.addMarkAllButtons();

    if (options?.checkboxes) {
      sublist.addField({
        id: "selected",
        type: serverWidget.FieldType.CHECKBOX,
        label: "Selected",
      });
    }

    let templateIdField = sublist
      .addField({
        id: "custpage_template_id",
        type: serverWidget.FieldType.TEXT,
        label: "template id",
      })
      .updateDisplayType({ displayType: serverWidget.FieldDisplayType.HIDDEN });

    let actionsField = null;

    if (options?.actions) {
      actionsField = sublist.addField({
        id: "custcol_collection_actions",
        type: serverWidget.FieldType.TEXT,
        label: "Actions",
      });        
    }

    log.debug({ title: "🟢 Data Result:", details: data });

    let sublistField = null;

    if (options?.columnMapping) {
      if (data && data.length !== 0) {
        let lineNumber = 0;
        let previewUrl = url.resolveScript({
          scriptId: "customscript_ng_cs_sl_evt_blrb_prv",
          deploymentId: "customdeploy_ng_cs_sl_evt_blrb_prv_dep",
          returnExternalUrl: false,
          params: {},
        });

        let cols = Object.keys(options.columnMapping);

        // Add fields to sublist
        cols.forEach(function (col) {
          sublistField = null;
          let map = options.columnMapping[col];

          if (map.field === 'internalid') {
            sublistField = sublist
              .addField({ id: col, type: map.type, label: map.label })
              .updateDisplayType({ displayType: serverWidget.FieldDisplayType.HIDDEN });
            return;
          }

          sublistField = sublist.addField({ id: col, type: map.type, label: map.label });
          
          if (map.isHidden) {
            sublistField.updateDisplayType({ displayType: serverWidget.FieldDisplayType.HIDDEN });
          }
        });

        // Populate data
        data.forEach((item) => {
          // Set default selection - select all except child collections
          if (options?.checkboxes) {
            const hasParent = item.getValue({ name: "parent" });
            sublist.setSublistValue({
              id: "selected",
              line: lineNumber,
              value: hasParent ? "F" : "T"
            });
          }

          for (let key in options.columnMapping) {
            let columnDef = null;
            let value = options.columnMapping[key];

            if (value?.isHTML && value?.formatter) {
              // Use formatter function if provided
              columnDef = value.formatter(
                item.getValue({ name: value.field }),
                item
              );
            } else if (value?.getMulti) {
              columnDef = item.getText({ name: value.field }).split(",").join("\n");
            } else if (value?.isBool) {
              columnDef = item.getValue({ name: value.field }) === "T" ? "Yes" : "No";
            } else if (value?.getText) {
              let colText = item.getText({ name: value.field });
              if (value?.image) {
                columnDef = `<div class="card-image-container"><sl-card class="card-image"><img slot="image" alt="Collection Image" src="${colText}"/></sl-card></div>`;
              } else {
                columnDef = colText;
              }
            } else if (value?.isHTML) {
              columnDef = `<div style="width: 600px; border: 0 none;">${item.getValue({ name: value.field })}</div>`;
            } else if (value?.previewUrl) {
              let previewLink = `${previewUrl}&tid=${item.id}`;
              columnDef = `<sl-button size="small" variant="primary" onclick="openPreview('${previewLink}')">Preview <sl-icon slot="suffix" name="box-arrow-up-right"></sl-icon></sl-button>`;
            } else {
              let characterLimit = value.type === serverWidget.FieldType.TEXTAREA ? 4000 : 300;
              let columnDefValue = item.getValue({ name: value.field });
              columnDef = columnDefValue ? columnDefValue.slice(0, characterLimit) : "";
            }

            columnDef && sublist.setSublistValue({
              id: key,
              line: lineNumber,
              value: columnDef,
            });
          }

          // ... rest of the existing code for actions field and template ID
          if (actionsField) {
            let collectionId = item.getValue({ name: "internalid" });

            let viewUrl = url.resolveRecord({
              recordType: "customrecord_ng_cs_item_collection",
              recordId: collectionId,
              isEditMode: false,
            });
    
            let editUrl = url.resolveRecord({
              recordType: "customrecord_ng_cs_item_collection",
              recordId: collectionId,
              isEditMode: true,
            });
    
            actionsField.defaultValue = `<div style="display: flex; justify-content: center; align-items: center; padding: 10px;">
              <sl-button-group>
                <sl-button size="small" pill onclick="window.open('${viewUrl}', '_blank')">View</sl-button>
                <sl-button size="small" pill onclick="window.open('${editUrl}', '_blank')">Edit</sl-button>
              </sl-button-group>
            </div>`;
          }

          sublist.setSublistValue({
            id: "custpage_template_id",
            value: item.id,
            line: lineNumber,
          });

          lineNumber++;
        });
      }
    }
  };

  /* ===============================
   *       Utility Functions
   * ==============================**/

  /**
   * Callback function to run on each result
   * @param {Search} searchObj - Search object
   * @param {(Result) => void} callback - Callback function to run on each result
   * @returns {void}
   * */
  function getAllResultsFor(searchObj, callback) {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  }

  const getStateShortName = (stateId) =>
    query
      .runSuiteQL(`SELECT shortname FROM state WHERE id = '${stateId}'`)
      .asMappedResults()[0].shortname;

  const getCountryShortName = (countryId) =>
    query
      .runSuiteQL(`SELECT id FROM COUNTRY WHERE uniquekey = '${countryId}'`)
      .asMappedResults()[0].id;

  const replacePlaceholders = (string = "", ...data) => {
    const dataMap = new Map(
      data.map((value, index) => [index.toString(), value]),
    );
    const regex = /{(\d+)}/g;
    return string.replace(regex, (match, index) => {
      if (dataMap.has(index)) {
        return dataMap.get(index);
      }
      return match;
    });
  };

  /**
   * Match the string to the closest string in an array
   * @param {string} str - String to match
   * @param {string[]} arr - Array of strings to match against
   * @returns {string} - Closest string match
   * */
  const matchesStringClosest = (str, arr) => {
    function findClosestString(array, staticString) {
      let closestMatch = "";
      let closestDistance = Infinity;

      for (let i = 0; i < array.length; i++) {
        const currentDistance = levenshteinDistance(array[i], staticString);
        if (currentDistance < closestDistance) {
          closestMatch = array[i];
          closestDistance = currentDistance;
        }
      }

      return closestMatch;
    }

    // Function to calculate the Levenshtein distance between two strings
    function levenshteinDistance(str1, str2) {
      const m = str1.length;
      const n = str2.length;

      if (m === 0) return n;
      if (n === 0) return m;

      const dp = Array.from(Array(m + 1), () => Array(n + 1).fill(0));

      for (let i = 0; i <= m; i++) {
        dp[i][0] = i;
      }

      for (let j = 0; j <= n; j++) {
        dp[0][j] = j;
      }

      for (let i = 1; i <= m; i++) {
        for (let j = 1; j <= n; j++) {
          if (str1[i - 1] === str2[j - 1]) {
            dp[i][j] = dp[i - 1][j - 1];
          } else {
            dp[i][j] =
              Math.min(dp[i - 1][j], dp[i][j - 1], dp[i - 1][j - 1]) + 1;
          }
        }
      }

      return dp[m][n];
    }

    return findClosestString(arr, str);
  };

  /* ===============================
   * Column Mappings
   * ============================= */

  const additionalInfoMap = {
    custcol_link_text: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_stai_link_text",
      label: "Link Text",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
    custcol_link_url: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_stai_link_url",
      label: "Link URL",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
    custcol_file: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_stai_file",
      label: "File",
      getText: true,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
    custcol_link_display_order: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_stai_link_display_order",
      label: "Display Order",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
  };

  const webBlurbMap = {
    custcol_blurb_id: {
      type: serverWidget.FieldType.TEXT,
      field: "internalid",
      label: "Blurb ID",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
    custcol_auto_blurb: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_automated_content_enabled",
      label: "Auto Blurb",
      getText: false,
      getMulti: false,
      isBool: true,
      isImage: false,
      isHTML: false,
      previewUrl: false,
    },
    custcol_blurb_disp_title: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_stwb_title",
      label: "Display Title",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
    custcol_blurb_subtitle: {
      type: serverWidget.FieldType.TEXTAREA,
      field: "custrecord_rcs_blurb_subtitle",
      label: "Display Subtitle",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
    custcol_blurb: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_stwb_web_blurb",
      label: "Blurb",
      getText: false,
      getMulti: false,
      isBool: false,
      isImage: false,
      isHTML: false,
      previewUrl: true,
    },
    
    custcol_blurb_media: {
      type: serverWidget.FieldType.URL,
      field: "custrecord_cs_web_blurb_media",
      label: "Media",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
  };

  const itemCollectionMap = {
    custcol_collection_id: {
      type: serverWidget.FieldType.TEXT,
      field: "internalid",
      label: "Collection ID",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
    custcol_parent_id: {
      type: serverWidget.FieldType.TEXT,
      field: "parent",
      label: "Parent Collection",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
      isHidden: true
    },
    custcol_display_name: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_itemcoll_display_name",
      label: "Display Name",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
      isHidden: true
    },
    custcol_formatted_display: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_itemcoll_display_name",
      label: "Collection Name",
      getText: false,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: true,
      previewUrl: false,
      formatter: (value, item) => {
        const level = item.getValue({ name: "parent" }) ? 1 : 0;
        const prefix = level > 0 ? "└─".repeat(level) + " " : "";
        return `<div style="font-family: monospace; padding: 4px;">
                  <span style="color: ${level > 0 ? '#666' : '#000'}; margin-left: ${level * 20}px;">
                    ${prefix}${value}
                  </span>
                </div>`;
      }
    },
    custcol_ic_original_event: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_itemcoll_event",
      label: "Original Event",
      getText: true,
      getMulti: false,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
    custcol_ic_show_in_web: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_itemcoll_show_in_web",
      label: "Display In Web",
      getText: false,
      getMulti: false,
      isBool: true,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
    custcol_ic_image: {
      type: serverWidget.FieldType.TEXT,
      field: "custrecord_ng_cs_itemcoll_image",
      label: "Image",
      getText: true,
      getMulti: false,
      isBool: false,
      image: true,
      isHTML: false,
      previewUrl: false,
    },
    custcol_ic_items: {
      type: serverWidget.FieldType.TEXTAREA,
      field: "custrecord_ng_cs_itemcoll_items",
      label: "Items",
      getText: false,
      getMulti: true,
      isBool: false,
      image: false,
      isHTML: false,
      previewUrl: false,
    },
  };

  /**
   * A utility function to check if there are any existing labor schedules for a given event.
   *
   * @param {Object} eventData - An object containing the necessary data to check for existing labor schedules.
   * @param date
   * @returns {boolean} - A boolean value indicating whether or not there are existing labor schedules for the given event.
   */
  const checkEventSchedules = (eventData, date) => {
    let eventChecked = false;
    let scheduleFilters = [];

    log.debug({
      title: "📝 Checking labor schedules:",
      details: { eventData, date },
    });

    if (
      eventData?.id &&
      date &&
      eventData?.startTime &&
      eventData?.endTime &&
      eventData?.laborType &&
      eventData?.rateMultiplier &&
      eventData?.supervisionMarkup
    ) {
      scheduleFilters = [
        ["custrecord_ng_cs_labor_show", "is", eventData.id],
        "and",
        ["custrecord_ng_cs_labor_date", "on", date],
        "and",
        ["custrecord_ng_cs_labor_start", "is", eventData.startTime],
        "and",
        ["custrecord_ng_cs_labor_end", "is", eventData.endTime],
        "and",
        ["custrecord_ng_cs_labor_type", "anyof", [eventData.laborType]],
        "and",
        ["custrecord_ng_cs_labor_multiplier", "is", eventData.rateMultiplier],
        "and",
        [
          "custrecord_ng_cs_supervisor_markup",
          "is",
          eventData.supervisionMarkup,
        ],
      ];
    } else {
      scheduleFilters = [
        search.createFilter({
          name: "custrecord_ng_cs_labor_show",
          operator: search.Operator.IS,
          values: eventData.id,
        }),
        search.createFilter({
          name: "custrecord_ng_cs_labor_date",
          operator: search.Operator.ON,
          values: date,
        }),
      ];
    }

    const scheduleSearch = search.create({
      type: "customrecord_ng_cs_show_labor_schedule",
      filters: scheduleFilters,
      columns: [],
    });

    try {
      const resultsCount = scheduleSearch.runPaged().count;
      eventChecked = resultsCount !== 0;

      log.audit({
        title: "📘 Count of schedule items on event:",
        details: resultsCount,
      });
    } catch (error) {
      log.error({
        title: "🔴 Error checking event schedules",
        details: error,
      });
    }

    return eventChecked;
  };

  /**
   * This function is used to create labor schedules.
   * It parses the event date and creates labor schedules based on the parsed date and event details.
   * If a labor schedule matching the current day of the week already exists for the given event, it logs an appropriate audit message.
   * If a labor schedule does not exist, it proceeds to create a new one.
   * It also catches and logs any errors that may occur during the labor schedule creation.
   *
   * @param {string|number} eventId - The id of the event.
   * @param {Date} eventDate - The date of the event.
   */
  const createLaborSchedules = (eventId, eventDate) => {
    log.audit({
      title: "📝 Creating Labor Schedules...",
      details: {
        eventId,
        eventDate,
      },
    });
    const COMPANY_PREFERENCES = config.load({
      type: config.Type.COMPANY_PREFERENCES,
    });
    const dateformat = COMPANY_PREFERENCES.getValue("DATEFORMAT");
    const eventDateParsed = moment(eventDate, dateformat);
    const eventDay = eventDateParsed.date();
    const eventDayOfWeek = String(eventDateParsed.day() + 1); // 1 = Sunday, 7 = Saturday
    const eventDateFormatted = eventDateParsed.format(dateformat);

    log.debug({
      title: "📬 Labor date formatted:",
      details: {
        eventDate,
        eventDateParsed,
        eventDay,
        eventDayOfWeek,
        eventDateFormatted,
      },
    });

    const masterScheduleResults = [];
    const laborMasterColumns = [
      search.createColumn({ name: "custrecord_ng_cs_day_of_week" }),
      search.createColumn({ name: "custrecord_ng_cs_master_start_time" }),
      search.createColumn({ name: "custrecord_ng_cs_master_end_time" }),
      search.createColumn({ name: "custrecord_ng_cs_master_labor_type" }),
      search.createColumn({ name: "custrecord_ng_cs_master_rate_multiplier" }),
      search.createColumn({ name: "custrecord_ng_cs_master_super_markup" }),
    ];
    const laborMasterFilters = [
      ["custrecord_ng_cs_day_of_week", "anyof", eventDayOfWeek],
      "and",
      ["isinactive", "is", "F"],
    ];

    const laborMasterSearch = search.create({
      type: "customrecord_ng_cs_show_mstr_labor_schd",
      columns: laborMasterColumns,
      filters: laborMasterFilters,
    });

    const laborMasterCount = laborMasterSearch.runPaged().count;

    log.debug({
      title: "🟡 Searching for master schedule:",
      details: laborMasterCount,
    });

    getAllResultsFor(laborMasterSearch, (result) => {
      masterScheduleResults.push({
        dayOfWeek: parseMultiSelectResult(
          result,
          "custrecord_ng_cs_day_of_week",
        ),
        startTime: result.getValue("custrecord_ng_cs_master_start_time"),
        endTime: result.getValue("custrecord_ng_cs_master_end_time"),
        laborType: result.getValue("custrecord_ng_cs_master_labor_type"),
        rateMultiplier: result.getValue(
          "custrecord_ng_cs_master_rate_multiplier",
        ),
        supervisionMarkup: result.getValue(
          "custrecord_ng_cs_master_super_markup",
        ),
      });
    });

    if (masterScheduleResults.length !== 0) {
      log.audit({
        title:
          "➰ Looping through master schedule to create new dates for event",
        details: masterScheduleResults,
      });

      masterScheduleResults.forEach((schedule) => {
        // Check if the labor schedule already exists for the current date
        if (schedule.dayOfWeek.find((day) => day.id === eventDayOfWeek)) {
          // Check the event existing labor schedules for the current date
          const eventSchedulesExist = checkEventSchedules(
            { ...schedule, id: eventId },
            eventDateFormatted,
          );

          if (!eventSchedulesExist) {
            // Create the labor schedule
            const laborSchedule = record.create({
              type: "customrecord_ng_cs_show_labor_schedule",
              isDynamic: true,
            });

            const times = {
              startTime: moment(schedule.startTime, "hh:mm a").toDate(),
              endTime: moment(schedule.endTime, "hh:mm a").toDate(),
            };

            log.audit({
              title: "📝 Schedule found setting values:",
              details: schedule,
            });

            const supervisionMarkup = Number.isNaN(
              parseFloat(schedule.supervisionMarkup),
            )
              ? 0
              : parseFloat(schedule.supervisionMarkup);

            laborSchedule.setValue({
              fieldId: "custrecord_ng_cs_labor_show",
              value: eventId,
            });
            laborSchedule.setValue({
              fieldId: "custrecord_ng_cs_labor_date",
              value: eventDate,
            });
            laborSchedule.setValue({
              fieldId: "custrecord_ng_cs_labor_start",
              value: times.startTime,
            });
            laborSchedule.setValue({
              fieldId: "custrecord_ng_cs_labor_end",
              value: times.endTime,
            });
            laborSchedule.setValue({
              fieldId: "custrecord_ng_cs_labor_type",
              value: schedule.laborType,
            });
            laborSchedule.setValue({
              fieldId: "custrecord_ng_cs_labor_multiplier",
              value: schedule.rateMultiplier,
            });
            laborSchedule.setValue({
              fieldId: "custrecord_ng_cs_supervisor_markup",
              value: supervisionMarkup,
            });

            try {
              laborSchedule.save();
            } catch (error) {
              log.error({
                title: "🔴 Error creating labor schedule",
                details: error,
              });
            }
          } else {
            log.audit({
              title: `❗ A schedule already exists for event: `,
              details: eventId,
            });
          }
        } else {
          log.debug({
            title:
              "Master schedule does not include event date scheduled day of week:",
            details: eventDayOfWeek,
          });
        }
      });
    } else {
      log.audit({ title: "🙅 No master labor schedules found", details: "" });
    }
  };

  const getChildCollections = (collectionId, level = 1) => {
    let children = [];

    let collectionSearch = search.create({
      type: "customrecord_ng_cs_item_collection",
      filters: [
        ["isinactive", "is", "F"],
        "AND",
        ["parent", "anyof", [collectionId]],
      ],
      columns: [
        search.createColumn({ name: "custrecord_ng_cs_itemcoll_display_name" }),
        search.createColumn({ name: "parent" }),
      ],
    });

    const countOfCollections = collectionSearch.runPaged().count;

    log.debug({
      title: "➡️ Children count:",
      details: { countOfCollections, level },
    });

    getAllResultsFor(collectionSearch, (result) => {
      let childCollection = {
        id: result.id,
        name: result.getValue({
          name: "custrecord_ng_cs_itemcoll_display_name",
        }),
        parent: result.getValue({ name: "parent" }),
        children: getChildCollections(result.id, level + 1),
      };

      children.push(childCollection);
    });

    return children;
  };

  const createChildCollections = (collections, parentId, eventId) => {
    let createdCollections = [];

    if (collections.length === 0) {
      return createdCollections;
    }

    if (collections.length !== 0) {
      let updatingCollectionId = parentId;

      collections.forEach((collection) => {
        let newCollection = record.copy({
          type: "customrecord_ng_cs_item_collection",
          isDynamic: true,
          id: collection.id,
        });

        newCollection.setValue({
          fieldId: "custrecord_ng_cs_itemcoll_event",
          value: eventId,
        });

        newCollection
          .setValue({
            fieldId: "parent",
            value: parentId,
          })
          .setValue({
            fieldId: "custrecord_ng_cs_itemcoll_template",
            value: false,
          });

        try {
          let newCollectionId = newCollection.save({
            ignoreMandatoryFields: true,
          });

          createdCollections.push({
            parentId,
            newCollectionId,
          });

          updatingCollectionId = newCollectionId;
        } catch (error) {
          log.error({
            title: "🔴 Error creating child collection",
            details: error,
          });
        }

        if (collection.children.length !== 0) {
          let childCollections = createChildCollections(
            collection.children,
            updatingCollectionId,
            eventId,
          );

          createdCollections.push(...childCollections);
        }
      });
    }

    return createdCollections;
  };

  /**
   * A utility function to fetch all results from a search object in a paged manner.
   *
   * @param {Object} searchObj - The search object to fetch results from.
   * @param {Function} callback - A function to be called for each result fetched.
   *
   * @returns {undefined} - This function does not return a value.
   */
  function getAllResultsFor(searchObj, callback) {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  }

  /**
   * Parses a comma-separated string of values into an array of trimmed strings.
   *
   * @param {string} valuesString - A string containing comma-separated values.
   * @returns {Array<string>} - An array of trimmed strings representing the parsed values.
   */
  function parseMultiSelectQueryValues(valuesString) {
    if (!valuesString) {
      return [];
    }

    // If there is only one value, return it as a single-element array
    if (valuesString.indexOf(",") === -1) {
      return [valuesString.trim()];
    }

    // Split the string into individual values
    var valuesArray = valuesString.split(",");

    // Trim any leading or trailing spaces from each value
    valuesArray = valuesArray.map(function (value) {
      return value.trim();
    });

    return valuesArray;
  }

  /**
   * Parse mulitselect field results from a search.Result object
   * @param {search.Result} result - The search result object
   * @param {string} fieldName - The name of the field to parse
   * @returns {Array} - An array of objects with id and name properties
   * */
  function parseMultiSelectResult(result, searchColName) {
    let resultArray = result.getValue(String(searchColName)).split(","); // Split the string using comma as the delimiter
    let parsedArray = [];

    let getText = result.getText; // Cache the function

    let textArray = getText(String(searchColName)).split(",");
    parsedArray = resultArray.map(function (item, i) {
      return {
        id: item,
        name: textArray[i],
      };
    });

    return parsedArray;
  }

  return { onRequest };
});
