/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 */
define(['N/https', 'N/record', 'N/runtime', 'N/search', 'N/url','../lib/newgen.library.v21'],
    
    (https, record, runtime, search, url, NG) => {
        /**
         * Defines the Suitelet script trigger point.
         * @param {Object} scriptContext
         * @param {ServerRequest} scriptContext.request - Incoming request
         * @param {ServerResponse} scriptContext.response - Suitelet response
         * @since 2015.2
         */
        const onRequest = (scriptContext) => {

            try{

                if(scriptContext.request.method == 'POST' || scriptContext.request.method == 'GET'){

                    log.debug({title: 'Start Post Function'});

                    log.debug({title: 'Request Data', details: JSON.stringify(scriptContext.request)});

                    let params = scriptContext.request.parameters;

                    log.debug({title: 'Params', details: JSON.stringify(params)});

                    let eventRecalcProcessData = params.eventRecalcProcessData;

                    log.debug({title: 'Event Recalc Process Data', details: eventRecalcProcessData});

                    // if(NG.tools.isEmpty(eventRecalcProcessData)){
                    //
                    //     eventRecalcProcessData = JSON.stringify({"timesCalled":0,"eventID":"109","eventVenueID":"105","eventVenueCurrencyID":1,"eventCustomerIsTaxable":true,"eventTaxGroupID":"-706","eventTaxState":"IL","eventTaxNexusCountry":"US","taxRate":10.25,"taxRate2":"","itemServiceTiersData":[{"id":"1","name":"Electrical Services"},{"id":"2","name":"Equipment Rentals"},{"id":"3","name":"Equipment Subrentals"},{"id":"4","name":"Internet Services"},{"id":"5","name":"Operator Labor"},{"id":"6","name":"Rigging Equipment"},{"id":"8","name":"Service Charge"},{"id":"7","name":"Telephony Services"}],"eventItemDiscounts":[{"internalid":[{"value":"3","text":"3"}],"name":"ALT 30% Equipment Subrentals Discount - Rosemont","custrecord_ng_cs_event_discount_item":[{"value":"1083","text":"30% Discount - Equipment Subrentals"}],"custrecord_ng_cs_event_discount_tiers":[{"value":"2","text":"Equipment Rentals"},{"value":"3","text":"Equipment Subrentals"}],"custrecord_ng_cs_event_discount_venues":[{"value":"105","text":"Rosemont Convention Center"}],"CUSTRECORD_NG_CS_EVENT_DISCOUNT_ITEM.baseprice":"-30.00%","custrecord_ng_discount_rate":"30.0%"},{"internalid":[{"value":"4","text":"4"}],"name":"ALT Equipment Subrental Discount","custrecord_ng_cs_event_discount_item":[{"value":"1082","text":"Equipment Subrental Discount"}],"custrecord_ng_cs_event_discount_tiers":[{"value":"3","text":"Equipment Subrentals"}],"custrecord_ng_cs_event_discount_venues":[{"value":"105","text":"Rosemont Convention Center"}],"CUSTRECORD_NG_CS_EVENT_DISCOUNT_ITEM.baseprice":"-20.00%","custrecord_ng_discount_rate":"20.0%"}],"venueServiceTiersData":[{"id":"4","custrecord_ng_venue_item_svc_tier":[{"value":"2","text":"Equipment Rentals"}],"custrecord_ng_venue_pt_price_level":[{"value":"1","text":"Standard Price"}],"custrecord_ng_venue_pt_commission_rate":"33.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_disc_threshold":"40.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_comm_discount":"50.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_service_fee":"15.0%","custrecord_ng_venue_pt_srv_fee_tier":false},{"id":"5","custrecord_ng_venue_item_svc_tier":[{"value":"5","text":"Operator Labor"}],"custrecord_ng_venue_pt_price_level":[{"value":"1","text":"Standard Price"}],"custrecord_ng_venue_pt_commission_rate":"55.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_disc_threshold":"40.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_comm_discount":"50.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_service_fee":"15.0%","custrecord_ng_venue_pt_srv_fee_tier":false},{"id":"6","custrecord_ng_venue_item_svc_tier":[{"value":"7","text":"Telephony Services"}],"custrecord_ng_venue_pt_price_level":[{"value":"2","text":"Advanced Price"}],"custrecord_ng_venue_pt_commission_rate":"22.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_disc_threshold":"40.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_comm_discount":"50.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_service_fee":"15.0%","custrecord_ng_venue_pt_srv_fee_tier":false},{"id":"7","custrecord_ng_venue_item_svc_tier":[{"value":"3","text":"Equipment Subrentals"}],"custrecord_ng_venue_pt_price_level":[{"value":"2","text":"Advanced Price"}],"custrecord_ng_venue_pt_commission_rate":"77.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_disc_threshold":"40.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_comm_discount":"50.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_service_fee":"15.0%","custrecord_ng_venue_pt_srv_fee_tier":false},{"id":"8","custrecord_ng_venue_item_svc_tier":[{"value":"8","text":"Service Charge"}],"custrecord_ng_venue_pt_price_level":[],"custrecord_ng_venue_pt_commission_rate":"47.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_disc_threshold":"40.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_comm_discount":"50.0%","CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_service_fee":"15.0%","custrecord_ng_venue_pt_srv_fee_tier":true}],"sessions":[{"id":"23","name":"Breakfast Presentation - Meeting Room Gold (ALT Atlanta Tech Summit 2021)","processed":false,"equipmentData":[{"internalid":[{"value":"18","text":"18"}],"custrecord_ng_cs_sess_equip_item":[{"value":"7","text":"Side Chair"}],"CUSTRECORD_NG_CS_SESS_EQUIP_ITEM.custitem_ng_cs_item_svc_tier":[{"value":"3","text":"Equipment Subrentals"}],"custrecord_ng_cs_sess_equip_sess":[{"value":"23","text":"Breakfast Presentation - Meeting Room Gold (ALT Atlanta Tech Summit 2021)"}],"processed":false},{"internalid":[{"value":"19","text":"19"}],"custrecord_ng_cs_sess_equip_item":[{"value":"578","text":"Deluxe Booth Chair"}],"CUSTRECORD_NG_CS_SESS_EQUIP_ITEM.custitem_ng_cs_item_svc_tier":[{"value":"2","text":"Equipment Rentals"}],"custrecord_ng_cs_sess_equip_sess":[{"value":"23","text":"Breakfast Presentation - Meeting Room Gold (ALT Atlanta Tech Summit 2021)"}],"processed":false}],"tasksData":[{"internalid":[{"value":"25","text":"25"}],"custrecord_ng_cs_sess_task_item":[{"value":"539","text":"Show Labor"}],"CUSTRECORD_NG_CS_SESS_TASK_ITEM.custitem_ng_cs_item_svc_tier":[{"value":"5","text":"Operator Labor"}],"custrecord_ng_cs_sess_task_sess":[{"value":"23","text":"Breakfast Presentation - Meeting Room Gold (ALT Atlanta Tech Summit 2021)"}],"processed":false},{"internalid":[{"value":"26","text":"26"}],"custrecord_ng_cs_sess_task_item":[{"value":"539","text":"Show Labor"}],"CUSTRECORD_NG_CS_SESS_TASK_ITEM.custitem_ng_cs_item_svc_tier":[{"value":"5","text":"Operator Labor"}],"custrecord_ng_cs_sess_task_sess":[{"value":"23","text":"Breakfast Presentation - Meeting Room Gold (ALT Atlanta Tech Summit 2021)"}],"processed":false},{"internalid":[{"value":"27","text":"27"}],"custrecord_ng_cs_sess_task_item":[{"value":"539","text":"Show Labor"}],"CUSTRECORD_NG_CS_SESS_TASK_ITEM.custitem_ng_cs_item_svc_tier":[{"value":"5","text":"Operator Labor"}],"custrecord_ng_cs_sess_task_sess":[{"value":"23","text":"Breakfast Presentation - Meeting Room Gold (ALT Atlanta Tech Summit 2021)"}],"processed":false},{"internalid":[{"value":"28","text":"28"}],"custrecord_ng_cs_sess_task_item":[{"value":"539","text":"Show Labor"}],"CUSTRECORD_NG_CS_SESS_TASK_ITEM.custitem_ng_cs_item_svc_tier":[{"value":"5","text":"Operator Labor"}],"custrecord_ng_cs_sess_task_sess":[{"value":"23","text":"Breakfast Presentation - Meeting Room Gold (ALT Atlanta Tech Summit 2021)"}],"processed":false},{"internalid":[{"value":"29","text":"29"}],"custrecord_ng_cs_sess_task_item":[{"value":"539","text":"Show Labor"}],"CUSTRECORD_NG_CS_SESS_TASK_ITEM.custitem_ng_cs_item_svc_tier":[{"value":"5","text":"Operator Labor"}],"custrecord_ng_cs_sess_task_sess":[{"value":"23","text":"Breakfast Presentation - Meeting Room Gold (ALT Atlanta Tech Summit 2021)"}],"processed":false},{"internalid":[{"value":"30","text":"30"}],"custrecord_ng_cs_sess_task_item":[{"value":"539","text":"Show Labor"}],"CUSTRECORD_NG_CS_SESS_TASK_ITEM.custitem_ng_cs_item_svc_tier":[{"value":"5","text":"Operator Labor"}],"custrecord_ng_cs_sess_task_sess":[{"value":"23","text":"Breakfast Presentation - Meeting Room Gold (ALT Atlanta Tech Summit 2021)"}],"processed":false}]},{"id":"24","name":"Breakfast Presentation 2 (ALT Atlanta Tech Summit 2021)","processed":false,"equipmentData":[{"internalid":[{"value":"17","text":"17"}],"custrecord_ng_cs_sess_equip_item":[{"value":"7","text":"Side Chair"}],"CUSTRECORD_NG_CS_SESS_EQUIP_ITEM.custitem_ng_cs_item_svc_tier":[{"value":"3","text":"Equipment Subrentals"}],"custrecord_ng_cs_sess_equip_sess":[{"value":"24","text":"Breakfast Presentation 2 (ALT Atlanta Tech Summit 2021)"}],"processed":false}],"tasksData":[]}]});
                    //
                    //     log.debug({title: 'Event Recalc Process Data', details: eventRecalcProcessData});
                    //
                    // }

                    var callAgain = false;

                    var taxSchedulesData = [];

                    let sessionCount = params.sessionCount;

                    log.debug({title: 'Session Count', details: sessionCount});

                    if(NG.tools.isEmpty(sessionCount)){

                        sessionCount = 0;

                    }else{

                        sessionCount = Number(sessionCount);

                    }

                    if(!NG.tools.isEmpty(eventRecalcProcessData)){

                        log.debug('TP1')

                        log.debug({title: 'Process Recalc Data', details: eventRecalcProcessData});

                        var scriptObj = runtime.getCurrentScript();
                        var remainingUnits = scriptObj.getRemainingUsage()
                        log.debug('Remaining governance units: ' + remainingUnits);

                        eventRecalcProcessData = JSON.parse(eventRecalcProcessData);

                        eventRecalcProcessData.timesCalled = Number(eventRecalcProcessData.timesCalled) + 1;

                        for(let i=sessionCount; i < eventRecalcProcessData.sessions.length; i++){

                            try{

                                let sessionDataObj = eventRecalcProcessData.sessions[i];

                                log.debug({title: 'Session Data Obj', details: JSON.stringify(sessionDataObj)});

                                if(!sessionDataObj.processed && NG.tools.isEmpty(sessionDataObj.error)){

                                    if(!NG.tools.isEmpty(sessionDataObj.equipmentData) && sessionDataObj.equipmentData.length > 0){

                                        log.debug('TP3');

                                        //Start Session Equipment Recalc

                                        log.debug('Start Process of Session Equipment Records');

                                        let equipmentCount = sessionDataObj.equipmentCount;

                                        if(NG.tools.isEmpty(equipmentCount)){

                                            sessionDataObj.equipmentCount = 0;

                                        }else{

                                            sessionDataObj.equipmentCount = Number(equipmentCount);

                                        }

                                        for(let i=sessionDataObj.equipmentCount; i < sessionDataObj.equipmentData.length; i++){

                                            try{

                                                remainingUnits = scriptObj.getRemainingUsage()
                                                log.debug('Remaining governance units: ' + remainingUnits);

                                                if(remainingUnits < 100 || sessionDataObj.equipmentCount >= sessionDataObj.equipmentData.length){

                                                    break;

                                                }

                                                let isTaxable = false;

                                                let equipmentDataObj = sessionDataObj.equipmentData[i];

                                                log.debug({title: 'Equipment Data Object', details: JSON.stringify(equipmentDataObj)});

                                                if(!equipmentDataObj.processed && NG.tools.isEmpty(equipmentDataObj.error)){

                                                    let equipmentRecID = equipmentDataObj['internalid'][0].value;

                                                    let equipmentRec = record.load({
                                                        type: 'customrecord_ng_cs_sess_equip',
                                                        id: equipmentRecID,
                                                        isDynamic: true
                                                    });

                                                    let itemID = equipmentDataObj.custrecord_ng_cs_sess_equip_item[0].value;

                                                    log.debug({title: 'Item ID', details: itemID});

                                                    let itemServiceTierID = equipmentDataObj['CUSTRECORD_NG_CS_SESS_EQUIP_ITEM.custitem_ng_cs_item_svc_tier'][0].value;

                                                    log.debug({title: 'Item Service Tier ID', details: itemServiceTierID});

                                                    log.debug({title: 'Event ID', details: eventRecalcProcessData.eventID});

                                                    log.debug({title: 'Tax Group Rec ID', details: eventRecalcProcessData.eventTaxGroupID});

                                                    log.debug({title: 'Tax State', details: eventRecalcProcessData.eventTaxState});

                                                    log.debug({title: 'Tax Country', details: eventRecalcProcessData.eventTaxNexusCountry});

                                                    log.debug({title: 'Tax Rate', details: eventRecalcProcessData.taxRate});

                                                    log.debug({title: 'Tax Rate 2', details: eventRecalcProcessData.taxRate2});

                                                    equipmentRec.setText({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_taxrate',
                                                        text: eventRecalcProcessData.taxRate
                                                    });

                                                    equipmentRec.setText({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_taxrate_pst',
                                                        text: eventRecalcProcessData.taxRate2
                                                    });

                                                    let eventItemDiscounts = eventRecalcProcessData.eventItemDiscounts;

                                                    //Get Item Discounts

                                                    let itemDiscountsResults = eventItemDiscounts.filter(function(discount){

                                                        let discountServiceTiers = discount['custrecord_ng_cs_event_discount_tiers'];

                                                        if(!Array.isArray(discountServiceTiers)){

                                                            discountServiceTiers = [discountServiceTiers];

                                                        }

                                                        if(discountServiceTiers.findIndex(function(tier){return tier.value == itemServiceTierID}) != -1){

                                                            return true;

                                                        }else{

                                                            return false;

                                                        }
                                                    })

                                                    let itemDiscount = 0;

                                                    let itemFlatDiscount = 0;

                                                    if(!NG.tools.isEmpty(itemDiscountsResults) && itemDiscountsResults.length > 0){

                                                        for(let i=0; i < itemDiscountsResults.length; i++){

                                                            let itemDiscountRate = itemDiscountsResults[i]['custrecord_ng_discount_rate'];

                                                            log.debug({title: 'Discount Percent/Flat Rate', details: itemDiscountRate});

                                                            if(itemDiscountRate.indexOf('%') != -1){

                                                                let itemDiscountPercent = Math.abs(parseFloat(itemDiscountRate));

                                                                itemDiscount = itemDiscount + itemDiscountPercent;

                                                            }else{

                                                                itemFlatDiscount = itemFlatDiscount + Number(itemDiscountRate);

                                                            }

                                                        }


                                                    }

                                                    equipmentRec.setText({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_discount',
                                                        text: Number(itemDiscount).toFixed(1) + '%'
                                                    })

                                                    equipmentRec.setText({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_flat_discnt',
                                                        text: itemFlatDiscount
                                                    });

                                                    //Get Venue Service Tier

                                                    let itemVenueServiceTiers = eventRecalcProcessData.venueServiceTiersData;

                                                    let itemVenueServiceTier = '';

                                                    let itemVenueServiceTierIndex = itemVenueServiceTiers.findIndex(function(venueTier){return venueTier['custrecord_ng_venue_item_svc_tier'][0].value == itemServiceTierID});

                                                    if(itemVenueServiceTierIndex != -1){

                                                        itemVenueServiceTier = itemVenueServiceTiers[itemVenueServiceTierIndex];

                                                    }

                                                    let serviceFeeVenueServiceTier = '';

                                                    let serviceFeeVenueServiceTierIndex = itemVenueServiceTiers.findIndex(function(venueTier){return venueTier['custrecord_ng_venue_pt_srv_fee_tier']});

                                                    if(serviceFeeVenueServiceTierIndex != -1){

                                                        serviceFeeVenueServiceTier = itemVenueServiceTiers[serviceFeeVenueServiceTierIndex];

                                                    }

                                                    let itemPriceLevelID = itemVenueServiceTier['custrecord_ng_venue_pt_price_level'][0].value;

                                                    log.debug({title: 'Item Price Level ID', details: itemPriceLevelID});

                                                    let itemCommissionRate = itemVenueServiceTier['custrecord_ng_venue_pt_commission_rate'];

                                                    log.debug({title: 'Item Commission Rate', details: itemCommissionRate});

                                                    let itemServiceFeeCommissionRate = serviceFeeVenueServiceTier['custrecord_ng_venue_pt_commission_rate'];

                                                    log.debug({title: 'Item Service Fee Commission Rate', details: itemServiceFeeCommissionRate});

                                                    let itemServiceFeeRate = itemVenueServiceTier["CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_service_fee"];

                                                    log.debug({title: 'Item Service Fee Rate', details: itemServiceFeeRate});

                                                    let discountThreshold = itemVenueServiceTier["CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_disc_threshold"];

                                                    log.debug({title: 'Venue Discount Threshold', details: discountThreshold});

                                                    let commissionDiscount = itemVenueServiceTier["CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_comm_discount"];

                                                    log.debug({title: 'Venue Commission Discount', details: commissionDiscount});

                                                    if(!NG.tools.isEmpty(discountThreshold) && !NG.tools.isEmpty(itemDiscount) && !NG.tools.isEmpty(itemCommissionRate) && !NG.tools.isEmpty(commissionDiscount) && itemDiscount > parseFloat(discountThreshold)){

                                                        let discountDiff = itemDiscount - parseFloat(discountThreshold);

                                                        log.debug({titel: 'Discount % Over Threshold', details:  discountDiff});

                                                        itemCommissionRate = parseFloat(itemCommissionRate) - (discountDiff * (parseFloat(commissionDiscount)/100));

                                                        log.debug({title: 'Item Commission Rate after discount', details: itemCommissionRate});

                                                    }

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_pricelvl',
                                                        value: itemPriceLevelID
                                                    });

                                                    equipmentRec.setText({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_service_fee',
                                                        text: itemServiceFeeRate
                                                    });

                                                    if(!NG.tools.isEmpty(itemServiceFeeCommissionRate)){

                                                        log.debug({title: 'Item Service Fee Commission Rate', details: itemServiceFeeCommissionRate});

                                                        itemServiceFeeCommissionRate = parseFloat(itemServiceFeeCommissionRate).toFixed(1) + '%';

                                                        log.debug({title: 'Item Service Fee Commission Rate', details: itemServiceFeeCommissionRate});

                                                        equipmentRec.setText({
                                                            fieldId: 'custrecord_ng_cs_sess_equip_ser_com_rate',
                                                            text: itemServiceFeeCommissionRate
                                                        });

                                                    }else{

                                                        equipmentRec.setText({
                                                            fieldId: 'custrecord_ng_cs_sess_equip_ser_com_rate',
                                                            text: ''
                                                        });

                                                    }

                                                    log.debug({title: 'Venue Currency ID', details: eventRecalcProcessData.eventVenueCurrencyID});

                                                    let priceLevelsSearchFilters = [
                                                        ["internalid","anyof",itemID]
                                                    ]

                                                    let multiCurrencyEnabled = runtime.isFeatureInEffect({
                                                        feature: 'MULTICURRENCY'
                                                    });

                                                    if(multiCurrencyEnabled){

                                                        priceLevelsSearchFilters.push("AND");

                                                        priceLevelsSearchFilters.push(["pricing.currency","anyof",eventRecalcProcessData.eventVenueCurrencyID]);

                                                    }

                                                    let priceLevelsSearchColumns = [
                                                        search.createColumn({
                                                            name: "itemid",
                                                            sort: search.Sort.ASC,
                                                            label: "Name"
                                                        }),
                                                        search.createColumn({name: "taxschedule", label: "Tax Schedule"}),
                                                        search.createColumn({name: "custitem_ng_cs_is_subrental_item", label: "Is Sub-Rental Item"}),
                                                        search.createColumn({name: "costestimate", label: "Item Defined Cost"}),
                                                        search.createColumn({name: "type", label: "Type"}),
                                                        search.createColumn({name: "baseprice", label: "Base Price"}),
                                                        search.createColumn({
                                                            name: "pricelevel",
                                                            join: "pricing",
                                                            label: "Price Level"
                                                        }),
                                                        search.createColumn({
                                                            name: "unitprice",
                                                            join: "pricing",
                                                            label: "Unit Price"
                                                        }),
                                                        search.createColumn({
                                                            name: "internalid",
                                                            join: "pricing",
                                                            label: "Internal ID"
                                                        })
                                                    ]

                                                    let priceLevelsSearchResults = NG.tools.getSearchResultsAdv({
                                                        type: 'item',
                                                        filters: priceLevelsSearchFilters,
                                                        columns: priceLevelsSearchColumns
                                                    });

                                                    if(!NG.tools.isEmpty(priceLevelsSearchResults) && priceLevelsSearchResults.length > 0){

                                                        let filteredPriceLevelResult = priceLevelsSearchResults.filter(function(result){return result.getValue({name: 'internalid', join: 'pricing'}) == itemPriceLevelID});

                                                        if(!NG.tools.isEmpty(filteredPriceLevelResult) && filteredPriceLevelResult.length > 0){

                                                            if(eventRecalcProcessData.eventCustomerIsTaxable){

                                                                let itemTaxScheduleID = filteredPriceLevelResult[0].getValue({name: 'taxschedule'});

                                                                let taxSchedDataIndex = taxSchedulesData.findIndex(function(data){return data.id == itemTaxScheduleID});

                                                                let taxScheduleRec = '';

                                                                if(taxSchedDataIndex == -1){

                                                                    taxScheduleRec = record.load({
                                                                        type: 'taxschedule',
                                                                        id: itemTaxScheduleID,
                                                                        isDynamic: true
                                                                    });

                                                                    let taxScheduleData = {};

                                                                    taxScheduleData.rec = taxScheduleRec;

                                                                    taxSchedulesData.push(taxScheduleData);

                                                                }else{

                                                                    taxScheduleRec = taxSchedulesData[taxSchedDataIndex].rec;

                                                                }

                                                                if(eventRecalcProcessData.eventTaxNexusCountry == 'US'){

                                                                    let lineCount = taxScheduleRec.getLineCount({sublistId: 'usnexuses'});

                                                                    for(let i=0; i < lineCount; i++){

                                                                        let nexusText = taxScheduleRec.getSublistText({sublistId: 'usnexuses', line: i, fieldId: 'nexus'});

                                                                        if(nexusText == eventRecalcProcessData.eventTaxState){

                                                                            isTaxable = taxScheduleRec.getSublistValue({sublistId: 'usnexuses', line: i, fieldId: 'taxable'});

                                                                            break;
                                                                        }
                                                                    }

                                                                }else{

                                                                    let lineCount = taxScheduleRec.getLineCount({sublistId: 'nexuses'});

                                                                    for(let i=0; i < lineCount; i++){

                                                                        let nexusCountry = taxScheduleRec.getSublistValue({sublistId: 'nexuses', line: i, fieldId: 'nexuscountry'});

                                                                        if(nexusCountry == eventRecalcProcessData.eventTaxNexusCountry){

                                                                            let nexusValue = taxScheduleRec.getSublistValue({sublistId: 'nexuses', line: i, fieldId: 'nexus'});

                                                                            let nexusDescription = '';

                                                                            let nexusesSearchFilters = [
                                                                                ["isinactive","is","F"],
                                                                                "AND",
                                                                                ["internalid","anyof", nexusValue]
                                                                            ]

                                                                            let nexusesSearchColumns = [
                                                                                search.createColumn({name: "internalid", label: "Internal ID"}),
                                                                                search.createColumn({
                                                                                    name: "country",
                                                                                    sort: search.Sort.ASC,
                                                                                    label: "Country"
                                                                                }),
                                                                                search.createColumn({name: "state", label: "State/Province/County"}),
                                                                                search.createColumn({name: "description", label: "Description"})
                                                                            ]

                                                                            let taxNexusResults = NG.tools.getSearchResults('nexus', nexusesSearchFilters, nexusesSearchColumns);

                                                                            if(!NG.tools.isEmpty(taxNexusResults) && taxNexusResults.length > 0){

                                                                                nexusDescription = taxNexusResults[0].getValue({name: 'description'});

                                                                            }

                                                                            log.debug({title: 'Nexus Description', details: nexusDescription});
                                                                            log.debug({title: 'Tax State', details: eventRecalcProcessData.eventTaxState});


                                                                            if(nexusDescription == eventRecalcProcessData.eventTaxState){

                                                                                let salesTaxCode = taxScheduleRec.getSublistValue({sublistId: 'nexuses', line: i, fieldId: 'salestaxcode'});

                                                                                if(!NG.tools.isEmpty(salesTaxCode)){

                                                                                    isTaxable = true;

                                                                                }

                                                                                break;
                                                                            }

                                                                        }else{

                                                                            continue;

                                                                        }


                                                                    }

                                                                }


                                                            }

                                                            //Next Step Here

                                                            let itemPrice = filteredPriceLevelResult[0].getValue({name: 'unitprice', join: 'pricing'});

                                                            let itemDefinedCost = filteredPriceLevelResult[0].getValue({name: 'costestimate'});

                                                            let isSubRentalItem = filteredPriceLevelResult[0].getValue({name: 'custitem_ng_cs_is_subrental_item'});

                                                            log.debug({title: 'Item Price', details: itemPrice});

                                                            log.debug({title: 'Item Defined Cost', details: itemDefinedCost});

                                                            log.debug({title: 'Is Sub-rental Item', details: isSubRentalItem});

                                                            log.debug({title:'Is Taxable', details: isTaxable});

                                                            if(isTaxable == 'Yes' || isTaxable == 'T' || isTaxable == true){
                                                                isTaxable = true;
                                                            }else{
                                                                isTaxable = false;
                                                            }

                                                            equipmentRec.setValue({
                                                                fieldId: 'custrecord_ng_cs_sess_equip_is_taxable',
                                                                value: isTaxable
                                                            });

                                                            let currentRate = equipmentRec.getValue({
                                                                fieldId: 'custrecord_ng_cs_sess_equip_rate'
                                                            });

                                                            let currentDefaultRate = equipmentRec.getValue({
                                                                fieldId: 'custrecord_ng_cs_sess_equip_default_rate'
                                                            });

                                                            if(currentRate == currentDefaultRate){

                                                                equipmentRec.setValue({
                                                                    fieldId: 'custrecord_ng_cs_sess_equip_rate',
                                                                    value: itemPrice
                                                                });

                                                            }

                                                            equipmentRec.setValue({
                                                                fieldId: 'custrecord_ng_cs_sess_equip_default_rate',
                                                                value: itemPrice
                                                            });

                                                            if(isSubRentalItem){

                                                                equipmentRec.setValue({
                                                                    fieldId: 'custrecord_ng_cs_sess_equip_is_subrental',
                                                                    value: true
                                                                });

                                                                equipmentRec.setValue({
                                                                    fieldId: 'custrecord_ng_cs_sess_equip_subrent_cost',
                                                                    value: itemDefinedCost
                                                                });

                                                            }else{

                                                                equipmentRec.setValue({
                                                                    fieldId: 'custrecord_ng_cs_sess_equip_is_subrental',
                                                                    value: false
                                                                });

                                                                equipmentRec.setValue({
                                                                    fieldId: 'custrecord_ng_cs_sess_equip_subrent_cost',
                                                                    value: ''
                                                                });

                                                            }

                                                        }

                                                    }

                                                    //Item Commission Rate

                                                    if(!NG.tools.isEmpty(itemCommissionRate)){

                                                        log.debug({title: 'Item Commission Rate', details: itemCommissionRate});

                                                        itemCommissionRate = parseFloat(itemCommissionRate).toFixed(1) + '%';

                                                        log.debug({title: 'Item Commission Rate', details: itemCommissionRate});

                                                        equipmentRec.setText({
                                                            fieldId: 'custrecord_ng_cs_sess_equip_com_rate',
                                                            text: itemCommissionRate
                                                        });

                                                    }else{

                                                        equipmentRec.setText({
                                                            fieldId: 'custrecord_ng_cs_sess_equip_com_rate',
                                                            text: ''
                                                        });

                                                    }

                                                    //Run Calcs Here

                                                    let csSessionDates = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_session_equip_days'});

                                                    let lineQty = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_qty'});

                                                    let lineRate = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_rate'});

                                                    let lineDefaultRate = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_default_rate'});

                                                    let lineDailyPrice = Number(lineQty) * Number(lineRate);

                                                    let linePrice = Number(lineQty) * csSessionDates.length * Number(lineRate);

                                                    log.debug({title: 'Line Qty', details: lineQty});
                                                    log.debug({title: 'Line Rate', details: lineRate});
                                                    log.debug({title: 'Line Default Rate', details: lineDefaultRate});
                                                    log.debug({title: 'Line Daily Price', details: lineDailyPrice});
                                                    log.debug({title: 'Line Price', details: linePrice});

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_day_price',
                                                        value: lineDailyPrice
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_price',
                                                        value: linePrice
                                                    });

                                                    let lineDiscountRate = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_discount'});

                                                    let lineFlatDiscount = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_flat_discnt'});

                                                    let lineServiceFeeRate = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_service_fee'});

                                                    let lineTaxRate = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_taxrate'});

                                                    let lineTaxRatePST = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_taxrate_pst'});

                                                    let lineIsTaxable = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_is_taxable'});

                                                    let lineCommissionRate = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_com_rate'});

                                                    let lineServiceFeeCommissionRate = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_ser_com_rate'});

                                                    let lineIsSubrental = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_is_subrental'});

                                                    let lineSubrentalCost = equipmentRec.getValue({fieldId: 'custrecord_ng_cs_sess_equip_subrent_cost'});

                                                    log.debug({title: 'Line Discount Rate: ', details:  lineDiscountRate});
                                                    log.debug({title: 'Line Flat Discount: ', details: lineFlatDiscount});
                                                    log.debug({title: 'Line Service Fee Rate: ', details: lineServiceFeeRate});
                                                    log.debug({title: 'Line Tax Rate: ', details: lineTaxRate});
                                                    log.debug({title: 'Line Tax Rate PST: ', details: lineTaxRatePST});
                                                    log.debug({title: 'Line Is Taxable: ', details: lineIsTaxable});
                                                    log.debug({title: 'Line Item Commission Rate: ', details: lineCommissionRate});
                                                    log.debug({title: 'Line Item Service Fee Commission Rate: ', details: lineServiceFeeCommissionRate});
                                                    log.debug({title: 'Line Is Sub-rental: ', details: lineIsSubrental});
                                                    log.debug({title: 'Line Sub-rental Cost: ', details: lineSubrentalCost});

                                                    if(!NG.tools.isEmpty(lineServiceFeeRate)){

                                                        lineServiceFeeRate = Number(lineServiceFeeRate)/100;

                                                    }else{

                                                        lineServiceFeeRate = 0;

                                                    }

                                                    if(!NG.tools.isEmpty(lineDiscountRate)){

                                                        lineDiscountRate = Number(lineDiscountRate)/100;

                                                    }else{

                                                        lineDiscountRate = 0;

                                                    }

                                                    if(NG.tools.isEmpty(lineFlatDiscount)){

                                                        lineFlatDiscount = 0;

                                                    }

                                                    if(!NG.tools.isEmpty(lineCommissionRate)){

                                                        lineCommissionRate = Number(lineCommissionRate)/100;

                                                    }else{

                                                        lineCommissionRate = 0;

                                                    }

                                                    if(!NG.tools.isEmpty(lineServiceFeeCommissionRate)){

                                                        lineServiceFeeCommissionRate = Number(lineServiceFeeCommissionRate)/100;

                                                    }else{

                                                        lineServiceFeeCommissionRate = 0;

                                                    }

                                                    let lineDailyServiceFee = Math.round((lineDailyPrice * lineServiceFeeRate) *100)/100;

                                                    let lineServiceFee = lineDailyServiceFee * csSessionDates.length;

                                                    let lineDailyDiscount = Math.round((lineDailyPrice * lineDiscountRate)*100)/100;

                                                    let lineDiscount = (lineDailyDiscount * csSessionDates.length) + lineFlatDiscount;

                                                    if((!NG.tools.isEmpty(lineTaxRate) || !NG.tools.isEmpty(lineTaxRatePST)) && lineIsTaxable){

                                                        if(!NG.tools.isEmpty(lineTaxRate)){

                                                            lineTaxRate = Number(lineTaxRate)/100;

                                                        }else{

                                                            lineTaxRate = 0;

                                                        }

                                                        if(!NG.tools.isEmpty(lineTaxRatePST)){

                                                            lineTaxRatePST = Number(lineTaxRatePST)/100;

                                                        }else{

                                                            lineTaxRatePST = 0;

                                                        }


                                                    }else{

                                                        lineTaxRate = 0;
                                                        lineTaxRatePST = 0;

                                                    }

                                                    let lineDailySubTotal = lineDailyPrice - lineDailyDiscount;

                                                    let lineSubTotal = linePrice;

                                                    lineSubTotal = lineSubTotal - lineDiscount;

                                                    let lineCommissionAmount = 0;

                                                    let lineDailyCommissionAmount = 0;

                                                    if(lineIsSubrental){

                                                        lineDailyCommissionAmount = Math.round(((lineDailyPrice - (lineSubrentalCost * lineQty)) * lineCommissionRate)*100)/100;

                                                        lineCommissionAmount = lineDailyCommissionAmount * csSessionDates.length;

                                                    }else{

                                                        lineDailyCommissionAmount = Math.round((lineDailySubTotal * lineCommissionRate)*100)/100;

                                                        lineCommissionAmount = lineDailyCommissionAmount * csSessionDates.length;

                                                    }

                                                    let lineDailyServiceFeeTaxAmount = Math.round((lineDailyServiceFee * lineTaxRate)*100)/100;

                                                    lineSubTotal = lineSubTotal + lineServiceFee;

                                                    let lineDailyServiceFeeCommissionAmount = Math.round((lineDailyServiceFee * lineServiceFeeCommissionRate)*100)/100;

                                                    let lineServiceFeeCommissionAmount = lineDailyServiceFeeCommissionAmount * csSessionDates.length;

                                                    let lineDailyPriceTaxAmount = Math.round((lineDailySubTotal * lineTaxRate)*100)/100;

                                                    let lineDailyTaxAmount = lineDailyPriceTaxAmount + lineDailyServiceFeeTaxAmount;

                                                    let lineTaxAmount = lineDailyTaxAmount * csSessionDates.length;

                                                    let lineDailyTaxAmountPST = Math.round((lineDailySubTotal * lineTaxRatePST)*100)/100;

                                                    let lineTaxAmountPST = lineDailyTaxAmountPST * csSessionDates.length;

                                                    lineDailySubTotal =lineDailySubTotal + lineDailyServiceFee;

                                                    let lineDailyCommissionTotal = Math.round((lineDailyCommissionAmount + lineDailyServiceFeeCommissionAmount)*100)/100;

                                                    let lineCommissionTotal = lineDailyCommissionTotal * csSessionDates.length;

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_euip_daily_sub_total',
                                                        value: lineDailySubTotal
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_day_sub_total_deci',
                                                        value: Math.round(lineDailySubTotal*100000)/100000,
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_subtotal',
                                                        value: lineSubTotal
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_sub_total_deci',
                                                        value: Math.round(lineSubTotal*100000)/100000
                                                    });

                                                    let lineItemTotalAmount = linePrice+lineServiceFee-lineDiscount+lineTaxAmount+lineTaxAmountPST;

                                                    let lineItemDailyTotalAmount = lineDailyPrice + lineDailyServiceFee - lineDailyDiscount + lineDailyTaxAmount + lineDailyTaxAmountPST;

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_equip_day_discnt_amt',
                                                        value: lineDailyDiscount
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_day_disc_amt_deci',
                                                        value: Math.round(lineDailyDiscount*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_discount_amt',
                                                        value: lineDiscount
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_disc_amt_deci',
                                                        value: Math.round(lineDiscount*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_equip_day_serv_fee_amt',
                                                        value: lineDailyServiceFee
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_day_serv_fee_amt_deci',
                                                        value: Math.round(lineDailyServiceFee*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_serv_fee_amt',
                                                        value: lineServiceFee
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_serv_fee_amt_deci',
                                                        value: Math.round(lineServiceFee*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_equip_day_tax_amt',
                                                        value: lineDailyTaxAmount
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_day_tax_amt_deci',
                                                        value: Math.round(lineDailyTaxAmount*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_equip_day_tax_amt_pst',
                                                        value: lineDailyTaxAmountPST
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_day_tax_amt_pst_deci',
                                                        value: Math.round(lineDailyTaxAmountPST*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_tax_amt',
                                                        value: lineTaxAmount
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_tax_amt_deci',
                                                        value: Math.round(lineTaxAmount*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_tax_amt_pst',
                                                        value: lineTaxAmountPST
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_tax_amt_pst_deci',
                                                        value: Math.round(lineTaxAmountPST*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_equip_daily_amount',
                                                        value: lineItemDailyTotalAmount
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_day_amt_deci',
                                                        value: Math.round(lineItemDailyTotalAmount*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_amount',
                                                        value: lineItemTotalAmount
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_amount_deci',
                                                        value: Math.round(lineItemTotalAmount*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_equip_daily_comm_amount',
                                                        value: lineDailyCommissionAmount
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_day_com_amt_deci',
                                                        value: Math.round(lineDailyCommissionAmount*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_com_amount',
                                                        value: lineCommissionAmount
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_com_amt_deci',
                                                        value: Math.round(lineCommissionAmount*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_equip_day_serv_comm_amount',
                                                        value: lineDailyServiceFeeCommissionAmount
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_day_serv_fee_com_amt_deci',
                                                        value: Math.round(lineDailyServiceFeeCommissionAmount*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_ser_com_amt',
                                                        value: lineServiceFeeCommissionAmount
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_serv_fee_com_amt_deci',
                                                        value: Math.round(lineServiceFeeCommissionAmount*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_equip_day_comm_total',
                                                        value: lineDailyCommissionTotal
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_day_com_total_deci',
                                                        value: Math.round(lineDailyCommissionTotal*100000)/100000
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_equip_com_total',
                                                        value: lineCommissionTotal
                                                    });

                                                    equipmentRec.setValue({
                                                        fieldId: 'custrecord_ng_com_total_deci',
                                                        value: Math.round(lineCommissionTotal*100000)/100000
                                                    });

                                                    equipmentRec.save({
                                                        ignoreMandatoryFields: true,
                                                        enableSourcing: true
                                                    });

                                                    equipmentDataObj.processed = true;

                                                    sessionDataObj.equipmentData[i] = equipmentDataObj;

                                                    sessionDataObj.equipmentCount = sessionDataObj.equipmentCount + 1;

                                                    remainingUnits = scriptObj.getRemainingUsage()
                                                    log.debug('Remaining governance units: ' + remainingUnits);

                                                    if(remainingUnits <= 100 && ((i+1) < sessionDataObj.equipmentData.length)){

                                                        callAgain = true;

                                                        break;

                                                    }else if (sessionDataObj.equipmentCount >= sessionDataObj.equipmentData.length) {

                                                        callAgain = false;

                                                        break;
                                                    }

                                                }else{

                                                    sessionDataObj.equipmentCount = sessionDataObj.equipmentCount + 1;

                                                    continue;

                                                }

                                            }catch(err){

                                                sessionDataObj.equipmentCount = sessionDataObj.equipmentCount + 1;

                                                log.error({title: 'Error Equipment Record Recalc', details: JSON.stringify(err)});

                                                sessionDataObj.equipmentData[i].error = err.message;

                                            }

                                        }

                                    }

                                    eventRecalcProcessData.sessions[i] = sessionDataObj;

                                    log.debug({title: 'Session Equipment Processed', details: JSON.stringify(sessionDataObj)});

                                    if(callAgain){

                                        break;

                                    }

                                    remainingUnits = scriptObj.getRemainingUsage()
                                    log.debug('Remaining governance units: ' + remainingUnits);

                                    if(remainingUnits <= 100 && ((i+1) < eventRecalcProcessData.sessions.length) && sessionDataObj.tasksData.length > 0){

                                        callAgain = true;

                                        break;

                                    }


                                    if(!NG.tools.isEmpty(sessionDataObj.tasksData) && sessionDataObj.tasksData.length > 0){

                                        log.debug('Start Process of Session Tasks Records');

                                        let taskCount = sessionDataObj.taskCount;

                                        if(NG.tools.isEmpty(taskCount)){

                                            sessionDataObj.taskCount = 0;

                                        }else{

                                            sessionDataObj.taskCount = Number(taskCount);

                                        }

                                        for(let i=sessionDataObj.taskCount; i < sessionDataObj.tasksData.length; i++){

                                            try{

                                                remainingUnits = scriptObj.getRemainingUsage()
                                                log.debug('Remaining governance units: ' + remainingUnits);

                                                if(remainingUnits < 100 || sessionDataObj.taskCount >= sessionDataObj.tasksData.length){

                                                    break;

                                                }

                                                let isTaxable = false;

                                                let taskDataObj = sessionDataObj.tasksData[i];

                                                log.debug({title: 'Task Data Object', details: JSON.stringify(taskDataObj)});

                                                if(!taskDataObj.processed && NG.tools.isEmpty(taskDataObj.error)){

                                                    let taskRecID = taskDataObj['internalid'][0].value;

                                                    let taskRec = record.load({
                                                        type: 'customrecord_ng_cs_sess_task',
                                                        id: taskRecID,
                                                        isDynamic: true
                                                    });

                                                    let itemID = taskDataObj.custrecord_ng_cs_sess_task_item[0].value;

                                                    log.debug({title: 'Item ID', details: itemID});

                                                    let itemServiceTierID = taskDataObj['CUSTRECORD_NG_CS_SESS_TASK_ITEM.custitem_ng_cs_item_svc_tier'][0].value;

                                                    log.debug({title: 'Item Service Tier ID', details: itemServiceTierID});

                                                    log.debug({title: 'Event ID', details: eventRecalcProcessData.eventID});

                                                    log.debug({title: 'Tax Group Rec ID', details: eventRecalcProcessData.eventTaxGroupID});

                                                    log.debug({title: 'Tax State', details: eventRecalcProcessData.eventTaxState});

                                                    log.debug({title: 'Tax Country', details: eventRecalcProcessData.eventTaxNexusCountry});

                                                    log.debug({title: 'Tax Rate', details: eventRecalcProcessData.taxRate});

                                                    log.debug({title: 'Tax Rate 2', details: eventRecalcProcessData.taxRate2});

                                                    taskRec.setText({
                                                        fieldId: 'custrecord_ng_cs_sess_task_taxrate',
                                                        text: eventRecalcProcessData.taxRate
                                                    });

                                                    taskRec.setText({
                                                        fieldId: 'custrecord_ng_cs_sess_task_taxrate_pst',
                                                        text: eventRecalcProcessData.taxRate2
                                                    });

                                                    let eventItemDiscounts = eventRecalcProcessData.eventItemDiscounts;

                                                    //Get Item Discounts

                                                    let itemDiscountsResults = eventItemDiscounts.filter(function(discount){

                                                        let discountServiceTiers = discount['custrecord_ng_cs_event_discount_tiers'];

                                                        if(!Array.isArray(discountServiceTiers)){

                                                            discountServiceTiers = [discountServiceTiers];

                                                        }

                                                        if(discountServiceTiers.findIndex(function(tier){return tier.value == itemServiceTierID}) != -1){

                                                            return true;

                                                        }else{

                                                            return false;

                                                        }
                                                    })

                                                    let itemDiscount = 0;

                                                    let itemFlatDiscount = 0;

                                                    if(!NG.tools.isEmpty(itemDiscountsResults) && itemDiscountsResults.length > 0){

                                                        for(let i=0; i < itemDiscountsResults.length; i++){

                                                            let itemDiscountRate = itemDiscountsResults[i]['custrecord_ng_discount_rate'];

                                                            log.debug({title: 'Discount Percent/Flat Rate', details: itemDiscountRate});

                                                            if(itemDiscountRate.indexOf('%') != -1){

                                                                let itemDiscountPercent = Math.abs(parseFloat(itemDiscountRate));

                                                                itemDiscount = itemDiscount + itemDiscountPercent;

                                                            }else{

                                                                itemFlatDiscount = itemFlatDiscount + Number(itemDiscountRate);

                                                            }

                                                        }


                                                    }

                                                    taskRec.setText({
                                                        fieldId: 'custrecord_ng_cs_sess_task_discount',
                                                        text: Number(itemDiscount).toFixed(1) + '%'
                                                    })

                                                    taskRec.setText({
                                                        fieldId: 'custrecord_ng_cs_sess_task_flat_discnt',
                                                        text: itemFlatDiscount
                                                    });

                                                    //Get Venue Service Tier

                                                    let itemVenueServiceTiers = eventRecalcProcessData.venueServiceTiersData;

                                                    let itemVenueServiceTier = '';

                                                    let itemVenueServiceTierIndex = itemVenueServiceTiers.findIndex(function(venueTier){return venueTier['custrecord_ng_venue_item_svc_tier'][0].value == itemServiceTierID});

                                                    if(itemVenueServiceTierIndex != -1){

                                                        itemVenueServiceTier = itemVenueServiceTiers[itemVenueServiceTierIndex];

                                                    }

                                                    let serviceFeeVenueServiceTier = '';

                                                    let serviceFeeVenueServiceTierIndex = itemVenueServiceTiers.findIndex(function(venueTier){return venueTier['custrecord_ng_venue_pt_srv_fee_tier']});

                                                    if(serviceFeeVenueServiceTierIndex != -1){

                                                        serviceFeeVenueServiceTier = itemVenueServiceTiers[serviceFeeVenueServiceTierIndex];

                                                    }

                                                    let itemPriceLevelID = itemVenueServiceTier['custrecord_ng_venue_pt_price_level'][0].value;

                                                    log.debug({title: 'Item Price Level ID', details: itemPriceLevelID});

                                                    let itemCommissionRate = itemVenueServiceTier['custrecord_ng_venue_pt_commission_rate'];

                                                    log.debug({title: 'Item Commission Rate', details: itemCommissionRate});

                                                    let itemServiceFeeCommissionRate = serviceFeeVenueServiceTier['custrecord_ng_venue_pt_commission_rate'];

                                                    log.debug({title: 'Item Service Fee Commission Rate', details: itemServiceFeeCommissionRate});

                                                    let itemServiceFeeRate = itemVenueServiceTier["CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_service_fee"];

                                                    log.debug({title: 'Item Service Fee Rate', details: itemServiceFeeRate});

                                                    let discountThreshold = itemVenueServiceTier["CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_disc_threshold"];

                                                    log.debug({title: 'Venue Discount Threshold', details: discountThreshold});

                                                    let commissionDiscount = itemVenueServiceTier["CUSTRECORD_NG_VENUE_PT_PARENT_VENUE.custrecord_ng_cs_facility_comm_discount"];

                                                    log.debug({title: 'Venue Commission Discount', details: commissionDiscount});

                                                    if(!NG.tools.isEmpty(discountThreshold) && !NG.tools.isEmpty(itemDiscount) && !NG.tools.isEmpty(itemCommissionRate) && !NG.tools.isEmpty(commissionDiscount) && itemDiscount > parseFloat(discountThreshold)){

                                                        let discountDiff = itemDiscount - parseFloat(discountThreshold);

                                                        log.debug({titel: 'Discount % Over Threshold', details:  discountDiff});

                                                        itemCommissionRate = parseFloat(itemCommissionRate) - (discountDiff * (parseFloat(commissionDiscount)/100));

                                                        log.debug({title: 'Item Commission Rate after discount', details: itemCommissionRate});

                                                    }

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_task_pricelvl',
                                                        value: itemPriceLevelID
                                                    });

                                                    log.debug({title: 'Venue Currency ID', details: eventRecalcProcessData.eventVenueCurrencyID});

                                                    let priceLevelsSearchFilters = [
                                                        ["internalid","anyof",itemID]
                                                    ]

                                                    let multiCurrencyEnabled = runtime.isFeatureInEffect({
                                                        feature: 'MULTICURRENCY'
                                                    });

                                                    if(multiCurrencyEnabled){

                                                        priceLevelsSearchFilters.push("AND");

                                                        priceLevelsSearchFilters.push(["pricing.currency","anyof",eventRecalcProcessData.eventVenueCurrencyID]);

                                                    }

                                                    let priceLevelsSearchColumns = [
                                                        search.createColumn({
                                                            name: "itemid",
                                                            sort: search.Sort.ASC,
                                                            label: "Name"
                                                        }),
                                                        search.createColumn({name: "taxschedule", label: "Tax Schedule"}),
                                                        search.createColumn({name: "custitem_ng_cs_is_subrental_item", label: "Is Sub-Rental Item"}),
                                                        search.createColumn({name: "costestimate", label: "Item Defined Cost"}),
                                                        search.createColumn({name: "type", label: "Type"}),
                                                        search.createColumn({name: "baseprice", label: "Base Price"}),
                                                        search.createColumn({
                                                            name: "pricelevel",
                                                            join: "pricing",
                                                            label: "Price Level"
                                                        }),
                                                        search.createColumn({
                                                            name: "unitprice",
                                                            join: "pricing",
                                                            label: "Unit Price"
                                                        }),
                                                        search.createColumn({
                                                            name: "internalid",
                                                            join: "pricing",
                                                            label: "Internal ID"
                                                        })
                                                    ]

                                                    let priceLevelsSearchResults = NG.tools.getSearchResultsAdv({
                                                        type: 'item',
                                                        filters: priceLevelsSearchFilters,
                                                        columns: priceLevelsSearchColumns
                                                    });

                                                    if(!NG.tools.isEmpty(priceLevelsSearchResults) && priceLevelsSearchResults.length > 0){

                                                        let filteredPriceLevelResult = priceLevelsSearchResults.filter(function(result){return result.getValue({name: 'internalid', join: 'pricing'}) == itemPriceLevelID});

                                                        if(!NG.tools.isEmpty(filteredPriceLevelResult) && filteredPriceLevelResult.length > 0){

                                                            if(eventRecalcProcessData.eventCustomerIsTaxable){

                                                                let itemTaxScheduleID = filteredPriceLevelResult[0].getValue({name: 'taxschedule'});

                                                                let taxSchedDataIndex = taxSchedulesData.findIndex(function(data){return data.id == itemTaxScheduleID});

                                                                let taxScheduleRec = '';

                                                                if(taxSchedDataIndex == -1){

                                                                    taxScheduleRec = record.load({
                                                                        type: 'taxschedule',
                                                                        id: itemTaxScheduleID,
                                                                        isDynamic: true
                                                                    });

                                                                    let taxScheduleData = {};

                                                                    taxScheduleData.rec = taxScheduleRec;

                                                                    taxSchedulesData.push(taxScheduleData);

                                                                }else{

                                                                    taxScheduleRec = taxSchedulesData[taxSchedDataIndex].rec;

                                                                }

                                                                if(eventRecalcProcessData.eventTaxNexusCountry == 'US'){

                                                                    let lineCount = taxScheduleRec.getLineCount({sublistId: 'usnexuses'});

                                                                    for(let i=0; i < lineCount; i++){

                                                                        let nexusText = taxScheduleRec.getSublistText({sublistId: 'usnexuses', line: i, fieldId: 'nexus'});

                                                                        if(nexusText == eventRecalcProcessData.eventTaxState){

                                                                            isTaxable = taxScheduleRec.getSublistValue({sublistId: 'usnexuses', line: i, fieldId: 'taxable'});

                                                                            break;
                                                                        }
                                                                    }

                                                                }else{

                                                                    let lineCount = taxScheduleRec.getLineCount({sublistId: 'nexuses'});

                                                                    for(let i=0; i < lineCount; i++){

                                                                        let nexusCountry = taxScheduleRec.getSublistValue({sublistId: 'nexuses', line: i, fieldId: 'nexuscountry'});

                                                                        if(nexusCountry == eventRecalcProcessData.eventTaxNexusCountry){

                                                                            let nexusValue = taxScheduleRec.getSublistValue({sublistId: 'nexuses', line: i, fieldId: 'nexus'});

                                                                            let nexusDescription = '';

                                                                            let nexusesSearchFilters = [
                                                                                ["isinactive","is","F"],
                                                                                "AND",
                                                                                ["internalid","anyof", nexusValue]
                                                                            ]

                                                                            let nexusesSearchColumns = [
                                                                                search.createColumn({name: "internalid", label: "Internal ID"}),
                                                                                search.createColumn({
                                                                                    name: "country",
                                                                                    sort: search.Sort.ASC,
                                                                                    label: "Country"
                                                                                }),
                                                                                search.createColumn({name: "state", label: "State/Province/County"}),
                                                                                search.createColumn({name: "description", label: "Description"})
                                                                            ]

                                                                            let taxNexusResults = NG.tools.getSearchResults('nexus', nexusesSearchFilters, nexusesSearchColumns);

                                                                            if(!NG.tools.isEmpty(taxNexusResults) && taxNexusResults.length > 0){

                                                                                nexusDescription = taxNexusResults[0].getValue({name: 'description'});

                                                                            }

                                                                            log.debug({title: 'Nexus Description', details: nexusDescription});
                                                                            log.debug({title: 'Tax State', details: eventRecalcProcessData.eventTaxState});


                                                                            if(nexusDescription == eventRecalcProcessData.eventTaxState){

                                                                                let salesTaxCode = taxScheduleRec.getSublistValue({sublistId: 'nexuses', line: i, fieldId: 'salestaxcode'});

                                                                                if(!NG.tools.isEmpty(salesTaxCode)){

                                                                                    isTaxable = true;

                                                                                }

                                                                                break;
                                                                            }

                                                                        }else{

                                                                            continue;

                                                                        }


                                                                    }

                                                                }


                                                            }

                                                            //Next Step Here

                                                            let itemPrice = filteredPriceLevelResult[0].getValue({name: 'unitprice', join: 'pricing'});

                                                            let itemDefinedCost = filteredPriceLevelResult[0].getValue({name: 'costestimate'});

                                                            let isSubRentalItem = filteredPriceLevelResult[0].getValue({name: 'custitem_ng_cs_is_subrental_item'});

                                                            log.debug({title: 'Item Price', details: itemPrice});

                                                            log.debug({title: 'Item Defined Cost', details: itemDefinedCost});

                                                            log.debug({title: 'Is Sub-rental Item', details: isSubRentalItem});

                                                            log.debug({title:'Is Taxable', details: isTaxable});

                                                            if(isTaxable == 'Yes' || isTaxable == 'T' || isTaxable == true){
                                                                isTaxable = true;
                                                            }else{
                                                                isTaxable = false;
                                                            }

                                                            taskRec.setValue({
                                                                fieldId: 'custrecord_ng_cs_sess_task_is_taxable',
                                                                value: isTaxable
                                                            });

                                                            taskRec.setValue({
                                                                fieldId: 'custrecord_ng_cs_sess_task_rate',
                                                                value: itemPrice
                                                            });

                                                            taskRec.setValue({
                                                                fieldId: 'custrecord_ng_cs_sess_task_default_rate',
                                                                value: itemPrice
                                                            });

                                                        }

                                                    }

                                                    //Item Commission Rate

                                                    if(!NG.tools.isEmpty(itemCommissionRate)){

                                                        log.debug({title: 'Item Commission Rate', details: itemCommissionRate});

                                                        itemCommissionRate = parseFloat(itemCommissionRate).toFixed(1) + '%';

                                                        log.debug({title: 'Item Commission Rate', details: itemCommissionRate});

                                                        taskRec.setText({
                                                            fieldId: 'custrecord_ng_cs_sess_task_com_rate',
                                                            text: itemCommissionRate
                                                        });

                                                    }else{

                                                        taskRec.setText({
                                                            fieldId: 'custrecord_ng_cs_sess_task_com_rate',
                                                            text: ''
                                                        });

                                                    }

                                                    //Run Calcs Here

                                                    let csSessionDates = taskRec.getValue({fieldId: 'custrecord_ng_cs_session_task_days'});

                                                    let lineQty = taskRec.getValue({fieldId: 'custrecord_ng_cs_sess_task_qty'});

                                                    let lineHours = taskRec.getValue({fieldId: 'custrecord_ng_cs_sess_task_hours'});

                                                    let lineOTHours = taskRec.getValue({fieldId: 'custrecord_ng_cs_sess_task_hours_ot'});

                                                    let lineDTHours = taskRec.getValue({fieldId: 'custrecord_ng_cs_sess_task_hours_dt'});

                                                    let lineRate = taskRec.getValue({fieldId: 'custrecord_ng_cs_sess_task_rate'});

                                                    let linePrice = Number(lineQty) * Number(lineHours) * csSessionDates.length * Number(lineRate);

                                                    linePrice = linePrice + Number(lineQty) * Number(lineOTHours) * (Number(lineRate) * 1.5) * csSessionDates.length;

                                                    linePrice = linePrice + Number(lineQty) * Number(lineDTHours) * (Number(lineRate) * 2) * csSessionDates.length;

                                                    log.debug({title: 'Line Dates', details: csSessionDates});
                                                    log.debug({title: 'Line Qty', details: lineQty});
                                                    log.debug({title: 'Line Hours', details: lineHours});
                                                    log.debug({title: 'Line OT Hours', details: lineOTHours});
                                                    log.debug({title: 'Line DT Hours', details: lineDTHours});
                                                    log.debug({title: 'Line Rate', details: lineRate});
                                                    log.debug({title: 'Line Price', details: linePrice});

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_task_price',
                                                        value: linePrice
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_price_deci',
                                                        value: Math.round(linePrice*100000)/100000
                                                    });

                                                    let lineDiscountRate = taskRec.getValue({fieldId: 'custrecord_ng_cs_sess_task_discount'});

                                                    let lineTaxRate = taskRec.getValue({fieldId: 'custrecord_ng_cs_sess_task_taxrate'});

                                                    let lineTaxRatePST = taskRec.getValue({fieldId: 'custrecord_ng_cs_sess_task_taxrate_pst'});

                                                    let lineIsTaxable = taskRec.getValue({fieldId: 'custrecord_ng_cs_sess_task_is_taxable'});

                                                    let lineCommissionRate = taskRec.getValue({fieldId: 'custrecord_ng_cs_sess_task_com_rate'});

                                                    log.debug({title: 'Line Discount Rate', details: lineDiscountRate});
                                                    log.debug({title: 'Line Tax Rate', details: lineTaxRate});
                                                    log.debug({title: 'Line Tax Rate PST', details: lineTaxRatePST});
                                                    log.debug({title: 'Line Is Taxable', details: lineIsTaxable})
                                                    log.debug({title: 'Line Commission Rate', details: lineCommissionRate});

                                                    if(!NG.tools.isEmpty(lineDiscountRate)){

                                                        lineDiscountRate = Number(lineDiscountRate)/100;

                                                    }else{

                                                        lineDiscountRate = 0;

                                                    }

                                                    if(!NG.tools.isEmpty(lineCommissionRate)){

                                                        lineCommissionRate = Number(lineCommissionRate)/100;

                                                    }else{

                                                        lineCommissionRate = 0;

                                                    }

                                                    let lineDiscount = (linePrice) * lineDiscountRate;

                                                    let lineSubTotal = linePrice;

                                                    if((!NG.tools.isEmpty(lineTaxRate) || !NG.tools.isEmpty(lineTaxRatePST)) && lineIsTaxable){

                                                        if(!NG.tools.isEmpty(lineTaxRate)){

                                                            lineTaxRate = Number(lineTaxRate)/100;

                                                        }else{

                                                            lineTaxRate = 0;
                                                        }

                                                        if(!NG.tools.isEmpty(lineTaxRatePST)){

                                                            lineTaxRatePST = Number(lineTaxRatePST)/100;

                                                        }else{

                                                            lineTaxRatePST = 0;
                                                        }



                                                    }else{

                                                        lineTaxRate = 0;

                                                        lineTaxRatePST = 0

                                                    }

                                                    lineSubTotal = lineSubTotal - lineDiscount;

                                                    let lineCommissionAmount = Math.round((lineSubTotal * lineCommissionRate)*100)/100;

                                                    let lineTaxAmount = Math.round((linePrice * lineTaxRate)*100)/100;

                                                    let lineTaxAmountPST = Math.round((linePrice * lineTaxRatePST)*100)/100;

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_task_subtotal',
                                                        value: lineSubTotal
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_task_sub_total_deci',
                                                        value: Math.round(lineSubTotal*100000)/100000
                                                    });

                                                    let lineItemTotalAmount = linePrice-lineDiscount+lineTaxAmount+lineTaxAmountPST;

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_task_discount_amt',
                                                        value: lineDiscount
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_task_disc_amt_deci',
                                                        value: Math.round(lineDiscount*100000)/100000
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_task_tax_amt',
                                                        value: lineTaxAmount
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_task_tax_amt_deci',
                                                        value: Math.round(lineTaxAmount*100000)/100000
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_task_tax_amt_pst',
                                                        value: lineTaxAmountPST
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_task_tax_amt_pst_deci',
                                                        value: Math.round(lineTaxAmountPST*100000)/100000
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_task_amount',
                                                        value: lineItemTotalAmount
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_task_amt_deci',
                                                        value: Math.round(lineItemTotalAmount*100000)/100000
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_cs_sess_task_com_amount',
                                                        value: lineCommissionAmount
                                                    });

                                                    taskRec.setValue({
                                                        fieldId: 'custrecord_ng_comm_amt_deci',
                                                        value: Math.round(lineCommissionAmount*100000)/100000
                                                    });

                                                    taskRec.save({
                                                        ignoreMandatoryFields: true,
                                                        enableSourcing: true
                                                    });

                                                    taskDataObj.processed = true;

                                                    sessionDataObj.tasksData[i] = taskDataObj;

                                                    sessionDataObj.taskCount = sessionDataObj.taskCount + 1;

                                                    remainingUnits = scriptObj.getRemainingUsage()
                                                    log.debug('Remaining governance units: ' + remainingUnits);

                                                    if(remainingUnits <= 100 && ((i+1) < sessionDataObj.tasksData.length)){

                                                        callAgain = true;

                                                        break;

                                                    }else if (sessionDataObj.taskCount >= sessionDataObj.tasksData.length) {

                                                        callAgain = false;

                                                        break;
                                                    }

                                                }else{

                                                    sessionDataObj.taskCount = sessionDataObj.taskCount + 1;

                                                    continue;

                                                }

                                            }catch(err){

                                                sessionDataObj.taskCount = sessionDataObj.taskCount + 1;

                                                log.error({title: 'Error Task Record Recalc', details: JSON.stringify(err)});

                                                sessionDataObj.tasksData[i].error = err.message;

                                            }

                                        }


                                        eventRecalcProcessData.sessions[i] = sessionDataObj;

                                        log.debug({title: 'Session Tasks Processed', details: JSON.stringify(sessionDataObj)});

                                        if(callAgain){

                                            break;

                                        }

                                        if(remainingUnits <= 100 && ((i+1) < eventRecalcProcessData.sessions.length)){

                                            callAgain = true;

                                            break;

                                        }
                                    }

                                    sessionDataObj.processed = true;

                                    eventRecalcProcessData.sessions[i] = sessionDataObj;

                                    sessionCount = sessionCount + 1;

                                }

                            }catch(err){

                                sessionCount = sessionCount + 1;

                                log.error({title: 'Error with Session Recalc', details: JSON.stringify(err)});

                                eventRecalcProcessData.sessions[i].error = err.message;


                            }

                            log.debug('TP2');

                            remainingUnits = scriptObj.getRemainingUsage()
                            log.debug('Remaining governance units: ' + remainingUnits);

                            if(remainingUnits < 100 || sessionCount >= eventRecalcProcessData.sessions.length){

                                break;

                            }

                        }

                        try{

                            log.debug({title: 'Call Again', details: callAgain});

                            log.debug({title: 'Event Recalc Process Data', details: JSON.stringify(eventRecalcProcessData)});

                            if(callAgain){

                                var data = {};
                                data['eventRecalcProcessData'] = JSON.stringify(eventRecalcProcessData);
                                data['sessionCount'] = sessionCount;

                                var scriptURL = url.resolveScript({
                                    scriptId: 'customscript_ng_cs_sl_evnt_calc_dat_proc',
                                    deploymentId: 'customdeploy_ng_cs_sl_evnt_calc_dat_proc',
                                    returnExternalUrl: true
                                });

                                log.debug({title: 'Script URL', details: scriptURL});


                                var scriptResponse = https.post({
                                    url: scriptURL,
                                    body:data,
                                    headers: null
                                });

                                if(scriptResponse.code == 200){
                                    var responseBody = JSON.parse(scriptResponse.body);

                                    if(responseBody.eventRecalcProcessData){

                                        eventRecalcProcessData = responseBody.eventRecalcProcessData;

                                    }
                                }else{

                                    throw {message: scriptResponse.body};

                                }
                            }


                        }catch(err){

                            log.error({title: 'Error Calling Suitelet Again', details: err.message});

                            var responseObj = {};
                            responseObj.success = false;
                            responseObj.error = err.message;
                            responseObj.eventRecalcProcessData = eventRecalcProcessData;

                            scriptContext.response.write(JSON.stringify(responseObj));

                            return;
                        }

                        log.debug({title: 'Event Recalc Data', details: JSON.stringify(eventRecalcProcessData)});

                        var responseObj = {};
                        responseObj.success = true;
                        responseObj.eventRecalcProcessData = eventRecalcProcessData;

                        scriptContext.response.write(JSON.stringify(responseObj));

                    }

                }

            }catch(err){

                var responseObj = {};
                responseObj.success = false;
                responseObj.error = err.message;

                scriptContext.response.write(JSON.stringify(responseObj));

                return;

            }



        }

        return {onRequest}

    });
