/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NModuleScope SameAccount
 * @NAMDConfig ./amdSuiteletConfig.json
 * 
 * Module Description
 * Booth Checklist Report - generates booth checklist reports by booth or by event
 * 
 * Version    Date            Author           Remarks
 * 1.00       Current Date    Refactored from 1.0 script
 */

define(['N/ui/serverWidget', 'N/search', 'N/record', 'N/url', 'N/format', 'N/render', 'N/file', 'N/runtime', 'settings'], 

 /**
  * @param {serverWidget} serverWidget
  * @param {search} search
  * @param {record} record
  * @param {url} url
  * @param {format} format
  * @param {render} render
  * @param {file} file
  * @param {runtime} runtime
  * @param {Object} settings
  * @param {() => Object} settings.useSettings
  * */   
function(serverWidget, search, record, url, format, render, file, runtime, settings) {

    /**
     * Definition of the Suitelet script trigger point.
     *
     * @param {Object} context
     * @param {ServerRequest} context.request - Incoming request
     * @param {ServerResponse} context.response - Suitelet response
     */
    function onRequest(context) {
        const request = context.request;
        const response = context.response;
        
        log.debug('🚀 SUITELET', 'Booth Checklist Report started');
        
        const doPrint = request.parameters.prt === 'T';
        const opType = request.parameters.opt;
        
        if (request.method === 'GET') {
            log.debug('🔍 REQUEST', `Method: GET, Print: ${doPrint}, Operation: ${opType}`);
            if (doPrint) {
                switch (opType) {
                    case 'booth':
                        log.debug('📊 PROCESS', 'Starting booth report generation');
                        doWorkBooth(request, response);
                        break;
                    case 'show-management':
                        doWorkEvent(request, response);
                        break;
                    default:
                        log.info({
                            title: 'INVALID OPERATION TYPE',
                            details: `Received: ${opType || '**EMPTY**'}`
                        });
                        doWorkClose(request, response);
                        break;
                }
            } else {
                displayForm(request, response);
            }
        } else {
            response.write('INVALID_HTTP_REQUEST');
        }
    }

    /**
     * Helper function to get all search results using paged search
     */
    const getAllResultsFor = (searchObj, callback) => {
        let myPagedData = searchObj.runPaged();
        myPagedData.pageRanges.forEach(function (pageRange) {
          let myPage = myPagedData.fetch({ index: pageRange.index });
          myPage.data.forEach(function (result) {
            callback(result);
          });
        });
      };

    /**
     * Displays the main form for the report
     */
    function displayForm(request, response) {
        log.debug('📋 FORM', 'Initializing report form');
        
        const csSettings = settings.useSettings();

        // Create form
        const form = serverWidget.createForm({
            title: 'Booth Checklist Report',
            hideNavBar: false
        });
        
        // Add client script
        form.clientScriptModulePath = '../cs-client/ng_cs_slcs_booth_checklist_report.js';
        
        // Create field groups similar to original script
        form.addFieldGroup({
            id: 'primary_group',
            label: 'Report Options'
        });
        
        form.addFieldGroup({
            id: 'booth_group',
            label: 'Booth Order'
        });
        
        form.addFieldGroup({
            id: 'event_group',
            label: 'Event Management'
        });
        
        // Add show selection field
        const showSelect = form.addField({
            id: 'custpage_show',
            type: serverWidget.FieldType.MULTISELECT,
            label: 'Show',
            container: 'primary_group'
        });
        
        showSelect.isMandatory = true;  
        
        // Populate show dropdown with open shows
        getOpenShows(showSelect, false);
        
        // Add Item Subcategory field
        const subCatField = form.addField({
            id: 'custpage_sub_category',
            type: serverWidget.FieldType.MULTISELECT,
            label: 'Item Subcategory',
            container: 'primary_group'
        });
        
        subCatField.updateBreakType({
            breakType: serverWidget.FieldBreakType.STARTCOL
        });
        
        // Populate subcategories
        const subCatList = data_GetSubcategories();
        if (subCatList && subCatList.length > 0) {
            for (let i = 0; i < subCatList.length; i++) {
                subCatField.addSelectOption({
                    value: subCatList[i].id,
                    text: subCatList[i].value
                });
            }
        }
        
        // Add report type as radio buttons
        form.addField({
            id: 'custpage_func_label',
            type: serverWidget.FieldType.LABEL,
            label: 'Report Type',
            container: 'primary_group'
        }).updateBreakType({
            breakType: serverWidget.FieldBreakType.STARTCOL
        });
        
        // Add radio buttons for report type
        form.addField({
            id: 'custpage_func_type',
            type: serverWidget.FieldType.RADIO,
            label: ' Booth Order',
            source: 'booth',
            container: 'primary_group'
        });
        
        form.addField({
            id: 'custpage_func_type',
            type: serverWidget.FieldType.RADIO,
            label: ' Event Management',
            source: 'show-management',
            container: 'primary_group'
        });
        
        // Add checkbox for printing item descriptions
        const itemDescField = form.addField({
            id: 'custpage_display_item_desc',
            type: serverWidget.FieldType.CHECKBOX,
            label: ' Print Item Descriptions',
            container: 'primary_group'
        });
        
        itemDescField.updateBreakType({
            breakType: serverWidget.FieldBreakType.STARTCOL
        });
        
        // Add sort by dropdown
        const bSortField = form.addField({
            id: 'custpage_booth_sort',
            type: serverWidget.FieldType.SELECT,
            label: 'Sort By',
            container: 'booth_group'
        });
        
        bSortField.addSelectOption({
            value: 'A',
            text: 'Booth Number'
        });
        
        bSortField.addSelectOption({
            value: 'B',
            text: 'Exhibitor Name'
        });
        
        // Add Event Management specific fields
        // Add Order Types field
        const orderTypesField = form.addField({
            id: 'custpage_order_types',
            type: serverWidget.FieldType.MULTISELECT,
            label: 'Order Types',
            container: 'event_group'
        });
        
        // Get order types from settings
        const orderTypes = getShowManagementOrderTypes(csSettings);

        log.debug('🔍 ORDER TYPES', `Found ${orderTypes.length} order types`);

        for (let i = 0; i < orderTypes.length; i++) {
            orderTypesField.addSelectOption({
                value: orderTypes[i].id,
                text: orderTypes[i].name
            });
        }
        
        // Add Sort & Organize By field
        const sortOrganizeField = form.addField({
            id: 'custpage_event_sort',
            type: serverWidget.FieldType.SELECT,
            label: 'Sort & Organize By',
            container: 'event_group'
        });

        
        // Add sort options
        sortOrganizeField.addSelectOption({
            value: '',
            text: ''
        });
        
        sortOrganizeField.addSelectOption({
            value: 'booth',
            text: 'Booth Number'
        });
        
        sortOrganizeField.addSelectOption({
            value: 'exhibitor',
            text: 'Exhibitor Name'
        });
        
        sortOrganizeField.addSelectOption({
            value: 'order_type',
            text: 'Order Type'
        });
        
        // Initially hide Event Management fields if Booth Order is selected by default
        if (!request.parameters.custpage_func_type || request.parameters.custpage_func_type === 'A') {
            orderTypesField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.NODISPLAY
            });
            
            sortOrganizeField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.NODISPLAY
            });
        }
        
        // Add hidden field for operation type
        form.addField({
            id: 'custpage_opt',
            type: serverWidget.FieldType.TEXT,
            label: 'Operation Type'
        }).updateDisplayType({
            displayType: serverWidget.FieldDisplayType.HIDDEN
        });
        
        // Add hidden field for print flag
        form.addField({
            id: 'custpage_prt',
            type: serverWidget.FieldType.TEXT,
            label: 'Print Flag'
        }).updateDisplayType({
            displayType: serverWidget.FieldDisplayType.HIDDEN
        });
        
        // Add buttons
        form.addButton({
            id: 'custpage_print',
            label: 'Print',
            functionName: 'printPage'
        });
        
        form.addButton({
            id: 'custpage_reset',
            label: 'Reset',
            functionName: 'resetPage'
        });
        
        response.writePage(form);
    }

    /**
     * Get order types from settings
     */
    function getShowManagementOrderTypes(csSettings) {
        log.debug('⚙️ CONFIG', {
            meesage: 'Getting show management order types',
            defaultExhibitorOrderType: csSettings.custrecord_ng_cs_def_exhb_ord_type,
        });
        
        const orderTypes = [];
        const filters = [
            ["isinactive", "is", "F"],
            "and",
            ["internalid", "noneof", [csSettings.custrecord_ng_cs_def_exhb_ord_type]],
        ]
        const cols = [
            search.createColumn({
                name: "name",
            })
        ]

        try {
            const searchObj = search.create({
                type: "customrecord_ng_cs_order_type",
                filters: filters,
                columns: cols,
            });
            
            getAllResultsFor(searchObj, function(result) {
                orderTypes.push({
                    id: result.id,
                    name: result.getValue({name: "name"}),
                });
            });
        } catch (err) {
            log.error({
                title: "ERROR_GETTING_ORDER_TYPES",
                details: err.message,
            });
        }
        return orderTypes;
    }

    /**
     * Generates booth checklist report by booth
     */
    function doWorkBooth(request, response) {
        log.debug('🔄 BOOTH', 'Processing booth report');
        
        // Get parameters
        const showIds = JSON.parse(request.parameters.custpage_show || '[]');
        const printItemDesc = request.parameters.custpage_display_item_desc === 'T';
        const subcategories = request.parameters.custpage_sub_category ? 
            request.parameters.custpage_sub_category.split("\u0005") : [];
        
        // Prepare data
        const data = {};
        const eventData = getEventsData(showIds);
        
        if (!eventData) {
            log.error({
                title: 'SHOW_NOT_FOUND',
                details: `Show ID: ${showIds[0]}`
            });
            throw new Error('Show not found');
        }
        
        data[showIds[0]] = eventData;
        
        // Get booth data
        const booths = data_GetBooths(boothIds);
        if (!booths || Object.keys(booths).length === 0) {
            log.error({
                title: 'NO_BOOTHS_FOUND',
                details: `Booth IDs: ${boothIds.join(', ')}`
            });
            throw new Error('No booths found');
        }
        
        // Get table data
        const displayData = data_GetShowTables(data);
        
        // Get order data
        const itemData = data_GetBoothItems(boothIds, boothIds[0], subcategories);
        
        // Build PDF
        let xml = '<?xml version="1.0"?>';
        xml += '<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">';
        xml += '<pdfset>';
        
        // Create a PDF for each booth
        for (const boothId of boothIds) {
            if (booths[boothId]) {
                const boothItems = itemData.filter(item => item.booth_id === boothId);
                
                const header = xml_PageHeader(displayData[boothId], 'Booth Checklist');
                const boothInfo = xml_BoothInfo(booths[boothId]);
                const boothItems_xml = xml_BoothItems(boothItems, printItemDesc);
                
                xml += '<pdf>' + header + boothInfo + boothItems_xml + '</pdf>';
            }
        }
        
        xml += '</pdfset>';
        
        // Render PDF
        const pdfFile = render.xmlToPdf({
            xmlString: xml
        });
        
        // Send PDF response
        response.setContentType('PDF', 'boothChecklist.pdf');
        response.write(pdfFile.getValue());
    }

    /**
     * Generates booth checklist report by event
     */
    function doWorkEvent(request, response) {
        log.debug('🔄 EVENT', 'Processing event report');
        
        // Get parameters
        const showId = request.parameters.sid;
        const sortType = request.parameters.custpage_booth_sort || 'A';
        const printItemDesc = request.parameters.custpage_display_item_desc === 'T';
        const subcategories = request.parameters.custpage_sub_category ? 
            request.parameters.custpage_sub_category.split("\u0005") : [];
        const orderTypes = request.parameters.custpage_order_types ?
            request.parameters.custpage_order_types.split("\u0005") : [];
        const sortOrganize = request.parameters.custpage_sort_organize || '';
        
        // Prepare data
        let showObjList = [];
        const showObj = data_GetShow(showId);
        
        if (!showObj) {
            log.error({
                title: 'SHOW_NOT_FOUND',
                details: `Show ID: ${showId}`
            });
            throw new Error('Show not found');
        }
        
        showObjList.push(showObj);
        
        // Get table data and dates
        const orderObjList = data_GetShowItems(showId, subcategories);
        
        // Filter by order types if specified
        const filteredOrderList = orderTypes && orderTypes.length > 0 
            ? orderObjList.filter(item => {
                const itemOrderType = item.order_type || 'internal'; // Default to internal if not specified
                return orderTypes.includes(itemOrderType);
              })
            : orderObjList;
        
        // Build PDF
        let xml = '<?xml version="1.0"?>';
        xml += '<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">';
        xml += '<pdfset>';
        
        for (let s = 0; s < showObjList.length; s++) {
            const sObj = showObjList[s];
            const itemData = filteredOrderList.filter(x => x.table_id === sObj.table_id);
            
            // Group and sort based on sortOrganize parameter
            const organizedData = organizeEventData(itemData, sortOrganize);
            
            const displayData = {};
            displayData[sObj.id] = sObj;
            const tableData = data_GetShowDates(data_GetShowTables(displayData))[sObj.id];
            
            const header = xml_PageHeader(tableData, 'Booth Checklist');
            let body = '';
            
            if (sortOrganize === 'otype') {
                // Organize by order type
                for (const orderType in organizedData) {
                    body += `<h2>Order Type: ${orderType}</h2>`;
                    
                    for (let b = 0; b < organizedData[orderType].length; b++) {
                        const boothData = organizedData[orderType][b];
                        body += xml_BoothInfo({
                            id: boothData.id,
                            name: boothData.exhib_name
                        });
                        body += xml_BoothItems(boothData.items, printItemDesc);
                        
                        // Add page break if not the last booth
                        if (b < organizedData[orderType].length - 1) {
                            body += '<pbr />';
                        }
                    }
                    
                    // Add page break between order types
                    if (Object.keys(organizedData).indexOf(orderType) < Object.keys(organizedData).length - 1) {
                        body += '<pbr />';
                    }
                }
            } else {
                // Default organization (by booth or exhibitor)
                for (let b = 0; b < organizedData.length; b++) {
                    const boothData = organizedData[b];
                    body += xml_BoothInfo({
                        id: boothData.id,
                        name: boothData.exhib_name
                    });
                    body += xml_BoothItems(boothData.items, printItemDesc);
                    
                    // Add page break if not the last booth
                    if (b < organizedData.length - 1) {
                        body += '<pbr />';
                    }
                }
            }
            
            xml += '<pdf>' + header + body + '</pdf>';
        }
        
        xml += '</pdfset>';
        
        // Render PDF
        const pdfFile = render.xmlToPdf({
            xmlString: xml
        });
        
        // Send PDF response
        response.setContentType('PDF', 'eventChecklist.pdf');
        response.write(pdfFile.getValue());
    }

    /**
     * Handles closing the window
     */
    function doWorkClose(request, response) {
        log.debug('🚪 CLOSE', 'Closing report');
        const html = '<html><body><script>window.close();</script></body></html>';
        response.write(html);
    }

    /**
     * Gets open shows for dropdown
     */
    function getOpenShows(showsField, isMulti) {
        if (!isMulti) {
            showsField.addSelectOption({
                value: '',
                text: '',
                isSelected: true
            });
        }
        
        // Search for open shows
        const showSearch = search.create({
            type: 'customrecord_show',
            filters: [
                ['isinactive', 'is', 'F'],
                'AND',
                ['custrecord_cs_st_show_complete', 'anyof', ['@NONE@', '2']]
            ],
            columns: [
                {name: 'name', sort: search.Sort.ASC},
                {name: 'custrecord_show_type'}
            ]
        });
        
        const resultSet = showSearch.run();
        const results = resultSet.getRange({
            start: 0,
            end: 1000
        });
        
        // Populate dropdown
        if (results && results.length > 0) {
            for (let i = 0; i < results.length; i++) {
                const id = results[i].id;
                const name = results[i].getValue({name: 'name'});
                
                showsField.addSelectOption({
                    value: id,
                    text: name
                });
            }
        }
    }

    /**
     * Gets event data by IDs
     */
    function getEventsData(showIds) {
        log.debug('📊 DATA', `Getting show data for IDs: ${showIds}`);
        const eventResults = [];
        
        const eventSearch = search.create({
            type: 'customrecord_show',
            filters: [
                ['internalid', 'anyof', showIds],
                'AND',
                ['isinactive', 'is', 'F']
            ],
            columns: [
               search.createColumn({name: 'name'}),
               search.createColumn({name: 'custrecord_facility'}),
            ]
        });

        getAllResultsFor(eventSearch, (result) => {
            eventResults.push({
                id: result.id,
                name: result.getValue({name: 'name'}),
                facility: {
                    id: result.getValue({name: 'custrecord_facility'}),
                    name: result.getText({name: 'custrecord_facility'})
                }
            });
        });
        
        return eventResults;
    }

    /**
     * Gets booth data by IDs
     */
    function data_GetBooths(boothIds) {
        log.debug('📊 DATA', `Getting booth data for IDs: ${JSON.stringify(boothIds)}`);
        
        const boothData = {};
        
        if (!boothIds || boothIds.length === 0) return boothData;
        
        // Search for booth records
        const boothSearch = search.create({
            type: 'customrecord_booth',
            filters: [
                ['internalid', 'anyof', boothIds],
                'AND',
                ['isinactive', 'is', 'F']
            ],
            columns: [
                {name: 'name'},
                {name: 'custrecord_booth_name'},
                {name: 'custrecord_booth_exhibitor'},
                {name: 'custrecord_booth_table'}
            ]
        });
        
        const resultSet = boothSearch.run();
        const results = resultSet.getRange({
            start: 0,
            end: 1000
        });
        
        // Process results
        if (results && results.length > 0) {
            for (let i = 0; i < results.length; i++) {
                const id = results[i].id;
                const name = results[i].getValue({name: 'name'});
                const boothName = results[i].getValue({name: 'custrecord_booth_name'});
                const exhibitorId = results[i].getValue({name: 'custrecord_booth_exhibitor'});
                const exhibitorName = results[i].getText({name: 'custrecord_booth_exhibitor'});
                
                boothData[id] = {
                    id: id,
                    name: name,
                    booth_name: boothName,
                    exhibitor_id: exhibitorId,
                    exhibitor_name: exhibitorName
                };
            }
        }
        
        return boothData;
    }

    /**
     * Gets show tables data
     */
    function data_GetShowTables(data) {
        log.debug('📊 DATA', 'Getting show tables data');
        
        if (!data) return null;
        
        // Get table IDs from shows
        const tableIds = [];
        for (const showId in data) {
            if (data[showId].table_id && !tableIds.includes(data[showId].table_id)) {
                tableIds.push(data[showId].table_id);
            }
        }
        
        if (tableIds.length === 0) return data;
        
        // Search for table records
        const tableSearch = search.create({
            type: 'customrecord_show_table',
            filters: [
                ['internalid', 'anyof', tableIds],
                'AND',
                ['isinactive', 'is', 'F']
            ],
            columns: [
                {name: 'name'},
                {name: 'custrecord_show_table_move_in_date'},
                {name: 'custrecord_show_table_move_out_date'},
                {name: 'custrecord_show_table_end_date'},
                {name: 'custrecord_show_table_start_date'}
            ]
        });
        
        const resultSet = tableSearch.run();
        const results = resultSet.getRange({
            start: 0,
            end: 1000
        });
        
        // Process results
        if (results && results.length > 0) {
            for (let i = 0; i < results.length; i++) {
                const tableId = results[i].id;
                const tableName = results[i].getValue({name: 'name'});
                const moveInDate = results[i].getValue({name: 'custrecord_show_table_move_in_date'});
                const moveOutDate = results[i].getValue({name: 'custrecord_show_table_move_out_date'});
                const startDate = results[i].getValue({name: 'custrecord_show_table_start_date'});
                const endDate = results[i].getValue({name: 'custrecord_show_table_end_date'});
                
                // Update show data with table information
                for (const showId in data) {
                    if (data[showId].table_id === tableId) {
                        data[showId].tableName = tableName;
                        data[showId].moveInDate = moveInDate;
                        data[showId].moveOutDate = moveOutDate;
                        data[showId].startDate = startDate;
                        data[showId].endDate = endDate;
                    }
                }
            }
        }
        
        return data;
    }

    /**
     * Gets show dates in formatted form
     */
    function data_GetShowDates(data) {
        log.debug('📊 DATA', 'Getting show dates data');
        
        if (!data) return null;
        
        for (const showId in data) {
            // Format dates
            if (data[showId].moveInDate) {
                data[showId].moveInDate_f = format.format({
                    value: new Date(data[showId].moveInDate),
                    type: format.Type.DATE
                });
            }
            
            if (data[showId].moveOutDate) {
                data[showId].moveOutDate_f = format.format({
                    value: new Date(data[showId].moveOutDate),
                    type: format.Type.DATE
                });
            }
            
            if (data[showId].startDate) {
                data[showId].startDate_f = format.format({
                    value: new Date(data[showId].startDate),
                    type: format.Type.DATE
                });
            }
            
            if (data[showId].endDate) {
                data[showId].endDate_f = format.format({
                    value: new Date(data[showId].endDate),
                    type: format.Type.DATE
                });
            }
        }
        
        return data;
    }

    /**
     * Gets item subcategories
     */
    function data_GetSubcategories() {
        log.debug('📊 SUBCATEGORIES', 'Getting subcategories data');
        
        const subcategories = [];
        
        try {
            // Search for item subcategories
            const subcatSearch = search.create({
                type: 'customrecord_subcategory',
                filters: [
                    ['isinactive', 'is', 'F']
                ],
                columns: [
                    {name: 'internalid'},
                    {name: 'custrecord_ng_cs_subcat_p_cat'},
                    {name: 'name', sort: search.Sort.ASC}
                ]
            });
            
            const resultSet = subcatSearch.run();
            const results = resultSet.getRange({
                start: 0,
                end: 1000
            });
            
            // Process results
            if (results && results.length > 0) {
                for (let i = 0; i < results.length; i++) {
                    subcategories.push({
                        id: results[i].getValue({name: 'internalid'}),
                        value: `${results[i].getText({name: 'custrecord_ng_cs_subcat_p_cat'})} : ${results[i].getValue({name: 'name'})}`
                    });
                }
            }
        } catch (e) {
            log.error({
                title: 'ERROR_GETTING_SUBCATEGORIES',
                details: e.message
            });
        }
        
        return subcategories;
    }

    /**
     * Gets items for booths
     */
    function data_GetBoothItems(boothIds, showId, subcategories) {
        log.debug('📊 ITEMS', `Getting booth items for booth IDs: ${JSON.stringify(boothIds)}, show ID: ${showId}`);
        
        const items = [];
        
        if (!boothIds || boothIds.length === 0 || !showId) return items;
        
        // Build filters
        const filters = [
            ['custrecord_bi_booth', 'anyof', boothIds],
            'AND',
            ['custrecord_bi_show', 'is', showId],
            'AND',
            ['isinactive', 'is', 'F']
        ];
        
        // Add subcategory filter if specified
        if (subcategories && subcategories.length > 0) {
            filters.push('AND');
            filters.push(['custrecord_bi_item.custitem_cseg_item_subcateg', 'anyof', subcategories]);
        }
        
        // Search for booth items
        const itemSearch = search.create({
            type: 'customrecord_booth_item',
            filters: filters,
            columns: [
                {name: 'custrecord_bi_booth'},
                {name: 'custrecord_bi_item'},
                {name: 'custrecord_bi_quantity'},
                {name: 'custrecord_bi_received_qty'},
                {name: 'custrecord_bi_notes'},
                {name: 'custrecord_bi_complete'},
                {name: 'purchasedescription', join: 'CUSTRECORD_BI_ITEM'},
                {name: 'custitem_cseg_item_subcateg', join: 'CUSTRECORD_BI_ITEM'}
            ]
        });
        
        const resultSet = itemSearch.run();
        let start = 0;
        let end = 1000;
        let hasMore = true;
        
        while (hasMore) {
            const results = resultSet.getRange({
                start: start,
                end: end
            });
            
            if (results && results.length > 0) {
                for (let i = 0; i < results.length; i++) {
                    const boothId = results[i].getValue({name: 'custrecord_bi_booth'});
                    const itemId = results[i].getValue({name: 'custrecord_bi_item'});
                    const itemName = results[i].getText({name: 'custrecord_bi_item'});
                    const quantity = results[i].getValue({name: 'custrecord_bi_quantity'});
                    const receivedQty = results[i].getValue({name: 'custrecord_bi_received_qty'});
                    const notes = results[i].getValue({name: 'custrecord_bi_notes'});
                    const isComplete = results[i].getValue({name: 'custrecord_bi_complete'}) === 'T';
                    const description = results[i].getValue({name: 'purchasedescription', join: 'CUSTRECORD_BI_ITEM'});
                    
                    items.push({
                        id: results[i].id,
                        booth_id: boothId,
                        item_id: itemId,
                        item_name: itemName,
                        item_desc: description || '',
                        quantity: quantity,
                        received: receivedQty,
                        notes: notes,
                        complete: isComplete
                    });
                }
                
                if (results.length < 1000) {
                    hasMore = false;
                } else {
                    start += 1000;
                    end += 1000;
                }
            } else {
                hasMore = false;
            }
        }
        
        return items;
    }

    /**
     * Gets all items for a show
     */
    function data_GetShowItems(showId, subcategories) {
        log.debug('📊 ITEMS', `Getting show items for show ID: ${showId}`);
        
        const items = [];
        
        if (!showId) return items;
        
        // Build filters
        const filters = [
            ['custrecord_bi_show', 'is', showId],
            'AND',
            ['isinactive', 'is', 'F']
        ];
        
        // Add subcategory filter if specified
        if (subcategories && subcategories.length > 0) {
            filters.push('AND');
            filters.push(['custrecord_bi_item.custitem_cseg_item_subcateg', 'anyof', subcategories]);
        }
        
        // Search for booth items with exhibitor information
        const itemSearch = search.create({
            type: 'customrecord_booth_item',
            filters: filters,
            columns: [
                {name: 'custrecord_bi_booth'},
                {name: 'custrecord_bi_item'},
                {name: 'custrecord_bi_quantity'},
                {name: 'custrecord_bi_received_qty'},
                {name: 'custrecord_bi_notes'},
                {name: 'custrecord_bi_complete'},
                {name: 'name', join: 'custrecord_bi_booth'},
                {name: 'custrecord_booth_name', join: 'custrecord_bi_booth'},
                {name: 'custrecord_booth_sort', join: 'custrecord_bi_booth'},
                {name: 'custrecord_booth_table', join: 'custrecord_bi_booth'},
                {name: 'custrecord_booth_exhibitor', join: 'custrecord_bi_booth'},
                {name: 'entityid', join: 'CUSTRECORD_BOOTH_EXHIBITOR'},
                {name: 'purchasedescription', join: 'CUSTRECORD_BI_ITEM'},
                {name: 'custitem_cseg_item_subcateg', join: 'CUSTRECORD_BI_ITEM'},
                {name: 'custrecord_bi_order_type'} // Order type field
            ]
        });
        
        const resultSet = itemSearch.run();
        let start = 0;
        let end = 1000;
        let hasMore = true;
        
        while (hasMore) {
            const results = resultSet.getRange({
                start: start,
                end: end
            });
            
            if (results && results.length > 0) {
                for (let i = 0; i < results.length; i++) {
                    const boothId = results[i].getValue({name: 'custrecord_bi_booth'});
                    const itemId = results[i].getValue({name: 'custrecord_bi_item'});
                    const itemName = results[i].getText({name: 'custrecord_bi_item'});
                    const quantity = results[i].getValue({name: 'custrecord_bi_quantity'});
                    const receivedQty = results[i].getValue({name: 'custrecord_bi_received_qty'});
                    const notes = results[i].getValue({name: 'custrecord_bi_notes'});
                    const isComplete = results[i].getValue({name: 'custrecord_bi_complete'}) === 'T';
                    const description = results[i].getValue({name: 'purchasedescription', join: 'CUSTRECORD_BI_ITEM'});
                    const orderType = results[i].getValue({name: 'custrecord_bi_order_type'}) || 'internal';
                    
                    const boothName = results[i].getValue({name: 'name', join: 'custrecord_bi_booth'});
                    const boothDisplayName = results[i].getValue({name: 'custrecord_booth_name', join: 'custrecord_bi_booth'});
                    const boothSort = results[i].getValue({name: 'custrecord_booth_sort', join: 'custrecord_bi_booth'});
                    const tableId = results[i].getValue({name: 'custrecord_booth_table', join: 'custrecord_bi_booth'});
                    const exhibitorId = results[i].getValue({name: 'custrecord_booth_exhibitor', join: 'custrecord_bi_booth'});
                    const exhibitorName = results[i].getValue({name: 'entityid', join: 'CUSTRECORD_BOOTH_EXHIBITOR'});
                    
                    items.push({
                        id: results[i].id,
                        booth_id: boothId,
                        booth_name: boothName,
                        booth_display: boothDisplayName,
                        booth_sort: boothSort,
                        exhib_id: exhibitorId,
                        exhib_name: exhibitorName,
                        table_id: tableId,
                        item_id: itemId,
                        item_name: itemName,
                        item_desc: description || '',
                        quantity: quantity,
                        received: receivedQty,
                        notes: notes,
                        complete: isComplete,
                        order_type: orderType
                    });
                }
                
                if (results.length < 1000) {
                    hasMore = false;
                } else {
                    start += 1000;
                    end += 1000;
                }
            } else {
                hasMore = false;
            }
        }
        
        return items;
    }

    /**
     * Organizes event data based on sort parameter
     */
    function organizeEventData(itemData, sortOrganize) {
        log.debug('🔄 ORGANIZE', `Organizing event data with sort method: ${sortOrganize}`);
        
        if (sortOrganize === 'otype') {
            // Group by order type
            const orderTypeGroups = {};
            
            // First, group items by booth within each order type
            for (let i = 0; i < itemData.length; i++) {
                const item = itemData[i];
                const orderType = item.order_type || 'internal'; // Default to internal if not specified
                const orderTypeName = getOrderTypeName(orderType);
                
                if (!orderTypeGroups[orderTypeName]) {
                    orderTypeGroups[orderTypeName] = {};
                }
                
                if (!orderTypeGroups[orderTypeName][item.booth_id]) {
                    orderTypeGroups[orderTypeName][item.booth_id] = {
                        id: item.booth_id,
                        exhib_name: item.exhib_name,
                        booth_sort: isNaN(Number(item.booth_sort)) ? item.booth_sort : Number(item.booth_sort),
                        items: []
                    };
                }
                
                orderTypeGroups[orderTypeName][item.booth_id].items.push(item);
            }
            
            // Convert booth objects to arrays and sort within each order type
            for (const orderType in orderTypeGroups) {
                const boothsArray = [];
                for (const boothId in orderTypeGroups[orderType]) {
                    boothsArray.push(orderTypeGroups[orderType][boothId]);
                }
                
                // Sort by exhibitor name within each order type
                boothsArray.sort((a, b) => {
                    return String(a.exhib_name).localeCompare(String(b.exhib_name));
                });
                
                orderTypeGroups[orderType] = boothsArray;
            }
            
            return orderTypeGroups;
        } else {
            // Group by booth
            const boothMappedItems = [];
            for (let i = 0; i < itemData.length; i++) {
                const boothId = itemData[i].booth_id;
                const index = boothMappedItems.findIndex(x => x.id === boothId);
                
                if (index < 0) {
                    boothMappedItems.push({
                        id: boothId,
                        exhib_name: itemData[i].exhib_name,
                        booth_sort: isNaN(Number(itemData[i].booth_sort)) ? itemData[i].booth_sort : Number(itemData[i].booth_sort),
                        items: []
                    });
                    
                    const newIndex = boothMappedItems.findIndex(x => x.id === boothId);
                    boothMappedItems[newIndex].items.push(itemData[i]);
                } else {
                    boothMappedItems[index].items.push(itemData[i]);
                }
            }
            
            // Sort based on parameter
            if (sortOrganize === 'booth' || sortOrganize === '') {
                // Sort by booth number
                boothMappedItems.sort((a, b) => {
                    if (typeof a.booth_sort === 'number' && typeof b.booth_sort === 'number') {
                        return a.booth_sort - b.booth_sort;
                    } else {
                        return String(a.booth_sort).localeCompare(String(b.booth_sort));
                    }
                });
            } else if (sortOrganize === 'exhib') {
                // Sort by exhibitor name
                boothMappedItems.sort((a, b) => {
                    return String(a.exhib_name).localeCompare(String(b.exhib_name));
                });
            }
            
            return boothMappedItems;
        }
    }
    
    /**
     * Gets a readable name for an order type
     */
    function getOrderTypeName(orderTypeId) {
        log.debug('📝 ORDER', `Getting order type name for ID: ${orderTypeId}`);
        
        const orderTypeMap = {
            'internal': 'Internal',
            'smallevent': 'Small Event',
            'thirdparty': 'Third Party'
        };
        
        return orderTypeMap[orderTypeId] || orderTypeId;
    }

    /**
     * Generates XML for page header
     */
    function xml_PageHeader(tableData, reportTitle) {
        log.debug('📄 PDF', `Generating PDF header for: ${reportTitle}`);
        
        let xml = '<head>';
        xml += '<style type="text/css">';
        xml += 'table { font-size: 9pt; table-layout: fixed; }';
        xml += 'th { font-weight: bold; font-size: 8pt; vertical-align: middle; padding: 5px 6px 3px; background-color: #e3e3e3; color: #333333; }';
        xml += 'td { padding: 4px 6px; }';
        xml += 'td p { align: left; }';
        xml += 'b { font-weight: bold; color: #333333; }';
        xml += '.header { background-color: #f7f7f7; color: #333333; font-size: 8pt; }';
        xml += '.total { background-color: #e3e3e3; font-weight: bold; }';
        xml += '.item-desc { font-size: 8pt; font-style: italic; padding-left: 10px; }';
        xml += '</style>';
        xml += '</head>';
        
        xml += '<body padding="0.5in 0.5in 0.5in 0.5in" size="Letter-LANDSCAPE">';
        xml += '<table class="header" style="width: 100%;">';
        xml += '<tr><td colspan="2" align="center"><b style="font-size: 14pt;">' + reportTitle + '</b></td></tr>';
        xml += '<tr><td colspan="2" align="center"><b style="font-size: 12pt;">' + tableData.name + '</b></td></tr>';
        
        // Add date information
        if (tableData.moveInDate_f && tableData.moveOutDate_f) {
            xml += '<tr>';
            xml += '<td width="50%" align="right"><b>Move-In:</b> ' + tableData.moveInDate_f + '</td>';
            xml += '<td width="50%" align="left"><b>Move-Out:</b> ' + tableData.moveOutDate_f + '</td>';
            xml += '</tr>';
        }
        
        if (tableData.startDate_f && tableData.endDate_f) {
            xml += '<tr>';
            xml += '<td width="50%" align="right"><b>Start Date:</b> ' + tableData.startDate_f + '</td>';
            xml += '<td width="50%" align="left"><b>End Date:</b> ' + tableData.endDate_f + '</td>';
            xml += '</tr>';
        }
        
        xml += '</table>';
        
        return xml;
    }

    /**
     * Generates XML for booth information
     */
    function xml_BoothInfo(boothData) {
        let xml = '<table style="width: 100%; margin-top: 10px;">';
        xml += '<tr>';
        xml += '<td width="50%"><b>Booth:</b> ' + boothData.name + '</td>';
        xml += '<td width="50%"><b>Exhibitor:</b> ' + (boothData.exhibitor_name || '') + '</td>';
        xml += '</tr>';
        xml += '</table>';
        
        return xml;
    }

    /**
     * Generates XML for booth items
     */
    function xml_BoothItems(items, showDescriptions) {
        if (!items || items.length === 0) {
            return '<p style="margin-top: 20px; font-style: italic;">No items found for this booth.</p>';
        }
        
        let xml = '<table style="width: 100%; margin-top: 10px; border-collapse: collapse;" border="1" cellpadding="5">';
        xml += '<thead>';
        xml += '<tr>';
        xml += '<th width="40%">Item</th>';
        xml += '<th width="15%">Ordered</th>';
        xml += '<th width="15%">Received</th>';
        xml += '<th width="15%">Complete</th>';
        xml += '<th width="15%">Notes</th>';
        xml += '</tr>';
        xml += '</thead>';
        xml += '<tbody>';
        
        for (let i = 0; i < items.length; i++) {
            xml += '<tr>';
            xml += '<td>' + items[i].item_name + '</td>';
            xml += '<td align="center">' + (items[i].quantity || '0') + '</td>';
            xml += '<td align="center">' + (items[i].received || '0') + '</td>';
            xml += '<td align="center">' + (items[i].complete ? 'Yes' : 'No') + '</td>';
            xml += '<td>' + (items[i].notes || '') + '</td>';
            xml += '</tr>';
            
            // Add item description if enabled and available
            if (showDescriptions && items[i].item_desc) {
                xml += '<tr>';
                xml += '<td colspan="5" class="item-desc">' + items[i].item_desc + '</td>';
                xml += '</tr>';
            }
        }
        
        xml += '</tbody>';
        xml += '</table>';
        
        return xml;
    }

    return {
        onRequest: onRequest
    };
}); 