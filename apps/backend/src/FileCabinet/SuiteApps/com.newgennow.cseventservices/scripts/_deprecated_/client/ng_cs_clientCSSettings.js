/**
 * Module Description
 *
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _log = NewGen.lib.logging;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;

var evType = null;

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function clientPageInit(type) {
  evType = type;

  var genRand = nlapiGetFieldValue("custrecord_ng_cs_gen_rand_email");
  if (genRand != "T") {
    nlapiDisableField("custrecord_ng_cs_rand_email_domain", true);
    nlapiDisableField("custrecord_ng_cs_rand_email_prefix", true);
  }

  var useUndepFunds = nlapiGetFieldValue("custrecord_ng_cs_use_undep_funds");
  if (useUndepFunds != "T") {
    nlapiDisableField("custrecord_ng_cs_def_dep_account", true);
  }

  var useCancCharge = nlapiGetFieldValue("custrecord_ng_cs_use_cancl_charge");
  if (useCancCharge != "T") {
    nlapiDisableField("custrecord_ng_cs_cancl_charge_item", true);
    nlapiDisableField("custrecord_ng_cs_def_canc_chrg_pct", true);
    nlapiDisableField("custrecord_ng_cs_canc_threshold", true);
  }

  var useAuditing = nlapiGetFieldValue("custrecord_ng_cs_use_show_auditing");
  var usePreInvoicing = nlapiGetFieldValue(
    "custrecord_ng_cs_use_pre_invoicing"
  );
  var useAltForms = nlapiGetFieldValue("custrecord_ng_cs_use_alt_forms");
  if (useAuditing != "T" && usePreInvoicing != "T") {
    nlapiDisableField("custrecord_ng_cs_use_alt_forms", true);
    nlapiDisableField("custrecord_ng_cs_show_audit_form", true);
    nlapiDisableField("custrecord_ng_cs_pre_invoicing_form", true);
  } else {
    if (useAltForms == "T") {
      if (useAuditing != "T") {
        nlapiDisableField("custrecord_ng_cs_show_audit_form", true);
      }
      if (usePreInvoicing != "T") {
        nlapiDisableField("custrecord_ng_cs_pre_invoicing_form", true);
      }
    } else {
      nlapiDisableField("custrecord_ng_cs_show_audit_form", true);
      nlapiDisableField("custrecord_ng_cs_pre_invoicing_form", true);
    }
  }

  var invoiceFailureEmail = nlapiGetFieldValue(
    "custrecord_ng_cs_send_invoice_fail_email"
  );
  if (invoiceFailureEmail != "T") {
    nlapiDisableField("custrecord_ng_cs_inv_fail_sender", true);
    nlapiDisableField("custrecord_ng_cs_inv_fail_recip", true);
    nlapiDisableField("custrecord_ng_cs_inv_fail_cc", true);
  }

  var AutoSendInvoice = nlapiGetFieldValue(
    "custrecord_ng_cs_send_exhib_invoice"
  );
  if (AutoSendInvoice != "T") {
    nlapiDisableField("custrecord_ng_cs_exhib_invoice_sender", true);
    nlapiDisableField("custrecord_ng_cs_exhb_inv_email_template", true);
    //nlapiDisableField("custrecord_ng_cs_inv_email_conditions", true);
  }

  var webPaymentFailureEmail = nlapiGetFieldValue(
    "custrecord_ng_cs_send_web_pymnt_email"
  );
  if (webPaymentFailureEmail != "T") {
    nlapiDisableField("custrecord_ng_cs_web_pymnt_notice_sender", true);
    nlapiDisableField("custrecord_ng_cs_web_pymnt_fail_recip", true);
    nlapiDisableField("custrecord_ng_cs_web_pymnt_fail_cc", true);
    nlapiDisableField("custrecord_ng_cs_pymnt_fail_eml_template", true);
  }

  var enableMassBoothOrdDelete = nlapiGetFieldValue(
    "custrecord_ng_cs_allow_mass_booth_delete"
  );
  if (enableMassBoothOrdDelete != "T") {
    nlapiDisableField("custrecord_ng_cs_mass_booth_delete_roles", true);
  }

  var preventBilledOrderEdit = nlapiGetFieldValue(
    "custrecord_ng_cs_no_billed_order_editing"
  );
  if (preventBilledOrderEdit != "T") {
    nlapiDisableField("custrecord_ng_cs_billed_ord_edit_users", true);
  }

  var useJobNumbering = nlapiGetFieldValue(
    "custrecord_ng_cs_use_job_numbering"
  );
  if (useJobNumbering != "T") {
    nlapiDisableField("custrecord_ng_cs_simple_job_numbering", true);
    nlapiDisableField("custrecord_ng_cs_job_num_prefix", true);
    nlapiDisableField("custrecord_ng_cs_custom_job_numbering", true);
  }

  var preventAdtnlOrder = nlapiGetFieldValue(
    "custrecord_ng_cs_prev_adtl_orders"
  );
  if (preventAdtnlOrder != "T") {
    nlapiDisableField("custrecord_ng_cs_allow_mult_billng_part", true);
  }

  var useJobNumbering = nlapiGetFieldValue("custrecord_ng_cs_use_cc_conv_fee");
  if (useJobNumbering != "T") {
    nlapiDisableField("custrecord_ng_cs_cc_conv_fee_rate", true);
    nlapiDisableField("custrecord_ng_cs_cc_conv_fee_item", true);
    nlapiDisableField("custrecord_ng_cs_cc_conv_fee_order_types", true);
  }

  /*var enableWO = nlapiGetFieldValue("custrecord_ng_cs_enable_wo_printing");
	if (enableWO != "T") {
		nlapiDisableField("custrecord_ng_cs_wo_logo_img", true);
	}*/

  if (!_UseSubsidiaries) {
    nlapiDisableField("custrecord_ng_cs_name_from_subsidiary", true);
  }

  var sysDomain = "{0}//{1}".NG_Format(
    window.location.protocol,
    window.location.hostname
  );
  if (sysDomain.search(/debugger/i) < 0) {
    nlapiSetFieldValue("custrecord_ng_cs_acct_domain_url", sysDomain, false);
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @returns {Boolean} True to continue save, false to abort save
 */
function clientSaveRecord() {
  var genRand = nlapiGetFieldValue("custrecord_ng_cs_gen_rand_email");
  if (genRand == "T") {
    var domain = nlapiGetFieldValue("custrecord_ng_cs_rand_email_domain");
    var prefix = nlapiGetFieldValue("custrecord_ng_cs_rand_email_prefix");
    if (_tools.isEmpty(domain)) {
      window.alert(
        "You must define a default domain for randomly generated email addresses."
      );
      return false;
    } else {
      if (domain.search("@") != 0 || domain.search(".") < 2) {
        window.alert(
          "The default domain entered for email addresses is invalid."
        );
        return false;
      }
    }
    if (_tools.isEmpty(prefix)) {
      window.alert(
        "You must define a default prefix for randomly generated email addresses."
      );
      return false;
    } else {
      if (prefix.search(/^[A-z0-9_]/) >= 0) {
        window.alert(
          "Randomly generated email prefixes may only contain the letters A through Z, the numbers 0 through 9, and the underscore character."
        );
        return false;
      }
    }
  }

  var useUndepFunds = nlapiGetFieldValue("custrecord_ng_cs_use_undep_funds");
  if (useUndepFunds != "T") {
    var depositAccount = nlapiGetFieldValue("custrecord_ng_cs_def_dep_account");
    if (_tools.isEmpty(depositAccount)) {
      window.alert("You must select a default deposit account.");
      return false;
    }
  }

  var useCancCharge = nlapiGetFieldValue("custrecord_ng_cs_use_cancl_charge");
  if (useCancCharge == "T") {
    var cChargeItem = nlapiGetFieldValue("custrecord_ng_cs_cancl_charge_item");
    var cChargePct = nlapiGetFieldValue("custrecord_ng_cs_def_canc_chrg_pct");
    var cChargeThreshold = nlapiGetFieldValue(
      "custrecord_ng_cs_canc_threshold"
    );

    if (_tools.isEmpty(cChargeItem)) {
      window.alert("You must choose a cancellation charge item.");
      return false;
    }
    if (_tools.isEmpty(cChargePct)) {
      window.alert("You must enter a cancellation charge per cent value.");
      return false;
    } else {
      var pct =
        cChargePct.search("%") >= 0
          ? new Number(cChargePct.replace("%", ""))
          : new Number(cChargePct);
      var cancellationPct = pct / 100;
      if (cancellationPct <= 0) {
        window.alert(
          "The cancellation charge per cent value must be greater than zero."
        );
        return false;
      }
    }
    if (_tools.isEmpty(cChargeThreshold)) {
      window.alert("You must choose a cancellation charge threshold.");
      return false;
    }

    if (!_tools.isEmpty(cChargeThreshold)) {
      if (cChargeThreshold == "2") {
        if (
          _tools.isEmpty(
            nlapiGetFieldValue("custrecord_ng_cs_default_show_date")
          )
        ) {
          window.alert(
            'Your selection for "Cancellation Charge Threshold" requires a value to be set for \'Default "Show Date" Date Type\'.'
          );
          return false;
        }
      } else if (cChargeThreshold == "3") {
        if (
          _tools.isEmpty(
            nlapiGetFieldValue("custrecord_ng_cs_default_show_move_in")
          )
        ) {
          window.alert(
            'Your selection for "Cancellation Charge Threshold" requires a value to be set for \'Default "Show Move-In" Date Type\'.'
          );
          return false;
        }
      } else if (cChargeThreshold == "4") {
        if (
          _tools.isEmpty(
            nlapiGetFieldValue("custrecord_ng_cs_default_exhib_move_in")
          )
        ) {
          window.alert(
            'Your selection for "Cancellation Charge Threshold" requires a value to be set for \'Default "Exhibitor Move-In" Date Type\'.'
          );
          return false;
        }
      }
    }
  }

  var useAuditing = nlapiGetFieldValue("custrecord_ng_cs_use_show_auditing");
  var usePreInvoicing = nlapiGetFieldValue(
    "custrecord_ng_cs_use_pre_invoicing"
  );
  var useAltForms = nlapiGetFieldValue("custrecord_ng_cs_use_alt_forms");
  if (useAltForms == "T") {
    if (useAuditing == "T") {
      var auditForm = nlapiGetFieldValue("custrecord_ng_cs_show_audit_form");
      if (_tools.isEmpty(auditForm)) {
        window.alert(
          "You must select a custom form for auditing show transactions."
        );
        return false;
      }
    }
    if (usePreInvoicing == "T") {
      var preInvoiceForm = nlapiGetFieldValue(
        "custrecord_ng_cs_pre_invoicing_form"
      );
      if (_tools.isEmpty(preInvoiceForm)) {
        window.alert(
          "You must select a custom form for pre-invoicing show transactions."
        );
        return false;
      }
    }
  }

  var invoiceFailureEmail = nlapiGetFieldValue(
    "custrecord_ng_cs_send_invoice_fail_email"
  );
  if (invoiceFailureEmail == "T") {
    if (
      _tools.isEmpty(nlapiGetFieldValue("custrecord_ng_cs_inv_fail_sender"))
    ) {
      window.alert("You must select a sender for invoice failure emails.");
      return false;
    }
    if (_tools.isEmpty(nlapiGetFieldValue("custrecord_ng_cs_inv_fail_recip"))) {
      window.alert("You must select a recipient for invoice failure emails.");
      return false;
    }
    if (!_tools.isEmpty(nlapiGetFieldValue("custrecord_ng_cs_inv_fail_cc"))) {
      var ccRecips = nlapiGetFieldValue("custrecord_ng_cs_inv_fail_cc");
      var recips = ccRecips.split(/ , | ; |, |; |,|;/);
      for (var r = 0; r < recips.length; r++) {
        if (_tools.isEmpty(recips[r])) {
          window.alert(
            'There appears to be an empty entry for "Invoice Failure CC Recip". Please review the entered addresses and make sure multiple addresses are separated with only commas or semicolons, and that there are no extra commas or semicolons entered.'
          );
          return false;
        } else if (!_tools.checkNSEmailAddress(recips[r])) {
          window.alert(
            'The following email address entered for "Invoice Failure CC Recip" appears to be invalid:\n\n{0}\n\nPlease review the entered addresses and make sure multiple addresses are separated with only commas or semicolons, and that there are no extra commas or semicolons entered.'.NG_Format(
              recips[r]
            )
          );
          return false;
        }
      }
    }
  }

  var AutoSendInvoice = nlapiGetFieldValue(
    "custrecord_ng_cs_send_exhib_invoice"
  );
  if (AutoSendInvoice == "T") {
    if (
      _tools.isEmpty(
        nlapiGetFieldValue("custrecord_ng_cs_exhib_invoice_sender")
      )
    ) {
      window.alert("You must select a sender for invoice PDF emails.");
      return false;
    }
    if (
      _tools.isEmpty(
        nlapiGetFieldValue("custrecord_ng_cs_exhb_inv_email_template")
      )
    ) {
      window.alert("You must select a template for invoice PDF emails.");
      return false;
    }
    /*if (_tools.isEmpty(nlapiGetFieldValue("custrecord_ng_cs_inv_email_conditions"))) {
			window.alert("");
			return false;
		}*/
  }

  var webPaymentFailureEmail = nlapiGetFieldValue(
    "custrecord_ng_cs_send_web_pymnt_email"
  );
  if (webPaymentFailureEmail == "T") {
    if (
      _tools.isEmpty(
        nlapiGetFieldValue("custrecord_ng_cs_web_pymnt_notice_sender")
      )
    ) {
      window.alert("You must select a sender for web payment failure emails.");
      return false;
    }
    if (
      _tools.isEmpty(
        nlapiGetFieldValue("custrecord_ng_cs_web_pymnt_fail_recip")
      )
    ) {
      window.alert(
        "You must select a recipient for web payment failure emails."
      );
      return false;
    }
    if (
      _tools.isEmpty(
        nlapiGetFieldValue("custrecord_ng_cs_pymnt_fail_eml_template")
      )
    ) {
      window.alert(
        "You must select a template for web payment failure emails."
      );
      return false;
    }
  }

  if (_tools.isEmpty(scriptForms) || scriptForms.length < 1) {
    var go = window.confirm(
      "You have no custom forms selected for booth order scripting. Are you sure?"
    );
    if (!go) {
      return false;
    }
  }

  var scriptForms = _tools.getMultiSelect(
    "custrecord_ng_cs_add_item_forms",
    null,
    false,
    null
  );
  if (_tools.isEmpty(scriptForms) || scriptForms.length < 1) {
    var go = window.confirm(
      "You have no custom forms selected for add item scripting. Are you sure?"
    );
    if (!go) {
      return false;
    }
  }

  var scriptForms = _tools.getMultiSelect(
    "custrecord_ng_cs_show_mgt_forms",
    null,
    false,
    null
  );
  if (_tools.isEmpty(scriptForms) || scriptForms.length < 1) {
    var go = window.confirm(
      "You have no custom forms selected for show management scripting. Are you sure?"
    );
    if (!go) {
      return false;
    }
  }

  if (enableGlobalBooth == "T" && enableGlobalShowM == "T") {
    window.alert(
      "You cannot have both Global Booth Order and Global Show Management Order enabled at the same time."
    );
    return false;
  } else {
    var boList = _tools.getMultiSelect(
      "custrecord_ng_cs_booth_ord_forms",
      null,
      false,
      null
    );
    var smList = _tools.getMultiSelect(
      "custrecord_ng_cs_show_mgt_forms",
      null,
      false,
      null
    );
    if (boList.length > 0 && smList.length > 0) {
      var shared = boList.NG_intersect(smList);
      if (shared.length > 0) {
        window.alert(
          "There are {0} transaction forms selected for both booth orders and show management orders. Transaction forms cannot be used for both. Please make sure the selections are exclusive.".NG_Format(
            shared.length.toFixed(0)
          )
        );
        return false;
      }
    }
  }

  var enableMassBoothOrdDelete = nlapiGetFieldValue(
    "custrecord_ng_cs_allow_mass_booth_delete"
  );
  if (enableMassBoothOrdDelete == "T") {
    var deleteRoles = _tools.getMultiSelect(
      "custrecord_ng_cs_mass_booth_delete_roles",
      null,
      false,
      null
    );
    if (_tools.isEmpty(deleteRoles) || deleteRoles.length < 1) {
      window.alert(
        "You must select one or more user roles forms for enabling mass booth order deletion."
      );
      return false;
    }
  }

  var preventBilledOrderEdit = nlapiGetFieldValue(
    "custrecord_ng_cs_no_billed_order_editing"
  );
  if (preventBilledOrderEdit == "T") {
    var editUsers = _tools.getMultiSelect(
      "custrecord_ng_cs_billed_ord_edit_users",
      null,
      false,
      null
    );
    if (_tools.isEmpty(editUsers) || editUsers.length < 1) {
      var go = window.confirm(
        "Are you sure that no users are authorized to edit billed booth orders?"
      );
      if (!go) return false;
    }
  }

  var enableWO = nlapiGetFieldValue("custrecord_ng_cs_enable_wo_printing");
  if (enableWO == "T") {
    if (
      _tools.isEmpty(nlapiGetFieldValue("custrecord_ng_cs_acct_domain_url"))
    ) {
      window.alert(
        "If Work Order Printing is enabled, there must be a value for Account Domain URL. Example values:\n\n> https://system.nestuite.com\n> https://system.na1.netsuite.com"
      );
      return false;
    }
  }

  var dCalcDateTypes = _tools.getMultiSelect(
    "custrecord_ng_cs_add_item_forms",
    null,
    false,
    null
  );
  if (!_tools.isEmpty(dCalcDateTypes) && dCalcDateTypes.length >= 1) {
    if (
      _tools.isEmpty(nlapiGetFieldValue("custrecord_ng_cs_default_show_date"))
    ) {
      window.alert(
        'Your selection for "Default Days Calc Show Date Types" requires a value to be set for \'Default "Show Date" Date Type\'.'
      );
      return false;
    }
  }

  var laborDateTypes = _tools.getMultiSelect(
    "custrecord_ng_cs_add_item_forms",
    null,
    false,
    null
  );
  if (!_tools.isEmpty(laborDateTypes) && laborDateTypes.length >= 1) {
    if (
      _tools.isEmpty(nlapiGetFieldValue("custrecord_ng_cs_default_show_date"))
    ) {
      window.alert(
        'Your selection for "Default Labor Show Date Types" requires a value to be set for \'Default "Show Date" Date Type\'.'
      );
      return false;
    }
  }

  var shadeRegex = /^\#[0-9A-F]{6}/;
  var shadeHex = nlapiGetFieldValue("custrecord_ng_cs_report_line_shade_hex");
  if (!_tools.isEmpty(shadeHex) && !shadeRegex.test(shadeHex)) {
    window.alert(
      "The hex value entered of {0} for report line shading is an invalid hex entry. Please reenter your value.".NG_Format(
        shadeHex
      )
    );
    return false;
  }

  var algoliaCanonical = nlapiGetFieldValue(
    "custrecord_ng_cs_canonical_base_url"
  );
  if (!_tools.isEmpty(algoliaCanonical)) {
    var phPos = algoliaCanonical.search(/\{0\}/);
    if (phPos < 0) {
      window.alert(
        "The value for the Algolia Canonical URL is missing the item ID placeholder. Please enter in the text of '{0}' at the appropriate place."
      );
      return false;
    }
  }

  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Boolean} True to continue changing field value, false to abort value change
 */
function clientValidateField(type, name, linenum) {
  if (name == "custrecord_ng_cs_custom_job_numbering") {
    var value = nlapiGetFieldValue(name);
    if (value == "T") {
      var go = window.confirm(
        "This setting requires custom programming. If custom programming is not prepared, job numbering errors can occur. Do you wish to enable this setting?"
      );
      return go;
    }
  }

  if (name == "custrecord_ng_cs_use_multi_cc_proc") {
    var value = nlapiGetFieldValue(name);
    if (value == "T") {
      var go = window.confirm(
        "This setting requires custom programming. If custom programming is not prepared, credit card processing errors can occur. Do you wish to enable this setting?"
      );
      return go;
    }
  }

  return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Void}
 */
function clientFieldChanged(type, name, linenum) {
  if (name == "custrecord_ng_cs_gen_rand_email") {
    var genRand = nlapiGetFieldValue("custrecord_ng_cs_gen_rand_email");
    if (genRand != "T") {
      nlapiDisableField("custrecord_ng_cs_rand_email_domain", true);
      nlapiDisableField("custrecord_ng_cs_rand_email_prefix", true);
    } else {
      nlapiDisableField("custrecord_ng_cs_rand_email_domain", false);
      nlapiDisableField("custrecord_ng_cs_rand_email_prefix", false);
    }
  } else if (name == "custrecord_ng_cs_use_cancl_charge") {
    var useCancCharge = nlapiGetFieldValue("custrecord_ng_cs_use_cancl_charge");
    if (useCancCharge != "T") {
      nlapiDisableField("custrecord_ng_cs_cancl_charge_item", true);
      nlapiDisableField("custrecord_ng_cs_def_canc_chrg_pct", true);
      nlapiDisableField("custrecord_ng_cs_canc_threshold", true);
      nlapiSetFieldValue("custrecord_ng_cs_cancl_charge_item", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_def_canc_chrg_pct", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_canc_threshold", "", false);
    } else {
      nlapiDisableField("custrecord_ng_cs_cancl_charge_item", false);
      nlapiDisableField("custrecord_ng_cs_def_canc_chrg_pct", false);
      nlapiDisableField("custrecord_ng_cs_canc_threshold", false);
    }
  } else if (
    _tools.isInArray(name, [
      "custrecord_ng_cs_use_show_auditing",
      "custrecord_ng_cs_use_pre_invoicing",
      "custrecord_ng_cs_use_alt_forms",
    ])
  ) {
    var useAuditing = nlapiGetFieldValue("custrecord_ng_cs_use_show_auditing");
    var usePreInvoicing = nlapiGetFieldValue(
      "custrecord_ng_cs_use_pre_invoicing"
    );
    var useAltForms = nlapiGetFieldValue("custrecord_ng_cs_use_alt_forms");
    if (useAuditing != "T" && usePreInvoicing != "T") {
      nlapiDisableField("custrecord_ng_cs_use_alt_forms", true);
      nlapiDisableField("custrecord_ng_cs_show_audit_form", true);
      nlapiDisableField("custrecord_ng_cs_pre_invoicing_form", true);
    } else {
      if (useAltForms == "T") {
        if (useAuditing != "T") {
          nlapiDisableField("custrecord_ng_cs_show_audit_form", true);
          nlapiSetFieldValue("custrecord_ng_cs_show_audit_form", "", false);
        } else {
          nlapiDisableField("custrecord_ng_cs_show_audit_form", false);
        }
        if (usePreInvoicing != "T") {
          nlapiDisableField("custrecord_ng_cs_pre_invoicing_form", true);
          nlapiSetFieldValue("custrecord_ng_cs_pre_invoicing_form", "", false);
        } else {
          nlapiDisableField("custrecord_ng_cs_pre_invoicing_form", false);
        }
      } else {
        nlapiDisableField("custrecord_ng_cs_show_audit_form", true);
        nlapiDisableField("custrecord_ng_cs_pre_invoicing_form", true);
        nlapiSetFieldValue("custrecord_ng_cs_show_audit_form", "", false);
        nlapiSetFieldValue("custrecord_ng_cs_pre_invoicing_form", "", false);
      }
    }
  } else if (name == "custrecord_ng_cs_send_invoice_fail_email") {
    var invoiceFailureEmail = nlapiGetFieldValue(
      "custrecord_ng_cs_send_invoice_fail_email"
    );
    if (invoiceFailureEmail == "T") {
      nlapiDisableField("custrecord_ng_cs_inv_fail_sender", false);
      nlapiDisableField("custrecord_ng_cs_inv_fail_recip", false);
      nlapiDisableField("custrecord_ng_cs_inv_fail_cc", false);
    } else {
      nlapiDisableField("custrecord_ng_cs_inv_fail_sender", true);
      nlapiDisableField("custrecord_ng_cs_inv_fail_recip", true);
      nlapiDisableField("custrecord_ng_cs_inv_fail_cc", true);
      nlapiSetFieldValue("custrecord_ng_cs_inv_fail_sender", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_inv_fail_recip", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_inv_fail_cc", "", false);
    }
  } else if (name == "custrecord_ng_cs_send_exhib_invoice") {
    var AutoSendInvoice = nlapiGetFieldValue(
      "custrecord_ng_cs_send_exhib_invoice"
    );
    if (AutoSendInvoice == "T") {
      nlapiDisableField("custrecord_ng_cs_exhib_invoice_sender", false);
      nlapiDisableField("custrecord_ng_cs_exhb_inv_email_template", false);
      //nlapiDisableField("custrecord_ng_cs_inv_email_conditions", false);
    } else {
      nlapiDisableField("custrecord_ng_cs_exhib_invoice_sender", true);
      nlapiDisableField("custrecord_ng_cs_exhb_inv_email_template", true);
      nlapiSetFieldValue("custrecord_ng_cs_exhib_invoice_sender", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_exhb_inv_email_template", "", false);
      //nlapiSetFieldValue("custrecord_ng_cs_inv_email_conditions", "", false);
    }
  } else if (name == "custrecord_ng_cs_send_web_pymnt_email") {
    var webPaymentFailureEmail = nlapiGetFieldValue(
      "custrecord_ng_cs_send_web_pymnt_email"
    );
    if (webPaymentFailureEmail == "T") {
      nlapiDisableField("custrecord_ng_cs_web_pymnt_notice_sender", false);
      nlapiDisableField("custrecord_ng_cs_web_pymnt_fail_recip", false);
      nlapiDisableField("custrecord_ng_cs_web_pymnt_fail_cc", false);
      nlapiDisableField("custrecord_ng_cs_pymnt_fail_eml_template", false);
    } else {
      nlapiDisableField("custrecord_ng_cs_web_pymnt_notice_sender", true);
      nlapiDisableField("custrecord_ng_cs_web_pymnt_fail_recip", true);
      nlapiDisableField("custrecord_ng_cs_web_pymnt_fail_cc", true);
      nlapiDisableField("custrecord_ng_cs_pymnt_fail_eml_template", true);
      nlapiSetFieldValue("custrecord_ng_cs_web_pymnt_notice_sender", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_web_pymnt_fail_recip", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_web_pymnt_fail_cc", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_pymnt_fail_eml_template", "", false);
    }
  } else if (name == "custrecord_ng_cs_allow_mass_booth_delete") {
    var enableMassBoothOrdDelete = nlapiGetFieldValue(
      "custrecord_ng_cs_allow_mass_booth_delete"
    );
    if (enableMassBoothOrdDelete != "T") {
      nlapiDisableField("custrecord_ng_cs_mass_booth_delete_roles", true);
      nlapiSetFieldValue(
        "custrecord_ng_cs_mass_booth_delete_roles",
        new Array(),
        false
      );
    } else {
      nlapiDisableField("custrecord_ng_cs_mass_booth_delete_roles", false);
    }
  } else if (name == "custrecord_ng_cs_no_billed_order_editing") {
    var preventBilledOrderEdit = nlapiGetFieldValue(
      "custrecord_ng_cs_no_billed_order_editing"
    );
    if (preventBilledOrderEdit != "T") {
      nlapiDisableField("custrecord_ng_cs_billed_ord_edit_users", true);
      nlapiSetFieldValue(
        "custrecord_ng_cs_billed_ord_edit_users",
        new Array(),
        false
      );
    } else {
      nlapiDisableField("custrecord_ng_cs_billed_ord_edit_users", false);
    }
  } else if (name == "custrecord_ng_cs_use_job_numbering") {
    var useJobNumbering = nlapiGetFieldValue(
      "custrecord_ng_cs_use_job_numbering"
    );
    if (useJobNumbering != "T") {
      nlapiDisableField("custrecord_ng_cs_simple_job_numbering", true);
      nlapiDisableField("custrecord_ng_cs_job_num_prefix", true);
      nlapiDisableField("custrecord_ng_cs_custom_job_numbering", true);
      nlapiSetFieldValue("custrecord_ng_cs_simple_job_numbering", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_job_num_prefix", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_custom_job_numbering", "", false);
    } else {
      nlapiDisableField("custrecord_ng_cs_simple_job_numbering", false);
      nlapiDisableField("custrecord_ng_cs_job_num_prefix", false);
      nlapiDisableField("custrecord_ng_cs_custom_job_numbering", false);
    }
  } else if (name == "custrecord_ng_cs_prev_adtl_orders") {
    var preventAdtnlOrder = nlapiGetFieldValue(
      "custrecord_ng_cs_prev_adtl_orders"
    );
    if (preventAdtnlOrder == "T") {
      nlapiDisableField("custrecord_ng_cs_allow_mult_billng_part", false);
    } else {
      nlapiDisableField("custrecord_ng_cs_allow_mult_billng_part", true);
      nlapiSetFieldValue("custrecord_ng_cs_allow_mult_billng_part", "F", false);
    }
  } else if (name == "custrecord_ng_cs_use_cc_conv_fee") {
    var useConvFees = nlapiGetFieldValue("custrecord_ng_cs_use_cc_conv_fee");
    if (useConvFees != "T") {
      nlapiDisableField("custrecord_ng_cs_cc_conv_fee_rate", true);
      nlapiDisableField("custrecord_ng_cs_cc_conv_fee_item", true);
      nlapiDisableField("custrecord_ng_cs_cc_conv_fee_order_types", true);
      nlapiSetFieldValue("custrecord_ng_cs_cc_conv_fee_rate", "", false);
      nlapiSetFieldValue("custrecord_ng_cs_cc_conv_fee_item", "", false);
      nlapiSetFieldValue(
        "custrecord_ng_cs_cc_conv_fee_order_types",
        null,
        false
      );
    } else {
      nlapiDisableField("custrecord_ng_cs_cc_conv_fee_rate", false);
      nlapiDisableField("custrecord_ng_cs_cc_conv_fee_item", false);
      nlapiDisableField("custrecord_ng_cs_cc_conv_fee_order_types", false);
    }
  } else if (name == "custrecord_ng_cs_default_std_labor_rate") {
    var stdRate = new Number(nlapiGetFieldValue(name));
    var ovtRate = _M.roundToHundredths(stdRate * 1.5);
    var dblRate = _M.roundToHundredths(stdRate * 2);
    nlapiSetFieldValue(
      "custrecord_ng_cs_default_ovt_labor_rate",
      ovtRate.toFixed(2),
      false
    );
    nlapiSetFieldValue(
      "custrecord_ng_cs_default_dbl_labor_rate",
      dblRate.toFixed(2),
      false
    );
  } else if (name == "custrecord_ng_cs_enable_wo_printing") {
    var enableWO = nlapiGetFieldValue(name);
    if (enableWO == "T") {
      nlapiDisableField("custrecord_ng_cs_wo_logo_img", false);
    } else {
      nlapiDisableField("custrecord_ng_cs_wo_logo_img", true);
    }
  } else if (name == "custrecord_ng_cs_wo_img") {
    var fileID = nlapiGetFieldValue("custrecord_ng_cs_wo_img");
    if (!_tools.isEmpty(fileID)) {
      var fileInfo = nlapiLookupField("file", fileID, ["isavailable", "url"]);
      if (fileInfo.isavailable == "T") {
        nlapiSetFieldValue(
          "custrecord_ng_cs_wo_logo_img_url",
          "{0}{1}".NG_Format(
            nlapiGetFieldValue("custrecord_ng_cs_acct_domain_url"),
            fileInfo.url
          )
        );
      } else {
        window.alert("This file is not marked as 'Available Without Login'");
        nlapiSetFieldValue("custrecord_ng_cs_wo_logo_img", "");
        nlapiSetFieldValue("custrecord_ng_cs_wo_logo_img_url", "");
      }
    } else {
      nlapiSetFieldValue("custrecord_ng_cs_wo_logo_img_url", "");
    }
  } else if (name == "custrecord_ng_cs_report_line_shade_hex") {
    var regex = /^\#[0-9A-F]{6}/;
    var hex = nlapiGetFieldValue("custrecord_ng_cs_report_line_shade_hex");
    if (!_tools.isEmpty(hex) && !regex.test(hex)) {
      window.alert(
        "The hex value entered of {0} is an invalid hex entry. Please reenter your value.".NG_Format(
          hex
        )
      );
      //	nlapiSetFieldValue("custrecord_ng_cs_report_line_shade_hex", "", false);
    }
  } else if (name == "custrecord_ng_cs_company_header_logo") {
    var logoFileID = nlapiGetFieldValue("custrecord_ng_cs_company_header_logo");
    if (!_tools.isEmpty(logoFileID)) {
      var logoFileURL = nlapiLookupField("file", logoFileID, "url");
      nlapiSetFieldValue("custrecord_ng_cs_header_logo_url", logoFileURL);
    } else {
      nlapiSetFieldValue("custrecord_ng_cs_header_logo_url", "");
    }
  }

  if (
    _tools.isInArray(name, [
      "custrecord_ng_cs_web_img_folder_id",
      "custrecord_ng_cs_exhb_kit_folder_id",
      "custrecord_ng_cs_csv_import_folder_id",
      "custrecord_ng_cs_file_upload_folder_id",
      "custrecord_ng_cs_report_xml_folder_id",
    ])
  ) {
    var folderType = {
      custrecord_ng_cs_web_img_folder_id: "web image",
      custrecord_ng_cs_exhb_kit_folder_id: "exhibitor kit",
      custrecord_ng_cs_csv_import_folder_id: "CSV import",
      custrecord_ng_cs_file_upload_folder_id: "file upload",
      custrecord_ng_cs_report_xml_folder_id: "report PDF XML",
    };

    var folderID = nlapiGetFieldValue(name);
    if (!_tools.isEmpty(folderID)) {
      var folderList = new Array();
      var fFilt = new Array(["internalid", "is", folderID]);
      var fCols = new Array(
        new nlobjSearchColumn("name", null, null),
        new nlobjSearchColumn("parent", null, null)
      );
      var fSearch = null;
      try {
        fSearch = nlapiSearchRecord("folder", null, fFilt, fCols);
      } catch (err) {
        console.log(
          err,
          "Error encountered searching for {0} folder".NG_Format(
            folderType[name]
          )
        );
      }
      if (!_tools.isEmpty(fSearch)) {
        var name = fSearch[0].getValue("name");
        var parent = fSearch[0].getValue("parent");
        folderList.unshift(name);
        while (!_tools.isEmpty(parent)) {
          fFilt = new Array(["internalid", "is", parent]);
          var fSearchP = null;
          try {
            fSearchP = nlapiSearchRecord("folder", null, fFilt, fCols);
          } catch (err) {
            console.log(
              err,
              "Error encountered searching for {0} folder parent".NG_Format(
                folderType[name]
              )
            );
          }
          if (!_tools.isEmpty(fSearchP)) {
            var pName = fSearchP[0].getValue("name");
            parent = fSearchP[0].getValue("parent");
            folderList.unshift(pName);
          }
        }
        var fullPath = folderList.join(" / ");
        var go = window.confirm(
          "You have chosen the following folder:\n\n{0}\n\nConfirm folder?".NG_Format(
            fullPath
          )
        );
        if (!go) {
          nlapiSetFieldValue(name, "", false);
        }
      } else {
        window.alert(
          "That folder does not seem to exist. Please enter a different folder ID."
        );
        nlapiSetFieldValue(name, "", false);
      }
    }
  }
}
