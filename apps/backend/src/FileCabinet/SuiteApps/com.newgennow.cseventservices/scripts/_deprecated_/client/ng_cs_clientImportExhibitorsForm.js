/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType 
 * 
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function clientPageInit(type){
	
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @returns {Boolean} True to continue save, false to abort save
 */
function clientSaveRecord(){
	try {
		var imageFile = nlapiGetFieldValue("custpage_csvfile");
		var extPos = imageFile.lastIndexOf(".");
		if (extPos <= 0) {
			window.alert("You have selected an invalid file for CSV import. Please select a file of type CSV.");
			return false;
		} else {
			var ext = imageFile.substr(extPos + 1).toUpperCase();
			if (ext != "CSV") {
				window.alert("You have selected an invalid file for CSV import. Please select a file of type CSV.");
				return false;
			}
		}
	} catch (err) {
		window.alert("ERROR:\n[{0}] {1}".NG_Format(err.name,err.message));
		return false;
	}
	
	nlapiSetFieldValue("custpage_user", nlapiGetUser(), false);
	
	return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Boolean} True to continue changing field value, false to abort value change
 */
function clientValidateField(type, name, linenum){
	
	return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Void}
 */
function clientFieldChanged(type, name, linenum){
	if (name == "custpage_send_to_user") {
		var value = nlapiGetFieldValue(name);
		if (value == "T") {
			nlapiSetFieldValue("custpage_send_to_group", "", false);
			nlapiDisableField("custpage_send_to_group", true);
		} else {
			nlapiDisableField("custpage_send_to_group", false);
		}
	}
	
	if (name == "custpage_send_to_group") {
		var value = nlapiGetFieldValue(name);
		if (value == "T") {
			nlapiSetFieldValue("custpage_send_to_user", "F", false);
			nlapiDisableField("custpage_send_to_user", true);
		} else {
			nlapiDisableField("custpage_send_to_user", false);
		}
	}
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @returns {Void}
 */
function clientPostSourcing(type, name) {
	
}
