/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;
var _UseDepartments = _NSFeatures.DEPARTMENTS() == "T" ? true : false;
var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;

var _WebImageFolderID = _scLib.WebImageFolderID;
var _ExhibitorKitFolderID = _scLib.ExhibitorKitFolderID;
var _UseTaxRate = _scLib.UseTaxCode;
var _UseCnclPct = _scLib.UseCancellationCharge;
var _DefCnclPct = _scLib.CancellationChargePct;
var _ValidateDates = true;
var _UseCSJobs = _scLib.UseCustomJob;
var _DefaultSubsidiary = _scLib.DefaultSubsidiary;

var _HallArray = new Array();
var _BOOTH_LINE = "Booth Number __________";
var _EG_COMMENTS = "Auto-generated by Event Creation Wizard for event {0}";

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response){
	if (request.getMethod() == "GET") {
		displayForm(request, response);
	} else {
		buildShow(request, response);
	}
}


function displayForm(request, response, status) {
	var jobParam = request.getParameter("jb");
	var csJobParam = request.getParameter("csj");
	var showTitle = request.getParameter("sttl");
	var showNumber = request.getParameter("snbr");
	var showName = request.getParameter("snm");
	var showType = request.getParameter("stype");
	var subsidiary = request.getParameter("sdy");
	var department = request.getParameter("dpt");
	var location = request.getParameter("loc");
	var customerID = request.getParameter("cid");
	
	var form = nlapiCreateForm("Create New Event");
	
	if (status != null) {
		if (status.fail) {
			if (!_tools.isEmpty(status.stid)) {
				form.addFieldGroup("submissionresponse_a", "Event Creation Success with Errors");
				var successField = form.addField("custpage_success", "text", "Show Creation Succeeded", null, "submissionresponse_a");
				var createBoothsField = form.addField("custpage_createbooths", "text", "Booths", null, "submissionresponse_a");
				var showLink = nlapiResolveURL("RECORD", "customrecord_show", status.stid, "VIEW");
				var tranNum = nlapiLookupField("customrecord_show", status.stid, "name", false);
				var fieldVal = "<a href=\"{0}\">Show: {1}</a>".NG_Format(showLink,tranNum);
				var createBoothsPath = nlapiResolveURL("SUITELET", "customscript_booth_generator", "customdeploy_booth_generator_dep");
				successField.setDefaultValue(fieldVal);
				successField.setDisplayType("inline");
				createBoothsField.setDefaultValue("<a href=\"{0}&showid={1}\">Generate Booths</a>".NG_Format(createBoothsPath,status.stid));
				createBoothsField.setDisplayType("inline");
				form.addFieldGroup("submissionresponse", "Event Creation Error(s)");
			} else {
				form.addFieldGroup("submissionresponse", "Event Creation Failure");
			}
			
			var subjField = form.addField("custpage_failuresubject", "text", "Event Creation Failed", null, "submissionresponse");
			var codeField = form.addField("custpage_failurecode", "text", "Failure Code", null, "submissionresponse");
			var decrField = form.addField("custpage_failuredesc", "text", "Failure Description", null, "submissionresponse");
			
			subjField.setDefaultValue(status.failsubj);
			codeField.setDefaultValue(status.errname);
			decrField.setDefaultValue(status.errdesc.substr(0, 300));
			
			subjField.setDisplayType("inline");
			codeField.setDisplayType("inline");
			decrField.setDisplayType("inline");
		} else {
			if (!_tools.isEmpty(status.errList) && status.errList.length > 0) {
				form.addFieldGroup("submissionresponse", "Event Creation Success with Errors");
				for (var el = 0; el < status.errList.length; el++) {
					var err_grp = "submissionresponse_{0}".NG_Format(el);
					form.addFieldGroup(err_grp, "Recorded Error ({0} of {1})".NG_Format( (Math.round(el + 1)).toFixed(0) , status.errList.length ) );
					var subjField = form.addField("custpage_failuresubject_{0}".NG_Format(el), "text", "Error Message", null, err_grp);
					var codeField = form.addField("custpage_failurecode_{0}".NG_Format(el), "text", "Error Code", null, err_grp);
					var decrField = form.addField("custpage_failuredesc_{0}".NG_Format(el), "text", "Error Description", null, err_grp);
					
					subjField.setDefaultValue(status.errList[el].failsubj);
					codeField.setDefaultValue(status.errList[el].errname);
					decrField.setDefaultValue(status.errList[el].errdesc.substr(0, 300));
					
					subjField.setDisplayType("inline");
					codeField.setDisplayType("inline");
					decrField.setDisplayType("inline");
				}
			} else if (!_tools.isEmpty(status.failsubj)) {
				form.addFieldGroup("submissionresponse", "Event Creation Success with Errors");
				var subjField = form.addField("custpage_failuresubject", "text", "Show Creation Error", null, "submissionresponse");
				var codeField = form.addField("custpage_failurecode", "text", "Error Code", null, "submissionresponse");
				var decrField = form.addField("custpage_failuredesc", "text", "Error Description", null, "submissionresponse");
				
				subjField.setDefaultValue(status.failsubj);
				codeField.setDefaultValue(status.errname);
				decrField.setDefaultValue(status.errdesc.substr(0, 300));
				
				subjField.setDisplayType("inline");
				codeField.setDisplayType("inline");
				decrField.setDisplayType("inline");
			} else {
				form.addFieldGroup("submissionresponse", "Event Creation Success");
			}
			
			var successField = form.addField("custpage_success", "text", "Event Creation Succeeded", null, "submissionresponse");
			var createBoothsField = form.addField("custpage_createbooths", "text", "Booths", null, "submissionresponse");
			var showLink = nlapiResolveURL("RECORD", "customrecord_show", status.stid, "VIEW");
			var tranNum = nlapiLookupField("customrecord_show", status.stid, "name", false);
			var fieldVal = "<a href=\"{0}\">Show: {1}</a>".NG_Format(showLink,tranNum);
			var createBoothsPath = nlapiResolveURL("SUITELET", "customscript_booth_generator", "customdeploy_booth_generator_dep");
			successField.setDefaultValue(fieldVal);
			successField.setDisplayType("inline");
			createBoothsField.setDefaultValue("<a href=\"{0}&showid={1}\">Generate Booths</a>".NG_Format(createBoothsPath,status.stid));
			createBoothsField.setDisplayType("inline");
		}
	}
	
	form.addFieldGroup("group1", "Primary Event Information");
	form.addFieldGroup("group2", "Event Financial Information");
	form.addFieldGroup("group3", "Event Vendors");
	form.addFieldGroup("group4", "Event Floor Details");
	form.addFieldGroup("group5", "Group 5");
	form.addFieldGroup("misc", "Misc");
	
	form.addTab("datesmaintab", "Dates");
	form.addTab("webtab", "Web");
	form.addTab("displayforms", "Web Categories");
	form.addTab("freighttab", "Freight Table");
	
	form.setScript("customscript_ng_cs_cl_create_event_wzrd");
	
	//////////////////
	// Field Group #1 -- Primary Show Information
	//////////////////
	
	var showNameField = form.addField("custpage_showname", "text", "Event Name", null, "group1");
	var showNumberField = form.addField("custpage_shownumber", "text", "Event Number", null, "group1");
	var customerField = form.addField("custpage_customer", "select", "Customer", "customer", "group1");
	if (!_tools.isEmpty(customerID)) {
		customerField.setDefaultValue(customerID);
		customerField.setDisplayType("disabled");
	}
	var subsidiaryField = null;
	if (_UseSubsidiaries) {
		subsidiaryField = form.addField("custpage_subsidiary", "select", "Subsidiary", "subsidiary", "group1");
	}
	var showTypeField = form.addField("custpage_showtype", "select", "Event Type", "customrecord_order_type", "group1");
	form.addField("custpage_show_status", "select", "Event Status", "customrecord_cs_event_status", "group1");
	form.addField("custpage_venue", "select", "Event Venue", "customrecord_facility", "group1").setBreakType("startcol");
	if (_UseLocations) {
		var locField = form.addField("custpage_location", "select", "Advanced Warehouse Location", "location", "group1");
	//	locField.setBreakType("startcol");
		if (!_tools.isEmpty(location)) {
			locField.setDefaultValue(location);
		}
	}
	
	//////////////////
	// Field Group #2 -- Show Financial Information
	//////////////////
	
	var csJobField = null;
	var fShowField = null;
	if (_UseCSJobs) {
		csJobField = form.addField("custpage_cs_job", "select", "CS Job", "customrecord_cseg_ng_cs_job", "group2");
		fShowField = form.addField("custpage_financialshow", "select", "Class", "classification", "group2");
	} else {
		fShowField = form.addField("custpage_financialshow", "select", "Job", "classification", "group2");
	}
	
	var awShipRateField = form.addField("custpage_awshiprate", "currency", "Advanced Warehouse Drayage Rate", null, "group2");
	var ibShipRateField = form.addField("custpage_ibshiprate", "currency", "In Between Drayage Rate", null, "group2");
	var dShipRateField = form.addField("custpage_dshiprate", "currency", "Event Site Drayage Rate", null, "group2");
	var advPLField = form.addField("custpage_advpricelevel", "select", "Advance Price Level", "pricelevel", "group2");
	var stdPLField = form.addField("custpage_stdpricelevel", "select", "Standard Price Level", "pricelevel", "group2");
	var showPLField = form.addField("custpage_showpricelevel", "select", "Event Site Price Level", "pricelevel", "group2");
	var showMgmtPLField = form.addField("custpage_showmgmtpricelevel", "select", "Show Management Price Level", "pricelevel", "group2");
	var taxField = null;
	if (_UseTaxRate && _UseCnclPct) {
		taxField = form.addField("custpage_taxrate", "select", "Tax Group", null, "group2");
		if (_scLib.UseCanadianSalesTax) {
			form.addField("custpage_gst_percent", "text", "GST/HST", null, "group2").setDisplayType("inline");
			form.addField("custpage_pst_percent", "text", "PST", null, "group2").setDisplayType("inline");
		} else {
			form.addField("custpage_tax_percent", "text", "Tax Rate", null, "group2").setDisplayType("inline");
		}
		var cnclPctField = form.addField("custpage_cnclpct", "percent", "Cancellation Charge %", null, "group2");
		taxField.setBreakType("startcol");
		taxField.setDisplayType("normal");
	//	taxField.setMandatory(true);
		cnclPctField.setDisplayType("normal");
		cnclPctField.setDefaultValue(_DefCnclPct);
	} else if (_UseTaxRate && !_UseCnclPct) {
		taxField = form.addField("custpage_taxrate", "select", "Tax Group", null, "group2");
		if (_scLib.UseCanadianSalesTax) {
			form.addField("custpage_gst_percent", "text", "GST/HST", null, "group2").setDisplayType("inline");
			form.addField("custpage_pst_percent", "text", "PST", null, "group2").setDisplayType("inline");
		} else {
			form.addField("custpage_tax_percent", "text", "Tax Rate", null, "group2").setDisplayType("inline");
		}
		taxField.setBreakType("startcol");
		taxField.setDisplayType("normal");
	//	taxField.setMandatory(true);
	} else if (!_UseTaxRate && _UseCnclPct) {
		var cnclPctField = form.addField("custpage_cnclpct", "percent", "Cancellation Charge %", null, "group2");
		cnclPctField.setBreakType("startcol");
		cnclPctField.setDisplayType("normal");
		cnclPctField.setDefaultValue(_DefCnclPct);
	}
	
	if (_UseTaxRate && !_tools.isEmpty(taxField)) {
		var tgFilt = new Array(
				["isinactive","is","F"]
		);
		var tgCols = new Array(
				new nlobjSearchColumn("itemid", null, null)
		);
		tgCols[0].setSort();
		var results = _tools.getSearchResults(nlapiCreateSearch("taxgroup", tgFilt, tgCols).runSearch(), false);
		if (!_tools.isEmpty(results)) {
			taxField.addSelectOption("", "", true);
			results.forEach(function(res) {
				taxField.addSelectOption(res.getId(), res.getValue("itemid"));
			});
		}
	}
	
	//////////////////
	// Field Group #3 -- Show Vendors
	//////////////////
	
	
	//////////////////
	// Field Group #4 -- Show Floor Details
	//////////////////
	
	form.addField("custpage_advorderdate", "date", "Last Date for Advance Order Pricing", null, "datesmaintab");
	form.addField("custpage_whseshipdate", "date", "Warehouse Ship Date", null, "datesmaintab");
	form.addField("custpage_webstartdate", "date", "Website Start Date", null, "datesmaintab");
	form.addField("custpage_webenddate", "date", "Website End Date", null, "datesmaintab");
	
	//////////////////
	// Sublists
	//////////////////
	
	var timeList = getShowTimes();
	form = addDateSublists(form, timeList);
	
	var sdfList = form.addSubList("custpage_displayformlist", "list", "Web Categories", "displayforms");
	setDisplayFormList(sdfList, getDisplayForms());
	
	var freightList = form.addSubList("custpage_freightlist", "list", "Freight Table", "freighttab");
	setFreightList(freightList, getFreightItems());
	
	//////////////////
	// Additional Form Field Settings
	//////////////////
	
	showNameField.setMandatory(true);
	if (_scLib.UseJobNumbering) {
		showNumberField.setMandatory(true);
	} else {
		showNumberField.setDisplayType("disabled");
	}
	if (_UseCSJobs) {
		csJobField.setMandatory(true);
		if (!_tools.isEmpty(csJobParam)) {
			csJobField.setDefaultValue(csJobParam);
			csJobField.setDisplayType("disabled");
		}
	} else {
		fShowField.setMandatory(true);
		if (!_tools.isEmpty(jobParam)) {
			fShowField.setDefaultValue(jobParam);
			fShowField.setDisplayType("disabled");
		}
	}
	var apInfo = nlapiLoadConfiguration("accountingpreferences");
	if (apInfo.getFieldValue("classmandatory") == "T") {
		fShowField.setMandatory(true);
	}
	if (!_tools.isEmpty(jobParam) || !_tools.isEmpty(csJobParam)) {
		if (!_tools.isEmpty(showName)) {
			showNameField.setDefaultValue(showName);
			showNameField.setDisplayType("disabled");
		}
		if (_scLib.UseJobNumbering && !_tools.isEmpty(showNumber)) {
			showNumberField.setDefaultValue(showNumber);
			showNumberField.setDisplayType("disabled");
		}
	}
	if (_UseSubsidiaries) {
		subsidiaryField.setMandatory(true);
		subsidiaryField.setBreakType("startcol");
		if (!_tools.isEmpty(subsidiary)) {
			subsidiaryField.setDefaultValue(subsidiary);
		} else {
			subsidiaryField.setDefaultValue(_DefaultSubsidiary);
		}
	} else {
		
	}
	
	awShipRateField.setDefaultValue("0.00");
	ibShipRateField.setDefaultValue("0.00");
	dShipRateField.setDefaultValue("0.00");
	if (_scLib.DefaultOnSitePriceLvl != null) {
		showPLField.setDefaultValue(_scLib.DefaultOnSitePriceLvl);
	}
	if (_scLib.DefaultStdPriceLvl != null) {
		stdPLField.setDefaultValue(_scLib.DefaultStdPriceLvl);
	}
	if (_scLib.DefaultAdvPriceLvl != null) {
		advPLField.setDefaultValue(_scLib.DefaultAdvPriceLvl);
	}
	if (_scLib.DefaultShowMgmtPriceLvl != null) {
		showMgmtPLField.setDefaultValue(_scLib.DefaultShowMgmtPriceLvl);
	}
	
	awShipRateField.setBreakType("startcol");
	advPLField.setBreakType("startcol");
	
	showTypeField.setMandatory(true);
	if (!_tools.isEmpty(showType)) {
		showTypeField.setDefaultValue(showType);
	}
	
	form.addSubmitButton("Submit");
	
	response.writePage(form);
}

function buildShow(request, response) {
	var timeList = getShowTimes();
	
	var showName = request.getParameter("custpage_showname");
	var showNumber = request.getParameter("custpage_shownumber");
	var customer = request.getParameter("custpage_customer");
	var subsidiary = request.getParameter("custpage_subsidiary");
	var showType = request.getParameter("custpage_showtype");
	var department = request.getParameter("custpage_department");
	var location = request.getParameter("custpage_location");
	var eventVenue = request.getParameter("custpage_venue");
	var financialShow = request.getParameter("custpage_financialshow");
	var csJob = request.getParameter("custpage_cs_job");
	var advWhseShipRate = request.getParameter("custpage_awshiprate");
	var ibWhseShipRate = request.getParameter("custpage_ibshiprate");
	var directShipRate = request.getParameter("custpage_dshiprate");
	var stdPriceLevel = request.getParameter("custpage_stdpricelevel");
	var advPriceLevel = request.getParameter("custpage_advpricelevel");
	var sitePriceLevel = request.getParameter("custpage_showpricelevel");
	var siteMgmtPriceLevel = request.getParameter("custpage_showmgmtpricelevel");
	var eventTax = request.getParameter("custpage_taxrate");
	var eventTaxPct = request.getParameter("custpage_tax_percent");
	var gstRate = request.getParameter("custpage_gst_percent");
	var pstRate = request.getParameter("custpage_pst_percent");
	var cnclPct = request.getParameter("custpage_cnclpct");
	var advOrderDate = request.getParameter("custpage_advorderdate");
	var whseShipDate = request.getParameter("custpage_whseshipdate");
	var webStartDate = request.getParameter("custpage_webstartdate");
	var webEndDate = request.getParameter("custpage_webenddate");
	var showStatus = request.getParameter("custpage_show_status");
	
	var failure = false;
	var failureSubject = "";
	var failureErrName = "";
	var failureErrDesc = "";
	var errList = new Array();
	
	var showTableRecID = null;
	
		var showTableRec = nlapiCreateRecord("customrecord_show", { recordmode : "dynamic" , customform : _scLib.DefaultShowTableForm });
		
		var showNameComplete = "";
		
		if (_scLib.UseJobNumbering) {
			if (_scLib.NameNumberOrder == "1") {
				showNameComplete = "{0}{1}{2}".NG_Format(showNumber,_scLib.separators[_scLib.NameNumberSeparator],showName);
			} else {
				showNameComplete = "{0}{1}{2}".NG_Format(showName,_scLib.separators[_scLib.NameNumberSeparator],showNumber);
			}
		} else {
			showNameComplete = showName;
		}
		showTableRec.setFieldValue("name", showNameComplete);
		
		if (_UseSubsidiaries) {
			showTableRec.setFieldValue("custrecord_show_subsidiary", subsidiary);
		}
		
		showTableRec.setFieldValue("custrecord_show_type", showType);
		showTableRec.setFieldValue("custrecord_show_customer", customer);
		if (!_tools.isEmpty(csJob)) {
			showTableRec.setFieldValue("custrecord_show_job", csJob);
			showTableRec.setFieldValue("custrecord_58_cseg_ng_cs_job", csJob);
		}
		if (!_tools.isEmpty(financialShow)) {
			showTableRec.setFieldValue("custrecord_fin_show", financialShow);
		}
		showTableRec.setFieldValue("custrecord_show_status", showStatus);
		showTableRec.setFieldValue("custrecord_adv_wh_ship_rate", advWhseShipRate);
		showTableRec.setFieldValue("custrecord_inbetween_ship_rate", ibWhseShipRate);
		showTableRec.setFieldValue("custrecord_direct_shipping_rate", directShipRate);
		showTableRec.setFieldValue("custrecord_std_price_level", stdPriceLevel);
		showTableRec.setFieldValue("custrecord_adv_price_level", advPriceLevel);
		showTableRec.setFieldValue("custrecord_site_price_level", sitePriceLevel);
		showTableRec.setFieldValue("custrecord_show_mgmnt_price_lvl", siteMgmtPriceLevel);
		if (!_tools.isEmpty(eventVenue)) {
			showTableRec.setFieldValue("custrecord_facility", eventVenue);
			var templateData = nlapiLookupField("customrecord_ng_cs_settings", 1, ["custrecord_ng_cs_fclty_addy_template","custrecord_ng_cs_name_from_subsidiary","custrecord_ng_cs_booth_num_line_text"]);
			var facData = nlapiLookupField("customrecord_facility", eventVenue, [
				"custrecord_facility_address1","custrecord_facility_address2","custrecord_facility_city","custrecord_facility_state","custrecord_facility_zip"
			]);
			var boothLine = templateData.custrecord_ng_cs_booth_num_line_text || _BOOTH_LINE;
			var compName = GetCompanyName(templateData.custrecord_ng_cs_name_from_subsidiary == "T", subsidiary);
			var custName = !_tools.isEmpty(customer) ? nlapiLookupField("customer", customer, "entityid") : "";
			var stateAbbrev = !_tools.isEmpty(facData['custrecord_facility_state']) && facData['custrecord_facility_state'].length > 0 ? getStateAbbrev(facData['custrecord_facility_state'][0].text) : "";
			var addressLine = "{0}{1}".NG_Format(facData['custrecord_facility_address1'], !_tools.isEmpty(facData['custrecord_facility_address2']) ? "\n{0}".NG_Format(facData['custrecord_facility_address2']) : "");
			var addressMerge = templateData.custrecord_ng_cs_fclty_addy_template.NG_Format(showName, custName, boothLine, compName, addressLine, facData['custrecord_facility_city'], stateAbbrev, facData['custrecord_facility_zip']);
			showTableRec.setFieldValue("custrecord_ship_to_facility_address", addressMerge);
		}
		if (_UseTaxRate) {
			if (!_tools.isEmpty(eventTax)) {
				showTableRec.setFieldValue("custrecord_tax_rate", eventTax);
				var taxData, taxCols;
				if (_scLib.UseCanadianSalesTax) {
					taxCols = ["unitprice1", "unitprice2"];
				} else {
					taxCols = ["rate"];
				}
				try {
					taxData = nlapiLookupField("taxgroup", eventTax, taxCols);
				} catch (err) {
					_log.logError(err, "Error encountered getting tax rate from the tax group item");
				}
				
				if (!_tools.isEmpty(taxData)) {
					if (_scLib.UseCanadianSalesTax) {
						showTableRec.setFieldValue("custrecord_ng_cs_evt_gst_pct", taxData.unitprice1.replace("%", ""));
						showTableRec.setFieldValue("custrecord_ng_cs_evt_pst_pct", taxData.unitprice2.replace("%", ""));
						taxCols = ["unitprice1", "unitprice2"];
					} else {
						showTableRec.setFieldValue("custrecord_tax_percent", taxData.rate.replace("%", ""));
						taxCols = ["rate"];
					}
				}
			}
		}
		if (_UseCnclPct) {
			showTableRec.setFieldValue("custrecord_cancellation_pct", cnclPct);
		}
		if (_UseLocations) {
			showTableRec.setFieldValue("custrecord_show_venue", location);
			try {
				if (!_tools.isEmpty(location)) {
					var templateData = nlapiLookupField("customrecord_ng_cs_settings", 1, ["custrecord_ng_cs_wrhs_addy_template","custrecord_ng_cs_name_from_subsidiary","custrecord_ng_cs_booth_num_line_text"]);
					var lData = nlapiLookupField("location", location, ["address.address1","address.address2","address.city","address.state","address.zip","address.country"]);
					var boothLine = templateData.custrecord_ng_cs_booth_num_line_text || _BOOTH_LINE;
					var compName = GetCompanyName(templateData.custrecord_ng_cs_name_from_subsidiary == "T", subsidiary);
					var custName = !_tools.isEmpty(customer) ? nlapiLookupField("customer", customer, "entityid") : "";
					var addressLine = "{0}{1}".NG_Format(lData['address.address1'], !_tools.isEmpty(lData['address.address2']) ? "\n{0}".NG_Format(lData['address.address2']) : "");
					var addressMerge = templateData.custrecord_ng_cs_wrhs_addy_template.NG_Format(showName, custName, boothLine, compName, addressLine, lData['address.city'], lData['address.state'], lData['address.zip']);
					showTableRec.setFieldValue("custrecord_ship_to_warehouse_address", addressMerge);
				}
			} catch (err) {
				_log.logError(err, "Error encountered building 'ship to warehouse' address");
			}
		}
		
		showTableRec.setFieldValue("custrecord_adv_ord_date", advOrderDate);
		showTableRec.setFieldValue("custrecord_wh_ship_date", whseShipDate);
		showTableRec.setFieldValue("custrecord_w_start_date", webStartDate);
		showTableRec.setFieldValue("custrecord_w_end_date", webEndDate);
		
		try {
			showTableRecID = nlapiSubmitRecord(showTableRec, true, true);
		} catch (err) {
			_log.logError(err, "Error encountered creating event record");
			failure = true;
			failureSubject = "Error encountered creating event record";
			failureErrName = err.name;
			failureErrDesc = err.message;
		}
		
		if (!_tools.isEmpty(showTableRecID)) {
			try {
				var fields = new Array();
				var values = new Array();
				var searchID = null;
				
				try {
					var gFilt = new Array(
							["custrecord_booth_contact.custrecord_booth_show_table","anyof",[showTableRecID]]
						,	"and"
						,	["isinactive","is","F"]
					);
					var gCols = new Array(
							new nlobjSearchColumn("entityid", null, null)
						,	new nlobjSearchColumn("email", null, null)
						,	new nlobjSearchColumn("phone", null, null)
						,	new nlobjSearchColumn("altphone", null, null)
						,	new nlobjSearchColumn("fax", null, null)
						,	new nlobjSearchColumn("company", null, null)
						,	new nlobjSearchColumn("altemail", null, null)
						,	new nlobjSearchColumn("custentity_ng_cs_last_imported_on", null, null)
						,	new nlobjSearchColumn("custrecord_booth_number", "custrecord_booth_contact", null)
						,	new nlobjSearchColumn("custrecord_booth_exhibitor", "custrecord_booth_contact", null)
						,	new nlobjSearchColumn("custrecord_booth_contact", "custrecord_booth_contact", null)
						,	new nlobjSearchColumn("internalid", "custrecord_booth_contact", null)
					);
					var gcSearch = nlapiCreateSearch("contact", gFilt, gCols);
					gcSearch.setIsPublic(true);
					searchID = gcSearch.saveSearch("Contact Search: {0}".NG_Format(showName), "customsearch_evt_contact_srch_{0}".NG_Format(showTableRecID));
					fields.push("custrecord_cs_st_contact_search");
					values.push(searchID);
				} catch (err) {
					_log.logError(err, "Error encountered generating event contact search", "Event: {0}".NG_Format(showName));
				}
				
				if (!_tools.isEmpty(searchID)) {
					try {
						var gName = "Contact Group: {0}".NG_Format(showName);
						if (gName.length > 51) {
							gName = "Contact Grp: {0}".NG_Format(showName);
							if (gName.length > 51) {
								var rnd = _tools.randomString(0, 5);
								gName = "{0} {1}".NG_Format(gName.substr(0, 44), rnd);
							}
						}
						var egRec = nlapiCreateRecord("entitygroup", { grouptype : "Contact" , dynamic : "T" , recordmode : "dynamic" });
						egRec.setFieldValue("groupname", gName);
						egRec.setFieldValue("groupowner", nlapiGetUser());
						egRec.setFieldValue("restrictedtoowner", "T");
						egRec.setFieldValue("comments", _EG_COMMENTS.NG_Format(showName.toUpperCase()));
						egRec.setFieldValue("savedsearch", searchID);
						var egID = nlapiSubmitRecord(egRec, true, false);
						fields.push("custrecord_cs_st_contact_group");
						values.push(egID);
					} catch (err) {
						_log.logError(err, "Error encountered generating event contact group", "Event: {0}".NG_Format(showName));
					}
				}
				
				try {
					var cmpnRec = nlapiCreateRecord("campaign", NewGen.lib.dynRec);
					cmpnRec.setFieldValue("title", "Event Campaign: {0}".NG_Format(showName));
					cmpnRec.setFieldValue("owner", nlapiGetUser());
					cmpnRec.setFieldValue("message", _EG_COMMENTS.NG_Format(showName.toUpperCase()));
					var cmpnID = nlapiSubmitRecord(cmpnRec, true, false);
					fields.push("custrecord_ng_cs_st_show_campaign");
					values.push(cmpnID);
				} catch (err) {
					_log.logError(err, "Error encountered generating event campaign", "Event: {0}".NG_Format(showName));
				}
				
				if (!_tools.isEmpty(egID)) {
					nlapiSubmitField("customrecord_show", showTableRecID, fields, values);
				}
			} catch (err) {
				_log.logError(err, "Error encountered creating event contact search/group/campaign");
			}
			
			var sd = createShowDateRecords(showTableRecID, request, timeList);
			var ft = createFreightTableItems(request, showTableRecID, { advance : advWhseShipRate , inbetween : ibWhseShipRate , onsite : directShipRate });
			_log.logInfo("Event Date Data", JSON.stringify(sd));
			if (sd != null) {
				if (sd.f) {
					if (!_tools.isEmpty(sd.el) && sd.el.length > 0) {
						errList.concat(sd.el);
					} else {
						errList.push({
								failsubj : sd.a
							,	errname : sd.b
							,	errdesc : sd.c
						});
					}
				} else if (sd.ids != null && sd.ids.length > 0) {
					if (!_tools.isEmpty(sd.el) && sd.el.length > 0) {
						errList.concat(sd.el);
					}
					if (!_tools.isEmpty(_scLib['LaborDateTypes']) && _scLib['LaborDateTypes'].length > 0) {
						
					} else {
						_log.logInfo("Labor Scheduling", "No labor scheduling done; Labor date types not selected in settings");
					}
				}
			}
			if (ft != null) {
				if (!_tools.isEmpty(ft.el) && ft.el.length > 0) {
					errList.concat(ft.el);
				} else if (!_tools.isEmpty(ft.a)) {
					errList.push({
							failsubj : ft.a
						,	errname : ft.b
						,	errdesc : ft.c
					});
				}
			}
			
			var displayFormsCount = request.getLineItemCount("custpage_displayformlist");
			if (displayFormsCount > 0) {
				for (var d = 1; d <= displayFormsCount; d++) {
					var dispFormRec = nlapiCreateRecord("customrecord_show_display_forms");
					
					var showForm = request.getLineItemValue("custpage_displayformlist", "selected", d);
					if (showForm == "T") {
						dispFormRec.setFieldValue("custrecord_sdf_wshow", "T");
					} else {
						dispFormRec.setFieldValue("custrecord_sdf_wshow", "F");
					}
					dispFormRec.setFieldValue("custrecord_sdf_display_form", request.getLineItemValue("custpage_displayformlist", "custcol_internalid", d));
					dispFormRec.setFieldValue("custrecord_sdf_show_table", showTableRecID);
					dispFormRec.setFieldValue("custrecord_sdf_group", request.getLineItemValue("custpage_displayformlist", "custcol_groupid", d));
					
					try {
						nlapiSubmitRecord(dispFormRec);
					} catch (err) {
						_log.logError(err, "Error encountered setting event web category record");
						errList.push({
								failsubj : "Error encountered setting event web category record"
							,	errname : err.name
							,	errdesc : err.message
						});
					}
				}
			}
		}
	
	var status = { };
	status.fail = failure;
	status.failsubj = failureSubject;
	status.errList = errList;
	status.errname = failureErrName;
	status.errdesc = failureErrDesc;
	status.stid = showTableRecID;
	
	displayForm(request, response, status);
}

/*
function ng_FieldChanged(type, name, linenum) {
	console.log("field changed event");
	if (name == "custpage_siderail") {
		var value = nlapiGetFieldValue(name);
		if (value == "T") {
			nlapiDisableField("custpage_siderailcolor", false);
		} else {
			nlapiSetFieldValue("custpage_siderailcolor", null);
			nlapiDisableField("custpage_siderailcolor", true);
		}
	}
	
	if (name == "custpage_aislecarpet") {
		var value = nlapiGetFieldValue(name);
		if (value == "T") {
			nlapiDisableField("custpage_aislecarpetcolor", false);
		} else {
			nlapiSetFieldValue("custpage_aislecarpetcolor", null);
			nlapiDisableField("custpage_aislecarpetcolor", true);
		}
	}
	
	if (name == "custpage_aislesigns") {
		var value = nlapiGetFieldValue(name);
		if (value == "T") {
			nlapiDisableField("custpage_aislesigncolor", false);
		} else {
			nlapiSetFieldValue("custpage_aislesigncolor", null);
			nlapiDisableField("custpage_aislesigncolor", true);
		}
	}
	
	if (name == "custpage_facility") {
		var facility = nlapiGetFieldValue(name);
		if (_tools.isEmpty(facility)) {
			for (var n = _HallArray.length; n > 0; n--) {
				nlapiRemoveSelectOption("custpage_hall", _HallArray[n]);
				_HallArray.pop();
			}
		} else {
			for (var n = _HallArray.length; n > 0; n--) {
				nlapiRemoveSelectOption("custpage_hall", _HallArray[n]);
				_HallArray.pop();
			}
			var filt = new Array(
					["isinactive","is","F"]
				,	"and"
				,	["custrecord_hall_facility","anyof",[facility]]
			);
			var cols = new Array(
					new nlobjSearchColumn("name", null, null)
			);
			var search = null;
			try {
				search = nlapiSearchRecord("customrecord_exhibition_hall", null, filt, cols);
			} catch (err) {
				_log.logError(err, "Error encountered retrieving facility hall list");
			}
			if (search != null) {
				for (var i = 0; i < search.length; i++) {
					nlapiInsertSelectOption("custpage_hall", search[i].getId(), search[i].getValue("name"));
					_HallArray.push(search[i].getId());
				}
			}
		}
	}
	
	if (name == "custpage_boothpackage") {
		var value = nlapiGetFieldValue(name);
		if (value == "T") {
			nlapiDisableField("custpage_boothpackperexhb", false);
			nlapiDisableField("custpage_boothpacksize", false);
			nlapiDisableField("custpage_boothpackdesc", false);
		} else {
			nlapiSetFieldValue("custpage_boothpackperexhb", null);
			nlapiDisableField("custpage_boothpackperexhb", true);
			nlapiSetFieldValue("custpage_boothpacksize", null);
			nlapiDisableField("custpage_boothpacksize", true);
			nlapiSetFieldValue("custpage_boothpackdesc", null);
			nlapiDisableField("custpage_boothpackdesc", true);
		}
	}
	
	if (name == "custpage_boothcount" || name == "custpage_estattend" || name == "custpage_dshiprate" || name == "custpage_awshiprate") {
		var value = nlapiGetFieldValue(name);
		if (value < 0) {
			window.alert("You cannot enter a negative value into this field.");
			nlapiSetFieldValue(name, 0, false);
		}
	}
	
	if (name == "custpage_boothsize") {
		var boothSize = nlapiGetFieldValue(name);
		var boothSizeText = nlapiGetFieldText(name);
		if (_tools.isEmpty(boothSize)) {
			nlapiDisableField("custpage_boothlength", true);
			nlapiDisableField("custpage_boothwidth", true);
			
			nlapiSetFieldValue("custpage_boothlength", 0, false);
			nlapiSetFieldValue("custpage_boothwidth", 0, false);
		} else if (boothSizeText == "Other") {
			nlapiDisableField("custpage_boothlength", false);
			nlapiDisableField("custpage_boothwidth", false);
			
			nlapiSetFieldValue("custpage_boothlength", 0, false);
			nlapiSetFieldValue("custpage_boothwidth", 0, false);
		} else {
			nlapiDisableField("custpage_boothlength", true);
			nlapiDisableField("custpage_boothwidth", true);
			
			var boothText = nlapiGetFieldText("custpage_boothsize");
			var sizeVals = boothText.split(" ");
			var l = new Number(sizeVals[0]);
			var w = new Number(sizeVals[2]);
			nlapiSetFieldValue("custpage_boothwidth", w, false);
			nlapiSetFieldValue("custpage_boothlength", l, false);
		}
	}
	
	if (name == "custpage_taxrate") {
		console.log("tax rate change");
		var taxItem = nlapiGetFieldValue("custpage_taxrate");
		if (!_tools.isEmpty(taxItem)) {
			console.log("tax item set -- ", taxItem);
			var rateData = nlapiLookupField("taxgroup", taxItem, ["rate","unitprice1","unitprice2"]);
			if (_scLib.UseCanadianSalesTax) {
				console.log("setting CA tax rate fields");
				nlapiSetFieldValue("custpage_gst_percent", rateData.unitprice1.replace("%", ""));
				nlapiSetFieldValue("custpage_pst_percent", rateData.unitprice2.replace("%", ""));
			} else {
				console.log("setting standard tax rate field");
				nlapiSetFieldValue("custpage_tax_percent", rateData.rate.replace("%", ""));
			}
		} else {
			if (_scLib.UseCanadianSalesTax) {
				nlapiSetFieldValue("custpage_gst_percent", "");
				nlapiSetFieldValue("custpage_pst_percent", "");
			} else {
				nlapiSetFieldValue("custpage_tax_percent", "");
			}
		}
	}
}

function ng_ValidateField(type, name, linenum) {
	if (_tools.isInArray(name, ["custpage_shownumber","custpage_showname"])) {
		var showNumber = nlapiGetFieldValue("custpage_shownumber");
		var showName = nlapiGetFieldValue("custpage_showname");
		var fullName = "";
		
		if (!_tools.isEmpty(showName) && !_tools.isEmpty(showNumber)) {
			if (_scLib.UseJobNumbering) {
				if (_scLib.NameNumberOrder == "1") {
					fullName = "{0}{1}{2}".NG_Format(showNumber,_scLib.separators[_scLib.NameNumberSeparator],showName);
				} else {
					fullName = "{0}{1}{2}".NG_Format(showName,_scLib.separators[_scLib.NameNumberSeparator],showNumber);
				}
			} else {
				fullName = showName;
			}
			
			var filt = new Array(
					["name","is",fullName]
			);
			var search = null;
			try {
				search = nlapiSearchRecord("customrecord_show", null, filt, null);
			} catch (err) {
				window.alert("There was a problem verifying existence of another event with this name:\n\n[{0}] {1}".NG_Format(err.name,err.message));
			}
			
			if (!_tools.isEmpty(search)) {
				window.alert("An event already exists with this name/number. Please verify your entry.");
				return false;
			}
		}
	}
	
	return true;
}

function validateDateLine(type) {
	
}
*/

function createShowDateRecords(showTableId, request, timeList) {
	var failure = false;
	var failureSubject = "";
	var failureErrName = "";
	var failureErrDesc = "";
	var errList = new Array();
	
	var dtSearch = getShowDateTypes();
	var dateIDs = new Array();
	
	if (dtSearch != null) {
		for (var dt = 0; dt < dtSearch.length; dt++) {
			var dtID = dtSearch[dt].getId();
			var dtName = dtSearch[dt].getValue("name");
			var sl = "custpage_datetype_{0}".NG_Format(dtID);
			var dateTypeCount = request.getLineItemCount(sl);
			if (dateTypeCount > 0) {
				for (var d = 1; d <= dateTypeCount; d++) {
					var dsVal = timeList[request.getLineItemValue(sl, "custcol_starttime", d)];
					var deVal = timeList[request.getLineItemValue(sl, "custcol_endtime", d)];
					var showDate = request.getLineItemValue(sl, "custcol_date", d);
					
					var dateRec = nlapiCreateRecord("customrecord_show_date");
					dateRec.setFieldValue("custrecord_show_number_date", showTableId);
					dateRec.setFieldValue("custrecord_date_type", dtID);
					dateRec.setFieldValue("custrecord_date", showDate);
					dateRec.setFieldValue("custrecord_start_time", dsVal);
					dateRec.setFieldValue("custrecord_end_time", deVal);
					
					try {
						dateIDs.push(nlapiSubmitRecord(dateRec));
					} catch (err) {
						_log.logError(err, "Error encountered setting {0} date record for {1}".NG_Format(dtName,showDate));
						errList.push({
								failsubj : "Error encountered creating {0} date record for {1}".NG_Format(dtName,showDate)
							,	errname : err.name
							,	errdesc : err.message
						});
					}
				}
			}
		}
	}
	
	if (failure) {
		return { f : true , a : failureSubject , b : failureErrName , c : failureErrDesc , el : errList };
	} else {
		return { ids : dateIDs , el : errList };
	}
}

function createLaborSchedule(showTableId, idList) {
	var dFilt = new Array(
			["custrecord_show_number_date","anyof",[showTableId]]
		,	"and"
		,	["custrecord_date_type","anyof",_scLib['LaborDateTypes']]
	);
	var dCols = new Array(
			new nlobjSearchColumn("custrecord_date", null, null)
		,	new nlobjSearchColumn("custrecord_date_type", null, null)
	);
	var dSearch = null;
	try {
		dSearch = nlapiSearchRecord("customrecord_show_date", null, dFilt, dCols);
	} catch (err) {
		_log.logError(err, "Error encountered retrieving all event dates");
	}
	if (dSearch != null) {
		_log.logInfo("Event Date Count", "Created Count: {0} -- Labor Match Count: {1}".NG_Format(idList.length,dSearch.length));
		var dayList = new Array();
		var dayMap = { };
		for (var d = 0; d < dSearch.length; d++) {
			var sDateType = dSearch[d].getValue("custrecord_date_type");
			var sDateTypeText = dSearch[d].getValue("custrecord_date_type");
			var sDateText = dSearch[d].getValue("custrecord_date");
			if (!_tools.isInArray(sDateType, _scLib['LaborDateTypes'])) {
				_log.logInfo("Skipping event date", "Date type is not listed in labor types -- Date: {0} -- Type: {1} ({2})".NG_Format(sDateText,sDateTypeText,sDateType));
				continue;
			}
			var sDate = nlapiStringToDate(sDateText);
			var day = Math.round(sDate.getDay() + 1).toFixed(0);
			if (_tools.isEmpty(dayMap[sDateText])) {
				dayMap[sDateText] = day;
			}
			if (!_tools.isInArray(day, dayList)) {
				dayList.push(day);
			}
		}
		_log.logInfo("Day List", JSON.stringify(dayList));
		_log.logInfo("Day Map", JSON.stringify(dayMap));
		var lmFilt = new Array(
				["custrecord_ng_cs_day_of_week","anyof",dayList]
			,	"and"
			,	["isinactive","is","F"]
		);
		var lmCols = new Array(
				new nlobjSearchColumn("custrecord_ng_cs_day_of_week", null, null)
			,	new nlobjSearchColumn("custrecord_ng_cs_master_start_time", null, null)
			,	new nlobjSearchColumn("custrecord_ng_cs_master_end_time", null, null)
			,	new nlobjSearchColumn("custrecord_ng_cs_master_labor_type", null, null)
			,	new nlobjSearchColumn("custrecord_ng_cs_master_rate_multiplier", null, null)
			,	new nlobjSearchColumn("custrecord_ng_cs_master_super_markup", null, null)
		);
		var lmSearch = null;
		try {
			lmSearch = nlapiSearchRecord("customrecord_ng_cs_show_mstr_labor_schd", null, lmFilt, lmCols);
		} catch (err) {
			_log.logError(err, "Error encountered retrieving master labor schedule data");
		}
		var createdCounter = new Number(0);
		if (lmSearch != null) {
			for (var date in dayMap) {
				for (var lm = 0; lm < lmSearch.length; lm++) {
					var lDays = lmSearch[lm].getValue("custrecord_ng_cs_day_of_week").split(",");
					if (_tools.isInArray(dayMap[date], lDays)) {
						try {
							var laborRec = nlapiCreateRecord("customrecord_ng_cs_show_labor_schedule");
							laborRec.setFieldValue("custrecord_ng_cs_labor_show", showTableId);
							laborRec.setFieldValue("custrecord_ng_cs_labor_date", date);
							laborRec.setFieldValue("custrecord_ng_cs_labor_start", lmSearch[lm].getValue("custrecord_ng_cs_master_start_time"));
							laborRec.setFieldValue("custrecord_ng_cs_labor_end", lmSearch[lm].getValue("custrecord_ng_cs_master_end_time"));
							laborRec.setFieldValue("custrecord_ng_cs_labor_type", lmSearch[lm].getValue("custrecord_ng_cs_master_labor_type"));
							laborRec.setFieldValue("custrecord_ng_cs_labor_multiplier", lmSearch[lm].getValue("custrecord_ng_cs_master_rate_multiplier"));
							laborRec.setFieldValue("custrecord_ng_cs_supervisor_markup", lmSearch[lm].getValue("custrecord_ng_cs_master_super_markup"));
							nlapiSubmitRecord(laborRec);
							createdCounter++;
						} catch (err) {
							_log.logError(err, "Error encountered creating event labor schedule record");
						}
					}
				}
			}
		} else {
			_log.logInfo("No master labor schedule record info");
		}
		_log.logInfo("Labor schedule created count", "{0}".NG_Format(createdCounter));
	} else {
		_log.logInfo("No event date record info");
	}
}

function createFreightTableItems(request, showTableId, rates) {
	var failure = false;
	var failureSubject = "";
	var failureErrName = "";
	var failureErrDesc = "";
	var errList = new Array();
	
	var sl = "custpage_freightlist";
	var freightLines = request.getLineItemCount(sl);
	if (freightLines > 0) {
		for (var l = 1; l <= freightLines; l++) {
			if (request.getLineItemValue(sl, "custcol_addfreight", l) == "T") {
				var ftRec = nlapiCreateRecord("customrecord_freight_table", { recordmode : "dynamic" });
				ftRec.setFieldValue("custrecord_show_freight", showTableId);
				ftRec.setFieldValue("custrecord_freight_item", request.getLineItemValue(sl, "custcol_internalid", l));
				ftRec.setFieldValue("custrecord_freight_rate", (new Number(request.getLineItemValue(sl, "custcol_baseprice", l))).toFixed(2));
				ftRec.setFieldValue("custrecord_pre_show_rate", rates.advance);
				ftRec.setFieldValue("custrecord_inbetween_rate", rates.inbetween);
				ftRec.setFieldValue("custrecord_on_site_rate", rates.onsite);
				
				try {
					nlapiSubmitRecord(ftRec);
				} catch (err) {
					_log.logError("error encountered submitting freight table record");
					errList.push({
							failsubj : "Error encountered creating freight table record"
						,	errname : err.name
						,	errdesc : err.message
					});
				}
			}
		}
	}
	
	if (failure) {
		return { a : failureSubject , b : failureErrName , c : failureErrDesc , el : errList };
	} else {
		if (errList.length > 0) {
			return { el : errList };
		} else {
			return null;
		}
	}
}

function getShowTimes() {
	var timeSearch = null;
	var timeListObj = { };
	var timeListArr = new Array();
	var timeList = { };
	try {
		var cols = new Array(
				new nlobjSearchColumn("internalid", null, null)
			,	new nlobjSearchColumn("name", null, null)
		);
		
		timeSearch = nlapiSearchRecord("customrecord_time", null, null, cols);
		if (timeSearch != null) {
			for (var s = 0; s < timeSearch.length; s++) {
				timeListArr.push(new Number(timeSearch[s].getValue("internalid")));
				timeListObj[timeSearch[s].getValue("internalid")] = timeSearch[s].getValue("name"); 
			}
		} else {
			_log.logDebug("no custom list search results");
		}
	} catch (err) {
		_log.logError(err, "Error with time custom list search");
	}
	
	timeListArr.sort(function(a,b){return a-b;});
	for (var t = 0; t < timeListArr.length; t++) {
		timeList[timeListArr[t] + ""] = timeListObj[timeListArr[t] + ""];
	}
			
	return timeList;
}

function addDateSublists(form, timeList) {
	var dtSearch = getShowDateTypes();
	
	if (dtSearch != null) {
		for (var dt = 0; dt < dtSearch.length; dt++) {
			var dtID = dtSearch[dt].getId();
			var dtName = dtSearch[dt].getValue("name");
			var tab = "dt_tab_{0}".NG_Format(dtID);
			var sl = "custpage_datetype_{0}".NG_Format(dtID);
			var title = "{0} Dates".NG_Format(dtName);
			form.addSubTab(tab, title, "datesmaintab");
			var dtSubList = form.addSubList(sl, "inlineeditor", title, tab);
			
			dtSubList.addField("custcol_date", "date", "Date").setMandatory(true);
			addTimeSelectOption(dtSubList.addField("custcol_starttime", "select", "Start Time"), timeList);
			addTimeSelectOption(dtSubList.addField("custcol_endtime", "select", "End Time"), timeList);
		}
	}

	return form;
}

function getDisplayForms() {
	var dispfor = { };
	var filt = new Array(
			["isinactive","is","F"]
	);
	var cols = new Array(
			new nlobjSearchColumn("custrecord_form_group", null, null)
		,	new nlobjSearchColumn("custrecord_form_name", null, null)
		,	new nlobjSearchColumn("custrecord_form_active", null, null)
	);
	var search = null;
	try {
		search = nlapiSearchRecord("customrecord_display_forms", null, filt, cols);
	} catch (err) {
		nlapiLogExecution("ERROR", "Error encountered retrieving CS web category records", "[" + err.name + "] " + err.message);
		return null;
	}
	
	if (search != null) {
		for (var s = 0; s < search.length; s++) {
			var group = search[s].getText("custrecord_form_group");
			var groupId = search[s].getValue("custrecord_form_group");
			var name = search[s].getValue("custrecord_form_name");
			var active = search[s].getValue("custrecord_form_active");
			var nameobj = { };
			nameobj.name = name;
			nameobj.active = active;
			nameobj.id = search[s].getId();
			nameobj.gid = groupId;
			
			if (dispfor[group] == null) {
				dispfor[group] = new Array();
			}
			
			dispfor[group].push(nameobj);
		}
	}
	
	return dispfor;
}

function getShowDateTypes() {
	var dtFilt = new Array(
			["isinactive","is","F"]
	);
	var dtCols = new Array(
			new nlobjSearchColumn("custrecord_ng_dt_sequence", null, null)
		,	new nlobjSearchColumn("name", null, null)
	);
	dtCols[0].setSort();
	var dtSearch = null;
	try {
		dtSearch = nlapiSearchRecord("customrecord_ng_cs_date_types", null, dtFilt, dtCols);
	} catch (err) {
		_log.logError(err, "Error encountered retrieving date type records");
	}
	return dtSearch;
}

function setDisplayFormList(sublist, dispfor) {
	sublist.addMarkAllButtons();
	sublist.addField("selected", "checkbox", "Display Web Category");
	sublist.addField("custcol_group", "text", "Form Group");
	sublist.addField("custcol_name", "text", "Form Name");
	var idField = sublist.addField("custcol_internalid", "text", "");
	idField.setDisplayType("hidden");
	var gidField = sublist.addField("custcol_groupid", "text", "");
	gidField.setDisplayType("hidden");
	
	var lineCounter = new Number(1);
	for (var key in dispfor) {
		var forms = dispfor[key];
		for (var f = 0; f < forms.length; f++) {
			if (forms[f].active == "T") {
				sublist.setLineItemValue("selected", lineCounter, forms[f].active);
			} else {
				sublist.setLineItemValue("custcol_hideform", lineCounter, "T");
			}
			sublist.setLineItemValue("custcol_group", lineCounter, key);
			sublist.setLineItemValue("custcol_name", lineCounter, forms[f].name);
			sublist.setLineItemValue("custcol_internalid", lineCounter, forms[f].id);
			sublist.setLineItemValue("custcol_groupid", lineCounter, forms[f].gid);
			
			lineCounter++;
		}
	}
}

function getFreightItems() {
	var freightList = new Array();
	var filt = new Array(
			["isinactive","is","F"]
		,	"and"
		,	[_scLib.fields.item.freight,"is","T"]
	);
	var cols = new Array(
			new nlobjSearchColumn("itemid", null, null)
		,	new nlobjSearchColumn("displayname", null, null)
		,	new nlobjSearchColumn("salesdescription", null, null)
		,	new nlobjSearchColumn("baseprice", null, null)
	);
	var search = null;
	try {
		search = nlapiSearchRecord("item", null, filt, cols);
	} catch (err) { }
	
	if (search != null) {
		for (var i = 0; i < search.length; i++) {
			freightList.push({
					custcol_addfreight : "T"
				,	custcol_internalid : search[i].getId()
				,	custcol_name : search[i].getValue("itemid")
				,	custcol_altname : search[i].getValue("displayname")
				,	custcol_description : (search[i].getValue("salesdescription") || "").substr(0, 300)
				,	custcol_baseprice : search[i].getValue("baseprice")
			});
		}
	}
	
	return freightList;
}

function setFreightList(sublist, freightList) {
	sublist.addMarkAllButtons();
	sublist.addField("custcol_addfreight", "checkbox", "Add to Show");
	sublist.addField("custcol_name", "text", "Freight Item");
	sublist.addField("custcol_altname", "text", "Alt Name");
	sublist.addField("custcol_description", "text", "Description");
	var idField = sublist.addField("custcol_internalid", "text", "");
	idField.setDisplayType("hidden");
	var priceField = sublist.addField("custcol_baseprice", "text", "");
	priceField.setDisplayType("hidden");
	sublist.setLineItemValues(freightList);
}

function addTimeSelectOption(field, values) {
	field.addSelectOption("", "", true);
	for (var t in values) {
		field.addSelectOption(t, values[t]);
	}
	field.setMandatory(true);
}

function AddFilesToSelect(form, field) {
	var cols = new Array(
			new nlobjSearchColumn("internalid", null, null)
		,	new nlobjSearchColumn("name", null, null)
	);
	
	var search = null;
	
	try {
		search = nlapiSearchRecord("file", "customsearch_image_search", null, cols);
	} catch (err) {
		_log.logError(err, "Image Search Error");
		var errField = form.addField("custpage_err", "text", "Image Search Error", null, "misc");
		errField.setDefaultValue(("[{0}] {1}".NG_Format(err.name,err.message)).substr(0, 300));
		errField.setDisplayType("inline");
		return;
	}
	
	if (search != null) {
		for (var i = 0; i < search.length; i++) {
			var name = search[i].getValue("name");
			var id = search[i].getValue("internalid");
			field.addSelectOption(id, name);
		}
	}
}

function onShowSave() {
	try {
		var imageFile = nlapiGetFieldValue("custpage_uploadimage");
		if (!_tools.isEmpty(imageFile)) {
			var extPos = imageFile.lastIndexOf(".");
			if (extPos <= 0) {
				window.alert("You have selected an invalid file for image upload. Please select an image file of type GIF, JPEG, or PNG. (1)");
				return false;
			} else {
				var ext = imageFile.substr(extPos + 1).toUpperCase();
				if (ext != "JPG" && ext != "JPEG" && ext != "PNG" && ext != "GIF") {
					window.alert("You have selected an invalid file for image upload. Please select an image file of type GIF, JPEG, or PNG. (2)");
					return false;
				}
			}
		}
	} catch (err) {
		_log.logError(err, "Error validating event image file");
		window.alert("Error validating event image file:\n[{0}] {1}".NG_Format(err.name, err.message));
		return false;
	}
	
	try {
		var kitFile = nlapiGetFieldValue("custpage_uploadexhbkit");
		if (!_tools.isEmpty(kitFile)) {
			var extPos = kitFile.lastIndexOf(".");
			if (extPos <= 0) {
				window.alert("You have selected an invalid file for exhibitor kit upload. Please select a file of type PDF. (1)");
				return false;
			} else {
				var ext = kitFile.substr(extPos + 1).toUpperCase();
				if (ext != "PDF") {
					window.alert("You have selected an invalid file for exhibitor kit upload. Please select a file of type PDF. (2)");
					return false;
				}
			}
		}
	} catch (err) {
		_log.logError(err, "Error validating show exhibitor kit file");
		window.alert("Error validating event exhibitor kit file:\n[{0}] {1}".NG_Format(err.name, err.message));
		return false;
	}
	
	if (_ValidateDates) {
		var showDateTypeId = _scLib.DefaultShowDateType; //null;
		
		if (!_tools.isEmpty(showDateTypeId)) {
			var sl = "custpage_datetype_{0}".NG_Format(showDateTypeId);
			var sdCount = nlapiGetLineItemCount(sl); 
			if (sdCount < 1) {
				if (!window.confirm("You have not set any event dates for this event. Is this correct?")) {
					return false;
				}
			}
		}
	}
	
	return true;
}

function onPageInit() {
	
}

function GetCompanyName(useSubsidiaryName, subId) {
	var name = "";
	if (_UseSubsidiaries && useSubsidiaryName) {
		if (!_tools.isEmpty(subId)) {
			var sData = nlapiLookupField("subsidiary", subId, ["name"]);
		}
	} else {
		var cInfo = nlapiLoadConfiguration("companyinformation");
		name = cInfo.getFieldValue("companyname");
	}
	
	return name;
}

function getStateAbbrev(state) {
	var stateList = NewGen.lib.states.US;
	var stateAbbrev = "";
	for (var s = 0; s < stateList.length; s++) {
		if (stateList[s]['text'] == state) {
			stateAbbrev = stateList[s]['value'];
		}
	}
	
	return stateAbbrev;
}
