/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       27 Jan 2020     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;

var _USE_UOM = _NSFeatures.UNITSOFMEASURE() == "T";
var _SHOW_MAP = new Array();
var _RANGE_START = null;
var _RANGE_END = null;

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response){
	var doPrint = request.getParameter("prt") == "T";
	if (request.getMethod() == "GET") {
		if (doPrint) {
			doWork(request, response);
		} else {
			displayForm(request, response);
		}
	} else {
		response.write("INVALID_HTTP_REQUEST");
	}
}

function displayForm(request, response) {
	var context = nlapiGetContext();
	
	var form = nlapiCreateForm("Inventory Deficiency Report");
	
	form.addFieldGroup("search_opt_group", "Search Selection");
	form.addFieldGroup("date_range_group", "Search By Date Range");
	form.addFieldGroup("selected_show_group", "Search By Event");
	form.addFieldGroup("display_opt_group", "Display Options");
	
	var sortField = form.addField("custpage_search_type", "select", "Search By", null, "search_opt_group");
	sortField.addSelectOption("A", "Date Range", true);
	sortField.addSelectOption("B", "Event");
	
	var ordTypeField = form.addField("custpage_order_type", "multiselect", "Order Type", null, "search_opt_group");
	ordTypeField.setBreakType("startcol");
	var orderTypes = data_GetOrderTypes();
	for (var o = 0; o < orderTypes.length; o++) {
		ordTypeField.addSelectOption(orderTypes[o]['id'], orderTypes[o]['value']);
	}
	form.setFieldValues({
			custpage_order_type : [_scLib.DefaultExhibitorOrderType,_scLib.DefaultShowMgmtOrderType]
	});
	
	form.addField("custpage_start_date", "date", "Start Date", null, "date_range_group");
	form.addField("custpage_end_date", "date", "End Date", null, "date_range_group");
	
	var showsField = form.addField("custpage_show", "select", "Event", null, "selected_show_group");
	_scLib.getOpenShows(showsField, false);
	
	form.addField("custpage_show_all_items", "checkbox", "Display All Items", null, "display_opt_group").setBreakType("startcol");
	form.addField("custpage_show_req_and_max", "checkbox", "Display Total Request and Max Available", null, "display_opt_group");
	form.addField("custpage_show_details", "checkbox", "Display Event Details", null, "display_opt_group");
	
	form.addField("custpage_url", "text", "").setDisplayType("hidden").setDefaultValue(nlapiResolveURL("SUITELET", context.getScriptId(), context.getDeploymentId()));
	
	addTimeZoneOffsetHTML(form);
	
	form.setFieldValues({
			custpage_show_req_and_max : "T"
		,	custpage_show_details : "T"
	});
	
	form.addButton("custpage_reset_button", "Reset", "resetPage()");
	form.addButton("custpage_print_button", "Print", "printPage()");
	form.setScript("customscript_ng_cs_client_inv_def_report");
	response.writePage(form);
}

function doWork(request, response) {
	var sType = request.getParameter("styp");
	var show = request.getParameter("sid");
	var sDate = request.getParameter("sdt");
	var eDate = request.getParameter("edt");
	var showAll = request.getParameter("sai") == "T";
	var showReqAndMax = request.getParameter("srm") == "T";
	var showDetails = request.getParameter("sdet") == "T";
	var ordType = !_tools.isEmpty(request.getParameter("otp")) ? request.getParameter("otp").split(";") : "";
	
	var printDate = setOffsetPrintDate(request);
	var subTitle = "";
	
	var showData = null;
	if (sType == "A") {
		showData = getDataByDate(sDate, eDate);
	} else if (sType == "B") {
		showData = getDataByShow(show);
	} else {
		response.write("INVALID_PARAMETERS");
		return;
	}
	
	if (!_tools.isEmpty(showData)) {
		if (sType == "A") {
			_RANGE_START = sDate;
			_RANGE_END = eDate;
			subTitle = "<b>Data Source:</b> Date Range {0} - {1}".NG_Format(_RANGE_START, _RANGE_END);
		}
		
		var showList = new Array()
		for (var s = 0; s < showData.length; s++) {
			_SHOW_MAP.push({
					id : showData[s].getId()
				,	name : nlapiEscapeXML(showData[s].getValue("name"))
				,	pullDate : showData[s].getValue("custrecord_cs_st_send_inventory_date")
				,	returnDate : showData[s].getValue("custrecord_cs_st_return_inventory_date")
			});
			showList.push(showData[s].getId());
			if (sType == "B") {
				if (_tools.isEmpty(_RANGE_START)) {
					if (!_tools.isEmpty(showData[s].getValue("custrecord_cs_st_send_inventory_date"))) {
						_RANGE_START = showData[s].getValue("custrecord_cs_st_send_inventory_date");
					}
				} else {
					if (!_tools.isEmpty(showData[s].getValue("custrecord_cs_st_send_inventory_date"))) {
						var t1 = nlapiStringToDate(_RANGE_START).getTime();
						var t2 = nlapiStringToDate(showData[s].getValue("custrecord_cs_st_send_inventory_date")).getTime();
						if (t2 < t1) {
							_RANGE_START = showData[s].getValue("custrecord_cs_st_send_inventory_date");
						}
					}
				}
				if (_tools.isEmpty(_RANGE_END)) {
					if (!_tools.isEmpty(showData[s].getValue("custrecord_cs_st_send_inventory_date"))) {
						_RANGE_END = showData[s].getValue("custrecord_cs_st_send_inventory_date");
					}
				} else {
					if (!_tools.isEmpty(showData[s].getValue("custrecord_cs_st_send_inventory_date"))) {
						var t1 = nlapiStringToDate(_RANGE_END).getTime();
						var t2 = nlapiStringToDate(showData[s].getValue("custrecord_cs_st_send_inventory_date")).getTime();
						if (t2 > t1) {
							_RANGE_END = showData[s].getValue("custrecord_cs_st_send_inventory_date");
						}
					}
				}
			}
		}
		if (sType == "B") {
			for (var s = 0; s < _SHOW_MAP.length; s++) {
				if (_SHOW_MAP[s].id == show) {
					subTitle = "<b>Data Source:</b> Show <i>{0}</i> Inventory Pull {1} - {2}".NG_Format(_SHOW_MAP[s].name.length > 45 ? "{0}...".NG_Format(_SHOW_MAP[s].name.substr(0, 45)) : _SHOW_MAP[s].name, _RANGE_START, _RANGE_END);
					break;
				}
			}
		}
		_SHOW_MAP = _tools.objectSort(_SHOW_MAP, "name");
		var defData = getOrderData(showList, ordType);
		if (!_tools.isEmpty(defData)) {
			var header = xml_PageHeaderDeficiency(showData, "Inventory Deficiency Report");
			var xml = "";
			xml += '<?xml version="1.0"?>';
			xml += '<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">';
			
			xml += '<pdf>\n';
			xml += '<head>\n';
			xml += '<macrolist>{0}{1}</macrolist>\n'.NG_Format(header,xml_PageFooter(printDate));
			xml += xml_StyleSheet();
			xml += '</head>\n';
			xml += '<body size="Letter" header="header" header-height="50px" footer="footer" footer-height="15px" style="margin: 0.25in; padding: 0;">\n';
			
			xml += '<table table-layout="fixed" border="0" style="width:720px; margin:3px; padding:0; overflow:hidden; background-color:#DDDDDD; border-bottom: 2px #AAAAAA;" cellmargin="0" cellpadding="0"><tr><td>';
			xml += '<table table-layout="fixed" border="0" style="width:720px; margin:0px; padding:0; overflow:hidden" cellmargin="0" cellpadding="0">';
			xml += '<tr><td width="100%" class="h_cell_g"><p style="padding:10px 0px 0px 5px;">{0}</p></td></tr>'.NG_Format(subTitle);
			xml += '</table>';
			xml += '<hr style="width:100%; color:DDDDDD; background-color:#DDDDDD; border-top: 2px dashed #AAAAAA;" />';
			xml += '<table table-layout="fixed" border="0" style="width:720px; margin:3px; padding:0; overflow:hidden;" cellmargin="0" cellpadding="0">';
			xml += '<THEAD>\n';
			xml += '<tr><th colspan="6" align="left"><b>Show</b></th><th colspan="3" align="left"><b>Pull Date</b></th><th colspan="3" align="left"><b>Return Date</b></th></tr>\n';
			xml += '</THEAD>\n';
			xml += '<TBODY>\n';
			
			for (var s = 0; s < _SHOW_MAP.length; s++) {
				xml += '<tr><td colspan="6" align="left" class="h_cell_b"><p>{0}</p></td><td colspan="3" align="center" class="h_cell_f"><p>{1}</p></td><td colspan="3" align="center" class="h_cell_f"><p>{2}</p></td></tr>\n'.NG_Format(
						_SHOW_MAP[s].name , _SHOW_MAP[s].pullDate , _SHOW_MAP[s].returnDate
				);
			}
			
			xml += '</TBODY>';
			xml += '</table>';
			xml += '</td></tr></table>';
			
			xml += '<table table-layout="fixed" border="0" style="width:720px; margin-top:10px;" cellmargin="0" cellpadding="0" class="datatbl"><THEAD>\n';
			if (showReqAndMax) {
				xml += '<tr><th colspan="6" align="left"><b>Item</b></th><th colspan="3" align="left"><b>Total Requested</b></th><th colspan="3" align="left"><b>Maximum Available</b></th><th colspan="3" align="left"><b>Deficiency</b></th></tr>\n';
			} else {
				xml += '<tr><th colspan="12" align="left"><b>Item</b></th><th colspan="3" align="left"><b>Deficiency</b></th></tr>\n';
			}
			if (showDetails) {
				xml += '<tr><th colspan="1">&nbsp;</th><th colspan="7" align="left"><b>Show</b></th><th colspan="3" align="left"><b>Pull Date</b></th><th colspan="3" align="left"><b>Amount Needed</b></th><th colspan="1">&nbsp;</th></tr>\n';
			}
			xml += '</THEAD><TBODY>\n';
			var shaded = false;
			for (var itemId in defData) {
				if (showAll || defData[itemId].defncy < 0) {
					var item = defData[itemId];
					var shadeCSS = "";
					if (_scLib.ShadeAltRptLines && shaded) {
						shadeCSS = ' background-color: {0};'.NG_Format(_scLib.RptLineShadeHex || _SHADED_HEX);
					}
					var styleA = "";
					if (!_tools.isEmpty(item.size) || !_tools.isEmpty(item.color) || !_tools.isEmpty(item.orientation) || !_tools.isEmpty(item.substrate)) {
						styleA = ' style="padding-top: 3px;{0}"'.NG_Format(shadeCSS);
					} else {
						styleA = ' style="padding-top: 3px; padding-bottom: 3px;{0}"'.NG_Format(shadeCSS);
					}
					var styleB = ' style="padding-bottom: 3px;{0}"'.NG_Format(shadeCSS);
					if (shaded) {
						shaded = false;
					} else {
						shaded = true;
					}
					
					if (showReqAndMax) {
						xml += '<tr{0}><td colspan="6" align="left">{1}</td><td colspan="3" align="right"><p style="padding-right: 10px;">{2}</p></td><td colspan="3" align="right"><p style="padding-right: 10px;">{3}</p></td><td colspan="3" align="right"><p style="padding-right: 10px;">{4}</p></td></tr>'.NG_Format(
								styleA , item.name , item.orderData.total.toFixed(0) , item.maxInv.toFixed(0) , item.defncy < 0 ? (Math.abs(item.defncy)).toFixed(0) : "0"
						);
					} else {
						xml += '<tr{0}><td colspan="12" align="left">{1}</td><td colspan="3" align="right"><p style="padding-right: 10px;">{2}</p></td></tr>'.NG_Format(
								styleA , item.name , (Math.abs(item.defncy)).toFixed(0)
						);
					}
					
					if (!_tools.isEmpty(item.size) || !_tools.isEmpty(item.color) || !_tools.isEmpty(item.orientation) || !_tools.isEmpty(item.substrate)) {
						var attr = new Array();
						if (!_tools.isEmpty(item.size)) {
							attr.push('<b>Size:</b> {0}'.NG_Format(item.sizeName));
						}
						if (!_tools.isEmpty(item.color)) {
							attr.push('<b>Color:</b> {0}'.NG_Format(item.colorName));
						}
						if (!_tools.isEmpty(item.orientation)) {
							attr.push('<b>Orientation:</b> {0}'.NG_Format(item.orientName));
						}
						if (!_tools.isEmpty(item.substrate)) {
							attr.push('<b>Substrate:</b> {0}'.NG_Format(item.substrName));
						}
						xml += '<tr{0}><td colspan="2">&nbsp;</td><td colspan="8" align="left">{1}</td><td colspan="5" align="left">&nbsp;</td></tr>'.NG_Format(
								styleB , attr.join('<br />')
						);
					}
					
					if (showDetails) {
						for (var s = 0; s < _SHOW_MAP.length; s++) {
							var styleC = "";
							var styleD = "";
							if (s == 0) {
								styleC = ' style="padding-top: 3px; padding-bottom: 3px;{0}"'.NG_Format(shadeCSS);
								styleD = ' style = "border-top: 1px solid #AAAAAA;"';
							} else {
								styleC = ' style="padding-top: 3px;{0}"'.NG_Format(shadeCSS);
							}
							xml += '<tr{0}><td colspan="1">&nbsp;</td><td colspan="7" align="left"{4}>{1}</td><td colspan="3" align="center"{4}>{2}</td><td colspan="3" align="right"{4}>{3}</td><td colspan="1">&nbsp;</td></tr>\n'.NG_Format(
									styleC , _SHOW_MAP[s].name , _SHOW_MAP[s].pullDate , item.orderData[_SHOW_MAP[s].id] , styleD
							);
						}
					}
				}
			}
			xml += '</TBODY></table>\n';
			
			xml += '</body></pdf>\n';
			
			_log.logInfo("Printing...");
			try {
				var file = nlapiXMLToPDF(xml);
				response.setContentType("PDF", "booth_checklist_{0}.pdf".NG_Format(_tools.timeStamp(null,null,null)), "inline");
				response.write(file.getValue());
			} catch (err) {
				_log.logError(err, "Error encountered creating Booth Checklist PDF");
				if (!_tools.isEmpty(_scLib.ReportXMLFolderID)) {
					try {
						var text = nlapiCreateFile("pdf_xml_out_inv_deficiency_{0}.txt".NG_Format(_tools.timeStamp()), "PLAINTEXT", xml);
						text.setFolder(_scLib.ReportXMLFolderID);
						nlapiSubmitFile(text);
					} catch (e) {
						_log.logError(e, "Error encountered saving PDF XML text file");
					}
				}
				response.write("PDF creation failed: [{0}] {1}".NG_Format(err.name,err.message));
			}
		} else {
			response.write("Could not get item deficiency data");
			return;
		}
	} else {
		response.write("Could not get item deficiency data");
		return;
	}
}

function getDataByDate(sDate, eDate) {
	var defFilt = new Array(
			["custrecord_cs_st_send_inventory_date","onorafter",sDate]
		,	"and"
		,	["custrecord_cs_st_send_inventory_date","onorbefore",eDate]
	);
	var defCols = new Array(
			new nlobjSearchColumn("name", null, null)
		,	new nlobjSearchColumn("custrecord_cs_st_send_inventory_date", null, null)
		,	new nlobjSearchColumn("custrecord_cs_st_return_inventory_date", null, null)
	);
	var results = null;
	try {
		results = nlapiSearchRecord("customrecord_show", null, defFilt, defCols);
	} catch (err) {
		_log.logError(err, "Error encountered searching for applicable shows for deficiency data");
	}
	
	return results;
}

function getDataByShow(show) {
	var results = null;
	var rangeData = nlapiLookupField("customrecord_show", show, ["name","custrecord_cs_st_send_inventory_date","custrecord_cs_st_return_inventory_date"]);
	if (!_tools.isEmpty(rangeData.custrecord_cs_st_send_inventory_date) && !_tools.isEmpty(rangeData.custrecord_cs_st_send_inventory_date)) {
		var defFilt = new Array(
				[
						["custrecord_cs_st_send_inventory_date","onorbefore",rangeData.custrecord_cs_st_send_inventory_date]
					,	"and"
					,	["custrecord_cs_st_return_inventory_date","onorafter",rangeData.custrecord_cs_st_send_inventory_date]
				]
			,	"or"
			,	["internalid","anyof",[show]]
		);
		var defCols = new Array(
				new nlobjSearchColumn("name", null, null)
			,	new nlobjSearchColumn("custrecord_cs_st_send_inventory_date", null, null)
			,	new nlobjSearchColumn("custrecord_cs_st_return_inventory_date", null, null)
		);
		try {
			results = nlapiSearchRecord("customrecord_show", null, defFilt, defCols);
		} catch (err) {
			_log.logError(err, "Error encountered searching for applicable shows for deficiency data");
		}
	} else {
		_log.logError(null, "Selected show is missing information", "Show table record is missing one or both of the inventory pull/return dates; Cannot continue with deficiency report.");
	}
	
	return results;
}

function getOrderData(showList, ordTypes) {
	var ordFilt = new Array(
			["custbody_show_table","anyof",showList]
		,	"and"
		,	["mainline","is","F"]
		,	"and"
		,	["cogs","is","F"]
		,	"and"
		,	["shipping","is","F"]
		,	"and"
		,	["taxline","is","F"]
//		,	"and"
//		,	["custbody_booth","noneof",["@NONE@"]]
		,	"and"
		,	["closed","is","F"]
		,	"and"
		,	["item.type","noneof",["Description"]]
	);
	if (_scLib.ItemReportsExlcudeCats.length > 0) {
		ordFilt.push("and", ["item.custitem_item_category","noneof",_scLib.ItemReportsExlcudeCats]);
	}
	if (!_tools.isEmpty(ordTypes) && ordTypes.length > 0) {
		ordFilt.push("and", ["custbody_ng_cs_order_type","anyof",ordTypes]);
	} else {
		ordFilt.push("and", ["custbody_booth","noneof",["@NONE@"]]);
	}
	var ordCols = new Array(
			new nlobjSearchColumn("item", null, null) // 0
		,	new nlobjSearchColumn("quantity", null, null) // 1
		,	new nlobjSearchColumn("itemid", "item", null) // 2
		,	new nlobjSearchColumn("name", "custbody_show_table", null) // 3
		,	new nlobjSearchColumn("custitem_item_category", "item", null) // 4
		,	new nlobjSearchColumn("custitem_subcategory", "item", null) // 5
		,	new nlobjSearchColumn("custbody_ng_cs_order_type", null, null) // 6
		,	new nlobjSearchColumn("custbody_show_table", null, null) // 7
		,	new nlobjSearchColumn("custitem27", "item", null) // color // 8
		,	new nlobjSearchColumn("custitem28", "item", null) // size // 9
		,	new nlobjSearchColumn("custitem_orientation", "item", null) // 10
		,	new nlobjSearchColumn("custitem42", "item", null) // graphic material/substrate // 11
		,	new nlobjSearchColumn("salesdescription", "item", null) // 12
		,	new nlobjSearchColumn("custcol_description", null, null) // 13
		,	new nlobjSearchColumn("custitem_ng_cs_max_avail_inventory", "item", null) // 14
		,	new nlobjSearchColumn("custbody_show_table", null, null) // 15
		,	new nlobjSearchColumn("displayname", "item", null) // 16
		,	new nlobjSearchColumn("itemtype", null, null) // 17
	);
	if (_USE_UOM) {
		ordCols.push(new nlobjSearchColumn("unit", null, null));
	}
	
	var results = null;
	try {
		var ordItemSearch = nlapiCreateSearch("salesorder", ordFilt, ordCols);
		results = _tools.getSearchResults(ordItemSearch.runSearch(), false);
	} catch (err) {
		_log.logError(err, "Error encountered retrieving order item data");
	}
	
	var itemData = null;
	if (!_tools.isEmpty(results)) {
		itemData = { };
		
		for (var r = 0; r < results.length; r++) {
			var itemType = results[r].getValue(ordCols[17]);
			if (_tools.isInArray(itemType, ["Group","EndGroup","Description","Discount","Subtotal","Markup","Service","Payment","OthCharge","DwnLdItem","GiftCert"])) {
				continue;
			}
			var itemId = results[r].getValue(ordCols[0]);
			if (_tools.isEmpty(itemData[itemId])) {
				itemData[itemId] = {
						id : itemId
					,	name : nlapiEscapeXML(itemNameDisplay(results[r].getValue(ordCols[2]), results[r].getValue(ordCols[16])))
					,	cat : results[r].getValue(ordCols[4])
					,	catName : nlapiEscapeXML(results[r].getText(ordCols[4]))
					,	subcat : results[r].getValue(ordCols[5])
					,	subcatName :nlapiEscapeXML( results[r].getText(ordCols[5]))
					,	ordType : results[r].getValue(ordCols[6])
					,	ordTypeName : nlapiEscapeXML(results[r].getText(ordCols[6]))
					,	color : results[r].getValue(ordCols[8])
					,	colorName : nlapiEscapeXML(results[r].getText(ordCols[8]))
					,	size : results[r].getValue(ordCols[9])
					,	sizeName : nlapiEscapeXML(results[r].getText(ordCols[9]))
					,	orientation : results[r].getValue(ordCols[10])
					,	orientName : nlapiEscapeXML(results[r].getText(ordCols[10]))
					,	substrate : results[r].getValue(ordCols[11])
					,	substrName : nlapiEscapeXML(results[r].getText(ordCols[11]))
					,	itemDescr : results[r].getValue(ordCols[12]) || ""
					,	maxInv : new Number(results[r].getValue(ordCols[14]) || "0")
					,	unit : _USE_UOM ? nlapiEscapeXML(results[r].getValue("unit") || "Each") : "Each"
					,	orderData : {
								total : new Number(0)
						}
				};
				
				for (var s = 0; s < _SHOW_MAP.length; s++) {
					itemData[itemId].orderData[_SHOW_MAP[s].id] = new Number(0);
				}
			}
			
			var showId = results[r].getValue(ordCols[15]);
			var qty = new Number(results[r].getValue(ordCols[1]));
			
			itemData[itemId].orderData.total += qty;
			itemData[itemId].orderData[showId] += qty;
		}
		
		for (var item in itemData) {
			var data = itemData[item];
			for (var num in data.orderData) {
				itemData[item].orderData[num] = parseInt(Math.round(data.orderData[num]));
			}
			itemData[item].defncy = parseInt(Math.round(itemData[item].maxInv - itemData[item].orderData.total));
		}
	}
	
	return itemData;
}

function xml_PageHeaderDeficiency(showData, reportTitle) {
//	var s_in_DOW = !_tools.isEmpty(tableData['show_in']) ? _DAY_OF_WEEK[nlapiStringToDate(tableData['show_in']).getDay()] : "";
//	var s_out_DOW = !_tools.isEmpty(tableData['show_out']) ? _DAY_OF_WEEK[nlapiStringToDate(tableData['show_out']).getDay()] : "";
//	var e_in_DOW = !_tools.isEmpty(tableData['exhb_in']) ? _DAY_OF_WEEK[nlapiStringToDate(tableData['exhb_in']).getDay()] : "";
//	var e_out_DOW = !_tools.isEmpty(tableData['exhb_out']) ? _DAY_OF_WEEK[nlapiStringToDate(tableData['exhb_out']).getDay()] : "";
//	var s_s_DOW = !_tools.isEmpty(tableData['show_s']) ? _DAY_OF_WEEK[nlapiStringToDate(tableData['show_s']).getDay()] : "";
//	var s_e_DOW = !_tools.isEmpty(tableData['show_e']) ? _DAY_OF_WEEK[nlapiStringToDate(tableData['show_e']).getDay()] : "";
	
	var xml = '';
	xml += '<macro id="header">';
	xml += '<div style="position:absolute;" x="0%" y="0%"><table table-layout="fixed" border="0" style="width:720px; margin:0; padding:0; overflow:hidden;" cellmargin="0" cellpadding="0">';
	xml += '<tr><td style="overflow:hidden; height:0.93in;"><img src="{0}" width="2.5in" height="0.50in" style="margin:0; padding:0;" /></td>'.NG_Format(_scLib.WorkOrderImageURL); //  height:0.53in;
	xml += '<td style="width:55%;"><p style="width:100%; align:left; vertical-align:top;"><span style="font-size:24px;">{0}</span><br />'.NG_Format(reportTitle);
//	xml += '{ADDITIONAL_A}';
	xml += '</p>';
	xml += '</td></tr>';
	xml += '</table></div>';
	
	/*xml += '<div style="background-color:#DDDDDD; border-bottom: 2px #AAAAAA; position:absolute;" x="0%" y="0.95in">';
	
	xml += '<table table-layout="fixed" border="0" style="width:720px; margin:3px; padding:0; overflow:hidden" cellmargin="0" cellpadding="0">';
	xml += '<tr><td width="100%" class="h_cell_a"><p>&nbsp;</p></td></tr>';
	xml += '</table>';
	xml += '<hr style="width:100%; color:DDDDDD; background-color:#DDDDDD; border-top: 2px dashed #AAAAAA;" />';
	xml += '<table table-layout="fixed" border="0" style="width:720px; margin:3px; padding:0; overflow:hidden;" cellmargin="0" cellpadding="0">';
	xml += '<THEAD>\n';
	xml += '<tr><th colspan="6" align="left"><b>Show</b></th><th colspan="3" align="left"><b>Pull Date</b></th><th colspan="3" align="left"><b>Return Date</b></th></tr>\n';
	xml += '</THEAD><TBODY>\n';
	
	for (var s = 0; s < _SHOW_MAP.length; s++) {
		xml += '<tr><td colspan="6" align="left" class="h_cell_b"><p>{0}</p></td><td colspan="3" align="center" class="h_cell_f"><p>{1}</p></td><td colspan="3" align="center" class="h_cell_f"><p>{2}</p></td></tr>\n'.NG_Format(
				_SHOW_MAP[s].name , _SHOW_MAP[s].pullDate , _SHOW_MAP[s].returnDate
		);
	}
	
	xml += '</TBODY>';
	xml += '</table>';
	
	xml += '</div>';*/
	
	xml += '</macro>\n';
	return xml;
}
