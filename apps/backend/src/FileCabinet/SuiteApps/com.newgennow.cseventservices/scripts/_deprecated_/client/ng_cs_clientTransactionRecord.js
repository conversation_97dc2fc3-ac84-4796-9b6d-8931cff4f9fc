/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;
var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;

var _UndepositedFunds = _scLib.UndepositedFunds;
var _DefaultDepositAccount = _scLib.DefaultDepositAccount;
var _UseCSJobs = _scLib.UseCustomJob;

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType 
 * 
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function NG_T_clientPageInit(type){
	if (type == "create") {
		nlapiSetFieldValue("undepfunds", _UndepositedFunds);
		if (_DefaultDepositAccount != null) {
			nlapiSetFieldValue("account", _DefaultDepositAccount);
		}
	}
	
	if (_scLib.RetainLastShow) {
		if (_tools.isEmpty(nlapiGetFieldValue("custbody_show_table"))) {
			var values = { };
			values['action'] = "get";
			values['user'] = nlapiGetUser();
			var lastShow = _scLib.getLastShow(values);
			if (!_tools.isEmpty(lastShow)) {
				nlapiSetFieldValue("custbody_show_table", lastShow, true);
			}
		}
	}
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @returns {Boolean} True to continue save, false to abort save
 */
function NG_T_clientSaveRecord(){

    return true;
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Sublist internal id
 * @param {String} name Field internal id
 * @param {Number} linenum Optional line item number, starts from 1
 * @returns {Void}
 */
function NG_T_clientFieldChanged(type, name, linenum){
	if (name == "custbody_booth" || name == "custbody_show_table") {
		if (_scLib.PreventAdditionalOrders) {
			var boothID = nlapiGetFieldValue("custbody_booth");
			var showTableID = nlapiGetFieldValue("custbody_show_table");
			if (!_tools.isEmpty(showTableID) && !_tools.isEmpty(boothID)) {
				var exhbID = nlapiLookupField("customrecord_show_booths", boothID, "custrecord_booth_exhibitor");
				if (!_tools.isEmpty(exhbID)) {
					nlapiSetFieldValue("entity", exhbID, true);
					nlapiSetFieldValue("customer", exhbID, true);
					
					var filt = new Array(
							["custbody_show_table","anyof",[showTableID]]
						,	"and"
						,	["custbody_booth","anyof",[boothID]]
					);
					var cols = new Array(
							new nlobjSearchColumn("internalid", null, null)
					);
					if (_UseSubsidiaries) {
						cols.push(new nlobjSearchColumn("subsidiary", null, null));
					}
					if (_UseLocations) {
						cols.push(new nlobjSearchColumn("location", null, null));
					}
					var search = null;
					try {
						search = nlapiSearchRecord("salesorder", null, filt, cols);
					} catch (err) { window.alert("Booth order search error: [{0}] {1}".NG_Format(err.name,err.message)); }
					
					if (search != null) {
						var soID = search[0].getValue("internalid");
						
						if (!_tools.isEmpty(soID)) {
							try {
								nlapiSetFieldValue("custbody_booth_order", soID, false);
								if (_UseSubsidiaries) {
									nlapiSetFieldValue("subsidiary", search[0].getValue("subsidiary"), false);
								}
								if (_UseLocations) {
									nlapiSetFieldValue("location", search[0].getValue("location"), false);
								}
							} catch (err) {
								console.log("Error encountered setting location/subsidiary\n", err);
							}
						}
					}
				} else {
					nlapiSetFieldValue("entity", "", true);
					nlapiSetFieldValue("customer", "", true);
					nlapiSetFieldValue("custbody_booth_order", "", false);
				}
			} else {
				nlapiSetFieldValue("entity", "", true);
				nlapiSetFieldValue("customer", "", true);
				nlapiSetFieldValue("custbody_booth_order", "", false);
			}
		}
	}
	
	if (name == "custbody_show_table") {
		var showTableID = nlapiGetFieldValue("custbody_show_table");
		if (!_tools.isEmpty(showTableID)) {
			var _SHOW_DATA = nlapiLookupField("customrecord_show", showTableID, ["custrecord_show_job","custrecord_fin_show"]);
			if (_UseCSJobs) {
				nlapiSetFieldValue("custbody_cseg_ng_cs_job", _SHOW_DATA['custrecord_show_job']);
				nlapiSetFieldValue("class", _SHOW_DATA['custrecord_fin_show']);
			} else {
				nlapiSetFieldValue("class", _SHOW_DATA['custrecord_fin_show']);
			}
		} else {
			if (_UseCSJobs) {
				nlapiSetFieldValue("custbody_cseg_ng_cs_job", "");
				nlapiSetFieldValue("class", "");
			} else {
				nlapiSetFieldValue("class", "");
			}
		}
		
		if (_scLib.RetainLastShow) {
			var values = { };
			values['action'] = "set";
			values['user'] = nlapiGetUser();
			values['show'] = nlapiGetFieldValue("custbody_show_table");
			_scLib.setLastShow(values);
		}
	}
}

function printRefund() {
	window.alert("Order ID: " + nlapiGetRecordId());
}
