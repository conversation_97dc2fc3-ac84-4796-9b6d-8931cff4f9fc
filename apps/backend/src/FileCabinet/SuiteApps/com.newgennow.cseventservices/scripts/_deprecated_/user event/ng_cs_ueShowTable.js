/**
 * Module Description
 *
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;
var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;
var _UseTaxRate = _scLib.UseTaxCode;
var _UseCnclPct = _scLib.UseCancellationCharge;
var _UseCSJobs = _scLib.UseCustomJob;
var _DefaultSubsidiary = _scLib.DefaultSubsidiary;

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Operation types: create, edit, view, copy, print, email
 * @param {nlobjForm} form Current form
 * @param {nlobjRequest} request Request object
 * @returns {Void}
 */
function userEventBeforeLoad(type, form, request) {
  if (type == "copy") {
    throw nlapiCreateError(
      "USER_ERROR",
      "Copying is not allowed for event records"
    );
  }

  _scLib.hideCSJobField(form);

  if (type == "create") {
    var jobParam = !_tools.isEmpty(request) ? request.getParameter("jb") : null;
    var showNumber = !_tools.isEmpty(request)
      ? request.getParameter("snbr")
      : null;
    var showName = !_tools.isEmpty(request)
      ? request.getParameter("snm")
      : null;
    var showType = !_tools.isEmpty(request)
      ? request.getParameter("stype")
      : null;
    var customerID = !_tools.isEmpty(request)
      ? request.getParameter("cid")
      : null;
    var csJobParam = !_tools.isEmpty(request)
      ? request.getParameter("csj")
      : null;
    var subsidiary = !_tools.isEmpty(request)
      ? request.getParameter("sdy")
      : null;
    var department = !_tools.isEmpty(request)
      ? request.getParameter("dpt")
      : null;
    var location = !_tools.isEmpty(request)
      ? request.getParameter("loc")
      : null;

    if (!_tools.isEmpty(jobParam)) {
      var jobFields = new Array(
        "custrecord_use_show_wizard",
        "custrecord_use_std_show_form",
        "custrecord_show_form_id",
        "custrecord_apply_job_num_to_show",
        "custrecord_redirect_to_sales_order",
        "custrecord_sales_order_form"
      );
      var jobData = nlapiLookupField(
        "customrecord_order_type",
        showType,
        jobFields
      );

      var jobField = form.getField("custrecord_fin_show");
      jobField.setDefaultValue(jobParam);
      jobField.setDisplayType("disabled");
      if (_UseCSJobs) {
        var csJobField = form.getField("custrecord_show_job");
        csJobField.setDefaultValue(csJobParam);
        csJobField.setDisplayType("disabled");
        var csJobField2 = form.getField("custrecord_58_cseg_ng_cs_job");
        csJobField2.setDefaultValue(csJobParam);
        csJobField2.setDisplayType("disabled");
      }
      var showNameField = form.getField("name");
      if (
        _scLib.UseJobNumbering &&
        jobData.custrecord_apply_job_num_to_show == "T"
      ) {
        if (_scLib.NameNumberOrder == "1") {
          showNameField.setDefaultValue(
            "{0}{1}{2}".NG_Format(
              showNumber,
              _scLib.separators[_scLib.NameNumberSeparator],
              showName
            )
          );
        } else {
          showNameField.setDefaultValue(
            "{0}{1}{2}".NG_Format(
              showName,
              _scLib.separators[_scLib.NameNumberSeparator],
              showNumber
            )
          );
        }
      } else {
        showNameField.setDefaultValue(showName);
      }
      showNameField.setDisplayType("disabled");
      if (!_tools.isEmpty(customerID)) {
        var customerField = form.getField("custrecord_show_customer");
        customerField.setDefaultValue(customerID);
        customerField.setDisplayType("disabled");
      }
    }

    if (_UseSubsidiaries) {
      if (!_tools.isEmpty(subsidiary)) {
        form.setFieldValues({
          custrecord_show_subsidiary: subsidiary,
        });
      } else {
        form.setFieldValues({
          custrecord_show_subsidiary: _DefaultSubsidiary,
        });
      }
    }
    if (_UseLocations) {
      if (!_tools.isEmpty(location)) {
        form.setFieldValues({
          custrecord_show_venue: location,
        });
      }
    }
  }

  if (!_tools.isInArray(type, ["print", "email"])) {
    var taxField = form.getField("custrecord_tax_rate");
    var cnclPctField = form.getField("custrecord_cancellation_pct");

    if (_UseTaxRate && _UseCnclPct) {
      taxField.setBreakType("startcol");

      taxField.setDisplayType("normal");
      cnclPctField.setDisplayType("normal");
    } else if (_UseTaxRate && !_UseCnclPct) {
      taxField.setBreakType("startcol");

      taxField.setDisplayType("normal");
      cnclPctField.setDisplayType("hidden");
    } else if (!_UseTaxRate && _UseCnclPct) {
      cnclPctField.setBreakType("startcol");

      taxField.setDisplayType("hidden");
      cnclPctField.setDisplayType("normal");
    } else {
      taxField.setDisplayType("hidden");
      cnclPctField.setDisplayType("hidden");
    }

    var recIdNum = nlapiGetFieldValue("rectype");
    if (_UseCSJobs) {
      var classField = form.getField("custrecord_fin_show");
      classField.setLabel("Class");
      var csJobField = form.getField("custrecord_show_job");
      csJobField.setMandatory(true);
      try {
        var csJobField2 = form.getField(
          "custrecord_{0}_cseg_ng_cs_job".NG_Format(recIdNum)
        );
        csJobField2.setMandatory(true);
      } catch (err) {
        _log.logError(
          err,
          "Error encountered trying to make the CS Job field mandatory",
          "Rec Type ID #: {0}".NG_Format(recIdNum)
        );
      }
    } else {
      var csJobField = form.getField("custrecord_show_job");
      csJobField.setDisplayType("hidden");
      csJobField.setMandatory(false);
      try {
        var csJobField2 = form.getField(
          "custrecord_{0}_cseg_ng_cs_job".NG_Format(recIdNum)
        );
        csJobField2.setDisplayType("hidden");
        csJobField2.setMandatory(false);
      } catch (err) {
        _log.logError(
          err,
          "Error encountered trying to make the CS Job field hidden and not mandatory",
          "Rec Type ID #: {0}".NG_Format(recIdNum)
        );
      }
    }
  }

  if (_tools.isInArray(type, ["create", "copy", "edit"])) {
    var cInfo = nlapiLoadConfiguration("companyinformation");
    var cData = {
      name: cInfo.getFieldValue("name"),
      cname: cInfo.getFieldValue("companyname"),
      lname: cInfo.getFieldValue("legalname"),
    };
    form
      .addField("custpage_company_info", "longtext", "")
      .setDisplayType("hidden")
      .setDefaultValue(JSON.stringify(cData));

    var apInfo = nlapiLoadConfiguration("accountingpreferences");
    if (apInfo.getFieldValue("classmandatory") == "T") {
      var classField = form.getField("custrecord_fin_show");
      classField.setMandatory(true);
    }

    if (_UseSubsidiaries) {
      form.getField("custrecord_show_subsidiary").setMandatory(true);
      var sFilt = new Array(["isinactive", "is", "F"]);
      var sCols = new Array(
        new nlobjSearchColumn("namenohierarchy", null, null)
      );
      var sSearch = null;
      try {
        sSearch = nlapiSearchRecord("subsidiary", null, sFilt, sCols);
      } catch (err) {
        _log.logError(err, "Error encountered getting subsidiary info");
      }
      if (sSearch != null) {
        var sData = {};
        for (var i = 0; i < sSearch.length; i++) {
          sData[sSearch[i].getId()] = {
            name: sSearch[i].getValue("namenohierarchy"),
          };
        }
        form
          .addField("custpage_subsidiary_info", "longtext", "")
          .setDisplayType("hidden")
          .setDefaultValue(JSON.stringify(sData));
      }
    }

    if (_UseLocations) {
      var lFilt = new Array(["isinactive", "is", "F"]);
      var lCols = new Array(
        new nlobjSearchColumn("name", null, null),
        new nlobjSearchColumn("address1", "address", null),
        new nlobjSearchColumn("address2", "address", null),
        new nlobjSearchColumn("city", "address", null),
        new nlobjSearchColumn("state", "address", null),
        new nlobjSearchColumn("zip", "address", null),
        new nlobjSearchColumn("country", "address", null)
      );
      var lResults = null;
      try {
        var lSearch = nlapiCreateSearch("location", lFilt, lCols);
        var rlSearch = lSearch.runSearch();
        lResults = _tools.getSearchResults(rlSearch, false);
      } catch (err) {
        _log.logError(err, "Error encountered getting location data");
      }
      if (lResults != null) {
        var lData = {};
        for (var l = 0; l < lResults.length; l++) {
          lData[lResults[l].getId()] = {
            name: lResults[l].getValue(lCols[0]),
            addr1: lResults[l].getValue(lCols[1]),
            addr2: lResults[l].getValue(lCols[2]),
            city: lResults[l].getValue(lCols[3]),
            state: lResults[l].getValue(lCols[4]),
            zip: lResults[l].getValue(lCols[5]),
            country: lResults[l].getValue(lCols[6]),
          };
        }
        var lHtml = '<script type="text/javascript">';
        lHtml += "function GetLocationData() {";
        lHtml += "var lData = '{0}';".NG_Format(JSON.stringify(lData));
        lHtml += "return JSON.parse(lData);";
        lHtml += "}";
        lHtml += "</script>";
        form
          .addField("custpage_location_info", "inlinehtml", "")
          .setDefaultValue(lHtml);
      }
    }

    if (_UseTaxRate) {
      var taxGroupField = form.addField(
        "custpage_tax_group",
        "select",
        "Tax Group"
      );
      taxGroupField.setHelpText("Select the tax code for the show.");
      taxGroupField.setBreakType("startcol");
      var tgFilt = new Array(["isinactive", "is", "F"]);
      var tgCols = new Array(new nlobjSearchColumn("itemid", null, null));
      tgCols[0].setSort();
      var results = _tools.getSearchResults(
        nlapiCreateSearch("taxgroup", tgFilt, tgCols).runSearch(),
        false
      );
      if (!_tools.isEmpty(results)) {
        taxGroupField.addSelectOption("", "", true);
        results.forEach(function (res) {
          taxGroupField.addSelectOption(res.getId(), res.getValue("itemid"));
        });
      }

      form.insertField(taxGroupField, "custrecord_tax_rate");
      var taxRateField = form.getField("custrecord_tax_rate");
      //		form.insertField(taxRateField, "custpage_tax_group");
      //		form.getField("custrecord_tax_rate").setDisplayType("hidden");
      taxRateField.setDisplayType("hidden");

      if (type == "edit") {
        taxGroupField.setDefaultValue(
          nlapiGetFieldValue("custrecord_tax_rate")
        );
      } else if (type == "copy") {
        nlapiSetFieldValue("custrecord_tax_rate", "");
      }
    } else {
      form.getField("custrecord_tax_rate").setDisplayType("inline");
    }
  }

  if (type == "view") {
    var tabs = form.getAllTabs();
    try {
      for (var t = 0; t < tabs.length; t++) {
        var tab = form.getTab(tabs[t]);
        if (tab.getLabel() == "Communications") {
          var btnHTML = _tools.addNSStyleButton(
            "custpage_mass_mail_btn",
            " Mass Mailer ",
            "goToMassMail()"
          );
          var htmlButtonField = form
            .addField(
              "custpage_mass_mail_btn_html",
              "inlinehtml",
              "",
              null,
              tabs[t]
            )
            .setDefaultValue(btnHTML);

          var url = "{0}&sid={1}".NG_Format(
            nlapiResolveURL(
              "SUITELET",
              "customscript_ng_cs_sl_mass_mailer",
              "customdeploy_ng_cs_sl_mass_mailer_dep"
            ),
            nlapiGetRecordId()
          );
          var html = "";
          html += '<script type="text/javascript"> ';
          html += "function goToMassMail() { ";
          html += 'window.open("{0}", "_blank"); '.NG_Format(url);
          html += "} ";
          html += "</script>";
          form
            .addField("custpage_mass_mail_html", "inlinehtml", "")
            .setDefaultValue(html);
          break;
        }
      }
    } catch (err) {
      _log.logError(
        err,
        "Error encountered adding Mass Mailer button to Communications tab"
      );
    }

    try {
      for (var t = 0; t < tabs.length; t++) {
        var tab = form.getTab(tabs[t]);
        if (tab.getLabel() == "Web Categories") {
          var btnHTML = _tools.addNSStyleButton(
            "custpage_dispform_btn",
            " Update Event Web Categories ",
            "goToDispFormUpdate()"
          );
          var htmlButtonField = form
            .addField(
              "custpage_dispform_btn_html",
              "inlinehtml",
              "",
              null,
              tabs[t]
            )
            .setDefaultValue(btnHTML);

          var url = "{0}&stid={1}".NG_Format(
            nlapiResolveURL(
              "SUITELET",
              "customscript_ng_cs_sl_upd_sdf_recs",
              "customdeploy_ng_cs_sl_upd_sdf_recs_dep"
            ),
            nlapiGetRecordId()
          );
          var html = "";
          html += '<script type="text/javascript"> ';
          html += "function goToDispFormUpdate() { ";
          html += "window.location='{0}'; ".NG_Format(url);
          html += "} ";
          html += "</script>";
          form
            .addField("custpage_dispform_html", "inlinehtml", "")
            .setDefaultValue(html);
          break;
        }
      }
    } catch (err) {
      _log.logError(
        err,
        "Error encountered adding update button to Display Forms tab"
      );
    }

    try {
      form.addButton(
        "custpage_ng_cs_create_tasks",
        "Create Tasks",
        "createTasks()"
      );

      var html = "";
      html += '<script type="text/javascript">\n';
      html += "var popup = null;\n";
      html += "function createTasks() { ";
      html += "var showTableID = nlapiGetRecordId(); ";
      html += 'console.log("Show ID: " + showTableID); ';
      html +=
        'var url = nlapiResolveURL("SUITELET", "customscript_ng_cs_sl_create_tasks", "customdeploy_ng_cs_sl_create_tasks"); ';
      html += 'url += "&show=" + showTableID; ';
      html += "var screenHeight = screen.height; ";
      html += "var screenWidth = screen.width; ";
      html += 'console.log("Screen Height: " + screenHeight); ';
      html += 'console.log("Screen Width: " + screenWidth); ';
      html += "var popupHeight = Math.ceil(screenHeight * 0.75); ";
      html += "var popupWidth = Math.ceil(screenWidth * 0.75); ";
      html += "if (popup == null) { ";
      html +=
        '	var popupParams = "location=no,height=" + popupHeight + ",width=" + popupWidth + ",scrollbars=yes,status=no"; ';
      html += '	popup = window.open(url, "_blank", popupParams); ';
      html += '	console.log("Popup:\\n", popup); ';
      html += '	var bodyElement = document.getElementsByTagName("BODY")[0]; ';
      html += "	console.log(bodyElement.onunload); ";
      html += "	bodyElement.onunload = function() { ";
      html += "		if (popup != null) { ";
      html += "			popup.close(); ";
      html += "		} ";
      html += "	} ";
      html += "} else { ";
      html += "	popup.focus(); ";
      html += "} ";
      html += "}\n";
      html += "</script>";
      form
        .addField("custpage_task_btn_html", "inlinehtml", "")
        .setDefaultValue(html);
    } catch (err) {
      _log.logError(
        err,
        "Error encountered adding button for create tasks function"
      );
    }

    /*try {
			form.addButton("custpage_ng_cs_move_inventory_btn", "Move Inventory", "moveInventory()");
			
			var url = "{0}&show={1}".NG_Format(nlapiResolveURL("SUITELET", "customscript_ng_cs_sl_move_show_inv", "customdeploy_ng_cs_sl_move_show_inv_dep"),nlapiGetRecordId());
			var html = '';
			html += '<script type="text/javascript"> ';
			html += 'function moveInventory() { ';
			html += 'window.open("{0}", "_blank"); '.NG_Format(url);
			html += '} ';
			html += '</script>';
			form.addField("custpage_ng_cs_move_inventory_html", "inlinehtml", "").setDefaultValue(html);
		} catch (err) {
			_log.logError(err, "Error encountered adding move inventory button");
		}*/

    var stForm = nlapiLoadRecord(
      nlapiGetRecordType(),
      nlapiGetRecordId()
    ).getFieldValue("customform");
    if (
      !_tools.isEmpty(stForm) &&
      stForm == _scLib.DefaultShowTableForm &&
      (_tools.isEmpty(nlapiGetFieldValue("custrecord_cs_st_contact_group")) ||
        _tools.isEmpty(nlapiGetFieldValue("custrecord_cs_st_contact_search")) ||
        _tools.isEmpty(nlapiGetFieldValue("custrecord_ng_cs_st_show_campaign")))
    ) {
      var ctHTML = "";
      ctHTML += '<script type="text/javascript"> ';
      ctHTML += "function NG_SetContactSearchAndGroup() { ";
      ctHTML += "var showTableRecID = nlapiGetRecordId(); ";
      ctHTML += 'var targetURL = "{0}"; '.NG_Format(
        nlapiResolveURL(
          "SUITELET",
          "customscript_ng_cs_sl_set_ct_srchgrp",
          "customdeploy_ng_cs_sl_set_ct_srchgrp_dep"
        )
      );
      if (
        _tools.isEmpty(nlapiGetFieldValue("custrecord_cs_st_contact_search"))
      ) {
        ctHTML += 'targetURL += "&mksrch=T"; ';
      }
      if (
        _tools.isEmpty(nlapiGetFieldValue("custrecord_cs_st_contact_group"))
      ) {
        ctHTML += 'targetURL += "&mkgrp=T"; ';
      }
      if (
        _tools.isEmpty(nlapiGetFieldValue("custrecord_ng_cs_st_show_campaign"))
      ) {
        ctHTML += 'targetURL += "&cmpgn=T"; ';
      }
      ctHTML += 'targetURL += "&stid=" + showTableRecID; ';
      ctHTML += 'console.log("target url = ", targetURL); ';
      ctHTML += "var reply = nlapiRequestURL(targetURL).getBody(); ";
      ctHTML += 'if (reply == "OK") { ';
      ctHTML += "window.location.reload(); ";
      ctHTML += "} else { ";
      ctHTML += "window.alert(reply); ";
      ctHTML += "} ";
      ctHTML += " } ";
      ctHTML += "</script>";

      form
        .addField("custpage_ct_btn_html", "inlinehtml", "")
        .setDefaultValue(ctHTML);
      form.addButton(
        "custpage_add_ct_data_btn",
        "Set Contact Group/Search",
        "NG_SetContactSearchAndGroup()"
      );
    }
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      approve, reject, cancel (SO, ER, Time Bill, PO & RMA only)
 *                      pack, ship (IF)
 *                      markcomplete (Call, Task)
 *                      reassign (Case)
 *                      editforecast (Opp, Estimate)
 * @returns {Void}
 */
function userEventBeforeSubmit(type) {
  if (type == "delete") {
    var boothRecType = "customrecord_show_booths";
    var dateRecType = "customrecord_show_date";
    var freightRecType = "customrecord_freight_table";
    var displayRecType = "customrecord_show_display_forms";
    var docRecType = "customrecord_ng_cses_upload_attachment";
    var laborRecType = "customrecord_ng_cs_show_labor_schedule";
    var importLogRecType = "customrecord_exhb_import_log";

    var ShowTableID = nlapiGetRecordId();
    var boothRecFilt = new Array([
      "custrecord_booth_show_table",
      "anyof",
      [ShowTableID],
    ]);
    var dateRecFilt = new Array([
      "custrecord_show_number_date",
      "anyof",
      [ShowTableID],
    ]);
    var freightRecFilt = new Array([
      "custrecord_show_freight",
      "anyof",
      [ShowTableID],
    ]);
    var displayFormRecFilt = new Array([
      "custrecord_sdf_show_table",
      "anyof",
      [ShowTableID],
    ]);
    var docRecFilt = new Array([
      "custrecord_show_upload",
      "anyof",
      [ShowTableID],
    ]);
    var laborRecFilt = new Array([
      "custrecord_ng_cs_labor_show",
      "anyof",
      [ShowTableID],
    ]);
    var importLogRecFilt = new Array([
      "custrecord_exhb_log_show_table",
      "anyof",
      [ShowTableID],
    ]);

    try {
      var boothSearch = nlapiSearchRecord(
        boothRecType,
        null,
        boothRecFilt,
        null
      );
      if (boothSearch != null) {
        for (var i = 0; i < boothSearch.length; i++) {
          try {
            nlapiDeleteRecord(boothRecType, boothSearch[i].getId());
          } catch (err) {
            _log.logError(
              err,
              "Error encountered deleting booth record",
              "Booth record ID: {0}".NG_Format(boothSearch[i].getId())
            );
          }
        }
      }
    } catch (err) {
      _log.logError(
        err,
        "Error encountered handling booth record search and deletion"
      );
    }

    try {
      var dateSearch = nlapiSearchRecord(dateRecType, null, dateRecFilt, null);
      if (dateSearch != null) {
        for (var i = 0; i < dateSearch.length; i++) {
          try {
            nlapiDeleteRecord(dateRecType, dateSearch[i].getId());
          } catch (err) {
            _log.logError(
              err,
              "Error encountered deleting date record",
              "Date record ID: {0}".NG_Format(dateSearch[i].getId())
            );
          }
        }
      }
    } catch (err) {
      _log.logError(
        err,
        "Error encountered handling date record search and deletion"
      );
    }

    try {
      var freightSearch = nlapiSearchRecord(
        freightRecType,
        null,
        freightRecFilt,
        null
      );
      if (freightSearch != null) {
        for (var i = 0; i < freightSearch.length; i++) {
          try {
            nlapiDeleteRecord(freightRecType, freightSearch[i].getId());
          } catch (err) {
            _log.logError(
              err,
              "Error encountered deleting freight record",
              "Freight record ID: {0}".NG_Format(freightSearch[i].getId())
            );
          }
        }
      }
    } catch (err) {
      _log.logError(
        err,
        "Error encountered handling freight record search and deletion"
      );
    }

    try {
      var displaySearch = nlapiSearchRecord(
        displayRecType,
        null,
        displayFormRecFilt,
        null
      );
      if (displaySearch != null) {
        for (var i = 0; i < displaySearch.length; i++) {
          try {
            nlapiDeleteRecord(displayRecType, displaySearch[i].getId());
          } catch (err) {
            _log.logError(
              err,
              "Error encountered deleting display form record",
              "Display form record ID: {0}".NG_Format(displaySearch[i].getId())
            );
          }
        }
      }
    } catch (err) {
      _log.logError(
        err,
        "Error encountered handling display form record search and deletion"
      );
    }

    try {
      var docSearch = nlapiSearchRecord(docRecType, null, docRecFilt, null);
      if (docSearch != null) {
        for (var i = 0; i < docSearch.length; i++) {
          try {
            nlapiDeleteRecord(docRecType, docSearch[i].getId());
          } catch (err) {
            _log.logError(
              err,
              "Error encountered deleting document record",
              "Document record ID: {0}".NG_Format(docSearch[i].getId())
            );
          }
        }
      }
    } catch (err) {
      _log.logError(
        err,
        "Error encountered handling document record search and deletion"
      );
    }

    try {
      var laborSearch = nlapiSearchRecord(
        laborRecType,
        null,
        laborRecFilt,
        null
      );
      if (laborSearch != null) {
        for (var i = 0; i < laborSearch.length; i++) {
          try {
            nlapiDeleteRecord(laborRecType, laborSearch[i].getId());
          } catch (err) {
            _log.logError(
              err,
              "Error encountered deleting labor record",
              "Document record ID: {0}".NG_Format(laborSearch[i].getId())
            );
          }
        }
      }
    } catch (err) {
      _log.logError(
        err,
        "Error encountered handling labor record search and deletion"
      );
    }

    try {
      var importLogSearch = nlapiSearchRecord(
        importLogRecType,
        null,
        importLogRecFilt,
        null
      );
      if (importLogSearch != null) {
        for (var i = 0; i < importLogSearch.length; i++) {
          try {
            nlapiDeleteRecord(importLogRecType, importLogSearch[i].getId());
          } catch (err) {
            _log.logError(
              err,
              "Error encountered deleting import log record",
              "Document record ID: {0}".NG_Format(importLogSearch[i].getId())
            );
          }
        }
      }
    } catch (err) {
      _log.logError(
        err,
        "Error encountered handling import log record search and deletion"
      );
    }
  } else if (type == "edit") {
    // 8-9-2017 Added by Scott Anderson
    var recShowTableOld = nlapiGetOldRecord();
    var showStatus = recShowTableOld.getFieldValue("custrecord_show_status");
    var recShowTable = nlapiGetNewRecord();
    var showTableID = nlapiGetRecordId();
    var newShowStatus = recShowTable.getFieldValue("custrecord_show_status");

    if (showStatus != newShowStatus) {
      var arrSearchFilters = null;
      var arrSearchColumns = null;
      var arrSearchResults = null;

      arrSearchFilters = [["custrecord_show_number_date", "is", showTableID]];
      arrSearchColumns = [
        new nlobjSearchColumn("custrecord_show_number_date"),
        new nlobjSearchColumn("custrecord_date_type"),
        new nlobjSearchColumn("custrecord_date"),
        new nlobjSearchColumn("custrecord_start_time"),
        new nlobjSearchColumn("custrecord_end_time"),
        new nlobjSearchColumn("custrecord_date_show"),
        new nlobjSearchColumn("custrecord_cal_event"),
      ];

      try {
        arrSearchResults = nlapiSearchRecord(
          "customrecord_show_date",
          null,
          arrSearchFilters,
          arrSearchColumns
        );
      } catch (err) {
        _log.logError(err, "Error encountered searching for show date records");
      }

      if (!_tools.isEmpty(arrSearchResults)) {
        for (i = 0; i < arrSearchResults.length; i++) {
          var eventId = arrSearchResults[i].getValue("custrecord_cal_event");
          _log.logDebug("Event ID", eventId);
          if (_tools.isEmpty(eventId)) {
            eventId = createCalendarEvent(arrSearchResults[i].getId());
          }

          if (!_tools.isEmpty(eventId)) {
            var recEvent = nlapiLoadRecord("calendarevent", eventId);
            if (newShowStatus == 3) {
              recEvent.setFieldValue("status", "CONFIRMED");
              var title = recEvent.getFieldValue("title");
              title = title.replace("TENT - ", "");
              recEvent.setFieldValue("title", title);
            } else if (newShowStatus == "4") {
              recEvent.setFieldValue("status", "CANCELLED");
              var title = recEvent.getFieldValue("title");
              title = title.replace("TENT - ", "");
              recEvent.setFieldValue("title", title);
            } else {
              recEvent.setFieldValue("status", "TENTATIVE");
              var title = recEvent.getFieldValue("title");
              var index = title.indexOf("TENT - ");
              if (index == -1) {
                title = "TENT - {0}".NG_Format(title);
                recEvent.setFieldValue("title", title);
              }
            }

            nlapiSubmitRecord(recEvent);
          }
        }
      }
    }
    // End Add 8-9-2017 by Scott Anderson
  }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment.
 * @appliedtorecord recordType
 *
 * @param {String} type Operation types: create, edit, delete, xedit,
 *                      approve, cancel, reject (SO, ER, Time Bill, PO & RMA only)
 *                      pack, ship (IF only)
 *                      dropship, specialorder, orderitems (PO only)
 *                      paybills (vendor payments)
 * @returns {Void}
 */
function userEventAfterSubmit(type) {
  if (type == "create") {
  }
}

function createCalendarEvent(dateRecId) {
  var dateRec = nlapiLoadRecord("customrecord_show_date", dateRecId);
  var eventId = dateRec.getFieldValue("custrecord_cal_event");
  if (!_tools.isEmpty(eventId)) {
    return eventId;
  }
  var showNumber = dateRec.getFieldText("custrecord_show_number_date");
  var showDate = dateRec.getFieldValue("custrecord_date");
  var startTime = dateRec.getFieldValue("custrecord_start_time");
  var endTime = dateRec.getFieldValue("custrecord_end_time");
  var dateType = dateRec.getFieldText("custrecord_date_type");
  var showInternalId = dateRec.getFieldValue("custrecord_show_number_date"); // Get Internal Id of show to use for search

  var title = "{0} - {1}".NG_Format(showNumber, dateType);

  var showTableValues = nlapiLookupField("customrecord_show", showInternalId, [
    "custrecord_acct_exec",
    "custrecord_sales_rep",
    "custrecord_show_status",
  ]);

  var acctExec = showTableValues.custrecord_acct_exec;
  var salesRep = showTableValues.custrecord_sales_rep;

  var eventRec = nlapiCreateRecord("calendarevent");

  if (_tools.isEmpty(endTime)) {
    endTime = "11:59 pm";
  }

  eventRec.setFieldValue("title", title);
  eventRec.setFieldValue("startdate", showDate);
  eventRec.setFieldValue("starttime", startTime);
  eventRec.setFieldValue("endtime", endTime);
  eventRec.setFieldText("accesslevel", "Public");

  eventRec.removeLineItem("attendee", 1);

  if (!_tools.isEmpty(acctExec)) {
    eventRec.selectNewLineItem("attendee");
    eventRec.setCurrentLineItemValue("attendee", "attendee", acctExec);
    eventRec.commitLineItem("attendee");
  }

  if (!_tools.isEmpty(salesRep) && salesRep != acctExec) {
    eventRec.selectNewLineItem("attendee");
    eventRec.setCurrentLineItemValue("attendee", "attendee", salesRep);
    eventRec.commitLineItem("attendee");
  }

  eventRec.selectNewLineItem("resource");
  eventRec.setCurrentLineItemValue("resource", "resource", _scLib.ShowCalendar);
  eventRec.commitLineItem("resource");

  var eventId = null;
  try {
    eventId = nlapiSubmitRecord(eventRec);

    // 8-8-2017 Added by Scott Anderson
    var showStatus = showTableValues.custrecord_show_status;
    if (showStatus == 3) {
      nlapiSubmitField("calendarevent", eventId, "status", "CONFIRMED");
    } else if (showStatus == "4") {
      nlapiSubmitField("calendarevent", eventId, "status", "CANCELLED");
    } else {
      title = "TENT - {0}".NG_Format(title);
      nlapiSubmitField(
        "calendarevent",
        eventId,
        ["status", "title"],
        ["TENTATIVE", title]
      );
    }
    // End Add 8-8-2017 by Scott Anderson
  } catch (error) {
    _log.logError(error, "Error encountered saving event record");
  }

  if (eventId != null) {
    try {
      nlapiSubmitField(
        dateRec.getRecordType(),
        dateRec.getId(),
        "custrecord_cal_event",
        eventId
      );
    } catch (error) {
      _log.logError(error, "Error encountered saving date record");
    }
  }

  return eventId;
}
