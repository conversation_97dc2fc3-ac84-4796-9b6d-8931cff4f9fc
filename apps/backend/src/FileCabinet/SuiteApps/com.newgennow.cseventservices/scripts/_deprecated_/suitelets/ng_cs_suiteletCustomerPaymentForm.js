/**
 * Module Description
 *
 * Version    Date            Author           Remarks
 * 1.00       01 Jan 2015     <PERSON>, NewGen Business Solutions, Inc.
 *
 */

var _M = NewGen.lib.math;
var _tools = NewGen.lib.tools;
var _NSFeatures = NewGen.lib.features;
var _log = NewGen.lib.logging;
var _scLib = NewGen.sclib;
var _UseDepartments = _NSFeatures.DEPARTMENTS() == "T" ? true : false;
var _UseSubsidiaries = _NSFeatures.SUBSIDIARIES() == "T" ? true : false;
var _UseLocations = _NSFeatures.LOCATIONS() == "T" ? true : false;
var _MultiPartner = _NSFeatures.MULTIPARTNER() == "T" ? true : false;
var _UseDupDetect = _NSFeatures.DUPLICATES() == "T" ? true : false;

var boothIDList;
var method;
var ccList;
var orderIdList = new Array();
var openShowSearch = _scLib.openShowsSearch;
var _<PERSON>positedFunds = _scLib.UndepositedFunds;
var _DefaultDepositAccount = _scLib.DefaultDepositAccount;
var _DefaultARAccount = _scLib.PaymentARAccount;
var _UseCSJobs = _scLib.UseCustomJob;
var testing = false;

var _AuthItem = _scLib.AuthItem;

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response) {
  if (request.getMethod() == "GET") {
    displayForm(request, response);
  } else {
    processRequest(request, response);
  }
}

function displayForm(request, response, status) {
  var showp =
    request.getParameter("custparam_show") != null
      ? request.getParameter("custparam_show")
      : status != null
      ? status.showp
      : "";
  var boothp =
    request.getParameter("custparam_booth") != null
      ? request.getParameter("custparam_booth")
      : status != null
      ? status.boothp
      : "";
  var totalp =
    request.getParameter("custparam_total") != null
      ? request.getParameter("custparam_total")
      : status != null
      ? status.totalp
      : "";
  var useCard =
    request.getParameter("custparam_usecard") != null
      ? request.getParameter("custparam_usecard")
      : status != null
      ? status.useCard
      : "";
  var orderp =
    request.getParameter("custparam_order") != null
      ? request.getParameter("custparam_order")
      : status != null
      ? status.orderp
      : "";
  var cccardID =
    request.getParameter("custparam_cardid") != null
      ? request.getParameter("custparam_cardid")
      : status != null
      ? status.cccardID
      : "";
  var ccnamep =
    request.getParameter("custparam_cardholder") != null
      ? request.getParameter("custparam_cardholder")
      : status != null
      ? status.ccnamep
      : "";
  var ccnump =
    request.getParameter("custparam_cardnum") != null
      ? request.getParameter("custparam_cardnum")
      : status != null
      ? status.ccnump
      : "";
  var ccexpp =
    request.getParameter("custparam_cardexp") != null
      ? request.getParameter("custparam_cardexp")
      : status != null
      ? status.ccexpp
      : "";
  var cccodep =
    request.getParameter("custparam_cardcode") != null
      ? request.getParameter("custparam_cardcode")
      : status != null
      ? status.cccodep
      : "";
  var cctypep =
    request.getParameter("custparam_cardtype") != null
      ? request.getParameter("custparam_cardtype")
      : status != null
      ? status.cctypep
      : "";

  var deptp =
    request.getParameter("custparam_department") != null
      ? request.getParameter("custparam_department")
      : status != null
      ? status.deptp
      : "";
  var locp =
    request.getParameter("custparam_location") != null
      ? request.getParameter("custparam_location")
      : status != null
      ? status.locp
      : "";
  var subp =
    request.getParameter("custparam_subsidiary") != null
      ? request.getParameter("custparam_subsidiary")
      : status != null
      ? status.subp
      : "";

  var useConvFee = _scLib.UseConvenienceFee;
  var convFeeRate =
    new Number(_scLib.ConvenienceFeeRate.replace("%", "")) / 100;
  var convFeeItem = _scLib.ConvenienceFeeItem;

  var form = nlapiCreateForm("Exhibitor Payment Form");
  form.addButton(
    "auth_button",
    "Authorize Card Only",
    "getCardAuthorization()"
  );

  if (status != null) {
    form.addFieldGroup("submissionresponse", "New Payment Status");

    if (status.err == null) {
      if (!_tools.isEmpty(status.payrecid)) {
        var successField = form.addField(
          "custpage_success",
          "text",
          "Payment Submission Succeeded",
          null,
          "submissionresponse"
        );
        var recType = "";
        var typeName = "";
        if (_scLib.PaymentType == "2") {
          recType = "customerdeposit";
          typeName = "Deposit";
        } else {
          recType = "customerpayment";
          typeName = "Payment";
        }
        var payLink = nlapiResolveURL(
          "RECORD",
          recType,
          status.payrecid,
          "VIEW"
        );
        var tranNum = nlapiLookupField(recType, status.payrecid, "tranid");
        var fieldVal = '<a href="{0}">{1} #{2}</a>'.NG_Format(
          payLink,
          typeName,
          tranNum
        );
        successField.setDefaultValue(fieldVal);
        successField.setDisplayType("inline");
        if (!_tools.isEmpty(status.orderid)) {
          var ordLink = nlapiResolveURL(
            "RECORD",
            "salesorder",
            status.orderid,
            "VIEW"
          );
          var ordNum = nlapiLookupField("salesorder", status.orderid, "tranid");
          var ordFieldVal = '<a href="{0}">Booth Order #{1}</a>'.NG_Format(
            ordLink,
            ordNum
          );
          var ordField = form.addField(
            "custpage_ord",
            "text",
            "Booth Order",
            null,
            "submissionresponse"
          );
          ordField.setDefaultValue(ordFieldVal);
          ordField.setDisplayType("inline");
        }
        var pmntField = form.addField(
          "custpage_newpayment",
          "select",
          "New Payment",
          "customerpayment",
          null
        );
        pmntField.setDefaultValue(status.payrecid);
        pmntField.setDisplayType("hidden");
        form.addButton("custpage_print", "Print Receipt", "printPayment()");
      } else if (status.authOnly == "T") {
        var successField = form.addField(
          "custpage_success",
          "text",
          "Status",
          null,
          "submissionresponse"
        );
        successField.setDefaultValue("Credit Card Authorization Succeeded");
        successField.setDisplayType("inline");
        if (!_tools.isEmpty(status.orderid)) {
          var ordLink = nlapiResolveURL(
            "RECORD",
            "salesorder",
            status.orderid,
            "VIEW"
          );
          var ordNum = nlapiLookupField("salesorder", status.orderid, "tranid");
          var ordFieldVal = '<a href="{0}">Booth Order #{1}</a>'.NG_Format(
            ordLink,
            ordNum
          );
          var ordField = form.addField(
            "custpage_ord",
            "text",
            "Booth Order",
            null,
            "submissionresponse"
          );
          ordField.setDefaultValue(ordFieldVal);
          ordField.setDisplayType("inline");
        }
      }

      var htmlField = form.addField("htmlfield_bgcolor", "inlinehtml", "html");
      var html = setBGColor("#DAEBD5");
      htmlField.setDefaultValue(html);
    } else {
      var authOnly = request.getParameter("custpage_authonly");
      var labelText = "";
      if (authOnly == "T") {
        labelText = "Card Authorization Failed";
      } else {
        labelText = "Payment Submission Failed";
      }
      var errorField = form.addField(
        "custpage_failure",
        "longtext",
        labelText,
        null,
        "submissionresponse"
      );
      errorField.setDefaultValue(status.err.message);
      errorField.setDisplayType("inline");

      var htmlField = form.addField("htmlfield_bgcolor", "inlinehtml", "html");
      var html = setBGColor("#FDC1C7");
      htmlField.setDefaultValue(html);
    }
    form.addButton("custpage_neworder", "New Order", "newOrder()");
  }

  form.addFieldGroup("exhibitorgroup", "Exhibitor");
  form.addFieldGroup("cashgroup", "Payment");
  form.addFieldGroup("cardinfogroup", "Credit Card Info");
  form.addFieldGroup("chequegroup", "Check Payment Info");
  form.addFieldGroup("balancesgroup", "Transactional Balances");

  var showsField = form.addField(
    "custpage_show",
    "select",
    "Event",
    null,
    "exhibitorgroup"
  );
  var boothField = form.addField(
    "custpage_booth",
    "select",
    "Booth",
    null,
    "exhibitorgroup"
  );
  var exhibitorField = form.addField(
    "custpage_exhibitor",
    "select",
    _scLib.AllowMultiBillingParties ? "Billing Party" : "Exhibitor",
    "customer",
    "exhibitorgroup"
  );
  var orderField = form.addField(
    "custpage_order",
    "select",
    "Booth Order",
    null,
    "exhibitorgroup"
  );
  orderField.addSelectOption("", "", true);
  var classField = null;
  var jobField = null;
  if (_UseCSJobs) {
    jobField = form.addField(
      "custpage_job",
      "select",
      "Job",
      "customrecord_cseg_ng_cs_job",
      "exhibitorgroup"
    );
    classField = form.addField(
      "custpage_financial",
      "select",
      "Class",
      "classification",
      "exhibitorgroup"
    );
  } else {
    classField = form.addField(
      "custpage_financial",
      "select",
      "Job",
      "classification",
      "exhibitorgroup"
    );
  }

  var deptField = null;
  if (_UseDepartments) {
    deptField = form.addField(
      "custpage_department",
      "select",
      "Department",
      "department",
      "exhibitorgroup"
    );
  }
  var locField = null;
  if (_UseLocations) {
    locField = form.addField(
      "custpage_location",
      "select",
      "Location",
      "location",
      "exhibitorgroup"
    );
  }
  var paymentTypeField = form.addField(
    "custpage_paymenttype",
    "select",
    "Payment Type",
    null,
    "cashgroup"
  );
  var paymentField = form.addField(
    "custpage_payment",
    "currency",
    "Payment Amount",
    null,
    "cashgroup"
  );

  var cardEntryField = form.addField(
    "custpage_entry",
    "text",
    "CC Swipe Entry",
    null,
    "cardinfogroup"
  );
  var cardSelectField = form.addField(
    "custpage_cardselect",
    "select",
    "Select Card",
    null,
    "cardinfogroup"
  );
  var cardTypeField = form.addField(
    "custpage_cardtype",
    "select",
    "Card Type",
    null,
    "cardinfogroup"
  );
  var cardNumberField = form.addField(
    "custpage_cardnumber",
    "text",
    "Card Number",
    null,
    "cardinfogroup"
  );
  var cardNumberFieldActual = form.addField(
    "custpage_cardnumberactual",
    "text",
    "Card Number",
    null,
    "cardinfogroup"
  );
  var cardNameField = form.addField(
    "custpage_cardname",
    "text",
    "Name on Card (Required)",
    null,
    "cardinfogroup"
  );
  var cardIdentField = form.addField(
    "custpage_cardidentity",
    "text",
    "Card Identifier",
    null,
    "cardinfogroup"
  );
  var cardExpField = form.addField(
    "custpage_expdate",
    "text",
    "Card Exp Date (MM/YYYY)",
    null,
    "cardinfogroup"
  );
  var cardCSCField = form.addField(
    "custpage_csc",
    "text",
    "Card Security Code (CVV2)",
    null,
    "cardinfogroup"
  );
  form
    .addField(
      "custpage_authonly",
      "checkbox",
      "Only Authorize Card",
      null,
      "cardinfogroup"
    )
    .setDisplayType("hidden");
  var addyField = form.addField(
    "custpage_address",
    "textarea",
    "Billing Address",
    null,
    "cardinfogroup"
  );
  var addyLineOneField = form.addField(
    "custpage_addy_line_1",
    "text",
    "Card Street",
    null,
    "cardinfogroup"
  );
  var addyZipField = form.addField(
    "custpage_addy_zip",
    "text",
    "Card Zip Code",
    null,
    "cardinfogroup"
  );

  var chequeField = form.addField(
    "custpage_chequenumber",
    "text",
    "Check Number",
    null,
    "chequegroup"
  );

  var oTotalField = form.addField(
    "custpage_orderstotal",
    "currency",
    "Total Orders",
    null,
    "balancesgroup"
  );
  var pTotalField = form.addField(
    "custpage_paymentstotal",
    "currency",
    "Total Payments",
    null,
    "balancesgroup"
  );
  var openInvField = form.addField(
    "custpage_openinvoices",
    "currency",
    "Total A/R Balance",
    null,
    "balancesgroup"
  );
  var overdueInvField = form.addField(
    "custpage_overdueinvoices",
    "currency",
    "Total Overdue Invoices",
    null,
    "balancesgroup"
  );

  var boothIDField = form.addField("custpage_boothid", "text", "Booth ID");

  var showsPField = form.addField("custpage_showp", "text", "Event");
  var boothPField = form.addField("custpage_boothp", "text", "Booth");
  var exhbPField = form.addField("custpage_exhibitorp", "text", "Exhibitor");
  var totalPField = form.addField("custpage_totalp", "text", "Total");
  var usecardPField = form.addField("custpage_usecard", "text", "Use Card");
  var orderPField = form.addField("custpage_orderp", "text", "Order");
  var cccardIDField = form.addField("custpage_cccardid", "text", "CC Card ID");
  var ccnamePField = form.addField("custpage_ccnamep", "text", "CC Name");
  var ccnumPField = form.addField("custpage_ccnump", "text", "CC Num");
  var ccexpPField = form.addField("custpage_ccexpp", "text", "CC Exp");
  var cccodePField = form.addField("custpage_cccodep", "text", "CC Code");
  var cctypePField = form.addField("custpage_cctypep", "text", "CC Type");

  boothField.setMandatory(true);
  exhibitorField.setMandatory(true);

  if (_UseCSJobs) {
    jobField.setMandatory(true);
  } else {
    classField.setMandatory(true);
  }

  showsPField.setDisplayType("hidden");
  boothPField.setDisplayType("hidden");
  exhbPField.setDisplayType("hidden");
  totalPField.setDisplayType("hidden");
  usecardPField.setDisplayType("hidden");
  orderPField.setDisplayType("hidden");
  cccardIDField.setDisplayType("hidden");
  ccnamePField.setDisplayType("hidden");
  ccnumPField.setDisplayType("hidden");
  ccexpPField.setDisplayType("hidden");
  cccodePField.setDisplayType("hidden");
  cctypePField.setDisplayType("hidden");

  if (_UseLocations && !_tools.isEmpty(locp)) {
    locField.setDefaultValue(locp);
  }
  if (_UseSubsidiaries && !_tools.isEmpty(subp)) {
    var subField = form.addField(
      "custpage_subsidiary",
      "select",
      "Subsidiary",
      "subsidiary"
    );
    subField.setDisplayType("hidden");
    subField.setDefaultValue(subp);
  }
  if (_UseDepartments && !_tools.isEmpty(deptp)) {
    deptField.setDefaultValue(deptp);
  }

  showsPField.setDefaultValue(showp);
  totalPField.setDefaultValue(totalp);
  usecardPField.setDefaultValue(useCard);
  orderPField.setDefaultValue(orderp);
  cccardIDField.setDefaultValue(cccardID);
  ccnamePField.setDefaultValue(ccnamep);
  ccnumPField.setDefaultValue(ccnump);
  ccexpPField.setDefaultValue(ccexpp);
  cccodePField.setDefaultValue(cccodep);
  cctypePField.setDefaultValue(cctypep);

  var pTypes =
    nlapiSearchRecord(
      "paymentmethod",
      null,
      ["isinactive", "is", "F"],
      new nlobjSearchColumn("name")
    ) || [];

  paymentTypeField.addSelectOption("", "", true);
  paymentTypeField.addSelectOption("A", "Credit Card");
  for (var p = 0; p < pTypes.length; p++) {
    if (!_tools.isInArray(pTypes[p].getId(), _scLib.CreditCardPayMethodIDs())) {
      paymentTypeField.addSelectOption(
        pTypes[p].getId(),
        pTypes[p].getValue("name")
      );
    }
  }

  cardSelectField.addSelectOption("", "", true);

  cardTypeField.addSelectOption("", "", true);
  for (var p = 0; p < pTypes.length; p++) {
    if (_tools.isInArray(pTypes[p].getId(), _scLib.CreditCardPayMethodIDs())) {
      cardTypeField.addSelectOption(
        pTypes[p].getId(),
        pTypes[p].getValue("name")
      );
    }
  }

  if (!_scLib.AllowMultiBillingParties) exhibitorField.setDisplayType("inline");
  cardSelectField.setDisplayType("disabled");
  cardTypeField.setDisplayType("disabled");
  cardEntryField.setDisplayType("disabled");
  cardNumberField.setDisplayType("disabled");
  cardNumberField.setDisplaySize(16);
  cardNameField.setDisplayType("disabled");
  cardIdentField.setMaxLength(90);
  cardIdentField.setDisplayType("disabled");
  cardExpField.setDisplayType("disabled");
  cardCSCField.setDisplayType("disabled");
  cardCSCField.setDisplaySize(4);
  cardNumberFieldActual.setDisplayType("hidden");
  chequeField.setDisplayType("disabled");
  paymentField.setDefaultValue("0.00");
  paymentField.setDisplayType("disabled");
  oTotalField.setDisplayType("inline");
  oTotalField.setDefaultValue(0.0);
  oTotalField.setBreakType("startcol");
  pTotalField.setDisplayType("inline");
  pTotalField.setDefaultValue(0.0);
  openInvField.setDisplayType("inline");
  openInvField.setDefaultValue(0.0);
  openInvField.setBreakType("startcol");
  overdueInvField.setDisplayType("inline");
  overdueInvField.setDefaultValue(0.0);
  boothIDField.setDisplayType("hidden");
  addyField.setDisplayType("inline");
  addyField.setDisplaySize(100, 6);

  showsField.setMandatory(true);
  _scLib.getOpenShows(showsField);

  if (useConvFee && !_tools.isEmpty(convFeeItem) && !_tools.isEmpty(orderp)) {
    var ordType = nlapiLookupField(
      "salesorder",
      orderp,
      "custbody_ng_cs_order_type"
    );
    if (!_tools.isInArray(ordType, _scLib.ConvenienceFeeOrderTypes)) {
      useConvFee = false;
    }
  } else if (useConvFee && _tools.isEmpty(convFeeItem)) {
    useConvFee = false;
  }
  form
    .addField("custpage_cc_conv_fee_data", "longtext", "")
    .setDisplayType("hidden")
    .setDefaultValue(
      JSON.stringify({
        enabled: useConvFee,
        rate: convFeeRate,
        item: convFeeItem,
      })
    );
  form
    .addField("custpage_cc_conv_fee_amount", "currency", "")
    .setDisplayType("hidden")
    .setDefaultValue("0.00");

  if (_tools.isEmpty(showp)) {
    boothField.addSelectOption("", "", true);

    if (_scLib.RetainLastShow) {
      var values = {};
      values["action"] = "get";
      values["user"] = nlapiGetUser();
      var lastShow = _scLib.getLastShow(values);
      if (!_tools.isEmpty(lastShow)) {
        showsField.setDefaultValue(lastShow);
      }
    }
  } else {
    showsField.setDefaultValue(showp);
    var showDataFields = ["custrecord_fin_show", "custrecord_show_job"];
    var showData = nlapiLookupField("customrecord_show", showp, showDataFields);
    if (_UseCSJobs) {
      jobField.setDefaultValue(showData.custrecord_show_job);
      classField.setDefaultValue(showData.custrecord_fin_show);
    } else {
      classField.setDefaultValue(showData.custrecord_fin_show);
    }

    if (!_tools.isEmpty(orderp)) {
      var showOrders = data_GetSalesOrderSelections(showp);
      for (var so = 0; so < showOrders.length; so++) {
        if (showOrders[so]["value"] == orderp) {
          orderField.addSelectOption(
            showOrders[so]["value"],
            showOrders[so]["text"],
            true
          );
        } else {
          orderField.addSelectOption(
            showOrders[so]["value"],
            showOrders[so]["text"]
          );
        }
      }
    }

    if (!_tools.isEmpty(boothp)) {
      var boothInfo = nlapiLookupField("customrecord_show_booths", boothp, [
        "name",
        "custrecord_booth_exhibitor",
      ]);
      var boothNum = boothInfo.name;
      boothField.addSelectOption(boothp, boothNum, true);

      var exhbID = null;
      if (!_tools.isEmpty(orderp)) {
        exhbID = nlapiLookupField("salesorder", orderp, "entity");
      } else {
        exhbID = boothInfo.custrecord_booth_exhibitor;
      }
      if (!_tools.isEmpty(exhbID)) {
        exhibitorField.setDefaultValue(exhbID);
        exhbPField.setDefaultValue(exhbID);
        var exhbData = nlapiLookupField("customer", exhbID, [
          "billaddress1",
          "billaddress2",
          "billaddress3",
          "billaddressee",
          "billattention",
          "billcity",
          "billcountry",
          "billcountrycode",
          "billphone",
          "billstate",
          "billzipcode",
        ]);
        var exhbCountry = nlapiLookupField(
          "customer",
          exhbID,
          "billcountry",
          true
        );
        var adrsArr = new Array();
        adrsArr.push(exhbData.billaddress1);
        if (!_tools.isEmpty(exhbData.billaddress2)) {
          adrsArr.push(exhbData.billaddress2);
        }
        if (!_tools.isEmpty(exhbData.billaddress3)) {
          adrsArr.push(exhbData.billaddress3);
        }
        adrsArr.push(
          "{0}, {1} {2}".NG_Format(
            exhbData.billcity,
            exhbData.billstate,
            exhbData.billzipcode
          ),
          exhbCountry
        );
        addyField.setDefaultValue(adrsArr.join("<br />"));
        addyLineOneField.setDefaultValue(exhbData.billaddress1);
        addyZipField.setDefaultValue(exhbData.billzipcode);

        var tFilt = new Array(
          ["custbody_show_table", "anyof", [showp]],
          "and",
          ["custbody_booth", "anyof", [boothp]],
          "and",
          ["mainline", "is", "T"]
        );
        if (_scLib.AllowMultiBillingParties) {
          tFilt.push("and", ["entity", "anyof", [exhibID]]);
        }
        var oCols = new Array(
          new nlobjSearchColumn("internalid", null, null),
          new nlobjSearchColumn("trandate", null, null),
          new nlobjSearchColumn("total", null, null)
        );
        var oSearch = null;
        var ordersTotal = _scLib.ExemptEstimatedItems
          ? !_tools.isEmpty(orderp)
            ? _scLib.getExemptedTotal(null, null, null, orderp)
            : _scLib.getExemptedTotal(
                showp,
                boothp,
                _scLib.AllowMultiBillingParties ? exhbID : null
              )
          : new Number(0);
        var paymentsTotal = !_tools.isEmpty(orderp)
          ? _scLib.getPaymentTotals(null, null, null, orderp)
          : _scLib.getPaymentTotals(
              showp,
              boothp,
              _scLib.AllowMultiBillingParties ? exhbID : null
            ); //new Number(0);
        var refundTotal = new Number(0);
        if (false) {
          try {
            var oFilt = tFilt;
            oFilt.push("and", ["custbody_to_be_deleted", "is", "F"]);
            oSearch = nlapiSearchRecord("salesorder", null, oFilt, oCols);
          } catch (err) {
            _log.logError(
              err,
              "Error encountered searching for related sales orders"
            );
          }

          if (oSearch != null) {
            for (var o = 0; o < oSearch.length; o++) {
              var orderBalance = oSearch[o].getValue("total");
              ordersTotal += new Number(orderBalance);
            }
          }
        }

        oTotalField.setDefaultValue(ordersTotal.toFixed(2));
        pTotalField.setDefaultValue(paymentsTotal.toFixed(2));

        var exhbBalances = nlapiLookupField("customer", exhbID, [
          "balance",
          "overduebalance",
        ]);
        openInvField.setDefaultValue(
          new Number(exhbBalances["balance"]).toFixed(2)
        );
        overdueInvField.setDefaultValue(
          new Number(exhbBalances["overduebalance"]).toFixed(2)
        );

        var payAmount = new Number(ordersTotal - paymentsTotal - refundTotal);
        if (payAmount > 0) {
          paymentField.setDefaultValue(payAmount.toFixed(2));
        } else {
          paymentField.setDefaultValue(0.0);
        }

        var exhbRec = nlapiLoadRecord("customer", exhbID);
        var ccCount = exhbRec.getLineItemCount("creditcards");

        if (ccList == null) {
          ccList = new Array();
        }

        if (ccCount > 0) {
          for (var c = 1; c <= ccCount; c++) {
            var ccExp = exhbRec.getLineItemValue(
              "creditcards",
              "ccexpiredate",
              c
            );
            var ccName = exhbRec.getLineItemValue("creditcards", "ccname", c);
            var ccNum = exhbRec.getLineItemValue("creditcards", "ccnumber", c);
            var ccID = exhbRec.getLineItemValue("creditcards", "internalid", c);
            var ccMemo = exhbRec.getLineItemValue("creditcards", "ccmemo", c);
            var ccTypeID = exhbRec.getLineItemValue(
              "creditcards",
              "paymentmethod",
              c
            );
            var ccDefault = exhbRec.getLineItemValue(
              "creditcards",
              "ccdefault",
              c
            );
            var ccType = "";
            var ccTypeSel = "";

            switch (ccTypeID) {
              case _scLib.MastercardPayMethodId:
                ccType = "Mastercard";
                ccTypeSel = _scLib.MastercardPayMethodId;
                break;
              case _scLib.VisaPayMethodId:
                ccType = "Visa";
                ccTypeSel = _scLib.VisaPayMethodId;
                break;
              case _scLib.AmexPayMethodId:
                ccType = "AMEX";
                ccTypeSel = _scLib.AmexPayMethodId;
                break;
              case _scLib.DiscoverPayMethodId:
                ccType = "Discover";
                ccTypeSel = _scLib.DiscoverPayMethodId;
                break;
              default:
                break;
            }

            var data = {};
            data.id = ccID;
            data.num = ccNum;
            data.name = ccName;
            data.exp = ccExp;
            data.type = ccType;
            data.typesel = ccTypeSel;
            data.typeid = ccTypeID;
            data.isdefault = ccDefault;
            data.memo = ccMemo;

            ccList.push(data);
          }

          for (var d = 0; d < ccList.length; d++) {
            var defaultCard = false;
            if (!_tools.isEmpty(cccardID)) {
              if (ccList[d].id == cccardID) {
                defaultCard = true;
                cardTypeField.setDefaultValue(ccList[d].typesel);
              }
            } else {
              if (ccList[d].isdefault == "T") {
                defaultCard = true;
                cardTypeField.setDefaultValue(ccList[d].typesel);
              }
            }
            cardSelectField.addSelectOption(
              ccList[d].id,
              ccList[d].type +
                " -- " +
                ccList[d].num +
                " (" +
                ccList[d].exp +
                ")" +
                (!_tools.isEmpty(ccList[d].memo)
                  ? " (" + ccList[d].memo + ")"
                  : "") +
                (defaultCard ? " (DEFAULT)" : ""),
              defaultCard
            );
            if (defaultCard) {
              cccardIDField.setDefaultValue(ccList[d].id);
              cardTypeField.setDefaultValue(ccList[d].typesel);
              cardNumberField.setDefaultValue(ccList[d].num);
              cardNumberFieldActual.setDefaultValue(ccList[d].num);
              cardNameField.setDefaultValue(ccList[d].name);
              cardIdentField.setDefaultValue(ccList[d].memo);
              cardExpField.setDefaultValue(ccList[d].exp);
            }
          }
        }
      }
    }
  }

  form.setScript("customscript_ng_cs_cl_exhib_pymnt_form");

  form.addResetButton("Reset");
  form.addSubmitButton("Submit");
  response.writePage(form);
}

function processRequest(request, response) {
  var showp =
    request.getParameter("custparam_show") != null
      ? request.getParameter("custparam_show")
      : "";
  var boothp =
    request.getParameter("custparam_booth") != null
      ? request.getParameter("custparam_booth")
      : "";
  var totalp =
    request.getParameter("custparam_total") != null
      ? request.getParameter("custparam_total")
      : "";
  var useCard =
    request.getParameter("custparam_usecard") != null
      ? request.getParameter("custparam_usecard")
      : "";
  var orderp =
    request.getParameter("custparam_order") != null
      ? request.getParameter("custparam_order")
      : "";
  var cccardID =
    request.getParameter("custparam_cardid") != null
      ? request.getParameter("custparam_cardid")
      : "";
  var ccnamep =
    request.getParameter("custparam_cardholder") != null
      ? request.getParameter("custparam_cardholder")
      : "";
  var ccnump =
    request.getParameter("custparam_cardnum") != null
      ? request.getParameter("custparam_cardnum")
      : "";
  var ccexpp =
    request.getParameter("custparam_cardexp") != null
      ? request.getParameter("custparam_cardexp")
      : "";
  var cccodep =
    request.getParameter("custparam_cardcode") != null
      ? request.getParameter("custparam_cardcode")
      : "";
  var cctypep =
    request.getParameter("custparam_cardtype") != null
      ? request.getParameter("custparam_cardtype")
      : "";

  var status = {};
  status["showp"] = showp;
  status["boothp"] = boothp;
  status["totalp"] = totalp;
  status["useCard"] = useCard;
  status["orderp"] = orderp;
  status["cccardID"] = cccardID;
  status["ccnamep"] = ccnamep;
  status["ccnump"] = ccnump;
  status["ccexpp"] = ccexpp;
  status["cccodep"] = cccodep;
  status["cctypep"] = cctypep;

  var booth = request.getParameter("custpage_booth");
  var showTableId = request.getParameter("custpage_show");
  var exhibitor = request.getParameter("custpage_exhibitor");
  var order = request.getParameter("custpage_order");
  var csJob = request.getParameter("custpage_job");
  var financial = request.getParameter("custpage_financial");
  var payType = request.getParameter("custpage_paymenttype");
  var payAmount = new Number(request.getParameter("custpage_payment"));
  var ccType = request.getParameter("custpage_cardtype");
  var ccNumber = request.getParameter("custpage_cardnumberactual");
  var ccName = request.getParameter("custpage_cardname");
  var ccExp = request.getParameter("custpage_expdate");
  var ccMemo = request.getParameter("custpage_cardidentity");
  var cardID = request.getParameter("custpage_cccardid");
  var chequeNumber = request.getParameter("custpage_chequenumber");
  var authOnly = request.getParameter("custpage_authonly");
  var addyLineOne = request.getParameter("custpage_addy_line_1");
  var addyZip = request.getParameter("custpage_addy_zip");
  var convFee = new Number(request.getParameter("custpage_cc_conv_fee_amount"));

  var department = null;
  var location = null;
  var subsidiary = null;
  if (_UseDepartments) {
    department = request.getParameter("custpage_department");
  }
  if (_UseLocations) {
    location = request.getParameter("custpage_location");
  }
  if (_UseSubsidiaries) {
    subsidiary = request.getParameter("custpage_subsidiary");
  }
  var recType = null;

  var useConvFee = _scLib.UseConvenienceFee;
  var convFeeRate =
    new Number(_scLib.ConvenienceFeeRate.replace("%", "")) / 100;
  var convFeeItem = _scLib.ConvenienceFeeItem;

  status["deptp"] = department;
  status["locp"] = location;
  status["subp"] = subsidiary;

  if (authOnly != "T") {
    if (useConvFee && !_tools.isEmpty(convFeeItem)) {
      var ordType = nlapiLookupField(
        "salesorder",
        order,
        "custbody_ng_cs_order_type"
      );
      if (!_tools.isInArray(ordType, _scLib.ConvenienceFeeOrderTypes)) {
        useConvFee = false;
      }
    } else if (useConFee && _tools.isEmpty(convFeeItem)) {
      useConvFee = false;
    }

    var payRec = null;
    if (_scLib.PaymentType == "2") {
      payRec = nlapiCreateRecord("customerdeposit", {
        recordmode: "dynamic",
        salesorder: order,
      });
    } else {
      payRec = nlapiTransformRecord("customer", exhibitor, "customerpayment", {
        recordmode: "dynamic",
      });
    }

    recType = payRec.getRecordType();

    payRec.setFieldValue("custbody_show_table", showTableId);
    payRec.setFieldValue("custbody_booth", booth);
    if (_UseCSJobs) {
      payRec.setFieldValue("custbody_cseg_ng_cs_job", csJob);
      payRec.setFieldValue("class", financial);
    } else {
      payRec.setFieldValue("class", financial);
    }
    payRec.setFieldValue("custbody_booth_order", order);

    if (_UseLocations && !_tools.isEmpty(location)) {
      payRec.setFieldValue("location", location);
    }
    if (_UseSubsidiaries && !_tools.isEmpty(subsidiary)) {
      payRec.setFieldValue("subsidiary", subsidiary);
    }
    if (_UseDepartments && !_tools.isEmpty(department)) {
      payRec.setFieldValue("department", department);
    }

    payRec.setFieldValue("autoapply", "F");
    if (_UndepositedFunds == "T")
      payRec.setFieldValue("undepfunds", _UndepositedFunds);
    else if (_DefaultDepositAccount != null)
      payRec.setFieldValue("account", _DefaultDepositAccount);

    if (!_tools.isEmpty(_DefaultARAccount))
      payRec.setFieldValue("aracct", _DefaultARAccount);

    if (payType == "A") {
      // credit card payment
      if (testing) {
        payRec.setFieldValue("chargeit", "F");
      } else {
        payRec.setFieldValue("chargeit", "T");
      }

      if (_tools.isEmpty(cardID) && _tools.isEmpty(cccardID)) {
        payRec.setFieldValue("ignoreavs", "T");
        var exhbRec = nlapiLoadRecord("customer", exhibitor);
        exhbRec.selectNewLineItem("creditcards");
        exhbRec.setCurrentLineItemValue("creditcards", "ccdefault", "T");

        payRec.setFieldValue("paymentmethod", ccType);
        exhbRec.setCurrentLineItemValue("creditcards", "paymentmethod", ccType);

        payRec.setFieldValue("ccnumber", ccNumber);
        payRec.setFieldValue("ccname", ccName);
        payRec.setFieldValue("ccexpiredate", ccExp);

        exhbRec.setCurrentLineItemValue("creditcards", "ccnumber", ccNumber);
        exhbRec.setCurrentLineItemValue("creditcards", "ccname", ccName);
        exhbRec.setCurrentLineItemValue("creditcards", "ccexpiredate", ccExp);
        if (!_tools.isEmpty(ccMemo)) {
          exhbRec.setCurrentLineItemValue("creditcards", "ccmemo", ccMemo);
        }
        exhbRec.commitLineItem("creditcards");
        try {
          nlapiSubmitRecord(exhbRec, false, true);
        } catch (err) {
          _log.logError(
            err,
            "Error encountered during update of exhibitor with new credit card info",
            "CC Data || Number: " +
              ccNumber +
              " -- Name: " +
              ccName +
              " -- Exp: " +
              ccExp +
              " -- Pay Type: " +
              payType +
              " -- CC Type: " +
              ccType
          );
        }
      } else {
        if (!_tools.isEmpty(cccardID)) {
          payRec.setFieldValue("creditcard", cccardID);
        } else if (!_tools.isEmpty(cardID)) {
          payRec.setFieldValue("creditcard", cardID);
        }
      }

      payRec.setFieldValue("ccstreet", addyLineOne);
      payRec.setFieldValue("cczipcode", addyZip);

      if (_scLib.MultiCCProcessing) {
        payRec.setFieldValue(
          "creditcardprocessor",
          _scLib.selectProcessor(payRec)
        );
      }
    } else {
      payRec.setFieldValue("paymentmethod", payType);
      payRec.setFieldValue("checknum", chequeNumber);
    }

    payRec.setFieldValue("payment", payAmount);

    var iFilt = new Array(
      ["custbody_show_table", "anyof", [showTableId]],
      "and",
      ["custbody_booth", "anyof", [booth]],
      "and",
      ["mainline", "is", "T"]
    );
    var iCols = new Array(new nlobjSearchColumn("internalid", null, null));
    var iSearch = null;
    try {
      iSearch = nlapiSearchRecord("invoice", null, iFilt, iCols);
    } catch (err) {
      _log.logError(err, "Error encountered searching for related invoices");
    }

    if (iSearch != null) {
      var invID = iSearch[0].getValue("internalid");
      var applyLines = payRec.getLineItemCount("apply");
      if (applyLines > 0) {
        for (var a = 1; a <= applyLines; a++) {
          var lineID = payRec.getLineItemValue("apply", "internalid", a);
          if (lineID == invID) {
            payRec.setLineItemValue("apply", "apply", a, "T");
            payRec.setLineItemValue("apply", "amount", a, payAmount);
            break;
          }
        }
      }
    }

    payRec.setFieldValue("ignoreavs", "T");

    payRec.setFieldValue("trandate", nlapiDateToString(new Date(), "date"));
    if (_UseCSJobs) {
      payRec.setFieldValue("custbody_cseg_ng_cs_job", csJob);
      payRec.setFieldValue("class", financial);
    } else {
      payRec.setFieldValue("class", financial);
    }

    var payRecId = null;

    try {
      payRecId = nlapiSubmitRecord(payRec, true, false);
      status.payrecid = payRecId;
      status.orderid = order;

      if (useConvFee) {
        var orderRec = null;
        try {
          orderRec = nlapiLoadRecord("salesorder", order, {
            recordmode: "dynamic",
          });
          updateOrderWithFee(orderRec, convFee, convFeeItem);
        } catch (err) {
          var orderNum = !_tools.isEmpty(orderRec)
            ? orderRec.getFieldValue("tranid")
            : nlapiLookupField("salesorder", order, "tranid");
          _log.logError(
            err,
            "Error encountered updating order with CC Convenience Fee",
            "Order: {0} ({1}) -- Fee Amount: {2}".NG_Format(
              orderNum,
              order,
              convFee
            )
          );
          status.feeErr = err;
          status.feeMsg =
            "Could not update order {0} ({1}) with CC convenience fee of ${2}".NG_Format(
              orderNum,
              order,
              convFee
            );
        }
      }
    } catch (err) {
      _log.logError(
        err,
        "Error encountered submitting customer payment",
        "CC Data || Number: {0} -- Name: {1} -- Exp: {2} -- Pay Type: {3} -- CC Type: {4}".NG_Format(
          ccNumber,
          ccName,
          ccExp,
          payType,
          ccType
        )
      );
      status.err = err;
    }

    if (!_tools.isEmpty(payRecId) && payRecId != 0) {
      var fields = [];
      var values = [];
      if (_UseLocations && !_tools.isEmpty(location)) {
        fields.push("location");
        values.push(location);
      }
      if (_UseCSJobs) {
        fields.push("custbody_cseg_ng_cs_job", "class");
        values.push(csJob, financial);
      } else {
        fields.push("class");
        values.push(financial);
      }
      try {
        nlapiSubmitField(recType, payRecId, fields, values);
      } catch (err) {
        _log.logError(
          err,
          "Error encountered attempting to update Job and Location on payment"
        );
      }
    }
  } else {
    var authSO = nlapiTransformRecord("customer", exhibitor, "salesorder", {
      recordmode: "dynamic",
    });
    authSO.selectNewLineItem("item");
    authSO.setCurrentLineItemValue("item", "item", _AuthItem);
    authSO.setCurrentLineItemValue("item", "quantity", "1");
    authSO.commitLineItem("item");

    if (_UseDepartments) {
      authSO.setFieldValue("department", department);
    }
    if (_UseCSJobs) {
      authSO.setFieldValue("custbody_cseg_ng_cs_job", csJob);
      authSO.setFieldValue("class", financial);
    } else {
      authSO.setFieldValue("class", financial);
    }
    authSO.setFieldValue("custbody_ng_cs_order_type", "3");

    if (_tools.isEmpty(cardID)) {
      authSO.setFieldValue("paymentmethod", ccType);

      authSO.setFieldValue("ccnumber", ccNumber);
      authSO.setFieldValue("ccname", ccName);
      authSO.setFieldValue("ccexpiredate", ccExp);
    } else {
      authSO.setFieldValue("creditcard", cardID);
    }

    authSO.setFieldValue("ccstreet", addyLineOne);
    authSO.setFieldValue("cczipcode", addyZip);

    authSO.setFieldValue("ignoreavs", "T");

    authSO.setFieldValue("getauth", "T");

    if (_scLib.MultiCCProcessing) {
      authSO.setFieldValue(
        "creditcardprocessor",
        _scLib.selectProcessor(authSO)
      );
    }
    var authSOID = null;
    try {
      authSOID = nlapiSubmitRecord(authSO, true, true);
    } catch (err) {
      _log.logError(
        err,
        "Error encountered creating credit card authorization transaction"
      );
      status.err = err;
    }

    if (authSOID != null) {
      var newAuthSO = nlapiLoadRecord("salesorder", authSOID, {
        recordmode: "dynamic",
      });
      var ccHold = newAuthSO.getFieldValue("cchold");
      if (ccHold == "T") {
        var fCode = "";
        var ohDetail = newAuthSO.getFieldValue("ccholdetails");
        if (ohDetail.search("General decline of the card") >= 0)
          fCode = "Credit card failure: General decline of the card";
        else if (ohDetail.search("General decline by the processor") >= 0)
          fCode = "Credit card failure: General decline by the processor";
        else if (ohDetail.search("Expired card") >= 0)
          fCode = "Credit card failure: Expired card";
        else if (ohDetail.search("Invalid account number") >= 0)
          fCode = "Credit card failure: Invalid account number";
        else if (ohDetail.search("Stolen or lost card") >= 0)
          fCode = "Credit card failure: Stolen or lost card";
        else if (ohDetail.search("Bad credit card number") >= 0)
          fCode = "Credit card failure: Bad credit card number";
        else if (
          ohDetail.search(
            "An unexpected error occurred while processing the credit card"
          ) >= 0
        )
          fCode =
            "Credit card failure: An unexpected error occurred while processing the credit card";
        else fCode = "Credit card failure: Unknown reason";

        status.err = nlapiCreateError("NO_AUTH", fCode);
      } else {
        status.orderid = order;
        status.authOnly = authOnly;

        if (_tools.isEmpty(cardID)) {
          var exhbRec = nlapiLoadRecord("customer", exhibitor);
          exhbRec.selectNewLineItem("creditcards");
          exhbRec.setCurrentLineItemValue("creditcards", "ccdefault", "T");

          exhbRec.setCurrentLineItemValue(
            "creditcards",
            "paymentmethod",
            ccType
          );

          exhbRec.setCurrentLineItemValue("creditcards", "ccnumber", ccNumber);
          exhbRec.setCurrentLineItemValue("creditcards", "ccname", ccName);
          exhbRec.setCurrentLineItemValue("creditcards", "ccexpiredate", ccExp);
          if (!_tools.isEmpty(ccMemo)) {
            exhbRec.setCurrentLineItemValue("creditcards", "ccmemo", ccMemo);
          }
          exhbRec.commitLineItem("creditcards");
          try {
            nlapiSubmitRecord(exhbRec, false, true);
          } catch (err) {
            _log.logError(
              err,
              "Error encountered during update of exhibitor with new credit card info",
              "CC Data || Number: " +
                ccNumber +
                " -- Name: " +
                ccName +
                " -- Exp: " +
                ccExp +
                " -- Pay Type: " +
                payType +
                " -- CC Type: " +
                ccType
            );
            status.err = nlapiCreateError(
              "USER_ERROR",
              "Could not update exhibitor with new credit card"
            );
          }
        }
      }

      try {
        nlapiSubmitField("salesorder", authSOID, "custbody_to_be_deleted", "T");
      } catch (err) {
        _log.logError(
          err,
          "Error encountered marking authorization order for deletion"
        );
      }
    } else {
    }
  }

  displayForm(request, response, status);
}

function data_GetSalesOrderSelections(showTableId) {
  var filt = new Array(["mainline", "is", "T"], "and");
  if (!_tools.isEmpty(showTableId)) {
    filt.push(["custbody_show_table", "anyof", [showTableId]]);
  } else {
    filt.push(["custbody_show_table", "noneof", ["@NONE@"]]);
  }
  var cols = new Array(
    new nlobjSearchColumn("tranid", null, null),
    new nlobjSearchColumn("custbody_show_table", null, null)
  );
  cols[0].setSort();
  var results = null;
  var orderList = new Array();
  try {
    var search = nlapiCreateSearch("salesorder", filt, cols);
    results = _tools.getSearchResults(search.runSearch(), false);
  } catch (err) {
    _log.logError(err, "Error encountered retreiving booth orders");
  }

  if (results != null) {
    for (var r = 0; r < results.length; r++) {
      orderList.push({
        value: results[r].getId(),
        text: "Order #{0}{1}".NG_Format(
          results[r].getValue("tranid"),
          _tools.isEmpty(showTableId)
            ? " ({0})".NG_Format(results[r].getText("custbody_show_table"))
            : ""
        ),
      });
    }
  }

  return orderList;
}

function setBGColor(bgHex) {
  var html = "";
  html += '<script type="text/javascript">';
  html +=
    "document.getElementById('tr_fg_submissionresponse').style.backgroundColor=\"" +
    bgHex +
    '";';
  html += "</script>";

  return html;
}

function updateOrderWithFee(rec, convFee, convFeeItem) {
  rec.selectNewLineItem("item");
  rec.setCurrentLineItemValue("item", "item", convFeeItem);
  rec.setCurrentLineItemValue("item", "quantity", 1);
  rec.setCurrentLineItemValue("item", "price", "-1");
  rec.setCurrentLineItemValue("item", "rate", convFee);
  rec.commitLineItem("item");
  nlapiSubmitRecord(rec, true, true);
}
