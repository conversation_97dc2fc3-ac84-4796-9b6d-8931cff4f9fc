//'use strict';

//Creating namespace
var NewGen = NewGen || {};
NewGen.lib = NewGen.lib || {};
var navigator = navigator || null;

NewGen.lib = {
		// placeholders: <RECTYPE>  successfully saved
	successMsg : "<div id=\"div__alert\" align=\"center\" width=\"100%\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#DAEBD5\"><tbody><tr><td><img src=\"/images/icons/messagebox/msgbox_corner_tl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img src=\"/images/icons/messagebox/msgbox_corner_tr.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img src=\"/images/icons/messagebox/icon_msgbox_confirmation.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><b>Confirmation:</b> <RECTYPE> successfully saved</td></tr><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img src=\"/images/icons/messagebox/msgbox_corner_bl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img src=\"/images/icons/messagebox/msgbox_corner_br.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>",
		// placeholders: <RECORD> <ERROR_CODE> <ERROR_DETAIL>  failed to save with error
	failureMsg : "<div id=\"div__alert\" align=\"center\" width=\"100%\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFAD9F\"><tbody><tr><td><img src=\"/images/icons/messagebox/msgbox_corner_tl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img src=\"/images/icons/messagebox/msgbox_corner_tr.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img src=\"/images/icons/messagebox/icon_msgbox_error.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><b>Error:</b> <RECORD> could not be Saved<br /><br />[<ERROR_CODE>]<br /><i><ERROR_DETAIL></i></td></tr><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img src=\"/images/icons/messagebox/msgbox_corner_bl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img src=\"/images/icons/messagebox/msgbox_corner_br.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>",
		// placeholders: <MESSAGE>
	failureMsgAlt : "<div id=\"div__alert\" align=\"center\" width=\"100%\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFAD9F\"><tbody><tr><td><img src=\"/images/icons/messagebox/msgbox_corner_tl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img src=\"/images/icons/messagebox/msgbox_corner_tr.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img src=\"/images/icons/messagebox/icon_msgbox_error.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><MESSAGE></td></tr><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img src=\"/images/icons/messagebox/msgbox_corner_bl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img src=\"/images/icons/messagebox/msgbox_corner_br.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>",
		// placeholders: <MESSAGE>
	warningMsg : "<div id=\"div__alert\" align=\"center\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFF2CC\"><tbody><tr><td><img src=\"/images/icons/messagebox/msgbox_corner_tl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img src=\"/images/icons/messagebox/msgbox_corner_tr.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img src=\"/images/icons/messagebox/icon_msgbox_warning.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><b>WARNING:</b> <MESSAGE></td></tr><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img src=\"/images/icons/messagebox/msgbox_corner_bl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img src=\"/images/icons/messagebox/msgbox_corner_br.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>",
		// placeholders: <MESSAGE>
	warningMsgAlt : "<div id=\"div__alert\" align=\"center\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFF2CC\"><tbody><tr><td><img src=\"/images/icons/messagebox/msgbox_corner_tl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img src=\"/images/icons/messagebox/msgbox_corner_tr.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img src=\"/images/icons/messagebox/icon_msgbox_warning.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><MESSAGE></td></tr><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img src=\"/images/icons/messagebox/msgbox_corner_bl.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img src=\"/images/icons/messagebox/msgbox_corner_br.png\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>",
		// placeholders: <MESSAGE>
	successMsgAction : "<div id=\"div__alert\" align=\"center\" width=\"100%\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#DAEBD5\"><tbody><tr><td><img src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img src=\"/images/icons/messagebox/icon_msgbox_confirmation.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><MESSAGE></td></tr><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>",
		// placeholders: <MESSAGE> <ERROR_CODE> <ERROR_DETAIL>
	failureMsgAction : "<div id=\"div__alert\" align=\"center\" width=\"100%\"><table style=\"margin:0px;\" width=\"100%\"><tbody><tr><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#FFAD9F\"><tbody><tr><td><img src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td width=\"40\"><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"1\" alt=\"\" hspace=\"20\"></td><td width=\"100%\"></td><td><img src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr><tr><td></td><td align=\"left\"><img src=\"/images/icons/messagebox/icon_msgbox_error.png\" alt=\"\" width=\"32\" height=\"32\" border=\"0\" style=\"margin-top:auto; margin-bottom:auto;\"></td><td width=\"100%\" valign=\"top\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"600\" style=\"font-size: 11px\"><tbody><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr><tr><td style=\"font-color: #000000\"><b>Error:</b> <MESSAGE><br /><br />[<ERROR_CODE>]<br /><i><ERROR_DETAIL></i></td></tr><tr><td><img src=\"/images/icons/reporting/x.gif\" width=\"1\" height=\"8\" alt=\"\"></td></tr></tbody></table></td><td></td></tr><tr><td><img src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td><td></td><td width=\"100%\"></td><td><img src=\"/images/icons/reporting/x.gif\" alt=\"\" width=\"7\" height=\"7\" border=\"0\"></td></tr></tbody></table></td></tr></tbody></table></div>",
	
	countries : [{"value":"AF","text":"Afghanistan"},{"value":"AX","text":"Aland Islands"},{"value":"AL","text":"Albania"},{"value":"DZ","text":"Algeria"},{"value":"AS","text":"American Samoa"},{"value":"AD","text":"Andorra"},{"value":"AO","text":"Angola"},{"value":"AI","text":"Anguilla"},{"value":"AQ","text":"Antarctica"},{"value":"AG","text":"Antigua and Barbuda"},{"value":"AR","text":"Argentina"},{"value":"AM","text":"Armenia"},{"value":"AW","text":"Aruba"},{"value":"AU","text":"Australia"},{"value":"AT","text":"Austria"},{"value":"AZ","text":"Azerbaijan"},{"value":"BS","text":"Bahamas"},{"value":"BH","text":"Bahrain"},{"value":"BD","text":"Bangladesh"},{"value":"BB","text":"Barbados"},{"value":"BY","text":"Belarus"},{"value":"BE","text":"Belgium"},{"value":"BZ","text":"Belize"},{"value":"BJ","text":"Benin"},{"value":"BM","text":"Bermuda"},{"value":"BT","text":"Bhutan"},{"value":"BO","text":"Bolivia"},{"value":"BQ","text":"Bonaire, Saint Eustatius and Saba"},{"value":"BA","text":"Bosnia and Herzegovina"},{"value":"BW","text":"Botswana"},{"value":"BV","text":"Bouvet Island"},{"value":"BR","text":"Brazil"},{"value":"IO","text":"British Indian Ocean Territory"},{"value":"BN","text":"Brunei Darussalam"},{"value":"BG","text":"Bulgaria"},{"value":"BF","text":"Burkina Faso"},{"value":"BI","text":"Burundi"},{"value":"KH","text":"Cambodia"},{"value":"CM","text":"Cameroon"},{"value":"CA","text":"Canada"},{"value":"IC","text":"Canary Islands"},{"value":"CV","text":"Cape Verde"},{"value":"KY","text":"Cayman Islands"},{"value":"CF","text":"Central African Republic"},{"value":"EA","text":"Ceuta and Melilla"},{"value":"TD","text":"Chad"},{"value":"CL","text":"Chile"},{"value":"CN","text":"China"},{"value":"CX","text":"Christmas Island"},{"value":"CC","text":"Cocos (Keeling) Islands"},{"value":"CO","text":"Colombia"},{"value":"KM","text":"Comoros"},{"value":"CD","text":"Congo, Democratic Republic of"},{"value":"CG","text":"Congo, Republic of"},{"value":"CK","text":"Cook Islands"},{"value":"CR","text":"Costa Rica"},{"value":"CI","text":"Cote d'Ivoire"},{"value":"HR","text":"Croatia/Hrvatska"},{"value":"CU","text":"Cuba"},{"value":"CW","text":"Cura�ao"},{"value":"CY","text":"Cyprus"},{"value":"CZ","text":"Czech Republic"},{"value":"DK","text":"Denmark"},{"value":"DJ","text":"Djibouti"},{"value":"DM","text":"Dominica"},{"value":"DO","text":"Dominican Republic"},{"value":"TL","text":"East Timor"},{"value":"EC","text":"Ecuador"},{"value":"EG","text":"Egypt"},{"value":"SV","text":"El Salvador"},{"value":"GQ","text":"Equatorial Guinea"},{"value":"ER","text":"Eritrea"},{"value":"EE","text":"Estonia"},{"value":"ET","text":"Ethiopia"},{"value":"FK","text":"Falkland Islands"},{"value":"FO","text":"Faroe Islands"},{"value":"FJ","text":"Fiji"},{"value":"FI","text":"Finland"},{"value":"FR","text":"France"},{"value":"GF","text":"French Guiana"},{"value":"PF","text":"French Polynesia"},{"value":"TF","text":"French Southern Territories"},{"value":"GA","text":"Gabon"},{"value":"GM","text":"Gambia"},{"value":"GE","text":"Georgia"},{"value":"DE","text":"Germany"},{"value":"GH","text":"Ghana"},{"value":"GI","text":"Gibraltar"},{"value":"GR","text":"Greece"},{"value":"GL","text":"Greenland"},{"value":"GD","text":"Grenada"},{"value":"GP","text":"Guadeloupe"},{"value":"GU","text":"Guam"},{"value":"GT","text":"Guatemala"},{"value":"GG","text":"Guernsey"},{"value":"GN","text":"Guinea"},{"value":"GW","text":"Guinea-Bissau"},{"value":"GY","text":"Guyana"},{"value":"HT","text":"Haiti"},{"value":"HM","text":"Heard and McDonald Islands"},{"value":"VA","text":"Holy See (City Vatican State)"},{"value":"HN","text":"Honduras"},{"value":"HK","text":"Hong Kong"},{"value":"HU","text":"Hungary"},{"value":"IS","text":"Iceland"},{"value":"IN","text":"India"},{"value":"ID","text":"Indonesia"},{"value":"IR","text":"Iran (Islamic Republic of)"},{"value":"IQ","text":"Iraq"},{"value":"IE","text":"Ireland"},{"value":"IM","text":"Isle of Man"},{"value":"IL","text":"Israel"},{"value":"IT","text":"Italy"},{"value":"JM","text":"Jamaica"},{"value":"JP","text":"Japan"},{"value":"JE","text":"Jersey"},{"value":"JO","text":"Jordan"},{"value":"KZ","text":"Kazakhstan"},{"value":"KE","text":"Kenya"},{"value":"KI","text":"Kiribati"},{"value":"KP","text":"Korea, Democratic People's Republic"},{"value":"KR","text":"Korea, Republic of"},{"value":"XK","text":"Kosovo"},{"value":"KW","text":"Kuwait"},{"value":"KG","text":"Kyrgyzstan"},{"value":"LA","text":"Lao People's Democratic Republic"},{"value":"LV","text":"Latvia"},{"value":"LB","text":"Lebanon"},{"value":"LS","text":"Lesotho"},{"value":"LR","text":"Liberia"},{"value":"LY","text":"Libya"},{"value":"LI","text":"Liechtenstein"},{"value":"LT","text":"Lithuania"},{"value":"LU","text":"Luxembourg"},{"value":"MO","text":"Macau"},{"value":"MK","text":"Macedonia"},{"value":"MG","text":"Madagascar"},{"value":"MW","text":"Malawi"},{"value":"MY","text":"Malaysia"},{"value":"MV","text":"Maldives"},{"value":"ML","text":"Mali"},{"value":"MT","text":"Malta"},{"value":"MH","text":"Marshall Islands"},{"value":"MQ","text":"Martinique"},{"value":"MR","text":"Mauritania"},{"value":"MU","text":"Mauritius"},{"value":"YT","text":"Mayotte"},{"value":"MX","text":"Mexico"},{"value":"FM","text":"Micronesia, Federal State of"},{"value":"MD","text":"Moldova, Republic of"},{"value":"MC","text":"Monaco"},{"value":"MN","text":"Mongolia"},{"value":"ME","text":"Montenegro"},{"value":"MS","text":"Montserrat"},{"value":"MA","text":"Morocco"},{"value":"MZ","text":"Mozambique"},{"value":"MM","text":"Myanmar (Burma)"},{"value":"NA","text":"Namibia"},{"value":"NR","text":"Nauru"},{"value":"NP","text":"Nepal"},{"value":"NL","text":"Netherlands"},{"value":"AN","text":"Netherlands Antilles (Deprecated)"},{"value":"NC","text":"New Caledonia"},{"value":"NZ","text":"New Zealand"},{"value":"NI","text":"Nicaragua"},{"value":"NE","text":"Niger"},{"value":"NG","text":"Nigeria"},{"value":"NU","text":"Niue"},{"value":"NF","text":"Norfolk Island"},{"value":"MP","text":"Northern Mariana Islands"},{"value":"NO","text":"Norway"},{"value":"OM","text":"Oman"},{"value":"PK","text":"Pakistan"},{"value":"PW","text":"Palau"},{"value":"PS","text":"Palestinian Territories"},{"value":"PA","text":"Panama"},{"value":"PG","text":"Papua New Guinea"},{"value":"PY","text":"Paraguay"},{"value":"PE","text":"Peru"},{"value":"PH","text":"Philippines"},{"value":"PN","text":"Pitcairn Island"},{"value":"PL","text":"Poland"},{"value":"PT","text":"Portugal"},{"value":"PR","text":"Puerto Rico"},{"value":"QA","text":"Qatar"},{"value":"RE","text":"Reunion Island"},{"value":"RO","text":"Romania"},{"value":"RU","text":"Russian Federation"},{"value":"RW","text":"Rwanda"},{"value":"BL","text":"Saint Barth�lemy"},{"value":"SH","text":"Saint Helena"},{"value":"KN","text":"Saint Kitts and Nevis"},{"value":"LC","text":"Saint Lucia"},{"value":"MF","text":"Saint Martin"},{"value":"VC","text":"Saint Vincent and the Grenadines"},{"value":"WS","text":"Samoa"},{"value":"SM","text":"San Marino"},{"value":"ST","text":"Sao Tome and Principe"},{"value":"SA","text":"Saudi Arabia"},{"value":"SN","text":"Senegal"},{"value":"RS","text":"Serbia"},{"value":"CS","text":"Serbia and Montenegro (Deprecated)"},{"value":"SC","text":"Seychelles"},{"value":"SL","text":"Sierra Leone"},{"value":"SG","text":"Singapore"},{"value":"SX","text":"Sint Maarten"},{"value":"SK","text":"Slovak Republic"},{"value":"SI","text":"Slovenia"},{"value":"SB","text":"Solomon Islands"},{"value":"SO","text":"Somalia"},{"value":"ZA","text":"South Africa"},{"value":"GS","text":"South Georgia"},{"value":"SS","text":"South Sudan"},{"value":"ES","text":"Spain"},{"value":"LK","text":"Sri Lanka"},{"value":"PM","text":"St. Pierre and Miquelon"},{"value":"SD","text":"Sudan"},{"value":"SR","text":"Suriname"},{"value":"SJ","text":"Svalbard and Jan Mayen Islands"},{"value":"SZ","text":"Swaziland"},{"value":"SE","text":"Sweden"},{"value":"CH","text":"Switzerland"},{"value":"SY","text":"Syrian Arab Republic"},{"value":"TW","text":"Taiwan"},{"value":"TJ","text":"Tajikistan"},{"value":"TZ","text":"Tanzania"},{"value":"TH","text":"Thailand"},{"value":"TG","text":"Togo"},{"value":"TK","text":"Tokelau"},{"value":"TO","text":"Tonga"},{"value":"TT","text":"Trinidad and Tobago"},{"value":"TN","text":"Tunisia"},{"value":"TR","text":"Turkey"},{"value":"TM","text":"Turkmenistan"},{"value":"TC","text":"Turks and Caicos Islands"},{"value":"TV","text":"Tuvalu"},{"value":"UG","text":"Uganda"},{"value":"UA","text":"Ukraine"},{"value":"AE","text":"United Arab Emirates"},{"value":"GB","text":"United Kingdom (GB)"},{"value":"US","text":"United States"},{"value":"UY","text":"Uruguay"},{"value":"UM","text":"US Minor Outlying Islands"},{"value":"UZ","text":"Uzbekistan"},{"value":"VU","text":"Vanuatu"},{"value":"VE","text":"Venezuela"},{"value":"VN","text":"Vietnam"},{"value":"VG","text":"Virgin Islands (British)"},{"value":"VI","text":"Virgin Islands (USA)"},{"value":"WF","text":"Wallis and Futuna"},{"value":"EH","text":"Western Sahara"},{"value":"YE","text":"Yemen"},{"value":"ZM","text":"Zambia"},{"value":"ZW","text":"Zimbabwe"}],
	csList : ["US","CA","MX"],
	states : {
		"US" : [{"value":"AL","text":"Alabama"},{"value":"AK","text":"Alaska"},{"value":"AZ","text":"Arizona"},{"value":"AR","text":"Arkansas"},{"value":"AA","text":"Armed Forces Americas"},{"value":"AE","text":"Armed Forces Europe"},{"value":"AP","text":"Armed Forces Pacific"},{"value":"CA","text":"California"},{"value":"CO","text":"Colorado"},{"value":"CT","text":"Connecticut"},{"value":"DE","text":"Delaware"},{"value":"DC","text":"District of Columbia"},{"value":"FL","text":"Florida"},{"value":"GA","text":"Georgia"},{"value":"HI","text":"Hawaii"},{"value":"ID","text":"Idaho"},{"value":"IL","text":"Illinois"},{"value":"IN","text":"Indiana"},{"value":"IA","text":"Iowa"},{"value":"KS","text":"Kansas"},{"value":"KY","text":"Kentucky"},{"value":"LA","text":"Louisiana"},{"value":"ME","text":"Maine"},{"value":"MD","text":"Maryland"},{"value":"MA","text":"Massachusetts"},{"value":"MI","text":"Michigan"},{"value":"MN","text":"Minnesota"},{"value":"MS","text":"Mississippi"},{"value":"MO","text":"Missouri"},{"value":"MT","text":"Montana"},{"value":"NE","text":"Nebraska"},{"value":"NV","text":"Nevada"},{"value":"NH","text":"New Hampshire"},{"value":"NJ","text":"New Jersey"},{"value":"NM","text":"New Mexico"},{"value":"NY","text":"New York"},{"value":"NC","text":"North Carolina"},{"value":"ND","text":"North Dakota"},{"value":"OH","text":"Ohio"},{"value":"OK","text":"Oklahoma"},{"value":"OR","text":"Oregon"},{"value":"PA","text":"Pennsylvania"},{"value":"PR","text":"Puerto Rico"},{"value":"RI","text":"Rhode Island"},{"value":"SC","text":"South Carolina"},{"value":"SD","text":"South Dakota"},{"value":"TN","text":"Tennessee"},{"value":"TX","text":"Texas"},{"value":"UT","text":"Utah"},{"value":"VT","text":"Vermont"},{"value":"VA","text":"Virginia"},{"value":"WA","text":"Washington"},{"value":"WV","text":"West Virginia"},{"value":"WI","text":"Wisconsin"},{"value":"WY","text":"Wyoming"},{"value":"AS","text":"American Samoa"},{"value":"GU","text":"Guam"},{"value":"MP","text":"Northern Mariana Islands"},{"value":"UM","text":"United States Minor Outlying Islands"},{"value":"VI","text":"Virgin Islands"}],
		"CA" : [{"value":"AB","text":"Alberta"},{"value":"BC","text":"British Columbia"},{"value":"MB","text":"Manitoba"},{"value":"NB","text":"New Brunswick"},{"value":"NL","text":"Newfoundland"},{"value":"NT","text":"Northwest Territories"},{"value":"NS","text":"Nova Scotia"},{"value":"NU","text":"Nunavut"},{"value":"ON","text":"Ontario"},{"value":"PE","text":"Prince Edward Island"},{"value":"QC","text":"Quebec"},{"value":"SK","text":"Saskatchewan"},{"value":"YT","text":"Yukon"}],
		"MX" : [{"value":"AG","text":"Aguascalientes"},{"value":"BC","text":"Baja California"},{"value":"BS","text":"Baja California Sur"},{"value":"CM","text":"Campeche"},{"value":"CS","text":"Chiapas"},{"value":"CH","text":"Chihuahua"},{"value":"CO","text":"Coahuila"},{"value":"CL","text":"Colima"},{"value":"DF","text":"Distrito Federal"},{"value":"DG","text":"Durango"},{"value":"GT","text":"Guanajuanto"},{"value":"GR","text":"Guerrero"},{"value":"HG","text":"Hidalgo"},{"value":"JA","text":"Jalisco"},{"value":"MX","text":"Mexico"},{"value":"MI","text":"Michoacan"},{"value":"MO","text":"Morelos"},{"value":"NA","text":"Nayarit"},{"value":"NL","text":"Nuevo Leon"},{"value":"OA","text":"Oaxaca"},{"value":"PU","text":"Puebla"},{"value":"QT","text":"Queretaro"},{"value":"QR","text":"Quintana Roo"},{"value":"SL","text":"San Luis Potosi"},{"value":"SI","text":"Sinaloa"},{"value":"SO","text":"Sonora"},{"value":"TB","text":"Tabasco"},{"value":"TM","text":"Tamaulipas"},{"value":"TL","text":"Tlaxcala"},{"value":"VE","text":"Veracruz"},{"value":"YU","text":"Yucatan"},{"value":"ZA","text":"Zacatecas"}]
	},
	
	lineBreak : /\r\n|\r|\n/,
	
	nsDateRegEx : new RegExp("^(1[0-2]|[1-9])/(3[01]|[12][0-9]|[1-9])/[12][0-9]{3}$"),
	
	rType : {
			so :		"salesorder"
		,	iff :		"itemfulfillment"
		,	inv :		"invoice"
		,	cs :		"cashsale"
		,	est :		"estimate"
		,	op :		"opportunity"
		,	rf :		"customerrefund"
		,	cdep :		"customerdeposit"
		,	dpap :		"depositapplication"
		,	dep :		"deposit"
		,	cm :		"creditmemo"
		,	pay :		"customerpayment"
		,	adj :		"inventoryadjustment"
		,	item :		"item"
		,	invi :		"inventoryitem"
		,	ninvi :		"noninventoryitem"
		,	asmb :		"assemblyitem"
		,	kit :		"kititem"
		,	cus :		"customer"
		,	lead :		"lead"
		,	pros : 		"prospect"
		,	prtr :	 	"partner"
		,	vend :		"vendor"
		,	con :		"contact"
		,	je :		"journalentry"
		,	case :		"supportcase"
	},
	
	altType : {
		so : "SalesOrd"
	},
	
	recStatus : {
		soPendAprvl : 	"A",
		soPendFulfill :	"B",
		soCancelled :	"C",
		soPartFulfill :	"D",
		soBillFulfill : "E",
		soPendBill : 	"F",
		soBilled :		"G",
		soClosed :		"H"
	},
	
	altRecStatus : {
		soPendAprvl : 	"SalesOrd:A",
		soPendFulfill :	"SalesOrd:B",
		soCancelled :	"SalesOrd:C",
		soPartFulfill :	"SalesOrd:D",
		soBillFulfill : "SalesOrd:E",
		soPendBill : 	"SalesOrd:F",
		soBilled :		"SalesOrd:G",
		soClosed :		"SalesOrd:H"
	},
	
	textRecStatus : {
		soPendAprvl : 	"pendingApproval",
		soPendFulfill :	"pendingFulfillment",
		soCancelled :	"cancelled",
		soPartFulfill :	"partiallyFulfilled",
		soBillFulfill : "pendingBillingPartFulfilled",
		soPendBill : 	"pendingBilling",
		soBilled :		"fullyBilled",
		soClosed :		"closed"
	},
	
	dynRec : {
		recordmode : 	"dynamic"
	},
	
	dynRecTrig : {
		recordmode : 			"dynamic",
		enablefieldtriggers : 	true
	},
	
	features : {
			ADVINVENTORYMGMT : function() { try { return nlapiGetContext().getSetting("FEATURE", "ADVINVENTORYMGMT"); } catch (err) { return "F"; } }
		,	CLASSES : function() { try { return nlapiGetContext().getSetting("FEATURE", "CLASSES"); } catch (err) { return "F"; } }
		,	DEPARTMENTS : function() { try { return nlapiGetContext().getSetting("FEATURE", "DEPARTMENTS"); } catch (err) { return "F"; } }
		,	DOCUMENTS : function() { try { return nlapiGetContext().getSetting("FEATURE", "DOCUMENTS"); } catch (err) { return "F"; } }
		,	DUPLICATES : function() { try { return nlapiGetContext().getSetting("FEATURE", "DUPLICATES"); } catch (err) { return "F"; } }
		,	GIFTCERTIFICATES : function() { try { return nlapiGetContext().getSetting("FEATURE", "GIFTCERTIFICATES"); } catch (err) { return "F"; } }
		,	GROSSPROFIT : function() { try { return nlapiGetContext().getSetting("FEATURE", "GROSSPROFIT"); } catch (err) { return "F"; } }
		,	INVAPPROVAL : function() { try { return nlapiGetContext().getSetting("FEATURE", "CUSTOMAPPROVALCUSTINVC"); } catch (err) { return "F"; } }
		,	INVENTORY : function() { try { return nlapiGetContext().getSetting("FEATURE", "INVENTORY"); } catch (err) { return "F"; } }
		,	ITEMOPTIONS : function() { try { return nlapiGetContext().getSetting("FEATURE", "ITEMOPTIONS"); } catch (err) { return "F"; } }
		,	LANDEDCOST : function() { try { return nlapiGetContext().getSetting("FEATURE", "LANDEDCOST"); } catch (err) { return "F"; } }
		,	LOCATIONS : function() { try { return nlapiGetContext().getSetting("FEATURE", "LOCATIONS"); } catch (err) { return "F"; } }
		,	LOTNUMBEREDINVENTORY : function() { try { return nlapiGetContext().getSetting("FEATURE", "LOTNUMBEREDINVENTORY"); } catch (err) { return "F"; } }
		,	MATRIXITEMS : function() { try { return nlapiGetContext().getSetting("FEATURE", "MATRIXITEMS"); } catch (err) { return "F"; } }
		,	MULTICURRENCY : function() { try { return nlapiGetContext().getSetting("FEATURE", "MULTICURRENCY"); } catch (err) { return "F"; } }
		,	MULTICURRENCYVENDOR : function() { try { return nlapiGetContext().getSetting("FEATURE", "MULTICURRENCYVENDOR"); } catch (err) { return "F"; } }
		,	MULTILOCINVT : function() { try { return nlapiGetContext().getSetting("FEATURE", "MULTILOCINVT"); } catch (err) { return "F"; } }
		,	MULTIPARTNER : function() { try { return nlapiGetContext().getSetting("FEATURE", "MULTIPARTNER"); } catch (err) { return "F"; } }
		,	MULTPRICE : function() { try { return nlapiGetContext().getSetting("FEATURE", "MULTPRICE"); } catch (err) { return "F"; } }
		,	MULTISUBCUSTOMER : function() { try { return nlapiGetContext().getSetting("FEATURE", "MULTISUBSIDIARYCUSTOMER"); } catch (err) { return "F"; } }
		,	PICKPACKSHIP : function() { try { return nlapiGetContext().getSetting("FEATURE", "PICKPACKSHIP"); } catch (err) { return "F"; } }
		,	QUANTITYPRICING : function() { try { return nlapiGetContext().getSetting("FEATURE", "QUANTITYPRICING"); } catch (err) { return "F"; } }
		,	REVENUERECOGNITION : function() { try { return nlapiGetContext().getSetting("FEATURE", "REVENUERECOGNITION"); } catch (err) { return "F"; } }
		,	SERIALIZEDINVENTORY : function() { try { return nlapiGetContext().getSetting("FEATURE", "SERIALIZEDINVENTORY"); } catch (err) { return "F"; } }
		,	SHIPPINGLABELS : function() { try { return nlapiGetContext().getSetting("FEATURE", "SHIPPINGLABELS"); } catch (err) { return "F"; } }
		,	SUBSIDIARIES : function() { try { return nlapiGetContext().getSetting("FEATURE", "SUBSIDIARIES"); } catch (err) { return "F"; } }
		,	TIMETRACKING : function() { try { return nlapiGetContext().getSetting("FEATURE", "TIMETRACKING"); } catch (err) { return "F"; } }
		,	UNITSOFMEASURE : function() { try { return nlapiGetContext().getSetting("FEATURE", "UNITSOFMEASURE"); } catch (err) { return "F"; } }
	},
	
	subl : {
			invt :		"inventory"
		,	addr :		"addressbook"
		,	mem :		"member"
	},
	
	subr : {
			invtd :		"inventorydetail"
		,	addr :		"addressbookaddress"
	},
	
	math : {
		/**
		 * @returns {Number}
		 */
		roundToTenths: function (num) {
			if (isNaN(new Number(num)))
				return num;
			
			return new Number(((Math.round(num * 10)) / 10));
		},

		/**
		 * @returns {Number}
		 */
		roundToHundredths : function (num) {
			if (isNaN(new Number(num)))
				return num;
			
			return (Math.round(num * 100) / 100);
		},

		/**
		 * @returns {Number}
		 */
		roundToThousandths : function (num) {
			if (isNaN(new Number(num)))
				return num;
			
			return (Math.round(num * 1000) / 1000);
		},

		/**
		 * @returns {Number}
		 */
		roundToTenThousandths : function (num) {
			if (isNaN(new Number(num)))
				return num;
			
			return (Math.round(num * 10000) / 10000);
		},

		/**
		 * @returns {Number}
		 */
		roundToHundThousandths : function (num) {
			if (isNaN(new Number(num)))
				return num;
			
			return (Math.round(num * 100000) / 100000);
		},

		/**
		 * @param {Number} num Number to be rounded
		 * @param {Number} dec Positive integer value of decimal places to return
		 * 
		 * @returns {Number}
		 */
		roundToDecimal : function (num, dec) {
			if (isNaN(new Number(num)))
				return num;
			
			if (isNaN(new Number(dec)))
				return num;

			if (new Number(dec) < 0)
				return num;
			dec = Math.floor(new Number(dec));
			
			return (Math.round(num * Math.pow(10, dec)) / Math.pow(10, dec));
		},
		
		randomInt : function (min, max) {
			min = Math.ceil(min);
			max = Math.floor(max);
			return Math.floor(Math.random() * (max - min + 1)) + min;
		}
	},
	
	obj : {
		IsArray : function () {
			if (arguments.length == 0 || NewGen.lib.tools.isEmpty(arguments[0])) {
				return false;
			}
			return Array.isArray(arguments[0]) || Object.prototype.toString.call(arguments[0]) === "[object JavaArray]";
		},
		
		IsBoolean : function () {
			if (arguments.length == 0 || NewGen.lib.tools.isEmpty(arguments[0])) {
				return false;
			}
			if (typeof arguments[0] == 'boolean')
				return true;
			if (typeof arguments[0] == 'object')
			{
				if (arguments[0].constructor == null || arguments[0].constructor == undefined)
					return false;
				
				var criterion = arguments[0].constructor.toString().match(/boolean/i);
				return (criterion != null);
			}
			
			return false;
		},
		
		IsNumber : function () {
			if (arguments.length == 0 || NewGen.lib.tools.isEmpty(arguments[0])) {
				return false;
			}
			if (typeof arguments[0] == 'number')
				return true;
			if (typeof arguments[0] == 'object')
			{
				if (arguments[0].constructor == null || arguments[0].constructor == undefined)
					return false;
				
				var criterion = arguments[0].constructor.toString().match(/number/i);
				return (criterion != null);
			}
			
			return false;
		},
		
		IsString : function () {
			if (arguments.length == 0 || arguments[0] == null || arguments[0] == undefined) {
				return false;
			}
			if (typeof arguments[0] == 'string')
				return true;
			if (typeof arguments[0] == 'object')
			{
				if (arguments[0].constructor == null || arguments[0].constructor == undefined)
					return false;
				
				var criterion = arguments[0].constructor.toString().match(/string/i);
				return (criterion != null);
			}
			
			return false;
		}
	},
	
	tools : {
		isInArray : function (value, array, caseInsensitive) {
			var match = false;
			var ciValue = value;
			
			if (array == null)
				return match;
			
			if (!NewGen.lib.obj.IsArray(array))
				return match;
			
			if (array.length == 0)
				return match;
			
			if (caseInsensitive) {
				try { ciValue = value.toUpperCase(); } catch (err) {}
			}
			
			for (var i = 0; i < array.length; i++) {
				if (caseInsensitive) {
					try {
						if (ciValue == array[i].toUpperCase()) {
							match = true;
							break;
						}
					} catch (err) {
						if (value == array[i]) {
							match = true;
							break;
						}
					}
				} else {
					if (value == array[i]) {
						match = true;
						break;
					}
				}
			}
			return match;
		},
		
		isEmpty : function (value) {
			if (value == null || value == undefined || value === "")
				return true;
			
			return false;
		},
		
		isEmptyObject : function (object) {
			if (object == null || object == "" || object == undefined)
				return true;
			var counter = 0;
			for (var key in object)
			{
				if (object[key])
					counter++;
			}
			if (counter == 0)
				return true;
			return false;
		},
		
		isInObject : function (value, object) {
			if (object == null || object === "" || object == undefined)
				return false;
			if (value == null || value == undefined)
				return false;
			for (var key in object) {
				if (object[key] === value)
					return true;
			}
			
			return false;
		},
		
		hasKey : function (value, object) {
			if (object == null || object === "" || object == undefined)
				return false;
			if (value == null || value == undefined)
				return false;
			for (var key in object) {
				if (key == value)
					return true;
			}
			
			return false;
		},
		
		/**
		*
		*  Javascript trim, ltrim, rtrim
		*  http://www.webtoolkit.info/
		*
		**/

		trim : function (str, chars) {
			return NewGen.lib.tools.ltrim(NewGen.lib.tools.rtrim(str, chars), chars);
		},

		ltrim : function (str, chars) {
			chars = chars || "\\s";
			return str.replace(new RegExp("^[" + chars + "]+", "g"), "");
		},

		rtrim : function (str, chars) {
			chars = chars || "\\s";
			return str.replace(new RegExp("[" + chars + "]+$", "g"), "");
		},

		randomString : function (llen, nlen, rlen, isPW) {
			var letters = new Array("A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z");
			var numbers = new Array("0","1","2","3","4","5","6","7","8","9");
			var lower = new Array("a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z");
			var sChar = new Array("!","@","#","$","%","*","=");
			var rcode = "";
			var ll = letters.length;
			var nl = numbers.length;
			var pwTest = /[ IilO01]/g;
			isPW = isPW || false;
			
			if (rlen == null || isNaN(new Number(rlen)) || (new Number(rlen)) == 0) {
				for (var l = 0; l < llen; l++) {
					var pos = Math.floor(Math.random() * ll);
					rcode += letters[pos];
				}
				
				for (var n = 0; n < nlen; n++) {
					var pos = Math.floor(Math.random() * nl);
					rcode += numbers[pos];
				}
			} else {
				var full = new Array(); //letters.concat(numbers);
				if (isPW) {
					full = letters.concat(lower, numbers, sChar);
				} else {
					full = letters.concat(numbers);
				}
				var rl = full.length;
				
				for (var r = 0; r < rlen; r++) {
					if (isPW) {
						var char = " ";
						do {
							var pos = Math.floor(Math.random() * rl);
							char = full[pos];
						} while (pwTest.test(char));
						rcode += char;
					} else {
						var pos = Math.floor(Math.random() * rl);
						rcode += full[pos];
					}
				}
			}
			
			return rcode;
		},
		
		/**
		 * @param {Boolean} inDateTimeFormat If a date/time formated string is wanted, pass true (default: false)
		 * @param {String} zone Continental US time zone for desired time stamp; Options: EST,EDT,CST,CDT,MST,MDT,PST,PDT (default: current Netsuite server time zone)
		 * 
		 * @returns {String}
		 * NOTE: Does not factor in Daylight Savings Time
		 */
		timeStamp : function (inDateTimeFormat, zone, inDateTimeSecFormat) {
			return NewGen.lib.time.timeStamp(inDateTimeFormat, zone, inDateTimeSecFormat);
			/*try
			{
				var now = new Date();
				
				// attempt to convert time to designated time zone (default: current Netsuite server time zone)
				var nowString = now.toString();
				var est = nowString.search(/EDT/i);
				var cst = nowString.search(/CDT/i);
				var mst = nowString.search(/MDT/i);
				var pst = nowString.search(/PDT/i);
				var diff = new Number(0);
				
				if (zone != null && zone != "") {
					switch (zone) {
						case "EST":
						case "EDT":
							if (est != -1)
								diff = new Number(0);
							else if (cst != -1)
								diff = new Number(1);
							else if (mst != -1)
								diff = new Number(2);
							else if (pst != -1)
								diff = new Number(3);
							break;
						case "CST":
						case "CDT":
							if (est != -1)
								diff = new Number(-1);
							else if (cst != -1)
								diff = new Number(0);
							else if (mst != -1)
								diff = new Number(1);
							else if (pst != -1)
								diff = new Number(2);
							break;
						case "MST":
						case "MDT":
							if (est != -1)
								diff = new Number(-2);
							else if (cst != -1)
								diff = new Number(-1);
							else if (mst != -1)
								diff = new Number(0);
							else if (pst != -1)
								diff = new Number(1);
							break;
						case "PST":
						case "PDT":
							if (est != -1)
								diff = new Number(-3);
							else if (cst != -1)
								diff = new Number(-2);
							else if (mst != -1)
								diff = new Number(-1);
							else if (pst != -1)
								diff = new Number(0);
							break;
					}
				}
				
				var msIncrement = new Number(diff * 60 * 60 * 1000);
				var msNow = Date.parse(now);
				now = new Date(msNow + msIncrement);
				
				var disp = "";
				if (inDateTimeFormat)
				{
					disp = nlapiDateToString(now, "datetime");
				}
				else if (inDateTimeSecFormat)
				{
					disp = nlapiDateToString(now, "datetimetz");
				}
				else
				{
					var m = (now.getMonth() + 1).toString();
					var d = now.getDate().toString();
					var y = now.getFullYear().toString();
					var h = now.getHours();
					var mm = now.getMinutes().toString();
					var s = now.getSeconds().toString();
					
					if (m.length == 1)
						m = "0" + m;
					if (d.length == 1)
						d = "0" + d;
					if (h < 10)
						h = "0" + h;
					if (mm.length == 1)
						mm = "0" + mm;
					if (s.length == 1)
						s = "0" + s;
					disp = y + m + d + h + mm + s;
				}
				return disp;
			}
			catch (err)
			{
				return "";
			}*/
		},

		adjustTime : function(now, zone) {
			return NewGen.lib.time.adjustTime(now, zone);
			// attempt to convert time to designated time zone (default: current Netsuite server time zone)
			/*var nowString = now.toString();
			var est = nowString.search(/EDT/i);
			var cst = nowString.search(/CDT/i);
			var mst = nowString.search(/MDT/i);
			var pst = nowString.search(/PDT/i);
			var diff = new Number(0);
			
			if (zone != null && zone != "") {
				switch (zone) {
					case "EST":
					case "EDT":
						if (est != -1)
							diff = new Number(0);
						else if (cst != -1)
							diff = new Number(1);
						else if (mst != -1)
							diff = new Number(2);
						else if (pst != -1)
							diff = new Number(3);
						break;
					case "CST":
					case "CDT":
						if (est != -1)
							diff = new Number(-1);
						else if (cst != -1)
							diff = new Number(0);
						else if (mst != -1)
							diff = new Number(1);
						else if (pst != -1)
							diff = new Number(2);
						break;
					case "MST":
					case "MDT":
						if (est != -1)
							diff = new Number(-2);
						else if (cst != -1)
							diff = new Number(-1);
						else if (mst != -1)
							diff = new Number(0);
						else if (pst != -1)
							diff = new Number(1);
						break;
					case "PST":
					case "PDT":
						if (est != -1)
							diff = new Number(-3);
						else if (cst != -1)
							diff = new Number(-2);
						else if (mst != -1)
							diff = new Number(-1);
						else if (pst != -1)
							diff = new Number(0);
						break;
				}
			}
			
			var msIncrement = new Number(diff * 60 * 60 * 1000);
			var msNow = Date.parse(now);
			now = new Date(msNow + msIncrement);
			
			return now;*/
		},

		addMinutes : function (date, minutes) {
			return NewGen.lib.time.addMinutes(date, minutes);
			//return new Date(date.getTime() + (minutes * 60 * 1000));
		},

		logTimeElapsed : function (time1, time2, msg) {
			NewGen.lib.time.logTimeElapsed(time1, time2, msg);
			/*msg = msg != null && msg != "" ? msg : "Time Elapsed";
			var time1MS = time1 != null ? Date.parse(time1) : 0;
			var time2MS = time2 != null ? Date.parse(time2) : 0;
			var elapsed = (new Number(time2MS) - new Number(time1MS)) / 1000;
			nlapiLogExecution("AUDIT", msg, elapsed + " seconds");*/
		},

		getTimeElapsed : function (time1, time2) {
			return NewGen.lib.time.getTimeElapsed(time1, time2);
			/*var time1MS = time1 != null ? Date.parse(time1) : 0;
			var time2MS = time2 != null ? Date.parse(time2) : 0;
			return (new Number(time2MS) - new Number(time1MS)) / 1000;*/
		},

		/**
		 * @param {Date} dt Date object to be converted
		 * @returns {Object} data Object encapsulating the date parameter converted to a NetSuite UTC
		 * 			date/time string and the Oleson GMT time zone code (data.UTC, data.tz)
		 */
		setDateTimeField : function (dt) {
			return NewGen.lib.time.setDateTimeField(dt);
			/*var diff = new Number(dt.toString().match(/GMT[+-]\d\d\d\d/)[0].substr(4, 4).replace(/0/g, ""));
			if (dt.toString().search(/GMT+\d\d\d\d/) >= 0)
				diff = diff * (-1);
			var data = { };
			data.UTC =  nlapiDateToString(new Date(dt.setHours(dt.getHours() + diff)), "datetimetz");
			data.tz = "GMT";
			
			return data;*/
		},
		
		//Return array of string values, or NULL if CSV string not well formed.
		//code source: ridgerunner, StackOverflow.com
		CSVtoArray : function (text) {
			var re_valid = /^\s*(?:'[^'\\]*(?:\\[\S\s][^'\\]*)*'|"[^"\\]*(?:\\[\S\s][^"\\]*)*"|[^,'"\s\\]*(?:\s+[^,'"\s\\]+)*)\s*(?:,\s*(?:'[^'\\]*(?:\\[\S\s][^'\\]*)*'|"[^"\\]*(?:\\[\S\s][^"\\]*)*"|[^,'"\s\\]*(?:\s+[^,'"\s\\]+)*)\s*)*$/;
			var re_value = /(?!\s*$)\s*(?:'([^'\\]*(?:\\[\S\s][^'\\]*)*)'|"([^"\\]*(?:\\[\S\s][^"\\]*)*)"|([^,'"\s\\]*(?:\s+[^,'"\s\\]+)*))\s*(?:,|$)/g;
			// Return NULL if input string is not well formed CSV string.
			if (!re_valid.test(text)) return null;
			var a = [];                     // Initialize array to receive values.
			text.replace(re_value, // "Walk" the string using replace with callback.
				function(m0, m1, m2, m3) {
					// Remove backslash from \' in single quoted values.
					if (m1 !== undefined) a.push(m1.replace(/\\'/g, "'"));
					// Remove backslash from \" in double quoted values.
					else if (m2 !== undefined) a.push(m2.replace(/\\"/g, '"'));
					else if (m3 !== undefined) a.push(m3);
					return ''; // Return empty string.
				});
			// Handle special case of empty last value.
			if (/,\s*$/.test(text)) a.push('');
			return a;
		},

		/**
		 * @param {Object} keyedObject Data object with keyed values
		 * @returns {Number} Total count of unique data keys
		 */
		getKeyCount : function (keyedObject) {
			var counter = new Number(0);
			
			if (NewGen.lib.tools.isEmpty(keyedObject) || NewGen.lib.tools.isEmptyObject(keyedObject))
				return counter;
			
			for (var key in keyedObject)
			{
				if (keyedObject[key])
					counter++;
			}
			
			return counter;
		},
		
		rescheduleScriptFromSearch : function (context, i, search, limit, params, isLargeSearch, fail) {
			var queued = false;
			var proceed = true;
			var triggered = false;
			var yieldObj = null;
			var num = new Number(((i + 1) / search.length) * 100);
			context.setPercentComplete(num.toFixed(2));
			context.getPercentComplete();
			
			var remainingUsage = context.getRemainingUsage();
			
			if ((isLargeSearch && remainingUsage <= limit) || (!isLargeSearch && ((remainingUsage <= limit && (i + 1) < search.length) || (i + 1 == 990)))) {
				triggered = true;
				var ys = nlapiYieldScript();
				if (ys.status == "FAILURE") {
					yieldObj = ys;
					nlapiLogExecution("ERROR", "Unable to yield script. Attempting to reschedule script...", "[" + ys.reason + "] " + ys.information);
					if (fail)
						return { queued : false , proceed : false , triggered : triggered , yld : yieldObj };
					
					if (ys.reason != "SS_EXCESSIVE_MEMORY_FOOTPRINT") {
						var status = null;
						while (remainingUsage >= 25 && status != "QUEUED") {
							status = nlapiScheduleScript(context.getScriptId(), context.getDeploymentId(), params);
							if (status == 'QUEUED') {
								queued = true;
							}
						}
					}
					
					if (queued) {
						nlapiLogExecution("AUDIT", "Script queued for additional execution.", "Remaining script usage: " + remainingUsage + " -- Ending position: " + i + " -- Per cent complete: " + num.toFixed(2) + "%");
					} else {
						nlapiLogExecution("ERROR", "Script could not be queued for additional execution.", "Remaining script usage: " + remainingUsage + " -- Ending position: " + i + " -- Per cent complete: " + num.toFixed(2) + "%");
					}
					
					proceed = false;
				}
			}
			
			return { queued : queued , proceed : proceed , triggered : triggered , yld : yieldObj };
		},
		
		rescheduleScriptOnCount : function(context, i, length, count) {
			var queued = false;
			var proceed = true;
			var triggered = false;
			var yieldObj = null;
			
			var num = new Number(((i + 1) / length) * 100);
			context.setPercentComplete(num.toFixed(2));
			context.getPercentComplete();
			var remainingUsage = context.getRemainingUsage();
			
			if (Math.round(i + 1) % count == 0) {
				triggered = true;
				var ys = nlapiYieldScript();
				if (ys.status == "FAILURE") {
					yieldObj = ys;
					nlapiLogExecution("ERROR", "Unable to yield script. Attempting to reschedule script...", "[" + ys.reason + "] " + ys.information);
					if (fail) {
						return { queued : false , proceed : false , triggered : triggered , yld : yieldObj };
					}
					
					if (ys.reason != "SS_EXCESSIVE_MEMORY_FOOTPRINT") {
						var status = null;
						while (remainingUsage >= 25 && status != "QUEUED") {
							status = nlapiScheduleScript(context.getScriptId(), context.getDeploymentId(), params);
							if (status == 'QUEUED') {
								queued = true;
							}
						}
					}
					
					if (queued) {
						nlapiLogExecution("AUDIT", "Script queued for additional execution.", "Remaining script usage: " + remainingUsage + " -- Ending position: " + i + " -- Per cent complete: " + num.toFixed(2) + "%");
					} else {
						nlapiLogExecution("ERROR", "Script could not be queued for additional execution.", "Remaining script usage: " + remainingUsage + " -- Ending position: " + i + " -- Per cent complete: " + num.toFixed(2) + "%");
					}
					
					proceed = false;
				}
			}
			
			return { queued : queued , proceed : proceed , triggered : triggered , yld : yieldObj };
		},
		
		xmlToJSON : function (xml) {
			var obj = {};
			if (xml.nodeType == 1) {
				if (xml.attributes.length > 0) {
					obj["@attributes"] = {};
					for (var j = 0; j < xml.attributes.length; j++) {
						var attribute = xml.attributes.item(j);
						obj["@attributes"][attribute.nodeName] = attribute.nodeValue;
					}
				}
			} else if (xml.nodeType == 3) {
				obj = xml.nodeValue;
			}
			if (xml.hasChildNodes()) {
				for (var i = 0; i < xml.childNodes.length; i++) {
					var item = xml.childNodes.item(i);
					var nodeName = item.nodeName;
					if (typeof (obj[nodeName]) == "undefined") {
						obj[nodeName] = this.xmlToJSON(item);
					} else {
						if (typeof (obj[nodeName].push) == "undefined") {
							var old = obj[nodeName];
							obj[nodeName] = [];
							obj[nodeName].push(old);
						}
						obj[nodeName].push(this.xmlToJSON(item));
					}
				}
			}
			return obj;
		},
		
		jsonToXML : function(o,tab) {
			var toXml = function(v, name, ind) {
				var xml = "";
				if (v instanceof Array) {
					for (var i=0, n=v.length; i<n; i++) {
						xml += ind + toXml(v[i], name, ind+"\t") + "\n";
					}
				}
				else if (typeof(v) == "object") {
					var hasChild = false;
					xml += ind + "<" + name;
					for (var m in v) {
						if (m.charAt(0) == "@") {
							xml += " " + m.substr(1) + "=\"" + v[m].toString() + "\"";
						} else {
							hasChild = true;
						}
					}
					xml += hasChild ? ">" : "/>";
					if (hasChild) {
						for (var m in v) {
							if (m == "#text") {
								xml += v[m];
							} else if (m == "#cdata") {
								xml += "<![CDATA[" + v[m] + "]]>";
							} else if (m.charAt(0) != "@") {
								xml += toXml(v[m], m, ind+"\t");
							}
						}
						xml += (xml.charAt(xml.length-1)=="\n"?ind:"") + "</" + name + ">";
					}
				} else {
					xml += ind + "<" + name + ">" + v.toString() +  "</" + name + ">";
				}
				return xml;
			}, xml="";
			for (var m in o) {
				xml += toXml(o[m], m, "");
			}
			
			var final_xml = tab ? xml.replace(/\t/g, tab) : xml.replace(/\t|\n/g, "");
			//final_xml = '<?xml version="1.0" encoding="UTF-8" ?>' + final_xml;
			
			return final_xml;
		},
		
		getSearchResults : function(search, returnIDs, context) {
			try {
				var allResults = new Array();
				var n = 0;
				while (true) {
					var results = search.getResults((1000 * n), (1000 * (n + 1)));
					if (results == null) {
						break;
					}
					if (results.length > 0) {
						for (var s = 0; s < results.length; s++) {
							if (returnIDs) {
								allResults.push(results[s].getId());
							} else {
								allResults.push(results[s]);
							}
						}
						
						if (results.length < 1000) {
							break;
						} else {
							n++;
						}
					} else {
						break;
					}
					
					if (context != null) {
						if (context.getRemainingUsage() <= 500) {
							nlapiYieldScript();
						}
					}
				}
				
				if (allResults.length > 0) {
					return allResults;
				} else {
					return null;
				}
			} catch (err) {
				NewGen.lib.logging.logError(err, "Error encountered processing results from nlobjSearch data");
				return null;
			}
		},
		
		getResultsIdList : function(searchResults, getUnique) {
			getUnique = getUnique || false;
			var list = new Array();
			if (NewGen.lib.tools.isEmpty(searchResults)) {
				return list;
			}
			if (!NewGen.lib.obj.IsArray(searchResults)) {
				return list;
			}
			for (var i = 0; i < searchResults.length; i++) {
				var id = searchResults[i].getId();
				if (getUnique) {
					if (!NewGen.lib.tools.isInArray(list, id)) {
						list.push(id);
					}
				} else {
					list.push(id);
				}
			}
			
			return list;
		},
		
		handleSearch : function(recType, filt, cols, returnIDs) {
			var results = null;
			try {
				var search = nlapiCreateSearch(recType, filt, cols);
				results = this.getSearchResults(search.runSearch(), returnIDs);
			} catch (err) {
				NewGen.lib.logging.logError(err, "Error encountered performing '{0}' search".NG_Format(recType));
			}
			
			return results;
		},
		
		checkNSEmailAddress : function(emailAddress) {
			var sQtext = '[^\\x0d\\x22\\x5c\\x80-\\xff]';
			var sDtext = '[^\\x0d\\x5b-\\x5d\\x80-\\xff]';
			var sAtom = '[^\\x00-\\x20\\x22\\x28\\x29\\x2c\\x2e\\x3a-\\x3c\\x3e\\x40\\x5b-\\x5d\\x7f-\\xff]+';
			var sQuotedPair = '\\x5c[\\x00-\\x7f]';
			var sDomainLiteral = '\\x5b(' + sDtext + '|' + sQuotedPair + ')*\\x5d';
			var sQuotedString = '\\x22(' + sQtext + '|' + sQuotedPair + ')*\\x22';
			var sDomain_ref = sAtom;
			var sSubDomain = '(' + sDomain_ref + '|' + sDomainLiteral + ')';
			var sWord = '(' + sAtom + '|' + sQuotedString + ')';
			var sDomain = sSubDomain + '(\\x2e' + sSubDomain + ')*';
			var sLocalPart = sWord + '(\\x2e' + sWord + ')*';
			var sAddrSpec = sLocalPart + '\\x40' + sDomain; // complete RFC822 email address spec
			var sValidEmail = '^' + sAddrSpec + '$'; // as whole string
			
			var reValidEmail = new RegExp(sValidEmail);
			
			var firstCheck = reValidEmail.test(emailAddress);
			
			var lastAtPos = emailAddress.lastIndexOf('@');
			var lastDotPos = emailAddress.lastIndexOf('.');
			var secondCheck = (lastAtPos < lastDotPos && lastAtPos > 0 && emailAddress.indexOf('@@') == -1 && lastDotPos > 2 && (emailAddress.length - lastDotPos) > 2);
			
			var nsRegEx = new RegExp('[",: <>;]', "g");
			var thirdCheck = !nsRegEx.test(emailAddress);
			
			var fourthMatch = emailAddress.match(/@/g);
			var fourthCheck = fourthMatch != null && fourthMatch.length == 1;
			
			return (firstCheck && secondCheck && thirdCheck && fourthCheck);
		},
		
		clearFormChanged : function(doIt) {
			NS = NS || null;
			doIt = doIt || false;
			if (!NewGen.lib.tools.isEmpty(NS)) {
				if (NS.form.isChanged())  {
					NS.form.setChanged(doIt);
				}
			}
			return;
		},
		
		createLineCode : function() {
			if (arguments != null  && arguments.length > 0) {
				var codeString = "";
				for (var a = 0; a < arguments.length; a++) {
					codeString += "{" + a.toFixed(0) + "}";
				}
				return codeString.NG_Format.apply(codeString, arguments);
			} else {
				return null;
			}
		},
		
		generateUUID : function() {
			var d = new Date().getTime();
			var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
				var r = (d + Math.random()*16)%16 | 0;
				d = Math.floor(d/16);
				return (c=='x' ? r : (r&0x3|0x8)).toString(16);
			});
			return uuid;
		},
		
		loadCurrentRecord : function(isDynRec) {
			var newRec = nlapiGetNewRecord();
			if (isDynRec) {
				return nlapiLoadRecord(newRec.getRecordType(), newRec.getId(), NewGen.lib.dynRec);
			} else {
				return nlapiLoadRecord(newRec.getRecordType(), newRec.getId());
			}
		},
		
		/**
		 * @param {Array} a Array object to chunkify
		 * @param {Number} n Number object for how many chunks to create
		 * @param {Boolean} balanced Boolean object to determine if chunk lengths should be balanced; ignored if nLength parameter is TRUE
		 * @param {Boolean} nLength Boolean object to determine if n is the chunk length instead of chunk count
		 * @returns {Array} Array of array chunks created from passed in array
		 */
		chunkify : function(a, n, balanced, nLength) { // only use "balanced" set to true to produce as equal as possible length n-count of arrays; not using balanced produces chunk lengths based on len/(n-1) rounded down
			if (a.length == 0 || !a.length) {
				return [];
			}
			if ((!n || n < 1) && nLength) {
				return a;
			}
			if (n < 2 && !nLength) {
				return [a];
			}
			
			var len = a.length,
				out = [],
				i = 0,
				size;
			
			if (nLength) {
				for (var i = 0; i < len; i += n) {
					out.push(a.slice(i, i + n));
				}
			} else {
				if (len % n === 0) {
					size = Math.floor(len / n);
					while (i < len) {
						out.push(a.slice(i, i += size));
					}
				} else if (balanced) {
					while (i < len) {
						size = Math.ceil((len - i) / n--);
						out.push(a.slice(i, i += size));
					}
				} else {
					n--;
					size = Math.floor(len / n);
					if (len % size === 0) {
						size--;
					}
					while (i < size * n) {
						out.push(a.slice(i, i += size));
					}
					out.push(a.slice(size * n));
				}
			}
			
			return out;
		},
		
		/**
		 * @param {Array} a Array object to chunkify
		 * @param {Number} n Number object for max length of chunks to create
		 * @returns {Array} Array of array chunks created from passed in array
		 */
		chunked : function(a, n) { // produce one array full of arrays of max length n
			var R = [];
			if (a.length == 0 || !a.length) {
				return R;
			}
			if (!n || n < 1) {
				return a;
			}
			for (var i = 0; i < a.length; i += n) {
				R.push(a.slice(i, i + n));
			}
			return R;
		},
		
		B64 : {
			alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
			lookup: null,
			ie: navigator ? /MSIE /.test(navigator.userAgent) : false,
			ieo: navigator ? /MSIE [67]/.test(navigator.userAgent) : false,
			encode: function (s) {
				var buffer = this.toUtf8(s),
					position = -1,
					len = buffer.length,
					nan1, nan2, enc = [, , , ];
				if (this.ie) {
					var result = [];
					while (++position < len) {
						nan1 = buffer[position + 1], nan2 = buffer[position + 2];
						enc[0] = buffer[position] >> 2;
						enc[1] = ((buffer[position] & 3) << 4) | (buffer[++position] >> 4);
						if (isNaN(nan1)) enc[2] = enc[3] = 64;
						else {
							enc[2] = ((buffer[position] & 15) << 2) | (buffer[++position] >> 6);
							enc[3] = (isNaN(nan2)) ? 64 : buffer[position] & 63;
						}
						result.push(this.alphabet[enc[0]], this.alphabet[enc[1]], this.alphabet[enc[2]], this.alphabet[enc[3]]);
					}
					return result.join('');
				} else {
					result = '';
					while (++position < len) {
						nan1 = buffer[position + 1], nan2 = buffer[position + 2];
						enc[0] = buffer[position] >> 2;
						enc[1] = ((buffer[position] & 3) << 4) | (buffer[++position] >> 4);
						if (isNaN(nan1)) {
							enc[2] = enc[3] = 64;
						} else {
							enc[2] = ((buffer[position] & 15) << 2) | (buffer[++position] >> 6);
							enc[3] = (isNaN(nan2)) ? 64 : buffer[position] & 63;
						}
						result += this.alphabet[enc[0]] + this.alphabet[enc[1]] + this.alphabet[enc[2]] + this.alphabet[enc[3]];
					}
					return result;
				}
			},
			decode: function (s) {
				var buffer = this.fromUtf8(s),
					position = 0,
					len = buffer.length;
				if (this.ieo) {
					var result = [];
					while (position < len) {
						if (buffer[position] < 128) result.push(String.fromCharCode(buffer[position++]));
						else if (buffer[position] > 191 && buffer[position] < 224) result.push(String.fromCharCode(((buffer[position++] & 31) << 6) | (buffer[position++] & 63)));
						else result.push(String.fromCharCode(((buffer[position++] & 15) << 12) | ((buffer[position++] & 63) << 6) | (buffer[position++] & 63)));
					}
					return result.join('');
				} else {
					var result = '';
					while (position < len) {
						if (buffer[position] < 128) result += String.fromCharCode(buffer[position++]);
						else if (buffer[position] > 191 && buffer[position] < 224) result += String.fromCharCode(((buffer[position++] & 31) << 6) | (buffer[position++] & 63));
						else result += String.fromCharCode(((buffer[position++] & 15) << 12) | ((buffer[position++] & 63) << 6) | (buffer[position++] & 63));
					}
					return result;
				}
			},
			toUtf8: function (s) {
				var position = -1,
					len = s.length,
					chr, buffer = [];
				if (/^[\x00-\x7f]*$/.test(s)) while (++position < len) {
					buffer.push(s.charCodeAt(position));
				} else while (++position < len) {
					chr = s.charCodeAt(position);
					if (chr < 128) buffer.push(chr);
					else if (chr < 2048) buffer.push((chr >> 6) | 192, (chr & 63) | 128);
					else buffer.push((chr >> 12) | 224, ((chr >> 6) & 63) | 128, (chr & 63) | 128);
				}
				return buffer;
			},
			fromUtf8: function (s) {
				var position = -1,
					len, buffer = [],
					enc = [, , , ];
				if (!this.lookup) {
					len = this.alphabet.length;
					this.lookup = {};
					while (++position < len)
					this.lookup[this.alphabet[position]] = position;
					position = -1;
				}
				len = s.length;
				while (position < len) {
					enc[0] = this.lookup[s.charAt(++position)];
					enc[1] = this.lookup[s.charAt(++position)];
					buffer.push((enc[0] << 2) | (enc[1] >> 4));
					enc[2] = this.lookup[s.charAt(++position)];
					if (enc[2] == 64) break;
					buffer.push(((enc[1] & 15) << 4) | (enc[2] >> 2));
					enc[3] = this.lookup[s.charAt(++position)];
					if (enc[3] == 64) break;
					buffer.push(((enc[2] & 3) << 6) | enc[3]);
				}
				return buffer;
			}
		},
		
		arrIntersect : function(source, target) {
			var sorted_a = source.concat().sort();
			var sorted_b = target.concat().sort();
			var common = [];
			var a_i = 0;
			var b_i = 0;
			
			while (a_i < source.length && b_i < target.length) {
				if (sorted_a[a_i] === sorted_b[b_i]) {
					common.push(sorted_a[a_i]);
					a_i++;
					b_i++;
				} else if(sorted_a[a_i] < sorted_b[b_i]) {
					a_i++;
				} else {
					b_i++;
				}
			}
			return common;
		},
		
		arrDifferential : function(source, comp) {
			return source.filter(function(el) {
				return comp.indexOf(el) < 0;
			});
		},
		
		getMultiSelect : function(field, record, getText, lookupValue) {
			var selections = new Array();
			var values = null;
			if (!this.isEmpty(record) && !this.isEmpty(field)) {
				if (getText) {
					values = record.getFieldText(field);
				} else {
					values = record.getFieldValue(field);
				}
			} else if (!this.isEmpty(lookupValue)) {
				values = lookupValue;
			} else if (!this.isEmpty(field)) {
				if (getText) {
					values = nlapiGetFieldText(field);
				} else {
					values = nlapiGetFieldValue(field);
				}
			}
			if (!this.isEmpty(values) && !NewGen.lib.obj.IsArray(values)) {
				selections = values.split(/, |,|\u0005 ,|\u0005/);
			} else if (!this.isEmpty(values) && NewGen.lib.obj.IsArray(values)) {
				selections = values;
			}
			
			return selections;
		},
		
		getStateProvince : function(cCode, sCode, sName) {
			if (NewGen.lib.tools.isInArray(cCode, NewGen.lib.csList)) {
				if (!NewGen.lib.tools.isEmpty(sCode)) {
					for (var s = 0; s < NewGen.lib.states[cCode].length; s++) {
						if (NewGen.lib.states[cCode][s]['value'] == sCode) {
							return NewGen.lib.states[cCode][s]['text'];
						}
					}
					return "";
				} else if (!NewGen.lib.tools.isEmpty(sName)) {
					for (var s = 0; s < NewGen.lib.states[cCode].length; s++) {
						if (NewGen.lib.states[cCode][s]['text'] == sCode) {
							return NewGen.lib.states[cCode][s]['value'];
						}
					}
					return "";
				} else {
					return "";
				}
			} else {
				return "";
			}
		},
		
		convertJavaArray : function (value) {
			var __tools = NewGen.lib.tools;
			var __obj = NewGen.lib.obj;
			if (__tools.isEmpty(value)) {
				return value;
			}
			if (!__obj.IsArray(value)) {
				return value;
			}
			
			if (Object.prototype.toString.call(value) === "[object JavaArray]") {
				return [].concat(value);
			} else {
				return value;
			}
		},
		
		/**
		 * @param {Array} arguments Array of arguments passed in
		 * @returns {Array} Sorted object array based upon arguments passed in
		 * 
		 * @summary The first argument must be the array to be sorted. If wanting to execute a case sensitive sort, the final argument must be a Boolean 'true'. After
		 * the array to be sorted, but before the case sensitive Boolean, all sort parameters are entered: string for field name, or array of field name string, Boolean
		 * for if descending order, and primer function (primer is optional)
		 */
		objectSort : function() {
			var args = arguments,
				array = args[0],
				case_sensitive, keys_length, key, desc, primer, a, b, i;
			
			if (typeof arguments[arguments.length - 1] === 'boolean') {
				case_sensitive = arguments[arguments.length - 1];
				keys_length = arguments.length - 1;
			} else {
				case_sensitive = false;
				keys_length = arguments.length;
			}
			
			return array.sort(function (obj1, obj2) {
				for (i = 1; i < keys_length; i++) {
					key = args[i];
					if (typeof key !== 'string') {
						desc = key[1];
						primer = key[2];
						key = key[0];
						if (primer == undefined) {
							a = obj1[args[i][0]];
							b = obj2[args[i][0]];
						} else {
							a = primer(obj1[args[i][0]]);
							b = primer(obj2[args[i][0]]);
						}
					} else {
						desc = false;
						a = obj1[args[i]];
						b = obj2[args[i]];
					}
					
					// Handle numerical comparison properly
					var isANumber = !isNaN(parseFloat(a)) && isFinite(a);
					var isBNumber = !isNaN(parseFloat(b)) && isFinite(b);
					
					if (isANumber && isBNumber) {
						// Both are numbers, convert to numbers for comparison
						a = parseFloat(a);
						b = parseFloat(b);
					} else if (case_sensitive === false && typeof a === 'string' && a !== null) {
						a = String(a).toLowerCase();
						b = String(b).toLowerCase();
					} else if (case_sensitive === false) {
						// Handle null, undefined or non-string values
						a = a ? String(a).toLowerCase() : '';
						b = b ? String(b).toLowerCase() : '';
					}

					// Handle case-insensitive comparison
					if (!desc) {
						if (a < b) return -1;
						if (a > b) return 1;
					} else {
						if (a > b) return -1;
						if (a < b) return 1;
					}
				}
				return 0;
			});
		},
		
		findKey : function(obj, func) {
			if (NewGen.lib.tools.isEmpty(obj)) { return null; }
			if (NewGen.lib.tools.isEmpty(func)) { return null; }
			for (var key in obj) {
				if (func(obj[key])) {
					return key;
				}
			}
			
			return null;
		},
		
		findAllKeys : function(obj, func) {
			var results = new Array();
			if (NewGen.lib.tools.isEmpty(obj)) { return results; }
			if (NewGen.lib.tools.isEmpty(func)) { return results; }
			for (var key in obj) {
				if (func(obj[key])) {
					results.push(key);
				}
			}
			
			return results;
		},
		
		/**
		 * @param {nlobjForm} form The NS form object being modified
		 * @param {Array} fieldList Array of key/value pairs indicating the custom record fields to pull ('id') and the labels to use ('label')
		 * @param {String} targetSubListId The ID to apply to the sublist being added for printing
		 * @param {String} custRecordId The internal ID of the child custom record to be sourced
		 * @param {String} filterFieldId The ID of the custom record field that references the parent record being printed
		 * @returns {Void}
		 * 
		 * @summary Creates a custom sublist on a record of a selected child custom record to be used for printing purposes on Advanced HTML/PDF templates. Requires list printing on the template
		 * that matches the target sublist ID passed in as a parameter. Intended only to work in user event scripts during the BeforeLoad trigger during printing. 
		 */
		addPrintableSublist : function(form, fieldList, targetSubListId, custRecordId, filterFieldId, addIfEmpty) {
			var __tools = NewGen.lib.tools;
			var __obj = NewGen.lib.obj;
			var __log = NewGen.lib.logging;
			
			if (!__obj.IsArray(fieldList)) {
				__log.logInfo("field list is not array");
				return;
			}
			if (fieldList.length < 1) {
				__log.logInfo("field list is empty");
				return;
			}
			if (__tools.isEmpty(form) || __tools.isEmpty(targetSubListId) || __tools.isEmpty(custRecordId) || __tools.isEmpty(filterFieldId)) {
				__log.logInfo("missing required arg");
				return;
			}
			
			var filt = new Array(
					[filterFieldId,"anyof",[nlapiGetRecordId()]]
				,	"and"
				,	["isinactive","is","F"]
			);
			addIfEmpty = addIfEmpty || false;
			
			var cols = new Array();
			for (var f = 0; f < fieldList.length; f++) {
				cols.push(new nlobjSearchColumn(fieldList[f]['id'], null, null));
			}
			cols.push(new nlobjSearchColumn("internalid", null, null));
			cols[cols.length - 1].setSort();
			var search = null;
			var hasErr = false;
			try {
				search = nlapiSearchRecord(custRecordId, null, filt, cols);
			} catch (err) {
				__log.logError(err, "Error encountered searching for child records to print");
				hasErr = true;
			}
			
			var sl = null;
			if (addIfEmpty) {
				var tabId = "{0}_tab".NG_Format(targetSubListId);
				form.addTab(tabId, "");
				sl = form.addSubList(targetSubListId, "list", "", tabId);
				for (var f = 0; f < fieldList.length; f++) {
					sl.addField(fieldList[f]['id'], "text", fieldList[f]['label']);
				}
			}
			
			if (search != null) {
				if (!addIfEmpty) {
					var tabId = "{0}_tab".NG_Format(targetSubListId);
					form.addTab(tabId, "");
					sl = form.addSubList(targetSubListId, "list", "", tabId);
					for (var f = 0; f < fieldList.length; f++) {
						sl.addField(fieldList[f]['id'], "text", fieldList[f]['label']);
					}
				}
				
				var slData = new Array();
				for (var i = 0; i < search.length; i++) {
					var data = { };
					for (var f = 0; f < fieldList.length; f++) {
						data[fieldList[f]['id']] = !__tools.isEmpty(search[i].getText(fieldList[f]['id'])) ? search[i].getText(fieldList[f]['id']) : search[i].getValue(fieldList[f]['id'])
					}
					slData.push(data);
				}
				sl.setLineItemValues(slData);
			} else {
				__log.logInfo("no child records found for printing");
				
				if (addIfEmpty) {
					var data = { };
					for (var f = 0; f < fieldList.length; f++) {
						data[fieldList[f]['id']] = " ";
					}
					sl.setLineItemValues([ data ]);
				}
			}
		},
		
		saveTextToFile : function (contents, fileName, fileType, folderId) {
			try {
				var file = nlapiCreateFile(fileName, fileType, contents);
				file.setFolder(folderId);
				nlapiSubmitFile(file);
			} catch (err) {
				NewGen.lib.logging.logError(err, "Error encountered saving text contents to file", "File Name: {0} -- File Type: {1} -- Folder ID: {2}".NG_Format(fileName, fileType, folderId));
			}
		},
		
		addMessageModal : function (form, text) {
			text = nlapiEscapeXML(text || "Please wait...");
			var htmlField = form.addField("custpage_assign_inv_cssmodal", "inlinehtml", "");
			// modal message box style definition (CSS)
			var html = "<style>\n";
			html += ".overlay {\n position: fixed;\n top: 0;\n bottom: 0;\n left: 0;\n right: 0;\n background: rgba(0, 0, 0, 0.7);\n transition: opacity 500ms, visibility 500ms;\n visibility: hidden;\n opacity: 0;\n z-index: 100000;\n}\n";
			html += ".overlay_a {\n  visibility: visible;\n opacity: 1;\n}\n";
			html += ".overlay_b {\n  visibility: hidden;\n opacity: 0;\n}\n";
			html += ".overlay:target {\n  visibility: visible;\n opacity: 1;\n}\n";
			html += ".popup {\n margin: 250px auto;\n padding: 20px;\n background: #fff;\n border-radius: 5px;\n width: 30%;\n position: relative;\n transition: all 5s ease-in-out;\n}\n";
			html += ".popup h2 {\n margin-top: 0;\n color: #333;\n font-family: Tahoma, Arial, sans-serif;\n font-size: 16px;\n}\n";
			html += ".popup .close {\n position: absolute;\n top: 20px;\n right: 30px;\n transition: all 200ms;\n font-size: 30px;\n font-weight: bold;\n text-decoration: none;\n color: #333;\n}\n";
			html += ".popup .close:hover {\n color: #06D85F;\n}\n";
			html += ".popup .content {\n max-height: 30%;\n overflow: auto;\n}\n";
			html += "@media screen and (max-width: 700px){\n";
			html += "	.box {\n width: 70%;\n}\n";
			html += "	.popup {\n width: 70%;\n}\n";
			html += "</style>\n";
			// modal message box html
			html += "<div id=\"popup_invall\" class=\"overlay\">\n";
			html += "	<div class=\"popup\">\n";
			html += "		<a id=\"modalclose\" class=\"close\" href=\"#\">&nbsp;</a>\n";
			html += "		<div class=\"content\">\n";
			html += "			<h2 style=\"width:100%; text-align:center;\"><br />{0}<br /><br /></h2>\n".NG_Format(text);
			html += "		</div>\n";
			html += "	</div>\n";
			html += "</div>\n";
			
			htmlField.setDefaultValue(html);
		},

		createModalAlert : function(message) {
			var html = "<style>\n";
			html += ".overlayAlert {\n position: fixed;\n top: 0;\n bottom: 0;\n left: 0;\n right: 0;\n background: rgba(0, 0, 0, 0.7);\n transition: opacity 500ms, visibility 500ms;\n visibility: hidden;\n opacity: 0;\n z-index: 1000;\n}\n";
			html += ".overlayAlert_a {\n  visibility: visible;\n opacity: 1;\n}\n";
			html += ".overlayAlert_b {\n  visibility: hidden;\n opacity: 0;\n}\n";
			html += ".overlayAlert:target {\n  visibility: visible;\n opacity: 1;\n}\n";
			html += ".popupAlert {\n margin: 250px auto;\n padding: 20px;\n background: #fff;\n border-radius: 5px;\n width: 30%;\n position: relative;\n transition: all 5s ease-in-out;\n overflow: auto;\n max-height: 50%}\n";
			html += ".popupAlert h2 {\n margin-top: 0;\n color: #333;\n font-family: Tahoma, Arial, sans-serif;\n font-size: 16px;\n}\n";
			html += ".popupAlert .close {\n position: absolute;\n top: 10px;\n right: 10px;\n transition: all 200ms;\n font-size: 10px;\n font-weight: bold;\n text-decoration: none;\n color: #333;\n}\n";
			html += ".popupAlert .close:hover {\n color: #06D85F;\n}\n";
			html += ".popupAlert .content {\n max-height: 30%;\n overflow: auto;\n}\n";
			html += "@media screen and (max-width: 700px){\n";
			html += "	.box{\n width: 70%;\n}\n";
			html += "	.popupAlert{\n width: 70%;\n}\n";
			html += "}\n";
			html += "</style>\n";
			
			// modal message box html
			html += "<div id=\"popup_alert_modal\" class=\"overlayAlert\">\n";
			html += "	<div class=\"popupAlert\">\n";
			html += "		<a id=\"modalclose_alert_modal\" style=\"display:block;\" class=\"close\" onclick=\"closeModalAlert('popup_alert_modal')\" href=\"#\">close</a>\n";
			html += "		<div id=\"modalcontent\" class=\"content\">\n";
			html += "			<br />" + message + "<br />\n";
			html += "		</div>\n";
			html += "	</div>\n";
			html += "</div>\n";
			var html2 = "";	   
			html2 += "function closeModalAlert(modalID){";
			html2 += "var popup = document.getElementById(modalID);";
			html2 += "if (popup != null){";
			html2 += "popup.className = \"overlay overlayAlert_b\";";
			html2 += "setTimeout(function(){";
			html2 += "popup.parentNode.removeChild(popup);"
			html2 += "},500);";
			html2 += "}"
			html2 += "}";
			
			var customModal = document.createElement('div');
			customModal.setAttribute('id', 'customModal');
			customModal.innerHTML = html;
			
			document.body.appendChild(customModal);
			
			var closeScript = document.createElement('script');
			closeScript.type = "text/javascript";
			closeScript.innerHTML = html2;
			
			document.body.appendChild(closeScript);
			
			setTimeout(function(){
				var popup = document.getElementById("popup_alert_modal");
				
				if (popup != null){
					popup.className = "overlayAlert overlayAlert_a";
				}
			}, 100);
		},
		
		createModalConfirm : function(message, yesCallback, noCallback) {		
			var html = "<style>\n";
			html += ".overlayConfirm {\n position: fixed;\n top: 0;\n bottom: 0;\n left: 0;\n right: 0;\n background: rgba(0, 0, 0, 0.7);\n transition: opacity 500ms, visibility 500ms;\n visibility: hidden;\n opacity: 0;\n z-index: 1000;\n}\n";
			html += ".overlayConfirm_a {\n  visibility: visible;\n opacity: 1;\n}\n";
			html += ".overlayConfirm_b {\n  visibility: hidden;\n opacity: 0;\n}\n";
			html += ".overlayConfirm:target {\n  visibility: visible;\n opacity: 1;\n}\n";
			html += ".popupConfirm {\n margin: 250px auto;\n padding: 20px;\n background: #fff;\n border-radius: 5px;\n width: 30%;\n position: relative;\n transition: all 5s ease-in-out;\n}\n";
			html += ".popupConfirm h2 {\n margin-top: 0;\n color: #333;\n font-family: Tahoma, Arial, sans-serif;\n font-size: 16px;\n}\n";
			html += ".popupConfirm .close {\n position: absolute;\n top: 10px;\n right: 10px;\n transition: all 200ms;\n font-size: 10px;\n font-weight: bold;\n text-decoration: none;\n color: #333;\n}\n";
			html += ".popupConfirm .close:hover {\n color: #06D85F;\n}\n";
			html += ".popupConfirm .content {\n max-height: 50%;\n overflow: auto;\n}\n";
			html += ".buttonConfirm {\n";
			html += "	background-color: #4CAF50;\n"
			html += "	border: none;\n"
			html += "	color: white;\n"
			html += "	padding: 15px 32px;\n"
			html += "	text-align: center;\n"
			html += "	text-decoration: none;\n"
			html += "	display: inline-block;\n"
			html += "	font-size: 16px;\n"
			html += "	margin: 4px 2px;\n"
			html += "	cursor: pointer;\n"
			html += "	border-radius: 12px;\n";
			html += "}\n"
			html += ".buttonConfirm2 {\n";
			html += "	background-color: #990000;\n"
			html += "	border: none;\n"
			html += "	color: white;\n"
			html += "	padding: 15px 32px;\n"
			html += "	text-align: center;\n"
			html += "	text-decoration: none;\n"
			html += "	display: inline-block;\n"
			html += "	font-size: 16px;\n"
			html += "	margin: 4px 2px;\n"
			html += "	cursor: pointer;\n"
			html += "	border-radius: 12px;\n";
			html += "}\n"
			
			html += "@media screen and (max-width: 700px){\n";
			html += "	.box{\n width: 70%;\n}\n";
			html += "	.popupConfirm{\n width: 70%;\n}\n";
			html += "}\n";
			html += "</style>\n";
			
			// modal message box html
			html += "<div id=\"popup_confirm_modal\" class=\"overlayConfirm\">\n";
			html += "	<div class=\"popupConfirm\">\n";
			html += "		<a id=\"modalclose_confirm_modal\" style=\"display:block;\" class=\"close\" onclick=\"closeModalConfirm('popup_confirm_modal')\" href=\"#\">close</a>\n";
			html += "		<div id=\"modalcontent\" class=\"content\">\n";
			html += "			<h2 style=\"width:100%; text-align:center;\"><br />" + message + "<br /><br /></h2>\n";
			html += "			<div style=\"text-align:center;\">";
			html +=	"				<a href=\"#\" class=\"buttonConfirm\" onclick=\"closeModalConfirm('popup_confirm_modal','Yes')\">Yes</a>\n";
			html +=	"				<a href=\"#\" class=\"buttonConfirm2\" onclick=\"closeModalConfirm('popup_confirm_modal','No')\">No</a>\n";
			html += "			</div>";
			html += "		</div>\n";
			html += "	</div>\n";
			html += "</div>\n";
			var html2 = "";	   
			html2 += "function closeModalConfirm(modalID,Answer){\n";
			html2 += "var popup = document.getElementById(modalID);\n";
			html2 += "if (popup != null){\n";
			html2 += "popup.className = \"overlay overlayConfirm_b\";\n";
			html2 += "setTimeout(function(){\n";
			html2 += "if(Answer == 'Yes'){\n";
			if (!NewGen.lib.tools.isEmpty(yesCallback)) {
				html2 += yesCallback + ";\n";
			}
			html2 += "}else if(Answer == 'No'){\n";
			if (!NewGen.lib.tools.isEmpty(yesCallback)) {
				html2 += noCallback + ";\n";
			}
			html2 += "}\n";
			html2 += "popup.parentNode.removeChild(popup);\n"
			html2 += "},500);\n";
			html2 += "}\n"
			html2 += "}\n";
			
			var customModal = document.createElement('div');
			customModal.setAttribute('id', 'customModal');
			customModal.innerHTML = html;
			
			document.body.appendChild(customModal);
			
			var closeScript = document.createElement('script');
			closeScript.type = "text/javascript";
			closeScript.innerHTML = html2;
			
			document.body.appendChild(closeScript);
			
			setTimeout(function() {
				var popup = document.getElementById("popup_confirm_modal");
				if (popup != null) {
					popup.className = "overlayConfirm overlayConfirm_a";
				}
			},100);
		},
		
		addNSStyleButton : function(buttonID, buttonText, buttonFunction) {
			var libContext = nlapiGetContext();
			var scriptID = "";
			var deployID = "";
			try {
				scriptID = libContext.getScriptId();
				deployID = libContext.getDeploymentId().toUpperCase();
			} catch (err) { }
			var html = '';
			html += '<table id="tbl_{0}" cellpadding="0" cellspacing="0" border="0" class="uir-button" style="margin-right:6px; margin-top:10px; cursor:hand;" role="presentation">\n'.NG_Format(buttonID);
			html += '	<tbody>\n';
			html += '		<tr id="tr_{0}" class="pgBntG">\n'.NG_Format(buttonID);
			html += '			<td id="tdleftcap_{0}">\n'.NG_Format(buttonID);
			html += '				<img src="/images/nav/ns_x.gif" class="bntLT" border="0" height="50%" width="3" alt="">\n';
			html += '				<img src="/images/nav/ns_x.gif" class="bntLB" border="0" height="50%" width="3" alt="">\n';
			html += '			</td>\n';
			html += '			<td id="tdbody_{0}" height="20" valign="top" nowrap="" class="bntBgB">\n'.NG_Format(buttonID);
			html += '				<input type="button" style="" class="rndbuttoninpt bntBgT" value="{0}" id="{1}" name="{1}" onclick="try { if (!!window) { var origScriptIdForLogging = window.NLScriptIdForLogging; var origDeploymentIdForLogging = window.NLDeploymentIdForLogging; window.NLScriptIdForLogging = \'{2}\'; window.NLDeploymentIdForLogging = \'{3}\'; } {4} } finally { if (!!window) { window.NLScriptIdForLogging = origScriptIdForLogging; window.NLDeploymentIdForLogging = origDeploymentIdForLogging; } }; return false;" onmousedown="this.setAttribute(\'_mousedown\',\'T\'); setButtonDown(true, false, this);" onmouseup="this.setAttribute(\'_mousedown\',\'F\'); setButtonDown(false, false, this);" onmouseout="if(this.getAttribute(\'_mousedown\')==\'T\') setButtonDown(false, false, this);" onmouseover="if(this.getAttribute(\'_mousedown\')==\'T\') setButtonDown(true, false, this);" _mousedown="F">\n'.NG_Format(buttonText, buttonID, (scriptID || 'window.NLScriptIdForLogging'), (deployID || 'window.NLDeploymentIdForLogging'), buttonFunction);
			html += '			</td>\n';
			html += '			<td id="tdrightcap_{0}">\n'.NG_Format(buttonID);
			html += '				<img src="/images/nav/ns_x.gif" height="50%" class="bntRT" border="0" width="3" alt="">\n';
			html += '				<img src="/images/nav/ns_x.gif" height="50%" class="bntRB" border="0" width="3" alt="">\n';
			html += '			</td>\n';
			html += '		</tr>\n';
			html += '	</tbody>\n';
			html += '</table>';
			return html;
		}
	},
	
	url : {
		/**
		 * @param None
		 * @returns {Object} data Object encapsulating all parameters in page URL
		 * NOTE: Only use in client SuiteScripts, will not work for server-side scripts
		 */
		QueryString : function () {
			// This function is anonymous, is executed immediately and 
			// the return value is assigned to QueryString!
			var query_string = {};
			var query = window.location.search.substring(1);
			var vars = query.split("&");
			for (var i=0;i<vars.length;i++) {
				var pair = vars[i].split("=");
				// If first entry with this name
				if (typeof query_string[pair[0]] === "undefined") {
					query_string[pair[0]] = pair[1];
					// If second entry with this name
				} else if (typeof query_string[pair[0]] === "string") {
					var arr = [ query_string[pair[0]], pair[1] ];
					query_string[pair[0]] = arr;
					// If third or later entry with this name
				} else {
					query_string[pair[0]].push(pair[1]);
				}
			}
			
			return query_string;
		},

		escapeURL : function (url) {
			url = url.replace(/%/ig, "%25");
			url = url.replace(/ /ig, "%20");
			url = url.replace(/\$/ig, "%24");
			url = url.replace(/\&/ig, "%26");
			url = url.replace(/`/ig, "%60");
			url = url.replace(/:/ig, "%3A");
			url = url.replace(/</ig, "%3C");
			url = url.replace(/>/ig, "%3E");
			url = url.replace(/\[/ig, "%5B");
			url = url.replace(/\]/ig, "%5D");
			url = url.replace(/\{/ig, "%7B");
			url = url.replace(/\}/ig, "%7D");
			url = url.replace(/\"/ig, "%22");
			url = url.replace(/\'/ig, "%20");
			url = url.replace(/;/ig, "%3B");
			url = url.replace(/\+/ig, "%2B");
			url = url.replace(/#/ig, "%23");
			url = url.replace(/\?/ig, "%3F");
			url = url.replace(/\//ig, "%2F");
			url = url.replace(/\\/ig, "%5C");
			url = url.replace(/\=/ig, "%3D");
			url = url.replace(/\|/ig, "%7C");
			url = url.replace(/~/ig, "%7E");
			url = url.replace(/\^/ig, "%5E");
			url = url.replace(/@/ig, "%40");
			url = url.replace(/\,/ig, "%2C");
			url = url.replace(/\!/ig, "%21");
			url = url.replace(/\*/ig, "%2A");
			
			return url;
		}
	},
	
	time : {
		/**
		 * @param {Boolean} inDateTimeFormat If a date/time formated string is wanted, pass true (default: false)
		 * @param {String} zone Continental US time zone for desired time stamp; Options: EST,EDT,CST,CDT,MST,MDT,PST,PDT (default: current Netsuite server time zone)
		 * @param {Boolean} inDateTimeSecFormat If a date/time formated string with seconds is wanted, pass true (default: false)
		 * 
		 * @returns {String}
		 * NOTE: Does not factor in Daylight Savings Time
		 */
		timeStamp : function (inDateTimeFormat, zone, inDateTimeSecFormat) {
			try {
				var now = new Date();
				
				// attempt to convert time to designated time zone (default: current Netsuite server time zone)
				var nowString = now.toString();
				var est = nowString.search(/EDT|EST|Eastern Standard Time|Eastern Daylight Time/i);
				var cst = nowString.search(/CDT|CST|Central Standard Time|Central Daylight Time/i);
				var mst = nowString.search(/MDT|MDT|Mountain Standard Time|Mountain Daylight Time/i);
				var pst = nowString.search(/PDT|PST|Pacific Standard Time|Pacific Daylight Time/i);
				var diff = new Number(0);
				
				if (!NewGen.lib.tools.isEmpty(zone)) {
					switch (zone) {
						case "EST":
						case "EDT":
							if (est != -1)
								diff = new Number(0);
							else if (cst != -1)
								diff = new Number(1);
							else if (mst != -1)
								diff = new Number(2);
							else if (pst != -1)
								diff = new Number(3);
							break;
						case "CST":
						case "CDT":
							if (est != -1)
								diff = new Number(-1);
							else if (cst != -1)
								diff = new Number(0);
							else if (mst != -1)
								diff = new Number(1);
							else if (pst != -1)
								diff = new Number(2);
							break;
						case "MST":
						case "MDT":
							if (est != -1)
								diff = new Number(-2);
							else if (cst != -1)
								diff = new Number(-1);
							else if (mst != -1)
								diff = new Number(0);
							else if (pst != -1)
								diff = new Number(1);
							break;
						case "PST":
						case "PDT":
							if (est != -1)
								diff = new Number(-3);
							else if (cst != -1)
								diff = new Number(-2);
							else if (mst != -1)
								diff = new Number(-1);
							else if (pst != -1)
								diff = new Number(0);
							break;
					}
				}
				
				var msIncrement = new Number(diff * 60 * 60 * 1000);
				var msNow = Date.parse(now);
				now = new Date(msNow + msIncrement);
				
				var disp = "";
				if (inDateTimeFormat) {
					disp = nlapiDateToString(now, "datetime");
				} else if (inDateTimeSecFormat) {
					disp = nlapiDateToString(now, "datetimetz");
				} else {
					var m = (now.getMonth() + 1).toString();
					var d = now.getDate().toString();
					var y = now.getFullYear().toString();
					var h = now.getHours();
					var mm = now.getMinutes().toString();
					var s = now.getSeconds().toString();
					
					if (m.length == 1)
						m = "0" + m;
					if (d.length == 1)
						d = "0" + d;
					if (h < 10)
						h = "0" + h;
					if (mm.length == 1)
						mm = "0" + mm;
					if (s.length == 1)
						s = "0" + s;
					disp = y + m + d + h + mm + s;
				}
				return disp;
			} catch (err) {
				return "";
			}
		},

		adjustTime : function(now, zone) {
			// attempt to convert time to designated time zone (default: current Netsuite server time zone)
			var nowString = now.toString();
			var est = nowString.search(/EDT|EST|Eastern Standard Time|Eastern Daylight Time/i);
			var cst = nowString.search(/CDT|CST|Central Standard Time|Central Daylight Time/i);
			var mst = nowString.search(/MDT|MDT|Mountain Standard Time|Mountain Daylight Time/i);
			var pst = nowString.search(/PDT|PST|Pacific Standard Time|Pacific Daylight Time/i);
			var diff = new Number(0);
			
			if (zone != null && zone != "") {
				switch (zone) {
					case "EST":
					case "EDT":
						if (est != -1)
							diff = new Number(0);
						else if (cst != -1)
							diff = new Number(1);
						else if (mst != -1)
							diff = new Number(2);
						else if (pst != -1)
							diff = new Number(3);
						break;
					case "CST":
					case "CDT":
						if (est != -1)
							diff = new Number(-1);
						else if (cst != -1)
							diff = new Number(0);
						else if (mst != -1)
							diff = new Number(1);
						else if (pst != -1)
							diff = new Number(2);
						break;
					case "MST":
					case "MDT":
						if (est != -1)
							diff = new Number(-2);
						else if (cst != -1)
							diff = new Number(-1);
						else if (mst != -1)
							diff = new Number(0);
						else if (pst != -1)
							diff = new Number(1);
						break;
					case "PST":
					case "PDT":
						if (est != -1)
							diff = new Number(-3);
						else if (cst != -1)
							diff = new Number(-2);
						else if (mst != -1)
							diff = new Number(-1);
						else if (pst != -1)
							diff = new Number(0);
						break;
				}
			}
			
			var msIncrement = new Number(diff * 60 * 60 * 1000);
			var msNow = Date.parse(now);
			now = new Date(msNow + msIncrement);
			
			return now;
		},

		addMinutes : function (date, minutes) {
			return new Date(date.getTime() + (minutes * 60 * 1000));
		},

		logTimeElapsed : function (time1, time2, msg) {
			msg = msg != null && msg != "" ? msg : "Time Elapsed";
			var time1MS = time1 != null ? Date.parse(time1) : 0;
			var time2MS = time2 != null ? Date.parse(time2) : 0;
			var elapsed = (new Number(time2MS) - new Number(time1MS)) / 1000;
			nlapiLogExecution("AUDIT", msg, elapsed + " seconds");
		},

		getTimeElapsed : function (time1, time2) {
			var time1MS = time1 != null ? Date.parse(time1) : 0;
			var time2MS = time2 != null ? Date.parse(time2) : 0;
			return (new Number(time2MS) - new Number(time1MS)) / 1000;
		},

		/**
		 * @param {Date} dt Date object to be converted
		 * @returns {Object} data Object encapsulating the date parameter converted to a NetSuite UTC
		 * 			date/time string and the Oleson GMT time zone code (data.UTC, data.tz)
		 */
		setDateTimeField : function (dt) {
			var diff = new Number(dt.toString().match(/GMT[+-]\d\d\d\d/)[0].substr(4, 4).replace(/0/g, ""));
			if (dt.toString().search(/GMT+\d\d\d\d/) >= 0) {
				diff = diff * (-1);
			}
			var data = { };
			data.UTC =  nlapiDateToString(new Date(dt.setHours(dt.getHours() + diff)), "datetimetz");
			data.tz = "GMT";
			
			return data;
		},
		
		// written by michaelkhalili 9/27/2009
		// returns time zone offset with respect to UTC and ignores DST
		// offset is neg for TZ west of UTC, pos for TZ east of UTC
		TimezoneDetect : function (){
			var dtDate = new Date('1/1/' + (new Date()).getUTCFullYear());
			var intOffset = 10000; //set initial offset high so it is adjusted on the first attempt
			var intMonth;
			//var intHoursUtc;
			//var intHours;
			//var intDaysMultiplyBy;
			 
			//go through each month to find the lowest offset to account for DST
			for (intMonth=0;intMonth < 12;intMonth++) {
				//go to the next month
				dtDate.setUTCMonth(dtDate.getUTCMonth() + 1);
				
				//To ignore daylight saving time look for the lowest offset.
				//Since, during DST, the clock moves forward, it'll be a bigger number.
				if (intOffset > (dtDate.getTimezoneOffset() * (-1))) {
					intOffset = (dtDate.getTimezoneOffset() * (-1));
				}
		    }
			
		    return intOffset;
		},

		//written by michaelkhalili 9/27/2009
		//Find start and end of DST
		DstDetect : function (){
			var dtDstDetect = new Date();
			var dtDstStart = '';
			var dtDstEnd = '';
			var dtDstStartHold = ''; //Temp date hold
			var intYearDayCount = 732; //366 (include leap year) * 2 (for two years)
			var intHourOfYear = 1;
			var intDayOfYear;
			var intOffset = this.lib.time.TimezoneDetect(); //Custom function. Make sure you include it.
			 
			//Start from a year ago to make sure we include any previously starting DST
			dtDstDetect = new Date();
			dtDstDetect.setUTCFullYear(dtDstDetect.getUTCFullYear() - 1);
			dtDstDetect.setUTCHours(0,0,0,0);
			
			//Going hour by hour through the year will detect DST with shorter code but that could result in 8760
			//FOR loops and several seconds of script execution time. Longer code narrows this down a little.
			//Go one day at a time and find out approx time of DST and if there even is DST on this computer.
			//Also need to make sure we catch the most current start and end cycle.
			for (intDayOfYear = 1; intDayOfYear <= intYearDayCount; intDayOfYear++) {
				dtDstDetect.setUTCDate(dtDstDetect.getUTCDate() + 1);
				
				if ((dtDstDetect.getTimezoneOffset() * (-1)) != intOffset && dtDstStartHold == '') {
					dtDstStartHold = new Date(dtDstDetect);
				}
				if ((dtDstDetect.getTimezoneOffset() * (-1)) == intOffset && dtDstStartHold != '') {
					dtDstStart = new Date(dtDstStartHold);
					dtDstEnd = new Date(dtDstDetect);
					dtDstStartHold = '';
					
					//DST is being used in this timezone. Narrow the time down to the exact hour the change happens
					//Remove 48 hours (a few extra to be on safe side) from the start/end date and find the exact change point
					//Go hour by hour until a change in the timezone offset is detected.
					dtDstStart.setUTCHours(dtDstStart.getUTCHours() - 48);
					dtDstEnd.setUTCHours(dtDstEnd.getUTCHours() - 48);
					
					//First find when DST starts
					for (intHourOfYear=1; intHourOfYear <= 48; intHourOfYear++) {
						dtDstStart.setUTCHours(dtDstStart.getUTCHours() + 1);
						
						//If we found it then exit the loop. dtDstStart will have the correct value left in it.
						if ((dtDstStart.getTimezoneOffset() * (-1)) != intOffset){
							break;
						}
					}
					
					//Now find out when DST ends
					for (intHourOfYear=1; intHourOfYear <= 48; intHourOfYear++) {
						dtDstEnd.setUTCHours(dtDstEnd.getUTCHours() + 1);
						
						//If we found it then exit the loop. dtDstEnd will have the correct value left in it.
						if ((dtDstEnd.getTimezoneOffset() * (-1)) != (intOffset + 60)) {
							break;
						}
					}
					
					//Check if DST is currently on for this time frame. If it is then return these values.
					//If not then keep going. The function will either return the last values collected
					//or another value that is currently in effect
					if ((new Date()).getTime() >= dtDstStart.getTime() && (new Date()).getTime() <= dtDstEnd.getTime()) {
						return new Array(dtDstStart,dtDstEnd);
					}
				}
			}
			return new Array(dtDstStart,dtDstEnd);
		},
		
		// wait timer
		// max wait: 15 seconds
		doWait : function (seconds, ignoreMax) {
			var ms = seconds * 1000;
			var start = new Date();
			var startMS = start.getTime();
			var diff = 0;
			var endMS = 0;
			do {
				var test = new Date();
				endMS = test.getTime();
				diff = endMS - startMS;
				if (diff >= 15000 && !ignoreMax) {
					diff = ms;
				}
			} while (diff < ms);

			return { s : startMS , e : endMS };
		},
		
		countDays : function (date1, date2) {
			return Math.round((date2-date1)/(1000*60*60*24));
		},
		
		dateToNSString : function (date, format) {
			format = format || "date";
			var dateString = "";
			if (format == "date") {
				dateString = "{0}/{1}/{2}".NG_Format((date.getMonth() + 1).toFixed(0),date.getDate().toFixed(0),date.getFullYear().toFixed(0));
			} else if (format == "datetime") {
				var h = date.getHours() > 12 ? (date.getHours() - 12).toFixed(0) : date.getHours().toFixed(0);
				var ap = date.getHours() > 12 ? "pm" : "am";
				dateString = "{0}/{1}/{2} {3}:{4} {5}".NG_Format((date.getMonth() + 1).toFixed(0),date.getDate().toFixed(0),date.getFullYear().toFixed(0),h,(date.getMinutes().toFixed(0)).paddingLeft("00"),ap);
			} else if (format == "datetimetz") {
				var h = date.getHours() > 12 ? (date.getHours() - 12).toFixed(0) : date.getHours().toFixed(0);
				var ap = date.getHours() > 12 ? "pm" : "am";
				dateString = "{0}/{1}/{2} {3}:{4}:{5} {6}".NG_Format((date.getMonth() + 1).toFixed(0),date.getDate().toFixed(0),date.getFullYear().toFixed(0),h,(date.getMinutes().toFixed(0)).paddingLeft("00"),(date.getSeconds().toFixed(0)).paddingLeft("00"),ap);
			}
			
			return dateString;
		},
		
		/*
		 * YYYY		four digit year
		 * YY		two digit year
		 * MMMM		full month name
		 * MMM		abbrev month name
		 * MM		month number with leading zero
		 * M		month number without leading zero
		 * DDDD		full day of week name
		 * DDD		abbrev day of week name
		 * DD		day of month number with leading zero
		 * D		day of month number without leading zero
		 * HH		Hour with leading zero (24h)
		 * H		Hour without leading zero (24h)
		 * hh		Hour with leading zero (12h)
		 * h		Hour without leading zero (12h)
		 * T		AM/PM
		 * t		am/pm
		 * mm		Minutes with leading zero
		 * m		Minutes without leading zero
		 * ss		Seconds with leading zero
		 * s		Seconds without leading zero
		 * zz		Milliseconds with leading zeroes
		 * z		Milliseconds without leading zeroes
		 */
		formatDateTime: function (date, format) {
			var days = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];
			var daysAlt = ["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
			var daysAbbrev = ["Sun","Mon","Tue","Wed","Thu","Fri","Sat"];
			var daysAltAbbrev = ["Mon","Tue","Wed","Thu","Fri","Sat","Sun"];
			var months = ["January","February","March","April","May","June","July","August","September","October","November","December"];
			var monthsAbbrev = ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];
			var y4 = date.getFullYear().toFixed(0);
			var y2 = y4.substr(2);
			var m = date.getMonth();
			var mo = Math.round(m + 1).toFixed(0);
			var mmo = mo.NG_paddingLeft("00");
			var d = date.getDate().toFixed(0);
			var dd = d.NG_paddingLeft("00");
			var dow = date.getDay();
			var dw = daysAbbrev[dow];
			var ddw = days[dow];
			var hInit = date.getHours();
			var h = hInit > 12 ? Math.round(hInit - 12).toFixed(0) : (hInit == 0 ? 12 : hInit.toFixed(0));
			var hh = h.NG_paddingLeft("00");
			var ap = hInit >= 12 ? "pm" : "am";
			var aapp = ap.toUpperCase();
			var h24 = hInit.toFixed(0);
			var hh24 = h24.NG_paddingLeft("00");
			var mi = date.getMinutes().toFixed(0);
			var mmi = mi.NG_paddingLeft("00");
			var s = date.getSeconds().toFixed(0);
			var ss = s.NG_paddingLeft("00");
			var z = date.getMilliseconds().toFixed(0);
			var zz = z.NG_paddingLeft("000");
			
			return format.replace("YYYY",y4).replace("YY",72).replace("MMMM",months[m]).replace("MMM",monthsAbbrev[m]).replace("MM",mmo).replace("M",mo)
				.replace("DDDD",days[dow]).replace("DDD",daysAbbrev[dow]).replace("DD",dd).replace("D",d).replace("HH",hh24).replace("H",h24)
				.replace("hh",hh).replace("h",h).replace("mm",mmi).replace("m",mi).replace("ss",ss).replace("s",s).replace("zz",zz).replace("z",z)
				.replace("T",aapp).replace("t",ap);
		},
		
		getCompanyTime : function(d) {
			var currentDateTime = d || new Date();
			var companyTimeZone = nlapiLoadConfiguration('companyinformation').getFieldText('timezone');
			var timeZoneOffSet = (companyTimeZone.indexOf('(GMT)') == 0) ? 0 : new Number(companyTimeZone.substr(4, 6).replace(/\+|:00/gi, '').replace(/:30/gi, '.5'));
			var UTC = currentDateTime.getTime() + (currentDateTime.getTimezoneOffset() * 60000);
			var companyDateTime = UTC + (timeZoneOffSet * 60 * 60 * 1000);
			
			return new Date(companyDateTime);
		},
		
		/*
		 * @param {Date} d Date object to be rounded 
		 * @param {Number} r Milliseconds by which to round down
		 * @param {Boolean} o If true, returns date object, else returns millsecond representation of date
		 */
		roundDownDate : function(d, r, o) {
			var rounded = Math.floor(d.getTime() / r) * r;
			if (o) { return new Date(rounded); } else { return rounded; }
		},
		
		/*
		 * @param {Date} d Date object to be rounded 
		 * @param {Number} r Milliseconds by which to round up
		 * @param {Boolean} o If true, returns date object, else returns millsecond representation of date
		 */
		roundUpDate : function(d, r, o) {
			var rounded = Math.ceil(d.getTime() / r) * r;
			if (o) { return new Date(rounded); } else { return rounded; }
		},
		
		/*
		 * @param {Date} d Date object to be rounded 
		 * @param {Number} r Milliseconds by which to round to nearest
		 * @param {Boolean} o If true, returns date object, else returns millsecond representation of date
		 */
		roundDate : function(d, r, o) {
			var rounded = Math.round(d.getTime() / r) * r;
			if (o) { return new Date(rounded); } else { return rounded; }
		},
		
		/*
		 * @param {Date} d Date object to be rounded 
		 * @param {Number} r Number of time units by which to round; Use negative to round down; Adds/subtracts value based on time units, then rounds to the unit; 0 assumes rounds d to nearest unit
		 * @param {Boolean} o If true, returns date object, else returns millsecond representation of date
		 * @param {String} t Character to define time unit: M = minute, H = hour, D = day (default = M)
		 * @param {String} n Character to define rounding action: N = nearest, U = up, D = down (default = N)
		 */
		roundDateX : function(d, r, o, t, n) {
			t = t || "M";
			n = d || "N";
			var m = { M : (1000 * 60) , H : (1000 * 60 * 60) , D : (1000 *  60 * 60 * 24) };
			if (r == 0) {
				return NewGen.lib.time.roundDate(d, m[t], o);
			} else {
				var a = new Date(d.getTime() + (r * m[t]));
				switch (n) {
					case "N":
						return NewGen.lib.time.roundDate(a, m[t], o);
						break;
					case "U":
						return NewGen.lib.time.roundUpDate(a, m[t], o);
						break;
					case "D":
						return NewGen.lib.time.roundDownDate(a, m[t], o);
						break;
					default:
						return NewGen.lib.time.roundDate(a, m[t], o);
						break;
				}
			}
		},
		
		getSimplifiedDate : function(date) {
			if (Object.prototype.toString.call(date) === '[object Date]') {
				return nlapiStringToDate(nlapiDateToString(date, "date"), "date");
			} else if (Object.prototype.toString.call(date) === '[object String]') {
				return nlapiDateToString(nlapiStringToDate(date, "date"), "date");
			}
		}
	},
	
	logging : {
		createActivityLog : function (status, title, details, zone, err, showSec, type) {
			var __tools = NewGen.lib.tools;
			var __log = NewGen.lib.logging;
			var __obj = NewGen.lib.obj;
			try {
				var logRec = nlapiCreateRecord("customrecord_ng_log");
				/*if (showSec) {
					logRec.setFieldValue("custrecord_nglog_logdate", __tools.timeStamp(false, zone, true));
				} else {
					logRec.setFieldValue("custrecord_nglog_logdate", __tools.timeStamp(true, zone, false));
				}*/
				
				if (!__tools.isEmpty(type)) {
					logRec.setFieldValue("custrecord_nglog_type", type);
				}
				if (!__tools.isEmpty(status)) {
					logRec.setFieldValue("custrecord_nglog_status", status);
				}
				if (!__tools.isEmpty(title)) {
					logRec.setFieldValue("custrecord_nglog_title", nlapiEscapeXML(title));
				}
				
				if (!__tools.isEmpty(details) && __tools.isEmpty(err)) {
					logRec.setFieldValue("custrecord_nglog_details", nlapiEscapeXML(details));
				} else if (__tools.isEmpty(details) && !__tools.isEmpty(err)) {
					if (err instanceof nlobjError) {
						var rawStack = err.getStackTrace();
						var stack = __obj.IsArray(rawStack) ? rawStack.join(", ") : rawStack; 
						logRec.setFieldValue("custrecord_nglog_details", "[{0}] {1}{2}".NG_Format(err.getCode(),err.getDetails(),!__tools.isEmpty(stack)?" -- {0}".NG_Format(stack):""));
					} else {
						logRec.setFieldValue("custrecord_nglog_details", "[{0}] {1}".NG_Format(err.name,err.message));
					}
				} else if (!__tools.isEmpty(details) && !__tools.isEmpty(err)) {
					var msg = "";
					if (err instanceof nlobjError) {
						var rawStack = err.getStackTrace();
						var stack = __obj.IsArray(rawStack) ? rawStack.join(", ") : rawStack;
						msg = "[{0}] {1}{2}".NG_Format(err.getCode(),err.getDetails(),!__tools.isEmpty(stack)?" -- {0}".NG_Format(stack):"");
					} else {
						msg = "[{0}] {1}".NG_Format(err.name,err.message);
					}
					
					logRec.setFieldValue("custrecord_nglog_details", details + " \//*****\// " + msg);
				} else {
					logRec.setFieldValue("custrecord_nglog_details", "N/A");
				}
				nlapiSubmitRecord(logRec);
			} catch (err) {
				__log.logError(err, "Error encountered submitting activity log record");
			}
		},
		
		logError : function (err, message, details) {
			var __tools = NewGen.lib.tools;
			var __obj = NewGen.lib.obj;
			if (err != null && err instanceof nlobjError) {
				var rawStack = err.getStackTrace();
				var stack = __obj.IsArray(rawStack) ? rawStack.join(", ") : rawStack;
				if (!__tools.isEmpty(details)) {
					nlapiLogExecution("ERROR", message, "[{0}] {1}{2} -- {3}".NG_Format(err.getCode(),err.getDetails(),!__tools.isEmpty(stack)?" -- {0}".NG_Format(stack):"",details));
				} else {
					nlapiLogExecution("ERROR", message, "[{0}] {1}{2}".NG_Format(err.getCode(),err.getDetails(),!__tools.isEmpty(stack)?" -- {0}".NG_Format(stack):""));
				}
			} else if (err != null) {
				if (!__tools.isEmpty(details)) {
					nlapiLogExecution("ERROR", message, "[{0}] {1} -- {2}".NG_Format(err.name,err.message,details));
				} else {
					nlapiLogExecution("ERROR", message, "[{0}] {1}".NG_Format(err.name,err.message));
				}
			} else {
				if (!__tools.isEmpty(details)) {
					nlapiLogExecution("ERROR", message, details);
				} else {
					nlapiLogExecution("ERROR", message);
				}
			}
		},
		
		logInfo : function (title, details) {
			details = details || "";
			nlapiLogExecution("AUDIT", title, details);
		},
		
		logDebug : function (title, details) {
			details = details || "";
			nlapiLogExecution("DEBUG", title, details);
		}
	},
	
	UI : {
		addSuccessDiv : function(form, message, fieldId, atTop, fieldGroup, divId) {
			fieldGroup = fieldGroup || null;
			var content = NewGen.lib.successMsgAction.replace("<MESSAGE>", message);
			if (!NewGen.lib.tools.isEmpty(divId)) {
				content.replace("div__alert", divId);
			}
			var divField = form.addField(fieldId, "inlinehtml", "", null, fieldGroup);
			if (atTop) {
				divField.setLayoutType("outsideabove", "startrow");
			} else {
				divField.setLayoutType("normal", "none");
			}
			divField.setDefaultValue(content);
		},
		
		addWarningDiv : function(form, message, fieldId, atTop, fieldGroup, divId) {
			fieldGroup = fieldGroup || null;
			var content = NewGen.lib.warningMsgAlt.replace("<MESSAGE>", message);
			if (!NewGen.lib.tools.isEmpty(divId)) {
				content.replace("div__alert", divId);
			}
			var divField = form.addField(fieldId, "inlinehtml", "", null, fieldGroup);
			if (atTop) {
				divField.setLayoutType("outsideabove", "startrow");
			} else {
				divField.setLayoutType("normal", "none");
			}
			divField.setDefaultValue(content);
		},
		
		addFailureDiv : function(form, message, fieldId, atTop, fieldGroup, divId) {
			fieldGroup = fieldGroup || null;
			var content = NewGen.lib.failureMsgAlt.replace("<MESSAGE>", message);
			if (!NewGen.lib.tools.isEmpty(divId)) {
				content.replace("div__alert", divId);
			}
			var divField = form.addField(fieldId, "inlinehtml", "", null, fieldGroup);
			if (atTop) {
				divField.setLayoutType("outsideabove", "startrow");
			} else {
				divField.setLayoutType("normal", "none");
			}
			divField.setDefaultValue(content);
		}
	}
};

////////////////////////////
// NUMBER prototypes
////////////////////////////

/**
 * @param {Number} c Decimal places to display [optional] (default: 2)
 * @param {String} d Decimal separator [optional] (default: . )
 * @param {Date} t Thousands separator [optional] (default: , )
 * @returns {String} Curremcy amount formatted into string
 */
Number.prototype.NG_formatMoney = function(c, d, t) {
	c = isNaN(c = Math.abs(c)) ? 2 : c;
	d = d == undefined ? "." : d;
	t = t == undefined ? "," : t;
	var n = this,
		s = n < 0 ? "-" : "",
		i = parseInt(n = Math.abs(+n || 0).toFixed(c)) + "",
		j = (j = i.length) > 3 ? j % 3 : 0;
	return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
};

Number.prototype.NG_paddingLeft = function (paddingValue) {
	var str = this.toString();
	return String(paddingValue + str).slice(-paddingValue.length);
};

////////////////////////////
// STRING prototypes
////////////////////////////

String.prototype.NG_Format = function () {
	var args = arguments;
	return this.replace(/{(\d+)}/g, function (match, number) {
		return typeof args[number] != 'undefined' ? args[number] : "null"; //match;
	});
};

String.prototype.NG_paddingLeft = function (paddingValue) {
	return String(paddingValue + this).slice(-paddingValue.length);
};

String.prototype.NG_paddingRight = function (paddingValue) {
	return String(this + paddingValue).substr(0, paddingValue.length);
};

String.prototype.NG_toProperCase = function () {
    //return this.replace(/\w\S*/g, function(txt){return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();});
	return this.replace(/([^\W_]+[^\s-]*) */g, function(txt){return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();});
};

String.prototype.NG_toUpperRoman = function () {
	return this.replace(/(M{0,4}(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3}))/g, function(txt){return txt.toUpperCase();});
};

String.prototype.NG_toTitleCase = function() {
	var i, j, str, lowers, uppers;
	str = this.replace(/([^\W_]+[^\s-]*) */g, function(txt) {
		return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
	});
	
	// Certain minor words should be left lowercase unless 
	// they are the first or last words in the string
	lowers = ['A', 'An', 'The', 'And', 'But', 'Or', 'For', 'Nor', 'As', 'At', 'By', 'For', 'From', 'In', 'Into', 'Near', 'Of', 'On', 'Onto', 'To', 'With'];
	for (i = 0, j = lowers.length; i < j; i++) {
		str = str.replace(new RegExp('\\s' + lowers[i] + '\\s', 'g'), function(txt) {
			return txt.toLowerCase();
		});
	}
	
	// Certain words such as initialisms or acronyms should be left uppercase
	uppers = ['Id', 'Tv'];
	for (i = 0, j = uppers.length; i < j; i++) {
		str = str.replace(new RegExp('\\b' + uppers[i] + '\\b', 'g'), 
		uppers[i].toUpperCase());
	}
	
	return str;
};

if (!String.prototype.includes) {
	String.prototype.includes = function(search, start) {
		'use strict';
		if (typeof start !== 'number') {
			start = 0;
		}
		
		if (start + search.length > this.length) {
			return false;
		} else {
			return this.indexOf(search, start) !== -1;
		}
	};
}

////////////////////////////
// ARRAY prototypes
////////////////////////////

Array.prototype.NG_intersect = function (target) {
	var sorted_a = this.concat().sort();
	var sorted_b = target.concat().sort();
	var common = [];
	var a_i = 0;
	var b_i = 0;
	
	while (a_i < this.length && b_i < target.length) {
		if (sorted_a[a_i] === sorted_b[b_i]) {
			common.push(sorted_a[a_i]);
			a_i++;
			b_i++;
		} else if(sorted_a[a_i] < sorted_b[b_i]) {
			a_i++;
		} else {
			b_i++;
		}
	}
	return common;
};

Array.prototype.NG_differential = function (comp) {
	return this.filter(function(el) {
		return comp.indexOf(el) < 0;
	});
};

Array.prototype.NG_contains = function (value, strict, caseInsensitive) {
	var match = false;
	var ciValue = value;
	strict = strict || false;
	caseInsensitive = caseInsensitive || false;
	
	if (caseInsensitive) {
		try { ciValue = value.toUpperCase(); } catch (err) {}
	}
	
	for (var i = 0; i < this.length; i++) {
		if (caseInsensitive) {
			try {
				if ((strict && ciValue === this[i].toUpperCase()) || (!strict && ciValue == this[i].toUpperCase())) {
					match = true;
					break;
				}
			} catch (err) {
				if ((strict && value === this[i]) || (!strict && value == this[i])) {
					match = true;
					break;
				}
			}
		} else {
			if ((strict && value === this[i]) || (!strict && value == this[i])) {
				match = true;
				break;
			}
		}
	}
	return match;
};

Array.prototype.NG_find = function (func) {
	if (NewGen.lib.tools.isEmpty(func)) { return null; }
	for (var i = 0; i < this.length; i++) {
		if (func(this[i])) {
			return this[i];
		}
	}
	
	return null;
}

Array.prototype.NG_findAlt = function (property, value) {
	if (NewGen.lib.tools.isEmpty(property)) { return null; }
	for (var i = 0; i < this.length; i++) {
		if (this[i][property] === value) {
			return this[i];
		}
	}
	
	return null;
}

Array.prototype.NG_findIndex = function (func) {
	if (NewGen.lib.tools.isEmpty(func)) { return -1; }
	for (var i = 0; i < this.length; i++) {
		if (func(this[i])) {
			return i;
		}
	}
	
	return -1;
}

Array.prototype.NG_findIndexAlt = function (property, value) {
	if (NewGen.lib.tools.isEmpty(property)) { return -1; }
	for (var i = 0; i < this.length; i++) {
		if (this[i][property] === value) {
			return i;
		}
	}
	
	return -1;
}

if (!Array.prototype.find) {
	Object.defineProperty(Array.prototype, 'find',
		{
				value: function (predicate) {
					if (NewGen.lib.tools.isEmpty(this)) {
						throw new TypeError('"this" is null or not defined');
					}
					if (NewGen.lib.tools.isEmpty(predicate)) {
						throw new TypeError('predicate is null or not defined');
					}
					var o = Object(this);
					var len = o.length >>> 0;
					if (typeof predicate !== 'function') {
						throw new TypeError('predicate must be a function');
					}
					var thisArg = arguments[1];
					var k = 0;
					while (k < len) {
						var kValue = o[k];
						if (predicate.call(thisArg, kValue, k, o)) { return kValue; }
						k++;
					}
					return undefined;
				}
			,	configurable: true
			,	writable: true
		}
	);
}

if (!Array.prototype.findIndex) {
	Object.defineProperty(Array.prototype, 'findIndex',
		{
				value: function (predicate) {
					if (NewGen.lib.tools.isEmpty(this)) {
						throw new TypeError('"this" is null or not defined');
					}
					if (NewGen.lib.tools.isEmpty(predicate)) {
						throw new TypeError('predicate is null or not defined');
					}
					var o = Object(this);
					var len = o.length >>> 0;
					if (typeof predicate !== 'function') {
						throw new TypeError('predicate must be a function');
					}
					var thisArg = arguments[1];
					var k = 0;
					while (k < len) {
						var kValue = o[k];
						if (predicate.call(thisArg, kValue, k, o)) { return k; }
						k++;
					}
					return -1;
				}
			,	configurable: true
			,	writable: true
		 }
	);
}

/*if (!Array.prototype.contains) {
	Object.defineProperty(Array.prototype, 'contains',
		{
				value: function (predicate) {
					if (NewGen.lib.tools.isEmpty(this)) {
						throw new TypeError('"this" is null or not defined');
					}
					if (NewGen.lib.tools.isEmpty(predicate)) {
						throw new TypeError('predicate is null or not defined');
					}
					var o = Object(this);
					var len = o.length >>> 0;
					if (typeof predicate !== 'function') {
						throw new TypeError('predicate must be a function');
					}
					var thisArg = arguments[1];
					var k = 0;
					while (k < len) {
						var kValue = o[k];
						if (predicate.call(thisArg, kValue, k, o)) { return true; }
						k++;
					}
					return false;
				}
			,	configurable: true
			,	writable: true
		 }
	);
}*/

/*if (!Array.prototype.includes) {
	Object.defineProperty(Array.prototype, 'includes', {
		value: function(searchElement, fromIndex) {
			if (this == null) {
				throw new TypeError('"this" is null or not defined');
			}
			
			const o = Object(this);
			// tslint:disable-next-line:no-bitwise
			const len = o.length >>> 0;
			
			if (len === 0) {
				return false;
			}
			// tslint:disable-next-line:no-bitwise
			const n = fromIndex | 0;
			let k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);
			
			while (k < len) {
				if (o[k] === searchElement) {
					return true;
				}
				k++;
			}
			return false;
		}
	});
}*/

Array.prototype.NG_findAll = function (func) {
	var results =  [];
	if (NewGen.lib.tools.isEmpty(func)) { return results; }
	for (var i = 0; i < this.length; i++) {
		if (func(this[i])) {
			results.push(this[i]);
		}
	}
	
	return results;
}

Array.prototype.NG_findAllIndex = function (func) {
	var results =  [];
	if (NewGen.lib.tools.isEmpty(func)) { return results; }
	for (var i = 0; i < this.length; i++) {
		if (func(this[i])) {
			results.push(i);
		}
	}
	
	return results;
}

////////////////////////////
//REGEXP prototypes
////////////////////////////

if (!RegExp.prototype.escape) {
	Object.defineProperty(RegExp.prototype, 'escape',
		{
				value : function(literal_string) {
					return literal_string.replace(/[-[\]{}()*+!<=:?.\/\\^$|#\s,]/g, '\\$&');
				}
			,	configurable: true
			,	writable: true
		}
	);
}

////////////////////////////
// Potentially conflicting prototypes
////////////////////////////

Number.prototype.formatMoney = function(c, d, t) {
	c = isNaN(c = Math.abs(c)) ? 2 : c;
	d = d == undefined ? "." : d;
	t = t == undefined ? "," : t;
	var n = this,
		s = n < 0 ? "-" : "",
		i = parseInt(n = Math.abs(+n || 0).toFixed(c)) + "",
		j = (j = i.length) > 3 ? j % 3 : 0;
	return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
};

String.prototype.paddingLeft = function (paddingValue) {
	return String(paddingValue + this).slice(-paddingValue.length);
};

Array.prototype.intersect = function (target) {
	var sorted_a = this.concat().sort();
	var sorted_b = target.concat().sort();
	var common = [];
	var a_i = 0;
	var b_i = 0;
	
	while (a_i < this.length && b_i < target.length) {
		if (sorted_a[a_i] === sorted_b[b_i]) {
			common.push(sorted_a[a_i]);
			a_i++;
			b_i++;
		} else if(sorted_a[a_i] < sorted_b[b_i]) {
			a_i++;
		} else {
			b_i++;
		}
	}
	return common;
};

Array.prototype.differential = function (comp) {
	return this.filter(function(el) {
		return comp.indexOf(el) < 0;
	});
};

Array.prototype.contains = function (value, strict, caseInsensitive) {
	var match = false;
	var ciValue = value;
	strict = strict || false;
	caseInsensitive = caseInsensitive || false;
	
	if (caseInsensitive) {
		try { ciValue = value.toUpperCase(); } catch (err) {}
	}
	
	for (var i = 0; i < this.length; i++) {
		if (caseInsensitive) {
			try {
				if ((strict && ciValue === this[i].toUpperCase()) || (!strict && ciValue == this[i].toUpperCase())) {
					match = true;
					break;
				}
			} catch (err) {
				if ((strict && value === this[i]) || (!strict && value == this[i])) {
					match = true;
					break;
				}
			}
		} else {
			if ((strict && value === this[i]) || (!strict && value == this[i])) {
				match = true;
				break;
			}
		}
	}
	return match;
};

//var window = window || { };
//var jQuery = jQuery || { };
//window.jQuery = window.jQuery || jQuery;