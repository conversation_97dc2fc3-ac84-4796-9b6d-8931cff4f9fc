import LocaleHelper from "@bryntum/core-thin/lib/localization/LocaleHelper.js";
import "@bryntum/core-thin/lib/localization/Uk.js";
const emptyString = new String();
const locale = {
  localeName: "Uk",
  localeDesc: "\u0423\u043A\u0440\u0430\u0457\u043D\u0441\u044C\u043A\u0430",
  localeCode: "uk-UA",
  ColumnPicker: {
    column: "\u0421\u0442\u043E\u0432\u043F\u0435\u0446\u044C",
    columnsMenu: "\u0421\u0442\u043E\u0432\u043F\u0446\u0456",
    hideColumn: "\u041F\u0440\u0438\u0445\u043E\u0432\u0430\u0442\u0438 \u0441\u0442\u043E\u0432\u043F\u0435\u0446\u044C",
    hideColumnShort: "\u041F\u0440\u0438\u0445\u043E\u0432\u0430\u0442\u0438",
    newColumns: "\u0421\u0442\u0432\u043E\u0440\u0438\u0442\u0438 \u0441\u0442\u043E\u0432\u043F\u0446\u0456"
  },
  Filter: {
    applyFilter: "\u0417\u0430\u0441\u0442\u043E\u0441\u0443\u0432\u0430\u0442\u0438 \u0444\u0456\u043B\u044C\u0442\u0440",
    filter: "\u0424\u0456\u043B\u044C\u0442\u0440",
    editFilter: "\u0420\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u0444\u0456\u043B\u044C\u0442\u0440",
    on: "\u0412",
    before: "\u0414\u043E",
    after: "\u041F\u0456\u0441\u043B\u044F",
    equals: "\u0414\u043E\u0440\u0456\u0432\u043D\u044E\u0454",
    lessThan: "\u041C\u0435\u043D\u0448\u0435",
    moreThan: "\u0411\u0456\u043B\u044C\u0448\u0435",
    removeFilter: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0444\u0456\u043B\u044C\u0442\u0440",
    disableFilter: "\u0412\u0438\u043C\u043A\u043D\u0443\u0442\u0438 \u0444\u0456\u043B\u044C\u0442\u0440"
  },
  FilterBar: {
    enableFilterBar: "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u0438 \u043F\u0430\u043D\u0435\u043B\u044C \u0444\u0456\u043B\u044C\u0442\u0440\u0443\u0432\u0430\u043D\u043D\u044F",
    disableFilterBar: "\u041F\u0440\u0438\u0445\u043E\u0432\u0430\u0442\u0438 \u043F\u0430\u043D\u0435\u043B\u044C \u0444\u0456\u043B\u044C\u0442\u0440\u0443\u0432\u0430\u043D\u043D\u044F"
  },
  Group: {
    group: "\u0413\u0440\u0443\u043F\u0443\u0432\u0430\u0442\u0438",
    groupAscending: "\u0413\u0440\u0443\u043F\u0443\u0432\u0430\u0442\u0438 \u0437\u0430 \u0437\u0440\u043E\u0441\u0442\u0430\u043D\u043D\u044F\u043C",
    groupDescending: "\u0413\u0440\u0443\u043F\u0443\u0432\u0430\u0442\u0438 \u0437\u0430 \u0441\u043F\u0430\u0434\u0430\u043D\u043D\u044F\u043C",
    groupAscendingShort: "\u0417\u0430 \u0437\u0440\u043E\u0441\u0442\u0430\u043D\u043D\u044F\u043C",
    groupDescendingShort: "\u0417\u0430 \u0441\u043F\u0430\u0434\u0430\u043D\u043D\u044F\u043C",
    stopGrouping: "\u0417\u0443\u043F\u0438\u043D\u0438\u0442\u0438 \u0433\u0440\u0443\u043F\u0443\u0432\u0430\u043D\u043D\u044F",
    stopGroupingShort: "\u0417\u0443\u043F\u0438\u043D\u0438\u0442\u0438"
  },
  HeaderMenu: {
    moveBefore: (text) => `\u041F\u0435\u0440\u0435\u043C\u0456\u0441\u0442\u0438\u0442\u0438 \u043F\u0435\u0440\u0435\u0434 "${text}"`,
    moveAfter: (text) => `\u041F\u0435\u0440\u0435\u043C\u0456\u0441\u0442\u0438\u0442\u0438 \u043F\u0456\u0441\u043B\u044F "${text}"`,
    collapseColumn: "\u0417\u0433\u043E\u0440\u043D\u0443\u0442\u0438 \u0441\u0442\u043E\u0432\u043F\u0435\u0446\u044C",
    expandColumn: "\u0420\u043E\u0437\u0433\u043E\u0440\u043D\u0443\u0442\u0438 \u0441\u0442\u043E\u0432\u043F\u0435\u0446\u044C"
  },
  ColumnRename: {
    rename: "\u041F\u0435\u0440\u0435\u0439\u043C\u0435\u043D\u0443\u0432\u0430\u0442\u0438"
  },
  MergeCells: {
    mergeCells: "\u041E\u0431\u2019\u0454\u0434\u043D\u0430\u0442\u0438 \u043A\u043B\u0456\u0442\u0438\u043D\u043A\u0438",
    menuTooltip: "\u041E\u0431\u2019\u0454\u0434\u043D\u0430\u0442\u0438 \u043A\u043B\u0456\u0442\u0438\u043D\u043A\u0438 \u0437 \u043E\u0434\u043D\u0430\u043A\u043E\u0432\u0438\u043C \u0437\u043D\u0430\u0447\u0435\u043D\u043D\u044F\u043C \u0443 \u0440\u0430\u0437\u0456 \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u0446\u0438\u043C \u0441\u0442\u043E\u0432\u043F\u0446\u0435\u043C"
  },
  Search: {
    searchForValue: "\u041F\u043E\u0448\u0443\u043A \u0437\u043D\u0430\u0447\u0435\u043D\u043D\u044F"
  },
  Sort: {
    sort: "\u0421\u043E\u0440\u0442\u0443\u0432\u0430\u0442\u0438",
    sortAscending: "\u0421\u043E\u0440\u0442\u0443\u0432\u0430\u0442\u0438 \u0437\u0430 \u0437\u0440\u043E\u0441\u0442\u0430\u043D\u043D\u044F\u043C",
    sortDescending: "\u0421\u043E\u0440\u0442\u0443\u0432\u0430\u0442\u0438 \u0437\u0430 \u0441\u043F\u0430\u0434\u0430\u043D\u043D\u044F\u043C",
    multiSort: "\u0421\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u043A\u0456\u043B\u044C\u043A\u043E\u043C\u0430 \u043A\u0440\u0438\u0442\u0435\u0440\u0456\u044F\u043C\u0438",
    removeSorter: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F",
    addSortAscending: "\u0414\u043E\u0434\u0430\u0442\u0438 \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u0437\u0440\u043E\u0441\u0442\u0430\u043D\u043D\u044F\u043C",
    addSortDescending: "\u0414\u043E\u0434\u0430\u0442\u0438 \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u0441\u043F\u0430\u0434\u0430\u043D\u043D\u044F\u043C",
    toggleSortAscending: "\u0417\u043C\u0456\u043D\u0438\u0442\u0438 \u043D\u0430 \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u0437\u0440\u043E\u0441\u0442\u0430\u043D\u043D\u044F\u043C",
    toggleSortDescending: "\u0417\u043C\u0456\u043D\u0438\u0442\u0438 \u043D\u0430 \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u0441\u043F\u0430\u0434\u0430\u043D\u043D\u044F\u043C",
    sortAscendingShort: "\u0417\u0430 \u0437\u0440\u043E\u0441\u0442\u0430\u043D\u043D\u044F\u043C",
    sortDescendingShort: "\u0417\u0430 \u0441\u043F\u0430\u0434\u0430\u043D\u043D\u044F\u043C",
    removeSorterShort: "\u0412\u0438\u043B\u0443\u0447\u0438\u0442\u0438",
    addSortAscendingShort: "+ \u0437\u0430 \u0437\u0440\u043E\u0441\u0442\u0430\u043D\u043D\u044F\u043C",
    addSortDescendingShort: "+ \u0437\u0430 \u0441\u043F\u0430\u0434\u0430\u043D\u043D\u044F\u043C"
  },
  Split: {
    split: "\u0420\u043E\u0437\u0434\u0456\u043B\u0438\u0442\u0438",
    unsplit: "\u0421\u043A\u0430\u0441\u0443\u0432\u0430\u0442\u0438 \u0440\u043E\u0437\u0434\u0456\u043B\u0435\u043D\u043D\u044F",
    horizontally: "\u0413\u043E\u0440\u0438\u0437\u043E\u043D\u0442\u0430\u043B\u044C\u043D\u043E",
    vertically: "\u0412\u0435\u0440\u0442\u0438\u043A\u0430\u043B\u044C\u043D\u043E",
    both: "\u0412 \u043E\u0431\u043E\u0445 \u043D\u0430\u043F\u0440\u044F\u043C\u043A\u0430\u0445"
  },
  LockRows: {
    lockRow: "\u0417\u0430\u043C\u043A\u043E\u0432\u0438\u0439 \u0440\u044F\u0434",
    unlockRow: "\u0420\u044F\u0434\u043E\u043A \u0431\u0435\u0437 \u0437\u0430\u043C\u043A\u0430"
  },
  Column: {
    columnLabel: (column) => `${column.text ? `\u0421\u0442\u043E\u0432\u043F\u0435\u0446\u044C \u0442\u0438\u043F\u0443 ${column.text}. ` : ""}\u041D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C \u043A\u043B\u0430\u0432\u0456\u0448\u0443 \u041F\u0420\u041E\u0411\u0406\u041B \u0434\u043B\u044F \u0432\u0438\u043A\u043B\u0438\u043A\u0443 \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E\u0433\u043E \u043C\u0435\u043D\u044E${column.sortable ? " \u0430\u0431\u043E ENTER \u0434\u043B\u044F \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F" : ""}`,
    cellLabel: emptyString
  },
  Checkbox: {
    toggleRowSelect: "\u0412\u0438\u0431\u0440\u0430\u0442\u0438 \u0440\u044F\u0434\u043E\u043A \u0430\u0431\u043E \u0441\u043A\u0430\u0441\u0443\u0432\u0430\u0442\u0438 \u0432\u0438\u0431\u0456\u0440",
    toggleSelection: "\u0412\u0438\u0431\u0440\u0430\u0442\u0438 \u0432\u0435\u0441\u044C \u043D\u0430\u0431\u0456\u0440 \u0434\u0430\u043D\u0438\u0445 \u0430\u0431\u043E \u0441\u043A\u0430\u0441\u0443\u0432\u0430\u0442\u0438 \u0432\u0438\u0431\u0456\u0440"
  },
  RatingColumn: {
    cellLabel: (column) => {
      var _a;
      return `${column.text ? column.text : ""} ${((_a = column.location) == null ? void 0 : _a.record) ? `\u041E\u0446\u0456\u043D\u043A\u0430 : ${column.location.record.get(column.field) || 0}` : ""}`;
    }
  },
  GridBase: {
    loadFailedMessage: "\u041D\u0435 \u0432\u0434\u0430\u043B\u043E\u0441\u044F \u0437\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0438\u0442\u0438 \u0434\u0430\u043D\u0456!",
    syncFailedMessage: "\u041D\u0435 \u0432\u0434\u0430\u043B\u043E\u0441\u044F \u0441\u0438\u043D\u0445\u0440\u043E\u043D\u0456\u0437\u0443\u0432\u0430\u0442\u0438 \u0434\u0430\u043D\u0456!",
    unspecifiedFailure: "\u041D\u0435\u0432\u0438\u0437\u043D\u0430\u0447\u0435\u043D\u0430 \u043F\u043E\u043C\u0438\u043B\u043A\u0430",
    networkFailure: "\u041F\u043E\u043C\u0438\u043B\u043A\u0430 \u043C\u0435\u0440\u0435\u0436\u0456",
    parseFailure: "\u041D\u0435 \u0432\u0434\u0430\u043B\u043E\u0441\u044F \u043F\u0440\u043E\u0430\u043D\u0430\u043B\u0456\u0437\u0443\u0432\u0430\u0442\u0438 \u0432\u0456\u0434\u043F\u043E\u0432\u0456\u0434\u044C \u0441\u0435\u0440\u0432\u0435\u0440\u0430",
    serverResponse: "\u0412\u0456\u0434\u043F\u043E\u0432\u0456\u0434\u044C \u0441\u0435\u0440\u0432\u0435\u0440\u0430:",
    noRows: "\u041D\u0435\u043C\u0430\u0454 \u0437\u0430\u043F\u0438\u0441\u0456\u0432 \u0434\u043B\u044F \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
    moveColumnLeft: "\u041F\u0435\u0440\u0435\u043C\u0456\u0441\u0442\u0438\u0442\u0438 \u0434\u043E \u043B\u0456\u0432\u043E\u0433\u043E \u0440\u043E\u0437\u0434\u0456\u043B\u0443",
    moveColumnRight: "\u041F\u0435\u0440\u0435\u043C\u0456\u0441\u0442\u0438\u0442\u0438 \u0434\u043E \u043F\u0440\u0430\u0432\u043E\u0433\u043E \u0440\u043E\u0437\u0434\u0456\u043B\u0443",
    moveColumnTo: (region) => `\u041F\u0435\u0440\u0435\u043C\u0456\u0441\u0442\u0438\u0442\u0438 \u0441\u0442\u043E\u0432\u043F\u0435\u0446\u044C \u0434\u043E ${region}`
  },
  CellMenu: {
    removeRow: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438"
  },
  RowCopyPaste: {
    copyRecord: "\u041A\u043E\u043F\u0456\u044E\u0432\u0430\u0442\u0438",
    cutRecord: "\u0412\u0438\u0440\u0456\u0437\u0430\u0442\u0438",
    pasteRecord: "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438",
    rows: "\u0420\u044F\u0434\u043A\u0438",
    row: "\u0420\u044F\u0434\u043E\u043A"
  },
  CellCopyPaste: {
    copy: "\u041A\u043E\u043F\u0456\u044E\u0432\u0430\u0442\u0438",
    cut: "\u0412\u0438\u0440\u0456\u0437\u0430\u0442\u0438",
    paste: "\u0412\u0441\u0442\u0430\u0432\u0438\u0442\u0438"
  },
  PdfExport: {
    "Waiting for response from server": "\u041E\u0447\u0456\u043A\u0443\u0432\u0430\u043D\u043D\u044F \u0432\u0456\u0434\u043F\u043E\u0432\u0456\u0434\u0456 \u0432\u0456\u0434 \u0441\u0435\u0440\u0432\u0435\u0440\u0430...",
    "Export failed": "\u041F\u043E\u043C\u0438\u043B\u043A\u0430 \u043F\u0456\u0434 \u0447\u0430\u0441 \u0441\u043F\u0440\u043E\u0431\u0438 \u0435\u043A\u0441\u043F\u043E\u0440\u0442\u0443\u0432\u0430\u0442\u0438 \u0434\u0430\u043D\u0456",
    "Server error": "\u041F\u043E\u043C\u0438\u043B\u043A\u0430 \u0441\u0435\u0440\u0432\u0435\u0440\u0430",
    "Generating pages": "\u0421\u0442\u0432\u043E\u0440\u0435\u043D\u043D\u044F \u0441\u0442\u043E\u0440\u0456\u043D\u043E\u043A...",
    "Click to abort": "\u0421\u043A\u0430\u0441\u0443\u0432\u0430\u0442\u0438"
  },
  ExportDialog: {
    width: "40em",
    labelWidth: "12em",
    exportSettings: "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0435\u043A\u0441\u043F\u043E\u0440\u0442\u0443 \u0434\u0430\u043D\u0438\u0445",
    export: "\u0415\u043A\u0441\u043F\u043E\u0440\u0442\u0443\u0432\u0430\u0442\u0438 \u0434\u0430\u043D\u0456",
    printSettings: "\u041F\u0430\u0440\u0430\u043C\u0435\u0442\u0440\u0438 \u0434\u0440\u0443\u043A\u0443",
    print: "\u0420\u043E\u0437\u0434\u0440\u0443\u043A\u0443\u0432\u0430\u0442\u0438",
    exporterType: "\u041A\u043E\u043D\u0442\u0440\u043E\u043B\u044C\u043D\u0430 \u043F\u0430\u0433\u0456\u043D\u0430\u0446\u0456\u044F",
    cancel: "\u0421\u043A\u0430\u0441\u0443\u0432\u0430\u0442\u0438",
    fileFormat: "\u0424\u043E\u0440\u043C\u0430\u0442 \u0444\u0430\u0439\u043B\u0443",
    rows: "\u0420\u044F\u0434\u043A\u0438",
    alignRows: "\u0412\u0438\u0440\u0456\u0432\u043D\u044F\u0442\u0438 \u0440\u044F\u0434\u043A\u0438",
    columns: "\u0421\u0442\u043E\u0432\u043F\u0446\u0456",
    paperFormat: "\u0424\u043E\u0440\u043C\u0430\u0442 \u0430\u0440\u043A\u0443\u0448\u0430",
    orientation: "\u041E\u0440\u0456\u0454\u043D\u0442\u0430\u0446\u0456\u044F",
    repeatHeader: "\u041F\u043E\u0432\u0442\u043E\u0440\u044E\u0432\u0430\u0442\u0438 \u0437\u0430\u0433\u043E\u043B\u043E\u0432\u043E\u043A"
  },
  ExportRowsCombo: {
    all: "\u0423\u0441\u0456 \u0440\u044F\u0434\u043A\u0438",
    visible: "\u0412\u0438\u0434\u0438\u043C\u0456 \u0440\u044F\u0434\u043A\u0438"
  },
  ExportOrientationCombo: {
    portrait: "\u041A\u043D\u0438\u0436\u043A\u043E\u0432\u0430",
    landscape: "\u0410\u043B\u044C\u0431\u043E\u043C\u043D\u0430"
  },
  SinglePageExporter: {
    singlepage: "\u041E\u0434\u043D\u0430 \u0441\u0442\u043E\u0440\u0456\u043D\u043A\u0430"
  },
  MultiPageExporter: {
    multipage: "\u0414\u0435\u043A\u0456\u043B\u044C\u043A\u0430 \u0441\u0442\u043E\u0440\u0456\u043D\u043E\u043A",
    exportingPage: ({ currentPage, totalPages }) => `\u0415\u043A\u0441\u043F\u043E\u0440\u0442 \u0441\u0442\u043E\u0440\u0456\u043D\u043A\u0438 ${currentPage}/${totalPages}`
  },
  MultiPageVerticalExporter: {
    multipagevertical: "\u0414\u0435\u043A\u0456\u043B\u044C\u043A\u0430 \u0441\u0442\u043E\u0440\u0456\u043D\u043E\u043A (\u043A\u043D\u0438\u0436\u043A\u043E\u0432\u0430 \u043E\u0440\u0456\u0454\u043D\u0442\u0430\u0446\u0456\u044F)",
    exportingPage: ({ currentPage, totalPages }) => `\u0415\u043A\u0441\u043F\u043E\u0440\u0442 \u0441\u0442\u043E\u0440\u0456\u043D\u043A\u0438 ${currentPage}/${totalPages}`
  },
  RowExpander: {
    loading: "\u0417\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0435\u043D\u043D\u044F",
    expand: "\u0420\u043E\u0437\u0433\u043E\u0440\u043D\u0443\u0442\u0438",
    collapse: "\u0417\u0433\u043E\u0440\u043D\u0443\u0442\u0438"
  },
  TreeGroup: {
    group: "\u0413\u0440\u0443\u043F\u0443\u0432\u0430\u0442\u0438 \u0437\u0430",
    stopGrouping: "\u0417\u0443\u043F\u0438\u043D\u0438\u0442\u0438 \u0433\u0440\u0443\u043F\u0443\u0432\u0430\u043D\u043D\u044F",
    stopGroupingThisColumn: "\u0420\u043E\u0437\u0433\u0440\u0443\u043F\u0443\u0432\u0430\u0442\u0438 \u0441\u0442\u043E\u0432\u043F\u0435\u0446\u044C"
  }
};
var Uk_default = LocaleHelper.publishLocale(locale);
export {
  Uk_default as default
};
