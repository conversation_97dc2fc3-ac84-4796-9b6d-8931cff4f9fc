import LocaleHelper from "@bryntum/core-thin/lib/localization/LocaleHelper.js";
import "@bryntum/core-thin/lib/localization/Bg.js";
const emptyString = new String();
const locale = {
  localeName: "Bg",
  localeDesc: "\u0411\u044A\u043B\u0433\u0430\u0440\u0441\u043A\u0438",
  localeCode: "bg",
  ColumnPicker: {
    column: "\u041A\u043E\u043B\u043E\u043D\u0430",
    columnsMenu: "\u041A\u043E\u043B\u043E\u043D\u0438",
    hideColumn: "\u0421\u043A\u0440\u0438\u0432\u0430\u043D\u0435 \u043D\u0430 \u043A\u043E\u043B\u043E\u043D\u0430",
    hideColumnShort: "\u0421\u043A\u0440\u0438\u0432\u0430\u043D\u0435",
    newColumns: "\u041D\u043E\u0432\u0430 \u043A\u043E\u043B\u043E\u043D\u0430"
  },
  Filter: {
    applyFilter: "\u041F\u0440\u0438\u043B\u0430\u0433\u0430\u043D\u0435 \u043D\u0430 \u0444\u0438\u043B\u0442\u044A\u0440",
    filter: "\u0424\u0438\u043B\u0442\u0440\u0438",
    editFilter: "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u0430\u043D\u0435 \u043D\u0430 \u0444\u0438\u043B\u0442\u044A\u0440",
    on: "\u0412\u043A\u043B.",
    before: "\u041F\u0440\u0435\u0434\u0438",
    after: "\u0421\u043B\u0435\u0434",
    equals: "\u0420\u0430\u0432\u043D\u043E",
    lessThan: "\u041F\u043E-\u043C\u0430\u043B\u043A\u043E \u043E\u0442",
    moreThan: "\u041F\u043E\u0432\u0435\u0447\u0435 \u043E\u0442",
    removeFilter: "\u041F\u0440\u0435\u043C\u0430\u0445\u0432\u0430\u043D\u0435 \u043D\u0430 \u0444\u0438\u043B\u0442\u044A\u0440",
    disableFilter: "\u0414\u0435\u0430\u043A\u0442\u0438\u0432\u0438\u0440\u0430\u043D\u0435 \u043D\u0430 \u0444\u0438\u043B\u0442\u044A\u0440\u0430"
  },
  FilterBar: {
    enableFilterBar: "\u041F\u043E\u043A\u0430\u0437\u0432\u0430\u043D\u0435 \u043D\u0430 \u043B\u0435\u043D\u0442\u0430\u0442\u0430 \u0441 \u0444\u0438\u043B\u0442\u0440\u0438",
    disableFilterBar: "\u0421\u043A\u0440\u0438\u0432\u0430\u043D\u0435 \u043D\u0430 \u043B\u0435\u043D\u0442\u0430\u0442\u0430 \u0441 \u0444\u0438\u043B\u0442\u0440\u0438"
  },
  Group: {
    group: "\u0413\u0440\u0443\u043F\u0430",
    groupAscending: "\u0412\u044A\u0437\u0445\u043E\u0434\u044F\u0449\u0430 \u0433\u0440\u0443\u043F\u0430",
    groupDescending: "\u041D\u0438\u0437\u0445\u043E\u0434\u044F\u0449 \u0433\u0440\u0443\u043F\u0430",
    groupAscendingShort: "\u0412\u044A\u0437\u0445\u043E\u0434\u044F\u0449",
    groupDescendingShort: "\u041D\u0438\u0437\u0445\u043E\u0434\u044F\u0449",
    stopGrouping: "\u0421\u0442\u043E\u043F \u043D\u0430 \u0433\u0440\u0443\u043F\u0438\u0440\u0430\u043D\u0435",
    stopGroupingShort: "\u0421\u0442\u043E\u043F"
  },
  HeaderMenu: {
    moveBefore: (text) => `\u041F\u0440\u0435\u043C\u0435\u0441\u0442\u0432\u0430\u043D\u0435 \u043F\u0440\u0435\u0434\u0438 "${text}"`,
    moveAfter: (text) => `\u041F\u0440\u0435\u043C\u0435\u0441\u0442\u0432\u0430\u043D\u0435 \u0441\u043B\u0435\u0434 "${text}"`,
    collapseColumn: "\u0421\u0432\u0438\u0432\u0430\u043D\u0435 \u043D\u0430 \u043A\u043E\u043B\u043E\u043D\u0430",
    expandColumn: "\u0420\u0430\u0437\u0448\u0438\u0440\u044F\u0432\u0430\u043D\u0435 \u043D\u0430 \u043A\u043E\u043B\u043E\u043D\u0430"
  },
  ColumnRename: {
    rename: "\u041F\u0440\u0435\u0438\u043C\u0435\u043D\u0443\u0432\u0430\u043D\u0435"
  },
  MergeCells: {
    mergeCells: "\u0421\u043B\u0438\u0432\u0430\u043D\u0435 \u043D\u0430 \u043A\u043B\u0435\u0442\u043A\u0438",
    menuTooltip: "\u0421\u043B\u0438\u0432\u0430\u043D\u0435 \u043D\u0430 \u043A\u043B\u0435\u0442\u043A\u0438 \u0441 \u0435\u0434\u043D\u0430 \u0438 \u0441\u044A\u0449\u0430 \u0441\u0442\u043E\u0439\u043D\u043E\u0441\u0442 \u043F\u0440\u0438 \u0441\u043E\u0440\u0442\u0438\u0440\u0430\u043D\u0435 \u043F\u043E \u0442\u0430\u0437\u0438 \u043A\u043E\u043B\u043E\u043D\u0430"
  },
  Search: {
    searchForValue: "\u0422\u044A\u0440\u0441\u0435\u043D\u0435 \u043D\u0430 \u0441\u0442\u043E\u0439\u043D\u043E\u0441\u0442"
  },
  Sort: {
    sort: "\u0421\u043E\u0440\u0442\u0438\u0440\u0430\u043D\u0435",
    sortAscending: "\u0421\u043E\u0440\u0442\u0438\u0440\u0430\u043D\u0435 \u0432\u044A\u0432 \u0432\u044A\u0437\u0445\u043E\u0434\u044F\u0449 \u0440\u0435\u0434",
    sortDescending: "\u0421\u043E\u0440\u0442\u0438\u0440\u0430\u043D\u0435 \u0432 \u043D\u0438\u0437\u0445\u043E\u0434\u044F\u0449 \u0440\u0435\u0434",
    multiSort: "\u041C\u0443\u043B\u0442\u0438 \u0441\u043E\u0440\u0442\u0438\u0440\u0430\u043D\u0435",
    removeSorter: "\u041F\u0440\u0435\u043C\u0430\u0445\u0432\u0430\u043D\u0435 \u043D\u0430 \u0441\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0447",
    addSortAscending: "\u0414\u043E\u0431\u0430\u0432\u044F\u043D\u0435 \u043D\u0430 \u0432\u044A\u0437\u0445\u043E\u0434\u044F\u0449 \u0441\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0447",
    addSortDescending: "\u0414\u043E\u0431\u0430\u0432\u044F\u043D\u0435 \u043D\u0430 \u043D\u0438\u0437\u0445\u043E\u0434\u044F\u0449 \u0441\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0447",
    toggleSortAscending: "\u041F\u0440\u043E\u043C\u044F\u043D\u0430 \u043A\u044A\u043C \u0432\u044A\u0437\u0445\u043E\u0434\u044F\u0449",
    toggleSortDescending: "\u041F\u0440\u043E\u043C\u044F\u043D\u0430 \u043A\u044A\u043C \u043D\u0438\u0437\u0445\u043E\u0434\u044F\u0449",
    sortAscendingShort: "\u0412\u044A\u0437\u0445\u043E\u0434\u044F\u0449",
    sortDescendingShort: "\u041D\u0438\u0437\u0445\u043E\u0434\u044F\u0449",
    removeSorterShort: "\u041E\u0442\u0441\u0442\u0440\u0430\u043D\u044F\u0432\u0430\u043D\u0435",
    addSortAscendingShort: "+ \u0412\u044A\u0437\u0445\u043E\u0434\u044F\u0449",
    addSortDescendingShort: "+ \u041D\u0438\u0437\u0445\u043E\u0434\u044F\u0449"
  },
  Split: {
    split: "\u0420\u0430\u0437\u0434\u0435\u043B\u0435\u043D\u043E",
    unsplit: "\u041D\u0435\u0440\u0430\u0437\u0434\u0435\u043B\u0435\u043D\u043E",
    horizontally: "\u0425\u043E\u0440\u0438\u0437\u043E\u043D\u0442\u0430\u043B\u043D\u043E",
    vertically: "\u0412\u0435\u0440\u0442\u0438\u043A\u0430\u043B\u043D\u043E",
    both: "\u0418 \u0434\u0432\u0435\u0442\u0435"
  },
  LockRows: {
    lockRow: "\u0417\u0430\u043A\u043B\u044E\u0447\u0432\u0430\u043D\u0435 \u043D\u0430 \u0440\u0435\u0434",
    unlockRow: "\u041E\u0442\u043A\u043B\u044E\u0447\u0432\u0430\u043D\u0435 \u043D\u0430 \u0440\u0435\u0434"
  },
  Column: {
    columnLabel: (column) => `${column.text ? `${column.text} \u043A\u043E\u043B\u043E\u043D\u0430. ` : ""}SPACE \u0437\u0430 \u043A\u043E\u043D\u0442\u0435\u043A\u0441\u0442\u043D\u043E \u043C\u0435\u043D\u044E${column.sortable ? ", ENTER \u0437\u0430 \u0441\u043E\u0440\u0442\u0438\u0440\u0430\u043D\u0435" : ""}`,
    cellLabel: emptyString
  },
  Checkbox: {
    toggleRowSelect: "\u041F\u0440\u0435\u0432\u043A\u043B\u044E\u0447\u0432\u0430\u043D\u0435 \u043D\u0430 \u0438\u0437\u0431\u043E\u0440\u0430 \u043D\u0430 \u0440\u0435\u0434",
    toggleSelection: "\u041F\u0440\u0435\u0432\u043A\u043B\u044E\u0447\u0432\u0430\u043D\u0435 \u043D\u0430 \u0438\u0437\u0431\u043E\u0440\u0430 \u043D\u0430 \u0446\u0435\u043B\u0438\u044F \u043D\u0430\u0431\u043E\u0440 \u043E\u0442 \u0434\u0430\u043D\u043D\u0438"
  },
  RatingColumn: {
    cellLabel: (column) => {
      var _a;
      return `${column.text ? column.text : ""} ${((_a = column.location) == null ? void 0 : _a.record) ? `\u0440\u0435\u0439\u0442\u0438\u043D\u0433 : ${column.location.record.get(column.field) || 0}` : ""}`;
    }
  },
  GridBase: {
    loadFailedMessage: "\u041D\u0435\u0443\u0441\u043F\u0435\u0448\u043D\u043E \u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043D\u0435 \u043D\u0430 \u0434\u0430\u043D\u043D\u0438!",
    syncFailedMessage: "\u041D\u0435\u0443\u0441\u043F\u0435\u0448\u043D\u0430 \u0441\u0438\u043D\u0445\u0440\u043E\u043D\u0438\u0437\u0430\u0446\u0438\u044F \u0437\u0430 \u0434\u0430\u043D\u043D\u0438!",
    unspecifiedFailure: "\u041D\u0435\u0443\u0442\u043E\u0447\u043D\u0435\u043D\u0430 \u043D\u0435\u0438\u0437\u043F\u0440\u0430\u0432\u043D\u043E\u0441\u0442",
    networkFailure: "\u0413\u0440\u0435\u0448\u043A\u0430 \u0432 \u043C\u0440\u0435\u0436\u0430\u0442\u0430",
    parseFailure: "\u041D\u0435\u0443\u0441\u043F\u0435\u0448\u0435\u043D \u0430\u043D\u0430\u043B\u0438\u0437 \u043D\u0430 \u043E\u0442\u0433\u043E\u0432\u043E\u0440\u0430 \u043D\u0430 \u0441\u044A\u0440\u0432\u044A\u0440\u0430",
    serverResponse: "\u041E\u0442\u0433\u043E\u0432\u043E\u0440 \u043D\u0430 \u0441\u044A\u0440\u0432\u044A\u0440\u0430:",
    noRows: "\u041D\u044F\u043C\u0430 \u0437\u0430\u043F\u0438\u0441\u0438 \u0437\u0430 \u043F\u043E\u043A\u0430\u0437\u0432\u0430\u043D\u0435",
    moveColumnLeft: "\u041F\u0440\u0435\u043C\u0435\u0441\u0442\u0432\u0430\u043D\u0435 \u0432 \u043B\u044F\u0432\u0430\u0442\u0430 \u0447\u0430\u0441\u0442",
    moveColumnRight: "\u041F\u0440\u0435\u043C\u0435\u0441\u0442\u0432\u0430\u043D\u0435 \u0432 \u0434\u044F\u0441\u043D\u0430\u0442\u0430 \u0447\u0430\u0441\u0442",
    moveColumnTo: (region) => `\u041F\u0440\u0435\u043C\u0435\u0441\u0442\u0432\u0430\u043D\u0435 \u043D\u0430 \u043A\u043E\u043B\u043E\u043D\u0430\u0442\u0430 \u0432 ${region}`
  },
  CellMenu: {
    removeRow: "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043D\u0435"
  },
  RowCopyPaste: {
    copyRecord: "\u041A\u043E\u043F\u0438\u0440\u0430\u0439",
    cutRecord: "\u0418\u0437\u0440\u0435\u0436\u0438",
    pasteRecord: "\u041F\u043E\u0441\u0442\u0430\u0432\u0438",
    rows: "\u0440\u0435\u0434\u043E\u0432\u0435",
    row: "\u0440\u0435\u0434"
  },
  CellCopyPaste: {
    copy: "\u041A\u043E\u043F\u0438\u0440\u0430\u043D\u0435",
    cut: "\u0418\u0437\u0440\u044F\u0437\u0432\u0430\u043D\u0435",
    paste: "\u0412\u043C\u044A\u043A\u0432\u0430\u043D\u0435"
  },
  PdfExport: {
    "Waiting for response from server": "\u0412 \u043E\u0447\u0430\u043A\u0432\u0430\u043D\u0435 \u043D\u0430 \u043E\u0442\u0433\u043E\u0432\u043E\u0440 \u043E\u0442 \u0441\u044A\u0440\u0432\u044A\u0440\u0430...",
    "Export failed": "\u041D\u0435\u0443\u0441\u043F\u0435\u0448\u0435\u043D \u0435\u043A\u0441\u043F\u043E\u0440\u0442",
    "Server error": "\u0413\u0440\u0435\u0448\u043A\u0430 \u0432 \u0441\u044A\u0440\u0432\u044A\u0440\u0430",
    "Generating pages": "\u0413\u0435\u043D\u0435\u0440\u0438\u0440\u0430\u043D\u0435 \u043D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0438...",
    "Click to abort": "\u041E\u0442\u043A\u0430\u0437"
  },
  ExportDialog: {
    width: "40em",
    labelWidth: "12em",
    exportSettings: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u043D\u0430 \u0435\u043A\u0441\u043F\u043E\u0440\u0442\u0430",
    export: "\u0415\u043A\u0441\u043F\u043E\u0440\u0442",
    printSettings: "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0437\u0430 \u043F\u0435\u0447\u0430\u0442",
    print: "\u041F\u0435\u0447\u0430\u0442",
    exporterType: "\u041A\u043E\u043D\u0442\u0440\u043E\u043B \u043D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0440\u0430\u043D\u0435\u0442\u043E",
    cancel: "\u041E\u0442\u043A\u0430\u0437",
    fileFormat: "\u0424\u0430\u0439\u043B\u043E\u0432 \u0444\u043E\u0440\u043C\u0430\u0442",
    rows: "\u0420\u0435\u0434\u043E\u0432\u0435",
    alignRows: "\u041F\u043E\u0434\u0440\u0430\u0432\u043D\u044F\u0432\u0430\u043D\u0435 \u043D\u0430 \u0440\u0435\u0434\u043E\u0432\u0435\u0442\u0435",
    columns: "\u041A\u043E\u043B\u043E\u043D\u0438",
    paperFormat: "\u0424\u043E\u0440\u043C\u0430\u0442 \u043D\u0430 \u0434\u043E\u043A\u0443\u043C\u0435\u043D\u0442\u0430",
    orientation: "\u041E\u0440\u0438\u0435\u043D\u0442\u0430\u0446\u0438\u044F",
    repeatHeader: "\u041F\u043E\u0432\u0442\u0430\u0440\u044F\u043D\u0435 \u043D\u0430 \u0437\u0430\u0433\u043B\u0430\u0432\u043A\u0430\u0442\u0430"
  },
  ExportRowsCombo: {
    all: "\u0412\u0441\u0438\u0447\u043A\u0438 \u0440\u0435\u0434\u043E\u0432\u0435",
    visible: "\u0412\u0438\u0434\u0438\u043C\u0438 \u0440\u0435\u0434\u043E\u0432\u0435"
  },
  ExportOrientationCombo: {
    portrait: "\u041F\u043E\u0440\u0442\u0440\u0435\u0442",
    landscape: "\u041F\u0435\u0439\u0437\u0430\u0436"
  },
  SinglePageExporter: {
    singlepage: "\u0415\u0434\u0438\u043D\u0438\u0447\u043D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430"
  },
  MultiPageExporter: {
    multipage: "\u041D\u044F\u043A\u043E\u043B\u043A\u043E \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0438",
    exportingPage: ({ currentPage, totalPages }) => `\u0415\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u0430\u043D\u0435 \u043D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430 ${currentPage}/${totalPages}`
  },
  MultiPageVerticalExporter: {
    multipagevertical: "\u041D\u044F\u043A\u043E\u043B\u043A\u043E \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0438 (\u0432\u0435\u0440\u0442\u0438\u043A\u0430\u043B\u043D\u043E)",
    exportingPage: ({ currentPage, totalPages }) => `\u0415\u043A\u0441\u043F\u043E\u0440\u0442\u0438\u0440\u0430\u043D\u0435 \u043D\u0430 \u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430 ${currentPage}/${totalPages}`
  },
  RowExpander: {
    loading: "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043D\u0435",
    expand: "\u0420\u0430\u0437\u0433\u0440\u044A\u0449\u0430\u043D\u0435",
    collapse: "\u0421\u0432\u0438\u0432\u0430\u043D\u0435"
  },
  TreeGroup: {
    group: "\u0413\u0440\u0443\u043F\u0438\u0440\u0430\u043D\u0435 \u043F\u043E",
    stopGrouping: "\u0421\u043F\u0438\u0440\u0430\u043D\u0435 \u043D\u0430 \u0433\u0440\u0443\u043F\u0438\u0440\u0430\u043D\u0435\u0442\u043E",
    stopGroupingThisColumn: "\u0420\u0430\u0437\u0433\u0440\u0443\u043F\u0438\u0440\u0430\u043D\u0435 \u043D\u0430 \u043A\u043E\u043B\u043E\u043D\u0430"
  }
};
var Bg_default = LocaleHelper.publishLocale(locale);
export {
  Bg_default as default
};
