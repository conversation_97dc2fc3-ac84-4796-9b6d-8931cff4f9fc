import LocaleHelper from "@bryntum/core-thin/lib/localization/LocaleHelper.js";
import "@bryntum/core-thin/lib/localization/Cs.js";
const emptyString = new String();
const locale = {
  localeName: "Cs",
  localeDesc: "\u010Cesky",
  localeCode: "cs",
  ColumnPicker: {
    column: "Sloupec",
    columnsMenu: "Sloupce",
    hideColumn: "Sk\xFDrt sloupec",
    hideColumnShort: "Skr\xFDt",
    newColumns: "Nov\xE9 sloupce"
  },
  Filter: {
    applyFilter: "Pou\u017E\xEDt filtr",
    filter: "Filtr",
    editFilter: "Upravit filtr",
    on: "Zap.",
    before: "P\u0159ed",
    after: "Po",
    equals: "Rovn\xE1 se",
    lessThan: "M\xE9n\u011B ne\u017E",
    moreThan: "V\xEDce ne\u017E",
    removeFilter: "Odebrat filtr",
    disableFilter: "Deaktivovat filtr"
  },
  FilterBar: {
    enableFilterBar: "Zobrazit li\u0161tu s filtrem",
    disableFilterBar: "Skr\xFDt li\u0161tu s filtrem"
  },
  Group: {
    group: "Seskupit",
    groupAscending: "Seskupit vzestupn\u011B",
    groupDescending: "Seskupit sestupn\u011B",
    groupAscendingShort: "Vzestupn\u011B",
    groupDescendingShort: "Sestupn\u011B",
    stopGrouping: "Zastavit seskupov\xE1n\xED",
    stopGroupingShort: "Zastavit"
  },
  HeaderMenu: {
    moveBefore: (text) => `Posunout p\u0159ed "${text}"`,
    moveAfter: (text) => `Posunout za "${text}"`,
    collapseColumn: "Slou\u010Dit sloupec",
    expandColumn: "Roz\u0161\xED\u0159it sloupec"
  },
  ColumnRename: {
    rename: "P\u0159ejmenovat"
  },
  MergeCells: {
    mergeCells: "Slou\u010Dit bu\u0148ky",
    menuTooltip: "P\u0159i \u0159azen\xED tohoto sloupce slou\u010Dit bu\u0148ky se stejnou hodnotou"
  },
  Search: {
    searchForValue: "Vyhledat hodnotu"
  },
  Sort: {
    sort: "Se\u0159adit",
    sortAscending: "Se\u0159adit vzestupn\u011B",
    sortDescending: "Se\u0159adit sestupn\u011B",
    multiSort: "Multi \u0159azen\xED",
    removeSorter: "Odebrat \u0159azen\xED",
    addSortAscending: "P\u0159idat vzestupn\xE9 \u0159azen\xED",
    addSortDescending: "P\u0159idat sestupn\xE9 \u0159azen\xED",
    toggleSortAscending: "Zm\u011Bnit na vzestupn\xE9",
    toggleSortDescending: "Zm\u011Bnit na sestupn\xE9",
    sortAscendingShort: "Vzestupn\u011B",
    sortDescendingShort: "Sestupn\u011B",
    removeSorterShort: "Odebrat",
    addSortAscendingShort: "+ vzestupn\u011B",
    addSortDescendingShort: "+ sestupn\u011B"
  },
  Split: {
    split: "Rozd\u011Blit",
    unsplit: "Nerozd\u011Blit",
    horizontally: "Vodorovn\u011B",
    vertically: "Svisle",
    both: "Oboje"
  },
  LockRows: {
    lockRow: "Uzamknout \u0159\xE1dek",
    unlockRow: "Odemknout \u0159\xE1dek"
  },
  Column: {
    columnLabel: (column) => `${column.text ? `${column.text} sloupec. ` : ""}MEZERN\xCDK pro kontextov\xE1 nab\xEDdka${column.sortable ? ", ENTER pro \u0159azen\xED" : ""}`,
    cellLabel: emptyString
  },
  Checkbox: {
    toggleRowSelect: "P\u0159epnout v\xFDb\u011Br \u0159\xE1dku",
    toggleSelection: "P\u0159epnout v\xFDb\u011Br cel\xE9 sady dat"
  },
  RatingColumn: {
    cellLabel: (column) => {
      var _a;
      return `${column.text ? column.text : ""} ${((_a = column.location) == null ? void 0 : _a.record) ? `hodnocen\xED : ${column.location.record.get(column.field) || 0}` : ""}`;
    }
  },
  GridBase: {
    loadFailedMessage: "Nahr\xE1n\xED dat se nezda\u0159ilo!",
    syncFailedMessage: "Synchronizace dat se nezda\u0159ila!",
    unspecifiedFailure: "Nespecifikovan\xE9 selh\xE1n\xED",
    networkFailure: "Chyba s\xEDt\u011B",
    parseFailure: "Nepoda\u0159ilo se analyzovat odezvu serveru",
    serverResponse: "Odezva serveru:",
    noRows: "\u017D\xE1dn\xE9 z\xE1znamy k zobrazen\xED",
    moveColumnLeft: "P\u0159esunout do lev\xE9 \u010D\xE1sti",
    moveColumnRight: "P\u0159esunout do prav\xE9 \u010D\xE1sti",
    moveColumnTo: (region) => `P\u0159esunout sloupec do ${region}`
  },
  CellMenu: {
    removeRow: "Vymazat"
  },
  RowCopyPaste: {
    copyRecord: "Kop\xEDrovat",
    cutRecord: "Vyjmout",
    pasteRecord: "Vlo\u017Eit",
    rows: "\u0159\xE1dky",
    row: "\u0159\xE1dek"
  },
  CellCopyPaste: {
    copy: "Kop\xEDrovat",
    cut: "Vy\u0159\xEDznout",
    paste: "Vlo\u017Eit"
  },
  PdfExport: {
    "Waiting for response from server": "\u010Cek\xE1n\xED na odezvu serveru...",
    "Export failed": "Export se nezda\u0159il",
    "Server error": "Chyba serveru",
    "Generating pages": "Generov\xE1n\xED str\xE1nek...",
    "Click to abort": "Zru\u0161it"
  },
  ExportDialog: {
    width: "40em",
    labelWidth: "12em",
    exportSettings: "Nastaven\xED exportu",
    export: "Export",
    printSettings: "Nastaven\xED tisku",
    print: "Tisk",
    exporterType: "Kontrola str\xE1nkov\xE1n\xED",
    cancel: "Zru\u0161it",
    fileFormat: "Form\xE1t souboru",
    rows: "\u0158\xE1dky",
    alignRows: "Srovnat \u0159\xE1dky",
    columns: "Sloupce",
    paperFormat: "Form\xE1t pap\xEDru",
    orientation: "Orientace",
    repeatHeader: "Opakovat z\xE1hlav\xED"
  },
  ExportRowsCombo: {
    all: "V\u0161echny \u0159\xE1dky",
    visible: "Viditeln\xE9 \u0159\xE1dky"
  },
  ExportOrientationCombo: {
    portrait: "Na v\xFD\u0161ku",
    landscape: "Na \u0161\xED\u0159ku"
  },
  SinglePageExporter: {
    singlepage: "Jedna str\xE1nka"
  },
  MultiPageExporter: {
    multipage: "V\xEDce str\xE1nek",
    exportingPage: ({ currentPage, totalPages }) => `Export str\xE1nky ${currentPage}/${totalPages}`
  },
  MultiPageVerticalExporter: {
    multipagevertical: "V\xEDce str\xE1nek (vertik\xE1ln\u011B) ",
    exportingPage: ({ currentPage, totalPages }) => `Export str\xE1nky ${currentPage}/${totalPages}`
  },
  RowExpander: {
    loading: "Nahr\xE1v\xE1n\xED",
    expand: "Rozbalit",
    collapse: "Sbalit"
  },
  TreeGroup: {
    group: "Seskupit podle",
    stopGrouping: "Ukon\u010Dit seskupov\xE1n\xED",
    stopGroupingThisColumn: "Zru\u0161it seskupen\xED sloupce"
  }
};
var Cs_default = LocaleHelper.publishLocale(locale);
export {
  Cs_default as default
};
