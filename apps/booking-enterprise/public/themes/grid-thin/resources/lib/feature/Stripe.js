var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import InstancePlugin from "@bryntum/core-thin/lib/mixin/InstancePlugin.js";
import GridFeatureManager from "../feature/GridFeatureManager.js";
class Stripe extends InstancePlugin {
  static get pluginConfig() {
    return {
      chain: [
        "beforeRenderRow"
      ]
    };
  }
  doDisable(disable) {
    if (!this.isConfiguring) {
      this.client.refreshRows();
    }
    super.doDisable(disable);
  }
  /**
   * Applies even/odd CSS when row is rendered
   * @param {Grid.row.Row} rowModel
   * @private
   */
  beforeRenderRow({ row, record }) {
    var _a, _b;
    const { enabled } = this, { isGroupHeader } = record, groupParent = this.client.store.isGrouped && record.instanceMeta(this.client.store).groupParent, even = ((_b = (_a = groupParent == null ? void 0 : groupParent.groupChildren) == null ? void 0 : _a.indexOf(record)) != null ? _b : row.dataIndex) % 2 === 0;
    row.assignCls({
      "b-even": enabled && !isGroupHeader && even,
      "b-odd": enabled && !isGroupHeader && !even
    });
  }
}
__publicField(Stripe, "$name", "Stripe");
Stripe._$name = "Stripe";
GridFeatureManager.registerFeature(Stripe);
export {
  Stripe as default
};
