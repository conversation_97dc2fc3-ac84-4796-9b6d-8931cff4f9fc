var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Base from "@bryntum/core-thin/lib/Base.js";
import Rectangle from "@bryntum/core-thin/lib/helper/util/Rectangle.js";
import GridLocation from "../../util/GridLocation.js";
import DomHelper from "@bryntum/core-thin/lib/helper/DomHelper.js";
import GlobalEvents from "@bryntum/core-thin/lib/GlobalEvents.js";
const defaultFocusOptions = Object.freeze({}), containedFocusable = function(e) {
  if (!this.focusableFinderCell.contains(e)) {
    return DomHelper.NodeFilter.FILTER_REJECT;
  }
  if (DomHelper.isFocusable(e) && !e.disabled) {
    return DomHelper.NodeFilter.FILTER_ACCEPT;
  }
  return DomHelper.NodeFilter.FILTER_SKIP;
};
var GridNavigation_default = (Target) => {
  var _a;
  return _a = class extends (Target || Base) {
    static get $name() {
      return "GridNavigation";
    }
    onStoreRecordIdChange(event) {
      var _a2;
      (_a2 = super.onStoreRecordIdChange) == null ? void 0 : _a2.call(this, event);
      const { focusedCell } = this, { oldValue, value } = event;
      if (focusedCell && focusedCell.id === oldValue) {
        focusedCell._id = value;
      }
    }
    /**
     * Called by the RowManager when the row which contains the focus location is derendered.
     *
     * This keeps focus in a consistent place.
     * @protected
     */
    onFocusedRowDerender() {
      const me = this, { focusedCell } = me;
      if ((focusedCell == null ? void 0 : focusedCell.id) != null && focusedCell.cell) {
        const isActive = focusedCell.cell.contains(DomHelper.getActiveElement(me));
        if (me.hideHeaders) {
          if (isActive) {
            me.revertFocus();
          }
        } else {
          const headerContext = me.normalizeCellContext({
            rowIndex: -1,
            columnIndex: isActive ? focusedCell.columnIndex : 0
          });
          if (isActive) {
            me.focusCell(headerContext);
          } else {
            headerContext.cell.tabIndex = 0;
          }
        }
        focusedCell.cell.tabIndex = -1;
      }
    }
    navigateFirstCell() {
      this.focusCell(GridLocation.FIRST_CELL);
    }
    navigateFirstColumn() {
      this.focusCell(GridLocation.FIRST_COLUMN);
    }
    navigateLastCell() {
      this.focusCell(GridLocation.LAST_CELL);
    }
    navigateLastColumn() {
      this.focusCell(GridLocation.LAST_COLUMN);
    }
    navigatePrevPage() {
      this.focusCell(GridLocation.PREV_PAGE);
    }
    navigateNextPage() {
      this.focusCell(GridLocation.NEXT_PAGE);
    }
    activateHeader(keyEvent) {
      var _a2;
      if (keyEvent.target.classList.contains("b-grid-header") && this.focusedCell.isColumnHeader) {
        const { column } = this.focusedCell;
        (_a2 = column.onKeyDown) == null ? void 0 : _a2.call(column, keyEvent);
        this.getHeaderElement(column.id).click();
      }
      return false;
    }
    onEscape(keyEvent) {
      var _a2, _b;
      const me = this, { focusedCell } = me;
      if (!keyEvent.target.closest(".b-dragging") && (focusedCell == null ? void 0 : focusedCell.isActionable)) {
        keyEvent.stopImmediatePropagation();
        me._focusedCell = null;
        me.focusCell({
          rowIndex: focusedCell.rowIndex,
          column: focusedCell.column
        }, {
          disableActionable: true
        });
      } else if (me.isNested && me.owner && !((_b = (_a2 = me.owner).catchFocus) == null ? void 0 : _b.call(_a2, { source: me }))) {
        me.revertFocus(true);
      }
    }
    onTab(keyEvent) {
      const { target } = keyEvent, {
        focusedCell,
        bodyElement
      } = this, {
        isActionable,
        actionTargets = []
      } = focusedCell, isEditable = isActionable && DomHelper.isEditable(target) && !target.readOnly;
      if (isEditable && target === actionTargets[actionTargets.length - 1]) {
        keyEvent.preventDefault();
        this.navigateRight(keyEvent);
      } else if (!isActionable || target === actionTargets[actionTargets.length - 1]) {
        bodyElement.style.display = "none";
        this.requestAnimationFrame(() => bodyElement.style.display = "");
        return false;
      }
    }
    onShiftTab(keyEvent) {
      const me = this, { target } = keyEvent, {
        focusedCell,
        bodyElement
      } = me, {
        cell,
        isActionable,
        actionTargets
      } = focusedCell, isEditable = isActionable && DomHelper.isEditable(target) && !target.readOnly, onFirstCell = focusedCell.columnIndex === 0 && focusedCell.rowIndex === (me.hideHeaders ? 0 : -1);
      if (!onFirstCell && isEditable && target === actionTargets[0]) {
        keyEvent.preventDefault();
        me.navigateLeft(keyEvent);
      } else if (!isActionable || target === actionTargets[0]) {
        const f = !onFirstCell && !me.hideHeaders && me.focusCell({
          rowIndex: -1,
          column: 0
        }, {
          disableActionable: true
        });
        if (f) {
          f.cell.tabIndex = -1;
          cell.tabIndex = 0;
          me._focusedCell = focusedCell;
        } else {
          bodyElement.style.display = "none";
          me.requestAnimationFrame(() => bodyElement.style.display = "");
        }
        return false;
      }
    }
    onSpace(keyEvent) {
      if (!this.focusedCell.isActionable) {
        keyEvent.preventDefault();
      }
      return false;
    }
    //region Cell
    /**
     * Triggered when a user navigates to a grid cell
     * @event navigate
     * @param {Event} [event] The UI event which caused navigation.
     * @param {String} eventName The name of the event
     * @param {Grid.util.GridLocation} focusedCell The currently focused cell
     * @param {Grid.util.GridLocation} lastFocusedCell The previously focused cell
     * @param {Grid.view.Grid} source The grid instance
     * @param {String} type The type of the event
     */
    /**
     * Grid GridLocation which encapsulates the currently focused cell.
     * Set to focus a cell or use {@link #function-focusCell}.
     * @property {Grid.util.GridLocation}
     */
    get focusedCell() {
      return this._focusedCell;
    }
    /**
     * This property is `true` if an element _within_ a cell is focused.
     * @property {Boolean}
     * @readonly
     */
    get isActionableLocation() {
      var _a2;
      return (_a2 = this._focusedCell) == null ? void 0 : _a2.isActionable;
    }
    set focusedCell(cellSelector) {
      this.focusCell(cellSelector);
    }
    get focusedRecord() {
      var _a2;
      return (_a2 = this._focusedCell) == null ? void 0 : _a2.record;
    }
    /**
     * CSS selector for currently focused cell. Format is "[data-index=index] [data-column-id=columnId]".
     * @property {String}
     * @readonly
     */
    get cellCSSSelector() {
      const cell = this._focusedCell;
      return cell ? `[data-index=${cell.rowIndex}] [data-column-id=${cell.columnId}]` : "";
    }
    afterHide() {
      super.afterHide(...arguments);
      this.lastFocusedCell = null;
    }
    /**
     * Checks whether a cell is focused.
     * @param {GridLocationConfig|Grid.util.GridLocation|String|Number} cellSelector Cell selector { id: x, columnId: xx } or row id
     * @returns {Boolean} true if cell or row is focused, otherwise false
     */
    isFocused(cellSelector) {
      var _a2;
      return Boolean((_a2 = this._focusedCell) == null ? void 0 : _a2.equals(this.normalizeCellContext(cellSelector)));
    }
    get focusElement() {
      var _a2;
      if (!this.isDestroying) {
        let focusCell;
        if (this.store.count && this._focusedCell) {
          focusCell = this._focusedCell.target;
        } else if (!this.hideHeaders) {
          focusCell = this.normalizeCellContext({
            rowIndex: -1,
            columnIndex: ((_a2 = this._focusedCell) == null ? void 0 : _a2.columnIndex) || 0
          }).target;
        }
        const superFocusEl = super.focusElement;
        if (superFocusEl && (!focusCell || focusCell.compareDocumentPosition(superFocusEl) === Node.DOCUMENT_POSITION_PRECEDING)) {
          return superFocusEl;
        }
        return focusCell;
      }
    }
    afterInternalPaint({ firstPaint }) {
      var _a2, _b;
      const me = this;
      (_a2 = super.afterInternalPaint) == null ? void 0 : _a2.call(this, ...arguments);
      const defaultFocus = this.normalizeCellContext({
        rowIndex: me.hideHeaders ? 0 : -1,
        column: me.hideHeaders ? 0 : me.columns.find((col) => !col.hidden && col.isFocusable)
      });
      if ((_b = defaultFocus.cell) == null ? void 0 : _b.getAttribute("tabIndex")) {
        defaultFocus._isDefaultFocus = true;
        me._focusedCell = defaultFocus;
        const { target } = defaultFocus;
        if (target === defaultFocus.cell) {
          defaultFocus.cell.tabIndex = 0;
        }
      }
    }
    /**
     * This function handles focus moving into, or within the grid.
     * @param {Event} focusEvent
     * @private
     */
    onGridBodyFocusIn(focusEvent) {
      var _a2, _b, _c, _d;
      const me = this, { bodyElement } = me, lastFocusedCell = me.focusedCell, lastTarget = (lastFocusedCell == null ? void 0 : lastFocusedCell.initialTarget) || (lastFocusedCell == null ? void 0 : lastFocusedCell.target), {
        target,
        relatedTarget
      } = focusEvent, targetCell = target.closest(me.focusableSelector), targetRow = target.closest(".b-grid-row");
      if (targetCell && (!GlobalEvents.currentMouseDown || GlobalEvents.isMouseDown(0) || GlobalEvents.isMouseDown(2))) {
        const cellSelector = new GridLocation(target), { cell } = cellSelector, lastCell = lastFocusedCell == null ? void 0 : lastFocusedCell.cell, actionTargets = cellSelector.actionTargets = me.findFocusables(targetCell), doSelect = (!me._fromFocusCell || me.selectOnFocus) && (target === cell || me._selectActionCell) && !(target == null ? void 0 : target._isRevertingFocus);
        if (targetRow && cellSelector.rowIndex === -1) {
          cell.focus({ preventScroll: true });
          return;
        }
        if (target.matches(me.focusableSelector)) {
          if (me.disableActionable) {
            cellSelector._target = cell;
          } else if (actionTargets.length) {
            const currentMouseDownTarget = (_a2 = GlobalEvents.currentMouseDown) == null ? void 0 : _a2.target;
            if (!actionTargets.some((el) => el.contains(currentMouseDownTarget))) {
              (_b = me.onCellNavigate) == null ? void 0 : _b.call(me, me, lastFocusedCell, cellSelector, doSelect);
              me.trigger("navigate", { lastFocusedCell, focusedCell: cellSelector, event: focusEvent });
            }
            me._selectActionCell = currentMouseDownTarget === target;
            actionTargets[0].focus();
            delete me._selectActionCell;
            return;
          }
        } else {
          if ((lastFocusedCell == null ? void 0 : lastFocusedCell.target) && relatedTarget && (!GlobalEvents.isMouseDown() || !bodyElement.contains((_c = GlobalEvents.currentMouseDown) == null ? void 0 : _c.target)) && !bodyElement.contains(relatedTarget) && !cellSelector.equals(lastFocusedCell)) {
            lastTarget.focus();
            return;
          }
          cellSelector._target = target;
        }
        if (lastCell) {
          lastCell.classList.remove("b-focused");
          lastCell.tabIndex = -1;
        }
        if (cell) {
          cell.classList.add("b-focused");
          cellSelector.column.onCellFocus(cellSelector);
          if (cell === target) {
            cell.tabIndex = 0;
          }
          if (cell.contains(focusEvent.relatedTarget)) {
            if (lastTarget === target) {
              return;
            }
          }
        }
        me._focusedCell = cellSelector;
        (_d = me.onCellNavigate) == null ? void 0 : _d.call(me, me, lastFocusedCell, cellSelector, doSelect);
        me.trigger("navigate", { lastFocusedCell, focusedCell: cellSelector, event: focusEvent });
      } else if (!target.closest(".b-rowexpander-body")) {
        lastTarget == null ? void 0 : lastTarget.focus({ preventScroll: true });
      }
    }
    findFocusables(cell) {
      const { focusableFinder } = this, result = [];
      focusableFinder.currentNode = this.focusableFinderCell = cell;
      for (let focusable = focusableFinder.nextNode(); focusable; focusable = focusableFinder.nextNode()) {
        result.push(focusable);
      }
      return result;
    }
    get focusableFinder() {
      const me = this;
      if (!me._focusableFinder) {
        me._focusableFinder = me.setupTreeWalker(me.bodyElement, DomHelper.NodeFilter.SHOW_ELEMENT, {
          acceptNode: containedFocusable.bind(me)
        });
      }
      return me._focusableFinder;
    }
    /**
     * Sets the passed record as the current focused record for keyboard navigation and selection purposes.
     * This API is used by Combo to activate items in its picker.
     * @param {Core.data.Model|Number|String} activeItem The record, or record index, or record id to highlight as the active ("focused") item.
     * @internal
     */
    restoreActiveItem(item = this._focusedCell) {
      if (this.rowManager.count) {
        if (!isNaN(item)) {
          item = this.store.getAt(item);
        } else if (!item.isModel) {
          item = this.store.getById(item);
        }
        return this.focusCell(item);
      }
    }
    /**
     * Navigates to a cell and/or its row (depending on selectionMode)
     * @param {GridLocationConfig|Grid.util.GridLocation} cellSelector Cell location descriptor
     * @param {Object} options Modifier options for how to deal with focusing the cell. These
     * are used as the {@link Core.helper.util.Scroller#function-scrollTo} options.
     * @param {BryntumScrollOptions|Boolean} [options.scroll=true] Pass `false` to not scroll the cell into view, or a
     * scroll options object to affect the scroll.
     * @returns {Grid.util.GridLocation} A `GridLocation` object representing the focused location.
     * @fires navigate
     */
    focusCell(cellSelector, options = defaultFocusOptions) {
      var _a2, _b, _c, _d;
      const me = this, { _focusedCell } = me, {
        scroll,
        disableActionable
      } = options, isDown = cellSelector === GridLocation.DOWN, isUp = cellSelector === GridLocation.UP, isLeftRightKey = cellSelector === GridLocation.PREV_CELL || cellSelector === GridLocation.NEXT_CELL;
      if ((cellSelector == null ? void 0 : cellSelector.rowIndex) === -1 && me.hideHeaders) {
        me.revertFocus();
        return;
      }
      if (typeof cellSelector === "number" && !(_focusedCell == null ? void 0 : _focusedCell.isLocation)) {
        return;
      }
      cellSelector = typeof cellSelector === "number" && (_focusedCell == null ? void 0 : _focusedCell.isLocation) ? _focusedCell.move(cellSelector) : me.normalizeCellContext(cellSelector);
      const doSelect = "doSelect" in options ? options.doSelect : !cellSelector.isActionable || cellSelector.initialTarget === cellSelector.cell;
      if (cellSelector.equals(_focusedCell)) {
        if (me.isNested && (isDown || isUp)) {
          if (!((_b = (_a2 = me.owner) == null ? void 0 : _a2.catchFocus) == null ? void 0 : _b.call(_a2, { source: me, navigationDirection: isDown ? "down" : "up" }))) {
            me.revertFocus(true);
          }
        } else {
          (_c = me.onCellNavigate) == null ? void 0 : _c.call(me, me, _focusedCell, cellSelector, doSelect);
        }
        return _focusedCell;
      }
      const subGrid = me.getSubGridFromColumn(cellSelector.columnId), { cell } = cellSelector, testCell = cell || me.getCell({
        rowIndex: me.rowManager.topIndex,
        columnId: cellSelector.columnId
      }), subGridRect = Rectangle.from(subGrid.element), bodyRect = Rectangle.from(me.bodyElement), cellRect = Rectangle.from(testCell).moveTo(null, subGridRect.y);
      if (scroll !== false) {
        if (cellSelector.rowIndex === -1) {
          if (isLeftRightKey) {
            me.scrollColumnIntoView(cellSelector.column);
          }
        } else {
          options = Object.assign({}, options, scroll);
          if (cellRect.width > subGridRect.width || cellRect.height > bodyRect.height) {
            options.x = options.y = false;
          } else {
            options.column = cellSelector.columnId;
          }
          me.scrollRowIntoView(cellSelector.id, options);
        }
      }
      if (me._hoveredRow || me.hoveredCell) {
        me.hoveredCell = null;
      }
      me.disableActionable = disableActionable;
      me.selectOnFocus = doSelect;
      me._fromFocusCell = true;
      (_d = cellSelector[disableActionable ? "cell" : "target"]) == null ? void 0 : _d.focus({ preventScroll: true });
      me.disableActionable = me.selectOnFocus = false;
      delete me._fromFocusCell;
      return cellSelector;
    }
    blurCell(cellSelector) {
      const me = this, cell = me.getCell(cellSelector);
      if (cell) {
        cell.classList.remove("b-focused");
      }
    }
    clearFocus(fullClear) {
      const me = this;
      if (me._focusedCell) {
        me.lastFocusedCell = fullClear ? null : me._focusedCell;
        me.blurCell(me._focusedCell);
        me._focusedCell = null;
      }
    }
    // For override-ability
    catchFocus() {
    }
    /**
     * Selects the cell before or after currently focused cell.
     * @private
     * @param next Specify true to select the next cell, false to select the previous
     * @returns {Object} Used cell selector
     */
    internalNextPrevCell(next = true) {
      const me = this, cellSelector = me._focusedCell;
      if (cellSelector) {
        return me.focusCell({
          id: cellSelector.id,
          columnId: me.columns.getAdjacentVisibleLeafColumn(cellSelector.column, next, true).id
        });
      }
      return null;
    }
    /**
     * Select the cell after the currently focused one.
     * @returns {Grid.util.GridLocation} Cell selector
     */
    navigateRight() {
      var _a2;
      if ((_a2 = arguments[0]) == null ? void 0 : _a2.fromKeyMap) {
        return this.focusCell(this.rtl ? GridLocation.PREV_CELL : GridLocation.NEXT_CELL);
      }
      return this.internalNextPrevCell(!this.rtl);
    }
    /**
     * Select the cell before the currently focused one.
     * @returns {Grid.util.GridLocation} Cell selector
     */
    navigateLeft() {
      var _a2;
      if ((_a2 = arguments[0]) == null ? void 0 : _a2.fromKeyMap) {
        return this.focusCell(this.rtl ? GridLocation.NEXT_CELL : GridLocation.PREV_CELL);
      }
      return this.internalNextPrevCell(Boolean(this.rtl));
    }
    //endregion
    //region Row
    /**
     * Selects the next or previous record in relation to the current selection. Scrolls into view if outside.
     * @private
     * @param next Next record (true) or previous (false)
     * @param {Boolean} [skipSpecialRows=true] True to not return specialRows like headers
     * @param {Boolean} [moveToHeader=true] True to allow focus to move to a header
     * @returns {Grid.util.GridLocation|Boolean} Selection context for the focused row (& cell) or false if no selection was made
     */
    internalNextPrevRow(next, skipSpecialRows = true, moveToHeader = true) {
      const me = this, cell = me._focusedCell;
      if (!cell)
        return false;
      const record = me.store[`get${next ? "Next" : "Prev"}`](cell.id, false, skipSpecialRows);
      if (record) {
        return me.focusCell({
          id: record.id,
          columnId: cell.columnId,
          scroll: {
            x: false
          }
        });
      } else if (!next && moveToHeader && !cell.isColumnHeader) {
        this.clearFocus();
        this.getHeaderElement(cell.columnId).focus();
      }
      return false;
    }
    /**
     * Navigates to the cell below the currently focused cell
     * @returns {Grid.util.GridLocation} Selector for focused row (& cell)
     */
    navigateDown() {
      var _a2;
      if ((_a2 = arguments[0]) == null ? void 0 : _a2.fromKeyMap) {
        return this.focusCell(GridLocation.DOWN);
      }
      return this.internalNextPrevRow(true, false);
    }
    /**
     * Navigates to the cell above the currently focused cell
     * @returns {Grid.util.GridLocation} Selector for focused row (& cell)
     */
    navigateUp() {
      var _a2;
      if ((_a2 = arguments[0]) == null ? void 0 : _a2.fromKeyMap) {
        return this.focusCell(GridLocation.UP);
      }
      return this.internalNextPrevRow(false, false);
    }
    //endregion
    // This does not need a className on Widgets.
    // Each *Class* which doesn't need 'b-' + constructor.name.toLowerCase() automatically adding
    // to the Widget it's mixed in to should implement this.
    get widgetClass() {
    }
  }, __publicField(_a, "configurable", {
    focusable: false,
    // Cells, and headers which have a tabIndex are focus targets.
    // If hideHeaders is set, the headers must not have tabIndex set
    focusableSelector: ".b-grid-cell,.b-grid-header.b-depth-0[tabIndex]",
    // Set to `true` to revert focus on Esc or on ArrowUp/ArrowDown above/below first/last row
    isNested: false,
    // Documented on Grid
    keyMap: {
      ArrowUp: { handler: "navigateUp", weight: 10 },
      ArrowRight: { handler: "navigateRight", weight: 10 },
      ArrowDown: { handler: "navigateDown", weight: 10 },
      ArrowLeft: { handler: "navigateLeft", weight: 10 },
      "Ctrl+Home": "navigateFirstCell",
      Home: "navigateFirstColumn",
      "Ctrl+End": "navigateLastCell",
      End: "navigateLastColumn",
      PageUp: "navigatePrevPage",
      PageDown: "navigateNextPage",
      Enter: "activateHeader",
      // Private
      Escape: { handler: "onEscape", weight: 10 },
      "Shift+Tab": { handler: "onShiftTab", preventDefault: false, weight: 200 },
      Tab: { handler: "onTab", preventDefault: false, weight: 200 },
      " ": { handler: "onSpace", preventDefault: false }
    }
  }), _a;
};
export {
  GridNavigation_default as default
};
