var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Base from "@bryntum/core-thin/lib/Base.js";
import AjaxStore from "@bryntum/core-thin/lib/data/AjaxStore.js";
import DomDataStore from "@bryntum/core-thin/lib/data/DomDataStore.js";
import Store from "@bryntum/core-thin/lib/data/Store.js";
import ArrayHelper from "@bryntum/core-thin/lib/helper/ArrayHelper.js";
import BrowserHelper from "@bryntum/core-thin/lib/helper/BrowserHelper.js";
import DomHelper from "@bryntum/core-thin/lib/helper/DomHelper.js";
import EventHelper from "@bryntum/core-thin/lib/helper/EventHelper.js";
import ObjectHelper from "@bryntum/core-thin/lib/helper/ObjectHelper.js";
import Rectangle from "@bryntum/core-thin/lib/helper/util/Rectangle.js";
import VersionHelper from "@bryntum/core-thin/lib/helper/VersionHelper.js";
import ScrollManager from "@bryntum/core-thin/lib/util/ScrollManager.js";
import Mask from "@bryntum/core-thin/lib/widget/Mask.js";
import Panel from "@bryntum/core-thin/lib/widget/Panel.js";
import GlobalEvents from "@bryntum/core-thin/lib/GlobalEvents.js";
import LocaleManager from "@bryntum/core-thin/lib/localization/LocaleManager.js";
import Pluggable from "@bryntum/core-thin/lib/mixin/Pluggable.js";
import State from "@bryntum/core-thin/lib/mixin/State.js";
import ColumnStore, { columnResizeEvent } from "../data/ColumnStore.js";
import GridRowModel from "../data/GridRowModel.js";
import RowManager from "../row/RowManager.js";
import GridScroller from "../util/GridScroller.js";
import GridLocation from "../util/GridLocation.js";
import Header from "./Header.js";
import Footer from "./Footer.js";
import GridElementEvents from "./mixin/GridElementEvents.js";
import GridFeatures from "./mixin/GridFeatures.js";
import GridNavigation from "./mixin/GridNavigation.js";
import GridResponsive from "./mixin/GridResponsive.js";
import GridSelection from "./mixin/GridSelection.js";
import GridState from "./mixin/GridState.js";
import GridSubGrids from "./mixin/GridSubGrids.js";
import LoadMaskable from "@bryntum/core-thin/lib/mixin/LoadMaskable.js";
import Column from "../column/Column.js";
import "../localization/En.js";
const resolvedPromise = new Promise((resolve) => resolve()), storeListenerName = "GridBase:store", defaultScrollOptions = {
  block: "nearest",
  inline: "nearest"
}, datasetReplaceActions = {
  dataset: 1,
  pageLoad: 1,
  filter: 1
}, fullRefreshAfterUpdateFields = {
  hidden: 1,
  mergeCells: 1,
  region: 1
}, emptyArray = Object.freeze([]), ascending = (l, r) => l - r;
class GridBase extends Panel.mixin(
  Pluggable,
  State,
  GridElementEvents,
  GridFeatures,
  GridNavigation,
  GridResponsive,
  GridSelection,
  GridState,
  GridSubGrids,
  LoadMaskable
) {
  constructor() {
    super(...arguments);
    //endregion
    __publicField(this, "_animationSuspendedCounter", 0);
  }
  //region Config
  static get $name() {
    return "GridBase";
  }
  // Factoryable type name
  static get type() {
    return "gridbase";
  }
  static get delayable() {
    return {
      onGridVerticalScroll: {
        type: "raf"
      },
      // These use a shorter delay for tests, see finishConfigure()
      bufferedAfterColumnsResized: 250,
      bufferedElementResize: 250
    };
  }
  static get configurable() {
    return {
      //region Hidden configs
      /**
       * @hideconfigs autoUpdateRecord, defaults, hideWhenEmpty, itemCls, items, layout, layoutStyle, lazyItems,
       * namedItems, record, textContent, defaultAction, html, htmlCls, tag, textAlign, trapFocus, content,
       * defaultBindProperty, defaultFocus, align, anchor, centered, constrainTo, draggable, floating,
       * hideAnimation, positioned, scrollAction, showAnimation, x, y, localeClass, localizableProperties,
       * showTooltipWhenDisabled, tooltip, strictRecordMapping, maximizeOnMobile
       */
      /**
       * @hideproperties html, isSettingValues, isValid, items, record, values, content, layoutStyle, firstItem,
       * lastItem, anchorSize, x, y, layout, strictRecordMapping, visibleChildCount, maximizeOnMobile
       */
      /**
       * @hidefunctions attachTooltip, add, getWidgetById, insert, processWidgetConfig, remove, removeAll, getAt,
       * alignTo, setXY, showBy, showByPoint, toFront
       */
      //endregion
      /**
       * Set to `true` to make the grid read-only, by disabling any UIs for modifying data.
       *
       * __Note that checks MUST always also be applied at the server side.__
       * @prp {Boolean} readOnly
       * @default false
       * @category Misc
       */
      /**
       * Automatically set grids height to fit all rows (no scrolling in the grid). In general you should avoid
       * using `autoHeight: true`, since it will bypass Grids virtual rendering and render all rows at once, which
       * in a larger grid is really bad for performance.
       * @config {Boolean}
       * @default false
       * @category Layout
       */
      autoHeight: null,
      disableAutoHeightWarning: null,
      disableNoIDWarning: null,
      /**
       * Configure this as `true` to allow elements within cells to be styled as `position: sticky`.
       *
       * Columns which contain sticky content will need to be configured with
       *
       * ```javascript
       *    cellCls : 'b-sticky-cell',
       * ```
       *
       * Or a custom renderer can add the class to the passed cell element.
       *
       * It is up to the application author how to style the cell content. It is recommended that
       * a custom renderer create content with CSS class names which the application author
       * will use to apply the `position`, and matching `margin-top` and `top` styles to keep the
       * content stuck at the grid's top.
       *
       * Note that not all browsers support this CSS feature. A cross browser alternative
       * is to use the {link Grid.feature.StickyCells StickyCells} Feature.
       * @config {Boolean}
       * @category Misc
       */
      enableSticky: null,
      /**
       * Set to `true` to allow text selection in the grid cells. Note, this cannot be used simultaneously with the
       * `RowReorder` feature.
       * @config {Boolean}
       * @default false
       * @category Selection
       */
      enableTextSelection: null,
      /**
       * Set to `true` to stretch the last column in a grid with all fixed width columns
       * to fill extra available space if the grid's width is wider than the sum of all
       * configured column widths.
       * @config {Boolean}
       * @default
       * @category Layout
       */
      fillLastColumn: true,
      /**
       * See {@link Grid.view.Grid#keyboard-shortcuts Keyboard shortcuts} for details
       * @config {Object<String,String>} keyMap
       * @category Common
       */
      positionMode: "translate",
      // translate, translate3d, position
      /**
       * Configure as `true` to have the grid show a red "changed" tag in cells who's
       * field value has changed and not yet been committed.
       *
       * Set `showDirty.duringEdit` to `true` to show the red tag while editing a cell
       * ```javascript
       * showDirty : {
       *     duringEdit : true
       * }
       * ```
       *
       * @config {Boolean|Object}
       * @property {Boolean} showDirty.duringEdit Set to `true` to show the red tag while editing a cell
       * @default false
       * @category Misc
       */
      showDirty: null,
      /**
       * An object containing sub grid configuration objects keyed by a `region` property.
       * By default, grid has a 'locked' region (if configured with locked columns) and a 'normal' region.
       * The 'normal' region defaults to use `flex: 1`.
       *
       * This config can be used to reconfigure the "built-in" sub grids or to define your own.
       *
       * Redefining the default regions:
       *
       * {@frameworktabs}
       * {@js}
       * ```javascript
       * new Grid({
       *   subGridConfigs : {
       *     locked : { flex : 1 },
       *     normal : { width : 100 }
       *   }
       * });
       * ```
       * {@endjs}
       * {@react}
       * ```javascript
       * const App = props => {
       *     const subGridConfigs = {
       *         locked : { flex : 1 },
       *         normal : { width : 100 }
       *     };
       *
       *     return <bryntum-grid subGridConfigs={subGridConfigs} />
       * }
       * ```
       * {@endreact}
       * {@vue}
       * ```html
       * <bryntum-grid :sub-grid-configs="subGridConfigs" />
       * ```
       * ```javascript
       * export default {
       *     setup() {
       *         return {
       *             subGridConfigs : [
       *                 locked : { flex : 1 },
       *                 normal : { width : 100 }
       *             ]
       *         };
       *     }
       * }
       * ```
       * {@endvue}
       * {@angular}
       * ```html
       * <bryntum-grid [subGridConfigs]="subGridConfigs"></bryntum-grid>
       * ```
       * ```typescript
       * export class AppComponent {
       *      subGridConfigs = [
       *          locked : { flex : 1 },
       *          normal : { width : 100 }
       *      ]
       *  }
       * ```
       * {@endangular}
       * {@endframeworktabs}
       *
       * Defining your own multi region grid:
       *
       * ```javascript
       * new Grid({
       *   subGridConfigs : {
       *     left   : { width : 100 },
       *     middle : { flex : 1 },
       *     right  : { width  : 100 }
       *   },
       *
       *   columns : [
       *     { field : 'manufacturer', text: 'Manufacturer', region : 'left' },
       *     { field : 'model', text: 'Model', region : 'middle' },
       *     { field : 'year', text: 'Year', region : 'middle' },
       *     { field : 'sales', text: 'Sales', region : 'right' }
       *   ]
       * });
       * ```
       * @config {Object<String,SubGridConfig>}
       * @category Misc
       */
      subGridConfigs: {
        normal: { flex: 1 }
      },
      /**
       * Store that holds records to display in the grid, or a store config object. If the configuration contains
       * a `readUrl`, an `AjaxStore` will be created.
       *
       * Note that a store will be created during initialization if none is specified.
       *
       * Supplying a store config object at initialization time:
       *
       * ```javascript
       * const grid = new Grid({
       *     store : {
       *         fields : ['name', 'powers'],
       *         data   : [
       *             { id : 1, name : 'Aquaman', powers : 'Decent swimmer' },
       *             { id : 2, name : 'Flash', powers : 'Pretty fast' },
       *         ]
       *     }
       * });
       * ```
       *
       * Accessing the store at runtime:
       *
       * ```javascript
       * grid.store.sort('powers');
       * ```
       *
       * @prp {Core.data.Store}
       * @accepts {Core.data.Store|StoreConfig}
       * @typings {Core.data.Store|StoreConfig|Core.data.AjaxStore|AjaxStoreConfig}
       * @category Common
       */
      store: {
        value: {},
        $config: "nullify"
      },
      rowManager: {
        value: {},
        $config: ["nullify", "lazy"]
      },
      /**
       * Configuration values for the {@link Core.util.ScrollManager} class on initialization. Returns the
       * {@link Core.util.ScrollManager} at runtime.
       *
       * @prp {Core.util.ScrollManager}
       * @accepts {ScrollManagerConfig|Core.util.ScrollManager}
       * @readonly
       * @category Scrolling
       */
      scrollManager: {
        value: {},
        $config: ["nullify", "lazy"]
      },
      /**
       * Accepts column definitions for the grid during initialization. They will be used to create
       * {@link @bryntum/grid-thin/lib/column/Column} instances that are added to a {@link @bryntum/grid-thin/lib/data/ColumnStore}.
       *
       * At runtime it returns the {@link @bryntum/grid-thin/lib/data/ColumnStore}.
       *
       * Initialization using column config objects:
       *
       * ```javascript
       * new Grid({
       *   columns : [
       *     { text : 'Alias', field : 'alias' },
       *     { text : 'Superpower', field : 'power' }
       *   ]
       * });
       * ```
       *
       * Also accepts a store config object:
       *
       * ```javascript
       * new Grid({
       *   columns : {
       *     data : [
       *       { text : 'Alias', field : 'alias' },
       *       { text : 'Superpower', field : 'power' }
       *     ],
       *     listeners : {
       *       update() {
       *         // Some update happened
       *       }
       *     }
       *   }
       * });
       * ```
       *
       * Access the {@link @bryntum/grid-thin/lib/data/ColumnStore} at runtime to manipulate columns:
       *
       * ```javascript
       * grid.columns.add({ field : 'column', text : 'New column' });
       * ```
       *
       * Replacing the columns fully at runtime:
       *
       * ```javascript
       * grid.columns = [{ field : 'column', text : 'New column' }];
       * ```
       * @prp {Grid.data.ColumnStore}
       * @accepts {Grid.data.ColumnStore|GridColumnConfig[]|ColumnStoreConfig}
       * @category Common
       */
      columns: {
        value: [],
        $config: "nullify"
      },
      /**
       * Grid's `min-height`. Defaults to `10em` to be sure that the Grid always has a height wherever it is
       * inserted.
       *
       * Can be either a String or a Number (which will have 'px' appended).
       *
       * Note that _reading_ the value will return the numeric value in pixels.
       *
       * @config {String|Number}
       * @category Layout
       */
      minHeight: "10em",
      /**
       * Set to `true` to hide the column header elements
       * @prp {Boolean}
       * @default false
       * @category Misc
       */
      hideHeaders: null,
      /**
       * Set to `true` to hide the footer elements
       * @prp {Boolean}
       * @default
       * @category Misc
       */
      hideFooters: true,
      /**
       * Set to `true` to hide the Grid's horizontal scrollbar(s)
       * @config {Boolean}
       * @default false
       * @category Misc
       */
      hideHorizontalScrollbar: null,
      contentElMutationObserver: false,
      trapFocus: false,
      ariaElement: "bodyElement",
      cellTabIndex: -1,
      rowCls: {
        value: "b-grid-row",
        $config: {
          merge: this.mergeCls
        }
      },
      cellCls: {
        value: "b-grid-cell",
        $config: {
          merge: this.mergeCls
        }
      },
      /**
       * Text or HTML, or a {@link DomConfig} block to display when there is no data to display in the grid
       * @prp {String|DomConfig}
       * @default
       * @category Common
       */
      emptyText: "L{noRows}",
      sortFeatureStore: "$store",
      /**
       * Row height in pixels. This allows the default height for rows to be controlled. Note that it may be
       * overriden by specifying a {@link @bryntum/grid-thin/lib/data/GridRowModel#field-rowHeight} on a per record basis, or from
       * a column {@link @bryntum/grid-thin/lib/column/Column#config-renderer}.
       *
       * When initially configured as `null`, an empty row will be measured and its height will be used as default
       * row height, enabling it to be controlled using CSS
       *
       * @prp {Number}
       * @category Common
       */
      rowHeight: null,
      /**
       * Preserve the grid's vertical scroll position when changesets are applied, as in the case of remote
       * changes, or when stores are configured with {@link Core.data.Store#config-syncDataOnLoad}.
       *
       * @prp {PreserveScrollOptions|Boolean}
       * @default
       * @category Common
       */
      preserveScroll: false,
      /**
       * When the {@link Grid.feature.Tree} feature is in use and the Store is a tree store, this
       * config may be set to `true` to visually animate branch node expand and collapse operations.
       * {@note}This is not supported in Scheduler and Gantt{/@note}
       * @prp {Boolean}
       * @default true
       * @category Tree
       */
      animateTreeNodeToggle: !VersionHelper.isTestEnv,
      /**
       * Set to `false` to not show column lines. End result might be overruled by/differ between themes.
       *
       * @prp {Boolean}
       * @default
       * @category Misc
       */
      columnLines: true,
      /**
       * Set to `false` to not show row lines. End result might be overruled by/differ between themes.
       *
       * @prp {Boolean}
       * @default
       * @category Misc
       */
      rowLines: true,
      /**
       * Use fixed row height. Setting this to `true` will configure the underlying RowManager to use fixed row
       * height, which sacrifices the ability to use rows with variable height to gain a fraction better
       * performance.
       *
       * Using this setting also ignores the {@link Grid.view.GridBase#config-getRowHeight} function, and thus any
       * row height set in data. Only Grids configured {@link Grid.view.GridBase#config-rowHeight} is used.
       *
       * @config {Boolean}
       * @category Layout
       */
      fixedRowHeight: null
    };
  }
  // Default settings, applied in grids constructor.
  static get defaultConfig() {
    return {
      /**
       * A function called for each row to determine its height. It is passed a {@link Core.data.Model record} and
       * expected to return the desired height of that records row. If the function returns a falsy value, Grids
       * configured {@link Grid.view.GridBase#config-rowHeight} is used.
       *
       * The default implementation of this function returns the row height from the records
       * {@link Grid.data.GridRowModel#field-rowHeight rowHeight field}.
       *
       * Override this function to take control over how row heights are determined:
       *
       * ```javascript
       * new Grid({
       *    getRowHeight(record) {
       *        if (record.low) {
       *            return 20;
       *        }
       *        else if (record.high) {
       *            return 60;
       *        }
       *
       *        // Will use grids configured rowHeight
       *        return null;
       *    }
       * });
       * ```
       *
       * NOTE: Height set in a Column renderer takes precedence over the height returned by this function.
       *
       * @config {Function} getRowHeight
       * @param {Core.data.Model} getRowHeight.record Record to determine row height for
       * @returns {Number} Desired row height
       * @category Layout
       */
      // used if no rowHeight specified and none found in CSS. not public since our themes have row height
      // specified and this is more of an internal failsafe
      defaultRowHeight: 45,
      /**
       * Refresh entire row when a record changes (`true`) or, if possible, only the cells affected (`false`).
       *
       * When this is set to `false`, then if a column uses a renderer, cells in that column will still
       * be updated because it is impossible to know whether the cells value will be affected.
       *
       * If a standard, provided Column class is used with no custom renderer, its cells will only be updated
       * if the column's {@link Grid.column.Column#config-field} is changed.
       * @config {Boolean}
       * @default
       * @category Misc
       */
      fullRowRefresh: true,
      /**
       * Specify `true` to preserve vertical scroll position after store actions that trigger a `refresh` event,
       * such as loading new data and filtering.
       * @config {Boolean}
       * @default false
       * @category Misc
       */
      preserveScrollOnDatasetChange: null,
      /**
       * True to preserve focused cell after loading new data
       * @config {Boolean}
       * @default
       * @category Misc
       */
      preserveFocusOnDatasetChange: true,
      /**
       * Convenient shortcut to set data in grids store both during initialization and at runtime. Can also be
       * used to retrieve data at runtime, although we do recommend interacting with Grids store instead using
       * the {@link #property-store} property.
       *
       * Setting initial data during initialization:
       *
       * ```javascript
       * const grid = new Grid({
       *     data : [
       *       { id : 1, name : 'Batman' },
       *       { id : 2, name : 'Robin' },
       *       ...
       *     ]
       * });
       * ```
       *
       * Setting data at runtime:
       *
       * ```javascript
       * grid.data = [
       *     { id : 3, name : 'Joker' },
       *     ...
       * ];
       * ```
       *
       * Getting data at runtime:
       *
       * ```javascript
       * const records = store.data;
       * ```
       *
       * Note that a Store will be created during initialization if none is specified.
       *
       * @prp {Core.data.Model[]}
       * @accepts {Object[]|Core.data.Model[]}
       * @non-lazy-load
       * @category Common
       */
      data: null,
      /**
       * Region to which columns are added when they have none specified
       * @config {String}
       * @default
       * @category Misc
       */
      defaultRegion: "normal",
      /**
       * Set to `true` to destroy the store when the grid is destroyed.
       * @config {Boolean}
       * @default false
       * @category Misc
       */
      destroyStore: null,
      /**
       * Grids change the `maskDefaults` to cover only their `body` element.
       * @config {MaskConfig}
       * @category Misc
       */
      maskDefaults: {
        cover: "body",
        target: "element"
      },
      /**
       * Set to `false` to only measure cell contents when double-clicking the edge between column headers.
       * @config {Boolean}
       * @default
       * @category Layout
       */
      resizeToFitIncludesHeader: true,
      /**
       * Set to `false` to prevent remove row animation and remove the delay related to that.
       * @config {Boolean}
       * @default
       * @category Misc
       */
      animateRemovingRows: true,
      /**
       * Set to `true` to not get a warning when using another base class than GridRowModel for your grid data. If
       * you do, and would like to use the full feature set of the grid then include the fields from GridRowModel
       * in your model definition.
       * @config {Boolean}
       * @default false
       * @category Misc
       */
      disableGridRowModelWarning: null,
      /**
       * Set to `true` to not get a warning when calling {@link #function-getState} when there is a column
       * configured without an `id`. But the recommended action is to always configure columns with an `id` when
       * using states.
       * @config {Boolean}
       * @default false
       * @category State
       */
      disableGridColumnIdWarning: null,
      headerClass: Header,
      footerClass: Footer,
      testPerformance: false,
      rowScrollMode: "move",
      // move, dom, all
      /**
       * Grid monitors window resize by default.
       * @config {Boolean}
       * @default true
       * @category Misc
       */
      monitorResize: true,
      /**
       * An object containing Feature configuration objects (or `true` if no configuration is required)
       * keyed by the Feature class name in all lowercase.
       * @config {Object}
       * @category Common
       */
      features: true,
      /**
       * Configures whether the grid is scrollable in the `Y` axis. This is used to configure a {@link Core.helper.util.Scroller}.
       * See the {@link #config-scrollerClass} config option.
       * @config {Boolean|ScrollerConfig|Core.helper.util.Scroller}
       * @category Scrolling
       */
      scrollable: {
        // Just Y for now until we implement a special grid.view.Scroller subclass
        // Which handles the X scrolling of subgrids.
        overflowY: true
      },
      /**
       * The class to instantiate to use as the {@link #config-scrollable}. Defaults to {@link Core.helper.util.Scroller}.
       * @config {Core.helper.util.Scroller}
       * @typings {typeof Scroller}
       * @category Scrolling
       */
      scrollerClass: GridScroller,
      refreshSuspended: 0,
      /**
       * Animation transition duration in milliseconds.
       * @prp {Number}
       * @default
       * @category Misc
       */
      transitionDuration: 500,
      /**
       * Event which is used to show context menus.
       * Available options are: 'contextmenu', 'click', 'dblclick'.
       * @config {'contextmenu'|'click'|'dblclick'}
       * @category Misc
       * @default
       */
      contextMenuTriggerEvent: "contextmenu",
      localizableProperties: ["emptyText"],
      asyncEventSuffix: "",
      fixElementHeightsBuffer: 350,
      testConfig: {
        transitionDuration: 50,
        fixElementHeightsBuffer: 50
      }
    };
  }
  static get properties() {
    return {
      _selectedRecords: [],
      _verticalScrollHeight: 0,
      virtualScrollHeight: 0,
      _scrollTop: null
    };
  }
  // Keep this commented out to have easy access to the syntax next time we need to use it
  // static get deprecatedEvents() {
  //     return {
  //         cellContextMenuBeforeShow : {
  //             product            : 'Grid',
  //             invalidAsOfVersion : '5.0.0',
  //             message            : '`cellContextMenuBeforeShow` event is deprecated, in favor of `cellMenuBeforeShow` event. Please see https:@bryntum/grid-thin/lib/upgrades/4.0.0 for more information.'
  //         }
  //     };
  // }
  //endregion
  //region Init-destroy
  finishConfigure(config) {
    const me = this, { initScroll } = me;
    me.initScroll = () => !me.scrollInitialized && initScroll.call(me);
    if (VersionHelper.isTestEnv) {
      me.bufferedAfterColumnsResized.delay = 50;
      me.bufferedElementResize.delay = 50;
    }
    super.finishConfigure(config);
    LocaleManager.ion({
      locale: "onBeforeLocaleChange",
      prio: 1,
      thisObj: me
    });
    LocaleManager.ion({
      locale: "onLocaleChange",
      prio: -1,
      thisObj: me
    });
    GlobalEvents.ion({
      theme: "onThemeChange",
      thisObj: me
    });
    me.ion({
      subGridExpand: "internalOnSubGridExpand",
      prio: -1,
      thisObj: me
    });
    me.bufferedFixElementHeights = me.buffer("fixElementHeights", me.fixElementHeightsBuffer, me);
    me.setGridClassList(me.element.classList);
    me.verticalScroller.classList.remove("b-content-element", "b-auto-container");
    me.bodyWrapElement.classList.remove("b-auto-container-panel");
  }
  internalOnSubGridExpand() {
    this.renderContents();
  }
  onBeforeLocaleChange() {
    this._suspendRenderContentsOnColumnsChanged = true;
  }
  onLocaleChange() {
    this._suspendRenderContentsOnColumnsChanged = false;
    if (this.isPainted) {
      this.renderContents();
    }
  }
  finalizeInit() {
    super.finalizeInit();
    if (this.store.isLoading) {
      this.onStoreBeforeRequest();
    }
  }
  changeScrollManager(scrollManager, oldScrollManager) {
    oldScrollManager == null ? void 0 : oldScrollManager.destroy();
    if (scrollManager) {
      return ScrollManager.new({
        element: this.element,
        owner: this
      }, scrollManager);
    } else {
      return null;
    }
  }
  /**
   * Cleanup
   * @private
   */
  doDestroy() {
    var _a, _b;
    const me = this;
    me.detachListeners(storeListenerName);
    (_a = me.scrollManager) == null ? void 0 : _a.destroy();
    for (const feature of Object.values(me.features)) {
      (_b = feature.destroy) == null ? void 0 : _b.call(feature);
    }
    me._focusedCell = null;
    me.columns.destroy();
    super.doDestroy();
  }
  /**
   * Adds extra classes to the Grid element after it's been configured.
   * Also iterates through features, thus ensuring they have been initialized.
   * @private
   */
  setGridClassList(classList) {
    const me = this;
    Object.values(me.features).forEach((feature) => {
      if (feature.disabled || feature === false) {
        return;
      }
      let featureClass;
      if (Object.prototype.hasOwnProperty.call(feature.constructor, "featureClass")) {
        featureClass = feature.constructor.featureClass;
      } else {
        featureClass = `b-${feature instanceof Base ? feature.$$name : feature.constructor.name}`;
      }
      if (featureClass) {
        classList.add(featureClass.toLowerCase());
      }
    });
  }
  //endregion
  // region Feature events
  // For documentation & typings purposes
  /**
   * Fires after a sub grid is collapsed.
   * @event subGridCollapse
   * @param {Grid.view.Grid} source The firing Grid instance
   * @param {Grid.view.SubGrid} subGrid The sub grid instance
   */
  /**
   * Fires after a sub grid is expanded.
   * @event subGridExpand
   * @param {Grid.view.Grid} source The firing Grid instance
   * @param {Grid.view.SubGrid} subGrid The sub grid instance
   */
  /**
   * Fires before a row is rendered.
   * @event beforeRenderRow
   * @param {Grid.view.Grid} source The firing Grid instance.
   * @param {Grid.row.Row} row The row about to be rendered.
   * @param {Core.data.Model} record The record for the row.
   * @param {Number} recordIndex The zero-based index of the record.
   */
  /**
   * Fires after a row is rendered.
   * @event renderRow
   * @param {Grid.view.Grid} source The firing Grid instance.
   * @param {Grid.row.Row} row The row that has been rendered.
   * @param {Core.data.Model} record The record for the row.
   * @param {Number} recordIndex The zero-based index of the record.
   */
  //endregion
  //region Grid template & elements
  compose() {
    const { autoHeight, enableSticky, enableTextSelection, fillLastColumn, positionMode, showDirty } = this;
    return {
      class: {
        [`b-grid-${positionMode}`]: 1,
        "b-enable-sticky": enableSticky,
        "b-grid-notextselection": !enableTextSelection,
        "b-autoheight": autoHeight,
        "b-fill-last-column": fillLastColumn,
        "b-show-dirty": showDirty,
        "b-show-dirty-during-edit": showDirty == null ? void 0 : showDirty.duringEdit
      }
    };
  }
  async recomposeInternal() {
    var _a;
    let scrollState;
    const { isConstructing } = this;
    if (!isConstructing) {
      scrollState = this.storeScroll();
    }
    await super.recomposeInternal();
    if (!isConstructing) {
      const oldValues = [...Object.keys(scrollState.scrollLeft).map((k) => scrollState.scrollLeft[k]), scrollState.scrollTop];
      if (Math.max(...oldValues)) {
        const newState = this.storeScroll();
        if (!Math.max([...Object.keys(newState.scrollLeft).map((k) => newState.scrollLeft[k]), newState.scrollTop])) {
          (_a = this.restoreScroll) == null ? void 0 : _a.call(this, scrollState);
        }
      }
    }
  }
  get cellCls() {
    const { _cellCls } = this;
    return _cellCls.value || _cellCls;
  }
  get bodyConfig() {
    const { autoHeight, hideFooters, hideHeaders } = this;
    return {
      reference: "bodyElement",
      className: {
        "b-autoheight": autoHeight,
        "b-grid-panel-body": 1
      },
      // Only include aria-labelled-by if we have a header
      [this.hasHeader ? "ariaLabelledBy" : ""]: `${this.id}-panel-title`,
      children: {
        headerContainer: {
          tag: "header",
          role: "row",
          "aria-rowindex": 1,
          className: {
            "b-grid-header-container": 1,
            "b-hidden": hideHeaders
          }
        },
        bodyContainer: {
          className: "b-grid-body-container",
          tabIndex: -1,
          // Explicitly needs this because it's in theory focusable
          // and DomSync won't add a default role
          role: "presentation",
          children: {
            verticalScroller: {
              className: "b-grid-vertical-scroller"
            }
          }
        },
        virtualScrollers: {
          className: "b-virtual-scrollers b-hide-display"
        },
        footerContainer: {
          tag: "footer",
          className: {
            "b-grid-footer-container": 1,
            "b-hidden": hideFooters
          }
        }
      }
    };
  }
  get contentElement() {
    return this.verticalScroller;
  }
  get overflowElement() {
    return this.bodyContainer;
  }
  updateHideHeaders(hide) {
    var _a;
    hide = Boolean(hide);
    (_a = this.headerContainer) == null ? void 0 : _a.classList.toggle("b-hidden", hide);
    this.eachSubGrid((subGrid) => subGrid.toggleHeaders(hide));
  }
  updateHideFooters(hide) {
    var _a;
    hide = Boolean(hide);
    (_a = this.footerContainer) == null ? void 0 : _a.classList.toggle("b-hidden", hide);
    this.eachSubGrid((subGrid) => {
      subGrid.scrollable[hide ? "removePartner" : "addPartner"](subGrid.footer.scrollable, "x");
    });
  }
  updateHideHorizontalScrollbar(hide) {
    hide = Boolean(hide);
    this.eachSubGrid((subGrid) => {
      subGrid.virtualScrollerElement.classList.toggle("b-hide-display", hide);
      subGrid.scrollable[hide ? "removePartner" : "addPartner"](subGrid.fakeScroller, "x");
      if (!hide) {
        subGrid.refreshFakeScroll();
      }
    });
  }
  //endregion
  //region Columns
  changeColumns(columns, currentStore) {
    const me = this;
    if (!columns && currentStore) {
      if (me.isDestroying) {
        currentStore.owner === me && currentStore.destroy();
      } else {
        currentStore.removeAll();
      }
      return currentStore;
    }
    if (columns.isStore) {
      (currentStore == null ? void 0 : currentStore.owner) === me && currentStore.destroy();
      columns.grid = me;
      return columns;
    }
    if (Array.isArray(columns)) {
      if (currentStore) {
        const columnsBefore = currentStore.allRecords.slice();
        currentStore.data = columns;
        for (const oldColumn of columnsBefore) {
          if (!currentStore.includes(oldColumn)) {
            oldColumn.destroy();
          }
        }
        return currentStore;
      }
      columns = { data: columns };
    }
    if (currentStore) {
      throw new Error("Replacing ColumnStore is not supported");
    }
    return ColumnStore.new({
      grid: me,
      owner: me
    }, columns);
  }
  updateColumns(columns, was) {
    var _a, _b;
    const me = this;
    (_a = super.updateColumns) == null ? void 0 : _a.call(this, columns, was);
    columns.ion({
      refresh: me.onColumnsChanged,
      sort: me.onColumnsChanged,
      change: me.onColumnsChanged,
      move: me.onColumnsChanged,
      thisObj: me
    });
    columns.ion(columnResizeEvent(me.onColumnsResized, me));
    if (BrowserHelper.isTouchDevice) {
      me.touch = true;
      columns.forEach((column) => {
        const { touchConfig } = column;
        if (touchConfig) {
          column.applyState(touchConfig);
        }
      });
    }
    (_b = me.bodyElement) == null ? void 0 : _b.setAttribute("aria-colcount", columns.visibleColumns.length);
  }
  onColumnsChanged({ type, action, changes, record: column, records: changedColumns, isMove }) {
    var _a;
    const me = this, {
      columns,
      checkboxSelectionColumn
    } = me, changedFields = changes && Object.keys(changes), isSingleFieldChange = (changedFields == null ? void 0 : changedFields.length) === 1;
    isMove = isMove === true ? true : isMove && Object.values(isMove).some((field) => field);
    me.beforeColumnsChange({ type, action, changes, column, columns: changedColumns });
    if (isMove || type === "refresh" && action !== "batch" && action !== "sort" || // Ignore the update of parentIndex following a column move (we redraw on the insert)
    action === "update" && isSingleFieldChange && "parentIndex" in changes || // Ignore sort caused by sync, will refresh on the batch instead
    action === "sort" && columns.isSyncingDataOnLoad) {
      return;
    }
    const addingColumnToNonExistingSubGrid = action === "add" && changedColumns.some((col) => col.region && !me.subGrids[col.region]);
    if (me.isConfiguring || !addingColumnToNonExistingSubGrid && (!me.isPainted || isMove && action === "remove")) {
      return;
    }
    if (action === "add") {
      for (const column2 of changedColumns) {
        const { region } = column2;
        if (!me.subGrids[region]) {
          me.add(me.createSubGrid(region, (_a = me.subGridConfigs) == null ? void 0 : _a[region]));
        }
      }
    } else if (action === "update") {
      const changedField = changedFields[0];
      if (("width" in changes || "minWidth" in changes || "maxWidth" in changes || "flex" in changes) && !("region" in changes)) {
        const { region } = column;
        columns.visibleColumns.forEach((col) => {
          if (col.region === region && col.repaintOnResize) {
            me.refreshColumn(col);
          }
        });
        me.afterColumnsChange({ type, action, changes, column });
        return;
      }
      if ("text" in changes && isSingleFieldChange) {
        column.subGrid.refreshHeader();
        return;
      }
      if ("hidden" in changes) {
        const subGrid = me.getSubGridFromColumn(column.id);
        subGrid.header.fixHeaderWidths();
        subGrid.footer.fixFooterWidths();
        subGrid.updateHasFlex();
      } else if (isSingleFieldChange && !(changedField in fullRefreshAfterUpdateFields) && !column.hidden && !column.subGrid.collapsed) {
        column.subGrid.refreshHeader();
        me.refreshColumn(column);
        return;
      }
    } else if (action === "remove") {
      changedColumns.forEach((c) => {
        var _a2;
        return (_a2 = c.freeColumnWidgets) == null ? void 0 : _a2.call(c);
      });
    }
    if (action === "dataset" || action === "batch" || action === "update" && "region" in changes) {
      const regions = columns.getDistinctValues("region", true), { toRemove, toAdd } = ArrayHelper.delta(regions, me.regions, true);
      me.remove(toRemove.map((region) => me.getSubGrid(region)));
      me.add(toAdd.map((region) => me.createSubGrid(region, me.subGridConfigs[region])));
    }
    if (checkboxSelectionColumn && !columns.includes(checkboxSelectionColumn)) {
      const insertIndex = columns.indexOf(columns.findRecord("type", "rownumber")) + 1;
      columns.insert(insertIndex, checkboxSelectionColumn, true);
    }
    if (!me._suspendRenderContentsOnColumnsChanged) {
      me.renderContents();
    }
    me.syncFlexedSubCols();
    me.bodyElement.setAttribute("aria-colcount", columns.visibleColumns.length);
    me.afterColumnsChange({ type, action, changes, column, columns: changedColumns });
  }
  onColumnsResized({ changes, record: column }) {
    var _a;
    const me = this;
    if (me.isConfiguring) {
      return;
    }
    const domWidth = DomHelper.setLength(column.width), domMinWidth = DomHelper.setLength(column.minWidth), domMaxWidth = DomHelper.setLength(column.maxWidth), subGrid = me.getSubGridFromColumn(column.id);
    subGrid.header.fixHeaderWidths();
    subGrid.footer.fixFooterWidths();
    subGrid.updateHasFlex();
    if (!(column.flex && column.childLevel)) {
      if (!me.cellEls || column !== me.lastColumnResized) {
        me.cellEls = DomHelper.children(
          me.element,
          `.b-grid-cell[data-column-id="${column.id}"]`
        );
        me.lastColumnResized = column;
      }
      for (const cell of me.cellEls) {
        if ("width" in changes) {
          cell.style.width = domWidth;
        }
        if ("minWidth" in changes) {
          cell.style.minWidth = domMinWidth;
        }
        if ("maxWidth" in changes) {
          cell.style.maxWidth = domMaxWidth;
        }
        if ("flex" in changes) {
          cell.style.flex = (_a = column.flex) != null ? _a : null;
        }
      }
    }
    if (!me.resizingColumns) {
      me.afterColumnsResized(column);
    }
    me.syncFlexedSubCols();
  }
  afterColumnsResized(column) {
    const me = this;
    me.eachSubGrid((subGrid) => {
      if (!subGrid.collapsed && (!column || column.region === subGrid.region)) {
        subGrid.fixWidths();
        subGrid.fixRowWidthsInSafariEdge();
      }
    });
    me.lastColumnResized = me.cellEls = null;
    me.bufferedAfterColumnsResized(column);
    me.onHeightChange();
  }
  syncFlexedSubCols() {
    const flexedSubCols = this.columns.query((c) => c.flex && c.childLevel && c.element);
    if (flexedSubCols) {
      for (const column of flexedSubCols) {
        const width = column.element.getBoundingClientRect().width, cellEls = DomHelper.children(
          this.element,
          `.b-grid-cell[data-column-id="${column.id}"]`
        );
        for (const cell of cellEls) {
          cell.style.flex = `0 0 ${width}px`;
        }
      }
    }
  }
  bufferedAfterColumnsResized(column) {
    if (this.columns.usesAutoHeight) {
      this.refreshRows();
    }
    this.refreshVirtualScrollbars();
    this.eachSubGrid((subGrid) => {
      if (!subGrid.collapsed && (!column || column.region === subGrid.region)) {
        subGrid.refreshFakeScroll();
      }
    });
  }
  bufferedElementResize() {
    this.refreshRows();
  }
  onInternalResize(element, newWidth, newHeight, oldWidth, oldHeight) {
    if (DomHelper.scrollBarWidth && newWidth < oldWidth) {
      this.eachSubGrid((subGrid) => {
        if (subGrid.flex) {
          subGrid.onElementResize(subGrid.element);
        }
      });
    }
    super.onInternalResize(...arguments);
    if (this.isPainted && newWidth !== oldWidth && this.columns.usesFlexAutoHeight) {
      this.bufferedElementResize();
    }
  }
  //endregion
  //region Rows
  /**
   * Get the topmost visible grid row
   * @member {Grid.row.Row} firstVisibleRow
   * @readonly
   * @category Rows
   */
  /**
   * Get the last visible grid row
   * @member {Grid.row.Row} lastVisibleRow
   * @readonly
   * @category Rows
   */
  /**
   * Get the Row that is currently displayed at top.
   * @member {Grid.row.Row} topRow
   * @readonly
   * @category Rows
   * @private
   */
  /**
   * Get the Row currently displayed furthest down.
   * @member {Grid.row.Row} bottomRow
   * @readonly
   * @category Rows
   * @private
   */
  /**
   * Get Row for specified record id.
   * @function getRowById
   * @param {Core.data.Model|String|Number} recordOrId Record id (or a record)
   * @returns {Grid.row.Row} Found Row or null if record not rendered
   * @category Rows
   * @private
   */
  /**
   * Returns top and bottom for rendered row or estimated coordinates for unrendered.
   * @function getRecordCoords
   * @param {Core.data.Model|String|Number} recordOrId Record or record id
   * @returns {Object} Record bounds with format { top, height, bottom }
   * @category Calculations
   * @private
   */
  /**
   * Get the Row at specified index. "Wraps" index if larger than available rows.
   * @function getRow
   * @param {Number} index
   * @returns {Grid.row.Row}
   * @category Rows
   * @private
   */
  /**
   * Get a Row for either a record, a record id or an HTMLElement
   * @function getRowFor
   * @param {HTMLElement|Core.data.Model|String|Number} recordOrId Record or record id or HTMLElement
   * @returns {Grid.row.Row} Found Row or `null` if record not rendered
   * @category Rows
   */
  /**
   * Get a Row from an HTMLElement
   * @function getRowFromElement
   * @param {HTMLElement} element
   * @returns {Grid.row.Row} Found Row or `null` if record not rendered
   * @category Rows
   * @private
   */
  changeRowManager(rowManager, oldRowManager) {
    const me = this;
    if (!me._isRowMeasured) {
      me.measureRowHeight();
    }
    oldRowManager == null ? void 0 : oldRowManager.destroy();
    if (rowManager) {
      const result = RowManager.new({
        grid: me,
        rowHeight: me.rowHeight,
        rowScrollMode: me.rowScrollMode || "move",
        autoHeight: me.autoHeight,
        fixedRowHeight: me.fixedRowHeight,
        internalListeners: {
          changeTotalHeight: "onRowManagerChangeTotalHeight",
          requestScrollChange: "onRowManagerRequestScrollChange",
          thisObj: me
        }
      }, rowManager);
      me.relayEvents(result, ["beforeRenderRow", "renderRow", "removeRows"]);
      me._rowManager = null;
      return result;
    }
  }
  // Manual relay needed for Split feature to catch the config change
  updateRowHeight(rowHeight) {
    if (!this.isConfiguring && this.rowManager) {
      this.rowManager.rowHeight = rowHeight;
    }
  }
  updateFixedRowHeight(fixedRowHeight) {
    var _a, _b;
    if (fixedRowHeight && ((_b = (_a = this.columns || this.peekConfig("columns")) == null ? void 0 : _a.some) == null ? void 0 : _b.call(_a, (col) => col.autoHeight))) {
      console.warn("One or more columns have `autoHeight` set, which is incompatible with `fixedRowHeight`.");
    }
  }
  // Default implementation, documented in `defaultConfig`
  getRowHeight(record) {
    return record.rowHeight;
  }
  // Hook for features that need to alter the row height
  processRowHeight(record, height) {
  }
  //endregion
  //region Store
  getAsyncEventSuffixForStore(store) {
    return this.asyncEventSuffix;
  }
  /**
   * Hooks up data store listeners
   * @private
   * @category Store
   */
  bindStore(store) {
    const suffix = this.getAsyncEventSuffixForStore(store), { masterStore } = store;
    store.ion({
      name: storeListenerName,
      [`refresh${suffix}`]: "onStoreDataChange",
      [`add${suffix}`]: "onStoreAdd",
      [`remove${suffix}`]: "onStoreRemove",
      [`replace${suffix}`]: "onStoreReplace",
      [`removeAll${suffix}`]: "onStoreRemoveAll",
      [`move${suffix}`]: store.tree ? null : "onFlatStoreMove",
      change: "relayStoreDataChange",
      idChange: "onStoreRecordIdChange",
      update: "onStoreUpdateRecord",
      beforeRequest: "onStoreBeforeRequest",
      afterRequest: "onStoreAfterRequest",
      exception: "onStoreException",
      commit: "onStoreCommit",
      clearChanges: "onStoreRecordClearChanges",
      startApplyChangeset: "internalOnStoreStartApplyChangeset",
      endApplyChangeset: "internalOnStoreEndApplyChangeset",
      lazyLoadStarted: "onStoreBeforeRequest",
      lazyLoadEnded: "onStoreAfterRequest",
      thisObj: this
    });
    masterStore == null ? void 0 : masterStore.ion({
      name: storeListenerName,
      // Using same name as ^ to unbind at same time
      beforeRequest: "onStoreBeforeRequest",
      afterRequest: "onStoreAfterRequest",
      lazyLoadEnded: "onStoreAfterRequest",
      thisObj: this
    });
    super.bindStore(store);
  }
  unbindStore(oldStore) {
    this.detachListeners(storeListenerName);
    if (this.destroyStore) {
      oldStore.destroy();
    }
  }
  get $store() {
    return this.originalStore || this.store;
  }
  changeStore(store) {
    var _a;
    if (store == null) {
      return null;
    }
    if (typeof store === "string") {
      store = Store.getStore(store);
    }
    if (!store.isStore) {
      store = ObjectHelper.assign({
        data: this.data,
        tree: Boolean((_a = this.initialConfig.features) == null ? void 0 : _a.tree)
      }, store);
      if (!store.data) {
        delete store.data;
      }
      if (!store.modelClass) {
        store.modelClass = GridRowModel;
      }
      store = new (store.readUrl ? AjaxStore : Store)(store);
    }
    return store;
  }
  updateStore(store, was) {
    var _a, _b;
    const me = this;
    (_a = super.updateStore) == null ? void 0 : _a.call(this, store, was);
    if (was) {
      me.unbindStore(was);
    }
    if (store) {
      if (was) {
        me.deselectAll();
      }
      me.onStoreLoadOverride();
      me.bindStore(store);
    }
    me.trigger("bindStore", { store, oldStore: was });
    if (!me.isDestroying && me.isPainted && !me.refreshSuspended) {
      (_b = me._rowManager) == null ? void 0 : _b.reinitialize();
    }
  }
  /**
   * Rerenders a cell if a record is updated in the store
   * @private
   * @category Store
   */
  onStoreUpdateRecord({ source: store, record, changes }) {
    const me = this;
    if (me.refreshSuspended) {
      return;
    }
    if (me.forceFullRefresh) {
      me.rowManager.refresh();
      me.forceFullRefresh = false;
    } else {
      let row;
      if (record.isFieldModified("id")) {
        row = me.getRowFor(record.meta.modified.id);
      }
      row = row || me.getRowFor(record);
      if (!row) {
        return;
      }
      if (me.fullRowRefresh || record.isSpecialRow) {
        const index = store.indexOf(record);
        if (index !== -1) {
          row.render(index, record);
        }
      } else {
        me.columns.visibleColumns.forEach((column) => {
          const field = column.field, isSafe = column.constructor.simpleRenderer && !Object.prototype.hasOwnProperty.call(column.data, "renderer");
          if (!isSafe || changes[field]) {
            const cellElement = row.getCell(field);
            if (cellElement) {
              row.renderCell(cellElement);
            }
          }
        });
      }
    }
  }
  refreshFromRowOnStoreAdd(row, context) {
    const me = this, { rowManager } = me;
    rowManager.renderFromRow(row);
    rowManager.trigger("changeTotalHeight", { totalHeight: rowManager.totalHeight });
    if (me.store.count === 1) {
      me.callEachSubGrid("refreshFakeScroll");
    }
  }
  onMaskAutoClose(mask) {
    super.onMaskAutoClose(mask);
    this.toggleEmptyText();
  }
  /**
   * Refreshes rows when data is added to the store
   * @private
   * @category Store
   */
  onStoreAdd({
    source: store,
    records,
    index,
    oldIndex,
    isChild,
    oldParent,
    parent,
    isMove,
    isExpand,
    isExpandAll
  }) {
    const me = this, { rowManager } = me;
    if (!me.isPainted || isExpandAll || me.refreshSuspended) {
      return;
    }
    if (isExpand && me.animateTreeNodeToggle) {
      return rowManager.insert(rowManager.getRowFor(parent).index + 1, records.length);
    }
    const hasExpandedOldParent = isMove && records.some((record) => {
      if (isMove[record.id]) {
        const oldParent2 = store.getById(record.meta.modified.parentId);
        return (oldParent2 == null ? void 0 : oldParent2.isExpanded(store)) && (oldParent2 == null ? void 0 : oldParent2.ancestorsExpanded(store));
      }
    });
    if (isChild && !records[0].ancestorsExpanded(store) && !hasExpandedOldParent) {
      if (!parent.isLeaf) {
        const parentRow = rowManager.getRowById(parent);
        if (parentRow) {
          rowManager.renderRows([parentRow]);
        }
      }
      return;
    }
    rowManager.calculateRowCount(false, true, true);
    if (store.isFiltered) {
      index = store.indexOf(records[0]);
    }
    const {
      topIndex,
      rows,
      rowCount
    } = rowManager, bottomIndex = rowManager.topIndex + rowManager.rowCount - 1, dataStart = index, dataEnd = index + records.length - 1, atEnd = bottomIndex >= store.count - records.length - 1;
    if (oldParent || oldIndex > -1 || isChild && isMove && Object.values(isMove).some((v) => v)) {
      rowManager.refresh();
    } else if (dataStart >= topIndex && dataStart < topIndex + rowCount) {
      me.refreshFromRowOnStoreAdd(rows[dataStart - topIndex], ...arguments);
    } else if (dataEnd >= topIndex && dataEnd < topIndex + rowCount) {
      rowManager.refresh();
    } else {
      if (atEnd && index > bottomIndex) {
        rowManager.fillBelow(me._scrollTop || 0);
      } else if (index < topIndex) {
        rowManager.refresh();
      }
      rowManager.estimateTotalHeight(true);
    }
  }
  /**
   * Responds to exceptions signalled by the store
   * @private
   * @category Store
   */
  onStoreException({ action, type, response, exceptionType, error }) {
    var _a;
    const me = this;
    let message;
    switch (type) {
      case "server":
        message = response.message || me.L("L{unspecifiedFailure}");
        break;
      case "exception":
        message = exceptionType === "network" ? me.L("L{networkFailure}") : (error == null ? void 0 : error.message) || ((_a = response == null ? void 0 : response.parsedJson) == null ? void 0 : _a.message) || me.L("L{parseFailure}");
        break;
    }
    me.applyMaskError(
      `<div class="b-grid-load-failure">
                <div class="b-grid-load-fail">${me.L(action === "read" ? "L{loadFailedMessage}" : "L{syncFailedMessage}")}</div>
                ${(response == null ? void 0 : response.url) ? `<div class="b-grid-load-fail">${response.url}</div>` : ""}
                <div class="b-grid-load-fail">${me.L("L{serverResponse}")}</div>
                <div class="b-grid-load-fail">${message}</div>
            </div>`
    );
  }
  /**
   * Refreshes rows when data is changed in the store
   * @private
   * @category Store
   */
  onStoreDataChange({ action, changes, source: store, syncInfo }) {
    var _a;
    (_a = super.onStoreDataChange) == null ? void 0 : _a.call(this, ...arguments);
    const me = this;
    if (me.refreshSuspended || !me.rowManager) {
      return;
    }
    if (action === "dataset") {
      me.rowManager.clearKnownHeights();
      if (store.isTree && store.isFiltered) {
        return;
      }
    }
    const isGroupFieldChange = store.isGrouped && changes && store.groupers.some((grp) => grp.rootFieldName in changes);
    if (me.isPainted && !isGroupFieldChange) {
      me.renderRows(Boolean(!(action in datasetReplaceActions) || me.preserveScrollOnDatasetChange || store.isChained && store.isFillingFromMaster));
    }
    me.toggleEmptyText();
  }
  /**
   * The hook is called when the id of a record has changed.
   * @private
   * @category Store
   */
  onStoreRecordIdChange() {
    var _a;
    (_a = super.onStoreRecordIdChange) == null ? void 0 : _a.call(this, ...arguments);
  }
  /**
   * Shows a load mask while the connected store is loading
   * @private
   * @category Store
   */
  onStoreBeforeRequest() {
    if (!this.isLockedRows && (!this.store.count || !this.store.lazyLoad)) {
      this.applyLoadMask();
    }
  }
  /**
   * Hides load mask after a load request ends either in success or failure
   * @private
   * @category Store
   */
  onStoreAfterRequest(event) {
    if (this.loadMask && !event.exception) {
      this.masked = null;
      this.toggleEmptyText();
    }
  }
  needsFullRefreshOnStoreRemove({ isCollapse }) {
    var _a, _b;
    const { store } = this;
    return ((_b = (_a = this.features) == null ? void 0 : _a.group) == null ? void 0 : _b.enabled) && store.isGrouped || // Need to redraw parents when children are removed since they might be converted to leaves
    store.tree && !isCollapse && store.modelClass.convertEmptyParentToLeaf;
  }
  /**
   * Animates removal of record.
   * @private
   * @category Store
   */
  onStoreRemove({ source: store, records, isCollapse, isChild, isMove, isCollapseAll }) {
    var _a;
    if (!this.isPainted || isMove || isCollapseAll) {
      return;
    }
    (_a = super.onStoreRemove) == null ? void 0 : _a.call(this, ...arguments);
    const me = this, { rowManager } = me;
    rowManager.invalidateKnownHeight(records);
    if (me.refreshSuspended) {
      return;
    }
    if (me.animateRemovingRows && !isCollapse && !isChild) {
      const rowsToRemove = records.reduce((result, record) => {
        const row = rowManager.getRowById(record.id);
        row && result.push(row);
        return result;
      }, []);
      if (rowsToRemove.length) {
        const topRow = rowsToRemove[0];
        me.isAnimating = true;
        EventHelper.onTransitionEnd({
          element: topRow._elementsArray[0],
          property: "left",
          // Detach listener after timeout even if event wasn't fired
          duration: me.transitionDuration + 50,
          thisObj: me,
          handler: () => {
            me.isAnimating = false;
            rowsToRemove.forEach((row) => !row.isDestroyed && row.removeCls("b-removing"));
            rowManager.refresh();
            me.trigger("rowRemove");
            me.afterRemove(arguments[0]);
          }
        });
        rowsToRemove.forEach((row) => row.addCls("b-removing"));
      }
    } else if (isCollapse && me.animateTreeNodeToggle) {
      const indicesToRemove = records.flatMap((record) => {
        const row = rowManager.getRowFor(record);
        return row ? row.index : emptyArray;
      }).sort(ascending), { length } = indicesToRemove;
      if (length && indicesToRemove[length - 1] === indicesToRemove[0] + length - 1) {
        return rowManager.insert(indicesToRemove[0], -indicesToRemove.length);
      }
    } else if (me.needsFullRefreshOnStoreRemove(...arguments)) {
      rowManager.refresh();
      me.afterRemove(arguments[0]);
    } else {
      const { rows } = rowManager, topRowIndex = records.reduce((result, record) => {
        const row = rowManager.getRowById(record.id);
        if (row) {
          result = Math.min(result, rows.indexOf(row));
        }
        return result;
      }, rows.length);
      if (rows[topRowIndex]) {
        !me.refreshSuspended && rowManager.renderFromRow(rows[topRowIndex]);
      } else {
        rowManager.trigger("changeTotalHeight", { totalHeight: rowManager.totalHeight });
      }
      me.trigger("rowRemove", { isCollapse });
      me.afterRemove(arguments[0]);
    }
  }
  onFlatStoreMove({ from, to }) {
    const { rowManager, store } = this, {
      topIndex,
      rowCount
    } = rowManager, [dataStart, dataEnd] = [from, to].sort((a, b) => a - b), visibleStart = store.indexOf(store.getAt(dataStart, true)), visibleEnd = store.indexOf(store.getAt(dataEnd, true));
    if (visibleStart >= topIndex && visibleStart < topIndex + rowCount) {
      rowManager.renderFromRow(rowManager.rows[visibleStart - topIndex]);
    } else if (visibleEnd >= topIndex && visibleEnd < topIndex + rowCount) {
      rowManager.refresh();
    }
  }
  onStoreReplace({ records, all }) {
    const { rowManager } = this;
    if (all) {
      rowManager.clearKnownHeights();
      rowManager.refresh();
    } else {
      const rows = records.reduce((rows2, [, record]) => {
        const row = this.getRowFor(record);
        if (row) {
          rows2.push(row);
        }
        return rows2;
      }, []);
      rowManager.invalidateKnownHeight(records);
      rowManager.renderRows(rows);
    }
  }
  relayStoreDataChange(event) {
    var _a;
    (_a = this.ariaElement) == null ? void 0 : _a.setAttribute("aria-rowcount", this.store.count + 1);
    if (!this.project) {
      return this.trigger("dataChange", { ...event, store: event.source, source: this });
    }
  }
  /**
   * Rerenders grid when all records have been removed
   * @private
   * @category Store
   */
  onStoreRemoveAll() {
    var _a;
    (_a = super.onStoreRemoveAll) == null ? void 0 : _a.call(this, ...arguments);
    if (this.isPainted) {
      this.rowManager.clearKnownHeights();
      this.renderRows(false);
      this.toggleEmptyText();
    }
  }
  // Refresh dirty cells on commit
  onStoreCommit({ changes }) {
    if (this.showDirty && changes.modified.length) {
      const rows = [];
      changes.modified.forEach((record) => {
        const row = this.rowManager.getRowFor(record);
        row && rows.push(row);
      });
      this.rowManager.renderRows(rows);
    }
  }
  // Refresh dirty cells after a call to Model#clearChanges
  onStoreRecordClearChanges({ record }) {
    const row = this.rowManager.getRowFor(record);
    if (row) {
      this.rowManager.renderRows([row]);
    }
  }
  internalOnStoreStartApplyChangeset() {
    this.suspendRefresh();
    if (this.constructor.bindStoreChangeset) {
      this.captureScrollTargetRow();
    }
  }
  internalOnStoreEndApplyChangeset() {
    this.resumeRefresh(true);
    if (this.constructor.bindStoreChangeset) {
      this.restoreScrollTargetRow();
    }
  }
  /**
   * Remember scroll position when store is about to apply a changeset
   * @private
   */
  captureScrollTargetRow() {
    const me = this;
    if (me.preserveScroll) {
      const { firstFullyVisibleRow: firstRow, lastVisibleRow: lastRow } = me.rowManager;
      if (firstRow) {
        me.lastVisibleRowIds = [firstRow.id];
        for (let index = firstRow.dataIndex + 1; index <= lastRow.dataIndex; index++) {
          me.lastVisibleRowIds.push(me.rowManager.getRow(index).id);
        }
        me.lastTopRowOffset = me.scrollable.getDeltaTo(
          firstRow.element,
          { block: "start", x: false, constrainToScrollable: false }
        );
      }
    }
  }
  /**
   * Restore scroll position. Go to the topmost row formerly in the view that is still present in the dataset.
   * @private
   */
  restoreScrollTargetRow() {
    const me = this;
    let didOverscroll = false;
    if (me.preserveScroll) {
      if (me.lastVisibleRowIds) {
        me.rowManager.refresh();
        if (me.scrollable.scrollHeight >= me.lastOverscrollHeight && me.scrollable.y === me.lastOverscrollY) {
          me.rowManager.fillBelow(me.scrollable.y);
        } else {
          me.lastOverscrollHeight = null;
          me.lastOverscrollY = null;
        }
        const targetId = me.lastVisibleRowIds.find((rowId) => me.store.getById(rowId));
        if (targetId != void 0 && targetId !== me.rowManager.firstFullyVisibleRow.id) {
          if (me.preserveScroll.overscroll) {
            const scrollNeeded = me.scrollable.getDeltaTo(
              me.getRecordCoords(targetId),
              {
                block: "start",
                x: false,
                constrainToScrollable: false,
                edgeOffset: me.lastTopRowOffset.yDelta
              }
            ).yDelta;
            if (scrollNeeded > me.scrollable.maxY - me.scrollable.y) {
              me.scrollable.scrollHeight += scrollNeeded;
              didOverscroll = true;
            }
          }
          me.scrollRowIntoView(targetId, {
            block: "start",
            edgeOffset: me.lastTopRowOffset.yDelta,
            x: false
          });
        }
      }
      me.lastVisibleRowIds = void 0;
      me.lastTopRowOffset = void 0;
      if (didOverscroll) {
        me.lastOverscrollHeight = me.scrollable.scrollHeight;
        me.lastOverscrollY = me.scrollable.y;
      }
    }
  }
  // Documented with config
  get data() {
    if (this._store) {
      return this._store.records;
    } else {
      return this._data;
    }
  }
  set data(data) {
    if (this._store) {
      this._store.data = data;
    } else {
      this._data = data;
    }
  }
  //endregion
  //region Context menu items
  /**
   * Populates the header context menu. Chained in features to add menu items.
   * @param {Object} options Contains menu items and extra data retrieved from the menu target.
   * @param {Grid.column.Column} options.column Column for which the menu will be shown
   * @param {Object<String,MenuItemConfig|Boolean|null>} options.items A named object to describe menu items
   * @internal
   */
  populateHeaderMenu({ column, items }) {
    const me = this, { subGrids, regions } = me, { parent } = column;
    let first = true;
    Object.entries(subGrids).forEach(([region, subGrid]) => {
      if (subGrid.sealedColumns) {
        return;
      }
      if (column.draggable && region !== column.region && (!parent && subGrids[column.region].columns.count > 1 || parent && parent.children.length > 1)) {
        const preceding = subGrid.element.compareDocumentPosition(subGrids[column.region].element) === document.DOCUMENT_POSITION_PRECEDING, moveRight = me.rtl ? !preceding : preceding, text = regions.length > 2 ? me.L("L{moveColumnTo}", me.optionalL(region)) : me.L(moveRight ? "L{moveColumnRight}" : "L{moveColumnLeft}");
        items[`${region}Region`] = {
          targetSubGrid: region,
          text,
          icon: "b-fw-icon b-icon-column-move-" + (moveRight ? "right" : "left"),
          separator: first,
          disabled: !column.allowDrag,
          onItem: ({ item }) => {
            const lastRegionIndex = me.columns.indexOf(subGrids[item.targetSubGrid].columns.last);
            column.traverse((col) => col.region = region);
            me.columns.insert(lastRegionIndex + 1, column);
            me.scrollColumnIntoView(column);
          }
        };
        first = false;
      }
    });
  }
  /**
   * Populates the cell context menu. Chained in features to add menu items.
   * @param {Object} options Contains menu items and extra data retrieved from the menu target.
   * @param {Grid.column.Column} options.column Column for which the menu will be shown
   * @param {Core.data.Model} options.record Record for which the menu will be shown
   * @param {Object<String,MenuItemConfig|Boolean|null>} options.items A named object to describe menu items
   * @internal
   */
  populateCellMenu({ record, items }) {
  }
  getColumnDragToolbarItems(column, items) {
    return items;
  }
  //endregion
  //region Getters
  normalizeCellContext(cellContext) {
    const grid = this, { columns } = grid;
    if (cellContext.isLocation) {
      return cellContext;
    }
    if (cellContext.isModel) {
      return new GridLocation({
        grid,
        id: cellContext.id,
        columnId: columns.visibleColumns[0].id
      });
    }
    return new GridLocation(ObjectHelper.assign({ grid }, cellContext));
  }
  /**
   * Returns a cell if rendered or null if not found.
   * @param {GridLocationConfig} cellContext A cell location descriptor
   * @returns {HTMLElement|null}
   * @category Getters
   */
  getCell(cellContext) {
    const { store, columns } = this, { visibleColumns } = this.columns, rowIndex = !isNaN(cellContext.row) ? cellContext.row : !isNaN(cellContext.rowIndex) ? cellContext.rowIndex : store.indexOf(cellContext.record || cellContext.id), columnIndex = !isNaN(cellContext.column) ? cellContext.column : !isNaN(cellContext.columnIndex) ? cellContext.columnIndex : visibleColumns.indexOf(cellContext.column || columns.getById(cellContext.columnId) || columns.get(cellContext.field) || visibleColumns[0]);
    return rowIndex > -1 && rowIndex < store.count && columnIndex > -1 && columnIndex < visibleColumns.length && this.normalizeCellContext(cellContext).cell || null;
  }
  /**
   * Returns the header element for the column
   * @param {String|Number|Grid.column.Column} columnId or Column instance
   * @returns {HTMLElement} Header element
   * @category Getters
   */
  getHeaderElement(columnId) {
    if (columnId.isModel) {
      columnId = columnId.id;
    }
    return this.fromCache(`.b-grid-header[data-column-id="${columnId}"]`);
  }
  getHeaderElementByField(field) {
    const column = this.columns.get(field);
    return column ? this.getHeaderElement(column) : null;
  }
  /**
   * Body height
   * @member {Number}
   * @readonly
   * @category Layout
   */
  get bodyHeight() {
    return this._bodyHeight;
  }
  /**
   * Header height
   * @member {Number}
   * @readonly
   * @category Layout
   */
  get headerHeight() {
    const me = this;
    if (me.isPainted && !me._headerHeight) {
      me._headerHeight = me.headerContainer.offsetHeight;
    }
    return me._headerHeight;
  }
  /**
   * Footer height
   * @member {Number}
   * @readonly
   * @category Layout
   */
  get footerHeight() {
    const me = this;
    if (me.isPainted && !me._footerHeight) {
      me._footerHeight = me.footerContainer.offsetHeight;
    }
    return me._footerHeight;
  }
  get isTreeGrouped() {
    var _a;
    return Boolean((_a = this.features.treeGroup) == null ? void 0 : _a.isGrouped);
  }
  /**
   * Searches up from the specified element for a grid row and returns the record associated with that row.
   * @param {HTMLElement} element Element somewhere within a row or the row container element
   * @returns {Core.data.Model} Record for the row
   * @category Getters
   */
  getRecordFromElement(element) {
    const el = element.closest(".b-grid-row");
    if (!el) {
      return null;
    }
    return this.store.storage.getAt(el.dataset.index);
  }
  /**
   * Searches up from specified element for a grid cell or an header and returns the column which the cell belongs to
   * @param {HTMLElement} element Element somewhere in a cell
   * @returns {Grid.column.Column} Column to which the cell belongs
   * @category Getters
   */
  getColumnFromElement(element) {
    const cell = element.closest(".b-grid-cell, .b-grid-header");
    if (!cell)
      return null;
    if (cell.matches(".b-grid-header")) {
      return this.columns.getById(cell.dataset.columnId);
    }
    const cellData = DomDataStore.get(cell);
    return this.columns.getById(cellData.columnId);
  }
  // Only added for type checking, since it seems common to get it wrong in react/angular
  updateAutoHeight(autoHeight) {
    ObjectHelper.assertBoolean(autoHeight, "autoHeight");
  }
  updateColumnLines(columnLines) {
    ObjectHelper.assertBoolean(columnLines, "columnLines");
    DomHelper.toggleClasses(this.element, "b-no-column-lines", !columnLines);
  }
  updateRowLines(rowLines) {
    DomHelper.toggleClasses(this.element, "b-no-row-lines", !rowLines);
  }
  get keyMapElement() {
    return this.bodyElement;
  }
  //endregion
  //region Fix width & height
  /**
   * Sets widths and heights for headers, rows and other parts of the grid as needed
   * @private
   * @category Width & height
   */
  fixSizes() {
    this.callEachSubGrid("fixWidths");
    const colHeaders = this.headerContainer.querySelectorAll(".b-grid-header.b-depth-0");
    for (let i = 0, { length } = colHeaders; i < length; i++) {
      colHeaders[i].setAttribute("aria-colindex", i + 1);
    }
  }
  onRowManagerChangeTotalHeight({ totalHeight, immediate }) {
    return this.refreshTotalHeight(totalHeight, immediate);
  }
  /**
   * Makes height of vertical scroller match estimated total height of grid. Called when scrolling vertically and
   * when showing/hiding rows.
   * @param {Number} [height] Total height supplied by RowManager
   * @param {Boolean} [immediate] Flag indicating if buffered element sizing should be bypassed
   * @private
   * @category Width & height
   */
  refreshTotalHeight(height = this.rowManager.totalHeight, immediate = false) {
    const me = this;
    if (me.renderingRows || !me.isVisible) {
      return false;
    }
    const scroller = me.scrollable, delta = Math.abs(me.virtualScrollHeight - height), clientHeight = me._bodyRectangle.height, newMaxY = height - clientHeight;
    if (delta) {
      const isCritical = newMaxY - me._scrollTop < clientHeight * 2 || // Or if we have scrolled pass visual height
      me._verticalScrollHeight && me._verticalScrollHeight - clientHeight < me._scrollTop;
      scroller.scrollHeight = me.virtualScrollHeight = height;
      if (me.isPainted && (me.scrolling && !isCritical || delta < 100) && !immediate) {
        me.bufferedFixElementHeights();
      } else {
        me.virtualScrollHeightDirty && me.virtualScrollHeightDirty();
        me.bufferedFixElementHeights.cancel();
        me.fixElementHeights();
      }
    }
  }
  fixElementHeights() {
    const me = this, height = me.virtualScrollHeight, heightInPx = `${height}px`;
    me._verticalScrollHeight = height;
    me.verticalScroller.style.height = heightInPx;
    me.virtualScrollHeightDirty = false;
    if (me.autoHeight) {
      me._height = null;
      me.bodyContainer.style.height = heightInPx;
      me._bodyHeight = height;
      me.refreshBodyRectangle();
    }
    me.refreshVirtualScrollbars();
  }
  refreshBodyRectangle() {
    return this._bodyRectangle = Rectangle.client(this.bodyContainer);
  }
  //endregion
  //region Scroll & virtual rendering
  set scrolling(scrolling) {
    this._scrolling = scrolling;
  }
  get scrolling() {
    return this._scrolling;
  }
  /**
   * Activates automatic scrolling of a subGrid when mouse is moved closed to the edges. Useful when dragging DOM
   * nodes from outside this grid and dropping on the grid.
   * @param {Grid.view.SubGrid|String|Grid.view.SubGrid[]|String[]} subGrid A subGrid instance or its region name or
   * an array of either
   * @category Scrolling
   */
  enableScrollingCloseToEdges(subGrids) {
    this.scrollManager.startMonitoring({
      scrollables: [
        {
          element: this.scrollable.element,
          direction: "vertical"
        },
        ...ArrayHelper.asArray(subGrids || []).map((subGrid) => ({ element: (typeof subGrid === "string" ? this.subGrids[subGrid] : subGrid).scrollable.element }))
      ],
      direction: "horizontal"
    });
  }
  /**
   * Deactivates automatic scrolling of a subGrid when mouse is moved closed to the edges
   * @param {Grid.view.SubGrid|String|Grid.view.SubGrid[]|String[]} subGrid A subGrid instance or its region name or
   * an array of either
   * @category Scrolling
   */
  disableScrollingCloseToEdges(subGrids) {
    this.scrollManager.stopMonitoring([
      this.scrollable.element,
      ...ArrayHelper.asArray(subGrids || []).map((subGrid) => (typeof subGrid === "string" ? this.subGrids[subGrid] : subGrid).element)
    ]);
  }
  /**
   * Responds to request from RowManager to adjust scroll position. Happens when jumping to a scroll position with
   * variable row height.
   * @param {Number} bottomMostRowY
   * @private
   * @category Scrolling
   */
  onRowManagerRequestScrollChange({ bottom }) {
    this.scrollable.y = bottom - this.bodyHeight;
  }
  /**
   * Scroll syncing for normal headers & grid + triggers virtual rendering for vertical scroll
   * @private
   * @fires scroll
   * @category Scrolling
   */
  initScroll() {
    const me = this, { scrollable } = me;
    if (!me.scrollInitialized) {
      me.scrollInitialized = true;
      scrollable.contentElement = me.contentElement;
      scrollable.ion({
        scroll: "onGridVerticalScroll",
        scrollend: "onGridVerticalScrollEnd",
        thisObj: me
      });
      me.callEachSubGrid("initScroll");
      if (BrowserHelper.isMobileSafari) {
        scrollable.element.style.transform = "translate3d(0, 0, 0)";
      }
    }
  }
  onGridVerticalScroll({ source: scrollable }) {
    const me = this, { y: scrollTop } = scrollable;
    if (scrollTop !== me._scrollTop) {
      me._scrollTop = scrollTop;
      if (!me.scrolling) {
        me.scrolling = true;
        me.eachSubGrid((s) => s.suspendResizeMonitor = true);
      }
      me.rowManager.updateRenderedRows(scrollTop);
      me.afterScroll({ scrollTop });
      me.trigger("scroll", { scrollTop });
    }
  }
  onGridVerticalScrollEnd() {
    this.scrolling = false;
    this.eachSubGrid((s) => s.suspendResizeMonitor = false);
  }
  /**
   * Scrolls a locally available record's row into view. If row isn't rendered it tries to calculate position.
   * Accepts the {@link BryntumScrollOptions} `column` property
   * @param {Core.data.Model|String|Number} recordOrId Record or record id
   * @param {BryntumScrollOptions} [options] How to scroll.
   * @returns {Promise} A promise which resolves when the specified row has been scrolled into view.
   * @category Scrolling
   */
  async scrollRowIntoView(recordOrId, options = defaultScrollOptions) {
    var _a;
    const me = this, blockPosition = options.block || "nearest", { rowManager } = me, record = me.store.getById(recordOrId);
    if (record) {
      let scrollPromise;
      if (me.store.indexOf(record) === -1) {
        return resolvedPromise;
      }
      if (!me.rowManager.topRow) {
        await me.await("renderRows");
      }
      let scroller = me.scrollable, recordRect = me.getRecordCoords(record);
      const scrollerRect = Rectangle.from(scroller.element);
      if (recordRect.virtual) {
        const virtualBlock = recordRect.block, innerOptions = blockPosition !== "nearest" ? options : {
          block: virtualBlock
        };
        scrollPromise = scroller.scrollIntoView(recordRect, {
          block: "center"
        });
        rowManager.scrollTargetRecordId = record;
        rowManager.updateRenderedRows(scroller.y, true);
        recordRect = me.getRecordCoords(record);
        rowManager.lastScrollTop = scroller.y;
        if (recordRect.virtual) {
          return resolvedPromise;
        }
        if (options.animate) {
          scroller.suspendEvents();
          if (blockPosition === "end" || blockPosition === "nearest" && virtualBlock === "end") {
            scroller.y -= scrollerRect.bottom - recordRect.bottom;
          } else if (blockPosition === "start" || blockPosition === "nearest" && virtualBlock === "start") {
            scroller.y += recordRect.y - scrollerRect.y;
          }
          rowManager.updateRenderedRows(scroller.y, false, true);
          if (virtualBlock === "end") {
            scroller.y -= rowManager.appendRowBuffer * rowManager.rowHeight - 1;
          } else {
            scroller.y += rowManager.prependRowBuffer * rowManager.rowHeight - 1;
          }
          scroller.resumeEvents();
          await scroller.scrollIntoView(me.getRecordCoords(record), Object.assign({}, options, innerOptions));
        } else {
          if (!options.recursive) {
            await scrollPromise;
          }
          await ((_a = me.scrollRowIntoView) == null ? void 0 : _a.call(me, record, Object.assign({ recursive: true }, options, innerOptions)));
        }
      } else {
        let { column } = options;
        if (column) {
          if (!column.isModel) {
            column = me.columns.getById(column) || me.columns.get(column);
          }
          if (column) {
            scroller = me.getSubGridFromColumn(column).scrollable;
            const cellRect = Rectangle.from(rowManager.getRowFor(record).getCell(column.id));
            recordRect.x = cellRect.x;
            recordRect.width = cellRect.width;
          }
        } else {
          options = ObjectHelper.assign({}, options, { x: false });
        }
        await scroller.scrollIntoView(recordRect, options);
      }
    }
  }
  /**
   * Scrolls a column into view (if it is not already)
   * @param {Grid.column.Column|String|Number} column Column name (data) or column index or actual column object.
   * @param {BryntumScrollOptions} [options] How to scroll.
   * @returns {Promise} If the column exists, a promise which is resolved when the column header element has been
   * scrolled into view.
   * @category Scrolling
   */
  scrollColumnIntoView(column, options) {
    column = column instanceof Column ? column : this.columns.get(column) || this.columns.getById(column) || this.columns.getAt(column);
    return this.getSubGridFromColumn(column).scrollColumnIntoView(column, options);
  }
  /**
   * Scrolls a locally available record's cell into view (if it is not already).
   * @param {GridLocationConfig|Grid.util.GridLocation} cellContext Cell selector { id: recordId, column: 'columnName' }
   * @returns {Promise} A promise which resolves when the specified cell has been scrolled into view.
   * @category Scrolling
   */
  scrollCellIntoView(cellContext, options) {
    const location = this.normalizeCellContext(cellContext);
    return this.scrollRowIntoView(location.id, {
      column: location.columnId,
      ...typeof options === "boolean" ? { animate: options } : options
    });
  }
  /**
   * Scroll all the way down
   * @returns {Promise} A promise which resolves when the bottom is reached.
   * @category Scrolling
   * @non-lazy-load
   */
  scrollToBottom(options) {
    return this.scrollRowIntoView(this.store.last, options);
  }
  /**
   * Scroll all the way up
   * @returns {Promise} A promise which resolves when the top is reached.
   * @category Scrolling
   */
  scrollToTop(options) {
    return this.scrollable.scrollBy(0, -this.scrollable.y, options);
  }
  /**
   * Stores the scroll state. Returns an objects with a `scrollTop` number value for the entire grid and a `scrollLeft`
   * object containing a left position scroll value per sub grid.
   * @returns {Object}
   * @category Scrolling
   */
  storeScroll() {
    const state = this.storedScrollState = {
      scrollTop: this.scrollable.y,
      scrollLeft: {}
    };
    this.eachSubGrid((subGrid) => {
      state.scrollLeft[subGrid.region] = subGrid.scrollable.x;
    });
    return state;
  }
  /**
   * Restore scroll state. If state is not specified, restores the last stored state.
   * @param {Object} [state] Scroll state, optional
   * @category Scrolling
   */
  restoreScroll(state = this.storedScrollState) {
    const me = this;
    me.eachSubGrid((subGrid) => {
      var _a;
      const x = state.scrollLeft[subGrid.region];
      if (x != null) {
        subGrid.scrollable.updateX(x);
        subGrid.header.scrollable.updateX(x);
        subGrid.footer.scrollable.updateX(x);
        (_a = subGrid.fakeScroller) == null ? void 0 : _a.updateX(x);
      }
    });
    me.scrollable.updateY(state.scrollTop);
  }
  //endregion
  //region Theme & measuring
  beginGridMeasuring() {
    const me = this;
    if (!me.$measureCellElements) {
      me.$measureCellElements = DomHelper.createElement({
        // For row height measuring, features are not yet there. Work around that for the stripe feature,
        // which removes borders
        className: "b-grid-subgrid " + (!me._isRowMeasured && me.hasFeature("stripe") ? "b-stripe" : ""),
        reference: "subGridElement",
        style: {
          position: "absolute",
          top: "-10000px",
          left: "-100000px",
          visibility: "hidden",
          contain: "strict"
        },
        children: [
          {
            className: "b-grid-row",
            reference: "rowElement",
            children: [
              {
                className: "b-grid-cell",
                reference: "cellElement",
                style: {
                  width: "auto",
                  contain: BrowserHelper.isFirefox ? "layout paint" : "layout style paint"
                }
              }
            ]
          }
        ]
      });
    }
    me.getConfig("element");
    me.verticalScroller.appendChild(me.$measureCellElements.subGridElement);
    if (!me.rendered) {
      const targetEl = me.appendTo || me.insertBefore || document.body, rootElement = DomHelper.getRootElement(typeof targetEl === "string" ? document.getElementById(targetEl) : targetEl) || document.body;
      if (!me.adopt || !rootElement.contains(me.element)) {
        rootElement.appendChild(me.element);
        me.$removeAfterMeasuring = true;
      }
    }
    return me.$measureCellElements;
  }
  endGridMeasuring() {
    if (this.$removeAfterMeasuring) {
      this.element.remove();
      this.$removeAfterMeasuring = false;
    }
    this.$measureCellElements.subGridElement.remove();
  }
  /**
   * Creates a fake subgrid with one row and measures its height. Result is used as rowHeight.
   * @private
   */
  measureRowHeight() {
    const me = this, { rowElement } = me.beginGridMeasuring(), bodyZoom = parseFloat(DomHelper.getStyleValue(document.body, "zoom")), styles = DomHelper.getStyleValue(rowElement, ["height", "border-top-width", "border-bottom-width"]), styleHeight = parseInt(styles.height), multiplier = bodyZoom * (BrowserHelper.isFirefox ? globalThis.devicePixelRatio / Math.max(Math.trunc(globalThis.devicePixelRatio), 1) : 1), borderTop = styles["border-top-width"] ? Math.round(multiplier * parseFloat(styles["border-top-width"])) : 0, borderBottom = styles["border-bottom-width"] ? Math.round(multiplier * parseFloat(styles["border-bottom-width"])) : 0;
    if (me.rowHeight == null || me.rowHeight === me._rowHeightFromStyle) {
      me.rowHeight = !isNaN(styleHeight) && styleHeight ? styleHeight : me.defaultRowHeight;
      me._rowHeightFromStyle = me.rowHeight;
    }
    me._rowBorderHeight = borderTop + borderBottom;
    me._isRowMeasured = true;
    me.endGridMeasuring();
  }
  /**
   * Handler for global theme change event (triggered by shared.js). Remeasures row height.
   * @private
   */
  onThemeChange({ theme }) {
    this.whenVisible("measureRowHeight");
    this.trigger("theme", { theme });
  }
  //endregion
  //region Rendering of rows
  /**
   * Triggers a render of all the cells in the row for the passed record.
   *
   * Since Grid uses virtualized rows, calling this method for a record that is not currently displayed in a row will
   * not have any effect.
   *
   * {@note}Manipulating records and Grid settings refreshes the Grid automatically. You should only need to call
   * this function if you have outside data/settings unknown to the Grid that has changed and requires Grid to update
   * the row to reflect the changes{/@note}
   *
   * @param {Core.data.Model} record The record whose row should be refreshed
   * @category Rendering
   */
  refreshRow(record) {
    this.refreshRows([record]);
  }
  /**
   * Triggers a render of all the cells in the rows for the passed records. Leave the argument out to refresh all
   * rows.
   *
   * Since Grid uses virtualized rows, calling this method for records that are not currently displayed in rows will
   * not have any effect.
   *
   * {@note}Manipulating records and Grid settings refreshes the Grid automatically. You should only need to call
   * this function if you have outside data/settings unknown to the Grid that has changed and requires Grid to update
   * rows to reflect the changes{/@note}
   *
   * @param {Core.data.Model[]} [records] The records whose rows should be refreshed
   * @category Rendering
   */
  refreshRows(records = null, returnToTop = false) {
    if (this.refreshSuspended) {
      return;
    }
    const { element, rowManager } = this;
    if (!records) {
      element.classList.add("b-notransition");
      if (returnToTop) {
        rowManager.returnToTop();
      } else {
        rowManager.refresh(true);
      }
      element.classList.remove("b-notransition");
    } else {
      const rows = records.flatMap((record) => {
        var _a;
        return (_a = rowManager.getRowById(record)) != null ? _a : [];
      });
      rowManager.renderRows(rows);
    }
  }
  /**
   * Triggers a render of all the cells in a column.
   * @param {Grid.column.Column} column
   * @category Rendering
   */
  refreshColumn(column) {
    if (column.isVisible) {
      if (column.isLeaf) {
        this.rowManager.forEach((row) => row.renderCell(row.getCell(column.id)));
      } else {
        column.children.forEach((child) => this.refreshColumn(child));
      }
    }
  }
  //endregion
  //region Render the grid
  /**
   * Recalculates virtual scrollbars widths and scrollWidth
   * @private
   */
  refreshVirtualScrollbars() {
    const me = this, {
      headerContainer,
      footerContainer,
      virtualScrollers,
      scrollable,
      hasVerticalOverflow
    } = me, { classList } = virtualScrollers, hadHorizontalOverflow = !classList.contains("b-hide-display"), hasHorizontalOverflow = Object.values(me.subGrids).some((subGrid) => subGrid.overflowingHorizontally), horizontalOverflowChanged = hasHorizontalOverflow !== hadHorizontalOverflow;
    if (horizontalOverflowChanged) {
      virtualScrollers.classList.toggle("b-hide-display", !hasHorizontalOverflow);
    }
    if (DomHelper.scrollBarWidth) {
      const needsPadding = hasVerticalOverflow || scrollable.overflowY === "scroll";
      headerContainer.classList.toggle("b-show-yscroll-padding", needsPadding);
      footerContainer.classList.toggle("b-show-yscroll-padding", needsPadding);
      virtualScrollers.classList.toggle("b-show-yscroll-padding", needsPadding);
      if (horizontalOverflowChanged || !me.$virtualScrollbarsRefreshed) {
        me.callEachSubGrid("refreshFakeScroll");
        me.onHeightChange();
      }
    }
  }
  get hasVerticalOverflow() {
    return this.scrollable.hasOverflow("y");
  }
  /**
   * Returns content height calculated from row manager
   * @private
   */
  get contentHeight() {
    const rowManager = this.rowManager;
    return Math.max(rowManager.totalHeight, rowManager.bottomRow ? rowManager.bottomRow.bottom : 0);
  }
  onContentChange() {
    const me = this, rowManager = me.rowManager;
    if (me.isVisible) {
      rowManager.estimateTotalHeight(me.autoHeight);
      me.paintListener = null;
      me.refreshTotalHeight(me.contentHeight);
      me.callEachSubGrid("refreshFakeScroll");
      me.onHeightChange();
    } else if (!me.paintListener) {
      me.paintListener = me.ion({
        paint: "onContentChange",
        once: true,
        thisObj: me
      });
    }
  }
  triggerPaint() {
    if (!this.isPainted) {
      this.refreshBodyRectangle();
    }
    super.triggerPaint();
  }
  onHeightChange() {
    const me = this;
    me.refreshBodyRectangle();
    me._bodyHeight = me.autoHeight ? me.contentHeight : me.bodyContainer.offsetHeight;
  }
  /**
   * Suspends UI refreshes after updates to the underlying data.
   *
   * Multiple calls to `suspendRefresh` stack up, and will require an equal number of `resumeRefresh` calls to
   * actually resume UI refresh.
   *
   * @category Rendering
   */
  suspendRefresh() {
    this.refreshSuspended++;
  }
  /**
   * Resumes UI refreshes after updates to the underlying data.
   *
   * Multiple calls to `suspendRefresh` stack up, and will require an equal number of `resumeRefresh` calls to
   * actually resume UI refresh.
   *
   * Specify `false` as the first param to not refresh if this call unblocked the refresh suspension.
   *
   * @param {Boolean} [trigger=true] `true` to trigger a refresh, if this resume unblocks suspension
   * @category Rendering
   */
  resumeRefresh(trigger = true) {
    if (this.refreshSuspended && !--this.refreshSuspended) {
      if (trigger) {
        this.refreshRows();
      }
      this.trigger("resumeRefresh", { trigger });
    }
  }
  /**
   * Rerenders all grid rows, completely replacing all row elements with new ones
   * @category Rendering
   */
  renderRows(keepScroll = true) {
    const me = this, scrollState = keepScroll && me.storeScroll();
    if (me.refreshSuspended) {
      return;
    }
    me.trigger("beforeRenderRows");
    me.renderingRows = true;
    me.element.classList.add("b-grid-refreshing");
    if (!keepScroll) {
      me.scrollable.y = me._scrollTop = 0;
    }
    me.rowManager.reinitialize(!keepScroll);
    me.trigger("renderRows");
    me.renderingRows = false;
    me.onContentChange();
    if (keepScroll) {
      me.restoreScroll(scrollState);
    }
    if (me.autoHeight && me.store.count > 200 && !me.autoHeightWarning && !me.disableAutoHeightWarning) {
      console.warn("Warning: 'autoHeight' is enabled for a large number of rows (200+). While 'autoHeight' provides flexibility, it should be used cautiously as it disables virtualization, leading to significant performance degradation with large datasets. Consider alternatives to maintain optimal performance.");
      me.autoHeightWarning = true;
    }
    me.element.classList.remove("b-grid-refreshing");
  }
  /**
   * Rerenders the grids rows, headers and footers, completely replacing all row elements with new ones
   * @category Rendering
   */
  renderContents() {
    const me = this, { element, headerContainer, footerContainer, rowManager } = me;
    me.emptyCache();
    if (me.isPainted) {
      me._headerHeight = null;
      me.callEachSubGrid("refreshHeader");
      me.callEachSubGrid("refreshFooter");
      me.renderHeader(headerContainer, element);
      me.renderFooter(footerContainer, element);
      me.fixSizes();
      const refreshContext = rowManager.removeAllRows();
      rowManager.calculateRowCount(false, true, true);
      rowManager.setPosition(refreshContext);
      me.renderRows();
    }
  }
  /**
   * Rerenders all grid headers
   * @category Rendering
   */
  refreshHeaders() {
    this.callEachSubGrid("refreshHeader");
  }
  /**
   * Rerender a single grid header
   * @param {Grid.column.Column} column The column to refresh
   * @category Rendering
   */
  refreshHeader(column) {
    column.subGrid.refreshHeader();
  }
  onPaintOverride() {
  }
  onStoreLoadOverride() {
  }
  // Render rows etc. on first paint, to make sure Grid's element has been laid out
  onInternalPaint({ firstPaint }) {
    var _a;
    const me = this;
    me.ariaElement.setAttribute("aria-rowcount", me.store.count + 1);
    (_a = super.onInternalPaint) == null ? void 0 : _a.call(this, ...arguments);
    if (me.onPaintOverride() || !firstPaint) {
      return;
    }
    const {
      rowManager,
      store,
      element,
      headerContainer,
      bodyContainer,
      footerContainer
    } = me, scrollPad = DomHelper.scrollBarPadElement;
    let columnsChanged, maxDepth = 0;
    me.role = (store == null ? void 0 : store.isTree) ? "treegrid" : "grid";
    me.columns.ion({
      change: () => columnsChanged = true,
      once: true
    });
    me.updateResponsive(me.width, 0);
    if (columnsChanged) {
      me.callEachSubGrid("refreshHeader", headerContainer);
      me.callEachSubGrid("refreshFooter", footerContainer);
    }
    me.renderHeader(headerContainer, element);
    me.renderFooter(footerContainer, element);
    DomHelper.append(headerContainer, scrollPad);
    DomHelper.append(footerContainer, scrollPad);
    DomHelper.append(me.virtualScrollers, scrollPad);
    me.refreshBodyRectangle();
    const bodyOffsetHeight = me.bodyContainer.offsetHeight;
    if (me.autoHeight) {
      me._bodyHeight = rowManager.initWithHeight(element.offsetHeight - headerContainer.offsetHeight - footerContainer.offsetHeight, true);
      bodyContainer.style.height = me.bodyHeight + "px";
    } else {
      me._bodyHeight = bodyOffsetHeight;
      rowManager.initWithHeight(me._bodyHeight, true);
    }
    me.eachSubGrid((subGrid) => {
      if (subGrid.header.maxDepth > maxDepth) {
        maxDepth = subGrid.header.maxDepth;
      }
    });
    headerContainer.dataset.maxDepth = maxDepth;
    me.fixSizes();
    if (store.count || !store.isLoading) {
      me.renderRows();
    }
    if (me.columns.usesAutoHeight) {
      const { fonts } = document;
      if ((fonts == null ? void 0 : fonts.status) !== "loaded") {
        fonts.ready.then(() => !me.isDestroyed && me.refreshRows());
      }
    }
    me.initScroll();
    me.initInternalEvents();
  }
  render() {
    var _a;
    const me = this;
    me.requireSize = Boolean(me.owner);
    super.render(...arguments);
    me.setupFocusListeners();
    if (!me.autoHeight) {
      if (me.headerContainer.offsetHeight && !me.bodyContainer.offsetHeight) {
        console.warn("Grid element not sized correctly, please check your CSS styles and review how you size the widget");
      }
      if (!me.splitFrom && !((_a = me.features.split) == null ? void 0 : _a.owner) && // Don't warn for splits
      !("minHeight" in me.initialConfig) && !("height" in me.initialConfig) && parseInt(globalThis.getComputedStyle(me.element).minHeight) === me.height) {
        console.warn(
          `The ${me.$$name} is sized by its predefined minHeight, likely this is not intended. Please check your CSS and review how you size the widget, or assign a fixed height in the config. For more information, see the "Basics/Sizing the component" guide in docs.`
        );
      }
    }
  }
  //endregion
  //region Hooks
  /**
   * Called after headers have been rendered to the headerContainer.
   * This does not do anything, it's just for Features to hook in to.
   * @param {HTMLElement} headerContainer DOM element which contains the headers.
   * @param {HTMLElement} element Grid element
   * @private
   * @category Rendering
   */
  renderHeader(headerContainer, element) {
  }
  /**
   * Called after footers have been rendered to the footerContainer.
   * This does not do anything, it's just for Features to hook in to.
   * @param {HTMLElement} footerContainer DOM element which contains the footers.
   * @param {HTMLElement} element Grid element
   * @private
   * @category Rendering
   */
  renderFooter(footerContainer, element) {
  }
  // Hook for features to affect cell rendering before renderers are run
  beforeRenderCell() {
  }
  // Hooks for features to react to a row being rendered
  beforeRenderRow() {
  }
  afterRenderRow() {
  }
  // Hook for features to react to scroll
  afterScroll() {
  }
  // Hook that can be overridden to prepare custom editors, can be used by framework wrappers
  processCellEditor(editorConfig) {
  }
  // Hook for features to react to column changes
  afterColumnsChange() {
    super.afterColumnsChange(...arguments);
  }
  // Hook for features to react to record removal (which might be transitioned)
  afterRemove(removeEvent) {
  }
  // Hook for features to react to groups being collapsed/expanded
  afterToggleGroup() {
  }
  // Hook for features to react to subgrid being collapsed
  afterToggleSubGrid() {
  }
  // Hook into Base, to trigger another hook for features to hook into :)
  // If features hook directly into this, it will be called both for Grid's changes + feature's changes,
  // since they also extend Base.
  onConfigChange(info) {
    super.onConfigChange(info);
    if (!this.isConfiguring) {
      this.afterConfigChange(info);
    }
  }
  afterConfigChange(info) {
  }
  afterAddListener(eventName, listener) {
  }
  afterRemoveListener(eventName, listener) {
  }
  //endregion
  //region Masking and Appearance
  syncMaskCover(mask = this.masked) {
    if (mask) {
      const bodyRect = mask.cover === "body" && this.rectangleOf("bodyContainer"), scrollerRect = bodyRect && DomHelper.scrollBarWidth && this.rectangleOf("virtualScrollers"), { style } = mask.element;
      style.marginTop = bodyRect ? `${bodyRect.y}px` : "";
      style.height = bodyRect ? `${bodyRect.height + ((scrollerRect == null ? void 0 : scrollerRect.height) || 0)}px` : "";
    }
  }
  changeMasked(mask, maskInstance) {
    if (mask) {
      const me = this;
      if (typeof mask === "string") {
        mask = { text: mask };
      }
      if (me.isLockedRows || me.splitFrom) {
        mask.cover = null;
        mask.target = me.element.parentElement;
      } else {
        mask.cover = "body";
        mask.target = me.element;
      }
    }
    return super.changeMasked(mask, maskInstance);
    ;
  }
  /**
   * Show a load mask with a spinner and the specified message. When using an AjaxStore masking and unmasking is
   * handled automatically, but if you are loading data in other ways you can call this function manually when your
   * load starts.
   * ```
   * myLoadFunction() {
   *   // Show mask before initiating loading
   *   grid.maskBody('Loading data');
   *   // Your custom loading code
   *   load.then(() => {
   *      // Hide the mask when loading is finished
   *      grid.unmaskBody();
   *   });
   * }
   * ```
   * @param {String|MaskConfig} loadMask The message to show in the load mask (next to the spinner) or a config object
   * for a {@link Core.widget.Mask}.
   * @returns {Core.widget.Mask}
   * @category Misc
   */
  maskBody(loadMask) {
    let ret;
    if (this.bodyContainer) {
      this.masked = Mask.mergeConfigs(this.loadMaskDefaults, loadMask);
      ret = this.masked;
    }
    return ret;
  }
  /**
   * Hide the load mask.
   * @category Misc
   */
  unmaskBody() {
    this.masked = null;
  }
  updateEmptyText(emptyText) {
    var _a, _b;
    (_a = this.emptyTextEl) == null ? void 0 : _a.remove();
    const isDomHelper = typeof emptyText === "object";
    if (isDomHelper) {
      emptyText = ArrayHelper.asArray(emptyText);
    }
    this.emptyTextEl = DomHelper.createElement({
      parent: (_b = this.firstItem) == null ? void 0 : _b.element,
      className: "b-empty-text",
      [isDomHelper ? "children" : (emptyText == null ? void 0 : emptyText.includes("<")) ? "html" : "text"]: emptyText
    });
  }
  toggleEmptyText() {
    const { store } = this, isEmpty = !(store.count > 0 || store.isLoading || store.isCommitting);
    this.element.classList.toggle("b-grid-empty", isEmpty);
  }
  // Notify columns when our read-only state is toggled
  updateReadOnly(readOnly, old) {
    var _a;
    super.updateReadOnly(readOnly, old);
    if (!this.isConfiguring) {
      for (const column of this.columns.bottomColumns) {
        (_a = column.updateReadOnly) == null ? void 0 : _a.call(column, readOnly);
      }
    }
  }
  //endregion
  //region Extract config
  // This function is not meant to be called by any code other than Base#getCurrentConfig().
  // It extracts the current configs for the grid, with special handling for inline data
  getCurrentConfig(options) {
    const result = super.getCurrentConfig(options), { store } = this, data = store.getInlineData(options), storeState = store.getCurrentConfig(options) || result.store;
    if (data.length) {
      result.data = data;
    }
    if (storeState && store.originalModelClass === GridRowModel) {
      delete storeState.modelClass;
    }
    if (!ObjectHelper.isEmpty(storeState)) {
      result.store = storeState;
    }
    if (result.store) {
      delete result.store.data;
    }
    return result;
  }
  /**
   * Suspends CSS transitions after a row / event has been updated
   *
   * Multiple calls to `suspendAnimations` stack up, and will require an equal number of `resumeAnimations` calls to
   * actually resume animations.
   * @category Misc
   */
  suspendAnimations() {
    this._animationSuspendedCounter++;
  }
  /**
   * Resumes CSS transitions after a row / event has been updated
   * @category Misc
   */
  resumeAnimations() {
    this._animationSuspendedCounter--;
  }
  /**
   * Runs a function with transitions enabled (row height, event size etc.). Useful if you want to alter the UI
   * state with a transition.
   *
   * @param {Function} fn The function to run
   * @privateparam {Boolean} shouldAnimate boolean if it is false, animation won't run, otherwise animation runs
   * @returns {Promise} A promise which resolves when the transition duration has expired
   * @category Misc
   */
  async runWithTransition(fn, shouldAnimate) {
    const me = this;
    if (!shouldAnimate || !me.isVisible || me._animationSuspendedCounter !== 0 || me.enableEventAnimations === false) {
      return fn();
    }
    me.isAnimating = true;
    await me.executeAndAwaitAnimations(me.element, fn);
    me.isAnimating = false;
    if (!me.isDestroyed && !me.isAnimating) {
      me.trigger("transitionEnd");
    }
  }
}
__publicField(GridBase, "bindStoreChangeset", true);
GridBase.initClass();
VersionHelper.setVersion("grid", "6.0.4");
GridBase._$name = "GridBase";
export {
  GridBase as default
};
