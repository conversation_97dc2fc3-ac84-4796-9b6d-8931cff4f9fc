:root, :host{
  --b-fa-style-family-classic:"Font Awesome 6 Free";
  --b-fa-font-solid:normal 900 1em/1 "Font Awesome 6 Free";
}

@font-face{
  font-family:"Font Awesome 6 Free";
  font-style:normal;
  font-weight:900;
  font-display:block;
  src:url("../../core-thin/resources/fonts/fa-solid-900.woff2") format("woff2"), url("../../core-thin/resources/fonts/fa-solid-900.ttf") format("truetype");
}
.fas,
.b-fa-solid{
  font-weight:900;
}

.b-content-icon, .b-group-state-icon, .b-gridbase.b-columnresize.b-touch.b-column-resizing .b-grid-header.b-resizing::before{
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
  display:inline-block;
  font-style:normal;
  font-variant:normal;
  font-weight:normal;
  line-height:1;
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  vertical-align:0;
}

.b-theme-info:before{
  content:'{"name":"Classic-Light"}';
}
.b-grid.b-readonly.b-actioncolumn-readonly .b-action-cell{
  filter:grayscale(1);
  opacity:0.2;
  pointer-events:none;
}

.b-action-cell{
  text-align:end;
}

.b-action-ct{
  display:flex;
  flex-flow:row nowrap;
}

.b-action-item{
  margin:0 -0.2em;
  text-align:center;
  font-size:1.2em;
  color:#757575;
}

.b-action-cell.b-grid-cell .b-action-item{
  transition:all 0.15s ease-in-out;
}

.b-action-item:hover{
  transform:scale3d(1.1, 1.1, 1);
  color:#4e4e4e;
}

.b-check-header-with-checkbox .b-grid-header-text{
  display:none;
}
.b-check-header-with-checkbox .b-field-inner{
  justify-content:center;
}

.b-check-header-with-checkbox .b-checkbox-label,
.b-check-cell .b-checkbox-label{
  display:flex;
  gap:0.4em;
  align-items:center;
}
.b-check-header-with-checkbox .b-checkbox-label:before,
.b-check-cell .b-checkbox-label:before{
  margin:0 !important;
}
.b-grid-cell.b-color-cell .b-color-cell-inner{
  border:none;
  border-radius:0.4em;
  cursor:pointer;
  height:1.5em;
  width:1.5em;
}
.b-grid-cell.b-color-cell .b-color-cell-inner.b-empty{
  border:1px solid #d9d9d9;
}
.b-percent-bar-outer{
  flex:1;
  height:30%;
  max-height:0.8em;
  background:#f1f1f1;
  border-radius:1em;
  overflow:clip;
}

.b-percent-bar{
  display:flex;
  flex-direction:column;
  justify-content:center;
  height:100%;
  max-width:100%;
  padding:0 0.2em;
  background:#64b5f6;
  color:#f9f9f9;
  font-size:0.7em;
  text-align:end;
  transition:width 0.5s, padding 0.5s, background-color 0.5s;
}
.b-percent-bar.b-zero{
  padding:0;
  text-indent:0.2em;
}
.b-percent-bar.b-low{
  background:#ef9a9a;
  color:#262626;
}
.b-percent-bar.b-low span{
  left:100%;
  margin-block:0;
  margin-inline:5px 0;
}
.b-percent-bar span{
  position:relative;
  display:inline-block;
  margin-block:0;
  margin-inline:0 5px;
  line-height:1em;
}

.b-percentdone-circle{
  --grid-percent-circle-angle:0;
  display:flex;
  align-items:center;
  justify-content:center;
  margin:0 auto;
  max-height:3em;
  max-width:3em;
  border-radius:50%;
  background-color:#d9d9d9;
  background-image:conic-gradient(#64b5f6 0 var(--grid-percent-circle-angle), transparent var(--grid-percent-circle-angle) 1turn);
}
.b-percentdone-circle.b-full, .b-percentdone-circle.b-empty{
  background-image:none;
}
.b-percentdone-circle.b-full{
  background-color:#64b5f6;
}
.b-percentdone-circle::after{
  content:attr(data-value);
  display:flex;
  align-items:center;
  justify-content:center;
  width:calc(100% - 6px);
  height:calc(100% - 6px);
  border-radius:50%;
  background:#fff;
  font-size:0.8em;
  color:#222;
}

.b-grid-body-container.b-scrolling .b-percent-bar{
  transition:none;
}
.b-rating-cell .b-icon{
  font-size:1.8em;
}
.b-rating-cell .b-empty{
  color:rgba(249, 249, 249, 0.3);
}
.b-rating-cell .b-filled{
  color:#ffe182;
}

.b-gridbase:not(.b-readonly) .b-rating-cell-inner:not(.b-not-editable) .b-icon{
  cursor:pointer;
  transition:all 0.2s linear;
  position:relative;
}
.b-gridbase:not(.b-readonly) .b-rating-cell-inner:not(.b-not-editable):hover .b-icon{
  color:#ffe182;
}
.b-gridbase:not(.b-readonly) .b-rating-cell-inner:not(.b-not-editable):hover .b-icon.b-empty::before{
  opacity:0.4;
}
.b-gridbase:not(.b-readonly) .b-rating-cell-inner:not(.b-not-editable) .b-icon:hover{
  transform:scale(1.3);
}
.b-gridbase:not(.b-readonly) .b-rating-cell-inner:not(.b-not-editable) .b-icon:hover.b-filled{
  opacity:1;
}
.b-gridbase:not(.b-readonly) .b-rating-cell-inner:not(.b-not-editable) .b-icon:hover:hover ~ .b-icon::before{
  color:rgba(249, 249, 249, 0.3);
  opacity:1;
}

.b-grid-body-container.b-scrolling .b-rating-cell-inner .b-icon{
  transition:none;
}
.b-grid-cell.b-row-number-cell, .b-grid-cell.b-sequence-cell{
  background-color:#f1f1f1;
  border-inline-end:1px solid #d9d9d9;
}

.b-grid-row.b-hover .b-grid-cell:hover.b-sequence-cell, .b-grid-row.b-hover .b-grid-cell:hover.b-row-number-cell{
  border-inline-end:1px solid #d9d9d9;
}

.b-group-row .b-grid-cell:first-child.b-sequence-cell, .b-group-row .b-grid-cell:first-child.b-row-number-cell{
  border-inline-end:1px solid #d9d9d9;
}

.b-gridbase:focus .b-grid-row.b-selected .b-grid-cell.b-selected.b-row-number-cell, .b-gridbase:focus .b-grid-row.b-selected .b-grid-cell.b-selected.b-sequence-cell{
  border-inline-end:1px solid #d9d9d9;
}

.b-gridbase .b-grid-row.b-selected .b-row-number-cell,
.b-gridbase .b-grid-row .b-grid-cell.b-selected.b-row-number-cell,
.b-gridbase .b-grid-row.b-selected .b-sequence-cell,
.b-gridbase .b-grid-row .b-grid-cell.b-selected.b-sequence-cell{
  background-color:#e4e4e4;
}

.b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-timeaxissubgrid) .b-grid-row:not(.b-group-row).b-hover .b-grid-cell.b-row-number-cell,
.b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-timeaxissubgrid) .b-grid-row:not(.b-group-row) .b-grid-cell.b-row-number-cell.b-hover, .b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-timeaxissubgrid) .b-grid-row:not(.b-group-row).b-hover .b-grid-cell.b-sequence-cell,
.b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-timeaxissubgrid) .b-grid-row:not(.b-group-row) .b-grid-cell.b-sequence-cell.b-hover{
  background-color:#ececec;
}
.b-tree-expander{
  position:relative;
  top:1px;
  margin:0 0.55em;
  cursor:pointer;
  display:flex;
  align-items:center;
  justify-content:center;
}

.b-loading-children .b-tree-expander.b-icon:before{
  content:"\f110";
  animation:rotate 2s infinite linear;
}

.b-grid-cell.b-tree-cell{
  align-items:stretch;
}

.b-tree-leaf-cell .b-tree-cell-inner:before{
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  color:#64b5f6;
  margin-inline-end:0.6em;
  width:1.25em;
  min-width:1.25em;
  text-align:center;
}

.b-tree-icon,
.b-icon-tree-leaf,
.b-icon-tree-folder,
.b-icon-tree-folder-open,
.b-icon-tree-expand,
.b-icon-tree-collapse{
  color:#64b5f6;
  width:1.25em;
  min-width:1.25em;
  text-align:center;
}

.b-tree-icon,
.b-icon-tree-leaf,
.b-icon-tree-folder,
.b-icon-tree-folder-open{
  margin-inline-end:0.6em;
}

.b-icon-tree-leaf:before{
  font-size:0.3em;
  vertical-align:middle;
}

.b-icon-tree-folder:before,
.b-icon-tree-folder-open:before{
  margin-inline-start:0.1em;
}

.b-icon-tree-expand.b-empty-parent,
.b-icon-tree-collapse.b-empty-parent{
  visibility:hidden;
}
.b-icon-tree-expand:before,
.b-icon-tree-collapse:before{
  font-size:1.2em;
}

.b-tree-cell-inner{
  padding:0.5em 0;
  display:flex;
  align-items:center;
  flex-shrink:0;
  flex-grow:1;
}
.b-tree-cell-inner.b-text-value{
  flex-shrink:1;
  overflow:hidden;
}
.b-tree-cell-inner.b-text-value .b-tree-cell-value{
  display:initial;
  overflow:hidden;
  text-overflow:ellipsis;
}
.b-tree-cell.b-auto-height .b-tree-cell-inner{
  flex-shrink:1;
}

.b-tree-cell.b-auto-height{
  flex-shrink:1;
}

a.b-tree-cell-inner{
  text-decoration:none;
}
a.b-tree-cell-inner:hover .b-tree-cell-value{
  text-decoration:underline;
}

.b-tree-parent-cell,
.b-tree-leaf-cell{
  padding-inline-start:10px;
}

.b-tree-parent-row .b-grid-cell{
  font-weight:400;
  color:#6f6f6f;
}

.b-tree-cell-value{
  flex:1;
  display:flex;
  flex-direction:row;
  align-items:center;
}

.b-touch .b-tree-expander{
  width:1.1em;
}
.b-touch .b-icon-tree-expand:before,
.b-touch .b-icon-tree-collapse:before{
  font-size:1.8em;
}
.b-touch .b-icon-tree-leaf:before{
  font-size:0.6em;
}
.b-touch .b-tree-icon,
.b-touch .b-tree-leaf-cell:not(.b-tree-parent-cell):before{
  font-size:1.2em;
}
.b-editor.b-cell-editor{
  position:absolute;
  background-color:transparent;
  border-width:0;
  border-style:solid;
  box-shadow:none !important;
  z-index:1;
}
.b-grid-cell:not(.b-tree-cell) .b-editor.b-cell-editor{
  width:100% !important;
}
.b-editor.b-cell-editor .b-field > .b-label{
  clip-path:inset(0 100% 100% 0);
  position:absolute;
  contain:strict;
}
.b-editor.b-cell-editor .b-numberfield:not(.b-has-start-trigger) .b-field-inner,
.b-editor.b-cell-editor .b-textfield:not(.b-has-start-trigger) .b-field-inner{
  padding-inline-start:0;
}
.b-editor.b-cell-editor .b-numberfield input,
.b-editor.b-cell-editor .b-textfield input{
  padding:0 calc(0.5em - 1px);
}
.b-editor.b-cell-editor .b-combo.b-uses-chipview .b-chipview{
  margin:0.3em;
}

.b-gridbase :is(.b-grid-row, .b-grid-cell).b-editing{
  overflow:visible;
  contain:unset;
  z-index:1;
  outline:none !important;
}
.b-gridbase :is(.b-grid-row, .b-grid-cell).b-editing .b-grid-cell.b-editing{
  color:transparent;
}

.b-grid-cell:not(.b-editing) .b-cell-editor{
  display:none;
}
.b-celltooltip-tip{
  max-height:50%;
}
.b-columndragtoolbar{
  position:absolute;
  top:calc(100% - 3em);
  left:50%;
  z-index:100;
  animation-name:b-show-columndragtoolbar;
  animation-duration:0.2s;
  transform:translateX(-50%);
  display:inline-flex;
  flex-direction:row;
  flex-wrap:wrap;
  align-items:center;
  justify-content:center;
  border-radius:1em;
  background:#fafafa;
  transition:opacity 0.2s, top 0.2s;
  box-shadow:none;
  padding:1em 1em 0.5em 1em;
  opacity:0.4;
  font-size:0.8em;
}
.b-columndragtoolbar.b-closer{
  top:50%;
  transform:translate(-50%, -50%);
}
.b-columndragtoolbar.b-hover{
  opacity:0.8;
}
.b-columndragtoolbar.b-remove{
  animation-name:b-hide-columndragtoolbar;
  animation-duration:0.2s;
  top:100%;
  opacity:0;
}
.b-columndragtoolbar.b-remove.b-closer{
  animation-name:b-hide-columndragtoolbar-closer;
}
.b-columndragtoolbar > .b-title{
  color:#fff;
  flex-basis:100%;
  margin-bottom:1em;
  text-align:center;
}
.b-columndragtoolbar > .b-title:before{
  content:"Drag header downwards";
}
.b-columndragtoolbar.b-closer > .b-title:before{
  content:"Drop header on a button";
}
.b-columndragtoolbar .b-group{
  display:inline-flex;
  flex-direction:column;
  align-items:center;
  margin-inline-end:1.5em;
}
.b-columndragtoolbar .b-group:last-child{
  margin-inline-end:0;
}
.b-columndragtoolbar .b-group .b-title{
  color:#fff;
  margin-block:0.5em;
}
.b-columndragtoolbar .b-buttons{
  display:inline-flex;
  flex-direction:row;
}
.b-columndragtoolbar .b-target-button{
  display:inline-flex;
  flex-direction:column;
  align-items:center;
  transition:all 0.2s;
  color:#fff;
  border:0.3em solid #64b5f6;
  border-radius:1em;
  background:transparent;
  padding:1em 0;
  width:7em;
  margin-inline-end:0.5em;
  box-shadow:none;
}
.b-columndragtoolbar .b-target-button:last-child{
  margin-inline-end:0;
}
.b-columndragtoolbar .b-target-button i{
  font-size:2.5em;
  color:#64b5f6;
  pointer-events:none;
  transition:all 0.2s;
}
.b-columndragtoolbar .b-target-button.b-hover:not([data-disabled=true]){
  background-color:#64b5f6;
  color:#fff;
  box-shadow:none;
}
.b-columndragtoolbar .b-target-button.b-hover:not([data-disabled=true]) i{
  transform:scale(1.1);
  color:#fff;
}
.b-columndragtoolbar .b-target-button.b-activate i{
  transform:scale(1.1) rotate(180deg) !important;
}
.b-columndragtoolbar .b-target-button[data-button-id^=group]{
  border-color:#ffcc80;
  background-color:transparent;
}
.b-columndragtoolbar .b-target-button[data-button-id^=group] i{
  color:#ffcc80;
}
.b-columndragtoolbar .b-target-button[data-button-id^=group].b-hover:not([data-disabled=true]){
  background-color:#ffcc80;
}
.b-columndragtoolbar .b-target-button[data-button-id^=group].b-hover:not([data-disabled=true]) i{
  color:#fff;
}
.b-columndragtoolbar .b-target-button[data-button-id^=multisort]{
  border-color:#349ef3;
}
.b-columndragtoolbar .b-target-button[data-button-id^=multisort] i{
  color:#349ef3;
}
.b-columndragtoolbar .b-target-button[data-button-id^=multisort].b-hover:not([data-disabled=true]){
  background-color:#349ef3;
}
.b-columndragtoolbar .b-target-button[data-button-id^=multisort].b-hover:not([data-disabled=true]) i{
  color:#fff;
}
.b-columndragtoolbar .b-target-button[data-disabled=true]{
  opacity:0.5;
}

@keyframes b-show-columndragtoolbar{
  from{
    top:100%;
    opacity:0;
  }
  to{
    top:calc(100% - 3em);
    opacity:0.4;
  }
}
@keyframes b-hide-columndragtoolbar{
  from{
    top:calc(100% - 3em);
    opacity:0.4;
  }
  to{
    top:100%;
    opacity:0;
  }
}
@keyframes b-hide-columndragtoolbar-closer{
  from{
    top:50%;
    opacity:0.4;
  }
  to{
    top:100%;
    opacity:0;
  }
}
.b-grid-header.b-drag-proxy{
  line-height:normal;
  font-weight:inherit;
  background-color:#f9f9f9;
  outline:1px solid #cccccc;
  transition:background-color 0.3s;
  border-inline-end:none;
}
.b-grid-header.b-drag-proxy.b-grid-header-parent{
  justify-content:stretch;
}
.b-grid-header.b-drag-proxy.b-grid-header-parent > .b-grid-header-text{
  border-inline-end:none;
}
.b-grid-header.b-drag-proxy .b-grid-header:last-child{
  border-inline-end:none;
}
.b-grid-header.b-drop-placeholder{
  opacity:0.3;
}
.b-grid-header.b-drag-invalid{
  outline:1px solid #ef9a9a;
}
.b-grid-header.b-drag-invalid, .b-grid-header.b-drag-invalid .b-grid-header{
  color:#ef9a9a;
}

.b-column-reorder-stretched.b-drag-proxy{
  background-color:rgba(49, 131, 254, 0.1254901961);
  outline:none;
}
.b-column-reorder-stretched.b-drag-proxy > *{
  display:none !important;
}
.b-gridbase.b-columnresize .b-grid-header.b-resize-handle{
  cursor:col-resize !important;
}
.b-gridbase.b-columnresize .b-grid-header.b-resizing{
  background:white;
}
.b-gridbase.b-columnresize.b-sort .b-grid-header.b-resizing:not(.b-filter):not(.b-multifilter) .b-filter-icon,
.b-gridbase.b-columnresize.b-sort .b-grid-header.b-over-resize-handle:not(.b-filter):not(.b-multifilter) .b-filter-icon,
.b-gridbase.b-columnresize.b-sort .b-grid-header.b-resizing:not(.b-sort):after,
.b-gridbase.b-columnresize.b-sort .b-grid-header.b-over-resize-handle:hover:not(.b-sort):after{
  display:none;
}
.b-gridbase.b-columnresize.b-touch.b-column-resizing .b-grid-header.b-resizing{
  overflow:visible;
  z-index:100;
}
.b-gridbase.b-columnresize.b-touch.b-column-resizing .b-grid-header.b-resizing::before{
  content:"\f337";
  font-size:1.5em;
  color:#fff;
  position:absolute;
  top:50%;
  transform:translateX(50%) translateY(-50%);
  right:0;
  z-index:101;
  border-radius:100%;
  background-color:#cccccc;
  pointer-events:none;
  padding:0.5em;
}
.b-rtl .b-gridbase.b-columnresize.b-touch.b-column-resizing .b-grid-header.b-resizing::before{
  right:auto;
  left:0;
}
.b-gridbase.b-columnresize.b-touch.b-column-resizing .b-grid-header:not(.b-resizing){
  z-index:1;
}

.b-context-menu{
  min-width:14em;
}

.b-fill-handle{
  width:0.8em;
  height:0.8em;
  transform:translate(-50%, -50%);
  position:absolute;
  background:#94ccf9;
  cursor:crosshair;
  border:1px solid #fff;
  z-index:200;
  background-clip:content-box;
  user-select:none;
  -webkit-user-select:none;
}

.b-fill-selection-border{
  position:absolute;
  border:2px solid #94ccf9;
  border-radius:0.1em;
  pointer-events:none;
  z-index:2;
  -webkit-user-select:none;
  user-select:none;
}

.b-indicate-crop{
  opacity:0.4;
}

.b-fill-handle-right-edge{
  border-right:0;
  transform:translate(-100%, -50%);
  width:0.5em;
}

.b-rtl .b-fill-handle{
  transform:translate(50%, -50%);
}
.b-rtl .b-fill-handle.b-fill-handle-left-edge{
  border-right:0;
  transform:translate(100%, -50%);
  width:0.5em;
}
@keyframes b-filter-icon-color{
  0%{
    color:#cccccc;
  }
  50%{
    color:#ffcc80;
  }
  100%{
    color:#cccccc;
  }
}
.b-grid-header .b-filter-icon{
  display:none;
  cursor:pointer;
  transition:opacity 0.2s;
}
.b-grid-header .b-filter-icon::after{
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  color:#ececec;
  transition:transform 0.3s, color 0.2s;
  transform:none;
  background:inherit;
  content:"\f0b0";
}
.b-grid-header .b-filter-icon:hover::after{
  color:#cccccc;
}

.b-gridbase:not(.b-filter):not(.b-multifilter) .b-filter-icon{
  opacity:0.2;
  pointer-events:none;
}

.b-gridbase:not(.b-column-resizing) .b-grid-header-container:not(.b-dragging-header) .b-grid-header.b-filterable:hover > .b-grid-header-text > .b-filter-icon{
  display:flex;
}

.b-grid-header.b-filter .b-grid-header-text{
  font-weight:700;
  color:#666;
}
.b-grid-header.b-filter .b-filter-icon{
  display:flex;
}
.b-grid-header.b-filter .b-filter-icon::after{
  color:#cccccc;
}
.b-grid-header.b-filter .b-filter-icon.b-latest::after{
  animation-name:b-filter-icon-color;
  animation-duration:0.75s;
}

.b-filter-popup-legacymode .b-field{
  width:15em;
}

.b-filter-popup .b-fieldfilterpickergroup{
  width:32em;
}
.b-filter-popup .b-multifilter-add{
  width:12em;
  align-self:center;
  margin:1.1em 0 0 0;
}
.b-filter-popup .b-fieldfilterpicker-property{
  display:none;
}
.b-filter-popup .b-panel-content{
  gap:0.6em;
}
.b-filter-bar-field{
  margin:0 0 0.5em 0;
  width:100%;
}

.b-filter-bar-compact .b-grid-header.b-filter-bar-enabled{
  flex-direction:row;
}
.b-filter-bar-compact .b-grid-header.b-filter-bar-enabled .b-grid-header-text{
  padding:0.25em 0;
  width:2em;
  order:1;
  margin-inline-end:-0.5em;
}
.b-filter-bar-compact .b-grid-header.b-filter-bar-enabled .b-grid-header-text .b-grid-header-text-content{
  display:none;
}
.b-filter-bar-compact .b-grid-header.b-filter-bar-enabled.b-grid-header-align-end .b-field{
  order:100000;
}
.b-filter-bar-compact .b-grid-header.b-filter-bar-enabled.b-grid-header-align-end input{
  text-align:end;
}
.b-filter-bar-compact .b-filter-bar-field{
  margin:0;
}
.b-filter-bar-compact .b-filter-bar-field .b-field-inner{
  background-color:transparent;
  border:none;
}
.b-filter-bar-compact .b-filter-bar-field .b-field-inner::before{
  display:none;
}
.b-filter-bar-compact .b-filter-bar-field .b-field-inner input{
  padding:0.8em 0;
}
.b-filter-bar-compact .b-filter-bar-field .b-field-inner input::placeholder{
  color:#565656;
  text-transform:none;
  font-size:1em;
  font-weight:inherit;
}
.b-filter-bar-compact .b-filter-bar-field .b-fieldtrigger{
  display:none;
}
.b-filter-bar-compact .b-filter-bar-field:focus-within:not(.b-empty) .b-icon-remove, .b-filter-bar-compact .b-filter-bar-field:focus-within .b-fieldtrigger:not(.b-step-trigger):not(.b-icon-remove):not(.b-spintrigger){
  display:inline-flex;
}
.b-filter-bar-compact .b-filter-bar-field.b-uses-chipview:not(.b-empty) input{
  margin-inline-start:0.5em;
  margin-top:-0.25em;
}
.b-filter-bar-compact .b-filter-bar-field.b-uses-chipview:not(.b-empty) input::placeholder{
  color:transparent;
}
.b-filter-bar-compact .b-filter-bar-field.b-uses-chipview:not(.b-empty) .b-chip{
  font-size:0.9em;
}
.b-filter-bar-compact .b-filter-bar-field.b-uses-chipview:not(.b-empty) .b-chip[data-index="0"]{
  margin-inline-start:0;
}
.b-filter-bar-compact.b-dragging .b-filter-bar-field{
  display:none;
}
.b-group-summary .b-grid-group-collapsed.b-header-summary .b-grid-cell.b-group-title{
  overflow:hidden;
}
.b-group-summary .b-grid-row.b-group-footer .b-grid-cell{
  border-inline-end-color:transparent;
  background-color:white;
}
.b-group-summary .b-grid-row.b-group-footer td{
  padding:0.25em 0;
}
.b-group-summary .b-grid-row.b-group-footer .b-grid-summary-label{
  padding-inline-end:1em;
}
.b-group-summary .b-grid-row.b-group-footer .b-grid-summary-value{
  width:100%;
}
.b-gridbase.b-firefox .b-mergecells .b-single-child .b-grid-subgrid:not(.b-timeaxissubgrid):not(.b-overlay-scrollbar), .b-mergecells.b-gridbase.b-firefox .b-single-child .b-grid-subgrid:not(.b-timeaxissubgrid):not(.b-overlay-scrollbar){
  overflow:visible !important;
}
.b-mergecells .b-single-child .b-grid-subgrid:not(.b-grid-subgrid-collapsed):not(.b-timeaxissubgrid):not(.b-horizontal-overflow),
.b-mergecells .b-grid-vertical-scroller{
  overflow:visible !important;
}
.b-mergecells .b-grid-row.b-hover, .b-mergecells.b-mergecells-passthrough .b-grid-row.b-selected, .b-using-keyboard .b-mergecells .b-grid-row.b-selected{
  z-index:2;
  overflow:visible;
}
.b-mergecells .b-grid-row.b-hover .b-merged-cell, .b-mergecells.b-mergecells-passthrough .b-grid-row.b-selected .b-merged-cell, .b-using-keyboard .b-mergecells .b-grid-row.b-selected .b-merged-cell{
  top:-1px;
  height:calc(100% + 1px);
  border-top:1px solid #d9d9d9;
}
.b-mergecells .b-stripe .b-grid-row.b-group-row,
.b-mergecells .b-grid-row.b-group-row{
  z-index:2;
}
.b-mergecells .b-merged-cell{
  border-bottom:none;
}

.b-grid-merged-cells{
  position:absolute;
  contain:strict;
  border-inline-end:1px solid #d9d9d9;
  border-bottom:1px solid #d9d9d9;
  z-index:1;
  display:flex;
  align-items:flex-start;
  background:#fff;
}
.b-gridbase.b-mergecells-passthrough .b-grid-merged-cells{
  pointer-events:none;
}
.b-grid-merged-cells:hover{
  box-shadow:inset 0 0 0 1000px rgba(255, 204, 128, 0.1);
}
.b-grid-merged-cells.b-selected{
  box-shadow:inset 0 0 0 1000px rgba(255, 204, 128, 0.2);
}
.b-grid-merged-cells .b-grid-cell{
  position:sticky;
  top:0;
  flex:1;
  contain:none;
  overflow:visible;
}
html.b-export-root,
.b-export-root body{
  margin:0;
}

html.b-print-root,
.b-print-root body{
  margin:0;
  height:auto;
  overflow:unset;
  display:block;
  -webkit-print-color-adjust:exact !important;
  print-color-adjust:exact !important;
}

html.b-export-root{
  overflow:auto;
}

.b-export-root body,
.b-print-root body{
  position:relative;
}

.b-print-root .b-float-root,
.b-print-root .b-scrollbar-measure-element,
.b-print-root .b-grid-header-resize-handle{
  display:none !important;
}

.b-print:not(.b-safari) .b-page-wrap{
  page-break-after:always;
}

.b-export:not(.b-print){
  overflow:hidden;
  display:flex;
  flex-flow:column nowrap;
}
.b-export .b-gridbase{
  min-height:0 !important;
}
.b-export .b-export-content{
  display:flex;
  flex-direction:column;
}
.b-export:not(.b-print) .b-export-content{
  position:absolute;
  height:100%;
}
.b-export .b-export-body{
  flex:1;
}
.b-export .b-grid-header-container{
  margin-inline-end:0 !important;
  border-inline-end:none !important;
}
.b-export .b-grid-body-container.b-widget-scroller{
  overflow-y:hidden !important;
}
.b-export .b-grid-footer-container{
  padding-inline-end:0 !important;
}
.b-export .b-virtual-scrollers{
  display:none;
}
.b-export.b-visible-scrollbar .b-show-yscroll-padding > .b-yscroll-pad{
  display:none;
}
.b-export.b-multipage .b-export-content{
  width:100%;
  height:100%;
}
.b-export.b-multipage .b-export-body{
  overflow:hidden;
}

.b-export-header,
.b-export-header *{
  box-sizing:border-box;
}

@media print{
  .b-page-wrap{
    overflow:hidden;
  }
  .b-grid-body-container{
    contain:paint !important;
  }
}
.b-quick-hit-cell{
  background:#fffbdc;
}

.b-quick-hit-text{
  font-weight:300;
  background:#fffbdc;
}

.b-quick-hit-text{
  padding:0.3em 0;
}

.b-quick-hit-header{
  position:absolute;
  top:0.3em;
  left:0.3em;
  bottom:0.3em;
  right:0.3em;
  z-index:1;
}
.b-quick-hit-header.b-quick-hit-mode-grid{
  bottom:auto;
  height:3em;
  z-index:100;
  opacity:0.75;
}

.b-quick-hit-field{
  display:flex;
  justify-content:center;
  align-items:center;
  position:absolute;
  top:0;
  left:0;
  bottom:0;
  right:0;
  padding:0.5em 0;
  background:#fffbdc;
}

.b-quick-hit-cell-badge,
.b-quick-hit-header .b-quick-hit-badge{
  position:absolute;
  top:0.4em;
  right:0.4em;
  font-size:0.7em;
  line-height:0.7em;
}
.b-rtl .b-quick-hit-cell-badge,
.b-rtl .b-quick-hit-header .b-quick-hit-badge{
  right:auto;
  left:0.4em;
}

.b-quick-hit-header .b-quick-hit-badge{
  color:#dcc600;
}

.b-quick-hit-cell-badge{
  color:#dcc600;
}
.b-grid-splitter{
  z-index:5;
  position:relative;
  background:#b3b3b3;
  pointer-events:none;
  flex:0 0 1px;
}
@media (pointer: coarse){
  .b-grid-splitter{
    --splitter-button-size:1.3em;
  }
}
@media (pointer: fine){
  .b-grid-splitter{
    --splitter-button-size:1em;
  }
}
.b-grid-splitter .b-grid-splitter-inner{
  display:none;
}
.b-split .b-grid-splitter{
  pointer-events:all;
}
.b-grid-splitter, .b-grid-splitter.b-disabled{
  pointer-events:none;
}

.b-gridbase.b-split.b-rtl .b-grid-splitter-inner .b-grid-splitter-buttons .b-grid-splitter-button-collapse, .b-gridbase.b-split.b-rtl .b-grid-splitter-inner .b-grid-splitter-buttons .b-grid-splitter-button-expand{
  transform:scaleX(-1);
}
.b-gridbase.b-split.b-rtl .b-grid-splitter-inner .b-grid-splitter-buttons .b-grid-splitter-button-expand{
  justify-content:flex-end;
}
.b-gridbase.b-split.b-rtl .b-grid-splitter-inner .b-grid-splitter-buttons .b-grid-splitter-button-collapse{
  justify-content:flex-start;
}

.b-gridbase.b-split .b-grid-splitter:not(.b-disabled){
  flex:0 0 0.5em;
  background:#f0f0f0;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled) .b-grid-splitter-inner{
  display:flex;
  background:#f0f0f0;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-grid-splitter-collapsed{
  cursor:initial;
  flex:0 0 0.5em;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-grid-splitter-collapsed .b-grid-splitter-inner{
  width:0.5em;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-grid-splitter-collapsed:not(.b-grid-splitter-allow-collapse) .b-grid-splitter-button-collapse, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-grid-splitter-collapsed.b-grid-splitter-allow-collapse .b-grid-splitter-button-expand{
  visibility:hidden;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-grid-splitter-collapsed .b-grid-splitter-buttons{
  display:flex;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-left-only:not(.b-grid-splitter-collapsed) .b-grid-splitter-button-expand, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-right-only:not(.b-grid-splitter-collapsed) .b-grid-splitter-button-collapse{
  visibility:hidden;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled) .b-grid-splitter-buttons{
  display:none;
  position:absolute;
  transform:translateY(-50%);
  height:2.4em;
  width:2.4em;
  font-size:var(--splitter-button-size);
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled) .b-grid-splitter-button-collapse,
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled) .b-grid-splitter-button-expand{
  flex:1;
  cursor:pointer;
  background:#f0f0f0;
  display:flex;
  align-items:center;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled) .b-grid-splitter-button-collapse:hover .b-grid-splitter-button-icon,
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled) .b-grid-splitter-button-expand:hover .b-grid-splitter-button-icon{
  fill:#ffcc80;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled) .b-grid-splitter-button-collapse{
  border-top-left-radius:100% 50%;
  border-bottom-left-radius:100% 50%;
  justify-content:flex-end;
  padding-right:0.1em;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled) .b-grid-splitter-button-expand{
  border-top-right-radius:100% 50%;
  border-bottom-right-radius:100% 50%;
  padding-left:0.1em;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled) .b-grid-splitter-button-icon{
  cursor:pointer;
  fill:#999999;
  height:1.5em;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-hover, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-partner-splitter-hover, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-touching, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-moving{
  overflow:visible;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-hover .b-grid-splitter-inner, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-partner-splitter-hover .b-grid-splitter-inner, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-touching .b-grid-splitter-inner, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-moving .b-grid-splitter-inner{
  left:calc((10px - 0.5em) / -2);
  width:10px;
  transition:width 0.1s, left 0.1s;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-hover:not(.b-partner-splitter-hover) .b-grid-splitter-buttons, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-partner-splitter-hover:not(.b-partner-splitter-hover) .b-grid-splitter-buttons, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-touching:not(.b-partner-splitter-hover) .b-grid-splitter-buttons, .b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-moving:not(.b-partner-splitter-hover) .b-grid-splitter-buttons{
  display:flex;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-grid-splitter-collapsed:nth-child(2):hover:not(.b-moving) .b-grid-splitter-inner{
  left:0;
}
.b-gridbase.b-split .b-grid-splitter:not(.b-disabled) .b-grid-splitter-inner{
  width:0.5em;
  position:absolute;
  top:0;
  left:0;
  bottom:0;
  flex-direction:column;
  align-items:center;
}

.b-gridbase.b-split:not(.b-grid-splitter-no-drag) .b-grid-splitter:not(.b-disabled, .b-grid-splitter-collapsed) .b-grid-splitter-inner{
  cursor:col-resize;
}

.b-grid-header-scroller.b-collapsed,
.b-grid-footer-scroller.b-collapsed,
.b-virtual-scroller.b-collapsed{
  width:0;
  min-width:0 !important;
}

.b-splitter-touch-area{
  width:2em;
  left:calc((2em - 0.5em) / -2);
  position:absolute;
  top:0;
  height:100%;
}

.b-splitter-button-touch-area{
  height:3em;
  top:-0.3em;
  width:2em;
  position:absolute;
}

.b-grid-splitter-button-collapse .b-splitter-button-touch-area{
  left:-0.9em;
}

.b-grid-splitter-button-expand .b-splitter-button-touch-area{
  right:-0.9em;
}

.b-draghelper-active .b-gridbase.b-split .b-grid-splitter{
  pointer-events:none !important;
}

.b-hide-splitter-buttons.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-grid-splitter-collapsed .b-grid-splitter-buttons, .b-hide-splitter-buttons.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-hover .b-grid-splitter-buttons, .b-hide-splitter-buttons.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-touching .b-grid-splitter-buttons, .b-hide-splitter-buttons.b-gridbase.b-split .b-grid-splitter:not(.b-disabled).b-moving .b-grid-splitter-buttons{
  display:none;
}
.b-row-drop-indicator{
  display:none;
  pointer-events:none;
}

.b-row-reordering .b-row-drop-indicator{
  position:absolute;
  display:block;
  left:0;
  top:-1px;
  width:100%;
  height:2px;
  background-color:#64b5f6;
  z-index:2000;
}
.b-row-reordering .b-row-drop-indicator.b-drag-invalid{
  background-color:#ef9a9a;
}
.b-row-reordering .b-row-reorder-proxy.b-dragging{
  transition:margin-top 0.2s, opacity 0.2s;
  background:transparent;
  opacity:0.5;
}
.b-row-reordering .b-row-reorder-proxy.b-dragging .b-grid-row{
  width:100%;
}
.b-row-reordering .b-row-reorder-proxy.b-dragging .b-grid-row, .b-row-reordering .b-row-reorder-proxy.b-dragging .b-row-dragging-multiple{
  transition:transform 0.2s, background-color 0.2s;
  box-shadow:1px 1px 4px rgba(0, 0, 0, 0.2);
  background:#fff;
}
.b-row-reordering .b-row-reorder-proxy.b-dragging .b-row-dragging-multiple{
  top:-0.3em;
  left:0.3em;
  z-index:-1;
}
.b-row-reordering .b-row-reorder-proxy.b-dragging.b-drag-invalid .b-grid-row{
  border:none;
  background:rgba(239, 154, 154, 0.2);
}
.b-row-reordering .b-grid-body-container{
  z-index:4;
}
.b-row-reordering .b-drag-original{
  opacity:0.3;
}
.b-row-reordering .b-grid-row.b-grid-group-collapsed.b-row-reordering-target{
  background-color:rgba(255, 204, 128, 0.1);
}
.b-row-reordering .b-grid-body-container .b-grid-subgrid .b-row-reordering-target{
  box-shadow:0 1px 0 0 #64b5f6 inset, 0 -1px 0 0 #64b5f6 inset;
  z-index:9999;
}

.b-row-reorder-grip{
  display:grid;
  grid-template-columns:auto 1fr;
}
.b-row-reorder-grip.b-grid-cell-align-end{
  justify-items:end;
}
.b-row-reorder-grip.b-grid-cell-align-end:before{
  margin-inline-end:auto;
}
.b-row-reorder-grip.b-grid-cell-align-right{
  justify-items:right;
}
.b-row-reorder-grip.b-grid-cell-align-center{
  justify-items:center;
}
.b-row-reorder-grip:before{
  display:flex;
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  align-self:stretch;
  align-items:center;
  content:"\f58e";
  cursor:move;
  padding-inline:0.85em;
  margin-inline-start:-0.5em;
  font-size:0.8em;
}

.b-row-reorder-proxy .b-row-proxy-copy{
  display:none;
  height:1em;
  width:1em;
  align-items:center;
  justify-content:center;
  position:absolute;
  inset-inline-start:-0.5em;
  top:-0.5em;
  box-shadow:1px 1px 5px #d9d9d9;
  border-radius:50%;
  padding:0.75em;
  background:#fff;
  z-index:20000;
}

.b-row-reorder-proxy.b-drag-copy .b-row-proxy-copy{
  display:flex;
}
.b-gridbase.b-rowresize .b-grid-row.b-over-resize-handle, .b-gridbase.b-rowresize .b-grid-row.b-over-resize-handle *, .b-gridbase.b-rowresize .b-grid-row.b-over-resize-handle *:before, .b-gridbase.b-rowresize .b-grid-row.b-resize-handle{
  cursor:row-resize !important;
}
.b-gridbase.b-rowresize .b-grid-row.b-resizing{
  z-index:2;
}
.b-gridbase.b-rowresize.b-row-resizing .b-grid-row{
  pointer-events:none;
}
.b-gridbase.b-rowresize.b-row-resizing *{
  cursor:row-resize !important;
}
.b-search-hit-cell{
  background:#fffbdc;
}

.b-search-hit-text{
  font-weight:300;
  background:#fffbdc;
  padding:0.3em 0;
}

.b-search-hit-field{
  position:absolute;
  top:0;
  left:0;
  bottom:0;
  right:0;
  padding:0.5em 0;
  background:#fffbdc;
}

.b-search-hit-cell-badge{
  position:absolute;
  top:0.4em;
  right:0.4em;
  font-size:0.7em;
  line-height:0.7em;
  color:#dcc600;
}
.b-gridbase.b-sort .b-grid-header .b-sort-icon{
  display:contents;
}
.b-gridbase.b-sort .b-grid-header.b-sort .b-grid-header-text{
  color:#565656;
}
.b-gridbase.b-sort .b-grid-header.b-sort .b-grid-header-text .b-sort-icon::before{
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  color:#cccccc;
  transition:transform 0.3s, color 0.2s;
  transform:none;
  background:inherit;
  content:"\f062";
}
.b-gridbase.b-sort .b-grid-header.b-sort .b-grid-header-text[data-sort-index]::before{
  content:attr(data-sort-index);
  position:relative;
  top:1em;
  color:#999999;
  z-index:30;
  font-size:0.6em;
  order:1;
}
.b-gridbase.b-sort .b-grid-header.b-sort.b-desc .b-grid-header-text .b-sort-icon::before{
  transform:rotate(180deg);
}

.b-gridbase.b-sort:not(.b-column-resizing, .b-row-reordering, .b-dragging-header) .b-grid-header-container .b-grid-header.b-sortable.b-depth-0:hover:not(.b-sort):not(.b-group) .b-grid-header-text .b-sort-icon::before, .b-gridbase.b-sort:not(.b-column-resizing, .b-row-reordering, .b-dragging-header) .b-grid-header-container .b-grid-header.b-sortable.b-depth-0:focus:not(.b-sort):not(.b-group) .b-grid-header-text .b-sort-icon::before{
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  color:#ececec;
  transition:transform 0.3s, color 0.2s;
  transform:none;
  background:inherit;
  content:"\f15e";
}

.b-split-container{
  display:flex;
  width:100%;
  height:100%;
  position:relative;
  overflow:hidden;
}
.b-split-container.b-split-horizontal, .b-split-container.b-split-both{
  flex-direction:column;
}
.b-split-container.b-split-vertical{
  flex-direction:row;
}
.b-split-container .b-split-top,
.b-split-container .b-split-bottom{
  display:flex;
  flex:1;
}
.b-split-container > .b-gridbase,
.b-split-container > div > .b-gridbase{
  flex:1;
  flex-basis:0;
  height:unset !important;
}

.b-gridbase.b-locked-rows{
  z-index:1;
}
.b-gridbase.b-locked-rows [aria-rowcount="1"] .b-virtual-scrollers{
  flex-basis:0;
}
.b-gridbase.b-locked-rows:has(.b-widget-scroller.b-grid-empty){
  overflow:visible;
}
.b-gridbase.b-locked-rows:has(.b-widget-scroller.b-grid-empty) .b-grid-body-wrap, .b-gridbase.b-locked-rows:has(.b-widget-scroller.b-grid-empty) .b-grid-panel-body{
  overflow:visible;
}
.b-gridbase.b-locked-rows:not(.b-grid-empty){
  border-bottom:1px solid #d9d9d9;
}
.b-gridbase.b-locked-rows:not(.b-grid-empty):not(.b-split){
  border-bottom-color:#b3b3b3;
}
.b-gridbase.b-locked-rows:not(.b-grid-empty).b-split{
  border-bottom:2px solid #f0f0f0;
}
.b-gridbase.b-locked-rows:not(:has(.b-grid-row)){
  border-bottom-width:0;
}
.b-gridbase.b-locked-rows.b-grid-empty .b-widget-scroller{
  min-height:0;
}
.b-gridbase.b-locked-rows .b-grid-row.b-last{
  border-bottom:none;
}
.b-grid-header.b-group .b-grid-header-text .b-sort-icon::after{
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  color:#ffcc80;
  transition:transform 0.3s, color 0.2s;
  transform:none;
  background:inherit;
  content:"\f012";
  transform:rotate(90deg);
}
.b-grid-header.b-group.b-desc .b-grid-header-text .b-sort-icon::after{
  transform:rotate(270deg) scaleX(-1);
}

.b-group-row{
  background:#fff;
}
.b-group-row .b-grid-cell{
  border-inline-end-color:transparent;
}
.b-group-row .b-group-title.b-grid-cell-align-right, .b-group-row .b-group-title.b-grid-cell-align-end{
  justify-content:flex-end;
}
.b-group-row .b-group-title.b-grid-cell-align-center{
  justify-content:flex-start;
}

.b-grid-cell.b-group-title{
  color:#757575;
  overflow:visible;
  contain:size layout style;
  font-weight:500;
  text-transform:none;
}
.b-firefox .b-grid-cell.b-group-title{
  contain:size layout;
}

.b-group-state-icon{
  margin-inline-end:0.5em;
  cursor:pointer;
  color:#757575;
}

.b-stripe .b-grid-row.b-group-row,
.b-grid-row.b-group-row{
  border-bottom:2px solid #cccccc;
  cursor:pointer;
}

.b-gridbase.b-group .b-grid-header .b-sort-icon{
  display:contents;
}

.b-cut-row{
  opacity:0.4;
}

.b-stickycells .b-grid-sticky-row{
  position:absolute;
  top:0;
  left:0;
  min-width:auto;
  border:0 none;
  padding:0;
  contain:initial;
}
.b-stickycells .b-grid-sticky-row .b-grid-cell{
  width:min-content;
  contain:initial;
  padding:0;
  border:0 none;
}
.b-stickycells .b-sticky-cells-current-top-row .b-sticky-content-el{
  visibility:hidden;
}
.b-stickycells .b-sticky-cells-current-top-row.b-not-enough-height .b-sticky-content-el{
  visibility:visible;
  align-self:flex-end;
}
.b-stripe .b-grid-row.b-odd{
  background-color:rgba(250, 250, 250, 0.8);
}
.b-stripe .b-grid-row.b-even{
  background-color:rgba(255, 255, 255, 0.8);
}
.b-stripe .b-grid-row{
  border-bottom:none;
}
.b-stripe .b-grid-row.b-selected{
  background-color:rgba(255, 204, 128, 0.2);
}
.b-stripe .b-grid-row.b-selected .b-grid-cell.b-selected{
  background-color:rgba(255, 204, 128, 0.2);
}

.b-stripe:focus .b-grid-row.b-selected{
  background-color:rgba(255, 204, 128, 0.1);
}
.b-summary-wrap{
  flex:0 0 100%;
  display:grid;
  gap:0.1em 1em;
  grid-template-columns:auto 1fr;
  align-items:center;
}
.b-summary-wrap .b-grid-summary-value{
  overflow:hidden;
  text-overflow:ellipsis;
}
.b-summary-wrap .b-grid-summary-value.b-nolabel{
  grid-column:span 2/span 2;
}

.b-summary-disabled .b-grid-footer-container{
  display:none;
}
.b-generated-parent.b-readonly .b-grid-cell{
  color:#222;
}
.b-rowexpander-body{
  display:flex;
  border-top:1px solid #d9d9d9;
  left:0;
  right:0;
  color:#222;
  font-weight:300;
}
.b-rowexpander-body, .b-rowexpander-body.b-no-resizeobserver.b-resize-monitored{
  position:absolute;
}
.b-grid-vertical-scroller > .b-rowexpander-body{
  z-index:100;
  transition:height 0.3s, top 0.3s;
  overflow:hidden;
}

.b-rowexpander-loading{
  justify-content:center;
  align-items:center;
  font-size:1.2em;
  display:flex;
  width:100%;
}
.b-grid-row > .b-rowexpander-loading{
  position:absolute;
}
.b-rowexpander-loading .b-icon-spinner{
  margin-inline-end:0.5em;
}

.b-gridbase .b-grid-row [data-column=expanderActionColumn] .b-icon{
  transition:transform 0.15s ease-in-out;
  font-size:1em;
  transform:rotate(180deg);
}

.b-rowexpander-disabled [data-column=expanderActionColumn]{
  opacity:0.2;
  pointer-events:none;
}

.b-rowexpander.b-gridbase.b-animating.b-rowexpander-animating.b-autoheight .b-grid-body-container, .b-rowexpander.b-gridbase.b-animating.b-rowexpander-animating.b-autoheight .b-grid-vertical-scroller{
  transition:height 0.3s;
  overflow-y:hidden !important;
}
.b-rowexpander.b-gridbase.b-animating.b-rowexpander-animating.b-autoheight .b-yscroll-pad{
  display:none;
}
.b-rowexpander.b-gridbase.b-animating.b-rowexpander-animating .b-grid-row{
  transition:height 0.3s, transform 0.3s;
}

.b-gridbase .b-rowexpander-row-expanded:not(.b-row-is-collapsing) [data-column=expanderActionColumn] .b-icon-collapse-left{
  transform:rotate(270deg);
}
.b-gridbase .b-rowexpander-row-expanded:not(.b-row-is-collapsing) [data-column=expanderActionColumn] .b-icon-collapse-right{
  transform:rotate(90deg);
}

.b-grid-header[data-column=expanderActionColumn] .b-grid-header-text-content:empty{
  display:none;
}
.b-exportdialog.b-popup .b-bottom-toolbar > button{
  flex:1;
  margin-inline-end:0.5em;
}
.b-exportdialog.b-popup .b-bottom-toolbar > button:last-child{
  margin-inline-end:0;
}
.b-grid-footer-container{
  background-color:#f9f9f9;
  color:#222;
  outline:1px solid #cccccc;
  z-index:2;
}
.b-grid-footer-container.b-hidden{
  display:none;
}

.b-grid-footers{
  display:inline-flex;
  align-items:stretch;
  height:100%;
  white-space:nowrap;
  line-height:initial;
  overscroll-behavior:none;
}

.b-grid-footer{
  display:flex;
  border-inline-end:1px solid #cccccc;
  flex-shrink:0;
  align-items:stretch;
  padding:0.5em;
}
.b-no-column-lines .b-grid-footer{
  border-inline-end:none;
}

.b-grid-footer:last-child{
  border-inline-end-color:transparent;
}

.b-grid-footer-align-start,
.b-grid-footer-align-left{
  text-align:start;
}

.b-grid-footer-align-center{
  text-align:center;
}

.b-grid-footer-align-end,
.b-grid-footer-align-right{
  text-align:end;
}
.b-gridbase{
  position:relative;
}
.b-gridbase.b-outer{
  height:100%;
}
.b-gridbase.b-autoheight{
  height:auto;
  flex:none;
}
.b-gridbase.b-autoheight .b-grid-vertical-scroller{
  position:relative;
}
.b-gridbase .b-grid-panel-body:not(.b-autoheight) > .b-grid-body-container{
  flex:1 1 0;
  contain:strict;
}
.b-gridbase .b-grid-panel-body:not(.b-autoheight) > .b-grid-body-container > .b-grid-vertical-scroller{
  min-height:100%;
}
.b-gridbase.b-grid-translate .b-grid-row, .b-gridbase.b-grid-translate3d .b-grid-row{
  transform-style:flat;
}
.b-gridbase.b-enable-sticky .b-sticky-cell,
.b-gridbase.b-enable-sticky .b-grid-subgrid,
.b-gridbase.b-enable-sticky .b-grid-vertical-scroller,
.b-gridbase.b-enable-sticky .b-grid-row{
  overflow:visible !important;
}
.b-gridbase.b-no-column-lines .b-grid-row .b-grid-cell,
.b-gridbase.b-no-column-lines .b-grid-header{
  border-inline-end-color:transparent;
}
.b-gridbase.b-no-row-lines .b-grid-row{
  border-block:none;
}
.b-gridbase.b-fill-last-column .b-grid-subgrid:not(.b-has-flex):not(.b-horizontal-overflow) .b-grid-cell:last-child,
.b-gridbase.b-fill-last-column .b-grid-subgrid:not(.b-has-flex):not(.b-horizontal-overflow) .b-grid-row.b-rowexpander-row-expanded .b-grid-cell:nth-last-child(2),
.b-gridbase.b-fill-last-column .b-grid-footers:not(.b-has-flex):not(.b-horizontal-overflow) .b-grid-footer:last-child,
.b-gridbase.b-fill-last-column .b-grid-headers:not(.b-has-flex):not(.b-horizontal-overflow) .b-last-parent,
.b-gridbase.b-fill-last-column .b-grid-headers:not(.b-has-flex):not(.b-horizontal-overflow) .b-last-leaf{
  flex-grow:1;
}
.b-gridbase:not(.b-masked).b-grid-empty{
  min-height:5em;
}
.b-gridbase:not(.b-masked).b-grid-empty .b-empty-text{
  display:block;
  color:#565656;
  background-color:#fff;
  padding:1em;
  position:absolute;
  pointer-events:none;
  z-index:100;
}
.b-gridbase.b-grid-notextselection .b-grid-cell{
  -webkit-user-select:none;
  user-select:none;
}
.b-gridbase.b-notransition .b-grid-cell *{
  transition:none !important;
}
.b-gridbase.b-disabled{
  opacity:0.5;
}

.b-grid-panel-body{
  position:relative;
  flex:1;
  display:flex;
  flex-direction:column;
  overflow:hidden;
  background-color:#fff;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}

.b-gridbase:not(.b-animating) .b-grid-row:not(.b-grid-row-updating) .b-grid-cell:not(.b-widget-cell.b-focused) *{
  transition:none;
}

.b-gridbase.b-autoheight.b-grid-empty .b-grid-vertical-scroller{
  min-height:100%;
}

.b-grid-body-container{
  position:relative;
}
.b-grid-body-container .b-empty-text{
  display:none;
}
.b-grid-body-container:focus{
  outline:none;
}
.b-grid-body-container:focus .b-grid-row.b-selected{
  background-color:rgba(255, 204, 128, 0.1);
}

.b-playing-demo .b-grid-body-container{
  overflow:hidden !important;
}

.b-grid-vertical-scroller{
  position:absolute;
  display:flex;
  flex-flow:row nowrap;
  overflow:hidden;
  width:100%;
  align-items:stretch;
}

.b-grid-row-container{
  display:inline-block;
  min-width:100%;
}

.b-virtual-scrollers{
  z-index:3;
  display:flex;
  flex-direction:row;
  contain:paint style layout;
}
.b-firefox .b-virtual-scrollers{
  contain:paint layout;
}
.b-overlay-scrollbar.b-firefox.b-windows .b-virtual-scrollers{
  pointer-events:auto;
}
.b-overlay-scrollbar.b-firefox.b-windows .b-virtual-scrollers .b-virtual-scroller{
  pointer-events:auto;
}
.b-overlay-scrollbar .b-virtual-scrollers{
  position:absolute;
  left:0;
  right:0;
  bottom:0;
  pointer-events:none;
}
.b-overlay-scrollbar .b-virtual-scrollers .b-virtual-scroller{
  height:16px;
  opacity:0;
  pointer-events:none;
}
.b-virtual-scrollers .b-virtual-scroller{
  overflow-x:scroll;
  overflow-y:hidden;
}
.b-virtual-scrollers .b-virtual-width{
  height:1px;
}

.b-overlay-scrollbar .b-virtual-scroller:hover,
.b-overlay-scrollbar .b-virtual-scroller.b-show-virtual-scroller{
  pointer-events:all;
  opacity:1;
  transition:opacity 0.5s;
}
.b-grid-footer-container,
header.b-grid-header-container{
  display:flex;
  flex-direction:row;
}

.b-grid-header-container{
  border-bottom:1px solid #cccccc;
  position:relative;
}

.b-grid-footer-scroller,
.b-grid-header-scroller{
  overflow:hidden;
  position:relative;
  display:flex;
}

.b-grid-footers,
.b-grid-headers{
  z-index:2;
  contain:paint style layout;
  flex:1 1 auto;
}
.b-firefox .b-grid-footers,
.b-firefox .b-grid-headers{
  contain:paint layout;
}

.b-no-transitions .b-grid-row{
  transition:none !important;
}
:root{
  --row-splice-duration:300ms;
}

.b-grid-row{
  display:flex;
  flex-direction:row;
  align-items:stretch;
  position:absolute;
  left:0;
  overflow:hidden;
  border-bottom:1px solid #d9d9d9;
  height:45px;
  contain:layout;
}
@media screen{
  .b-grid-row.b-export-row{
    transform:translate(-99999px, -99999px);
  }
}
.b-grid-subgrid .b-grid-row{
  min-width:100%;
}
.b-grid-subgrid.b-horizontal-overflow .b-grid-row{
  min-width:max(var(--total-column-width, 100%), 100%);
}
.b-grid-row.b-aborting{
  transition:background-color 0.2s, transform 0.3s;
}
.b-splicing-rows .b-grid-row:is(.b-adding, .b-removing, .b-repositioning){
  transition:transform var(--row-splice-duration), top var(--row-splice-duration);
  z-index:-1;
  pointer-events:none;
}
.b-rtl .b-grid-row{
  left:auto;
  right:0;
}
.b-grid-row.b-selected{
  background-color:rgba(255, 204, 128, 0.2);
}
.b-grid-row.b-selected .b-grid-cell.b-checkbox-selection{
  background-color:#e4e4e4;
}
.b-grid-row.b-removing{
  animation:row-removing var(--row-splice-duration) forwards;
}
.b-grid-row.b-adding{
  animation:row-adding var(--row-splice-duration) forwards;
}
.b-grid-row.b-fa{
  display:flex;
}
.b-grid-row.b-fa::before{
  content:none;
}
.b-grid-row.b-row-placeholder .b-grid-cell{
  color:transparent;
  clip-path:inset(1em 1em 1em 1em);
}
.b-grid-row.b-row-placeholder .b-grid-cell > *:not(.b-editor){
  display:none;
}
.b-grid-row.b-row-placeholder .b-grid-cell::after{
  content:"";
  background:#f7f7f7;
  position:absolute;
  inset:1em;
  border-radius:5px;
}

.b-grid-refreshing .b-grid-row{
  transition:none;
}

.b-grid-row:not(.b-selected) .b-grid-cell.b-selected{
  background-color:rgba(255, 204, 128, 0.15);
}
.b-grid-row:not(.b-selected) .b-grid-cell.b-selected.b-hover{
  background-color:rgba(255, 204, 128, 0.2);
}
.b-grid-row:not(.b-selected) .b-grid-cell.b-selected.b-checkbox-selection{
  background-color:#e4e4e4;
}

.b-grid-cell{
  position:relative;
  display:flex;
  align-items:center;
  color:#222;
  font-weight:300;
  padding:0 0.5em;
  overflow:hidden;
  white-space:nowrap;
  flex-shrink:0;
  text-overflow:ellipsis;
  border-inline-end:1px solid #d9d9d9;
  transform-style:flat;
  width:0;
  contain:strict;
}
.b-fill-last-column .b-grid-cell:last-child, .b-fill-last-column.b-rowexpander-row-expanded .b-grid-cell:nth-last-child(2){
  border-inline-end-color:transparent;
}
.b-grid-cell > i:not(:last-child){
  margin-inline-end:0.5em;
}
.b-show-dirty .b-grid-cell.b-cell-dirty:not(.b-editing):before, .b-show-dirty-during-edit .b-grid-cell.b-cell-dirty:before{
  content:"";
  clip-path:polygon(0% 50%, 0% 0%, 50% 0%);
  z-index:2;
  position:absolute;
  top:0;
  left:1px;
  background-color:red;
  width:1.2em;
  height:1.2em;
}
.b-rtl .b-show-dirty .b-grid-cell.b-cell-dirty:not(.b-editing):before, .b-rtl .b-show-dirty-during-edit .b-grid-cell.b-cell-dirty:before{
  left:auto;
  right:0;
  clip-path:polygon(100% 50%, 50% 0%, 100% 0%);
}
.b-gridbase:not(.b-panel-ui-plain) .b-grid-cell.b-checkbox-selection{
  background-color:#f1f1f1;
}
.b-grid-cell:focus, .b-grid-cell:focus-within{
  transition:none !important;
  outline:none;
}
.b-grid-cell.b-auto-height{
  white-space:normal;
}
.b-grid-cell.b-measuring-auto-height{
  contain:paint style layout;
  align-self:baseline;
}
.b-firefox .b-grid-cell.b-measuring-auto-height{
  contain:paint layout;
}

body.b-using-keyboard .b-grid-cell:focus, body.b-using-keyboard .b-grid-cell:focus-within, body.b-using-keyboard .b-grid-header:focus, body.b-using-keyboard .b-grid-header:focus-within,
.b-outer.b-using-keyboard .b-grid-cell:focus,
.b-outer.b-using-keyboard .b-grid-cell:focus-within,
.b-outer.b-using-keyboard .b-grid-header:focus,
.b-outer.b-using-keyboard .b-grid-header:focus-within{
  outline:2px solid #94ccf9;
  outline-offset:-2px;
}

.b-readonly > .b-grid-cell{
  color:#b3b3b3;
}

.b-grid-cell-align-right, .b-grid-cell-align-end{
  justify-content:flex-end;
}

.b-grid-cell-align-center{
  justify-content:center;
}

.b-grid-subgrid{
  position:relative;
  overscroll-behavior:none auto;
}
.b-grid-subgrid.b-grid-subgrid-collapsed{
  width:0;
  min-width:0 !important;
  overflow:hidden !important;
}

.b-playing-demo .b-grid-subgrid{
  overflow:hidden !important;
}

.b-animating .b-grid-header-scroller,
.b-animating .b-grid-subgrid{
  transition-property:width, flex, min-width;
  transition-duration:0.3s;
}

.b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-hide-row-hover) .b-grid-row:not(.b-group-row).b-hover{
  background-color:rgba(255, 204, 128, 0.1);
}
.b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-hide-row-hover) .b-grid-row:not(.b-group-row).b-hover.b-selected{
  background-color:rgba(255, 204, 128, 0.2);
}
.b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-hide-row-hover) .b-grid-row:not(.b-group-row).b-hover .b-grid-cell{
  background-color:rgba(255, 204, 128, 0.1);
}
.b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-hide-row-hover) .b-grid-row:not(.b-group-row).b-hover .b-grid-cell.b-checkbox-selection{
  background-color:#ececec;
}
.b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-hide-row-hover) .b-grid-row:not(.b-group-row) .b-grid-cell.b-hover{
  background-color:rgba(255, 204, 128, 0.1);
}
.b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-hide-row-hover) .b-grid-row:not(.b-group-row) .b-grid-cell.b-hover.b-selected{
  background-color:rgba(255, 204, 128, 0.2);
}
.b-gridbase:not(.b-moving-splitter) .b-grid-subgrid:not(.b-hide-row-hover) .b-grid-row:not(.b-group-row) .b-grid-cell.b-hover.b-checkbox-selection{
  background-color:#ececec;
}

@keyframes row-removing{
  from{
    opacity:1;
  }
  to{
    opacity:0.3;
    height:0;
  }
}
@keyframes row-adding{
  from{
    opacity:0.3;
    height:0;
  }
  to{
    opacity:1;
  }
}
@keyframes fadeInOpacity{
  0%{
    opacity:0;
  }
  100%{
    opacity:1;
  }
}
.b-grid-header-container{
  background-color:#f9f9f9;
  z-index:4;
}
.b-gridbase.b-panel-ui-plain .b-grid-header-container{
  background-color:transparent;
}
.b-gridbase.b-panel-ui-plain .b-grid-header-container .b-header{
  box-shadow:none;
}
.b-grid-header-container.b-hidden{
  position:absolute;
  clip-path:inset(0 0 100% 0);
  contain:strict;
}

.b-grid-headers{
  display:inline-flex;
  flex-flow:row nowrap;
  align-items:stretch;
  line-height:initial;
  position:relative;
  overscroll-behavior:none;
}

.b-grid-header-align-right > .b-grid-header-text > .b-grid-header-text-content,
.b-grid-header-align-end > .b-grid-header-text > .b-grid-header-text-content{
  text-align:end;
}

.b-grid-header-align-center > .b-grid-header-text > .b-grid-header-text-content{
  text-align:center;
}

.b-gridbase:not(.b-column-resizing, .b-row-reordering, .b-dragging-header) .b-grid-header-container .b-depth-0:focus, .b-gridbase:not(.b-column-resizing, .b-row-reordering, .b-dragging-header) .b-grid-header-container .b-depth-0.b-check-header-with-checkbox:focus-within{
  background-color:white;
}
.b-gridbase:not(.b-column-resizing, .b-row-reordering, .b-dragging-header) .b-grid-header-container .b-depth-0:hover, .b-gridbase:not(.b-column-resizing, .b-row-reordering, .b-dragging-header) .b-grid-header-container .b-depth-0.b-hover{
  background-color:white;
}

.b-gridbase.b-columnresize .b-grid-header-resizable:not(.b-last-leaf){
  overflow:visible;
}
.b-gridbase.b-columnresize .b-grid-header-resizable .b-grid-header-resize-handle{
  position:absolute;
  top:0;
  right:-5px;
  bottom:0;
  width:10px;
  background-color:transparent;
  z-index:3;
  cursor:col-resize;
  display:block;
}

.b-gridbase.b-columnresize.b-rtl .b-grid-header-resizable .b-grid-header-resize-handle{
  left:-5px;
  right:auto;
}

.b-touch-events .b-gridbase.b-columnresize .b-grid-header-resizable .b-grid-header-resize-handle{
  right:-10px;
  width:20px;
}
.b-rtl .b-touch-events .b-gridbase.b-columnresize .b-grid-header-resizable .b-grid-header-resize-handle{
  left:-10px;
  right:auto;
}

.b-fill-last-column .b-grid-headers .b-last-parent,
.b-fill-last-column .b-grid-headers .b-last-leaf{
  border-inline-end-color:transparent;
}
.b-fill-last-column .b-grid-headers .b-last-parent > .b-grid-header-text,
.b-fill-last-column .b-grid-headers .b-last-leaf > .b-grid-header-text{
  border-inline-end-color:transparent;
}

.b-grid-header-text{
  -webkit-user-select:none;
  user-select:none;
  padding:1em 0 1em 0;
  white-space:nowrap;
  position:relative;
  font-weight:inherit;
  display:flex;
  align-items:center;
  overflow:hidden;
  gap:0.5em;
}
.b-grid-header-text > .b-grid-header-text-content{
  overflow:hidden;
  text-overflow:ellipsis;
  flex:1 1 0;
  width:0;
  font-size:1em;
}
.b-grid-header-text > .b-grid-header-text-content > i{
  margin-inline-end:0.5em;
}

.b-grid-header-children{
  display:flex;
  flex-direction:column;
}
.b-grid-header-children > *{
  width:inherit;
}

.b-grid-header{
  -webkit-user-select:none;
  user-select:none;
  display:flex;
  flex-direction:column;
  align-items:stretch;
  justify-content:center;
  flex-shrink:0;
  position:relative;
  overflow:hidden;
  color:#565656;
  outline:none;
  border-inline-end:1px solid #cccccc;
  text-transform:none;
}
.b-grid-header:not(.b-depth-0){
  overflow:visible;
}
.b-grid-header.b-depth-0{
  padding-inline:0.5em 0.5em;
  width:0;
}
.b-grid-header.b-depth-0 .b-grid-header-text{
  border-bottom:none;
  transition:background-color 0.2s;
  flex-direction:row;
}
.b-grid-header.b-sortable{
  cursor:pointer;
}
.b-grid-header.b-grid-header-parent{
  border-inline-end:none;
  flex-basis:auto;
  padding-inline:0;
}
.b-grid-header.b-grid-header-parent > .b-grid-header-text{
  padding-inline:0.5em 0.5em;
  border-inline-end:1px solid #cccccc;
  flex:1 1 auto;
}
.b-grid-header .b-grid-header-resize-handle{
  display:none;
}

.b-grid-header-parent > .b-grid-header-children{
  border-top:1px solid #cccccc;
  flex-flow:row nowrap;
  flex:1 1 auto;
}

.b-grid-header .b-button[data-ref=collapseExpand]{
  min-width:2em;
  min-height:2em;
  position:absolute;
  inset-inline-end:0.5em;
  padding:0;
}
.b-grid-header .b-button[data-ref=collapseExpand]:hover{
  background-color:transparent;
}

.b-groupbar{
  display:flex;
  flex:1;
  align-self:center;
  gap:2em;
  margin-inline-start:0.3em;
  height:2.5em;
}
.b-groupbar .b-chip{
  position:relative;
  padding-block:0.5em;
  padding-inline:1em;
  outline:none !important;
  cursor:pointer;
}
.b-groupbar .b-chip .b-close-icon{
  margin-inline-start:1em;
}
.b-groupbar .b-chip:not(:last-child):after{
  content:"\f105";
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  font-style:normal;
  text-decoration:none;
  position:absolute;
  right:-1.25em;
}
.b-rtl .b-groupbar .b-chip:not(:last-child):after{
  content:"\f104";
  right:unset;
  left:-1.25em;
}
.b-groupbar.b-chips-closable .b-chip{
  padding-inline-end:0.6em;
}
.b-groupbar .b-chip.b-drop-target{
  width:8em;
  opacity:0.5;
}
.b-groupbar .b-chip.b-drop-target .b-icon{
  display:none;
}

.b-grid-reordering-columns-with-groupbar.b-grid-header.b-drag-proxy{
  border-radius:1em;
  min-width:10em !important;
  padding-inline:1em;
}
.b-grid-reordering-columns-with-groupbar.b-grid-header.b-drag-proxy .b-grid-header-text{
  padding:0.5em 0;
}
.b-grid-reordering-columns-with-groupbar.b-grid-header.b-drag-proxy, .b-grid-reordering-columns-with-groupbar.b-grid-header.b-drag-proxy .b-grid-header-text-content{
  width:auto !important;
}

/*# sourceMappingURL=grid.classic-light.thin.css.map */