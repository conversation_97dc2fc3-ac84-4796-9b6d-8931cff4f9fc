var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Base from "../Base.js";
import ArrayHelper from "../helper/ArrayHelper.js";
import StringHelper from "../helper/StringHelper.js";
import VersionHelper from "../helper/VersionHelper.js";
import BrowserHelper from "../helper/BrowserHelper.js";
import FunctionHelper from "../helper/FunctionHelper.js";
import Objects from "../helper/util/Objects.js";
const { isArray } = Array, { hasOwnProperty } = Object.prototype, specialProperties = {
  thisObj: 1,
  detachable: 1,
  once: 1,
  detacher: 1,
  prio: 1,
  args: 1,
  expires: 1,
  buffer: 1,
  throttle: 1,
  name: 1,
  $internal: 1
}, priorityComparator = (a, b) => b.prio - a.prio;
var Events_default = (Target) => class Events extends (Target || Base) {
  constructor() {
    super(...arguments);
    __publicField(this, "eventsSuspended", null);
  }
  static get $name() {
    return "Events";
  }
  //region Events
  /**
   * Fires before an object is destroyed.
   * @event beforeDestroy
   * @param {Core.Base} source The Object that is being destroyed.
   */
  /**
   * Fires when an object is destroyed.
   * @event destroy
   * @param {Core.Base} source The Object that is being destroyed.
   */
  /**
   * Fires when any other event is fired from the object.
   *
   * **Note**: `catchAll` is fired for both public and private events. Please rely on the public events only.
   * @event catchAll
   * @param {Object} event The Object that contains event details
   * @param {String} event.type The type of the event which is caught by the listener
   * @typings event -> {{[key: string]: any, type: string}}
   */
  //endregion
  static get declarable() {
    return [
      /**
       * The list of deprecated events as an object, where `key` is an event name which is deprecated and
       * `value` is an object which contains values for
       * {@link Core.helper.VersionHelper#function-deprecate-static VersionHelper}:
       * - product {String} The name of the product;
       * - invalidAsOfVersion {String} The version where the offending code is invalid (when any compatibility
       *   layer is actually removed);
       * - message {String} Warning message to show to the developer using a deprecated API;
       *
       * For example:
       *
       * ```javascript
       * return {
       *     click : {
       *         product            : 'Grid',
       *         invalidAsOfVersion : '1.0.0',
       *         message            : 'click is deprecated!'
       *     }
       * }
       * ```
       *
       * @name deprecatedEvents
       * @returns {Object}
       * @static
       * @internal
       */
      "deprecatedEvents"
    ];
  }
  static setupDeprecatedEvents(cls, meta) {
    const all = meta.getInherited("deprecatedEvents"), add = cls.deprecatedEvents;
    for (const eventName in add) {
      all[eventName.toLowerCase()] = all[eventName] = add[eventName];
    }
  }
  //region Config
  static get configurable() {
    return {
      /**
       * Set to `true` to call onXXX method names (e.g. `onShow`, `onClick`), as an easy way to listen for events.
       *
       * ```javascript
       * const container = new Container({
       *     callOnFunctions : true
       *
       *     onHide() {
       *          // Do something when the 'hide' event is fired
       *     }
       * });
       * ```
       *
       * @config {Boolean} callOnFunctions
       * @category Misc
       * @default false
       */
      /**
       * The listener set for this object.
       *
       * An object whose property names are the names of events to handle, or options which modifiy
       * __how__ the handlers are called.
       *
       * See {@link #function-addListener} for details about the options.
       *
       * Listeners can be specified in target class config and they will be merged with any listeners specified in
       * the instantiation config. Class listeners will be fired first:
       *
       * ```javascript
       * class MyStore extends Store({
       *     static get configurable() {
       *         return {
       *             listeners : {
       *                 myCustomEvent() {
       *                 },
       *                 load : {
       *                     prio : 10000,
       *                     fn() { // this load listener handles things first }
       *                 }
       *             }
       *         }
       *     }
       * });
       *
       * let store = new MyStore({
       *   listeners: {
       *     load: () => { // This load listener runs after the class's },
       *     ...
       *   }
       * });
       * ```
       *
       * ### Handlers as function name
       *
       * Object event handlers may be specified as a function __name__. If a string is specified, it is the name
       * of the function in the `thisObj` object.
       *
       * If the string begins with `up.`, this object's ownership hierarchy
       * (if present) is scanned for an object which implements that function name:
       *
       * ```javascript
       * new Popup({
       *     tbar : {
       *         items : {
       *             myCombo : {
       *                 type      : 'combo',
       *                 editable  : false,
       *                 label     : 'Type',
       *                 listeners : {
       *                     // Look in owner chain for this function name
       *                     change : 'up.onFilterChange'
       *                 },
       *                 items     : [
       *                     'Event',
       *                     'Task',
       *                     'Appointment'
       *                 ]
       *             }
       *         }
       *     },
       *     items : {
       *         ...
       *     },
       *     onFilterChange({ value }) {
       *         // Handle event type selection here
       *     }
       * });
       *```
       *
       * @config {Object}
       * @category Common
       */
      listeners: {
        value: null,
        $config: {
          merge(newValue, currentValue) {
            if (newValue !== null) {
              if (!newValue) {
                return currentValue;
              }
              if (currentValue) {
                newValue = newValue ? [newValue] : [];
                newValue.push[isArray(currentValue) ? "apply" : "call"](newValue, currentValue);
              }
            }
            return newValue;
          }
        }
      },
      /**
       * Internal listeners, that cannot be removed by the user.
       * @config {Object}
       * @internal
       */
      internalListeners: null,
      /**
       * An object where property names with a truthy value indicate which events should bubble up the ownership
       * hierarchy when triggered.
       *
       * ```javascript
       * const container = new Container({
       *     items : [
       *        { type : 'text', bubbleEvents : { change : true }}
       *     ],
       *
       *     listeners : {
       *         change() {
       *             // Will catch change event from the text field
       *         }
       *     }
       * });
       * ```
       *
       * @config {Object}
       * @category Misc
       */
      bubbleEvents: null
    };
  }
  destroy() {
    this.trigger("beforeDestroy");
    super.destroy();
  }
  //endregion
  //region Init
  construct(config, ...args) {
    if (this.configuredListeners = config == null ? void 0 : config.listeners) {
      config = Objects.assign({}, config);
      delete config.listeners;
    }
    super.construct(config, ...args);
    this.processConfiguredListeners();
  }
  processConfiguredListeners() {
    if (this.configuredListeners) {
      const me = this, { isConfiguring } = me;
      me.isConfiguring = false;
      me.listeners = me.configuredListeners;
      me.configuredListeners = null;
      me.isConfiguring = isConfiguring;
    }
  }
  /**
   * Auto detaches listeners registered from start, if set as detachable
   * @internal
   */
  doDestroy() {
    this.trigger("destroy");
    this.removeAllListeners(false);
    super.doDestroy();
  }
  static setupClass(meta) {
    super.setupClass(meta);
    Events.prototype.onListen.$nullFn = true;
    Events.prototype.onUnlisten.$nullFn = true;
  }
  //endregion
  //region Listeners
  /**
   * Adds an event listener. This method accepts parameters in the following format:
   *
   * ```javascript
   *  myObject.addListener({
   *      thisObj    : this,          // The this reference for the handlers
   *      eventname2 : 'functionName' // Resolved at invocation time using the thisObj,
   *      otherevent : {
   *          fn      : 'handlerFnName',
   *          once    : true          // Just this handler is auto-removed on fire
   *      },
   *      yetanother  : {
   *          fn      : 'yetAnotherHandler',
   *          args    : [ currentState1, currentState2 ] // Capture info to be passed to handler
   *      },
   *      prio        : 100           // Higher prio listeners are called before lower
   *  });
   * ```
   *
   * When listeners have a `thisObj` option, they are linked to the lifecycle of that object.
   * When it is destroyed, those listeners are removed.
   *
   * The `config` parameter allows supplying options for the listener(s), for available options see {@link #typedef-BryntumListenerConfig}.
   *
   * A simpler signature may be used when only adding a listener for one event and no extra options
   * (such as `once` or `delay`) are required:
   *
   * ```javascript
   * myObject.addListener('click', myController.handleClicks, myController);
   * ```
   *
   * The args in this simple case are `eventName`, `handler` and `thisObj`
   *
   * @param {BryntumListenerConfig|String} config An object containing listener definitions, or the event name to listen for
   * @param {Object|Function} [thisObj] Default `this` reference for all listeners in the config object, or the handler
   * function to call if providing a string as the first arg.
   * @param {Object} [oldThisObj] The `this` reference if the old signature starting with a string event name is used..
   * @returns {Function} Returns a detacher function unless configured with `detachable: false`. Call detacher to remove listeners
   */
  addListener(config, thisObj, oldThisObj) {
    var _a;
    if (isArray(config)) {
      for (let i = 0, { length } = config; i < length; i++) {
        this.addListener(config[i], thisObj);
      }
      return;
    }
    const me = this, deprecatedEvents = me.$meta.getInherited("deprecatedEvents");
    if (typeof config === "string") {
      return me.addListener({
        [config]: thisObj,
        detachable: thisObj.detachable !== false,
        thisObj: oldThisObj
      });
    } else {
      thisObj = config.thisObj = config.thisObj !== void 0 ? config.thisObj : thisObj;
      for (const key in config) {
        if (!specialProperties[key] && config[key] != null) {
          const eventName = key.toLowerCase(), deprecatedEvent = deprecatedEvents == null ? void 0 : deprecatedEvents[eventName], events = me.eventListeners || (me.eventListeners = {}), specs = ArrayHelper.asArray(config[key]);
          if (deprecatedEvent) {
            const { product, invalidAsOfVersion, message } = deprecatedEvent;
            VersionHelper.deprecate(product, invalidAsOfVersion, message);
          }
          for (const listenerSpec of specs) {
            const expires = listenerSpec.expires || config.expires, listener = {
              fn: typeof listenerSpec === "object" ? listenerSpec.fn : listenerSpec,
              thisObj: listenerSpec.thisObj !== void 0 ? listenerSpec.thisObj : thisObj,
              args: listenerSpec.args || config.args,
              prio: listenerSpec.prio !== void 0 ? listenerSpec.prio : config.prio !== void 0 ? config.prio : 0,
              once: listenerSpec.once !== void 0 ? listenerSpec.once : config.once !== void 0 ? config.once : false,
              buffer: listenerSpec.buffer || config.buffer,
              throttle: listenerSpec.throttle || config.throttle,
              $internal: config.$internal,
              catchAll: key === "catchAll"
            };
            if (expires) {
              const { alt } = expires, delay = alt ? expires.delay : expires, name2 = config.name || key, fn = () => {
                me.un(eventName, listener);
                if (alt && !listener.called) {
                  me.callback(alt, thisObj);
                }
              };
              if (me.isDelayable) {
                me.setTimeout({
                  fn,
                  name: name2,
                  cancelOutstanding: true,
                  delay
                });
              } else {
                globalThis.setTimeout(fn, delay);
              }
            }
            let listeners = events[eventName] || (events[eventName] = []);
            if (listeners.$firing) {
              events[eventName] = listeners = listeners.slice();
            }
            listeners.splice(
              ArrayHelper.findInsertionIndex(listener, listeners, priorityComparator, listeners.length),
              0,
              listener
            );
            if (!me.onListen.$nullFn && listeners.length < 2) {
              me.onListen(eventName);
            }
            (_a = me.afterAddListener) == null ? void 0 : _a.call(me, eventName, listener);
          }
        }
      }
      if (config.relayAll) {
        me.relayAll(config.relayAll);
      }
      if (thisObj && thisObj !== me) {
        me.attachAutoDetacher(config, thisObj);
      }
      const detachable = config.detachable !== false, name = config.name, destroy = config.expires || detachable || name ? () => {
        if (!me.isDestroyed) {
          me.removeListener(config, thisObj);
        }
      } : null;
      if (destroy) {
        destroy.eventer = me;
        destroy.listenerName = name;
        if (name && (thisObj == null ? void 0 : thisObj.trackDetacher)) {
          thisObj.trackDetacher(name, destroy);
        }
        if (config.expires) {
          me.delay(destroy, isNaN(config.expires) ? config.expires.delay : config.expires, name);
        }
        if (detachable) {
          return destroy;
        }
      }
    }
  }
  /**
   * Alias for {@link #function-addListener}. Adds an event listener. This method accepts parameters in the following format:
   *
   * ```javascript
   *  myObject.on({
   *      thisObj    : this,          // The this reference for the handlers
   *      eventname2 : 'functionName' // Resolved at invocation time using the thisObj,
   *      otherevent : {
   *          fn      : 'handlerFnName',
   *          once    : true          // Just this handler is auto-removed on fire
   *      },
   *      yetanother  : {
   *          fn      : 'yetAnotherHandler',
   *          args    : [ currentState1, currentState2 ] // Capture info to be passed to handler
   *      },
   *      prio        : 100           // Higher prio listeners are called before lower
   *  });
   * ```
   *
   * When listeners have a `thisObj` option, they are linked to the lifecycle of that object.
   * When it is destroyed, those listeners are removed.
   *
   * The `config` parameter allows supplying options for the listener(s), for available options see {@link #typedef-BryntumListenerConfig}.
   *
   * A simpler signature may be used when only adding a listener for one event and no extra options
   * (such as `once` or `delay`) are required:
   *
   * ```javascript
   * myObject.on('click', myController.handleClicks, myController);
   * ```
   *
   * The args in this simple case are `eventName`, `handler` and `thisObj`
   *
   * @param {BryntumListenerConfig|String} config An object containing listener definitions, or the event name to listen for
   * @param {Object|Function} [thisObj] Default `this` reference for all listeners in the config object, or the handler
   * function to call if providing a string as the first arg.
   * @param {Object} [oldThisObj] The `this` reference if the old signature starting with a string event name is used..
   * @returns {Function} Returns a detacher function unless configured with `detachable: false`. Call detacher to remove listeners
   */
  on(config, thisObj, oldThisObj) {
    return this.addListener(config, thisObj, oldThisObj);
  }
  /**
   * Internal convenience method for adding an internal listener, that cannot be removed by the user.
   *
   * Alias for `on({ $internal : true, ... })`. Only supports single argument form.
   *
   * @internal
   */
  ion(config) {
    config.$internal = true;
    return this.on(config);
  }
  /**
   * Shorthand for {@link #function-removeListener}
   * @param {Object|String} config A config object or the event name
   * @param {Object|Function} [thisObj] `this` reference for all listeners, or the listener function
   * @param {Object} [oldThisObj] `this` The `this` object for the legacy way of adding listeners
   */
  un(...args) {
    this.removeListener(...args);
  }
  updateInternalListeners(internalListeners, oldInternalListeners) {
    oldInternalListeners == null ? void 0 : oldInternalListeners.detach();
    if (internalListeners) {
      internalListeners.detach = this.ion(internalListeners);
    }
  }
  get listeners() {
    return this.eventListeners;
  }
  changeListeners(listeners) {
    if (this.isConfiguring) {
      this.getConfig("internalListeners");
      if (listeners) {
        this.on(listeners, this);
      }
    } else {
      if (Array.isArray(listeners)) {
        for (let i = 0, l = listeners[0], { length } = listeners; i < length; l = listeners[++i]) {
          if (!("thisObj" in l)) {
            listeners[i] = Objects.assign({ thisObj: this }, l);
          }
        }
      } else if (listeners && !("thisObj" in listeners)) {
        listeners = Objects.assign({ thisObj: this }, listeners);
      }
      return listeners;
    }
  }
  updateListeners(listeners, oldListeners) {
    oldListeners && this.un(oldListeners);
    listeners && this.on(listeners);
  }
  /**
   * Removes an event listener. Same API signature as {@link #function-addListener}
   * @param {Object|String} config A config object or the event name
   * @param {Object|Function} thisObj `this` reference for all listeners, or the listener function
   * @param {Object} oldThisObj `this` The `this` object for the legacy way of adding listeners
   */
  removeListener(config, thisObj = config.thisObj, oldThisObj) {
    const me = this;
    if (typeof config === "string") {
      return me.removeListener({ [config]: thisObj }, oldThisObj);
    }
    Object.entries(config).forEach(([eventName, listenerToRemove]) => {
      var _a;
      if (!specialProperties[eventName] && listenerToRemove != null) {
        eventName = eventName.toLowerCase();
        const { eventListeners } = me, index = me.findListener(eventName, listenerToRemove, thisObj);
        if (index >= 0) {
          let listeners = eventListeners[eventName];
          (_a = me.afterRemoveListener) == null ? void 0 : _a.call(me, eventName, listeners[index]);
          if (listeners.length > 1) {
            if (listeners.$firing) {
              eventListeners[eventName] = listeners = listeners.slice();
            }
            listeners.splice(index, 1);
          } else {
            delete eventListeners[eventName];
            if (!me.onUnlisten.$nullFn) {
              me.onUnlisten(eventName);
            }
          }
        }
      }
    });
    if (config.thisObj && !config.thisObj.isDestroyed) {
      me.detachAutoDetacher(config);
    }
  }
  /**
   * Finds the index of a particular listener to the named event. Returns `-1` if the passed
   * function/thisObj listener is not present.
   * @param {String} eventName The name of an event to find a listener for.
   * @param {String|Function} listenerToFind The handler function to find.
   * @param {Object} defaultThisObj The `thisObj` for the required listener.
   * @internal
   */
  findListener(eventName, listenerToFind, defaultThisObj) {
    var _a;
    const eventListeners = (_a = this.eventListeners) == null ? void 0 : _a[eventName], fn = listenerToFind.fn || listenerToFind, thisObj = listenerToFind.thisObj || defaultThisObj;
    if (eventListeners) {
      for (let listenerEntry, i = 0, { length } = eventListeners; i < length; i++) {
        listenerEntry = eventListeners[i];
        if (listenerEntry.fn === fn && listenerEntry.thisObj === thisObj) {
          return i;
        }
      }
    }
    return -1;
  }
  /**
   * Check if any listener is registered for the specified eventName
   * @param {String} eventName
   * @returns {Boolean} `true` if listener is registered, otherwise `false`
   * @advanced
   */
  hasListener(eventName) {
    var _a;
    return Boolean((_a = this.eventListeners) == null ? void 0 : _a[eventName == null ? void 0 : eventName.toLowerCase()]);
  }
  /**
   * Relays all events through another object that also implements Events mixin. Adds a prefix to the event name
   * before relaying, for example add -> storeAdd
   * ```
   * // Relay all events from store through grid, will make it possible to listen for store events prefixed on grid:
   * 'storeLoad', 'storeChange', 'storeRemoveAll' etc.
   * store.relayAll(grid, 'store');
   *
   * grid.on('storeLoad', () => console.log('Store loaded');
   * ```
   * @param {Core.mixin.Events} through Object to relay the events through, needs to mix Events mixin in
   * @param {String} prefix Prefix to add to event name
   * @param {Boolean} [transformCase] Specify false to prevent making first letter of event name uppercase
   * @advanced
   */
  relayAll(through, prefix, transformCase = true) {
    if (!this.relayAllTargets) {
      this.relayAllTargets = [];
    }
    const { relayAllTargets } = this;
    through.ion({
      beforeDestroy: ({ source }) => {
        if (source === through) {
          const configs = relayAllTargets.filter((r) => r.through === through);
          configs.forEach((config) => ArrayHelper.remove(relayAllTargets, config));
        }
      }
    });
    relayAllTargets.push({ through, prefix, transformCase });
  }
  /**
   * Removes all listeners registered to this object by the application.
   */
  removeAllListeners(preserveInternal = true) {
    var _a;
    const listeners = this.eventListeners;
    let i, thisObj;
    for (const event in listeners) {
      const bucket = listeners[event];
      for (i = bucket.length; i-- > 0; ) {
        const cfg = bucket[i];
        if (!cfg.$internal || !preserveInternal) {
          this.removeListener(event, cfg);
          thisObj = cfg.thisObj;
          (_a = thisObj == null ? void 0 : thisObj.untrackDetachers) == null ? void 0 : _a.call(thisObj, this);
        }
      }
    }
  }
  relayEvents(source, eventNames, prefix = "", relayerAsSource) {
    const listenerConfig = { detachable: true, thisObj: this };
    eventNames.forEach((eventName) => {
      if (prefix) {
        eventName = StringHelper.capitalize(eventName);
      }
      listenerConfig[eventName] = (event, ...params) => {
        if (relayerAsSource) {
          event = Object.assign({}, event, { source: this });
        }
        return this.trigger(prefix + eventName, event, ...params);
      };
    });
    return source.on(listenerConfig);
  }
  /**
   * This method is called when the first listener for an event is added.
   * @param {String} eventName
   * @internal
   */
  onListen() {
  }
  /**
   * This method is called when the last listener for an event is removed.
   * @param {String} eventName
   * @internal
   */
  onUnlisten() {
  }
  destructorInterceptor() {
    const { autoDetachers, target, oldDestructor } = this;
    for (let i = 0; i < autoDetachers.length; i++) {
      const { dispatcher, config } = autoDetachers[i];
      if (!dispatcher.isDestroyed) {
        dispatcher.removeListener(config, target);
      }
    }
    oldDestructor.call(target);
  }
  /**
   * Internal function used to hook destroy() calls when using thisObj
   * @private
   */
  attachAutoDetacher(config, thisObj) {
    const target = config.thisObj || thisObj, destructorName = "doDestroy" in target ? "doDestroy" : "destroy";
    if (destructorName in target) {
      let { $autoDetachers } = target;
      if (!$autoDetachers) {
        target.$autoDetachers = $autoDetachers = [];
      }
      if (!target.$oldDestructor) {
        target.$oldDestructor = target[destructorName];
        target[destructorName] = this.destructorInterceptor.bind({
          autoDetachers: $autoDetachers,
          oldDestructor: target.$oldDestructor,
          target
        });
      }
      $autoDetachers.push({ config, dispatcher: this });
    } else {
      target[destructorName] = () => {
        this.removeListener(config);
      };
    }
  }
  /**
   * Internal function used restore hooked destroy() calls when using thisObj
   * @private
   */
  detachAutoDetacher(config) {
    const target = config.thisObj;
    if (target.$oldDestructor && !target.isDestroying) {
      ArrayHelper.remove(
        target.$autoDetachers,
        target.$autoDetachers.find((detacher) => detacher.config === config && detacher.dispatcher === this)
      );
      if (!target.$autoDetachers.length) {
        target["doDestroy" in target ? "doDestroy" : "destroy"] = target.$oldDestructor;
        target.$oldDestructor = null;
      }
    }
  }
  /**
   * Internal function used to run a callback function after an event is triggered
   * @private
   */
  once(eventName, callback) {
    return this.ion({
      [eventName]: () => this.callback(callback),
      once: true
    });
  }
  //endregion
  //region Promise based workflow
  // experimental, used in tests to support async/await workflow
  await(eventName, options = { checkLog: true, resetLog: true, args: null }) {
    const me = this;
    if (options === false) {
      options = { checkLog: false };
    }
    const { args } = options;
    return new Promise((resolve) => {
      var _a;
      if (options.checkLog && ((_a = me._triggered) == null ? void 0 : _a[eventName])) {
        resolve();
        if (options.resetLog) {
          me.clearLog(eventName);
        }
      }
      if (args) {
        const detacher = me.on({
          [eventName]: (...params) => {
            const argsOk = typeof args === "function" ? args(...params) : Object.keys(args).every((key) => {
              return key in params[0] && params[0][key] === args[key];
            });
            if (argsOk) {
              resolve(...params);
              if (options.resetLog) {
                me.clearLog(eventName);
              }
              detacher();
            }
          },
          prio: -1e4
          // Let others do their stuff first
        });
      } else {
        me.on({
          [eventName]: (...params) => {
            resolve(...params);
            if (options.resetLog) {
              me.clearLog(eventName);
            }
          },
          prio: -1e4,
          // Let others do their stuff first
          once: true
          // promises can only be resolved once anyway
        });
      }
    });
  }
  clearLog(eventName) {
    if (this._triggered) {
      if (eventName) {
        delete this._triggered[eventName];
      } else {
        this._triggered = {};
      }
    }
  }
  //endregion
  //region Trigger
  /**
   * Triggers an event, calling all registered listeners with the supplied arguments. Returning false from any listener
   * makes function return false.
   * @param {String} eventName Event name for which to trigger listeners
   * @param {Object} [param] Single parameter passed on to listeners, source property will be added to it (this)
   * @param {Boolean} [param.bubbles] Pass as `true` to indicate that the event will bubble up the widget
   * ownership hierarchy. For example up a `Menu`->`parent` Menu tree, or a `Field`->`Container` tree.
   * @typings param -> {{bubbles?: boolean, [key: string]: any}}
   * @returns {Boolean|Promise} Returns false if any listener returned `false`, or a `Promise` yielding
   * `true` / `false` based on what is returned from the async listener functions, otherwise `true`
   * @async
   * @advanced
   */
  trigger(eventName, param) {
    var _a, _b, _c, _d;
    const me = this, name = eventName.toLowerCase(), {
      eventsSuspended,
      relayAllTargets,
      callOnFunctions
    } = me;
    let listeners = (_a = me.eventListeners) == null ? void 0 : _a[name], handlerPromises;
    if (!me._triggered) {
      me._triggered = {};
    }
    me._triggered[eventName] = true;
    if (eventsSuspended) {
      if (eventsSuspended.shouldQueue) {
        eventsSuspended.queue.push(arguments);
      }
      return true;
    }
    if ((_b = me.eventListeners) == null ? void 0 : _b.catchall) {
      (listeners = listeners ? listeners.slice() : []).push(...me.eventListeners.catchall);
      listeners.sort(priorityComparator);
    }
    if (!listeners && !relayAllTargets && !callOnFunctions) {
      return true;
    }
    if (param) {
      if (!("source" in param)) {
        if (Object.isExtensible(param)) {
          param.source = me;
        } else {
          param = Object.setPrototypeOf({
            source: me
          }, param);
        }
      }
    } else {
      param = {
        source: me
      };
    }
    if (param.type !== name) {
      if (param.constructor !== Object) {
        Reflect.defineProperty(param, "type", { get: () => name });
      } else {
        param.type = name;
      }
    }
    param.eventName = eventName;
    if (!("bubbles" in param) && ((_c = me.bubbleEvents) == null ? void 0 : _c[eventName])) {
      param.bubbles = me.bubbleEvents[eventName];
    }
    if (callOnFunctions) {
      const fnName = "on" + StringHelper.capitalize(eventName);
      if (fnName in me) {
        const result = me[fnName] ? me.callback(me[fnName], me, [param]) : true;
        let inhibit;
        if (Objects.isPromise(result)) {
          (handlerPromises || (handlerPromises = [])).push(result);
        } else {
          inhibit = result === false || inhibit;
        }
        if (!me.isDestroyed && hasOwnProperty.call(me, fnName) && !((_d = me.pluginFunctionChain) == null ? void 0 : _d[fnName])) {
          const myProto = Object.getPrototypeOf(me);
          if (fnName in myProto) {
            const result2 = myProto[fnName].call(me, param);
            if (Objects.isPromise(result2)) {
              (handlerPromises || (handlerPromises = [])).push(result2);
            } else {
              inhibit = result2 === false || inhibit;
            }
            if (me.isDestroyed) {
              return;
            }
          }
        }
        if (inhibit) {
          return false;
        }
      }
    }
    let ret;
    if (listeners) {
      let i = 0, internalAbort = false;
      listeners.$firing = true;
      for (i; i < listeners.length && !me.isDestroyed && !internalAbort; i++) {
        const listener = listeners[i];
        if (ret === false && !listener.$internal) {
          continue;
        }
        let handler, thisObj = listener.thisObj;
        if (!thisObj || !thisObj.isDestroyed) {
          listener.called = true;
          if (listener.once) {
            me.removeListener(name, listener);
          }
          if (typeof listener.fn === "string") {
            if (thisObj) {
              handler = thisObj[listener.fn];
            }
            if (!handler) {
              const result2 = me.resolveCallback(listener.fn);
              handler = result2.handler;
              thisObj = result2.thisObj;
            }
          } else {
            handler = listener.fn;
          }
          if (listener.buffer) {
            if (!listener.bufferFn) {
              const buffer = Number(listener.buffer);
              if (typeof buffer !== "number" || isNaN(buffer)) {
                throw new Error(`Incorrect type for buffer, got "${buffer}" (expected a Number)`);
              }
              listener.bufferFn = FunctionHelper.createBuffered(handler, buffer, thisObj, listener.args);
            }
            handler = listener.bufferFn;
          }
          if (listener.throttle) {
            const throttle = Number(listener.throttle);
            if (typeof throttle !== "number" || isNaN(throttle)) {
              throw new Error(`Incorrect type for throttle, got "${throttle}" (expected a Number)`);
            }
            if (!listener.throttledFn) {
              listener.throttledFn = FunctionHelper.createThrottled(handler, throttle, thisObj, listener.args);
            }
            handler = listener.throttledFn;
          }
          const result = handler.call(thisObj || me, ...listener.args || [], param);
          if (ret !== false) {
            ret = result;
          }
          if (listener.$internal && result === false) {
            internalAbort = true;
          }
          if (Objects.isPromise(result)) {
            result.$internal = listener.$internal;
            (handlerPromises || (handlerPromises = [])).push(result);
          }
        }
      }
      listeners.$firing = false;
      if (internalAbort) {
        return false;
      }
    }
    relayAllTargets == null ? void 0 : relayAllTargets.forEach((config) => {
      let name2 = eventName;
      if (config.transformCase) {
        name2 = StringHelper.capitalize(name2);
      }
      if (config.prefix) {
        name2 = config.prefix + name2;
      }
      if (config.through.trigger(name2, param) === false) {
        return false;
      }
    });
    if (param.bubbles && me.owner && !me.owner.isDestroyed) {
      return me.owner.trigger(eventName, param);
    }
    handlerPromises = handlerPromises == null ? void 0 : handlerPromises.filter((p) => ret !== false || p.$internal);
    if (handlerPromises == null ? void 0 : handlerPromises.length) {
      return new Promise((resolve) => {
        Promise.all(handlerPromises).then((promiseResults) => {
          const finalResult = !promiseResults.some((result) => result === false);
          resolve(finalResult);
        });
      });
    }
    return ret !== false;
  }
  /**
   * Prevents events from being triggered until {@link #function-resumeEvents()} is called. Optionally queues events that are triggered while
   * suspended. Multiple calls stack to require matching calls to `resumeEvents()` before actually resuming.
   * @param {Boolean} queue Specify true to queue events triggered while suspended
   * @advanced
   */
  suspendEvents(queue = false) {
    const eventsSuspended = this.eventsSuspended || (this.eventsSuspended = { shouldQueue: queue, queue: [], count: 0 });
    eventsSuspended.count++;
  }
  /**
   * Resume event triggering after a call to {@link #function-suspendEvents()}. If any triggered events were queued they will be triggered.
   * @returns {Boolean} `true` if events have been resumed (multiple calls to suspend require an equal number of resume calls to resume).
   * @advanced
   */
  resumeEvents() {
    const suspended = this.eventsSuspended;
    if (suspended) {
      if (--suspended.count === 0) {
        this.eventsSuspended = null;
        if (suspended.shouldQueue) {
          for (const queued of suspended.queue) {
            this.trigger(...queued);
          }
        }
      }
    }
    return !Boolean(this.eventsSuspended);
  }
  //endregion
};
export {
  Events_default as default
};
