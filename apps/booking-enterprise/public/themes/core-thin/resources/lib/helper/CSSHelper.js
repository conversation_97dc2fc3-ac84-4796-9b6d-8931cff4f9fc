var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Helpers from "./Helpers.js";
import DomH<PERSON>per from "./DomHelper.js";
class CSSHelper {
  /**
   * Inserts a CSS style rule based upon the passed text
   * @param {String|String[]} cssText The text of the rule including selector and rule body just as it would
   * be specified in a CSS file.
   * @returns {CSSRule|CSSRule[]} The resulting `CSSRule` object(s) if the add was successful.
   */
  static insertRule(cssText, parentElement = document.head) {
    const styleSheet = this.getStyleSheet(parentElement), { cssRules } = styleSheet, oldCount = cssRules.length, one = typeof cssText === "string";
    (one ? [cssText] : cssText).forEach((rule, index) => styleSheet.insertRule(rule, index));
    if (cssRules.length > oldCount) {
      return one ? cssRules[0] : Array.from(cssRules).slice(0, cssText.length);
    }
  }
  /**
   * Looks up the first rule which matched the passed selector.
   * @param {String|Function} selector Either the selector string to exactly match or a function which
   * when passed a required selector, returns `true`.
   * @returns {CSSRule} The first matching CSS Rule object if any found.
   */
  static findRule(selector) {
    let result;
    const isFn = typeof selector === "function";
    Array.prototype.find.call(document.head.querySelectorAll("link[rel=stylesheet],style[type*=css]"), (element) => {
      result = Array.prototype.find.call(element.sheet.rules || element.sheet.cssRules, (r) => {
        return isFn ? selector(r) : r.selectorText === selector;
      });
      if (result) {
        return true;
      }
    });
    return result;
  }
  static getStyleSheet(parentElement = document.head) {
    if (!parentElement.$bryntumStylesheet) {
      parentElement.$bryntumStylesheet = DomHelper.createElement({
        tag: "style",
        id: "bryntum-private-styles",
        type: "text/css",
        parent: parentElement
      }).sheet;
    }
    return parentElement.$bryntumStylesheet;
  }
  /**
   * Returns current CSS version
   * @returns {String}
   * @internal
   */
  static getCSSVersion() {
    return getComputedStyle(document.documentElement).getPropertyValue("--bryntum-version").replace(/[" ]/gm, "");
  }
}
__publicField(CSSHelper, "$name", "CSSHelper");
Helpers.register(CSSHelper);
CSSHelper._$name = "CSSHelper";
export {
  CSSHelper as default
};
