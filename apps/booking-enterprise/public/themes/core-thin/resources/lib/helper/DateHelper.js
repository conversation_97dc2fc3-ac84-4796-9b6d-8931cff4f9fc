var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Localizable from "../localization/Localizable.js";
import LocaleManager from "../localization/LocaleManager.js";
import "../localization/En.js";
import StringHelper from "./StringHelper.js";
import Helpers from "./Helpers.js";
import Objects from "./util/Objects.js";
const { toString } = Object.prototype, DATE_TYPE = toString.call(/* @__PURE__ */ new Date()), tempDate = /* @__PURE__ */ new Date(), MS_PER_HOUR = 1e3 * 60 * 60, defaultValue = (value, defValue) => isNaN(value) || value == null ? defValue : value, rangeFormatPartRe = /([ES]){([^}]+)}/g, enOrdinalSuffix = (number) => {
  const hasSpecialCase = ["11", "12", "13"].find((n) => number.endsWith(n));
  let suffix = "th";
  if (!hasSpecialCase) {
    const lastDigit = number[number.length - 1];
    suffix = { 1: "st", 2: "nd", 3: "rd" }[lastDigit] || "th";
  }
  return number + suffix;
}, useIntlFormat = (name, options, date) => {
  const formatter = intlFormatterCache[name] || (intlFormatterCache[name] = new Intl.DateTimeFormat(locale, options));
  return formatter.format(date);
}, formatTime = (name, options, date, isShort = false) => {
  let strTime = useIntlFormat(name, options, date);
  if (/am|pm/i.test(strTime)) {
    strTime = strTime.replace(/^0/, "");
    if (isShort) {
      strTime = strTime.replace(/:00/, "");
    }
  }
  return strTime;
}, getDayDiff = (end, start) => Math.floor((end.getTime() - start.getTime() - (end.getTimezoneOffset() - start.getTimezoneOffset()) * validConversions.minute.millisecond) / validConversions.day.millisecond) + 1, normalizeDay = (day) => day >= 0 ? day : day + 7, msRegExp = /([^\w])(S+)/gm, msReplacer = (match, g1) => g1 + "SSS", splitRegExp = /[:.\-/\s]/;
let locale = "en-US", ordinalSuffix = enOrdinalSuffix, formatCache = {}, formatRedirects = {}, intlFormatterCache = {}, parserCache = {};
const redirectFormat = (format) => {
  const intlConfig = intlFormatConfigs[format];
  if (!intlConfig) {
    throw new Error("Only international formats should be used here");
  }
  if (formatRedirects[format] !== void 0) {
    return formatRedirects[format];
  }
  const intl = new Intl.DateTimeFormat(locale, intlConfig), fmt = intl.formatToParts(new Date(2001, 1, 2, 3, 4, 5, 6)).map((part) => {
    const type = part.type, intlCfg = intlConfig[type];
    if (type === "literal") {
      return part.value.replace(/,/g, "");
    } else if (type === "day") {
      return intlCfg === "numeric" ? "D" : "DD";
    } else if (type === "month") {
      return intlCfg === "short" ? "MMM" : intlCfg === "long" ? "MMMM" : intlCfg === "numeric" ? "M" : "MM";
    } else if (type === "year") {
      return intlCfg === "numeric" ? "YYYY" : "YY";
    }
  }).join("");
  return formatRedirects[format] = fmt;
};
const DEFAULT_YEAR = 2020, DEFAULT_MONTH = 0, DEFAULT_DAY = 1, intlFormatConfigs = {
  l: { year: "numeric", month: "numeric", day: "numeric" },
  ll: { year: "numeric", month: "short", day: "numeric" }
}, formats = {
  // 1, 2, ... 11, 12
  M: (date) => date.getMonth() + 1,
  //date.toLocaleDateString(locale, { month : 'numeric' }),
  // 1st, 2nd, 3rd, 4th, ... 11th, 12th
  Mo: (date) => ordinalSuffix(formats.M(date).toString()),
  // 01, 02, ...
  MM: (date) => (date.getMonth() + 1).toString().padStart(2, "0"),
  //date.toLocaleDateString(locale, { month : '2-digit' }),
  // Jan, Feb, ...
  MMM: (date) => useIntlFormat("MMM", { month: "short" }, date),
  // January, February, ...
  MMMM: (date) => useIntlFormat("MMMM", { month: "long" }, date),
  // 1, 2, ...
  Q: (date) => Math.ceil((date.getMonth() + 1) / 3),
  // 1st, 2nd, ...
  Qo: (date) => ordinalSuffix(formats.Q(date).toString()),
  // 1, 2, ...
  D: (date) => date.getDate(),
  //date.toLocaleDateString(locale, { day : 'numeric' }),
  // 1st, 2nd, ...
  Do: (date) => ordinalSuffix(formats.D(date).toString()),
  // 01, 02, ...
  DD: (date) => date.getDate().toString().padStart(2, "0"),
  //date.toLocaleDateString(locale, { day : '2-digit' }),
  // 1, 2, ..., 365, 365
  DDD: (date) => Math.ceil(
    (new Date(date.getFullYear(), date.getMonth(), date.getDate(), 12, 0, 0) - new Date(date.getFullYear(), 0, 0, 12, 0, 0)) / validConversions.day.millisecond
  ),
  // 1st, 2nd, ...
  DDDo: (date) => ordinalSuffix(formats.DDD(date).toString()),
  // 001, 002, ...
  DDDD: (date) => formats.DDD(date).toString().padStart(3, "0"),
  // 0, 1, ..., 6
  d: (date) => date.getDay(),
  // 0th, 1st, ...
  do: (date) => ordinalSuffix(date.getDay().toString()),
  // S, M, ...
  d1: (date) => useIntlFormat("d1", { weekday: "narrow" }, date).substr(0, 1),
  // Su, Mo, ...
  dd: (date) => formats.ddd(date).substring(0, 2),
  // Sun, Mon, ...
  ddd: (date) => useIntlFormat("ddd", { weekday: "short" }, date),
  // Sunday, Monday, ...
  dddd: (date) => useIntlFormat("dddd", { weekday: "long" }, date),
  u: (date) => {
    const formatter = intlFormatterCache.u || (intlFormatterCache.u = new Intl.DateTimeFormat("en-GB", {
      timeZone: "UTC",
      year: "numeric",
      month: "2-digit",
      day: "2-digit"
    })), parts = formatter.formatToParts(date);
    return `${parts[4].value}${parts[2].value}${parts[0].value}Z`;
  },
  uu: (date) => {
    const formatter = intlFormatterCache.uu || (intlFormatterCache.uu = new Intl.DateTimeFormat("en-GB", {
      timeZone: "UTC",
      hour12: false,
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit"
    })), parts = formatter.formatToParts(date);
    return `${parts[4].value}${parts[2].value}${parts[0].value}T${parts[6].value}${parts[8].value}${parts[10].value}Z`;
  },
  e: (date) => date.getDay(),
  E: (date) => date.getDay() + 1,
  // ISO week, 1, 2, ...
  W: (date) => DateHelper.getWeekNumber(date)[1],
  Wo: (date) => ordinalSuffix(formats.W(date).toString()),
  WW: (date) => formats.W(date).toString().padStart(2, "0"),
  // ISO week, 1, 2, ... with localized 'Week ' prefix
  Wp: (date) => `${DateHelper.localize("L{Week}")} ${formats.W(date)}`,
  WWp: (date) => `${DateHelper.localize("L{Week}")} ${formats.WW(date)}`,
  Wp0: (date) => `${DateHelper.localize("L{Week}")[0]}${formats.W(date)}`,
  WWp0: (date) => `${DateHelper.localize("L{Week}")[0]}${formats.WW(date)}`,
  // 1979, 2018
  Y: (date) => date.getFullYear(),
  //date.toLocaleDateString(locale, { year : 'numeric' }),
  // 79, 18
  YY: (date) => (date.getFullYear() % 100).toString().padStart(2, "0"),
  //date.toLocaleDateString(locale, { year : '2-digit' }),
  // 1979, 2018
  YYYY: (date) => date.getFullYear(),
  //date.toLocaleDateString(locale, { year : 'numeric' }),
  // AM, PM
  A: (date) => date.getHours() < 12 ? "AM" : "PM",
  a: (date) => date.getHours() < 12 ? "am" : "pm",
  // 0, 1, ... 23
  H: (date) => date.getHours(),
  // 00, 01, ...
  HH: (date) => date.getHours().toString().padStart(2, "0"),
  // 1, 2, ... 12
  h: (date) => date.getHours() % 12 || 12,
  // 01, 02, ...
  hh: (date) => formats.h(date).toString().padStart(2, "0"),
  // 1, 2, ... 24
  k: (date) => date.getHours() || 24,
  // 01, 02, ...
  kk: (date) => formats.k(date).toString().padStart(2, "0"),
  // Locale specific (0 -> 24 or 1 AM -> 12 PM)
  K: (date) => formatTime("K", { hour: "numeric" }, date),
  // Locale specific (00 -> 24 or 1 AM -> 12 PM)
  KK: (date) => formatTime("KK", { hour: "2-digit" }, date),
  // 0, 1, ... 59
  m: (date) => date.getMinutes(),
  //date.toLocaleTimeString(locale, { minute : 'numeric' }),
  // 00, 01, ...
  mm: (date) => formats.m(date).toString().padStart(2, "0"),
  // 0, 1, ... 59
  s: (date) => date.getSeconds(),
  //date.toLocaleTimeString(locale, { second : 'numeric' }),
  // 00, 01, ...
  ss: (date) => formats.s(date).toString().padStart(2, "0"),
  // 0, 1, ... 9 which are 000, 100, 200 ... 900 in milliseconds
  S: (date) => Math.floor(date.getMilliseconds() / 100).toString(),
  // 00, 01, ... 99 which are 000, 010, 020 ... 990 in milliseconds
  SS: (date) => Math.floor(date.getMilliseconds() / 10).toString().padStart(2, "0"),
  // 000, 001, ... 999 in milliseconds
  SSS: (date) => date.getMilliseconds().toString().padStart(3, "0"),
  z: (date) => useIntlFormat("z", { timeZoneName: "short" }, date),
  zz: (date) => useIntlFormat("zz", { timeZoneName: "long" }, date),
  Z: (date) => DH.getGMTOffset(date),
  LT: (date) => formatTime("LT", { hour: "2-digit", minute: "2-digit" }, date),
  // if minutes is 0, doesn't show it
  LST: (date) => formatTime("LST", { hour: "numeric", minute: "2-digit" }, date, true),
  LTS: (date) => formatTime("LTS", { hour: "2-digit", minute: "2-digit", second: "2-digit" }, date),
  L: (date) => useIntlFormat("L", { year: "numeric", month: "2-digit", day: "2-digit" }, date),
  l: (date) => useIntlFormat("l", intlFormatConfigs.l, date),
  LL: (date) => useIntlFormat("LL", { year: "numeric", month: "long", day: "numeric" }, date),
  ll: (date) => useIntlFormat("ll", intlFormatConfigs.ll, date),
  LLL: (date) => useIntlFormat("LLL", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit"
  }, date),
  lll: (date) => useIntlFormat("lll", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit"
  }, date),
  LLLL: (date) => useIntlFormat("LLLL", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
    weekday: "long"
  }, date),
  llll: (date) => useIntlFormat("llll", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
    weekday: "short"
  }, date)
}, formatKeys = Object.keys(formats).sort((a, b) => b.length - a.length), formatRegexp = `^(?:${formatKeys.join("|")})`, emptyFn = () => ({}), isNumber = (str) => numberRegex.test(str), parseMilliseconds = (str) => isNumber(str) && { milliseconds: parseInt(str.padEnd(3, "0").substring(0, 3)) }, parsers = {
  YYYY: (str) => {
    const year = parseInt(str);
    return { year: year >= 1e3 && year <= 9999 ? year : NaN };
  },
  Y: (str) => ({ year: parseInt(str) }),
  YY: (str) => {
    const year = parseInt(str);
    return { year: year + (year > 1968 ? 1900 : 2e3) };
  },
  M: (str) => ({ month: parseInt(str) - 1 }),
  MM: (str) => ({ month: parseInt(str) - 1 }),
  Mo: (str) => ({ month: parseInt(str) - 1 }),
  MMM: (str) => {
    const month = (str || "").toLowerCase();
    for (const [name, entry] of Object.entries(DateHelper._monthShortNamesIndex)) {
      if (month.startsWith(name)) {
        return { month: entry.value };
      }
    }
  },
  MMMM: (str) => {
    const month = (str || "").toLowerCase();
    for (const [name, entry] of Object.entries(DateHelper._monthNamesIndex)) {
      if (month.startsWith(name)) {
        return { month: entry.value };
      }
    }
  },
  DD: (str) => ({ date: parseInt(str) }),
  D: (str) => ({ date: parseInt(str) }),
  Do: (str) => ({ date: parseInt(str) }),
  DDD: emptyFn,
  DDDo: emptyFn,
  DDDD: emptyFn,
  d: emptyFn,
  do: emptyFn,
  d1: emptyFn,
  dd: emptyFn,
  ddd: emptyFn,
  dddd: emptyFn,
  Q: emptyFn,
  Qo: emptyFn,
  W: emptyFn,
  Wo: emptyFn,
  WW: emptyFn,
  e: emptyFn,
  E: emptyFn,
  HH: (str) => ({ hours: parseInt(str) }),
  hh: (str) => ({ hours: parseInt(str) }),
  mm: (str) => ({ minutes: parseInt(str) }),
  H: (str) => ({ hours: parseInt(str) }),
  m: (str) => ({ minutes: parseInt(str) }),
  ss: (str) => ({ seconds: parseInt(str) }),
  s: (str) => ({ seconds: parseInt(str) }),
  S: parseMilliseconds,
  SS: parseMilliseconds,
  SSS: parseMilliseconds,
  A: (str) => ({ amPm: str.toLowerCase() }),
  a: (str) => ({ amPm: str.toLowerCase() }),
  L: "MM/DD/YYYY",
  LT: "HH:mm A",
  LTS: "HH:mm:ss A",
  l: { type: "dynamic", parser: () => redirectFormat("l") },
  ll: { type: "dynamic", parser: () => redirectFormat("ll") },
  // Can either be Z (=UTC, 0) or +-HH:MM
  Z: (str) => {
    if (!str || !timeZoneRegEx.test(str) && str !== "Z") {
      return null;
    }
    let timeZone = 0;
    if (str !== "Z") {
      const matches = timeZoneRegEx.exec(str);
      if (matches) {
        const sign = matches[1] === "+" ? 1 : -1, hours = parseInt(matches[2]) || 0, minutes = parseInt(matches[3]) || 0;
        timeZone = sign * (hours * 60 + minutes);
      } else {
        timeZone = -1 * (/* @__PURE__ */ new Date()).getTimezoneOffset();
      }
    }
    return { timeZone };
  }
}, parserKeys = Object.keys(parsers).sort((a, b) => b.length - a.length), parserRegexp = new RegExp(`(${parserKeys.join("|")})`), localeStrRegExp = new RegExp("^(LL|LLL|lll|LLLL|llll)$"), validConversions = {
  // The units below assume:
  // 30 days in a month, 91 days for a quarter and 365 for a year
  // 52 weeks per year, 4 per month, 13 per quarter
  // 3652 days per decade (assuming two of the years will be leap with 366 days)
  decade: {
    decade: 1,
    year: 10,
    quarter: 40,
    month: 120,
    week: 520,
    day: 3652,
    hour: 24 * 3652,
    minute: 1440 * 3652,
    second: 86400 * 3652,
    millisecond: 864e5 * 3652
  },
  year: {
    decade: 0.1,
    year: 1,
    quarter: 4,
    month: 12,
    week: 52,
    day: 365,
    hour: 24 * 365,
    minute: 1440 * 365,
    second: 86400 * 365,
    millisecond: 864e5 * 365
  },
  quarter: {
    decade: 1 / 40,
    year: 1 / 4,
    quarter: 1,
    month: 3,
    week: 4,
    day: 91,
    hour: 24 * 91,
    minute: 1440 * 91,
    second: 86400 * 91,
    millisecond: 864e5 * 91
  },
  month: {
    decade: 1 / 120,
    year: 1 / 12,
    quarter: 1 / 3,
    month: 1,
    week: 4,
    day: -30,
    hour: -24 * 30,
    minute: -1440 * 30,
    second: -86400 * 30,
    millisecond: -864e5 * 30
  },
  week: {
    decade: -1 / 520,
    year: -1 / 52,
    quarter: -1 / 13,
    month: -1 / 4,
    day: 7,
    hour: 168,
    minute: 10080,
    second: 604800,
    millisecond: 6048e5
  },
  day: {
    decade: -1 / 3652,
    year: -1 / 365,
    quarter: -1 / 91,
    month: -1 / 30,
    week: 1 / 7,
    hour: 24,
    minute: 1440,
    second: 86400,
    millisecond: 864e5
  },
  hour: {
    decade: -1 / (3652 * 24),
    year: -1 / (365 * 24),
    quarter: -1 / (91 * 24),
    month: -1 / (30 * 24),
    week: 1 / 168,
    day: 1 / 24,
    minute: 60,
    second: 3600,
    millisecond: 36e5
  },
  minute: {
    decade: -1 / (3652 * 1440),
    year: -1 / (365 * 1440),
    quarter: -1 / (91 * 1440),
    month: -1 / (30 * 1440),
    week: 1 / 10080,
    day: 1 / 1440,
    hour: 1 / 60,
    second: 60,
    millisecond: 6e4
  },
  second: {
    decade: -1 / (3652 * 86400),
    year: -1 / (365 * 86400),
    quarter: -1 / (91 * 86400),
    month: -1 / (30 * 86400),
    week: 1 / 604800,
    day: 1 / 86400,
    hour: 1 / 3600,
    minute: 1 / 60,
    millisecond: 1e3
  },
  millisecond: {
    decade: -1 / (3652 * 864e5),
    year: -1 / (365 * 864e5),
    quarter: -1 / (91 * 864e5),
    month: -1 / (30 * 864e5),
    week: 1 / 6048e5,
    day: 1 / 864e5,
    hour: 1 / 36e5,
    minute: 1 / 6e4,
    second: 1 / 1e3
  }
}, normalizedUnits = {
  ms: "millisecond",
  milliseconds: "millisecond",
  s: "second",
  seconds: "second",
  m: "minute",
  mi: "minute",
  min: "minute",
  minutes: "minute",
  h: "hour",
  hours: "hour",
  d: "day",
  days: "day",
  w: "week",
  weeks: "week",
  M: "month",
  mo: "month",
  mon: "month",
  months: "month",
  q: "quarter",
  quarters: "quarter",
  y: "year",
  years: "year",
  dec: "decade",
  decades: "decade"
}, withDecimalsDurationRegex = /^\s*([-+]?\d+(?:[.,]\d*)?|[-+]?(?:[.,]\d+))\s*([^\s]+)?/i, noDecimalsDurationRegex = /^\s*([-+]?\d+)(?![.,])\s*([^\s]+)?/i, canonicalUnitNames = [
  "millisecond",
  "second",
  "minute",
  "hour",
  "day",
  "week",
  "month",
  "quarter",
  "year",
  "decade"
], canonicalUnitAbbreviations = [
  ["mil"],
  ["s", "sec"],
  ["m", "min"],
  ["h", "hr"],
  ["d"],
  ["w", "wk"],
  ["mo", "mon", "mnt"],
  ["q", "quar", "qrt"],
  ["y", "yr"],
  ["dec"]
], deltaUnits = [
  "decade",
  "year",
  "month",
  "week",
  "day",
  "hour",
  "minute",
  "second",
  "millisecond"
], dateProperties = [
  "milliseconds",
  "seconds",
  "minutes",
  "hours",
  "date",
  "month",
  "year"
], parseNumber = (n) => {
  const result = parseFloat(n);
  return isNaN(result) ? null : result;
}, numberRegex = /^[0-9]+$/, timeZoneRegEx = /([+-])(\d\d):*(\d\d)*$/, unitMagnitudes = {
  millisecond: 0,
  second: 1,
  minute: 2,
  hour: 3,
  day: 4,
  week: 5,
  month: 6,
  quarter: 7,
  year: 8,
  decade: 9
}, snapFns = {
  round(number, step = 1) {
    return Math.round(number / step) * step;
  },
  floor(number, step = 1) {
    return Math.floor(number / step) * step;
  },
  ceil(number, step = 1) {
    return Math.ceil(number / step) * step;
  }
}, keyCache = {};
const _DateHelper = class _DateHelper extends Localizable() {
  //region Parse & format
  /**
   * Get/set the default format used by `format()` and `parse()`. Defaults to `'YYYY-MM-DDTHH:mm:ssZ'`
   * (~ISO 8601 Date and time, `'1962-06-17T09:21:34Z'`).
   * @member {String}
   */
  static set defaultFormat(format) {
    DH._defaultFormat = format;
  }
  static get defaultFormat() {
    return DH._defaultFormat || "YYYY-MM-DDTHH:mm:ssZ";
  }
  /**
   * Get/set the default format used by `parse()`. Defaults to `'YYYY-MM-DDTHH:mm:ss.SSSZ'` or {@link #property-defaultFormat-static}
   * (~ISO 8601 Date and time, `'1962-06-17T09:21:34.123Z'`).
   * @member {String}
   */
  static set defaultParseFormat(parseFormat) {
    this._defaultParseFormat = parseFormat;
  }
  static get defaultParseFormat() {
    return this._defaultParseFormat || this._defaultFormat || "YYYY-MM-DDTHH:mm:ss.SSSZ";
  }
  static get today() {
    return _DateHelper.clearTime(/* @__PURE__ */ new Date(), false);
  }
  static get tomorrow() {
    return _DateHelper.add(_DateHelper.today, 1, "day");
  }
  static get yesterday() {
    return _DateHelper.add(_DateHelper.today, -1, "day");
  }
  static buildParser(format) {
    const parts = format.split(parserRegexp), parser = [];
    if (parts.length === 1 || localeStrRegExp.test(format)) {
      return [];
    } else {
      parts.reduce((prev, curr, index, array) => {
        if (index !== 0 || curr !== "") {
          if (parserRegexp.test(curr)) {
            const localeParsers = this.localize("L{parsers}") || {}, fn = localeParsers[curr] || parsers[curr];
            if (curr === "Z" && index < array.length - 2) {
              throw new Error(`Invalid format ${format} TimeZone (Z) must be last token`);
            }
            const parserObj = typeof fn === "function" || typeof fn === "string" ? fn : fn.parser();
            if (typeof parserObj === "string") {
              const nestedParsers = DH.buildParser(parserObj), lastItem = nestedParsers.pop();
              delete lastItem.last;
              parser.push(...nestedParsers);
              prev = lastItem;
            } else {
              prev.pattern = curr;
              prev.fn = parserObj;
            }
          } else {
            prev.splitter = curr;
            parser.push(prev);
            prev = {};
          }
        } else if (Object.prototype.hasOwnProperty.call(prev, "pattern")) {
          parser.push(prev);
        }
        return prev;
      }, {});
    }
    parser[parser.length - 1].last = true;
    return parser;
  }
  /**
   * A utility function to create a sortable string key for the passed date or ms timestamp using the `'YYYY-MM-DD'`
   * format.
   * @param {Number|Date} ms The Date instance or ms timestamp to generate a key for
   * @returns {String} Date/timestamp as a string with `'YYYY-M-D'` format
   * @internal
   */
  static makeKey(ms) {
    if (ms.length === 10) {
      return ms;
    }
    if (ms.getTime) {
      ms = ms.getTime();
    }
    const cached = keyCache[Math.trunc(ms / MS_PER_HOUR)];
    if (cached) {
      return cached;
    }
    tempDate.setTime(ms);
    const month = tempDate.getMonth() + 1, date = tempDate.getDate();
    return keyCache[Math.trunc(ms / MS_PER_HOUR)] = `${tempDate.getFullYear()}-${month < 10 ? "0" + month : month}-${date < 10 ? "0" + date : date}`;
  }
  /**
   * A utility function to parse a sortable string to a date using the `'YYYY-MM-DD'` format.
   * @param {String} key The string to return a date for
   * @returns {Date} new Date instance
   * @internal
   */
  static parseKey(key) {
    return DH.parse(key, "YYYY-MM-DD");
  }
  /**
   * Returns a date created from the supplied string using the specified format. Will try to create even if format
   * is left out, by first using the default format (see {@link #property-defaultFormat-static}, by default
   * `YYYY-MM-DDTHH:mm:ssZ`) and then using `new Date(dateString)`.
   * Supported tokens:
   *
   * | Unit        | Token | Description                                      |
   * |-------------|-------|--------------------------------------------------|
   * | Year        | YYYY  | 4-digits year, like: 2018                        |
   * |             | Y     | numeric, any number of digits                    |
   * |             | YY    | < 68 -> 2000, > 68 -> 1900                       |
   * | Month       | MM    | 01 - 12                                          |
   * | Month       | MMM   | Short name of the month                          |
   * | Date        | DD    | 01 - 31                                          |
   * | Hour        | HH    | 00 - 23 or 1 - 12                                |
   * | Minute      | mm    | 00 - 59                                          |
   * | Second      | ss    | 00 - 59                                          |
   * | Millisecond | S     | 0 - 9 [000, 100, 200 .. 900 ]                    |
   * |             | SS    | 00 - 99 [000, 010, 020 .. 990 ]                  |
   * |             | SSS   | 000 - 999 [000, 001, 002 .. 999 ]                |
   * | AM/PM       | A     | AM or PM                                         |
   * |             | a     | am or pm                                         |
   * | TimeZone    | Z     | Z for UTC or +-HH:mm. Local timezone if left out |
   * | Predefined  | L     | Long date, MM/DD/YYYY                            |
   * |             | LT    | Long time, HH:mm A                               |
   *
   * Predefined formats and functions used to parse tokens can be localized, see for example the swedish locale
   * `SvSE.js`.
   *
   * NOTE: If no date parameters are provided then `Jan 01 2020` is used as a default date.
   *
   * {@note}Note that date strings without timezone information ('Z' or '+-HH:mm') will be in the local timezone. Eg.
   * '2024-05-14' -> local time, '2024-05-14Z' -> UTC{/@note}
   *
   * @param {String} dateString Date string
   * @param {String} [format] Date format (or {@link #property-defaultParseFormat-static} if left out)
   * @returns {Date} new Date instance parsed from the string
   * @category Parse & format
   */
  static parse(dateString, format = DH.defaultParseFormat, strict = false) {
    if (dateString instanceof Date) {
      return dateString;
    }
    if (typeof dateString !== "string" || !dateString) {
      return null;
    }
    const config = {
      year: null,
      month: null,
      date: null,
      hours: null,
      minutes: null,
      seconds: null,
      milliseconds: null
    };
    format = format.replace(msRegExp, msReplacer);
    let parser = parserCache[format], result;
    if (!parser) {
      parser = parserCache[format] = DH.buildParser(format);
    }
    if (dateString.includes("\u202F")) {
      dateString = dateString.replace(/\s/g, " ");
    }
    parser.reduce((dateString2, parser2) => {
      var _a;
      if (parser2.last) {
        Object.assign(config, parser2.fn(dateString2));
      } else {
        let splitAt;
        if (parser2.splitter === "T" && dateString2.indexOf("T") === -1) {
          splitAt = dateString2.indexOf(" ");
        } else {
          const timeZoneIndex = dateString2.indexOf("+");
          let { splitter } = parser2;
          if (!strict && splitRegExp.test(splitter)) {
            splitter = splitRegExp;
          }
          splitAt = parser2.splitter !== "" ? dateString2.search(typeof splitter === "string" ? StringHelper.escapeRegExp(splitter) : splitter) : ((_a = parser2.pattern) == null ? void 0 : _a.length) || -1;
          if (timeZoneIndex > -1 && splitAt > timeZoneIndex) {
            splitAt = -1;
          }
        }
        let part, rest;
        if (splitAt === -1 || parser2.pattern === "SSS" && dateString2.match(/^\d+Z$/)) {
          const chunks = dateString2.split(/([Z\-+])/);
          if (chunks.length === 1) {
            part = dateString2;
            rest = "";
          } else {
            part = chunks[0];
            rest = `${chunks[1]}${chunks[2]}`;
          }
        } else {
          part = dateString2.substring(0, splitAt) || dateString2;
          rest = dateString2.substring(splitAt + parser2.splitter.length);
        }
        if (parser2.fn) {
          const res = parser2.fn(part);
          if (res) {
            Object.assign(config, res);
          } else {
            rest = part + rest;
          }
        }
        return rest;
      }
    }, dateString);
    if (config.year && !config.date) {
      config.date = 1;
    }
    if (config.date > 31 || config.month > 12) {
      return null;
    }
    const date = DH.create(config, strict);
    if (date) {
      result = date;
    } else if (!strict) {
      result = new Date(dateString);
    }
    return result;
  }
  /**
   * Creates a date from a date definition object. The object can have the following properties:
   * - year
   * - month
   * - date (day in month)
   * - hours
   * - minutes
   * - seconds
   * - milliseconds
   * - amPm : 'am' or 'pm', implies 12-hour clock
   * - timeZone : offset from UTC in minutes
   * @param {Object} definition
   * @param {Number} definition.year
   * @param {Number} [definition.month]
   * @param {Number} [definition.date]
   * @param {Number} [definition.hours]
   * @param {Number} [definition.minutes]
   * @param {Number} [definition.seconds]
   * @param {Number} [definition.milliseconds]
   * @param {Number} [definition.amPm]
   * @param {Number} [definition.timeZone]
   * @returns {Date} new Date instance
   * @category Parse & format
   */
  static create(definition, strict = false) {
    const def = { ...definition };
    let invalid = isNaN(def.year) || strict && (isNaN(def.month) || isNaN(def.date)), useUTC = false;
    if (!invalid) {
      let allNull = true;
      dateProperties.forEach((property) => {
        if (!(property in def) || isNaN(def[property])) {
          def[property] = 0;
        }
        allNull = allNull && def[property] === null;
      });
      invalid = allNull;
    }
    if (invalid) {
      return null;
    }
    if (def.amPm === "am") {
      def.hours = def.hours % 12;
    } else if (def.amPm === "pm") {
      def.hours = def.hours % 12 + 12;
    }
    if ("timeZone" in def) {
      useUTC = true;
      def.minutes -= def.timeZone;
    }
    if (strict && (def.year == null || def.month == null || def.date == null)) {
      return null;
    }
    const args = [
      defaultValue(def.year, DEFAULT_YEAR),
      defaultValue(def.month, DEFAULT_MONTH),
      defaultValue(def.date, DEFAULT_DAY),
      def.hours,
      def.minutes,
      def.seconds,
      def.milliseconds
    ];
    return useUTC ? new Date(Date.UTC(...args)) : new Date(...args);
  }
  static toUTC(date) {
    return new Date(Date.UTC(
      date.getUTCFullYear(),
      date.getUTCMonth(),
      date.getUTCDate(),
      date.getUTCHours(),
      date.getUTCMinutes(),
      date.getUTCSeconds(),
      date.getUTCMilliseconds()
    ));
  }
  /**
   * Converts a date to string with the specified format. Formats heavily inspired by https://momentjs.com.
   * Available formats (input used for output below is `new Date(2018,8,9,18,7,8,145)`):
   *
   * | Unit                  | Token | Description & output                  |
   * |-----------------------|-------|---------------------------------------|
   * | Year                  | YYYY  | 2018                                  |
   * |                       | YY    | 18                                    |
   * |                       | Y     | 2018                                  |
   * | Quarter               | Q     | 3                                     |
   * |                       | Qo    | 3rd                                   |
   * | Month                 | MMMM  | September                             |
   * |                       | MMM   | Sep                                   |
   * |                       | MM    | 09                                    |
   * |                       | Mo    | 9th                                   |
   * |                       | M     | 9                                     |
   * | Week (iso)            | WW    | 37 (2 digit, zero padded)             |
   * |                       | Wo    | 37th                                  |
   * |                       | W     | 37                                    |
   * |                       | WWp   | Week 37 (localized prefix, zero pad)  |
   * |                       | Wp    | Week 37 (localized prefix)            |
   * |                       | WWp0  | W37 (localized prefix)                |
   * |                       | Wp0   | W37 (localized prefix)                |
   * | Date                  | DDDD  | Day of year, 3 digits                 |
   * |                       | DDDo  | Day of year, ordinal                  |
   * |                       | DDD   | Day of year                           |
   * |                       | DD    | 09                                    |
   * |                       | Do    | 9th                                   |
   * |                       | D     | 9                                     |
   * | Weekday               | dddd  | Sunday                                |
   * |                       | ddd   | Sun                                   |
   * |                       | dd    | Su                                    |
   * |                       | d1    | S                                     |
   * |                       | do    | 0th                                   |
   * |                       | d     | 0                                     |
   * | Hour                  | HH    | 18 (00 - 23)                          |
   * |                       | H     | 18 (0 - 23)                           |
   * |                       | hh    | 06 (00 - 12)                          |
   * |                       | h     | 6 (0 - 12)                            |
   * |                       | KK    | 19 (01 - 24)                          |
   * |                       | K     | 19 (1 - 24)                           |
   * |                       | kk    | 06 or 18, locale determines           |
   * |                       | k     | 6 or 18, locale determines            |
   * | Minute                | mm    | 07                                    |
   * |                       | m     | 7                                     |
   * | Second                | ss    | 08                                    |
   * |                       | s     | 8                                     |
   * | Millisecond           | S     | 1 (100ms)                             |
   * |                       | SS    | 14 (140ms)                            |
   * |                       | SSS   | 145 (145ms)                           |
   * | AM/PM                 | A     | AM or PM                              |
   * |                       | a     | am or pm                              |
   * | Predefined            | LT    | H: 2-digit (2d), m: 2d                |
   * | (uses browser locale) | LTS   | H: 2d, m: 2d, s : 2d                  |
   * |                       | LST   | Depends on 12 or 24 hour clock        |
   * |                       |       | 12h, H : 1d, m : 0 or 2d              |
   * |                       |       | 24h, H : 2d, m : 2d                   |
   * |                       | L     | Y: numeric (n), M : 2d, D : 2d        |
   * |                       | l     | Y: n, M : n, D : n                    |
   * |                       | LL    | Y: n, M : long (l), D : n             |
   * |                       | ll    | Y: n, M : short (s), D : n            |
   * |                       | LLL   | Y: n, M : l, D : n, H: n, m: 2d       |
   * |                       | lll   | Y: n, M : s, D : n, H: n, m: 2d       |
   * |                       | LLLL  | Y: n, M : l, D : n, H: n, m: 2d, d: l |
   * |                       | llll  | Y: n, M : s, D : n, H: n, m: 2d, d: s |
   *
   * Some examples:
   *
   * ```javascript
   * DateHelper.format(new Date(2019, 7, 16), 'dddd') -> Friday
   * DateHelper.format(new Date(2019, 7, 16, 14, 27), 'HH:mm') --> 14:27
   * DateHelper.format(new Date(2019, 7, 16, 14, 27), 'L HH') --> 2019-07-16 14
   * ```
   *
   * Arbitrary text can be embedded in the format string by wrapping it with {}:
   *
   * ```javascript
   * DateHelper.format(new Date(2019, 7, 16), '{It is }dddd{, yay!}') -> It is Friday, yay!
   * ```
   *
   * @param {Date} date Date
   * @param {String} [format] Desired format (uses `defaultFormat` if left out)
   * @returns {String} Formatted string
   * @category Parse & format
   */
  static format(date, format = DH.defaultFormat) {
    if (!date || isNaN(date)) {
      return null;
    }
    let formatter = formatCache[format], output = "";
    if (!formatter) {
      formatter = formatCache[format] = [];
      for (let i = 0; i < format.length; i++) {
        const formatMatch = format.slice(i).match(formatRegexp), predefined = formatMatch == null ? void 0 : formatMatch[0];
        if (predefined) {
          const localeFormats = this.localize("L{formats}") || {}, fn = localeFormats[predefined] || formats[predefined];
          formatter.push(fn);
          i += predefined.length - 1;
        } else if (format[i] === "{") {
          const index = format.indexOf("}", i + 1);
          if (index === -1) {
            formatter.push(format.substr(i + 1));
            i = format.length;
          } else {
            formatter.push(format.substring(i + 1, index));
            i = index;
          }
        } else {
          formatter.push(format[i]);
        }
      }
    }
    formatter.forEach((step) => {
      if (typeof step === "string") {
        output += step;
      } else {
        output += step(date);
      }
    });
    return output;
  }
  /**
   * Formats a range of `dates` using the specified `format`. Because two dates are involved, the `format` specifier
   * uses the tokens `S{}` and `E{}`. The text contained between the `{}` is the {@link #function-format-static format}
   * for the start date or end date, respectively. Text not inside these tokens is retained verbatim.
   *
   * For example:
   *
   * ```javascript
   *  DateHelper.formatRange(dates, 'S{DD MMM YYYY} - E{DD MMM YYYY}');
   * ```
   *
   * The above will format `dates[0]` based on the `S{DD MMM YYYY}` segment and `dates[1] using `E{DD MMM YYYY}`. The
   * `' - '` between these will remain between the two formatted dates.
   *
   * @param {Date[]} dates An array of start date and end date (`[startDate, endDate]`)
   * @param {String} format The format specifier
   * @returns {String}
   */
  static formatRange(dates, format) {
    return format.replace(
      rangeFormatPartRe,
      (s, which, fmt) => _DateHelper.format(dates[which === "S" ? 0 : 1], fmt)
    );
  }
  /**
   * Converts the specified amount of desired unit into milliseconds. Can be called by only specifying a unit as the
   * first argument, it then uses `amount = 1`.
   *
   * For example:
   *
   * ```javascript
   * asMilliseconds('hour') == asMilliseconds(1, 'hour')
   * ```
   *
   * @param {Number|String} amount Amount, what of is decided by specifying unit (also takes a unit which implies an amount of 1)
   * @param {String} [unit] Time unit (s, hour, months etc.)
   * @returns {Number}
   * @category Parse & format
   */
  static asMilliseconds(amount, unit = null) {
    if (typeof amount === "string") {
      unit = amount;
      amount = 1;
    }
    return DH.as("millisecond", amount, unit);
  }
  /**
   * Converts the passed Date to an accurate number of months passed since the epoch start.
   * @param {Date} time The Date to find the month value of
   * @returns {Number} The number of months since the system time epoch start. May be a fractional value
   */
  static asMonths(time) {
    const monthLength = DH.as("ms", DH.daysInMonth(time), "day"), fraction = (time.valueOf() - DH.startOf(time, "month").valueOf()) / monthLength;
    return time.getYear() * 12 + time.getMonth() + fraction;
  }
  static monthsToDate(months) {
    const intMonths = Math.floor(months), fraction = months - intMonths, result = new Date(0, intMonths), msInMonth = DH.as("ms", DH.daysInMonth(result), "days");
    result.setTime(result.getTime() + fraction * msInMonth);
    return result;
  }
  /**
   * Converts a millisecond time delta to a human-readable form. For example `1000 * 60 * 60 * 50`
   * milliseconds would be rendered as `'2 days, 2 hours'`.
   * @param {Number} delta The millisecond delta value
   * @param {Object} [options] Formatting options
   * @param {Boolean} [options.abbrev] Pass `true` to use abbreviated unit names, eg. `'2d, 2h'` for the above example
   * @param {String} [options.precision] The minimum precision unit
   * @param {String} [options.separator] The separator to use between magnitude and unit
   * @param {String} [options.unitSeparator] The separator to use between each duration part, defaults to ", " eg. `'2d, 2h'`
   * @param {Boolean} [options.asString] Pass `false` to return the result as an array, eg ['2d', '2h'] for the above example
   * @returns {String} Formatted string
   * @category Parse & format
   */
  static formatDelta(delta, options) {
    let abbrev, unitName, unitSeparator = ", ";
    if (typeof options === "boolean") {
      abbrev = options;
    } else if (options) {
      abbrev = options.abbrev;
      unitSeparator = options.unitSeparator || unitSeparator;
    }
    const deltaObj = this.getDelta(delta, options), result = [], sep = (options == null ? void 0 : options.separator) || (abbrev ? "" : " ");
    for (unitName in deltaObj) {
      result.push(`${deltaObj[unitName]}${sep}${unitName}`);
    }
    return (options == null ? void 0 : options.asString) === false ? result : result.join(unitSeparator);
  }
  /**
   * Converts a millisecond time delta to an object structure. For example `1000 * 60 * 60 * 50`
   * milliseconds the result would be as:
   *
   * ```javascript
   * {
   *     day  : 2,
   *     hour : 2
   * }
   *```
   *
   * @param {Number} delta The millisecond delta value
   * @param {Object} [options] Formatting options
   * @param {Boolean} [options.abbrev] Pass `true` to use abbreviated unit names, eg `{ d: 2, h: 2 }` for the above example
   * @param {String} [options.precision] The minimum precision unit
   * @param {Boolean} [options.ignoreLocale] Pass true to return unlocalized unit name. Requires `abbrev` to be false
   * @param {String} [options.maxUnit] Name of the maximum unit in the output. e.g. if you pass `day` then you'll get
   * `{ h: 25 }` instead of `{ d: 1, h: 1 }`
   * @returns {Object} The object with the values for each unit
   */
  static getDelta(delta, options) {
    let abbrev, d, done, precision, unitName, maxUnit, ignoreLocale;
    if (typeof options === "boolean") {
      abbrev = options;
    } else if (options) {
      abbrev = options.abbrev;
      precision = DH.normalizeUnit(options.precision);
      maxUnit = options.maxUnit;
      ignoreLocale = !abbrev && options.ignoreLocale;
    }
    const result = {}, getUnit = abbrev ? DH.getShortNameOfUnit : DH.getLocalizedNameOfUnit;
    const units = maxUnit ? deltaUnits.slice(deltaUnits.indexOf(maxUnit)) : deltaUnits;
    for (unitName of units) {
      d = DH.as(unitName, delta);
      done = precision === unitName;
      d = Math[done ? "round" : "floor"](d);
      if (d || done && !result.length) {
        result[ignoreLocale ? unitName : getUnit.call(DH, unitName, d !== 1)] = d;
        delta -= DH.as("ms", d, unitName);
      }
      if (done || !delta) {
        break;
      }
    }
    return result;
  }
  /**
   * Converts the specified amount of one unit (`fromUnit`) into an amount of another unit (`toUnit`).
   * @param {String} toUnit The name of units to convert to, eg: `'ms'`
   * @param {Number|String} amount The time to convert. Either the magnitude number form or a duration string such as '2d'
   * @param {String} [fromUnit='ms'] If the amount was passed as a number, the units to use to convert from
   * @returns {Number}
   * @category Parse & format
   */
  static as(toUnit, amount, fromUnit = "ms") {
    if (typeof amount === "string") {
      amount = DH.parseDuration(amount);
    }
    if (typeof amount === "object") {
      fromUnit = amount.unit;
      amount = amount.magnitude;
    }
    if (toUnit === fromUnit) {
      return amount;
    }
    toUnit = DH.normalizeUnit(toUnit);
    fromUnit = DH.normalizeUnit(fromUnit);
    if (toUnit === fromUnit) {
      return amount;
    } else if (unitMagnitudes[fromUnit] > unitMagnitudes[toUnit]) {
      return amount * Math.abs(validConversions[fromUnit][toUnit]);
    } else {
      return amount / Math.abs(validConversions[toUnit][fromUnit]);
    }
  }
  static formatContainsHourInfo(format) {
    const stripEscapeRe = /(\\.)/g, hourInfoRe = /([HhKkmSsAa]|LT|L{3,}|l{3,})/;
    return hourInfoRe.test(format.replace(stripEscapeRe, ""));
  }
  /**
   * Returns `true` for 24-hour format.
   * @param {String} format Date format
   * @returns {Boolean} `true` for 24-hour format
   * @category Parse & format
   */
  static is24HourFormat(format) {
    return DH.format(DH.getTime(13, 0, 0), format).includes("13");
  }
  //endregion
  //region Manipulate
  /**
   * Add days, hours etc. to a date. Always clones the date, original will be left unaffected.
   * @param {Date|String} date Original date
   * @param {Number|String|Core.data.Duration|DurationConfig} amount Amount of days, hours etc. or a string representation of a duration
   * as accepted by {@link #function-parseDuration-static} or an object with `{ magnitude, unit }` properties
   * @param {DurationUnitShort} [unit='ms'] Unit for amount
   * @privateparam {Boolean} [clone=true] Pass `false` to affect the original
   * @returns {Date} New calculated date
   * @category Manipulate
   */
  static add(date, amount, unit = "ms", clone = true) {
    let d;
    if (typeof date === "string") {
      d = DH.parse(date);
    } else if (clone) {
      d = new Date(date.getTime());
    } else {
      d = date;
    }
    if (typeof amount === "string") {
      const duration = _DateHelper.parseDuration(amount);
      amount = duration.magnitude;
      unit = duration.unit;
    } else if (amount && typeof amount === "object") {
      unit = amount.unit;
      amount = amount.magnitude;
    }
    if (!unit || amount === 0) {
      return d;
    }
    unit = DH.normalizeUnit(unit);
    switch (unit) {
      case "millisecond":
        d.setTime(d.getTime() + amount);
        break;
      case "second":
        d.setTime(d.getTime() + amount * 1e3);
        break;
      case "minute":
        d.setTime(d.getTime() + amount * 6e4);
        break;
      case "hour":
        d.setTime(d.getTime() + amount * 36e5);
        break;
      case "day":
        if (amount % 1 === 0) {
          d.setDate(d.getDate() + amount);
          if (d.getHours() === 23 && date.getHours() === 0) {
            d.setHours(d.getHours() + 1);
          }
        } else {
          d.setTime(d.getTime() + amount * 864e5);
        }
        break;
      case "week":
        d.setDate(d.getDate() + amount * 7);
        break;
      case "month": {
        let day = d.getDate();
        if (day > 28) {
          day = Math.min(day, DH.getLastDateOfMonth(DH.add(DH.getFirstDateOfMonth(d), amount, "month")).getDate());
        }
        d.setDate(day);
        d.setMonth(d.getMonth() + amount);
        break;
      }
      case "quarter":
        DH.add(d, amount * 3, "month", false);
        break;
      case "year":
        d.setFullYear(d.getFullYear() + amount);
        break;
      case "decade":
        d.setFullYear(d.getFullYear() + amount * 10);
        break;
    }
    return d;
  }
  /**
   * Calculates the difference between two dates, in the specified unit.
   * @param {Date} start First date
   * @param {Date} end Second date
   * @param {DurationUnitShort} [unit='ms'] Unit to calculate difference in
   * @param {Boolean} [fractional=true] Specify false to round result
   * @returns {Number} Difference in the specified unit
   * @category Manipulate
   */
  static diff(start, end, unit = "ms", fractional = true) {
    unit = DH.normalizeUnit(unit);
    if (!start || !end)
      return 0;
    let amount;
    switch (unit) {
      case "year":
        amount = DH.diff(start, end, "month") / 12;
        break;
      case "quarter":
        amount = DH.diff(start, end, "month") / 3;
        break;
      case "month":
        amount = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());
        if (amount === 0 && fractional) {
          amount = DH.diff(start, end, "day", fractional) / DH.daysInMonth(start);
        }
        break;
      case "week":
        amount = DH.diff(start, end, "day") / 7;
        break;
      case "day": {
        const dstDiff = start.getTimezoneOffset() - end.getTimezoneOffset();
        amount = (end - start + dstDiff * 60 * 1e3) / 864e5;
        break;
      }
      case "hour":
        amount = (end - start) / 36e5;
        break;
      case "minute":
        amount = (end - start) / 6e4;
        break;
      case "second":
        amount = (end - start) / 1e3;
        break;
      case "millisecond":
        amount = end - start;
        break;
    }
    return fractional ? amount : Math.round(amount);
  }
  /**
   * Sets the date to the start of the specified unit, by default returning a clone of the date instead of changing it
   * in place.
   * @param {Date} date Original date
   * @param {String} [unit='day'] Start of this unit, `'day'`, `'month'` etc
   * @param {Boolean} [clone=true] Manipulate a copy of the date
   * @param {Number} [weekStartDay] The first day of week, `0-6` (Sunday-Saturday). Defaults to the {@link #property-weekStartDay-static}
   * @returns {Date} Manipulated date
   * @category Manipulate
   */
  static startOf(date, unit = "day", clone = true, weekStartDay = DH.weekStartDay) {
    if (!date) {
      return null;
    }
    unit = DH.normalizeUnit(unit);
    if (clone) {
      date = DH.clone(date);
    }
    switch (unit) {
      case "year":
        date.setMonth(0, 1);
        date.setHours(0, 0, 0, 0);
        return date;
      case "quarter":
        date.setMonth((DH.get(date, "quarter") - 1) * 3, 1);
        date.setHours(0, 0, 0, 0);
        return date;
      case "month":
        date.setDate(1);
        date.setHours(0, 0, 0, 0);
        return date;
      case "week": {
        const dayOfWeek = date.getDay();
        let delta = dayOfWeek - weekStartDay;
        if (delta < 0) {
          delta += 7;
        }
        date.setDate(date.getDate() - delta);
        date.setHours(0, 0, 0, 0);
        return date;
      }
      case "day":
        date.setHours(0, 0, 0, 0);
        return date;
      case "hour":
        date.getMinutes() > 0 && date.setMinutes(0);
      case "minute":
        date.getSeconds() > 0 && date.setSeconds(0);
      case "second":
        date.getMilliseconds() > 0 && date.setMilliseconds(0);
      case "millisecond":
        return date;
    }
  }
  /**
   * Returns the end point of the passed date, that is 00:00:00 of the day after the passed date.
   * @param {Date} date The date to return the end point of
   * @returns {Date} Manipulated date
   */
  static endOf(date) {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);
  }
  /**
   * Creates a clone of the specified date
   * @param {Date} date Original date
   * @returns {Date} Cloned date
   * @category Manipulate
   */
  static clone(date) {
    return new Date(date.getTime());
  }
  /**
   * Removes time from a date (same as calling {@link #function-startOf-static startOf(date)}).
   * @param {Date} date Date to remove time from
   * @param {Boolean} [clone=true] Manipulate a copy of the date
   * @returns {Date} Manipulated date
   * @category Manipulate
   */
  static clearTime(date, clone = true) {
    if (!date) {
      return null;
    }
    if (clone) {
      date = new Date(date.getTime());
    }
    date.setHours(0, 0, 0, 0);
    return date;
  }
  static midnight(date, inclusive) {
    let ret = DH.clearTime(date);
    if (inclusive && ret < date) {
      ret = DH.add(ret, 1, "d");
    }
    return ret;
  }
  static isMidnight(date) {
    return DH.midnight(date).getTime() === date.getTime();
  }
  /**
   * Returns the elapsed milliseconds from the start of the specified date.
   * @param {Date} date Date to remove date from
   * @param {DurationUnitShort} [unit='ms'] The time unit to return
   * @returns {Number} The elapsed milliseconds from the start of the specified date
   * @category Manipulate
   */
  static getTimeOfDay(date, unit = "ms") {
    const t = date.getHours() * validConversions.hour.millisecond + date.getMinutes() * validConversions.minute.millisecond + date.getSeconds() * validConversions.second.millisecond + date.getMilliseconds();
    return unit === "ms" ? t : DH.as(unit, t, "ms");
  }
  /**
   * Sets a part of a date (in place).
   * @param {Date} date Date to manipulate
   * @param {String|Object} unit Part of date to set, for example `'minute'`. Or an object like `{ second: 1, minute: 1 }`
   * @param {Number} amount Value to set
   * @returns {Date} Passed date instance modified according to the arguments
   * @category Manipulate
   */
  static set(date, unit, amount) {
    if (!unit) {
      return date;
    }
    if (typeof unit === "string") {
      switch (DH.normalizeUnit(unit)) {
        case "millisecond":
          if (amount !== 0 || date.getMilliseconds() > 0) {
            date.setMilliseconds(amount);
          }
          break;
        case "second":
          if (amount !== 0 || date.getSeconds() > 0) {
            date.setSeconds(amount);
          }
          break;
        case "minute":
          if (amount !== 0 || date.getMinutes() > 0) {
            date.setMinutes(amount);
          }
          break;
        case "hour":
          date.setHours(amount);
          break;
        case "day":
        case "date":
          date.setDate(amount);
          break;
        case "week":
          throw new Error("week not implemented");
        case "month":
          date.setMonth(amount);
          break;
        case "quarter":
          date.setDate(1);
          date.setMonth((amount - 1) * 3);
          break;
        case "year":
          date.setFullYear(amount);
          break;
      }
    } else {
      Object.entries(unit).sort((a, b) => unitMagnitudes[a[0]] - unitMagnitudes[b[0]]).forEach(([unit2, amount2]) => {
        DH.set(date, unit2, amount2);
      });
    }
    return date;
  }
  static setDateToMidday(date, clone = true) {
    return DH.set(DH.clearTime(date, clone), "hour", 12);
  }
  /**
   * Constrains the date within a min and a max date.
   * @param {Date} date The date to constrain
   * @param {Date} [min] Min date
   * @param {Date} [max] Max date
   * @returns {Date} The constrained date
   * @category Manipulate
   */
  static constrain(date, min, max) {
    if (min != null) {
      date = DH.max(date, min);
    }
    return max == null ? date : DH.min(date, max);
  }
  /**
   * Returns time with default year, month, and day (Jan 1, 2020).
   * @param {Number|Date} hours Hours value or the full date to extract the time of
   * @param {Number} [minutes=0] Minutes value
   * @param {Number} [seconds=0] Seconds value
   * @param {Number} [ms=0] Milliseconds value
   * @returns {Date} A new default date with the time extracted from the given date or from the time values provided individually
   * @category Manipulate
   */
  static getTime(hours, minutes = 0, seconds = 0, ms = 0) {
    if (hours instanceof Date) {
      ms = hours.getMilliseconds();
      seconds = hours.getSeconds();
      minutes = hours.getMinutes();
      hours = hours.getHours();
    }
    return new Date(DEFAULT_YEAR, DEFAULT_MONTH, DEFAULT_DAY, hours, minutes, seconds, ms);
  }
  /**
   * Copies hours, minutes, seconds, milliseconds from one date to another.
   *
   * @param {Date} targetDate The target date
   * @param {Date} sourceDate The source date
   * @returns {Date} The adjusted target date
   * @category Manipulate
   * @static
   */
  static copyTimeValues(targetDate, sourceDate) {
    targetDate.setHours(sourceDate.getHours());
    targetDate.setMinutes(sourceDate.getMinutes());
    targetDate.setSeconds(sourceDate.getSeconds());
    targetDate.setMilliseconds(sourceDate.getMilliseconds());
    return targetDate;
  }
  /**
   * Combines the hours, minutes, seconds, milliseconds from one date with the year, month, and date of another.
   * The result is returned in a new `Date` instance
   *
   * @param {Date} date The `Date` object providing the day, month, and year components.
   * @param {Date} time The `Date` object providing the hours, minutes, seconds, and milliseconds components.
   * @returns {Date} A new `Date` combining the date from `date` and the time from `time`.
   * @category Manipulate
   * @static
   * @internal
   */
  static combineDateAndTime(date, time) {
    return _DateHelper.copyTimeValues(new Date(date), time);
  }
  //endregion
  //region Comparison
  static get isDSTEnabled() {
    const year = (/* @__PURE__ */ new Date()).getFullYear(), jan = new Date(year, 0, 1), jul = new Date(year, 6, 1);
    return jan.getTimezoneOffset() !== jul.getTimezoneOffset();
  }
  static isDST(date) {
    const year = date.getFullYear(), jan = new Date(year, 0, 1), jul = new Date(year, 6, 1);
    return date.getTimezoneOffset() < Math.max(jan.getTimezoneOffset(), jul.getTimezoneOffset());
  }
  /**
   * Determines if a date precedes another.
   * @param {Date} first First date
   * @param {Date} second Second date
   * @returns {Boolean} `true` if first precedes second, otherwise false
   * @category Comparison
   */
  static isBefore(first, second) {
    return first < second;
  }
  /**
   * Determines if a date succeeds another.
   * @param {Date} first First date
   * @param {Date} second Second date
   * @returns {Boolean} `true` if first succeeds second, otherwise false
   * @category Comparison
   */
  static isAfter(first, second) {
    return first > second;
  }
  /**
   * Checks if two dates are equal.
   * @param {Date} first First date
   * @param {Date} second Second date
   * @param {String} [unit] Unit to calculate difference in. If not given, the comparison will be done up to a millisecond
   * @returns {Boolean} `true` if the dates are equal
   * @category Comparison
   */
  static isEqual(first, second, unit = null) {
    if (unit === null) {
      return first && second && first.getTime() === second.getTime();
    }
    return DH.startOf(first, unit) - DH.startOf(second, unit) === 0;
  }
  /**
   * Compares two dates using the specified precision.
   * @param {Date} first First date
   * @param {Date} second Second date
   * @param {String} [unit] Unit to calculate difference in. If not given, the comparison will be done up to a millisecond
   * @returns {Number} `0` = equal, `-1` = first before second, `1` = first after second
   * @category Comparison
   */
  static compare(first, second, unit = null) {
    if (unit) {
      first = DH.startOf(first, unit);
      second = DH.startOf(second, unit);
    }
    if (first < second)
      return -1;
    if (first > second)
      return 1;
    return 0;
  }
  /**
   * Coerces the passed Date between the passed minimum and maximum values.
   * @param {Date} date The date to clamp between the `min` and `max`
   * @param {Date} min The minimum Date
   * @param {Date} max The maximum Date
   * @returns {Date} If the passed `date` is valid, a *new* Date object which is clamped between the `min` and `max`
   */
  static clamp(date, min, max) {
    if (!isNaN(date)) {
      if (min != null) {
        date = Math.max(date, min);
      }
      if (max != null) {
        date = Math.min(date, max);
      }
      return new Date(date);
    }
  }
  static isSameDate(first, second) {
    return DH.compare(first, second, "d") === 0;
  }
  static isSameTime(first, second) {
    return first.getHours() === second.getHours() && first.getMinutes() === second.getMinutes() && first.getSeconds() === second.getSeconds() && first.getMilliseconds() === second.getMilliseconds();
  }
  /**
   * Checks if date is the start of specified unit.
   * @param {Date} date Date
   * @param {DurationUnit} unit Time unit
   * @returns {Boolean} `true` if date is the start of specified unit
   * @category Comparison
   */
  static isStartOf(date, unit) {
    return DH.isEqual(date, DH.startOf(date, unit));
  }
  /**
   * Checks if this date is `>= start` and `< end`.
   * @param {Date} date The source date
   * @param {Date} start Start date
   * @param {Date} end End date
   * @returns {Boolean} `true` if this date falls on or between the given start and end dates
   * @category Comparison
   */
  static betweenLesser(date, start, end) {
    return start.getTime() <= date.getTime() && date.getTime() < end.getTime();
  }
  /**
   * Checks if this date is `>= start` and `<= end`.
   * @param {Date} date The source date
   * @param {Date} start Start date
   * @param {Date} end End date
   * @returns {Boolean} `true` if this date falls on or between the given start and end dates
   * @category Comparison
   */
  static betweenLesserEqual(date, start, end) {
    return start.getTime() <= date.getTime() && date.getTime() <= end.getTime();
  }
  /**
   * Returns `true` if dates intersect.
   * @param {Date} date1Start Start date of first span
   * @param {Date} date1End End date of first span
   * @param {Date} date2Start Start date of second span
   * @param {Date} date2End End date of second span
   * @returns {Boolean} Returns `true` if dates intersect
   * @category Comparison
   */
  static intersectSpans(date1Start, date1End, date2Start, date2End) {
    return DH.betweenLesser(date1Start, date2Start, date2End) || DH.betweenLesser(date2Start, date1Start, date1End);
  }
  /**
   * Compare two units. Returns `1` if first param is a greater unit than second param, `-1` if the opposite is true or `0` if they're equal.
   * @param {DurationUnit} unit1 The 1st unit
   * @param {DurationUnit} unit2 The 2nd unit
   * @returns {Number} Returns `1` if first param is a greater unit than second param, `-1` if the opposite is true or `0` if they're equal
   * @category Comparison
   */
  static compareUnits(unit1, unit2) {
    return Math.sign(unitMagnitudes[DH.normalizeUnit(unit1)] - unitMagnitudes[DH.normalizeUnit(unit2)]);
  }
  /**
   * Returns `true` if the first time span completely 'covers' the second time span.
   *
   * ```javascript
   * DateHelper.timeSpanContains(
   *     new Date(2010, 1, 2),
   *     new Date(2010, 1, 5),
   *     new Date(2010, 1, 3),
   *     new Date(2010, 1, 4)
   * ) ==> true
   * DateHelper.timeSpanContains(
   *   new Date(2010, 1, 2),
   *   new Date(2010, 1, 5),
   *   new Date(2010, 1, 3),
   *   new Date(2010, 1, 6)
   * ) ==> false
   * ```
   *
   * @param {Date} spanStart The start date for initial time span
   * @param {Date} spanEnd The end date for initial time span
   * @param {Date} otherSpanStart The start date for the 2nd time span
   * @param {Date} otherSpanEnd The end date for the 2nd time span
   * @returns {Boolean} `true` if the first time span completely 'covers' the second time span
   * @category Comparison
   */
  static timeSpanContains(spanStart, spanEnd, otherSpanStart, otherSpanEnd) {
    return otherSpanStart - spanStart >= 0 && spanEnd - otherSpanEnd >= 0;
  }
  //endregion
  //region Query
  /**
   * Get the first day of week, 0-6 (Sunday-Saturday).
   * This is determined by the current locale's `DateHelper.weekStartDay` parameter.
   * @property {Number}
   * @readonly
   */
  static get weekStartDay() {
    if (DH._weekStartDay == null) {
      DH._weekStartDay = this.localize("L{weekStartDay}") || 0;
    }
    return DH._weekStartDay;
  }
  /**
   * Get non-working days as an object where keys are day indices, 0-6 (Sunday-Saturday), and the value is `true`.
   * This is determined by the current locale's `DateHelper.nonWorkingDays` parameter.
   *
   * For example:
   * ```javascript
   * {
   *     0 : true, // Sunday
   *     6 : true  // Saturday
   * }
   * ```
   *
   * @property {Object<Number,Boolean>}
   * @readonly
   */
  static get nonWorkingDays() {
    return { ...this.localize("L{nonWorkingDays}") };
  }
  /**
   * Get non-working days as an array of day indices, 0-6 (Sunday-Saturday).
   * This is determined by the current locale's `DateHelper.nonWorkingDays` parameter.
   *
   * For example:
   *
   * ```javascript
   * [ 0, 6 ] // Sunday & Saturday
   * ```
   *
   * @property {Number[]}
   * @readonly
   * @internal
   */
  static get nonWorkingDaysAsArray() {
    return Object.keys(this.nonWorkingDays).map(Number);
  }
  /**
   * Get weekend days as an object where keys are day indices, 0-6 (Sunday-Saturday), and the value is `true`.
   * Weekends are days which are declared as weekend days by the selected country and defined by the current locale's
   * `DateHelper.weekends` parameter.
   * To get non-working days see {@link #property-nonWorkingDays-static}.
   *
   * For example:
   * ```javascript
   * {
   *     0 : true, // Sunday
   *     6 : true  // Saturday
   * }
   * ```
   * @property {Object<Number,Boolean>}
   * @readonly
   * @internal
   */
  static get weekends() {
    return { ...this.localize("L{weekends}") };
  }
  /**
   * Get the specified part of a date.
   * @param {Date} date
   * @param {DurationUnit} unit Part of date, hour, minute etc.
   * @returns {Number} The requested part of the specified date
   * @category Query
   */
  static get(date, unit) {
    switch (DH.normalizeUnit(unit)) {
      case "millisecond":
        return date.getMilliseconds();
      case "second":
        return date.getSeconds();
      case "minute":
        return date.getMinutes();
      case "hour":
        return date.getHours();
      case "date":
      case "day":
        return date.getDate();
      case "week":
        return formats.W(date);
      case "month":
        return date.getMonth();
      case "quarter":
        return Math.floor(date.getMonth() / 3) + 1;
      case "year":
        return date.getFullYear();
    }
    return null;
  }
  /**
   * Get number of days in the current year for the supplied date.
   * @param {Date} date Date to check
   * @returns {Number} Days in year
   * @category Query
   * @internal
   */
  static daysInYear(date) {
    const fullYear = date.getFullYear(), duration = new Date(fullYear + 1, 0, 1) - new Date(fullYear, 0, 1);
    return this.as("day", duration);
  }
  /**
   * Get number of days in the current month for the supplied date.
   * @param {Date} date Date which month should be checked
   * @returns {Number} Days in month
   * @category Query
   */
  static daysInMonth(date) {
    return 32 - new Date(date.getFullYear(), date.getMonth(), 32).getDate();
  }
  /**
   * Get number of hours in the current day for the supplied date.
   * @param {Date} date Date to check
   * @returns {Number} Hours in day
   * @category Query
   * @internal
   */
  static hoursInDay(date) {
    const fullYear = date.getFullYear(), month = date.getMonth(), day = date.getDate(), duration = new Date(fullYear, month, day + 1) - new Date(fullYear, month, day);
    return this.as("hour", duration);
  }
  /**
   * Converts unit related to the date to actual amount of milliseconds in it. Takes into account leap years and
   * different duration of months.
   * @param {Date} date Date
   * @param {DurationUnit} unit Time unit
   * @returns {Number} Returns amount of milliseconds
   * @internal
   */
  static getNormalizedUnitDuration(date, unit) {
    let result;
    switch (unit) {
      case "month":
        result = DH.asMilliseconds(DH.daysInMonth(date), "day");
        break;
      case "year":
        result = DH.asMilliseconds(DH.daysInYear(date), "day");
        break;
      case "day":
        result = DH.asMilliseconds(DH.hoursInDay(date), "hour");
        break;
      default:
        result = DH.asMilliseconds(unit);
    }
    return result;
  }
  /**
   * Get the first date of the month for the supplied date.
   * @param {Date} date Date
   * @returns {Date} New Date instance
   * @category Query
   */
  static getFirstDateOfMonth(date) {
    return new Date(date.getFullYear(), date.getMonth(), 1);
  }
  /**
   * Get the last date of the month for the supplied date.
   * @param {Date} date Date
   * @returns {Date} New Date instance
   * @category Query
   */
  static getLastDateOfMonth(date) {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0);
  }
  /**
   * Get the earliest of two dates.
   * @param {Date} first First date
   * @param {Date} second Second date
   * @returns {Date} Earliest date
   * @category Query
   */
  static min(first, second) {
    return first.getTime() < second.getTime() ? first : second;
  }
  /**
   * Get the latest of two dates.
   * @param {Date} first First date
   * @param {Date} second Second date
   * @returns {Date} Latest date
   * @category Query
   */
  static max(first, second) {
    return first.getTime() > second.getTime() ? first : second;
  }
  /**
   * Get an incremented date. Incrementation based on specified unit and optional amount.
   * @param {Date} date Date
   * @param {DurationUnit} unit Time unit
   * @param {Number} [increment=1] Increment amount
   * @param {Number} [weekStartDay] Will default to what is set in locale
   * @returns {Date} New Date instance
   * @category Query
   */
  static getNext(date, unit, increment = 1, weekStartDay = DH.weekStartDay) {
    if (unit === "week") {
      const dt = DH.clone(date), day = dt.getDay();
      DH.startOf(dt, "day", false);
      DH.add(dt, weekStartDay - day + 7 * (increment - (weekStartDay <= day ? 0 : 1)), "day", false);
      if (dt.getDay() !== weekStartDay) {
        DH.add(dt, 1, "hour");
      }
      return dt;
    }
    return DH.startOf(DH.add(date, increment, unit), unit, false);
  }
  /**
   * Checks if date object is valid.
   *
   * For example:
   *
   * ```javascript
   * date = new Date('foo')
   * date instanceof Date // true
   * date.toString() // Invalid Date
   * isNaN(date) // true
   * DateHelper.isValidDate(date) // false
   *
   * date = new Date()
   * date instanceof Date // true
   * date.toString() // Mon Jan 13 2020 18:27:38 GMT+0300 (GMT+03:00)
   * isNaN(date) // false
   * DateHelper.isValidDate(date) // true
   * ```
   *
   * @param {Date} date Date
   * @returns {Boolean} `true` if date object is valid
   */
  static isValidDate(date) {
    return DH.isDate(date) && !isNaN(date);
  }
  /**
   * Checks if value is a date object. Allows to recognize date object even from another context,
   * like the top frame when used in an iframe.
   *
   * @param {*} value Value to check
   * @returns {Boolean} `true` if value is a date object
   */
  static isDate(value) {
    return value && toString.call(value) === DATE_TYPE;
  }
  /**
   * Get the start of the next day.
   * @param {Date} date Date
   * @param {Boolean} [clone=false] Clone date
   * @param {Boolean} [noNeedToClearTime=false] Flag to not clear time from the result
   * @returns {Date} Passed Date or new Date instance, depending on the `clone` flag
   * @category Query
   */
  static getStartOfNextDay(date, clone = false, noNeedToClearTime = false) {
    let nextDay = DH.add(noNeedToClearTime ? date : DH.clearTime(date, clone), 1, "day");
    if (nextDay.getDate() === date.getDate()) {
      const offsetNextDay = DH.add(DH.clearTime(date, clone), 2, "day").getTimezoneOffset(), offsetDate = date.getTimezoneOffset();
      nextDay = DH.add(nextDay, offsetDate - offsetNextDay, "minute");
    }
    return nextDay;
  }
  /**
   * Get the end of previous day.
   * @param {Date} date Date
   * @param {Boolean} [noNeedToClearTime=false] Flag to not clear time from the result
   * @returns {Date} New Date instance
   * @category Query
   */
  static getEndOfPreviousDay(date, noNeedToClearTime = false) {
    const dateOnly = noNeedToClearTime ? date : DH.clearTime(date, true);
    if (dateOnly - date) {
      return dateOnly;
    } else {
      return DH.add(dateOnly, -1, "day");
    }
  }
  /**
   * Returns a string describing the specified week. For example, `'39, September 2020'` or `'40, Sep - Oct 2020'`.
   * @param {Date} startDate Start date
   * @param {Date} [endDate] End date
   * @returns {String} String describing the specified week
   * @internal
   */
  static getWeekDescription(startDate, endDate = startDate) {
    const monthDesc = startDate.getMonth() === endDate.getMonth() ? _DateHelper.format(startDate, "MMMM") : `${_DateHelper.format(startDate, "MMM")} - ${_DateHelper.format(endDate, "MMM")}`, week = _DateHelper.getWeekNumber(startDate);
    return `${week[1]}, ${monthDesc} ${week[0]}`;
  }
  /**
   * Get week number for the date.
   * @param {Date} date The date
   * @param {Number} [weekStartDay] The first day of week, 0-6 (Sunday-Saturday). Defaults to the {@link #property-weekStartDay-static}
   * @returns {Number[]} year and week number
   * @category Query
   */
  static getWeekNumber(date, weekStartDay = _DateHelper.weekStartDay) {
    const jan01 = new Date(date.getFullYear(), 0, 1), dec31 = new Date(date.getFullYear(), 11, 31), firstDay = normalizeDay(jan01.getDay() - weekStartDay), lastDay = normalizeDay(dec31.getDay() - weekStartDay), dayNumber = getDayDiff(date, jan01);
    let weekNumber;
    if (firstDay < 4) {
      weekNumber = Math.floor((dayNumber + firstDay - 1) / 7) + 1;
    } else {
      weekNumber = Math.floor((dayNumber + firstDay - 1) / 7);
    }
    if (weekNumber) {
      let year = date.getFullYear();
      if (weekNumber === 53 && lastDay < 3) {
        year++;
        weekNumber = 1;
      }
      return [year, weekNumber];
    }
    const lastWeekOfLastYear = _DateHelper.getWeekNumber(new Date(date.getFullYear() - 1, 11, 31))[1];
    return [date.getFullYear() - 1, lastWeekOfLastYear];
  }
  //endregion
  //region Unit helpers
  /**
   * Turns `(10, 'day')` into `'10 days'` etc.
   * @param {Number} count Amount of unit
   * @param {DurationUnit} unit Unit, will be normalized (days, d -> day etc.)
   * @returns {String} Amount formatted to string
   * @category Unit helpers
   */
  static formatCount(count, unit) {
    unit = DH.normalizeUnit(unit);
    if (count !== 1)
      unit += "s";
    return count + " " + unit;
  }
  /**
   * Get the ratio between two units ( year, month -> 1/12 ).
   * @param {DurationUnit} baseUnit Base time unit
   * @param {DurationUnit} unit Time unit
   * @param {Boolean} [acceptEstimate=false] If `true`, process negative values of validConversions
   * @returns {Number} Ratio
   * @category Unit helpers
   */
  static getUnitToBaseUnitRatio(baseUnit, unit, acceptEstimate = false) {
    baseUnit = DH.normalizeUnit(baseUnit);
    unit = DH.normalizeUnit(unit);
    if (baseUnit === unit)
      return 1;
    if (validConversions[baseUnit] && validConversions[baseUnit][unit] && (acceptEstimate || validConversions[baseUnit][unit] > 0)) {
      return 1 / DH.as(unit, 1, baseUnit);
    }
    if (validConversions[unit] && validConversions[unit][baseUnit] && (acceptEstimate || validConversions[unit][baseUnit] > 0)) {
      return DH.as(baseUnit, 1, unit);
    }
    return -1;
  }
  /**
   * Returns a localized abbreviated form of the name of the duration unit.
   * For example in the `EN` locale, for `'qrt'` it will return `'q'`.
   * @param {DurationUnit} unit Duration unit
   * @returns {String} Localized abbreviated form of the name of the duration unit
   * @category Unit helpers
   */
  static getShortNameOfUnit(unit) {
    unit = DH.parseTimeUnit(unit);
    return DH.unitLookup[unit].abbrev;
  }
  /**
   * Returns a localized full name of the duration unit.
   *
   * For example in the `EN` locale, for `'d'` it will return either
   * `'day'` or `'days'`, depending on the `plural` argument
   *
   * Preserves casing of first letter.
   *
   * @static
   * @param {DurationUnit} unit Time unit
   * @param {Boolean} [plural=false] Whether to return a plural name or singular
   * @returns {String} Localized full name of the duration unit
   * @category Unit helpers
   */
  static getLocalizedNameOfUnit(unit, plural = false) {
    const capitalize = unit.charAt(0) === unit.charAt(0).toUpperCase();
    unit = DH.normalizeUnit(unit);
    unit = DH.parseTimeUnit(unit);
    unit = DH.unitLookup[unit][plural ? "plural" : "single"];
    if (capitalize) {
      unit = StringHelper.capitalize(unit);
    }
    return unit;
  }
  /**
   * Normalizes a unit for easier usage in conditionals. For example `'year'`, `'years'`, `'y'` -> `'year'`.
   * @param {DurationUnit} unit Time unit
   * @returns {String} Normalized unit name
   * @category Unit helpers
   */
  static normalizeUnit(unit) {
    if (!unit) {
      return null;
    }
    const unitLower = unit.toLowerCase();
    if (unitLower === "date") {
      return unitLower;
    }
    return canonicalUnitNames.includes(unitLower) ? unitLower : normalizedUnits[unit] || normalizedUnits[unitLower];
  }
  static getUnitByName(name) {
    return DH.normalizeUnit(name) || DH.normalizeUnit(DH.parseTimeUnit(name));
  }
  /**
   * Returns a duration of the timeframe in the given unit.
   * @param {Date} start The start date of the timeframe
   * @param {Date} end The end date of the timeframe
   * @param {DurationUnit} unit Duration unit
   * @privateparam {Boolean} [doNotRound]
   * @returns {Number} The duration in the units
   * @category Unit helpers
   * @ignore
   */
  static getDurationInUnit(start, end, unit, doNotRound) {
    return DH.diff(start, end, unit, doNotRound);
  }
  /**
   * Checks if two date units align.
   * @private
   * @param {String} majorUnit Major time unit
   * @param {String} minorUnit Minor time unit
   * @returns {Boolean} `true` if two date units align
   * @category Unit helpers
   */
  static doesUnitsAlign(majorUnit, minorUnit) {
    return !(majorUnit !== minorUnit && minorUnit === "week");
  }
  static getSmallerUnit(unit) {
    return canonicalUnitNames[unitMagnitudes[DH.normalizeUnit(unit)] - 1] || null;
  }
  static getLargerUnit(unit) {
    return canonicalUnitNames[unitMagnitudes[DH.normalizeUnit(unit)] + 1] || null;
  }
  /**
   * Rounds the passed Date value to the nearest `increment` value.
   *
   * Optionally may round relative to a certain base time point.
   *
   * For example `DH.round(new Date('2020-01-01T09:35'), '30 min', new Date('2020-01-01T09:15'))`
   * would round to 9:45 because that's the nearest integer number of 30 minute increments
   * from the base.
   *
   * Note that `base` is ignored when rounding to weeks. The configured {@link #property-weekStartDay-static}
   * dictates what the base of a week is.
   *
   * @param {Date} time The time to round
   * @param {String|Number} increment A millisecond value by which to round the time
   * May be specified in string form eg: `'15 minutes'`
   * @param {Date} [base] The start from which to apply the rounding
   * @param {Number} [weekStartDay] Will default to what is set in locale
   * @returns {Date} New Date instance
   */
  static round(time, increment, base, weekStartDay) {
    return DH.snap("round", time, increment, base, weekStartDay);
  }
  /**
   * Floor the passed Date value to the nearest `increment` value.
   *
   * Optionally may floor relative to a certain base time point.
   *
   * For example `DH.floor(new Date('2020-01-01T09:35'), '30 min', new Date('2020-01-01T09:15'))`
   * would floor to 9:15 because that's the closest lower integer number of 30 minute increments
   * from the base.
   *
   * Note that `base` is ignored when flooring to weeks. The configured {@link #property-weekStartDay-static}
   * dictates what the base of a week is.
   *
   * @param {Date} time The time to floor
   * @param {String|Number|DurationConfig|Object} increment A numeric millisecond value by which to floor the time.
   * or a duration in string form eg `'30 min'` or object form : `{unit: 'minute', magnitude: 30}`
   * or `{unit: 'minute', increment: 30}`
   * @param {Date} [base] The start from which to apply the flooring
   * @param {Number} [weekStartDay] Will default to what is set in locale
   * @returns {Date} New Date instance
   */
  static floor(time, increment, base, weekStartDay) {
    return DH.snap("floor", time, increment, base, weekStartDay);
  }
  /**
   * Ceils the passed Date value to the nearest `increment` value.
   *
   * Optionally may ceil relative to a certain base time point.
   *
   * For example `DH.ceil(new Date('2020-01-01T09:35'), '30 min', new Date('2020-01-01T09:15'))`
   * would ceil to 9:45 because that's the closest higher integer number of 30 minute increments
   * from the base.
   *
   * Note that `base` is ignored when ceiling to weeks. Use weekStartDay argument which default to the configured
   * {@link #property-weekStartDay-static} dictates what the base of a week is
   *
   * @param {Date} time The time to ceil
   * @param {String|Number|DurationConfig|Object} increment A numeric millisecond value by which to ceil the time
   * or a duration in string form eg `'30 min'` or object form : `{unit: 'minute', magnitude: 30}`
   * or `{unit: 'minute', increment: 30}`
   * @param {Date} [base] The start from which to apply the ceiling
   * @param {Number} [weekStartDay] Will default to what is set in locale
   * @returns {Date} New Date instance
   */
  static ceil(time, increment, base, weekStartDay) {
    return DH.snap("ceil", time, increment, base, weekStartDay);
  }
  /**
   * Implementation for round, floor and ceil.
   * @internal
   */
  static snap(operation, time, increment, base, weekStartDay = DH.weekStartDay) {
    const snapFn = snapFns[operation];
    if (typeof increment === "string") {
      increment = DH.parseDuration(increment);
    }
    if (Objects.isObject(increment)) {
      const magnitude = increment.magnitude || increment.increment;
      switch (increment.unit) {
        case "week": {
          if (!base) {
            const weekDay = time.getDay();
            base = DH.add(
              DH.clearTime(time),
              weekDay >= weekStartDay ? weekStartDay - weekDay : -(weekDay - weekStartDay + 7),
              "day"
            );
          }
          return DH[operation](time, `${magnitude * 7} days`, base);
        }
        case "month": {
          time = DH.asMonths(time);
          let resultMonths;
          if (base) {
            base = DH.asMonths(base);
            resultMonths = base + snapFn(time - base, magnitude);
          } else {
            resultMonths = snapFn(time, magnitude);
          }
          return DH.monthsToDate(resultMonths);
        }
        case "quarter":
          return DH[operation](time, `${magnitude * 3} months`, base);
        case "year":
          return DH[operation](time, `${magnitude * 12} months`, base);
        case "decade":
          return DH[operation](time, `${magnitude * 10} years`, base);
      }
      increment = DH.as("ms", magnitude, increment.unit);
    }
    const timeUtcOffset = time.getTimezoneOffset() * 1e3 * 60;
    if (base) {
      const baseUtcOffset = base.getTimezoneOffset() * 1e3 * 60, diff = time - base + baseUtcOffset - timeUtcOffset, snappedDate = new Date(base.getTime() + snapFn(diff, increment)), dstChange = baseUtcOffset - snappedDate.getTimezoneOffset() * 1e3 * 60;
      if (dstChange) {
        DH.add(snappedDate, -dstChange, "ms", false);
      }
      return new Date(snappedDate);
    } else {
      return new Date(snapFn(time.valueOf() - timeUtcOffset, increment) + timeUtcOffset);
    }
  }
  //endregion
  //region Date picker format
  /**
   * Parses a typed duration value according to locale rules.
   *
   * The value is taken to be a string consisting of the numeric magnitude and the units:
   * - The numeric magnitude can be either an integer or a float value. Both `','` and `'.'` are valid decimal separators.
   * - The units may be a recognised unit abbreviation of this locale or the full local unit name.
   *
   * For example:
   * `'2d'`, `'2 d'`, `'2 day'`, `'2 days'` will be turned into `{ magnitude : 2, unit : 'day' }`
   * `'2.5d'`, `'2,5 d'`, `'2.5 day'`, `'2,5 days'` will be turned into `{ magnitude : 2.5, unit : 'day' }`
   *
   * **NOTE:** Doesn't work with complex values like `'2 days, 2 hours'`
   *
   * @param {String} value The value to parse
   * @param {Boolean} [allowDecimals=true] Decimals are allowed in the magnitude
   * @param {String} [defaultUnit] Default unit to use if only magnitude passed
   * @returns {DurationConfig} If successfully parsed, the result contains two properties, `magnitude` being a number, and
   * `unit` being the canonical unit name, *NOT* a localized name. If parsing was unsuccessful, `null` is returned
   * @category Parse & format
   */
  static parseDuration(value, allowDecimals = true, defaultUnit) {
    var _a;
    const durationRegEx = allowDecimals ? withDecimalsDurationRegex : noDecimalsDurationRegex, match = durationRegEx.exec(value);
    if (value == null || !match) {
      return null;
    }
    const magnitude = parseNumber((_a = match[1]) == null ? void 0 : _a.replace(",", ".")), unit = DH.parseTimeUnit(match[2]) || defaultUnit;
    if (!unit) {
      return null;
    }
    return {
      magnitude,
      unit
    };
  }
  /**
   * Parses a typed unit name, for example `'ms'` or `'hr'` or `'yr'` into the
   * canonical form of the unit name which may be passed to {@link #function-add-static}
   * or {@link #function-diff-static}.
   * @param {*} unitName Time unit name
   * @category Parse & format
   */
  static parseTimeUnit(unitName) {
    const unitMatch = unitName == null ? null : DH.durationRegEx.exec(unitName.toLowerCase());
    if (!unitMatch) {
      return null;
    }
    for (let unitOrdinal = 0; unitOrdinal < canonicalUnitNames.length; unitOrdinal++) {
      if (unitMatch[unitOrdinal + 1]) {
        return canonicalUnitNames[unitOrdinal];
      }
    }
  }
  //endregion
  //region Internal
  static getGMTOffset(date = /* @__PURE__ */ new Date()) {
    if (!date) {
      return;
    }
    const offsetInMinutes = date.getTimezoneOffset();
    if (!offsetInMinutes)
      return "Z";
    return (offsetInMinutes > 0 ? "-" : "+") + Math.abs(Math.trunc(offsetInMinutes / 60)).toString().padStart(2, "0") + ":" + Math.abs(offsetInMinutes % 60).toString().padStart(2, "0");
  }
  static fillDayNames() {
    const tempDate2 = /* @__PURE__ */ new Date("2000-01-01T12:00:00"), dayNames = DH._dayNames || [], dayShortNames = DH._dayShortNames || [];
    dayNames.length = 0;
    dayShortNames.length = 0;
    for (let day = 2; day < 9; day++) {
      tempDate2.setDate(day);
      dayNames.push(DH.format(tempDate2, "dddd"));
      dayShortNames.push(DH.format(tempDate2, "ddd"));
    }
    DH._dayNames = dayNames;
    DH._dayShortNames = dayShortNames;
  }
  static getDayNames() {
    return DH._dayNames;
  }
  static getDayName(day) {
    return DH._dayNames[day];
  }
  static getDayShortNames() {
    return DH._dayShortNames;
  }
  static getDayShortName(day) {
    return DH._dayShortNames[day];
  }
  static fillMonthNames() {
    const tempDate2 = /* @__PURE__ */ new Date("2000-01-15T12:00:00"), monthNames = DH._monthNames || [], monthShortNames = DH._monthShortNames || [], monthNamesIndex = {}, monthShortNamesIndex = {};
    monthNames.length = 0;
    monthShortNames.length = 0;
    for (let month = 0; month < 12; month++) {
      tempDate2.setMonth(month);
      const monthName = DH.format(tempDate2, "MMMM");
      monthNames.push(monthName);
      const monthShortName = DH.format(tempDate2, "MMM");
      monthShortNames.push(monthShortName);
      monthNamesIndex[monthName.toLowerCase()] = { name: monthName, value: month };
      monthShortNamesIndex[monthShortName.toLowerCase()] = { name: monthShortName, value: month };
    }
    DH._monthNames = monthNames;
    DH._monthShortNames = monthShortNames;
    DH._monthNamesIndex = monthNamesIndex;
    DH._monthShortNamesIndex = monthShortNamesIndex;
  }
  static getMonthShortNames() {
    return DH._monthShortNames;
  }
  static getMonthShortName(month) {
    return DH._monthShortNames[month];
  }
  static getMonthNames() {
    return DH._monthNames;
  }
  static getMonthName(month) {
    return DH._monthNames[month];
  }
  static set locale(name) {
    locale = name;
    intlFormatterCache = {};
    formatCache = {};
    formatRedirects = {};
  }
  static get locale() {
    return locale;
  }
  static setupDurationRegEx(unitNames = [], unitAbbreviations = []) {
    const me = this, unitLookup = {};
    let unitAbbrRegEx = "";
    for (let i = 0; i < unitAbbreviations.length; i++) {
      const abbreviations = unitAbbreviations[i], unitNamesCfg = unitNames[i];
      unitNamesCfg.canonicalUnitName = canonicalUnitNames[i];
      unitLookup[unitNamesCfg.single] = unitLookup[unitNamesCfg.single.toUpperCase()] = unitLookup[unitNamesCfg.canonicalUnitName] = unitLookup[unitNamesCfg.canonicalUnitName.toUpperCase()] = unitNamesCfg;
      unitAbbrRegEx += `${i ? "|" : ""}(`;
      for (let j = 0; j < abbreviations.length; j++) {
        unitAbbrRegEx += `${abbreviations[j].toLowerCase()}|`;
      }
      locale = me.localize("L{locale}") || "en-US";
      if (locale !== "en-US") {
        const canonicalAbbreviations = canonicalUnitAbbreviations[i];
        for (let j = 0; j < canonicalAbbreviations.length; j++) {
          unitAbbrRegEx += `${canonicalAbbreviations[j]}|`;
        }
      }
      unitAbbrRegEx += `${unitNamesCfg.single.toLowerCase()}|${unitNamesCfg.plural.toLowerCase()}|${unitNamesCfg.canonicalUnitName}|${unitNamesCfg.canonicalUnitName}s)`;
    }
    me.unitLookup = unitLookup;
    me.durationRegEx = new RegExp(`^(?:${unitAbbrRegEx})$`);
  }
  static applyLocale() {
    const me = this, unitAbbreviations = me.localize("L{unitAbbreviations}") || [], unitNames = me.unitNames = me.localize("L{unitNames}");
    if (unitNames === "unitNames") {
      return;
    }
    locale = me.localize("L{locale}") || "en-US";
    if (locale === "en-US") {
      ordinalSuffix = enOrdinalSuffix;
    } else {
      ordinalSuffix = me.localize("L{ordinalSuffix}") || ordinalSuffix;
    }
    formatCache = {};
    formatRedirects = {};
    parserCache = {};
    intlFormatterCache = {};
    DH._weekStartDay = null;
    DH.setupDurationRegEx(unitNames, unitAbbreviations);
    DH.fillDayNames();
    DH.fillMonthNames();
  }
  //endregion
};
__publicField(_DateHelper, "MS_PER_DAY", MS_PER_HOUR * 24);
__publicField(_DateHelper, "$name", "DateHelper");
let DateHelper = _DateHelper;
Helpers.register(DateHelper);
const DH = DateHelper;
DH.useIntlFormat = useIntlFormat;
LocaleManager.ion({
  locale: "applyLocale",
  prio: 1e3,
  thisObj: DH
});
if (LocaleManager.locale) {
  DH.applyLocale();
}
class DateSet extends Set {
  get isDateSet() {
    return true;
  }
  add(d) {
    d = DateHelper.makeKey(d);
    if (!this.has(d)) {
      this.generation = (this.generation || 0) + 1;
    }
    return super.add(d);
  }
  delete(d) {
    d = DateHelper.makeKey(d);
    if (this.has(d)) {
      this.generation++;
    }
    return super.delete(d);
  }
  has(d) {
    return super.has(DateHelper.makeKey(d));
  }
  clear() {
    if (this.size) {
      this.generation++;
    }
    return super.clear();
  }
  equals(other) {
    Array.isArray(other) && (other = new DateSet(other));
    return other.size === this.size && [...this].every((s) => other.has(s));
  }
  get dates() {
    return [...this].sort().map((k) => DateHelper.parseKey(k));
  }
}
DateHelper.DateSet = DateSet;
DateHelper._$name = "DateHelper";
export {
  DateHelper as default,
  unitMagnitudes
};
