import LocaleHelper from "@bryntum/core-thin/lib/localization/LocaleHelper.js";
const locale = {
  localeName: "Th",
  localeDesc: "\u0E44\u0E17\u0E22",
  localeCode: "th",
  Object: {
    Yes: "\u0E43\u0E0A\u0E48",
    No: "\u0E44\u0E21\u0E48\u0E43\u0E0A\u0E48",
    Cancel: "\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01",
    Ok: "\u0E15\u0E01\u0E25\u0E07",
    Week: "\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C",
    None: "\u0E44\u0E21\u0E48\u0E21\u0E35"
  },
  CodeEditor: {
    apply: "\u0E43\u0E0A\u0E49",
    autoApply: "\u0E43\u0E0A\u0E49\u0E42\u0E14\u0E22\u0E2D\u0E31\u0E15\u0E42\u0E19\u0E21\u0E31\u0E15\u0E34",
    downloadCode: "\u0E14\u0E32\u0E27\u0E19\u0E4C\u0E42\u0E2B\u0E25\u0E14\u0E42\u0E04\u0E49\u0E14",
    editor: "\u0E15\u0E31\u0E27\u0E41\u0E01\u0E49\u0E44\u0E02\u0E42\u0E04\u0E49\u0E14",
    viewer: "\u0E15\u0E31\u0E27\u0E14\u0E39\u0E42\u0E04\u0E49\u0E14"
  },
  ColorPicker: {
    noColor: "\u0E44\u0E21\u0E48\u0E21\u0E35\u0E2A\u0E35"
  },
  Combo: {
    noResults: "\u0E44\u0E21\u0E48\u0E1E\u0E1A\u0E1C\u0E25\u0E25\u0E31\u0E1E\u0E18\u0E4C",
    recordNotCommitted: "\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01\u0E44\u0E14\u0E49",
    addNewValue: (value) => `\u0E40\u0E1E\u0E34\u0E48\u0E21 ${value}`
  },
  FilePicker: {
    file: "\u0E44\u0E1F\u0E25\u0E4C"
  },
  Field: {
    badInput: "\u0E04\u0E48\u0E32\u0E43\u0E19\u0E0A\u0E48\u0E2D\u0E07\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07",
    patternMismatch: "\u0E04\u0E48\u0E32\u0E04\u0E27\u0E23\u0E15\u0E23\u0E07\u0E01\u0E31\u0E1A\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E17\u0E35\u0E48\u0E40\u0E09\u0E1E\u0E32\u0E30\u0E40\u0E08\u0E32\u0E30\u0E08\u0E07",
    rangeOverflow: (value) => `\u0E04\u0E48\u0E32\u0E04\u0E27\u0E23\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32\u0E2B\u0E23\u0E37\u0E2D\u0E40\u0E17\u0E48\u0E32\u0E01\u0E31\u0E1A ${value.max}`,
    rangeUnderflow: (value) => `\u0E04\u0E48\u0E32\u0E04\u0E27\u0E23\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32\u0E2B\u0E23\u0E37\u0E2D\u0E40\u0E17\u0E48\u0E32\u0E01\u0E31\u0E1A ${value.min}`,
    stepMismatch: "\u0E04\u0E48\u0E32\u0E04\u0E27\u0E23\u0E2A\u0E2D\u0E14\u0E04\u0E25\u0E49\u0E2D\u0E07\u0E01\u0E31\u0E1A\u0E01\u0E32\u0E23\u0E01\u0E49\u0E32\u0E27\u0E01\u0E23\u0E30\u0E42\u0E14\u0E14",
    tooLong: "\u0E04\u0E48\u0E32\u0E04\u0E27\u0E23\u0E2A\u0E31\u0E49\u0E19\u0E01\u0E27\u0E48\u0E32\u0E19\u0E35\u0E49",
    tooShort: "\u0E04\u0E48\u0E32\u0E04\u0E27\u0E23\u0E22\u0E32\u0E27\u0E01\u0E27\u0E48\u0E32\u0E19\u0E35\u0E49",
    typeMismatch: "\u0E04\u0E48\u0E32\u0E15\u0E49\u0E2D\u0E07\u0E2D\u0E22\u0E39\u0E48\u0E43\u0E19\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E1E\u0E34\u0E40\u0E28\u0E29",
    valueMissing: "\u0E08\u0E33\u0E40\u0E1B\u0E47\u0E19\u0E15\u0E49\u0E2D\u0E07\u0E01\u0E23\u0E2D\u0E01\u0E0A\u0E48\u0E2D\u0E07\u0E19\u0E35\u0E49",
    invalidValue: "\u0E04\u0E48\u0E32\u0E43\u0E19\u0E0A\u0E48\u0E2D\u0E07\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07",
    minimumValueViolation: "\u0E04\u0E48\u0E32\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32\u0E04\u0E48\u0E32\u0E15\u0E48\u0E33\u0E2A\u0E38\u0E14",
    maximumValueViolation: "\u0E04\u0E48\u0E32\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32\u0E04\u0E48\u0E32\u0E2A\u0E39\u0E07\u0E2A\u0E38\u0E14",
    fieldRequired: "\u0E0A\u0E48\u0E2D\u0E07\u0E19\u0E35\u0E49\u0E08\u0E33\u0E40\u0E1B\u0E47\u0E19\u0E15\u0E49\u0E2D\u0E07\u0E01\u0E23\u0E2D\u0E01",
    validateFilter: "\u0E15\u0E49\u0E2D\u0E07\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E04\u0E48\u0E32\u0E08\u0E32\u0E01\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23"
  },
  DateField: {
    invalidDate: "\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48\u0E17\u0E35\u0E48\u0E01\u0E23\u0E2D\u0E01\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07"
  },
  DatePicker: {
    gotoPrevYear: "\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E1B\u0E35\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",
    gotoPrevMonth: "\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E40\u0E14\u0E37\u0E2D\u0E19\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",
    gotoNextMonth: "\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E40\u0E14\u0E37\u0E2D\u0E19\u0E16\u0E31\u0E14\u0E44\u0E1B",
    gotoNextYear: "\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E1B\u0E35\u0E16\u0E31\u0E14\u0E44\u0E1B"
  },
  NumberFormat: {
    locale: "th",
    currency: "THB"
  },
  DurationField: {
    invalidUnit: "\u0E2B\u0E19\u0E48\u0E27\u0E22\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07"
  },
  TimeField: {
    invalidTime: "\u0E40\u0E27\u0E25\u0E32\u0E17\u0E35\u0E48\u0E01\u0E23\u0E2D\u0E01\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07"
  },
  TimePicker: {
    hour: "\u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07",
    minute: "\u0E19\u0E32\u0E17\u0E35",
    second: "\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35"
  },
  List: {
    loading: "\u0E01\u0E33\u0E25\u0E31\u0E07\u0E42\u0E2B\u0E25\u0E14...",
    selectAll: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E17\u0E31\u0E49\u0E07\u0E2B\u0E21\u0E14"
  },
  GridBase: {
    loadMask: "\u0E01\u0E33\u0E25\u0E31\u0E07\u0E42\u0E2B\u0E25\u0E14...",
    syncMask: "\u0E01\u0E33\u0E25\u0E31\u0E07\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01\u0E01\u0E32\u0E23\u0E40\u0E1B\u0E25\u0E35\u0E48\u0E22\u0E19\u0E41\u0E1B\u0E25\u0E07 \u0E01\u0E23\u0E38\u0E13\u0E32\u0E23\u0E2D\u0E2A\u0E31\u0E01\u0E04\u0E23\u0E39\u0E48..."
  },
  PagingToolbar: {
    firstPage: "\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E2B\u0E19\u0E49\u0E32\u0E41\u0E23\u0E01",
    prevPage: "\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E2B\u0E19\u0E49\u0E32\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",
    page: "\u0E2B\u0E19\u0E49\u0E32",
    nextPage: "\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E2B\u0E19\u0E49\u0E32\u0E16\u0E31\u0E14\u0E44\u0E1B",
    lastPage: "\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E2B\u0E19\u0E49\u0E32\u0E2A\u0E38\u0E14\u0E17\u0E49\u0E32\u0E22",
    reload: "\u0E42\u0E2B\u0E25\u0E14\u0E0B\u0E49\u0E33\u0E2B\u0E19\u0E49\u0E32\u0E1B\u0E31\u0E08\u0E08\u0E38\u0E1A\u0E31\u0E19",
    noRecords: "\u0E44\u0E21\u0E48\u0E21\u0E35\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01\u0E17\u0E35\u0E48\u0E15\u0E49\u0E2D\u0E07\u0E41\u0E2A\u0E14\u0E07",
    pageCountTemplate: (data) => `\u0E08\u0E32\u0E01 ${data.lastPage}`,
    summaryTemplate: (data) => `\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01\u0E17\u0E35\u0E48\u0E41\u0E2A\u0E14\u0E07 ${data.start} - ${data.end} \u0E08\u0E32\u0E01 ${data.allCount}`
  },
  PanelCollapser: {
    Collapse: "\u0E22\u0E48\u0E2D",
    Expand: "\u0E02\u0E22\u0E32\u0E22"
  },
  Popup: {
    close: "\u0E1B\u0E34\u0E14\u0E1B\u0E4A\u0E2D\u0E1B\u0E2D\u0E31\u0E1B"
  },
  UndoRedo: {
    Undo: "\u0E40\u0E25\u0E34\u0E01\u0E17\u0E33",
    Redo: "\u0E17\u0E33\u0E0B\u0E49\u0E33",
    UndoLastAction: "\u0E40\u0E25\u0E34\u0E01\u0E17\u0E33\u0E01\u0E32\u0E23\u0E01\u0E23\u0E30\u0E17\u0E33\u0E25\u0E48\u0E32\u0E2A\u0E38\u0E14",
    RedoLastAction: "\u0E17\u0E33\u0E0B\u0E49\u0E33\u0E01\u0E32\u0E23\u0E01\u0E23\u0E30\u0E17\u0E33\u0E17\u0E35\u0E48\u0E16\u0E39\u0E01\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01",
    NoActions: "\u0E44\u0E21\u0E48\u0E21\u0E35\u0E01\u0E32\u0E23\u0E01\u0E23\u0E30\u0E17\u0E33\u0E43\u0E19\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23\u0E40\u0E25\u0E34\u0E01\u0E17\u0E33"
  },
  FieldFilterPicker: {
    equals: "\u0E40\u0E17\u0E48\u0E32\u0E01\u0E31\u0E1A",
    doesNotEqual: "\u0E44\u0E21\u0E48\u0E40\u0E17\u0E48\u0E32\u0E01\u0E31\u0E1A",
    isEmpty: "\u0E27\u0E48\u0E32\u0E07\u0E40\u0E1B\u0E25\u0E48\u0E32",
    isNotEmpty: "\u0E44\u0E21\u0E48\u0E27\u0E48\u0E32\u0E07\u0E40\u0E1B\u0E25\u0E48\u0E32",
    contains: "\u0E21\u0E35",
    doesNotContain: "\u0E44\u0E21\u0E48\u0E21\u0E35",
    startsWith: "\u0E40\u0E23\u0E34\u0E48\u0E21\u0E15\u0E49\u0E19\u0E14\u0E49\u0E27\u0E22",
    endsWith: "\u0E25\u0E07\u0E17\u0E49\u0E32\u0E22\u0E14\u0E49\u0E27\u0E22",
    isOneOf: "\u0E40\u0E1B\u0E47\u0E19\u0E2B\u0E19\u0E36\u0E48\u0E07\u0E43\u0E19",
    isNotOneOf: "\u0E44\u0E21\u0E48\u0E44\u0E14\u0E49\u0E40\u0E1B\u0E47\u0E19\u0E2B\u0E19\u0E36\u0E48\u0E07\u0E43\u0E19",
    isGreaterThan: "\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32",
    isLessThan: "\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32",
    isGreaterThanOrEqualTo: "\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32\u0E2B\u0E23\u0E37\u0E2D\u0E40\u0E17\u0E48\u0E32\u0E01\u0E31\u0E1A",
    isLessThanOrEqualTo: "\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32\u0E2B\u0E23\u0E37\u0E2D\u0E40\u0E17\u0E48\u0E32\u0E01\u0E31\u0E1A",
    isBetween: "\u0E2D\u0E22\u0E39\u0E48\u0E23\u0E30\u0E2B\u0E27\u0E48\u0E32\u0E07",
    isNotBetween: "\u0E44\u0E21\u0E48\u0E2D\u0E22\u0E39\u0E48\u0E23\u0E30\u0E2B\u0E27\u0E48\u0E32\u0E07",
    isBefore: "\u0E01\u0E48\u0E2D\u0E19",
    isAfter: "\u0E2B\u0E25\u0E31\u0E07\u0E08\u0E32\u0E01",
    isToday: "\u0E40\u0E1B\u0E47\u0E19\u0E27\u0E31\u0E19\u0E19\u0E35\u0E49",
    isTomorrow: "\u0E40\u0E1B\u0E47\u0E19\u0E27\u0E31\u0E19\u0E1E\u0E23\u0E38\u0E48\u0E07\u0E19\u0E35\u0E49",
    isYesterday: "\u0E40\u0E1B\u0E47\u0E19\u0E40\u0E21\u0E37\u0E48\u0E2D\u0E27\u0E32\u0E19\u0E19\u0E35\u0E49",
    isThisWeek: "\u0E40\u0E1B\u0E47\u0E19\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C\u0E19\u0E35\u0E49",
    isNextWeek: "\u0E40\u0E1B\u0E47\u0E19\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C\u0E2B\u0E19\u0E49\u0E32",
    isLastWeek: "\u0E40\u0E1B\u0E47\u0E19\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C\u0E17\u0E35\u0E48\u0E41\u0E25\u0E49\u0E27",
    isThisMonth: "\u0E40\u0E1B\u0E47\u0E19\u0E40\u0E14\u0E37\u0E2D\u0E19\u0E19\u0E35\u0E49",
    isNextMonth: "\u0E40\u0E1B\u0E47\u0E19\u0E40\u0E14\u0E37\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",
    isLastMonth: "\u0E40\u0E1B\u0E47\u0E19\u0E40\u0E14\u0E37\u0E2D\u0E19\u0E17\u0E35\u0E48\u0E41\u0E25\u0E49\u0E27",
    isThisYear: "\u0E40\u0E1B\u0E47\u0E19\u0E1B\u0E35\u0E19\u0E35\u0E49",
    isNextYear: "\u0E40\u0E1B\u0E47\u0E19\u0E1B\u0E35\u0E2B\u0E19\u0E49\u0E32",
    isLastYear: "\u0E40\u0E1B\u0E47\u0E19\u0E1B\u0E35\u0E17\u0E35\u0E48\u0E41\u0E25\u0E49\u0E27",
    isYearToDate: "\u0E40\u0E1B\u0E47\u0E19\u0E1B\u0E35\u0E08\u0E19\u0E16\u0E36\u0E07\u0E1B\u0E31\u0E08\u0E08\u0E38\u0E1A\u0E31\u0E19",
    isTrue: "\u0E40\u0E1B\u0E47\u0E19\u0E08\u0E23\u0E34\u0E07",
    isFalse: "\u0E40\u0E1B\u0E47\u0E19\u0E40\u0E17\u0E47\u0E08",
    selectAProperty: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E04\u0E38\u0E13\u0E2A\u0E21\u0E1A\u0E31\u0E15\u0E34",
    selectAnOperator: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E1C\u0E39\u0E49\u0E1B\u0E0F\u0E34\u0E1A\u0E31\u0E15\u0E34\u0E01\u0E32\u0E23",
    caseSensitive: "\u0E01\u0E32\u0E23\u0E1A\u0E31\u0E07\u0E04\u0E31\u0E1A\u0E43\u0E0A\u0E49\u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23\u0E40\u0E25\u0E47\u0E01/\u0E43\u0E2B\u0E0D\u0E48",
    and: "\u0E41\u0E25\u0E30",
    dateFormat: "D/M/YY",
    selectValue: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E04\u0E48\u0E32",
    selectOneOrMoreValues: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E2B\u0E19\u0E36\u0E48\u0E07\u0E04\u0E48\u0E32\u0E2B\u0E23\u0E37\u0E2D\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32",
    enterAValue: "\u0E1B\u0E49\u0E2D\u0E19\u0E04\u0E48\u0E32",
    enterANumber: "\u0E1B\u0E49\u0E2D\u0E19\u0E2B\u0E21\u0E32\u0E22\u0E40\u0E25\u0E02",
    selectADate: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48",
    selectATime: "\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E40\u0E27\u0E25\u0E32"
  },
  FieldFilterPickerGroup: {
    addFilter: "\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E15\u0E31\u0E27\u0E01\u0E23\u0E2D\u0E07"
  },
  DateHelper: {
    locale: "th",
    weekStartDay: 1,
    nonWorkingDays: {
      0: true,
      6: true
    },
    weekends: {
      0: true,
      6: true
    },
    unitNames: [
      { single: "\u0E21\u0E34\u0E25\u0E25\u0E34\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35", plural: "\u0E21\u0E34\u0E25\u0E25\u0E34\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35", abbrev: "\u0E21\u0E34\u0E25\u0E25\u0E34\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35" },
      { single: "\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35", plural: "\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35", abbrev: "\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35" },
      { single: "\u0E19\u0E32\u0E17\u0E35", plural: "\u0E19\u0E32\u0E17\u0E35", abbrev: "\u0E19." },
      { single: "\u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07", plural: "\u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07", abbrev: "\u0E0A\u0E21." },
      { single: "\u0E27\u0E31\u0E19", plural: "\u0E27\u0E31\u0E19", abbrev: "\u0E27." },
      { single: "\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C", plural: "\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C", abbrev: "\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C" },
      { single: "\u0E40\u0E14\u0E37\u0E2D\u0E19", plural: "\u0E40\u0E14\u0E37\u0E2D\u0E19", abbrev: "\u0E14." },
      { single: "\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A", plural: "\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A", abbrev: "\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A" },
      { single: "\u0E1B\u0E35", plural: "\u0E1B\u0E35", abbrev: "\u0E1B." },
      { single: "\u0E17\u0E28\u0E27\u0E23\u0E23\u0E29", plural: "\u0E17\u0E28\u0E27\u0E23\u0E23\u0E29", abbrev: "\u0E17\u0E28\u0E27\u0E23\u0E23\u0E29" }
    ],
    unitAbbreviations: [
      ["\u0E21\u0E34\u0E25\u0E25\u0E34\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35"],
      ["\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35", "\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35"],
      ["\u0E19.", "\u0E19."],
      ["\u0E0A\u0E21.", "\u0E0A\u0E21."],
      ["\u0E27\u0E31\u0E19"],
      ["\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C", "\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C"],
      ["\u0E14.", "\u0E14.", "\u0E14."],
      ["\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A", "\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A", "\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A"],
      ["\u0E1B.", "\u0E1B."],
      ["\u0E17\u0E28\u0E27\u0E23\u0E23\u0E29"]
    ],
    parsers: {
      L: "DD/MM/YYYY",
      LT: "HH:mm",
      LTS: "HH:mm:ss A"
    },
    ordinalSuffix: (number) => number
  }
};
var Th_default = LocaleHelper.publishLocale(locale);
export {
  Th_default as default
};
