import LocaleHelper from "@bryntum/core-thin/lib/localization/LocaleHelper.js";
const locale = {
  localeName: "Ro",
  localeDesc: "Rom\xE2n\u0103",
  localeCode: "ro",
  Object: {
    Yes: "Da",
    No: "Nu",
    Cancel: "Anulare",
    Ok: "OK",
    Week: "S\u0103pt\u0103m\xE2n\u0103",
    None: "Niciun"
  },
  CodeEditor: {
    apply: "Aplic\u0103",
    autoApply: "Aplicare automat\u0103",
    downloadCode: "Descarc\u0103 codul",
    editor: "Editor de cod",
    viewer: "Vizualizator de cod"
  },
  ColorPicker: {
    noColor: "F\u0103r\u0103 culoare"
  },
  Combo: {
    noResults: "Niciun rezultat",
    recordNotCommitted: "Nu s-a putut ad\u0103uga \xEEnregistrarea",
    addNewValue: (value) => `Ad\u0103uga ${value}`
  },
  FilePicker: {
    file: "Fi\u0219ier"
  },
  Field: {
    badInput: "Valoare c\xE2mp nevalid\u0103",
    patternMismatch: "Valoarea trebuie s\u0103 se potriveasc\u0103 cu un \u0219ablon specific",
    rangeOverflow: (value) => `Valoarea trebuie s\u0103 fie mai mic\u0103 sau egal\u0103 cu ${value.max}`,
    rangeUnderflow: (value) => `Valoarea trebuie s\u0103 fie mai mare sau egal\u0103 cu ${value.min}`,
    stepMismatch: "Valoarea trebuie s\u0103 se potriveasc\u0103 cu pasul",
    tooLong: "Valoarea trebuie s\u0103 fie mai scurt\u0103",
    tooShort: "Valoarea trebuie s\u0103 fie mai lung\u0103",
    typeMismatch: "Valoarea trebuie s\u0103 fie \xEEntr-un format special",
    valueMissing: "Acest c\xE2mp este obligatoriu",
    invalidValue: "Valoare c\xE2mp nevalid\u0103",
    minimumValueViolation: "\xCEnc\u0103lcare a valorii minime",
    maximumValueViolation: "\xCEnc\u0103lcare a valorii maxime",
    fieldRequired: "Acest c\xE2mp este obligatoriu",
    validateFilter: "Valoarea trebuie selectat\u0103 din list\u0103"
  },
  DateField: {
    invalidDate: "A\u021Bi introdus o dat\u0103 nevalid\u0103"
  },
  DatePicker: {
    gotoPrevYear: "Merge\u021Bi la anul anterior",
    gotoPrevMonth: "Merge\u021Bi la luna anterioar\u0103",
    gotoNextMonth: "Merge\u021Bi la luna urm\u0103toare",
    gotoNextYear: "Merge\u021Bi la anul urm\u0103tor"
  },
  NumberFormat: {
    locale: "ro",
    currency: "RON"
  },
  DurationField: {
    invalidUnit: "Unitate nevalid\u0103"
  },
  TimeField: {
    invalidTime: "Or\u0103 nevalid\u0103 introdus\u0103"
  },
  TimePicker: {
    hour: "Or\u0103",
    minute: "Minut",
    second: "Secunda"
  },
  List: {
    loading: "Se \xEEncarc\u0103...",
    selectAll: "Selecteaz\u0103 tot"
  },
  GridBase: {
    loadMask: "Se \xEEncarc\u0103...",
    syncMask: "Se salveaz\u0103 modific\u0103rile, v\u0103 rug\u0103m a\u0219tepta\u021Bi..."
  },
  PagingToolbar: {
    firstPage: "Merge\u021Bi la prima pagin\u0103",
    prevPage: "Merge\u021Bi la pagina anterioar\u0103",
    page: "Pagina",
    nextPage: "Merge\u021Bi la pagina urm\u0103toare",
    lastPage: "Merge\u021Bi la ultima pagin\u0103",
    reload: "Re\xEEnc\u0103rcare pagin\u0103 curent\u0103",
    noRecords: "Nicio \xEEnregistrare de afi\u0219at",
    pageCountTemplate: (data) => `din${data.lastPage}`,
    summaryTemplate: (data) => `Se afi\u0219eaz\u0103 \xEEnregistr\u0103rile ${data.start} - ${data.end} din ${data.allCount}`
  },
  PanelCollapser: {
    Collapse: "Restr\xE2ngere",
    Expand: "Extindere"
  },
  Popup: {
    close: "\xCEnchidere popup"
  },
  UndoRedo: {
    Undo: "Anulare",
    Redo: "Refacere",
    UndoLastAction: "Anula\u021Bi ultima ac\u021Biune",
    RedoLastAction: "Reface\u021Bi ultima ac\u021Biune anulat\u0103",
    NoActions: "Niciun element \xEEn coada de anulare"
  },
  FieldFilterPicker: {
    equals: "egal",
    doesNotEqual: "nu este egal",
    isEmpty: "este gol",
    isNotEmpty: "nu este gol",
    contains: "con\u021Bine",
    doesNotContain: "nu con\u021Bine",
    startsWith: "\xEEncepe cu",
    endsWith: "se termin\u0103 cu",
    isOneOf: "face parte din",
    isNotOneOf: "nu face parte din",
    isGreaterThan: "este mai mare de",
    isLessThan: "este mai mic de",
    isGreaterThanOrEqualTo: "este mai mare sau egal cu",
    isLessThanOrEqualTo: "este mai mic sau egal cu",
    isBetween: "este \xEEntre",
    isNotBetween: "nu este \xEEntre",
    isBefore: "este \xEEnainte",
    isAfter: "este dup\u0103",
    isToday: "este azi",
    isTomorrow: "este m\xE2ine",
    isYesterday: "este ieri",
    isThisWeek: "este s\u0103pt\u0103m\xE2na aceasta",
    isNextWeek: "este s\u0103pt\u0103m\xE2na viitoare",
    isLastWeek: "este s\u0103pt\u0103m\xE2na trecut\u0103",
    isThisMonth: "este luna aceasta",
    isNextMonth: "este luna viitoare",
    isLastMonth: "este luna trecut\u0103",
    isThisYear: "este acest an",
    isNextYear: "este anul viitor",
    isLastYear: "este anul trecut",
    isYearToDate: "este anul acesta, p\xE2n\u0103 \xEEn prezent",
    isTrue: "este adev\u0103rat",
    isFalse: "este fals",
    selectAProperty: "Selecta\u021Bi o proprietate",
    selectAnOperator: "Selecta\u021Bi un operator",
    caseSensitive: "Sensibil la litere mari \u015Fi mici",
    and: "\u0219i",
    dateFormat: "D/M/YY",
    selectValue: "Selecta\u021Bi valoare",
    selectOneOrMoreValues: "Selecta\u021Bi una sau mai multe valori",
    enterAValue: "Introduce\u021Bi o valoare",
    enterANumber: "Introduce\u021Bi un num\u0103r",
    selectADate: "Selecta\u021Bi o dat\u0103",
    selectATime: "Selecta\u021Bi ora"
  },
  FieldFilterPickerGroup: {
    addFilter: "Ad\u0103uga\u021Bi filtru"
  },
  DateHelper: {
    locale: "ro",
    weekStartDay: 1,
    nonWorkingDays: {
      0: true,
      6: true
    },
    weekends: {
      0: true,
      6: true
    },
    unitNames: [
      { single: "millisecund\u0103", plural: "ms", abbrev: "ms" },
      { single: "secund\u0103", plural: "secunde", abbrev: "s" },
      { single: "minut", plural: "minute", abbrev: "min" },
      { single: "or\u0103", plural: "ore", abbrev: "h" },
      { single: "zi", plural: "zile", abbrev: "z" },
      { single: "s\u0103pt\u0103m\xE2n\u0103", plural: "s\u0103pt\u0103m\xE2ni", abbrev: "s" },
      { single: "lun\u0103", plural: "luni", abbrev: "lu" },
      { single: "trimestru", plural: "trimestre", abbrev: "t" },
      { single: "an", plural: "ani", abbrev: "an" },
      { single: "decad\u0103", plural: "decade", abbrev: "dec" }
    ],
    unitAbbreviations: [
      ["ms"],
      ["s", "sec"],
      ["m", "min"],
      ["h", "h"],
      ["z"],
      ["s", "s"],
      ["lu", "lun"],
      ["t", "trim"],
      ["a", "a"],
      ["dec"]
    ],
    parsers: {
      L: "DD.MM.YYYY",
      LT: "HH:mm",
      LTS: "HH:mm:ss A"
    },
    ordinalSuffix: (number, unit) => {
      let prefix = { 1: "" }[number] || "a ";
      let suffix = "-a";
      if (unit === "Qo" || unit === "Yo") {
        prefix = { 1: "" }[number] || "al ";
        suffix = { 1: "-ul" }[number] || "-lea";
      }
      return prefix + number + suffix;
    }
  }
};
var Ro_default = LocaleHelper.publishLocale(locale);
export {
  Ro_default as default
};
