import LocaleHelper from "@bryntum/core-thin/lib/localization/LocaleHelper.js";
const locale = {
  localeName: "No",
  localeDesc: "Norsk",
  localeCode: "no",
  Object: {
    Yes: "Ja",
    No: "<PERSON><PERSON>",
    Cancel: "<PERSON>v<PERSON><PERSON><PERSON>",
    Ok: "OK",
    Week: "Uke",
    None: "Ingen"
  },
  CodeEditor: {
    apply: "Bruk",
    autoApply: "Automatisk bruk",
    downloadCode: "Last ned kode",
    editor: "Kodeeditor",
    viewer: "Kodevisning"
  },
  ColorPicker: {
    noColor: "Ingen farge"
  },
  Combo: {
    noResults: "Ingen treff",
    recordNotCommitted: "Kunne ikke legge til oppf\xF8ringen",
    addNewValue: (value) => `Legg til ${value}`
  },
  FilePicker: {
    file: "Fil"
  },
  Field: {
    badInput: "Ugyldig feltverdi",
    patternMismatch: "Verdien skal samsvare med et bestemt m\xF8nster",
    rangeOverflow: (value) => `Verdien m\xE5 v\xE6re mindre eller lik ${value.max}`,
    rangeUnderflow: (value) => `Verdien m\xE5 v\xE6re st\xF8rre eller lik ${value.min}`,
    stepMismatch: "Verdien skal passe til trinnet",
    tooLong: "Verdien skal v\xE6re kortere",
    tooShort: "Verdien skal v\xE6re lengre",
    typeMismatch: "Verdien skal v\xE6re i et bestemt format",
    valueMissing: "Dette feltet er obligatorisk",
    invalidValue: "Ugyldig feltverdi",
    minimumValueViolation: "Minimumsverdibrudd",
    maximumValueViolation: "Maksimumsverdibrudd",
    fieldRequired: "Dette feltet er obligatorisk",
    validateFilter: "Verdien skal velges fra listen"
  },
  DateField: {
    invalidDate: "Ugyldig datoinntasting"
  },
  DatePicker: {
    gotoPrevYear: "G\xE5 til forrige \xE5r",
    gotoPrevMonth: "G\xE5 til forrige m\xE5ned",
    gotoNextMonth: "G\xE5 til neste m\xE5ned",
    gotoNextYear: "G\xE5 til neste \xE5r"
  },
  NumberFormat: {
    locale: "no",
    currency: "NOK"
  },
  DurationField: {
    invalidUnit: "Ugyldig enhet"
  },
  TimeField: {
    invalidTime: "Ugyldig tidsinntasting"
  },
  TimePicker: {
    hour: "Time",
    minute: "Minutt",
    second: "Sekund"
  },
  List: {
    loading: "Laster...",
    selectAll: "Velg alle"
  },
  GridBase: {
    loadMask: "Laster...",
    syncMask: "Lagrer endringer, vennligst vent..."
  },
  PagingToolbar: {
    firstPage: "G\xE5 til f\xF8rste side",
    prevPage: "G\xE5 til forrige side",
    page: "Side",
    nextPage: "G\xE5 til neste side",
    lastPage: "G\xE5 til siste side",
    reload: "Last inn gjeldende side p\xE5 nytt",
    noRecords: "Ingen oppf\xF8ringer \xE5 vise",
    pageCountTemplate: (data) => `av ${data.lastPage}`,
    summaryTemplate: (data) => `Viser oppf\xF8ringer ${data.start} - ${data.end} av ${data.allCount}`
  },
  PanelCollapser: {
    Collapse: "Skjul",
    Expand: "Utvid"
  },
  Popup: {
    close: "Lukk popup"
  },
  UndoRedo: {
    Undo: "Angre",
    Redo: "Gj\xF8re om",
    UndoLastAction: "Angre siste handling",
    RedoLastAction: "Gj\xF8re om siste handling",
    NoActions: "Ingen elementer I angrek\xF8en"
  },
  FieldFilterPicker: {
    equals: "er lik",
    doesNotEqual: "er ikke lik",
    isEmpty: "er tom",
    isNotEmpty: "er ikke tom",
    contains: "inneholder",
    doesNotContain: "inneholder ikke",
    startsWith: "begynner med",
    endsWith: "slutter med",
    isOneOf: "er et av",
    isNotOneOf: "er ikke et av",
    isGreaterThan: "er st\xF8rre enn",
    isLessThan: "er mindre enn",
    isGreaterThanOrEqualTo: "er st\xF8rre enn eller lik",
    isLessThanOrEqualTo: "er mindre enn eller lik",
    isBetween: "er mellom",
    isNotBetween: "er ikke mellom",
    isBefore: "er f\xF8r",
    isAfter: "er etter",
    isToday: "er i dag",
    isTomorrow: "er i morgen",
    isYesterday: "er i g\xE5r",
    isThisWeek: "er denne uken",
    isNextWeek: "er neste uke",
    isLastWeek: "er siste uke",
    isThisMonth: "er denne m\xE5neden",
    isNextMonth: "er neste m\xE5ned",
    isLastMonth: "er siste m\xE5ned",
    isThisYear: "er i \xE5r",
    isNextYear: "er neste \xE5r",
    isLastYear: "er i fjor",
    isYearToDate: "er \xE5r til dags dato",
    isTrue: "er sant",
    isFalse: "er falsk",
    selectAProperty: "Velg egenskap",
    selectAnOperator: "Velg operat\xF8r",
    caseSensitive: "Skille mellom store og sm\xE5 bokstaver",
    and: "og",
    dateFormat: "D/M/YY",
    selectValue: "Velg verdi",
    selectOneOrMoreValues: "Velg \xE9n eller flere verdier",
    enterAValue: "Skriv inn en verdi",
    enterANumber: "Skriv inn et tall",
    selectADate: "Velg en dato",
    selectATime: "Velg tidspunkt"
  },
  FieldFilterPickerGroup: {
    addFilter: "Legg til filter"
  },
  DateHelper: {
    locale: "no",
    weekStartDay: 1,
    nonWorkingDays: {
      0: true,
      6: true
    },
    weekends: {
      0: true,
      6: true
    },
    unitNames: [
      { single: "millisekund", plural: "ms", abbrev: "ms" },
      { single: "sekund", plural: "sekunder", abbrev: "s" },
      { single: "minutt", plural: "minutter", abbrev: "min" },
      { single: "time", plural: "timer", abbrev: "t" },
      { single: "dag", plural: "dager", abbrev: "d" },
      { single: "uke", plural: "uker", abbrev: "u" },
      { single: "m\xE5ned", plural: "m\xE5neder", abbrev: "m\xE5n" },
      { single: "kvartal", plural: "kvartaler", abbrev: "k" },
      { single: "\xE5r", plural: "\xE5r", abbrev: "\xE5r" },
      { single: "ti\xE5r", plural: "ti\xE5r", abbrev: "t\xE5r" }
    ],
    unitAbbreviations: [
      ["mls"],
      ["s", "sek"],
      ["m", "min"],
      ["t", "t"],
      ["d"],
      ["u", "uk"],
      ["m\xE5", "m\xE5n", "mdr"],
      ["k", "kvart", "kvt"],
      ["\xE5", "\xE5r"],
      ["dek"]
    ],
    parsers: {
      L: "DD.MM.YYYY;",
      LT: "HH:mm",
      LTS: "HH:mm:ss A"
    },
    ordinalSuffix: (number) => number
  }
};
var No_default = LocaleHelper.publishLocale(locale);
export {
  No_default as default
};
