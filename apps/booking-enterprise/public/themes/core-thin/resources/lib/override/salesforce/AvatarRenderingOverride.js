var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Override from "@bryntum/core-thin/lib/mixin/Override.js";
import AvatarRendering from "../../widget/util/AvatarRendering.js";
class AvatarRenderingOverride {
  getImageConfig() {
    const config = this._overridden.getImageConfig.apply(this, arguments);
    if (config) {
      config.style["max-width"] = "unset";
    }
    return config;
  }
}
__publicField(AvatarRenderingOverride, "target", {
  class: AvatarRendering
});
Override.apply(AvatarRenderingOverride);
