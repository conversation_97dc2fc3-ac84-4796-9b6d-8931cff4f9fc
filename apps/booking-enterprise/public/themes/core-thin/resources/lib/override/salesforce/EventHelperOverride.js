var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import EventHelper from "../../helper/EventHelper.js";
import Override from "../../mixin/Override.js";
const regExp = /-/, PROPS_CACHE = Symbol("props"), skipCopyProp = {
  originalTarget: true,
  // Mozilla specific
  explicitOriginalTarget: true,
  // Mozilla specific
  rangeParent: true
  // LWS restricted
};
let composedPathThrows;
const newPropertyOwner = (event) => {
  const wrapper = Object.create(event);
  const { relatedTarget } = event;
  if (relatedTarget && !relatedTarget.nodeType) {
    Object.defineProperty(wrapper, "relatedTarget", {
      get: () => null,
      configurable: true
    });
  }
  const props = /* @__PURE__ */ Object.create(null);
  for (const key in event) {
    if (skipCopyProp[key]) {
      continue;
    }
    if (typeof event[key] !== "function") {
      props[key] = event[key];
    }
  }
  wrapper[PROPS_CACHE] = props;
  return wrapper;
};
const getPropertyOwner = (target, propertyName) => {
  if (Object.hasOwn(target, propertyName)) {
    return target;
  }
  if (propertyName in target[PROPS_CACHE]) {
    return target[PROPS_CACHE];
  }
  return Object.getPrototypeOf(target);
};
const wrap = (event) => {
  if (event[PROPS_CACHE]) {
    return event;
  }
  return new Proxy(newPropertyOwner(event), {
    get: (target, propertyName) => {
      const owner = getPropertyOwner(target, propertyName);
      const value = owner[propertyName];
      if (typeof value === "function") {
        return (...args) => value.call(owner, ...args);
      }
      return value;
    }
  });
};
class EventHelperOverride {
  static getComposedPathTarget(event) {
    let result;
    if (composedPathThrows == null) {
      try {
        result = event.composedPath()[0];
        composedPathThrows = false;
      } catch (e) {
        composedPathThrows = true;
      }
    }
    if (composedPathThrows) {
      if (event.path) {
        result = event.path[0];
      } else {
        result = event.target;
      }
    } else {
      result = event.composedPath()[0];
    }
    return result;
  }
  static fixEvent(event) {
    event = this._overridden.fixEvent.call(this, wrap(event));
    if (event.target && event.path && regExp.test(event.target.tagName)) {
      const targetElement = event.path[0], originalTarget = event.target;
      if (event.target !== targetElement) {
        Object.defineProperty(event, "target", {
          get: () => targetElement,
          configurable: true
        });
        Object.defineProperty(event, "originalTarget", {
          get: () => originalTarget,
          configurable: true
        });
      }
    }
    return event;
  }
}
__publicField(EventHelperOverride, "target", { class: EventHelper });
Override.apply(EventHelperOverride);
