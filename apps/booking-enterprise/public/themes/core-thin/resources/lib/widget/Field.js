var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
var _a, _b;
import Widget from "./Widget.js";
import Badge from "./mixin/Badge.js";
import Labelable from "./mixin/Labelable.js";
import Validatable from "./mixin/Validatable.js";
import EventHelper from "../helper/EventHelper.js";
import FunctionHelper from "../helper/FunctionHelper.js";
import ObjectHelper from "../helper/ObjectHelper.js";
import DomHelper from "../helper/DomHelper.js";
import ClickRepeater from "../util/ClickRepeater.js";
import DynamicObject from "../util/DynamicObject.js";
import "./FieldContainer.js";
const byWeight = (l, r) => (l.weight || 0) - (r.weight || 0), byWeightReverse = (l, r) => (r.weight || 0) - (l.weight || 0), emptyArray = [], triggerConfigs = {
  align: true,
  weight: true
};
const _Field = class _Field extends Widget.mixin(Badge, Labelable, Validatable) {
  static get configurable() {
    return {
      defaultBindProperty: "value",
      /**
       * @hideconfigs htmlCls, content, tag, scrollable, html
       */
      /**
       * Set to `true`, completely bypasses validation logic (could be userful if your field is not `editable`
       * to the user).
       *
       * @config {Boolean}
       * @default false
       */
      skipValidation: null,
      /**
       * Text to display in empty field.
       * @config {String} placeholder
       */
      placeholder: null,
      /**
       * Gets or sets the value. The returned type will depend upon the Field subclass.
       *
       * `TextField` returns a `String`.
       *
       * `NumberField` returns a `Number`.
       *
       * `DateField` and `TimeField` return a `Date` object, and `null` if the field is empty.
       *
       * `Combo` will return a `String` if configured with `items` as a simple string array.
       * Otherwise it will return the {@link Core.widget.Combo#config-valueField} value from the
       * selected record, or `null` if no selection has been made.
       * @member {*} value
       */
      /**
       * Default value
       * @config {String}
       */
      value: "",
      /**
       * Name of the field which is used as a key to get/set values from/to the field.
       * Used prior to {@link Core.widget.Widget#config-ref ref} and {@link Core.widget.Widget#config-id id} in
       * {@link Core.widget.Container#property-values Container.values}.
       *
       * The config is useful when the field is used in EventEditor or TaskEditor to load/save values automatically.
       * @config {String}
       */
      name: null,
      /**
       * The labels to add either before or after the input field.
       * Each label may have the following properties:
       * * `html` The label text.
       * * `align` `'start'` or `'end'` which end of the field the label should go.
       * @config {Object[]}
       * @property {String} html Label text
       * @property {'start'|'end'} align Which end of the file the label should go
       * @category Label
       */
      labels: null,
      /**
       * Configure as `true` to indicate that a `null` field value is to be marked as invalid.
       * @config {Boolean}
       * @default false
       */
      required: null,
      /**
       * Show a trigger to clear field, and allow `SHIFT+BACKSPACE` key to clear field if this field is
       * not {@link #config-readOnly}. The trigger is available in the {@link #property-triggers} object
       * under the name `clear`. May also be an object which configures the `clear`
       * {@link #property-triggers trigger}.
       * @config {Boolean|FieldTriggerConfig}
       * @default false
       */
      clearable: null,
      /**
       * If this field is not {@link #config-readOnly}, then setting this option means that pressing
       * the `ESCAPE` key after editing the field will revert the field to the value it had when
       * the user focused the field. If the field is _not_ changed from when focused, the {@link #config-clearable}
       * behaviour will be activated.
       * @config {Boolean}
       * @default false
       */
      revertOnEscape: null,
      /**
       * An optional string to display inside the input field as an overlay. This can be useful for displaying
       * a field's units.
       *
       * This config is ignored if {@link #config-hintHtml} is set.
       *
       * For example:
       * ```javascript
       *  {
       *      type  : 'numberfield',
       *      label : 'Temperature',
       *      hint  : '°C'
       *  }
       * ```
       *
       * This config can be set to a function to dynamically generate the `hint` text:
       * ```javascript
       *  {
       *      type  : 'numberfield',
       *      label : 'Duration',
       *      hint  : ({ value }) => (value === 1) ? 'Day' : 'Days'
       *  }
       * ```
       *
       * The function is passed an object with the following properties:
       *
       *  - `source` A reference to the field instance.
       *  - `value` The current value of the field.
       *
       * A `hint` function will be called when the field changes value.
       *
       * @config {String|Function}
       * @param {Object} data A data object
       * @param {Core.widget.Field} data.source A reference to the field instance
       * @param {*} data.value The current value of the field
       * @returns {String}
       * @category Label
       */
      hint: null,
      /**
       * This config is similar to {@link #config-hint} except that this config is used to display HTML content.
       * Since this can allow malicious content to be executed, be sure not to include user-entered data or to
       * encode such data (see {@link Core.helper.StringHelper#function-encodeHtml-static}).
       *
       * If this config is set, {@link #config-hint} is ignored.
       *
       * For example:
       * ```javascript
       *  {
       *      type     : 'numberfield',
       *      label    : 'Temperature',
       *      hintHtml : '<i>°C</i>'
       *  }
       * ```
       *
       * This config can be set to a function to dynamically generate the `hintHtml` text:
       * ```javascript
       *  {
       *      type     : 'numberfield',
       *      label    : 'Duration',
       *      hintHtml : ({ value }) => (value === 1) ? '<i>Day</i>' : '<i>Days</i>'
       *  }
       * ```
       *
       * The function is passed an object with the following properties:
       *
       *  - `source` A reference to the field instance.
       *  - `value` The current value of the field.
       *
       * A `hintHtml` function will be called when the field changes value.
       *
       * @config {String|Function}
       * @param {Object} data A data object
       * @param {Core.widget.Field} data.source A reference to the field instance
       * @param {*} data.value The current value of the field
       * @returns {String}
       * @category Label
       */
      hintHtml: null,
      /**
       * The width to apply to the `<input>` element. If a number is specified, `px` will be used.
       * @config {String|Number}
       * @category Input element
       */
      inputWidth: null,
      /**
       * The delay in milliseconds to wait after the last keystroke before triggering a change event.
       * Set to 0 to not trigger change events from keystrokes (listen for input event instead to have
       * immediate feedback, change will still be triggered on blur).
       *
       * If the field is {@link #config-clearable}, the change event fires immediately on receiving
       * the clear gesture.
       * @config {Number}
       * @default
       */
      keyStrokeChangeDelay: 0,
      /**
       * Makes the field unmodifiable by user action. The input area is not editable, and triggers
       * are unresponsive.
       *
       * This is a wider-acting setting than {@link #config-editable} which *only* sets the
       * `readOnly` attribute of the `<input>` field.
       *
       * {@link Core.widget.PickerField}s such as `Combo` and `DateField` can be `editable : false`, but still
       * modifiable through the UI.
       * @prp {Boolean}
       */
      readOnly: null,
      /**
       * Set to `false` to prevent user from editing the field. For TextFields it is basically the same as setting
       * {@link #config-readOnly}, but for PickerFields there is a distinction where it allows you to pick a value
       * but not to type one in the field.
       *
       * {@link Core.widget.PickerField}s such as `Combo` and `DateField` can be `editable : false`, but still
       * modifiable through the UI.
       *
       * On mobile devices, {@link Core.widget.PickerField}s are set to `editable : false` by
       * default so that the user must select a value from the dropdown picker rather than having
       * to type a value which will cause a display of the virtual keyboard.
       *
       * If typing is essential to the functioning of the field, configuring the field
       * with `editable : true` will override this behaviour.
       * @prp {Boolean}
       * @default true
       */
      editable: true,
      defaultAction: "change",
      stepTriggers: null,
      /**
       * The triggers to add either before or after the input field. Each property name is the reference by which
       * an instantiated Trigger Widget may be retrieved from the live `triggers` property.
       *
       * Each trigger may have the following properties:
       * * `cls` The CSS class to apply.
       * * `handler` A method in the field to call upon click
       * * `align` `'start'` or `'end'` which end of the field the trigger should go.
       * * `weight` (Optional) Higher weighted triggers gravitate towards the input field.
       *
       * ```javascript
       * const textField = new TextField({
       *   triggers : {
       *       check : {
       *           cls : 'b-fa b-fa-check',
       *           handler() {
       *               ...
       *           }
       *       },
       *       ...
       *   }
       * })
       * ```
       *
       * @prp {Object<String,Core.widget.Widget>}
       * @accepts {Object<String,FieldTriggerConfig>}
       */
      triggers: null,
      /**
       * Specify `true` to highlight field after external value changes
       * @config {Boolean}
       */
      highlightExternalChange: false,
      localizableProperties: ["label", "title", "placeholder", "labelWidth"],
      /**
       * Specify `true` to auto select field contents on focus
       * @config {Boolean}
       * @default
       */
      autoSelect: false,
      /**
       * Sets the native `autocomplete` property of the underlying input element. For more information, please refer to
       * [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/autocomplete)
       * @config {String}
       * @default
       * @category Input element
       */
      autoComplete: "off",
      /**
       * Sets the native `spellcheck` property of the underlying input element. For more information, please refer to
       * [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/spellcheck)
       * @config {Boolean}
       * @category Input element
       */
      spellCheck: null,
      /**
       * Set to `false` to not highlight a field as invalid while typing, to instead show it on ENTER key press
       * or similar.
       * @config {Boolean}
       * @category Input element
       */
      validateOnInput: true,
      /**
       * Sets custom attributes of the underlying input element. For more information, please refer to
       * [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes)
       * @config {Object<String,String>}
       * @default
       * @category Input element
       */
      inputAttributes: null,
      /**
       * Sets the `type` attribute of the underlying input element (password, hidden, date, color, etc.).
       * @config {String}
       * @category Input element
       */
      inputType: "text",
      /**
       * Text alignment for the input field.
       * @config {String}
       * @category Input element
       */
      inputAlign: null,
      /**
       * A list of property names to be set in the underlying input element from properties
       * by the same name in this Field object if the value is not `== null`.
       * @private
       * @category Input element
       */
      attributes: {
        value: [
          "placeholder",
          "autoComplete",
          "spellCheck",
          "minLength",
          "maxLength",
          "pattern",
          "tabIndex",
          "min",
          "max"
        ],
        $config: {
          merge: "distinct"
        }
      },
      nullValue: {
        $config: null,
        value: null,
        default: null
        // store _value=null on prototype
      },
      updatedClsDuration: 500,
      inputReadOnly: false,
      ariaElement: "input",
      /**
       * The tab index of the input field or fields, or `null` for natural tab order (recommended). Setting to `0`
       * is equivalent to natural tab order, but is unnecessary for fields since they are naturally tabbable
       * (i.e., accessible via the TAB key). Setting to `-1` disables tabbability but allows for focus to be set
       * to the element programmatically.
       *
       * From [MDN documentation](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex):
       *
       * **Warning**: You are recommended to only use `0` and `-1` as tabindex values. Avoid using tabindex values
       * greater than 0 and CSS properties that can change the order of focusable HTML elements ([Ordering flex
       * items](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_flexible_box_layout/Ordering_flex_items)).
       * Doing so makes it difficult for people who rely on using keyboard for navigation or assistive technology
       * to navigate and operate page content. Instead, write the document with the elements in a logical sequence.
       *
       * @config {Number} tabIndex
       * @category Input element
       */
      /**
       * The configuration for additional items associated to this field. This is typically used to add contextual
       * fields related to a {@link Core.widget.Checkbox checkbox} or {@link Core.widget.Radio radio button}. See
       * these classes for examples of nested fields.
       *
       * This config can be provided as an array of widget config objects, an object with named widgets (see
       * {@link Core.widget.FieldContainer#config-namedItems}, or a config object for the whole
       * {@link Core.widget.FieldContainer field container}.
       *
       * To determine if the object is a `namedItems` object or a {@link Core.widget.FieldContainer field container}
       * config, the object is checked for either a `type` or an `items` property. If it has either of these
       * properties, it is a field container config object. Configuring the container is useful for applying
       * {@link Core.widget.FieldContainer#config-cls classes} or {@link Core.widget.FieldContainer#config-style styles}
       * to the container as a whole.
       *
       * For example, to add named items:
       * ```javascript
       *  new Checkbox({
       *      text : 'Separate shipping address',
       *      container : {
       *          address1 : {
       *              type : 'textfield'
       *          },
       *          address2 : {
       *              type : 'textfield'
       *          }
       *      }
       *  });
       * ```
       *
       * To style the container as well, move the items to the `items` property and add `cls`:
       * ```javascript
       *  new Checkbox({
       *      text : 'Separate shipping address',
       *      container : {
       *          cls   : 'address-form',
       *          items : {
       *              address1 : {
       *                  type : 'textfield'
       *              },
       *              address2 : {
       *                  type : 'textfield'
       *              }
       *          }
       *      }
       *  });
       * ```
       *
       * @config {Core.widget.FieldContainer}
       * @accepts {Object<string,ContainerItemConfig>|ContainerItemConfig[]|FieldContainerConfig}
       * @category Container
       */
      container: {
        $config: "nullify",
        value: null
      },
      /**
       * The default configuration for the {@link #config-container}.
       * @config {Core.widget.FieldContainer}
       * @accepts {Core.widget.FieldContainer|FieldContainerConfig}
       * @category Container
       * @internal
       */
      containerDefaults: {
        type: "fieldcontainer",
        syncableConfigs: {
          disabled: (field) => field.disabled
        },
        syncConfigTriggers: {
          disabled: 1
        }
      },
      /**
       * The config controls how the value of nested items are handled when a parent container gets or sets its
       * {@link Core.widget.Container#property-values}.
       *
       * The valid values for this config are:
       *  - `null` (the default) will include the values of this field's items if this field stores its own value.
       *  - `true` to always include the values of this field's items.
       *  - `false` to never include the values of this field's items.
       *  - `'nested'` to include the values of this field's items as a nested object under the field's `name`.
       *    This field's `value` is stored as the `'value'` property of that object.
       *  - Any other string is treated as the name of a property on this field. When truthy, the values of this
       *    field's items will be included.
       *  - A function can be supplied that must return a value given this field as its sole argument. If that
       *    value is truthy, this field's items will be included.
       *
       * @config {Boolean|String|Function}
       * @param {Core.widget.Field} field Field instance
       * @returns {Boolean}
       * @category Container
       */
      containValues: null,
      /**
       * Set this config to `true` to always display items horizontally along with this field. This assigns an
       * {@link Core.widget.layout.Box hbox} as the {@link Core.widget.Container#config-layout} to the
       * {@link #config-container}.
       *
       * Alternatively, set this config to `false` to wrap this field's items below. This assigns a
       * {@link Core.widget.layout.VBox} as the {@link Core.widget.Container#config-layout} to the
       * {@link #config-container}.
       *
       * This config defaults to `true` if there is exactly one item, and `false` otherwise.
       * @config {Boolean}
       * @category Container
       */
      inline: null,
      testConfig: {
        updatedClsDuration: 10
      },
      keyMap: {},
      twinSyncConfigs: ["value"]
    };
  }
  static get delayable() {
    return {
      highlightChanged: "raf"
    };
  }
  doDestroy() {
    var _a2, _b2, _c;
    const me = this, { triggers } = me;
    (_a2 = me.inputListenerRemover) == null ? void 0 : _a2.call(me);
    (_b2 = me.keyListenerRemover) == null ? void 0 : _b2.call(me);
    (_c = me.container) == null ? void 0 : _c.destroy();
    super.doDestroy();
    if (triggers) {
      for (const t of Object.values(triggers)) {
        t.destroy();
      }
    }
  }
  get childItems() {
    const { container, triggers } = this, ret = triggers ? Object.values(triggers) : [];
    return container ? [...ret, ...container.childItems] : ret;
  }
  get invalidValueError() {
    return "L{invalidValue}";
  }
  //endregion
  //region Event
  /**
   * Fired when the user types into this field.
   * @event input
   * @param {Core.widget.Field} source This field
   * @typings source -> {Core.widget.Field|any}
   * @param {String} value - This field's value
   * @typings value -> {String|Number|Boolean|any}
   * @param {Event} event - The triggering DOM event
   */
  /**
   * Fired when this field's value changes.
   * @event change
   * @param {Core.widget.Field} source - This Field
   * @typings source -> {Core.widget.Field|any}
   * @param {String} value - This field's value
   * @typings value -> {String|Number|Boolean|any}
   * @param {String} oldValue - This field's previous value
   * @typings oldValue -> {String|Number|Boolean|any}
   * @param {Boolean} valid - True if this field is in a valid state
   * @param {Event} event - The triggering DOM event if any
   * @param {Boolean} userAction - Triggered by user taking an action (`true`) or by setting a value (`false`)
   *
   * @typings checked -> {Boolean} // Used for compatibility with Checkbox event
   */
  /**
   * User performed default action (typed into this field).
   * @event action
   * @param {Core.widget.Field} source - This Field
   * @typings source -> {Core.widget.Field|any}
   * @param {String} value - This field's value
   * @typings value -> {String|Number|Boolean|any}
   * @param {String} oldValue - This field's previous value
   * @typings oldValue -> {String|Number|Boolean|any}
   * @param {Boolean} valid - True if this field is in a valid state
   * @param {Event} event - The triggering DOM event if any
   * @param {Core.data.Model} record Selected record. Available for fields with records selection functionality
   * @param {Core.data.Model[]} records Selected records as an array. Available for fields with records selection functionality
   * @param {Boolean} userAction - Triggered by user taking an action (`true`) or by setting a value (`false`)
   *
   * @typings checked -> {Boolean} // Used for compatibility with Checkbox event
   */
  /**
   * Fired when this field is {@link #function-clear cleared}.
   *
   * This will be triggered when a user clicks this field's clear {@link #property-triggers trigger}
   * @event clear
   * @param {Core.widget.Field} source - This Field
   * @typings source -> {Core.widget.Field|any}
   */
  /**
   * User clicked one of this field's {@link #property-triggers}
   * @event trigger
   * @param {Core.widget.Field} source This field
   * @typings source -> {Core.widget.Field|any}
   * @param {Core.widget.Widget} trigger The trigger activated by click or touch tap.
   */
  //endregion
  //region Init
  construct(config) {
    super.construct(config);
    const me = this;
    if (me.keyStrokeChangeDelay) {
      me.changeOnKeyStroke = me.buffer(me.internalOnChange, me.keyStrokeChangeDelay);
    }
  }
  // Adding triggers keymap to field keymap with prefix 'triggerName.';
  changeKeyMap(keyMap) {
    var _a2;
    const me = this;
    let mergedKeyMap = {};
    if (keyMap) {
      if (!me._fieldKeyMap) {
        me._fieldKeyMap = keyMap;
        ObjectHelper.assign(mergedKeyMap, keyMap);
      } else {
        ObjectHelper.assign(mergedKeyMap, me._fieldKeyMap);
      }
    }
    for (const triggerName in me.triggers) {
      if ((_a2 = me.triggers[triggerName]) == null ? void 0 : _a2.keyMap) {
        mergedKeyMap = me.mergeKeyMaps(mergedKeyMap, me.triggers[triggerName].keyMap, triggerName);
      }
    }
    return mergedKeyMap;
  }
  get keyMapSubComponents() {
    return this.triggers;
  }
  onFocusIn(e) {
    const me = this;
    me.valueOnFocus = ObjectHelper.clone(me.value);
    me.validOnFocus = !(me.errors && Object.keys(me.errors).length);
    super.onFocusIn(e);
    me.focusing = true;
    me.syncInvalid();
    me.focusing = false;
  }
  onFocusOut(e) {
    super.onFocusOut(e);
    this.syncRequired();
    this.onEditComplete();
  }
  /**
   * Template function which may be implemented by subclasses to synchronize
   * input state and validity state upon completion of the edit.
   * @internal
   */
  onEditComplete() {
  }
  get rootUiClass() {
    return _Field;
  }
  compose() {
    this.getConfig("clearable");
    const me = this, { container, innerElements, label, labels, labelCls, readOnly, stepTriggers, triggers } = me, triggerInstances = ObjectHelper.values(triggers, (k, v) => !v), startTriggers = triggerInstances.filter((t) => t.align === "start").sort(byWeight), endTriggers = triggerInstances.filter((t) => t.align !== "start").sort(byWeightReverse), setupLabel = me.setupLabel.bind(me), innerChildren = [
      ...startTriggers.map((t) => t.element),
      ...innerElements,
      ...endTriggers.map((t) => t.element)
    ];
    return {
      class: {
        "b-has-step-triggers": stepTriggers,
        "b-has-start-trigger": startTriggers.length,
        "b-no-input": !innerElements.length,
        "b-readonly": readOnly,
        "b-field-container": Boolean(container),
        "b-field-container-inline": container == null ? void 0 : container.inline
      },
      children: [
        ...(labels || emptyArray).filter((t) => t && t.align !== "end").map(setupLabel),
        (label || null) && setupLabel({
          reference: "labelElement",
          class: `b-label b-align-start ${labelCls || ""}`,
          html: label
        }),
        innerChildren.length ? {
          class: "b-field-inner",
          reference: "inputWrap",
          children: innerChildren
        } : null,
        ...(labels || emptyArray).filter((t) => (t == null ? void 0 : t.align) === "end").map(setupLabel),
        ...container ? [{
          reference: "containerWrapElement",
          children: [container.element],
          class: {
            "b-field-container-wrap": 1
          }
        }] : emptyArray
      ]
    };
  }
  updateElement(element, was) {
    var _a2;
    const me = this, value = me.initialConfig.value, { input } = me;
    super.updateElement(element, was);
    if (value != null) {
      me.value = value;
    }
    me.syncEmpty();
    me.updateInputReadOnly(me.inputReadOnly);
    me.syncInvalid();
    const keyEventElement = input || me.focusElement;
    (_a2 = me.keyListenerRemover) == null ? void 0 : _a2.call(me);
    me.keyListenerRemover = keyEventElement && EventHelper.on({
      element: keyEventElement,
      thisObj: me,
      keydown: "internalOnKeyEvent",
      keypress: "internalOnKeyEvent",
      keyup: "internalOnKeyEvent"
    });
  }
  // Subclasses may implement this.
  // Needed because Checkbox produces an array of two elements as its innerElements
  get innerElements() {
    return [this.inputElement];
  }
  /**
   * The input element at the heart if this field
   * @member {HTMLElement} input
   */
  get inputElement() {
    const { attributes, inputAttributes, id, inputCls, inputType, name } = this, domConfig = ObjectHelper.assign({
      reference: "input",
      tag: "input",
      type: inputType,
      name: name || id,
      id: `${id}-input`,
      retainElement: true
      // allow the input to be transplanted as in combo/chipView
    }, inputAttributes);
    if (inputCls) {
      domConfig.class = inputCls;
    }
    for (let key, value, i = attributes.length; i-- > 0; ) {
      key = attributes[i];
      value = this[key];
      if (value != null) {
        domConfig[key] = value;
      }
    }
    return domConfig;
  }
  //endregion
  //region Focus & select
  get caretPos() {
    return this.textSelection[0];
  }
  set caretPos(value) {
    this.textSelection = value;
  }
  get focusElement() {
    return this.input;
  }
  get textSelection() {
    var _a2;
    const input = this.input;
    let ret;
    try {
      ret = [input.selectionStart, input.selectionEnd, input.selectionDirection];
    } catch (e) {
      ret = ((_a2 = input.value) == null ? void 0 : _a2.length) || 0;
      ret = [ret, ret];
    }
    return ret;
  }
  set textSelection(value) {
    if (typeof value === "number") {
      this.select(value, value);
    } else {
      this.select(...value);
    }
  }
  get hasTextSelection() {
    const [selectionStart, selectionEnd] = this.textSelection;
    return selectionStart && selectionEnd - selectionStart > 0;
  }
  /**
   * Returns the input value for this field's input element that will be present if
   * the event carrying the given text is allowed to proceed.
   * @param {String} text
   * @returns {String}
   * @private
   */
  getAfterValue(text) {
    const [begin, end] = this.textSelection, value = this.input.value;
    return `${value.substr(0, begin)}${text}${value.substr(end || begin)}`;
  }
  /**
   * Selects the field contents. Optionally may be passed a start and end.
   * @param {Number} [start] The start index from which to select the input.
   * @param {Number} [end] The index at which to end the selection of the input.
   */
  select(start, end) {
    const input = this.focusElement;
    if (input.value.length) {
      if (arguments.length === 0) {
        this.selectAll();
        return;
      }
      if (!this.supportsTextSelection) {
        return;
      }
      input.setSelectionRange(start, end);
    }
  }
  moveCaretToEnd() {
    const input = this.input;
    if (input.createTextRange) {
      const range = input.createTextRange();
      range.collapse(false);
      range.select();
    } else if (this.supportsTextSelection) {
      this.select(input.value.length, input.value.length);
    }
  }
  selectAll() {
    this.focusElement.select();
  }
  get caretAtEnd() {
    return this.textSelection[1] === this.input.value.length;
  }
  get caretAtStart() {
    return !this.textSelection[1];
  }
  get selectedAll() {
    const { textSelection } = this;
    return !textSelection[0] && textSelection[1] === this.input.value.length;
  }
  // called on value changes to update styling of empty vs non-empty field
  syncEmpty() {
    var _a2;
    const { isEmptyInput, isEmpty, element } = this, clear = (_a2 = this.triggers) == null ? void 0 : _a2.clear, empty = isEmptyInput && isEmpty;
    if (element) {
      if (clear) {
        clear[empty ? "hide" : "show"]();
      }
      element.classList.toggle("b-empty", empty);
    }
  }
  updateHint() {
    this.syncHint();
  }
  updateHintHtml() {
    this.syncHint();
  }
  syncHint() {
    const me = this, { input, hint, hintHtml } = me, parent = input == null ? void 0 : input.parentElement;
    if (input) {
      let hintValue = hintHtml || hint;
      const hintElement = me.hintElement || hintValue && (me.hintElement = DomHelper.createElement({
        parent,
        className: "b-field-hint",
        nextSibling: input.nextSibling,
        children: [{
          className: "b-field-hint-content"
        }]
      }));
      if (hintElement) {
        if (typeof hintValue === "function") {
          hintValue = hintValue({ source: me, value: me.value });
        }
        hintElement.firstChild[hintHtml ? "innerHTML" : "textContent"] = hintValue || "";
      }
      me.element.classList[hintValue ? "remove" : "add"]("b-field-no-hint");
    }
  }
  //endregion
  //region Getters/setters
  updateEditable() {
    this.syncInputReadOnly();
  }
  syncInputReadOnly() {
    this.getConfig("readOnly");
    this.inputReadOnly = this._readOnly || this.editable === false;
  }
  updateInputReadOnly(readOnly) {
    const me = this, { input, inputListenerRemover } = me;
    if (input) {
      input.readOnly = readOnly ? "readOnly" : null;
      if (readOnly) {
        me.inputListenerRemover = inputListenerRemover == null ? void 0 : inputListenerRemover();
      } else if (!inputListenerRemover) {
        me.inputListenerRemover = EventHelper.on({
          element: input,
          thisObj: me,
          focus: "internalOnInputFocus",
          change: "internalOnChange",
          input: "internalOnInput"
        });
      }
    }
  }
  changeReadOnly(value) {
    return Boolean(value);
  }
  updateReadOnly(readOnly) {
    this.syncInputReadOnly();
  }
  updateClearable(clearable) {
    const me = this;
    me.getConfig("triggers");
    me.triggers = {
      clear: clearable && ObjectHelper.assign({
        cls: "b-icon-remove",
        weight: 1e3,
        key: "Shift+backspace",
        handler() {
          me.clear(...arguments);
        }
      }, clearable) || null
    };
  }
  changeTriggers(triggers, was) {
    const me = this, manager = me.$triggers || (me.$triggers = new DynamicObject({
      configName: "triggers",
      factory: _Field.Trigger,
      inferType: false,
      // the name of a trigger in the triggers object is not its type
      owner: me,
      created(instance) {
        FunctionHelper.after(instance, "onConfigChange", (ret, { name }) => {
          if (triggerConfigs[name]) {
            me.onConfigChange({
              name: "triggers",
              value: manager.target
            });
          }
        });
      },
      setup(config, name) {
        config.reference = config.ref = name;
        config.parent = me;
      }
    }));
    if (triggers && !me.stepTriggers && (triggers.back || triggers.forward)) {
      triggers = ObjectHelper.assign({}, triggers);
      delete triggers.back;
      delete triggers.forward;
    }
    manager.update(triggers);
    if (me.element) {
      me.keyMap = me.keyMap;
    }
    if (!was) {
      return manager.target;
    }
  }
  updateInputWidth(newValue) {
    this.input.style.width = DomHelper.setLength(newValue);
    this.element.classList.add("b-has-width");
  }
  updateInputAlign(newValue) {
    this.input.style.textAlign = newValue;
  }
  /**
   * Returns true if the field value is valid
   * @type {Boolean}
   * @readonly
   */
  get isValid() {
    const me = this;
    if (!me.disabled && !me.skipValidation) {
      me.syncRequired();
      if (me.checkValidity) {
        me.callback(me.checkValidity, me, [me]);
      }
      if (me.errors && Object.keys(me.errors).length) {
        return false;
      }
      const validity = me.validity;
      if (validity) {
        return validity.valid;
      }
    }
    return true;
  }
  /**
   * Returns `true` if this field is empty. That is, if it would violate the {@link #config-required}
   * setting.
   *
   * This may have different definitions in subclasses from simple text fields.
   * @type {Boolean}
   * @readonly
   */
  get isEmpty() {
    return this.value == null || this.value === "";
  }
  /**
   * Returns true if the field's input is empty
   * @type {Boolean}
   * @readonly
   */
  get isEmptyInput() {
    return !this.input || this.input.value == null || this.input.value === "";
  }
  /**
   * Returns the DOM `ValidityState` for this widget's input element, or `null` if there
   * isn't one.
   * @returns {ValidityState}
   * @private
   */
  get validity() {
    const input = this.input;
    return input == null ? void 0 : input.validity;
  }
  // Called by Editor.js, for overriding in subclasses that require async setting of values (custom editors in React)
  async setValue(value) {
    this.value = value;
  }
  changeValue(value, was) {
    if (value == null) {
      value = this.nullValue;
    }
    if (this.hasChanged(was, value)) {
      return value;
    }
    if (value === "") {
      this.syncRequired();
    }
    return was;
  }
  updateValue(value, oldValue) {
    const me = this;
    if (!me.isConfiguring) {
      me.syncRequired();
      if (!me.inputting) {
        me.triggerChange();
      }
    }
    if (!me.inputting) {
      me._lastValue = value;
    }
    me.syncInputFieldValue();
  }
  /**
   * Compares this field's value with its previous value. May be overridden in subclasses
   * which have more complex value types. See, for example, {@link Core.widget.DurationField}.
   * @param {*} oldValue
   * @param {*} newValue
   * @private
   */
  hasChanged(oldValue, newValue) {
    return newValue !== oldValue;
  }
  /**
   * Called by the base Field class's `set value` to sync the state of the UI with the field's value.
   *
   * Relies upon the class implementation of `get inputValue` to return a string representation of
   * the value for user consumption and editing.
   *
   * This method can be overriden.
   *
   * @param {Boolean} [skipHighlight] skip highlighting of the field
   * @returns {void}
   * @advanced
   */
  syncInputFieldValue(skipHighlight = false) {
    const me = this, { input, parent, inputValueAttr, inputValue } = me;
    if (input && !me.inputting && input[inputValueAttr] !== inputValue) {
      input[inputValueAttr] = inputValue;
      if (!me.isConfiguring && !me.containsFocus && me.highlightExternalChange) {
        input.classList.remove("b-field-updated");
        me.clearTimeout("removeUpdatedCls");
        if (parent) {
          const { assigningValues } = parent;
          if (assigningValues === true || (assigningValues == null ? void 0 : assigningValues.highlight) === false) {
            skipHighlight = true;
          }
        }
        if (!skipHighlight) {
          me.highlightChanged();
        }
      }
    }
    me.syncEmpty();
    me.syncInvalid();
  }
  highlightChanged() {
    this.input.classList.add("b-field-updated");
    this.setTimeout("removeUpdatedCls", this.updatedClsDuration);
  }
  removeUpdatedCls() {
    this.input.classList.remove("b-field-updated");
  }
  /**
   * A String representation of the value of this field for {@link #function-syncInputFieldValue} to use
   * as the input element's value.
   *
   * Subclasses may override this to create string representations.
   *
   * For example, {@link Core.widget.DateField}'s implementation will format the field date
   * value according to its configured {@link Core.widget.DateField#config-format}. And {@link Core.widget.Combo}'s
   * implementation will return the {@link Core.widget.Combo#config-displayField} of the selected record.
   * @internal
   * @readOnly
   */
  get inputValue() {
    return this.value == null ? "" : this.value;
  }
  get inputValueAttr() {
    return "value";
  }
  get supportsTextSelection() {
    const input = this.focusElement;
    return input && (input.tagName.toLowerCase() === "textarea" || input.type && /text|search|password|tel|url/.test(input.type));
  }
  //endregion
  //region Events
  internalOnInputFocus() {
    if (this.autoSelect) {
      this.selectAll();
    }
  }
  /**
   * Trigger event when fields input changes
   * @fires change
   * @private
   */
  internalOnChange(event) {
    const me = this;
    if (me.hasChanged(me._lastValue, me.value) && me.isValid) {
      me.triggerChange(event, true);
      me._lastValue = me.value;
    }
  }
  triggerChange(event, userAction = Boolean(this._isUserAction)) {
    const me = this, {
      value,
      _lastValue: oldValue,
      isValid: valid
    } = me;
    me.syncHint();
    me.triggerFieldChange({ value, oldValue, event, userAction, valid });
    if (me.defaultAction === "change") {
      me.trigger("action", { value, oldValue, event, userAction, valid });
    }
  }
  fetchInputValue() {
    this.value = this.input[this.inputValueAttr];
  }
  /**
   * Trigger event when user inputs into field
   * @fires input
   * @param event
   * @private
   */
  internalOnInput(event) {
    var _a2;
    const me = this;
    if (!me.validateOnInput) {
      me.errorTip.hide();
    }
    me.inputting = true;
    me.fetchInputValue();
    me.inputting = false;
    me.trigger("input", { value: me.value, event });
    (_a2 = me.changeOnKeyStroke) == null ? void 0 : _a2.call(me, event);
  }
  /**
   * Returns `true` if the {@link #property-input} field needs to be synced with the internal
   * {@link #property-value} of this field.
   *
   * May be overridden in subclasses where this is more complex such as multiSelect Combo
   * with a `ChipView` where the input area does not reflect the field's value.
   * @private
   */
  get needsInputSync() {
    var _a2;
    return this.input[this.inputValueAttr] !== String((_a2 = this.inputValue) != null ? _a2 : "");
  }
  internalOnKeyEvent(event) {
    const me = this, { value } = me;
    let stopEvent = false;
    if (event.type === "keydown") {
      if (event.key === "Escape" && !(me.readOnly || me.disabled)) {
        const wasValid = me.isValid, initialValue = "initialValue" in me ? me.initialValue : me.valueOnFocus, valueChanged = me.hasChanged(initialValue, value), { needsInputSync } = me;
        if (me.revertOnEscape && (!wasValid || valueChanged || needsInputSync)) {
          if (valueChanged) {
            me.value = initialValue;
          }
          if (needsInputSync) {
            me.syncInputFieldValue(true);
          }
          me.clearError();
          stopEvent = (valueChanged || needsInputSync) && me.isValid || wasValid !== me.validOnFocus;
        } else if (!me.isEmpty && me.clearable && me.revertOnEscape) {
          me.clear();
          event.stopPropagation();
        }
      }
    }
    if (!me.isDestroyed) {
      if (stopEvent) {
        event.stopImmediatePropagation();
      }
      me.trigger(event.type, { event });
    }
  }
  /**
   * Clears the value of this Field, and triggers the {@link #event-clear} event.
   */
  clear(event) {
    const me = this, { value } = me;
    if (value !== me.nullValue || me.input.value.length > 0) {
      me.value = me.nullValue;
      me.clearError();
      me.syncInvalid();
      me.trigger("clear", { event });
    }
  }
  /**
   * Called when disabled state is changed.
   * Used to add or remove 'b-invalid' class for the invalid field based on current disabled state.
   * @private
   */
  onDisabled() {
    this.syncInvalid();
  }
  //endregion
  //region Error
  syncRequired() {
    var _a2;
    const me = this;
    if (!me.isConfiguring && (!me.focusing || me.accessibility) && me.required && me.isEmpty && !((_a2 = me.parent) == null ? void 0 : _a2.isSettingValues)) {
      me.setError("L{fieldRequired}", me.updatingInvalid);
    } else {
      me.clearError("L{fieldRequired}", me.updatingInvalid);
    }
  }
  //endregion
  //region Container
  get containerCollapsed() {
    var _a2;
    return (_a2 = this.container) == null ? void 0 : _a2.collapsed;
  }
  set containerCollapsed(collapsed) {
    const { container } = this;
    if (container) {
      container.collapsed = collapsed;
    }
  }
  afterSyncChildConfigs(container) {
  }
  changeContainer(config, existing) {
    if (config && (Array.isArray(config) || !("type" in config || "items" in config))) {
      config = {
        items: config
      };
    }
    const me = this, defaults = config && ObjectHelper.merge({
      owner: me,
      layout: "box"
    }, me.containerDefaults), container = Widget.reconfigure(existing, config, {
      owner: me,
      defaults
    });
    if (container) {
      container.autoLayout = (defaults == null ? void 0 : defaults.layout) === "box";
      if (container === existing) {
        container.syncContainer();
        return;
      }
    }
    return container;
  }
  onConfigChange(info) {
    var _a2, _b2;
    super.onConfigChange(info);
    if ((_b2 = (_a2 = this.container) == null ? void 0 : _a2.syncConfigTriggers) == null ? void 0 : _b2[info.name]) {
      this.container.syncChildConfigs();
    }
  }
  assignValue(values, options) {
    const me = this, { container, containValues } = me, valueName = me.getValueName(options), storedValue = values && valueName in values;
    if (container && containValues !== false) {
      if (values && containValues === "nested") {
        if (storedValue) {
          values = ObjectHelper.clone(values[valueName]);
          super.assignValue({
            // Removing 'value:true' from nested values is a trick done by checkbox (could be done by
            // any toggle-like thing) so just put true back in:
            [valueName]: values && !("value" in values) || values.value
          }, options);
          if (values) {
            delete values.value;
          }
          container.assignValue(values, options);
        }
      } else {
        super.assignValue(values, options);
        container.assignValue(values, options);
      }
    } else {
      super.assignValue(values, options);
    }
  }
  gatherValue(values) {
    super.gatherValue(values);
    this.gatherValues(values);
  }
  gatherValues(values, storedValue) {
    const me = this, { container, containValues, valueName } = me, cvt = typeof containValues;
    storedValue = storedValue != null ? storedValue : valueName in values;
    if (container && containValues !== false) {
      if (containValues === "nested") {
        if (storedValue) {
          values[valueName] = {
            value: values[valueName],
            ...container.values
          };
        }
      } else if (containValues === true || containValues == null && storedValue || cvt === "string" && me[containValues] || cvt === "function" && containValues(me)) {
        container.gatherValues(values);
      }
    }
  }
  updateContainer(container, was) {
    was && this.detachListeners(was.id);
    if (container) {
      if (!container.rendered) {
        container.render();
      }
      container.syncContainer();
      container.syncChildConfigs();
    }
  }
  //endregion
};
//region Config
__publicField(_Field, "$name", "Field");
__publicField(_Field, "type", "field");
let Field = _Field;
Field.Trigger = (_a = class extends Widget {
  static get factoryable() {
    return {
      defaultType: Field.Trigger,
      extends: null
    };
  }
  get field() {
    return this.parent;
  }
  construct() {
    super.construct(...arguments);
    if (this.key) {
      this.keyMap = {
        [this.key]: "onClick"
      };
    }
  }
  compose() {
    return {
      "aria-hidden": true,
      class: {
        [`b-align-${this.align || "end"}`]: 1,
        "b-icon": 1
      },
      // eslint-disable-next-line bryntum/no-listeners-in-lib
      listeners: {
        click: "onClick",
        mousedown: "onMousedown"
      }
    };
  }
  changeAlign(align) {
    return align;
  }
  onClick(e) {
    const me = this, { field } = me;
    if (!(field.disabled || field.readOnly)) {
      if (e.key) {
        e.preventDefault();
      }
      if (field.trigger("trigger", { trigger: me }) !== false && me.handler) {
        field._isUserAction = true;
        field.callback(me.handler, field, [e, me]);
        field._isUserAction = false;
      }
    }
  }
  onMousedown(e) {
    const field = this.field, isKeyEvent = "key" in e;
    if (!isKeyEvent && DomHelper.isTouchEvent) {
      if (field.editable) {
        field.editable = false;
        field.setTimeout(() => field.editable = true, 500);
      }
    }
    e.preventDefault();
    if (DomHelper.getActiveElement(field.input) !== field.input) {
      field.focus();
    }
  }
}, __publicField(_a, "$name", "FieldTrigger"), // Factoryable type name
__publicField(_a, "type", "trigger"), // Align is a simple string at this level
__publicField(_a, "configurable", {
  align: null,
  weight: null,
  role: null
}), _a);
Field.SpinTrigger = (_b = class extends Field.Trigger {
  compose() {
    return {
      children: {
        upButton: {
          class: {
            "b-icon": 1,
            "b-spin-up": 1
          }
        },
        downButton: {
          class: {
            "b-icon": 1,
            "b-spin-down": 1
          }
        }
      }
    };
  }
  updateRepeat(repeat) {
    var _a2;
    (_a2 = this.clickRepeater) == null ? void 0 : _a2.destroy();
    this.clickRepeater = repeat ? ClickRepeater.new({
      element: this.element
    }, repeat) : null;
  }
  onClick(e) {
    const me = this, { field } = me;
    field._isUserAction = true;
    if (!(field.disabled || field.readOnly)) {
      if (e.target === me.upButton) {
        field.doSpinUp(e.shiftKey);
      } else if (e.target === me.downButton) {
        field.doSpinDown(e.shiftKey);
      }
    }
    field._isUserAction = false;
  }
  get isFocusable() {
    return !this.disabled && super.isFocusable;
  }
}, __publicField(_b, "$name", "SpinTrigger"), // Factoryable type name
__publicField(_b, "type", "spintrigger"), __publicField(_b, "configurable", {
  repeat: {
    $config: ["nullify"],
    value: true
  }
}), _b);
Field.Trigger.initClass();
Field.SpinTrigger.initClass();
Widget.register(Field.Trigger, "trigger");
Widget.register(Field.SpinTrigger, "spintrigger");
Field._$name = "Field";
export {
  Field as default
};
