var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Toolbar from "./Toolbar.js";
class PagingToolbar extends Toolbar {
  static get defaultConfig() {
    return {
      /**
       * The {@link Core.data.AjaxStore} that this PagingToolbar is to control.
       * @config {Core.data.AjaxStore}
       */
      store: null,
      defaults: {
        localeClass: this
      },
      items: {
        firstPageButton: {
          onClick: "up.onFirstPageClick",
          icon: "b-icon-first",
          weight: 100,
          tooltip: "L{PagingToolbar.firstPage}"
        },
        previousPageButton: {
          onClick: "up.onPreviousPageClick",
          icon: "b-icon-previous",
          weight: 110,
          tooltip: "L{PagingToolbar.prevPage}"
        },
        pageNumber: {
          type: "numberfield",
          label: "L{page}",
          min: 1,
          max: 1,
          triggers: null,
          onChange: "up.onPageNumberChange",
          highlightExternalChange: false,
          weight: 120
        },
        pageCount: {
          type: "widget",
          cls: "b-pagecount b-toolbar-text",
          weight: 130
        },
        nextPageButton: {
          onClick: "up.onNextPageClick",
          icon: "b-icon-next",
          weight: 140,
          tooltip: "L{PagingToolbar.nextPage}"
        },
        lastPageButton: {
          onClick: "up.onLastPageClick",
          icon: "b-icon-last",
          weight: 150,
          tooltip: "L{PagingToolbar.lastPage}"
        },
        separator: {
          type: "widget",
          cls: "b-toolbar-separator",
          weight: 151
        },
        reloadButton: {
          onClick: "up.onReloadClick",
          icon: "b-icon-reload",
          weight: 160,
          tooltip: "L{PagingToolbar.reload}"
        },
        spacer: {
          type: "widget",
          cls: "b-toolbar-fill",
          weight: 161
        },
        dataSummary: {
          type: "widget",
          cls: "b-toolbar-text",
          weight: 170
        }
      }
    };
  }
  // Retrieve store from grid when "assigned" to it
  set parent(parent) {
    super.parent = parent;
    if (!this.store) {
      this.store = parent.store;
    }
  }
  get parent() {
    return super.parent;
  }
  set store(store) {
    const me = this;
    me.detachListeners("store");
    me._store = store;
    if (store) {
      store.ion({
        name: "store",
        beforerequest: "onStoreBeforeRequest",
        afterrequest: "onStoreChange",
        change: "onStoreChange",
        thisObj: me
      });
      if (store.isLoading) {
        me.onStoreBeforeRequest();
      }
    }
  }
  get store() {
    return this._store;
  }
  onStoreBeforeRequest() {
    this.eachWidget((w) => w.disable());
  }
  updateLocalization() {
    this.updateSummary();
    super.updateLocalization();
  }
  updateSummary() {
    const me = this, { pageCount, dataSummary } = me.widgetMap;
    let count = 0, lastPage = 0, start = 0, end = 0, allCount = 0;
    if (me.store) {
      const { store } = me, { pageSize, currentPage } = store;
      count = store.count;
      lastPage = store.lastPage;
      allCount = store.totalCount;
      start = Math.max(0, (currentPage - 1) * pageSize + 1);
      end = Math.min(allCount, start + pageSize - 1);
    }
    pageCount.html = me.L("L{pageCountTemplate}")({ lastPage });
    dataSummary.html = count ? me.L("L{summaryTemplate}")({ start, end, allCount }) : me.L("L{noRecords}");
  }
  onStoreChange() {
    const me = this, { widgetMap, store } = me, { count, lastPage, currentPage } = store, {
      pageNumber,
      pageCount,
      firstPageButton,
      previousPageButton,
      nextPageButton,
      lastPageButton,
      dataSummary
    } = widgetMap;
    me.eachWidget((w) => w.enable());
    pageNumber.value = currentPage;
    pageNumber.max = lastPage;
    dataSummary.disabled = pageNumber.disabled = pageCount.disabled = !count;
    firstPageButton.disabled = previousPageButton.disabled = currentPage <= 1 || !count;
    nextPageButton.disabled = lastPageButton.disabled = currentPage >= lastPage || !count;
    me.updateSummary();
  }
  onPageNumberChange({ value }) {
    if (this.store.currentPage !== value) {
      this.store.loadPage(value);
    }
  }
  onFirstPageClick() {
    this.store.loadPage(1);
  }
  onPreviousPageClick() {
    this.store.previousPage();
  }
  onNextPageClick() {
    this.store.nextPage();
  }
  onLastPageClick() {
    this.store.loadPage(this.store.lastPage);
  }
  onReloadClick() {
    this.store.loadPage(this.store.currentPage);
  }
}
__publicField(PagingToolbar, "$name", "PagingToolbar");
__publicField(PagingToolbar, "type", "pagingtoolbar");
PagingToolbar.initClass();
PagingToolbar._$name = "PagingToolbar";
export {
  PagingToolbar as default
};
