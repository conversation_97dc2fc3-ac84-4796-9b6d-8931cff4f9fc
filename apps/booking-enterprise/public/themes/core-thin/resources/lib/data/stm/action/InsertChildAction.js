var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import { STORES_PROP } from "../Props.js";
import ActionBase, { ACTION_TYPES } from "./ActionBase.js";
const PARENT_MODEL_PROP = Symbol("PARENT_MODEL_PROP"), CHILD_MODELS_PROP = Symbol("CHILD_MODELS_PROP"), INSERT_INDEX_PROP = Symbol("INSERT_INDEX_PROP"), ORDERED_PRP = Symbol("ORDERED_PROP"), CONTEXT_PROP = Symbol("CONTEXT_PROP");
class InsertChildAction extends ActionBase {
  static get defaultConfig() {
    return {
      /**
       * Reference to a parent model a child model has been added to.
       *
       * @prp {Core.data.Model}
       * @readonly
       * @default
       */
      parentModel: void 0,
      /**
       * Children models inserted.
       *
       * @prp {Core.data.Model[]}
       * @readonly
       * @default
       */
      childModels: void 0,
      /**
       * Index a children models are inserted at
       *
       * @prp {Number}
       * @readonly
       * @default
       */
      insertIndex: void 0,
      /**
       * Reference node in the ordered tree
       *
       * @prp {Core.data.Model}
       * @readonly
       * @default
       * @internal
       */
      orderedBeforeNode: void 0,
      /**
       * Map having children models as keys and values containing previous parent
       * of each model and index at the previous parent.
       *
       * @prp {Object}
       * @readonly
       * @default
       */
      context: void 0,
      stores: void 0
    };
  }
  get type() {
    return ACTION_TYPES.INSERT_CHILD;
  }
  get parentModel() {
    return this[PARENT_MODEL_PROP];
  }
  set parentModel(model) {
    this[PARENT_MODEL_PROP] = model;
  }
  get childModels() {
    return this[CHILD_MODELS_PROP];
  }
  set childModels(models) {
    this[CHILD_MODELS_PROP] = models.slice(0);
  }
  get insertIndex() {
    return this[INSERT_INDEX_PROP];
  }
  set insertIndex(index) {
    this[INSERT_INDEX_PROP] = index;
  }
  get orderedBeforeNode() {
    return this[ORDERED_PRP];
  }
  set orderedBeforeNode(node) {
    this[ORDERED_PRP] = node;
  }
  get context() {
    return this[CONTEXT_PROP];
  }
  set context(ctx) {
    this[CONTEXT_PROP] = ctx;
  }
  get stores() {
    return this[STORES_PROP];
  }
  set stores(stores) {
    this[STORES_PROP] = stores;
  }
  undo() {
    const { parentModel, context, childModels } = this, byFromParent = /* @__PURE__ */ new Map(), newlyAdded = /* @__PURE__ */ new Set();
    for (const childModel of childModels) {
      const ctx = context.get(childModel);
      if (!ctx) {
        newlyAdded.add(childModel);
      } else {
        let undoTaskData = byFromParent.get(ctx.parent);
        if (!undoTaskData) {
          undoTaskData = { moveRight: [], moveLeft: [], moveFromAnotherParent: [] };
          byFromParent.set(ctx.parent, undoTaskData);
        }
        if (ctx.parent === parentModel) {
          if (ctx.index > childModel.parentIndex) {
            undoTaskData.moveRight.push({ parent: ctx.parent, model: childModel, index: ctx.index + 1 });
          } else {
            undoTaskData.moveLeft.push({ parent: ctx.parent, model: childModel, index: ctx.index });
          }
        } else {
          undoTaskData.moveFromAnotherParent.push({ parent: ctx.parent, model: childModel, index: ctx.index });
        }
      }
    }
    newlyAdded.forEach((model) => model.parent.removeChild(model));
    for (const undoTaskData of byFromParent.values()) {
      const { moveRight, moveLeft, moveFromAnotherParent } = undoTaskData;
      moveFromAnotherParent.forEach((task) => {
        task.parent.insertChild(task.model, task.index);
      });
      moveLeft.sort((a, b) => a.index - b.index);
      moveRight.sort((a, b) => b.index - a.index);
    }
    for (const undoTaskData of byFromParent.values()) {
      const { moveRight, moveLeft } = undoTaskData;
      moveLeft.forEach((task) => {
        task.parent.insertChild(task.model, task.index);
      });
      moveRight.forEach((task) => {
        task.parent.insertChild(task.model, task.index);
      });
    }
  }
  redo() {
    var _a, _b;
    const { parentModel, insertIndex, childModels, orderedBeforeNode } = this, insertBefore = (_a = parentModel.children) == null ? void 0 : _a[insertIndex];
    parentModel.insertChild(childModels, insertBefore, false, {
      orderedBeforeNode: orderedBeforeNode != null ? orderedBeforeNode : (_b = insertBefore == null ? void 0 : insertBefore.previousSibling) == null ? void 0 : _b.nextOrderedSibling
    });
  }
}
__publicField(InsertChildAction, "$name", "InsertChildAction");
InsertChildAction._$name = "InsertChildAction";
export {
  InsertChildAction as default
};
