import Base from "../../Base.js";
import { ACTION_TYPES } from "./action/ActionBase.js";
import { ACTION_QUEUE_PROP } from "./Props.js";
import { invertAction } from "./action/AllActions.js";
const TRANSACTION_TYPES = {
  DATA_CORRECTION: "DATA_CORRECTION",
  TEMPORARY: "TEMPORARY",
  CONFLICT_RESOLUTION: "CONFLICT_RESOLUTION"
};
class Transaction extends Base {
  static get configurable() {
    return {
      /**
       * Transaction title
       *
       * @config {String}
       */
      title: null,
      /**
       * Identifies if revision is temporary or not. Committed revisions get cleaned eventually, local
       * revisions should be committed.
       * @internal
       */
      committed: false,
      inputGeneration: 0
    };
  }
  static from(transactions, config = {}) {
    const result = new this({
      ...this.initialConfig,
      ...config,
      // In theory, here we can lose information about input generation. We are collecting latest generation of
      // actions from each transaction, and we set this value to the max. It means when we are collecting input,
      // we will skip actions with lower generation.
      inputGeneration: transactions.reduce((result2, transaction) => {
        var _a;
        return Math.max(result2, (_a = transaction.inputGeneration) != null ? _a : 0);
      }, 0),
      committed: false
    });
    result[ACTION_QUEUE_PROP] = transactions.flatMap((t) => t[ACTION_QUEUE_PROP].filter((a) => {
      if (t.inputGeneration > 0) {
        if (config.conflictResolutionFor) {
          return a.isUserInput === t.inputGeneration;
        } else {
          return a.isUserInput;
        }
      } else {
        return a;
      }
    }));
    return result;
  }
  /**
   * Creates transaction with the lowest generation of actions
   * @param {Core.data.stm.Transaction} transaction
   * @returns {Core.data.stm.Transaction}
   * @internal
   */
  static createTransactionWithOriginalInput(transaction) {
    const result = new this({
      ...this.initialConfig,
      title: transaction.title,
      committed: false
    });
    result[ACTION_QUEUE_PROP] = transaction[ACTION_QUEUE_PROP].filter((a) => a.isUserInput ? a.isUserInput === 1 : !a.isUserInput);
    return result;
  }
  construct(...args) {
    this[ACTION_QUEUE_PROP] = [];
    super.construct(...args);
  }
  /**
   * Gets transaction's actions queue
   *
   * @property {Core.data.stm.action.ActionBase[]}
   */
  get queue() {
    return this[ACTION_QUEUE_PROP].slice(0);
  }
  /**
   * Gets transaction's actions queue length
   *
   * @property {Number}
   */
  get length() {
    return this[ACTION_QUEUE_PROP].length;
  }
  get filterUserInput() {
    return this.inputGeneration > 0;
  }
  /**
   * Adds an action to the transaction.
   *
   * @param {Core.data.stm.action.ActionBase|Object} action
   */
  addAction(action) {
    this[ACTION_QUEUE_PROP].push(action);
  }
  /**
   * Undoes actions held
   */
  undo() {
    const queue = this[ACTION_QUEUE_PROP];
    for (let i = queue.length - 1; i >= 0; --i) {
      queue[i].undo();
    }
  }
  /**
   * Redoes actions held
   */
  redo() {
    const queue = this[ACTION_QUEUE_PROP];
    for (let i = 0, len = queue.length; i < len; ++i) {
      queue[i].redo();
    }
  }
  /**
   * Merges all update actions into one per model, keeping the oldest and the newest values
   */
  mergeUpdateModelActions() {
    var _a, _b;
    const queue = this[ACTION_QUEUE_PROP], recordMap = /* @__PURE__ */ new Map(), keep = [];
    function getActionKey(model, isUserInput = false) {
      return `${model.id}-${isUserInput}`;
    }
    for (const action of queue) {
      if (action.isUpdateAction && ((_a = action.model) == null ? void 0 : _a.isModel)) {
        const mergedRecordAction = recordMap.get(getActionKey(action.model, action.isUserInput));
        if (!mergedRecordAction) {
          recordMap.set(getActionKey(action.model, action.isUserInput), action);
          keep.push(action);
        } else {
          for (const key in action.oldData) {
            if (action.newData.hasOwnProperty(key)) {
              if (!mergedRecordAction.oldData.hasOwnProperty(key)) {
                mergedRecordAction.oldData[key] = action.oldData[key];
              }
              mergedRecordAction.newData[key] = action.newData[key];
            }
          }
        }
      } else {
        keep.push(action);
        if (action.isRemoveAction && ((_b = action.modelList) == null ? void 0 : _b.length)) {
          for (const model of action.modelList) {
            recordMap.delete(getActionKey(model));
            recordMap.delete(getActionKey(model, true));
          }
        }
      }
    }
    this[ACTION_QUEUE_PROP] = keep;
    this.inputGeneration = Math.max(...keep.map((a) => {
      var _a2;
      return (_a2 = a.isUserInput) != null ? _a2 : 0;
    }), 0);
  }
  // This is required to make task editor work with revisions and undo/redo. Some
  // models (dependency base model) contain code which prevents undo from adding record
  // back to the `added` store bag which does not allow revisions feature to be aware
  // of the change. To fix this we break the contract of the STM transaction and remove
  // update actions within one transaction which update added record.
  // The reasoning is that insert/add action will already contain reference to the actual
  // model, therefore there's no need to roll back field modifications. This assumption
  // seem safe.
  mergeAddUpdateModelActions() {
    const queue = this[ACTION_QUEUE_PROP], addedRecords = /* @__PURE__ */ new Set(), keep = [];
    for (const action of queue) {
      if (action.isAddAction || action.isInsertAction) {
        action.modelList.forEach((r) => addedRecords.add(r));
      } else if (action.isInsertChildAction) {
        action.childModels.forEach((r) => addedRecords.add(r));
      }
      if (action.isUpdateAction || action.isEventUpdateAction) {
        if (!addedRecords.has(action.model)) {
          keep.push(action);
        }
      } else {
        keep.push(action);
      }
    }
    this[ACTION_QUEUE_PROP] = keep;
    this.inputGeneration = Math.max(...keep.map((a) => {
      var _a;
      return (_a = a.isUserInput) != null ? _a : 0;
    }), 0);
  }
  invert() {
    const transaction = new this.constructor({ ...this.initialConfig, title: this.title });
    transaction[ACTION_QUEUE_PROP] = this[ACTION_QUEUE_PROP].map((action) => invertAction(action));
    return transaction;
  }
  /**
   * Returns a map of actions grouped by `isUserInput` value
   * @returns {Map}
   * @internal
   */
  groupUserInput() {
    return this[ACTION_QUEUE_PROP].reduce((result, action) => {
      var _a;
      const inputGroup = (_a = action.isUserInput) != null ? _a : "default";
      if (!result.has(inputGroup)) {
        result.set(inputGroup, []);
      }
      result.get(inputGroup).push(action);
      return result;
    }, /* @__PURE__ */ new Map());
  }
  /**
   * Collects all updates from the transaction into a map with model as key and changed data as value.
   * @param {Number} [generation] If undefined, last generation is returned
   * @returns {Object|undefined}
   * @internal
   */
  getUserInput(generation) {
    const updated = /* @__PURE__ */ new Map(), changes = {}, actions = this.groupUserInput().get(generation != null ? generation : this.inputGeneration || "default"), getBagForStore = (name, storeId) => {
      if (!(storeId in changes)) {
        changes[storeId] = { [name]: [] };
      } else if (!(name in changes[storeId])) {
        changes[storeId][name] = [];
      }
      return changes[storeId][name];
    };
    for (const action of actions) {
      switch (action.type) {
        case ACTION_TYPES.ADD:
        case ACTION_TYPES.INSERT: {
          if (action.inversed) {
            const removed = getBagForStore("removed", action.store.id);
            removed.push(...action.modelList);
          } else {
            const added = getBagForStore("added", action.store.id);
            added.push(...action.modelList);
          }
          break;
        }
        case ACTION_TYPES.REMOVE:
        case ACTION_TYPES.REMOVE_ALL: {
          if (action.inversed) {
            const added = getBagForStore("added", action.store.id);
            added.push(...action.modelList || action.allRecords);
          } else {
            const removed = getBagForStore("removed", action.store.id);
            removed.push(...action.modelList || action.allRecords);
          }
          break;
        }
        case ACTION_TYPES.INSERT_CHILD: {
          for (const store of action.stores) {
            action.childModels.forEach((record) => {
              if (store.added.includes(record)) {
                getBagForStore("added", store.id).push(record);
              } else if (store.removed.includes(record)) {
                getBagForStore("removed", store.id).push(record);
              } else {
                const treeChanges = record.hierarchyModificationDataToWrite;
                if (treeChanges) {
                  if (updated.has(record)) {
                    updated.set(record, { ...updated.get(record), ...treeChanges });
                  } else {
                    updated.set(record, treeChanges);
                  }
                }
              }
            });
          }
          break;
        }
        case ACTION_TYPES.REMOVE_CHILD: {
          for (const store of action.stores) {
            if (action.inversed) {
              const added = getBagForStore("added", store.id);
              added.push(...action.childModels);
            } else {
              const removed = getBagForStore("removed", store.id);
              removed.push(...action.childModels);
            }
          }
          break;
        }
        case ACTION_TYPES.UPDATE:
        case ACTION_TYPES.EVENT_UPDATE: {
          if (updated.has(action.model)) {
            updated.set(action.model, { ...updated.get(action.model), ...action.newData });
          } else {
            updated.set(action.model, { ...action.newData });
          }
          break;
        }
        default:
          break;
      }
    }
    if (updated.size > 0) {
      changes.updated = updated;
    }
    if (Object.keys(changes).length > 0) {
      return changes;
    }
  }
  markCurrentTransactionContentUserInput() {
    const generation = this.inputGeneration + 1, queue = this[ACTION_QUEUE_PROP];
    let generationAssigned = false;
    queue.forEach((action) => {
      if (action.isUserInput === void 0 && !action.isCalculated) {
        action.isUserInput = generation;
        generationAssigned = true;
      }
    });
    if (generationAssigned) {
      this.inputGeneration = generation;
    }
  }
  markCurrentTransactionContentCalculated() {
    const generation = this.inputGeneration, queue = this[ACTION_QUEUE_PROP];
    queue.forEach((action) => {
      if (action.isCalculated === void 0 && !action.isUserInput) {
        action.isCalculated = generation;
      }
    });
  }
  normalizeUserInputGeneration() {
    this[ACTION_QUEUE_PROP].forEach((action) => action.isUserInput = this.inputGeneration);
  }
}
Transaction._$name = "Transaction";
export {
  TRANSACTION_TYPES,
  Transaction as default
};
