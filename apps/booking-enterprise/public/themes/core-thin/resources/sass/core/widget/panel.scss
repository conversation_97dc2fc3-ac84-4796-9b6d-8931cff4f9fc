@import '../../variables';

// Only floating panels have radius. If they are contained, that would leave visible gaps.
.b-panel.b-floating {
    border-radius : $widget-border-radius;

    // By default the body gets a border radius
    &:not(.b-panel-has-header) > .b-panel-body-wrap {
        &, > .b-panel-content {
            border-radius : inherit;
        }
    }

    // If we have a header, it needs top border-radius instead of body (removed further down).
    // The same applies for no header but a top toolbar
    > .b-panel-header.b-dock-top,
    &:not(.b-has-header) > .b-panel-body-wrap > .b-toolbar.b-dock-top {
        border-top-left-radius  : $widget-border-radius;
        border-top-right-radius : $widget-border-radius;
    }

    // If we have a bottom toolbar, it needs bottom border-radius (removed from body further down)
    > .b-panel-body-wrap > .b-toolbar.b-dock-bottom {
        border-bottom-left-radius  : $widget-border-radius;
        border-bottom-right-radius : $widget-border-radius;
    }

    // Has header or top toolbar, remove top radius from body
    &.b-panel-has-header,
    &.b-panel-has-top-toolbar {
        > .b-panel-body-wrap > .b-panel-content {
            border-top-left-radius  : 0;
            border-top-right-radius : 0;
        }
    }

    // Has bottom toolbar, remove bottom radius from body
    &.b-panel-has-bottom-toolbar > .b-panel-body-wrap > .b-panel-content {
        border-bottom-left-radius  : 0;
        border-bottom-right-radius : 0;
    }
}

.b-panel {
    --panel-background-color : #{$panel-background-color};

    padding                  : 0;
    color                    : $panel-color;
    background-color         : var(--panel-background-color);
    z-index                  : 0; // establish our own stacking context to keep header above other elements

    > .b-positionable {
        z-index : 10;
    }

    // When collapsed, but revealed, the body wrap is moved to a deeper nesting position
    // We cannot use the > combinator
    .b-panel-body-wrap {
        background-color : var(--panel-background-color);
        overflow         : hidden;
    }

    .b-auto-container-panel {
        // The inner side of toolbars have collapsed padding so that they don't make double padding.
        .b-toolbar.b-innermost {
            &.b-dock-top {
                .b-toolbar-content {
                    padding-bottom : 0;
                }
            }

            &.b-dock-right {
                .b-toolbar-content {
                    padding-left : 0;
                }
            }

            &.b-dock-bottom {
                .b-toolbar-content {
                    padding-top : 0;
                }
            }

            &.b-dock-left {
                .b-toolbar-content {
                    padding-right : 0;
                }
            }
        }
    }

    // When we are displaying HTML, top and bottom must use margin because overflowing
    // content flows into the padding.
    &.b-html {
        .b-panel-content {
            padding-block : 3px;
            margin-block  : $panel-content-padding;
        }
    }

    > .b-panel-overlay,
    > .b-panel-body-wrap {
        z-index : 0; // lock all items inside the panel below the header
    }

    > .b-panel-collapse-size-locker {
        position : absolute !important;
    }

    &:not(.b-panel-collapsible-overlay) {
        &.b-collapsed > .b-panel-collapse-size-locker {
            clip : rect(0, 0, 0, 0);
        }

        &.b-panel-collapse-down:not(.b-panel-has-header),
        &.b-header-dock-bottom.b-panel-collapse-down,
        &.b-header-dock-bottom.b-panel-collapse-up {
            > .b-panel-collapse-size-locker {
                top : 0;
            }
        }

        &.b-panel-collapse-up:not(.b-panel-has-header),
        &.b-header-dock-top.b-panel-collapse-up,
        &.b-header-dock-top.b-panel-collapse-down {
            > .b-panel-collapse-size-locker {
                bottom : 0;
            }
        }

        &.b-panel-collapse-left:not(.b-panel-has-header),
        &.b-header-dock-left.b-panel-collapse-left,
        &.b-header-dock-left.b-panel-collapse-right {
            > .b-panel-collapse-size-locker {
                right : 0;
            }
        }

        &.b-panel-collapse-right:not(.b-panel-has-header),
        &.b-header-dock-right.b-panel-collapse-left,
        &.b-header-dock-right.b-panel-collapse-right {
            > .b-panel-collapse-size-locker {
                left : 0;
            }
        }
    }

    &.b-panel-collapsible-overlay {
        &:not(.b-collapsing,.b-expanding,.b-panel-overlay-revealed,.b-panel-overlay-revealing) {
            > .b-panel-overlay > .b-panel-overlay-header {
                display : none;
            }
        }

        &.b-panel-overlay-revealed > .b-panel-overlay {
            box-shadow : $panel-collapse-overlay-box-shadow;
            overflow   : visible;
        }

        > .b-panel-header {
            transition : transform 0.2s ease-in-out;
        }

        &.b-collapsed {
            > .b-panel-header > .b-collapsify-hide {
                display : none;
            }
        }

        // We need to "hide" the extra panel header because we go to overflow:visible during expand/collapse
        // to allow the overlay to remain visible
        &.b-collapsing,
        &.b-expanding {
            > .b-panel-header {
                opacity : 0;
            }
        }

        &:not(.b-collapsed,.b-collapsing) > .b-panel-overlay > .b-panel-overlay-header {
            display : none;
        }

        &.b-collapsing, &.b-expanding, &.b-panel-overlay-revealing, &.b-panel-overlay-revealed {
            overflow : visible;
            // Push collapsing/expanding/revealed panels above their normal siblings
            z-index  : 1;
        }

        &.b-collapsing {
            overflow : visible;

            > .b-panel-header {
                z-index : -1;
            }

            &.b-panel-collapse-up > .b-panel-header {
                transform : translate(0, -100%);
            }

            &.b-panel-collapse-down > .b-panel-header {
                transform : translate(0, 100%);
            }

            &.b-panel-collapse-right > .b-panel-header {
                transform : translate(100%, 0);
            }

            &.b-panel-collapse-left > .b-panel-header {
                transform : translate(-100%, 0);
            }
        }

        &.b-collapsed:not(.b-expanding) {
            > .b-panel-collapse-size-locker {
                transition :
                    transform 0.2s ease-in-out,
                    clip-path 0.2s ease-in-out,
                    top 0.2s ease-in-out,
                    right 0.2s ease-in-out,
                    bottom 0.2s ease-in-out,
                    left 0.2s ease-in-out;
            }

            &.b-panel-collapse-up {
                > .b-panel-collapse-size-locker {
                    transform : translate(0, -100%);
                }

                &.b-panel-overlay-revealed > .b-panel-collapse-size-locker {
                    clip-path : inset(0 0 -10px 0); // -10px bottom for box-shadow
                    transform : translate(0, 0);
                }
            }

            &.b-panel-collapse-down {
                > .b-panel-collapse-size-locker {
                    transform : translate(0, 100%);
                }

                &.b-panel-overlay-revealed > .b-panel-collapse-size-locker {
                    clip-path : inset(-10px 0 0 0); // -10px top for box-shadow
                    transform : translate(0, 0);
                }
            }

            &.b-panel-collapse-left {
                > .b-panel-collapse-size-locker {
                    transform : translate(-100%, 0);
                }

                &.b-panel-overlay-revealed > .b-panel-collapse-size-locker {
                    clip-path : inset(0 -10px 0 0); // -10px right for box-shadow
                    transform : translate(0, 0);
                }
            }

            &.b-panel-collapse-right {
                > .b-panel-collapse-size-locker {
                    transform : translate(100%, 0);
                }

                &.b-panel-overlay-revealed > .b-panel-collapse-size-locker {
                    clip-path : inset(0 0 0 -10px); // -10px left for box-shadow
                    transform : translate(0, 0);
                }
            }
        }

        &.b-expanding {
            flex     : none !important;
            overflow : visible;
        }
    }

    // When we collapse a panel, the header needs to stick to the desired edge:
    &.b-header-dock-right, &.b-header-dock-bottom {
        justify-content : flex-end;
    }

    &.b-collapsed {
        &:not(.b-expanding):not(.b-panel-overlay-revealed):not(.b-panel-overlay-revealing) {
            // Remove from tabbing sequence when collapsed
            visibility : hidden;

            // Ensure it's zero-sized by flexbox when collapsed
            flex       : 0 1 0px;
        }

        // But this must be visible
        > .b-panel-collapse-revealer {
            visibility : visible;
            cursor     : pointer;
        }
    }

    .b-panel-collapser-header {
        display : none;
    }

    &.b-collapse-unflex {
        // We must refuse to give away any space when collapsed
        flex-grow   : unset !important;
        flex-basis  : unset !important;
        flex-shrink : 0 !important;
    }

    &.b-collapsed, &.b-collapsing {
        .b-panel-collapser-header {
            display : flex;
        }

        &.b-header-dock-top, &.b-header-dock-bottom {
            min-height : auto !important;

            &.b-panel-collapsible-overlay {
                // override configured height on these panels and allow their header alone to determine their height
                height : unset !important;
            }

            .b-vbox > & {
                // all collapse modes need to ignore flex in this case
                flex : none !important;
            }
        }

        &.b-header-dock-right, &.b-header-dock-left {
            min-width : auto !important;

            &.b-panel-collapsible-overlay {
                // override configured width on these panels and allow their header alone to determine their width
                width : unset !important;
            }

            .b-hbox > & {
                // all collapse modes need to ignore flex in this case
                flex : none !important;
            }
        }
    }

    &.b-collapsed:not(.b-expanding) {
        &.b-header-dock-right, &.b-header-dock-left {
            width : unset !important;
            flex  : 0 0 auto !important;
        }
    }

    // Where the body joins the header, border radius has to be disabled
    &.b-header-dock-top {
        .b-panel-content {
            border-top-right-radius : 0;
            border-top-left-radius  : 0;
        }
    }

    &.b-header-dock-right {
        .b-panel-content {
            border-top-right-radius    : 0;
            border-bottom-right-radius : 0;
        }
    }

    &.b-header-dock-bottom {
        .b-panel-content {
            border-bottom-left-radius  : 0;
            border-bottom-right-radius : 0;
        }
    }

    &.b-header-dock-left {
        .b-panel-content {
            border-top-left-radius    : 0;
            border-bottom-left-radius : 0;
        }
    }

    &.b-panel-has-bottom-toolbar {
        .b-panel-content {
            border-bottom-left-radius  : 0;
            border-bottom-right-radius : 0;
        }
    }

    // Only radius the outer edges of docked items in floating Panels.
    &.b-floating {
        > .b-dock-top {
            border-top-right-radius : $widget-border-radius;
            border-top-left-radius  : $widget-border-radius;
        }

        > .b-dock-right {
            border-top-right-radius    : $widget-border-radius;
            border-bottom-right-radius : $widget-border-radius;
        }

        > .b-dock-bottom {
            position                   : static; // Inherits relative, which gives a subpixel spacing between docked and content
            border-bottom-left-radius  : $widget-border-radius;
            border-bottom-right-radius : $widget-border-radius;
        }

        > .b-dock-left {
            border-top-left-radius    : $widget-border-radius;
            border-bottom-left-radius : $widget-border-radius;
        }
    }

    &.b-panel-ui-plain {
        &,.b-panel-overlay {
            background-color : var(--panel-background-color);
        }

        .b-toolbar {
            background : transparent;
        }
    }
}

.b-panel-content {
    justify-content : space-between;
    overflow        : hidden;

    padding         : $panel-content-padding;

    .b-panel-ui-plain & {
        padding-block: 0;
    }

    // Try to help users out.
    // If they don't specify a layout, and only put one child in the Panel,
    // flex it to occupy the main axis.
    &.b-auto-container.b-single-child {
        > .b-container {
            flex : 1 1 auto;
        }
    }

    // No padding on body when using fit layout, let widget fill it fully
    &.b-fit-container {
        padding : 0;
    }
}

// When displayed in a tabpanel, match its color
.b-tabpanel .b-panel-content {
    background-color : $tabpanel-background-color;
}

.b-panel-collapser {
    z-index : 0;
}

.b-panel-header {
    display          : flex;
    background-color : $panel-header-background-color;
    color            : $panel-header-color;
    padding          : $panel-header-padding;
    flex             : 0 0 auto; // Must participate in flex using flex-basis: auto
    align-items      : center;
    z-index          : 1;

    .b-tool {
        color : $panel-tool-color;
    }

    &.b-panel-ui-toolbar {
        background-color : $panel-toolbar-background-color;
        color            : $panel-toolbar-color;
        padding          : $panel-toolbar-padding;

        .b-header-title {
            font-size   : unset;
            font-weight : unset;
        }

        .b-tool {
            color : inherit;
        }

        &.b-dock-top {
            border-bottom : $panel-toolbar-top-border-bottom;
        }

        &.b-panel-ui-plain {
            border : none;
        }
    }

    &.b-dock-top {
        border-bottom : $panel-header-border-bottom;
    }

    &.b-dock-right {
        flex-flow : column nowrap;
    }

    &.b-dock-left {
        flex-flow : column-reverse nowrap;

        .b-header-title {
            transform : rotate(180deg);
        }
    }

    &.b-panel-ui-plain {
        background-color : transparent;
        border           : none;
        color            : inherit;
    }

    .b-header-title {
        // If there are tools inside the header, add some air
        &:not(:last-child) {
            padding-inline-end : 1em;
        }

        flex            : 1 1 auto;
        text-align      : center;
        justify-content : center;
        font-size       : $panel-header-font-size;
        font-weight     : $panel-header-font-weight;
        white-space     : nowrap;
        overflow        : hidden;
        text-overflow   : ellipsis;

        &.b-align-start {
            text-align      : start;
            justify-content : flex-start;
        }

        &.b-align-center {
            text-align         : center;
            justify-content    : center;
            // No additional air for tools if centering
            padding-inline-end : 0;
        }

        &.b-align-end {
            text-align      : end;
            justify-content : flex-end;

            // Some air before tool icons if aligned end
            &:not(:last-child) {
                margin-inline-end : 1em;
            }
        }

        .b-panel.b-panel-ui-plain > &.b-panel-ui-plain {
            font-size   : 1.2em;
            font-weight : 600;
        }
    }

    // This invisible pseudo-el mimics the height of a tool to ensure that panel headers maintain the same height
    // when tools are added/removed/hidden/etc
    &:after {
        content    : " ";
        font-size  : $tool-font-size;
        height     : $tool-size;
        width      : 1px;
        visibility : hidden;
        display    : inline;
    }

    &.b-dock-right, &.b-dock-left {
        .b-header-title {
            -webkit-writing-mode : vertical-lr;
            writing-mode         : vertical-lr;
            -ms-writing-mode     : tb-lr;
        }

        &:after {
            height : 1px;
            width  : $tool-size;
        }
    }

    &.b-dock-right, &.b-dock-bottom {
        order : 100;
    }
}

.b-button.b-tool,
.b-tool {
    cursor           : pointer;
    color            : $tool-color;
    height           : $tool-size;
    width            : $tool-size;
    font-size        : $tool-font-size;
    display          : flex;
    align-items      : center;
    justify-content  : center;
    border-radius    : 50%;

    // Override user agent button styling
    border           : 0 none;
    background-color : transparent;

    // Remove inner focus outline in FF
    &::-moz-focus-inner {
        border : 0;
    }

    &:focus {
        outline : none;

        .b-using-keyboard & {
            background-color : rgba(220, 220, 220, 0.5);
        }
    }

    // Tools may not be compressed
    flex-shrink      : 0;

    // Constrain what gets repainted.
    contain          : paint;

    &.b-icon:before {
        width       : $tool-icon-size;
        height      : $tool-icon-size;
        text-align  : center;
        // To avoid misalignment from the smaller font-size used on tools
        line-height : inherit;
    }

    &:hover {
        opacity : 0.8;
    }

    &.b-disabled {
        opacity : 0.4;
    }

    &.b-rotate-left:before {
        transform : rotate(270deg);
    }

    &.b-rotate-right:before {
        transform : rotate(90deg);
    }

    i {
        // Aligns i to the center of the button by adjusting i size to the content
        display : flex;
    }

    a {
        color : $panel-tool-color;
    }
}

.b-button.b-tool {
    // Button defines a larger min-height & min-width, need to counteract
    min-height : 2em;
    min-width  : 2em;
}

.b-collapsetool {
    &:not(.b-collapsing):before {
        transition : all 0.2s ease-in-out;
    }

    // Normal tools rotate 90/270 when their panel headers are rotated, but this is not desired for the collapsetool
    // since it points in the direction of collapse.
    &.b-rotate-left,
    &.b-rotate-right {
        &:before {
            transform : rotate(0deg);
        }
    }

    &.b-collapsed:before {
        transform : rotate(180deg);
    }

    .b-panel-ui-toolbar.b-panel-collapse-right .b-collapsible-tr.b-dock-right & {
        margin-block-start : 0.6em;
    }

    .b-panel-ui-toolbar.b-panel-collapse-right .b-collapsible-tr.b-dock-top & {
        margin-inline-end : 0.7em;
    }
}

.b-dock-top, .b-dock-bottom {
    .b-header-title:not(:last-child) {
        padding-inline-end : 0.2em;
    }

    .b-tool {
        &.b-align-start,
        &.b-align-end {
            margin-block : 0;
        }
    }
}

.b-dock-right, .b-dock-left {
    .b-header-title:not(:last-child) {
        padding : 0.2em 0;
    }

    .b-tool {
        &.b-align-start {
            margin-block-end : 0.4em;
        }

        &.b-align-end {
            margin-block-start : 0.4em;
        }
    }
}
