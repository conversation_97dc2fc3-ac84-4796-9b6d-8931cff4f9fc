@charset "UTF-8";

.b-theme-info:before{
  content:'{"name":"Classic-Light"}';
}

:root{
  --bryntum-version:"6.0.4";
}
:root, :host{
  --b-fa-style-family-classic:"Font Awesome 6 Free";
  --b-fa-font-solid:normal 900 1em/1 "Font Awesome 6 Free";
}

@font-face{
  font-family:"Font Awesome 6 Free";
  font-style:normal;
  font-weight:900;
  font-display:block;
  src:url("../../core-thin/resources/fonts/fa-solid-900.woff2") format("woff2"), url("../../core-thin/resources/fonts/fa-solid-900.ttf") format("truetype");
}
.fas,
.b-fa-solid{
  font-weight:900;
}

.b-content-icon{
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
  display:inline-block;
  font-style:normal;
  font-variant:normal;
  font-weight:normal;
  line-height:1;
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  vertical-align:0;
}

.b-fa{
  font-family:var(--b-fa-style-family, "Font Awesome 6 Free");
  font-weight:var(--b-fa-style, 900);
}

.b-fa-solid,
.b-fa-regular,
.b-fa-brands,
.fas,
.far,
.fab,
.b-fa-sharp-solid,
.b-fa-classic,
.b-fa{
  -moz-osx-font-smoothing:grayscale;
  -webkit-font-smoothing:antialiased;
  display:var(--b-fa-display, inline-block);
  font-style:normal;
  font-variant:normal;
  line-height:1;
  text-rendering:auto;
}

.fas,
.b-fa-classic,
.b-fa-solid,
.far,
.b-fa-regular{
  font-family:"Font Awesome 6 Free";
}

.fab,
.b-fa-brands{
  font-family:"Font Awesome 6 Brands";
}

.b-fa-lg{
  font-size:1.3333333333em;
  line-height:0.75em;
  vertical-align:-0.0667em;
}

.b-fa-xs{
  font-size:0.75em;
}

.b-fa-sm{
  font-size:0.875em;
}

.b-fa-1x{
  font-size:1em;
}

.b-fa-2x{
  font-size:2em;
}

.b-fa-3x{
  font-size:3em;
}

.b-fa-4x{
  font-size:4em;
}

.b-fa-5x{
  font-size:5em;
}

.b-fa-6x{
  font-size:6em;
}

.b-fa-7x{
  font-size:7em;
}

.b-fa-8x{
  font-size:8em;
}

.b-fa-9x{
  font-size:9em;
}

.b-fa-10x{
  font-size:10em;
}

.b-fa-fw, .b-fw-icon:before{
  text-align:center;
  width:1.25em;
}

.b-fa-ul{
  list-style-type:none;
  margin-left:var(--b-fa-li-margin, 2.5em);
  padding-left:0;
}
.b-fa-ul > li{
  position:relative;
}

.b-fa-li{
  left:calc(-1 * var(--b-fa-li-width, 2em));
  position:absolute;
  text-align:center;
  width:var(--b-fa-li-width, 2em);
  line-height:inherit;
}

.b-fa-border{
  border-color:var(--b-fa-border-color, #eee);
  border-radius:var(--b-fa-border-radius, 0.1em);
  border-style:var(--b-fa-border-style, solid);
  border-width:var(--b-fa-border-width, 0.08em);
  padding:var(--b-fa-border-padding, 0.2em 0.25em 0.15em);
}

.b-fa-pull-left{
  float:left;
  margin-right:var(--b-fa-pull-margin, 0.3em);
}

.b-fa-pull-right{
  float:right;
  margin-left:var(--b-fa-pull-margin, 0.3em);
}

.b-fa-beat{
  animation-name:b-fa-beat;
  animation-delay:var(--b-fa-animation-delay, 0s);
  animation-direction:var(--b-fa-animation-direction, normal);
  animation-duration:var(--b-fa-animation-duration, 1s);
  animation-iteration-count:var(--b-fa-animation-iteration-count, infinite);
  animation-timing-function:var(--b-fa-animation-timing, ease-in-out);
}

.b-fa-bounce{
  animation-name:b-fa-bounce;
  animation-delay:var(--b-fa-animation-delay, 0s);
  animation-direction:var(--b-fa-animation-direction, normal);
  animation-duration:var(--b-fa-animation-duration, 1s);
  animation-iteration-count:var(--b-fa-animation-iteration-count, infinite);
  animation-timing-function:var(--b-fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));
}

.b-fa-fade{
  animation-name:b-fa-fade;
  animation-delay:var(--b-fa-animation-delay, 0s);
  animation-direction:var(--b-fa-animation-direction, normal);
  animation-duration:var(--b-fa-animation-duration, 1s);
  animation-iteration-count:var(--b-fa-animation-iteration-count, infinite);
  animation-timing-function:var(--b-fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

.b-fa-beat-fade{
  animation-name:b-fa-beat-fade;
  animation-delay:var(--b-fa-animation-delay, 0s);
  animation-direction:var(--b-fa-animation-direction, normal);
  animation-duration:var(--b-fa-animation-duration, 1s);
  animation-iteration-count:var(--b-fa-animation-iteration-count, infinite);
  animation-timing-function:var(--b-fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

.b-fa-flip{
  animation-name:b-fa-flip;
  animation-delay:var(--b-fa-animation-delay, 0s);
  animation-direction:var(--b-fa-animation-direction, normal);
  animation-duration:var(--b-fa-animation-duration, 1s);
  animation-iteration-count:var(--b-fa-animation-iteration-count, infinite);
  animation-timing-function:var(--b-fa-animation-timing, ease-in-out);
}

.b-fa-shake{
  animation-name:b-fa-shake;
  animation-delay:var(--b-fa-animation-delay, 0s);
  animation-direction:var(--b-fa-animation-direction, normal);
  animation-duration:var(--b-fa-animation-duration, 1s);
  animation-iteration-count:var(--b-fa-animation-iteration-count, infinite);
  animation-timing-function:var(--b-fa-animation-timing, linear);
}

.b-fa-spin{
  animation-name:b-fa-spin;
  animation-delay:var(--b-fa-animation-delay, 0s);
  animation-direction:var(--b-fa-animation-direction, normal);
  animation-duration:var(--b-fa-animation-duration, 2s);
  animation-iteration-count:var(--b-fa-animation-iteration-count, infinite);
  animation-timing-function:var(--b-fa-animation-timing, linear);
}

.b-fa-spin-reverse{
  --b-fa-animation-direction:reverse;
}

.b-fa-pulse,
.b-fa-spin-pulse{
  animation-name:b-fa-spin;
  animation-direction:var(--b-fa-animation-direction, normal);
  animation-duration:var(--b-fa-animation-duration, 1s);
  animation-iteration-count:var(--b-fa-animation-iteration-count, infinite);
  animation-timing-function:var(--b-fa-animation-timing, steps(8));
}

@media (prefers-reduced-motion: reduce){
  .b-fa-beat,
  .b-fa-bounce,
  .b-fa-fade,
  .b-fa-beat-fade,
  .b-fa-flip,
  .b-fa-pulse,
  .b-fa-shake,
  .b-fa-spin,
  .b-fa-spin-pulse{
    animation-delay:-1ms;
    animation-duration:1ms;
    animation-iteration-count:1;
    transition-delay:0s;
    transition-duration:0s;
  }
}
@keyframes b-fa-beat{
  0%, 90%{
    transform:scale(1);
  }
  45%{
    transform:scale(var(--b-fa-beat-scale, 1.25));
  }
}
@keyframes b-fa-bounce{
  0%{
    transform:scale(1, 1) translateY(0);
  }
  10%{
    transform:scale(var(--b-fa-bounce-start-scale-x, 1.1), var(--b-fa-bounce-start-scale-y, 0.9)) translateY(0);
  }
  30%{
    transform:scale(var(--b-fa-bounce-jump-scale-x, 0.9), var(--b-fa-bounce-jump-scale-y, 1.1)) translateY(var(--b-fa-bounce-height, -0.5em));
  }
  50%{
    transform:scale(var(--b-fa-bounce-land-scale-x, 1.05), var(--b-fa-bounce-land-scale-y, 0.95)) translateY(0);
  }
  57%{
    transform:scale(1, 1) translateY(var(--b-fa-bounce-rebound, -0.125em));
  }
  64%{
    transform:scale(1, 1) translateY(0);
  }
  100%{
    transform:scale(1, 1) translateY(0);
  }
}
@keyframes b-fa-fade{
  50%{
    opacity:var(--b-fa-fade-opacity, 0.4);
  }
}
@keyframes b-fa-beat-fade{
  0%, 100%{
    opacity:var(--b-fa-beat-fade-opacity, 0.4);
    transform:scale(1);
  }
  50%{
    opacity:1;
    transform:scale(var(--b-fa-beat-fade-scale, 1.125));
  }
}
@keyframes b-fa-flip{
  50%{
    transform:rotate3d(var(--b-fa-flip-x, 0), var(--b-fa-flip-y, 1), var(--b-fa-flip-z, 0), var(--b-fa-flip-angle, -180deg));
  }
}
@keyframes b-fa-shake{
  0%{
    transform:rotate(-15deg);
  }
  4%{
    transform:rotate(15deg);
  }
  8%, 24%{
    transform:rotate(-18deg);
  }
  12%, 28%{
    transform:rotate(18deg);
  }
  16%{
    transform:rotate(-22deg);
  }
  20%{
    transform:rotate(22deg);
  }
  32%{
    transform:rotate(-12deg);
  }
  36%{
    transform:rotate(12deg);
  }
  40%, 100%{
    transform:rotate(0deg);
  }
}
@keyframes b-fa-spin{
  0%{
    transform:rotate(0deg);
  }
  100%{
    transform:rotate(360deg);
  }
}
.b-fa-rotate-90{
  transform:rotate(90deg);
}

.b-fa-rotate-180{
  transform:rotate(180deg);
}

.b-fa-rotate-270{
  transform:rotate(270deg);
}

.b-fa-flip-horizontal{
  transform:scale(-1, 1);
}

.b-fa-flip-vertical{
  transform:scale(1, -1);
}

.b-fa-flip-both,
.b-fa-flip-horizontal.b-fa-flip-vertical{
  transform:scale(-1, -1);
}

.b-fa-rotate-by{
  transform:rotate(var(--b-fa-rotate-angle, 0));
}

.b-fa-stack{
  display:inline-block;
  height:2em;
  line-height:2em;
  position:relative;
  vertical-align:middle;
  width:2.5em;
}

.b-fa-stack-1x,
.b-fa-stack-2x{
  left:0;
  position:absolute;
  text-align:center;
  width:100%;
  z-index:var(--b-fa-stack-z-index, auto);
}

.b-fa-stack-1x{
  line-height:inherit;
}

.b-fa-stack-2x{
  font-size:2em;
}

.b-fa-inverse{
  color:var(--b-fa-inverse, #fff);
}
.b-fa-0::before{
  content:"\30 ";
}

.b-fa-1::before{
  content:"\31 ";
}

.b-fa-2::before{
  content:"\32 ";
}

.b-fa-3::before{
  content:"\33 ";
}

.b-fa-4::before{
  content:"\34 ";
}

.b-fa-5::before{
  content:"\35 ";
}

.b-fa-6::before{
  content:"\36 ";
}

.b-fa-7::before{
  content:"\37 ";
}

.b-fa-8::before{
  content:"\38 ";
}

.b-fa-9::before{
  content:"\39 ";
}

.b-fa-fill-drip::before{
  content:"\f576";
}

.b-fa-arrows-to-circle::before{
  content:"\e4bd";
}

.b-fa-circle-chevron-right::before{
  content:"\f138";
}

.b-fa-chevron-circle-right::before{
  content:"\f138";
}

.b-fa-at::before{
  content:"\@";
}

.b-fa-trash-can::before{
  content:"\f2ed";
}

.b-fa-trash-alt::before{
  content:"\f2ed";
}

.b-fa-text-height::before{
  content:"\f034";
}

.b-fa-user-xmark::before{
  content:"\f235";
}

.b-fa-user-times::before{
  content:"\f235";
}

.b-fa-stethoscope::before{
  content:"\f0f1";
}

.b-fa-message::before{
  content:"\f27a";
}

.b-fa-comment-alt::before{
  content:"\f27a";
}

.b-fa-info::before{
  content:"\f129";
}

.b-fa-down-left-and-up-right-to-center::before{
  content:"\f422";
}

.b-fa-compress-alt::before{
  content:"\f422";
}

.b-fa-explosion::before{
  content:"\e4e9";
}

.b-fa-file-lines::before{
  content:"\f15c";
}

.b-fa-file-alt::before{
  content:"\f15c";
}

.b-fa-file-text::before{
  content:"\f15c";
}

.b-fa-wave-square::before{
  content:"\f83e";
}

.b-fa-ring::before{
  content:"\f70b";
}

.b-fa-building-un::before{
  content:"\e4d9";
}

.b-fa-dice-three::before{
  content:"\f527";
}

.b-fa-calendar-days::before{
  content:"\f073";
}

.b-fa-calendar-alt::before{
  content:"\f073";
}

.b-fa-anchor-circle-check::before{
  content:"\e4aa";
}

.b-fa-building-circle-arrow-right::before{
  content:"\e4d1";
}

.b-fa-volleyball::before{
  content:"\f45f";
}

.b-fa-volleyball-ball::before{
  content:"\f45f";
}

.b-fa-arrows-up-to-line::before{
  content:"\e4c2";
}

.b-fa-sort-down::before{
  content:"\f0dd";
}

.b-fa-sort-desc::before{
  content:"\f0dd";
}

.b-fa-circle-minus::before{
  content:"\f056";
}

.b-fa-minus-circle::before{
  content:"\f056";
}

.b-fa-door-open::before{
  content:"\f52b";
}

.b-fa-right-from-bracket::before{
  content:"\f2f5";
}

.b-fa-sign-out-alt::before{
  content:"\f2f5";
}

.b-fa-atom::before{
  content:"\f5d2";
}

.b-fa-soap::before{
  content:"\e06e";
}

.b-fa-icons::before{
  content:"\f86d";
}

.b-fa-heart-music-camera-bolt::before{
  content:"\f86d";
}

.b-fa-microphone-lines-slash::before{
  content:"\f539";
}

.b-fa-microphone-alt-slash::before{
  content:"\f539";
}

.b-fa-bridge-circle-check::before{
  content:"\e4c9";
}

.b-fa-pump-medical::before{
  content:"\e06a";
}

.b-fa-fingerprint::before{
  content:"\f577";
}

.b-fa-hand-point-right::before{
  content:"\f0a4";
}

.b-fa-magnifying-glass-location::before{
  content:"\f689";
}

.b-fa-search-location::before{
  content:"\f689";
}

.b-fa-forward-step::before{
  content:"\f051";
}

.b-fa-step-forward::before{
  content:"\f051";
}

.b-fa-face-smile-beam::before{
  content:"\f5b8";
}

.b-fa-smile-beam::before{
  content:"\f5b8";
}

.b-fa-flag-checkered::before{
  content:"\f11e";
}

.b-fa-football::before{
  content:"\f44e";
}

.b-fa-football-ball::before{
  content:"\f44e";
}

.b-fa-school-circle-exclamation::before{
  content:"\e56c";
}

.b-fa-crop::before{
  content:"\f125";
}

.b-fa-angles-down::before{
  content:"\f103";
}

.b-fa-angle-double-down::before{
  content:"\f103";
}

.b-fa-users-rectangle::before{
  content:"\e594";
}

.b-fa-people-roof::before{
  content:"\e537";
}

.b-fa-people-line::before{
  content:"\e534";
}

.b-fa-beer-mug-empty::before{
  content:"\f0fc";
}

.b-fa-beer::before{
  content:"\f0fc";
}

.b-fa-diagram-predecessor::before{
  content:"\e477";
}

.b-fa-arrow-up-long::before{
  content:"\f176";
}

.b-fa-long-arrow-up::before{
  content:"\f176";
}

.b-fa-fire-flame-simple::before{
  content:"\f46a";
}

.b-fa-burn::before{
  content:"\f46a";
}

.b-fa-person::before{
  content:"\f183";
}

.b-fa-male::before{
  content:"\f183";
}

.b-fa-laptop::before{
  content:"\f109";
}

.b-fa-file-csv::before{
  content:"\f6dd";
}

.b-fa-menorah::before{
  content:"\f676";
}

.b-fa-truck-plane::before{
  content:"\e58f";
}

.b-fa-record-vinyl::before{
  content:"\f8d9";
}

.b-fa-face-grin-stars::before{
  content:"\f587";
}

.b-fa-grin-stars::before{
  content:"\f587";
}

.b-fa-bong::before{
  content:"\f55c";
}

.b-fa-spaghetti-monster-flying::before{
  content:"\f67b";
}

.b-fa-pastafarianism::before{
  content:"\f67b";
}

.b-fa-arrow-down-up-across-line::before{
  content:"\e4af";
}

.b-fa-spoon::before{
  content:"\f2e5";
}

.b-fa-utensil-spoon::before{
  content:"\f2e5";
}

.b-fa-jar-wheat::before{
  content:"\e517";
}

.b-fa-envelopes-bulk::before{
  content:"\f674";
}

.b-fa-mail-bulk::before{
  content:"\f674";
}

.b-fa-file-circle-exclamation::before{
  content:"\e4eb";
}

.b-fa-circle-h::before{
  content:"\f47e";
}

.b-fa-hospital-symbol::before{
  content:"\f47e";
}

.b-fa-pager::before{
  content:"\f815";
}

.b-fa-address-book::before{
  content:"\f2b9";
}

.b-fa-contact-book::before{
  content:"\f2b9";
}

.b-fa-strikethrough::before{
  content:"\f0cc";
}

.b-fa-k::before{
  content:"K";
}

.b-fa-landmark-flag::before{
  content:"\e51c";
}

.b-fa-pencil::before{
  content:"\f303";
}

.b-fa-pencil-alt::before{
  content:"\f303";
}

.b-fa-backward::before{
  content:"\f04a";
}

.b-fa-caret-right::before{
  content:"\f0da";
}

.b-fa-comments::before{
  content:"\f086";
}

.b-fa-paste::before{
  content:"\f0ea";
}

.b-fa-file-clipboard::before{
  content:"\f0ea";
}

.b-fa-code-pull-request::before{
  content:"\e13c";
}

.b-fa-clipboard-list::before{
  content:"\f46d";
}

.b-fa-truck-ramp-box::before{
  content:"\f4de";
}

.b-fa-truck-loading::before{
  content:"\f4de";
}

.b-fa-user-check::before{
  content:"\f4fc";
}

.b-fa-vial-virus::before{
  content:"\e597";
}

.b-fa-sheet-plastic::before{
  content:"\e571";
}

.b-fa-blog::before{
  content:"\f781";
}

.b-fa-user-ninja::before{
  content:"\f504";
}

.b-fa-person-arrow-up-from-line::before{
  content:"\e539";
}

.b-fa-scroll-torah::before{
  content:"\f6a0";
}

.b-fa-torah::before{
  content:"\f6a0";
}

.b-fa-broom-ball::before{
  content:"\f458";
}

.b-fa-quidditch::before{
  content:"\f458";
}

.b-fa-quidditch-broom-ball::before{
  content:"\f458";
}

.b-fa-toggle-off::before{
  content:"\f204";
}

.b-fa-box-archive::before{
  content:"\f187";
}

.b-fa-archive::before{
  content:"\f187";
}

.b-fa-person-drowning::before{
  content:"\e545";
}

.b-fa-arrow-down-9-1::before{
  content:"\f886";
}

.b-fa-sort-numeric-desc::before{
  content:"\f886";
}

.b-fa-sort-numeric-down-alt::before{
  content:"\f886";
}

.b-fa-face-grin-tongue-squint::before{
  content:"\f58a";
}

.b-fa-grin-tongue-squint::before{
  content:"\f58a";
}

.b-fa-spray-can::before{
  content:"\f5bd";
}

.b-fa-truck-monster::before{
  content:"\f63b";
}

.b-fa-w::before{
  content:"W";
}

.b-fa-earth-africa::before{
  content:"\f57c";
}

.b-fa-globe-africa::before{
  content:"\f57c";
}

.b-fa-rainbow::before{
  content:"\f75b";
}

.b-fa-circle-notch::before{
  content:"\f1ce";
}

.b-fa-tablet-screen-button::before{
  content:"\f3fa";
}

.b-fa-tablet-alt::before{
  content:"\f3fa";
}

.b-fa-paw::before{
  content:"\f1b0";
}

.b-fa-cloud::before{
  content:"\f0c2";
}

.b-fa-trowel-bricks::before{
  content:"\e58a";
}

.b-fa-face-flushed::before{
  content:"\f579";
}

.b-fa-flushed::before{
  content:"\f579";
}

.b-fa-hospital-user::before{
  content:"\f80d";
}

.b-fa-tent-arrow-left-right::before{
  content:"\e57f";
}

.b-fa-gavel::before{
  content:"\f0e3";
}

.b-fa-legal::before{
  content:"\f0e3";
}

.b-fa-binoculars::before{
  content:"\f1e5";
}

.b-fa-microphone-slash::before{
  content:"\f131";
}

.b-fa-box-tissue::before{
  content:"\e05b";
}

.b-fa-motorcycle::before{
  content:"\f21c";
}

.b-fa-bell-concierge::before{
  content:"\f562";
}

.b-fa-concierge-bell::before{
  content:"\f562";
}

.b-fa-pen-ruler::before{
  content:"\f5ae";
}

.b-fa-pencil-ruler::before{
  content:"\f5ae";
}

.b-fa-people-arrows::before{
  content:"\e068";
}

.b-fa-people-arrows-left-right::before{
  content:"\e068";
}

.b-fa-mars-and-venus-burst::before{
  content:"\e523";
}

.b-fa-square-caret-right::before{
  content:"\f152";
}

.b-fa-caret-square-right::before{
  content:"\f152";
}

.b-fa-scissors::before{
  content:"\f0c4";
}

.b-fa-cut::before{
  content:"\f0c4";
}

.b-fa-sun-plant-wilt::before{
  content:"\e57a";
}

.b-fa-toilets-portable::before{
  content:"\e584";
}

.b-fa-hockey-puck::before{
  content:"\f453";
}

.b-fa-table::before{
  content:"\f0ce";
}

.b-fa-magnifying-glass-arrow-right::before{
  content:"\e521";
}

.b-fa-tachograph-digital::before{
  content:"\f566";
}

.b-fa-digital-tachograph::before{
  content:"\f566";
}

.b-fa-users-slash::before{
  content:"\e073";
}

.b-fa-clover::before{
  content:"\e139";
}

.b-fa-reply::before{
  content:"\f3e5";
}

.b-fa-mail-reply::before{
  content:"\f3e5";
}

.b-fa-star-and-crescent::before{
  content:"\f699";
}

.b-fa-house-fire::before{
  content:"\e50c";
}

.b-fa-square-minus::before{
  content:"\f146";
}

.b-fa-minus-square::before{
  content:"\f146";
}

.b-fa-helicopter::before{
  content:"\f533";
}

.b-fa-compass::before{
  content:"\f14e";
}

.b-fa-square-caret-down::before{
  content:"\f150";
}

.b-fa-caret-square-down::before{
  content:"\f150";
}

.b-fa-file-circle-question::before{
  content:"\e4ef";
}

.b-fa-laptop-code::before{
  content:"\f5fc";
}

.b-fa-swatchbook::before{
  content:"\f5c3";
}

.b-fa-prescription-bottle::before{
  content:"\f485";
}

.b-fa-bars::before{
  content:"\f0c9";
}

.b-fa-navicon::before{
  content:"\f0c9";
}

.b-fa-people-group::before{
  content:"\e533";
}

.b-fa-hourglass-end::before{
  content:"\f253";
}

.b-fa-hourglass-3::before{
  content:"\f253";
}

.b-fa-heart-crack::before{
  content:"\f7a9";
}

.b-fa-heart-broken::before{
  content:"\f7a9";
}

.b-fa-square-up-right::before{
  content:"\f360";
}

.b-fa-external-link-square-alt::before{
  content:"\f360";
}

.b-fa-face-kiss-beam::before{
  content:"\f597";
}

.b-fa-kiss-beam::before{
  content:"\f597";
}

.b-fa-film::before{
  content:"\f008";
}

.b-fa-ruler-horizontal::before{
  content:"\f547";
}

.b-fa-people-robbery::before{
  content:"\e536";
}

.b-fa-lightbulb::before{
  content:"\f0eb";
}

.b-fa-caret-left::before{
  content:"\f0d9";
}

.b-fa-circle-exclamation::before{
  content:"\f06a";
}

.b-fa-exclamation-circle::before{
  content:"\f06a";
}

.b-fa-school-circle-xmark::before{
  content:"\e56d";
}

.b-fa-arrow-right-from-bracket::before{
  content:"\f08b";
}

.b-fa-sign-out::before{
  content:"\f08b";
}

.b-fa-circle-chevron-down::before{
  content:"\f13a";
}

.b-fa-chevron-circle-down::before{
  content:"\f13a";
}

.b-fa-unlock-keyhole::before{
  content:"\f13e";
}

.b-fa-unlock-alt::before{
  content:"\f13e";
}

.b-fa-cloud-showers-heavy::before{
  content:"\f740";
}

.b-fa-headphones-simple::before{
  content:"\f58f";
}

.b-fa-headphones-alt::before{
  content:"\f58f";
}

.b-fa-sitemap::before{
  content:"\f0e8";
}

.b-fa-circle-dollar-to-slot::before{
  content:"\f4b9";
}

.b-fa-donate::before{
  content:"\f4b9";
}

.b-fa-memory::before{
  content:"\f538";
}

.b-fa-road-spikes::before{
  content:"\e568";
}

.b-fa-fire-burner::before{
  content:"\e4f1";
}

.b-fa-flag::before{
  content:"\f024";
}

.b-fa-hanukiah::before{
  content:"\f6e6";
}

.b-fa-feather::before{
  content:"\f52d";
}

.b-fa-volume-low::before{
  content:"\f027";
}

.b-fa-volume-down::before{
  content:"\f027";
}

.b-fa-comment-slash::before{
  content:"\f4b3";
}

.b-fa-cloud-sun-rain::before{
  content:"\f743";
}

.b-fa-compress::before{
  content:"\f066";
}

.b-fa-wheat-awn::before{
  content:"\e2cd";
}

.b-fa-wheat-alt::before{
  content:"\e2cd";
}

.b-fa-ankh::before{
  content:"\f644";
}

.b-fa-hands-holding-child::before{
  content:"\e4fa";
}

.b-fa-asterisk::before{
  content:"\*";
}

.b-fa-square-check::before{
  content:"\f14a";
}

.b-fa-check-square::before{
  content:"\f14a";
}

.b-fa-peseta-sign::before{
  content:"\e221";
}

.b-fa-heading::before{
  content:"\f1dc";
}

.b-fa-header::before{
  content:"\f1dc";
}

.b-fa-ghost::before{
  content:"\f6e2";
}

.b-fa-list::before{
  content:"\f03a";
}

.b-fa-list-squares::before{
  content:"\f03a";
}

.b-fa-square-phone-flip::before{
  content:"\f87b";
}

.b-fa-phone-square-alt::before{
  content:"\f87b";
}

.b-fa-cart-plus::before{
  content:"\f217";
}

.b-fa-gamepad::before{
  content:"\f11b";
}

.b-fa-circle-dot::before{
  content:"\f192";
}

.b-fa-dot-circle::before{
  content:"\f192";
}

.b-fa-face-dizzy::before{
  content:"\f567";
}

.b-fa-dizzy::before{
  content:"\f567";
}

.b-fa-egg::before{
  content:"\f7fb";
}

.b-fa-house-medical-circle-xmark::before{
  content:"\e513";
}

.b-fa-campground::before{
  content:"\f6bb";
}

.b-fa-folder-plus::before{
  content:"\f65e";
}

.b-fa-futbol::before{
  content:"\f1e3";
}

.b-fa-futbol-ball::before{
  content:"\f1e3";
}

.b-fa-soccer-ball::before{
  content:"\f1e3";
}

.b-fa-paintbrush::before{
  content:"\f1fc";
}

.b-fa-paint-brush::before{
  content:"\f1fc";
}

.b-fa-lock::before{
  content:"\f023";
}

.b-fa-gas-pump::before{
  content:"\f52f";
}

.b-fa-hot-tub-person::before{
  content:"\f593";
}

.b-fa-hot-tub::before{
  content:"\f593";
}

.b-fa-map-location::before{
  content:"\f59f";
}

.b-fa-map-marked::before{
  content:"\f59f";
}

.b-fa-house-flood-water::before{
  content:"\e50e";
}

.b-fa-tree::before{
  content:"\f1bb";
}

.b-fa-bridge-lock::before{
  content:"\e4cc";
}

.b-fa-sack-dollar::before{
  content:"\f81d";
}

.b-fa-pen-to-square::before{
  content:"\f044";
}

.b-fa-edit::before{
  content:"\f044";
}

.b-fa-car-side::before{
  content:"\f5e4";
}

.b-fa-share-nodes::before{
  content:"\f1e0";
}

.b-fa-share-alt::before{
  content:"\f1e0";
}

.b-fa-heart-circle-minus::before{
  content:"\e4ff";
}

.b-fa-hourglass-half::before{
  content:"\f252";
}

.b-fa-hourglass-2::before{
  content:"\f252";
}

.b-fa-microscope::before{
  content:"\f610";
}

.b-fa-sink::before{
  content:"\e06d";
}

.b-fa-bag-shopping::before{
  content:"\f290";
}

.b-fa-shopping-bag::before{
  content:"\f290";
}

.b-fa-arrow-down-z-a::before{
  content:"\f881";
}

.b-fa-sort-alpha-desc::before{
  content:"\f881";
}

.b-fa-sort-alpha-down-alt::before{
  content:"\f881";
}

.b-fa-mitten::before{
  content:"\f7b5";
}

.b-fa-person-rays::before{
  content:"\e54d";
}

.b-fa-users::before{
  content:"\f0c0";
}

.b-fa-eye-slash::before{
  content:"\f070";
}

.b-fa-flask-vial::before{
  content:"\e4f3";
}

.b-fa-hand::before{
  content:"\f256";
}

.b-fa-hand-paper::before{
  content:"\f256";
}

.b-fa-om::before{
  content:"\f679";
}

.b-fa-worm::before{
  content:"\e599";
}

.b-fa-house-circle-xmark::before{
  content:"\e50b";
}

.b-fa-plug::before{
  content:"\f1e6";
}

.b-fa-chevron-up::before{
  content:"\f077";
}

.b-fa-hand-spock::before{
  content:"\f259";
}

.b-fa-stopwatch::before{
  content:"\f2f2";
}

.b-fa-face-kiss::before{
  content:"\f596";
}

.b-fa-kiss::before{
  content:"\f596";
}

.b-fa-bridge-circle-xmark::before{
  content:"\e4cb";
}

.b-fa-face-grin-tongue::before{
  content:"\f589";
}

.b-fa-grin-tongue::before{
  content:"\f589";
}

.b-fa-chess-bishop::before{
  content:"\f43a";
}

.b-fa-face-grin-wink::before{
  content:"\f58c";
}

.b-fa-grin-wink::before{
  content:"\f58c";
}

.b-fa-ear-deaf::before{
  content:"\f2a4";
}

.b-fa-deaf::before{
  content:"\f2a4";
}

.b-fa-deafness::before{
  content:"\f2a4";
}

.b-fa-hard-of-hearing::before{
  content:"\f2a4";
}

.b-fa-road-circle-check::before{
  content:"\e564";
}

.b-fa-dice-five::before{
  content:"\f523";
}

.b-fa-square-rss::before{
  content:"\f143";
}

.b-fa-rss-square::before{
  content:"\f143";
}

.b-fa-land-mine-on::before{
  content:"\e51b";
}

.b-fa-i-cursor::before{
  content:"\f246";
}

.b-fa-stamp::before{
  content:"\f5bf";
}

.b-fa-stairs::before{
  content:"\e289";
}

.b-fa-i::before{
  content:"I";
}

.b-fa-hryvnia-sign::before{
  content:"\f6f2";
}

.b-fa-hryvnia::before{
  content:"\f6f2";
}

.b-fa-pills::before{
  content:"\f484";
}

.b-fa-face-grin-wide::before{
  content:"\f581";
}

.b-fa-grin-alt::before{
  content:"\f581";
}

.b-fa-tooth::before{
  content:"\f5c9";
}

.b-fa-v::before{
  content:"V";
}

.b-fa-bangladeshi-taka-sign::before{
  content:"\e2e6";
}

.b-fa-bicycle::before{
  content:"\f206";
}

.b-fa-staff-snake::before{
  content:"\e579";
}

.b-fa-rod-asclepius::before{
  content:"\e579";
}

.b-fa-rod-snake::before{
  content:"\e579";
}

.b-fa-staff-aesculapius::before{
  content:"\e579";
}

.b-fa-head-side-cough-slash::before{
  content:"\e062";
}

.b-fa-truck-medical::before{
  content:"\f0f9";
}

.b-fa-ambulance::before{
  content:"\f0f9";
}

.b-fa-wheat-awn-circle-exclamation::before{
  content:"\e598";
}

.b-fa-snowman::before{
  content:"\f7d0";
}

.b-fa-mortar-pestle::before{
  content:"\f5a7";
}

.b-fa-road-barrier::before{
  content:"\e562";
}

.b-fa-school::before{
  content:"\f549";
}

.b-fa-igloo::before{
  content:"\f7ae";
}

.b-fa-joint::before{
  content:"\f595";
}

.b-fa-angle-right::before{
  content:"\f105";
}

.b-fa-horse::before{
  content:"\f6f0";
}

.b-fa-q::before{
  content:"Q";
}

.b-fa-g::before{
  content:"G";
}

.b-fa-notes-medical::before{
  content:"\f481";
}

.b-fa-temperature-half::before{
  content:"\f2c9";
}

.b-fa-temperature-2::before{
  content:"\f2c9";
}

.b-fa-thermometer-2::before{
  content:"\f2c9";
}

.b-fa-thermometer-half::before{
  content:"\f2c9";
}

.b-fa-dong-sign::before{
  content:"\e169";
}

.b-fa-capsules::before{
  content:"\f46b";
}

.b-fa-poo-storm::before{
  content:"\f75a";
}

.b-fa-poo-bolt::before{
  content:"\f75a";
}

.b-fa-face-frown-open::before{
  content:"\f57a";
}

.b-fa-frown-open::before{
  content:"\f57a";
}

.b-fa-hand-point-up::before{
  content:"\f0a6";
}

.b-fa-money-bill::before{
  content:"\f0d6";
}

.b-fa-bookmark::before{
  content:"\f02e";
}

.b-fa-align-justify::before{
  content:"\f039";
}

.b-fa-umbrella-beach::before{
  content:"\f5ca";
}

.b-fa-helmet-un::before{
  content:"\e503";
}

.b-fa-bullseye::before{
  content:"\f140";
}

.b-fa-bacon::before{
  content:"\f7e5";
}

.b-fa-hand-point-down::before{
  content:"\f0a7";
}

.b-fa-arrow-up-from-bracket::before{
  content:"\e09a";
}

.b-fa-folder::before{
  content:"\f07b";
}

.b-fa-folder-blank::before{
  content:"\f07b";
}

.b-fa-file-waveform::before{
  content:"\f478";
}

.b-fa-file-medical-alt::before{
  content:"\f478";
}

.b-fa-radiation::before{
  content:"\f7b9";
}

.b-fa-chart-simple::before{
  content:"\e473";
}

.b-fa-mars-stroke::before{
  content:"\f229";
}

.b-fa-vial::before{
  content:"\f492";
}

.b-fa-gauge::before{
  content:"\f624";
}

.b-fa-dashboard::before{
  content:"\f624";
}

.b-fa-gauge-med::before{
  content:"\f624";
}

.b-fa-tachometer-alt-average::before{
  content:"\f624";
}

.b-fa-wand-magic-sparkles::before{
  content:"\e2ca";
}

.b-fa-magic-wand-sparkles::before{
  content:"\e2ca";
}

.b-fa-e::before{
  content:"E";
}

.b-fa-pen-clip::before{
  content:"\f305";
}

.b-fa-pen-alt::before{
  content:"\f305";
}

.b-fa-bridge-circle-exclamation::before{
  content:"\e4ca";
}

.b-fa-user::before{
  content:"\f007";
}

.b-fa-school-circle-check::before{
  content:"\e56b";
}

.b-fa-dumpster::before{
  content:"\f793";
}

.b-fa-van-shuttle::before{
  content:"\f5b6";
}

.b-fa-shuttle-van::before{
  content:"\f5b6";
}

.b-fa-building-user::before{
  content:"\e4da";
}

.b-fa-square-caret-left::before{
  content:"\f191";
}

.b-fa-caret-square-left::before{
  content:"\f191";
}

.b-fa-highlighter::before{
  content:"\f591";
}

.b-fa-key::before{
  content:"\f084";
}

.b-fa-bullhorn::before{
  content:"\f0a1";
}

.b-fa-globe::before{
  content:"\f0ac";
}

.b-fa-synagogue::before{
  content:"\f69b";
}

.b-fa-person-half-dress::before{
  content:"\e548";
}

.b-fa-road-bridge::before{
  content:"\e563";
}

.b-fa-location-arrow::before{
  content:"\f124";
}

.b-fa-c::before{
  content:"C";
}

.b-fa-tablet-button::before{
  content:"\f10a";
}

.b-fa-building-lock::before{
  content:"\e4d6";
}

.b-fa-pizza-slice::before{
  content:"\f818";
}

.b-fa-money-bill-wave::before{
  content:"\f53a";
}

.b-fa-chart-area::before{
  content:"\f1fe";
}

.b-fa-area-chart::before{
  content:"\f1fe";
}

.b-fa-house-flag::before{
  content:"\e50d";
}

.b-fa-person-circle-minus::before{
  content:"\e540";
}

.b-fa-ban::before{
  content:"\f05e";
}

.b-fa-cancel::before{
  content:"\f05e";
}

.b-fa-camera-rotate::before{
  content:"\e0d8";
}

.b-fa-spray-can-sparkles::before{
  content:"\f5d0";
}

.b-fa-air-freshener::before{
  content:"\f5d0";
}

.b-fa-star::before{
  content:"\f005";
}

.b-fa-repeat::before{
  content:"\f363";
}

.b-fa-cross::before{
  content:"\f654";
}

.b-fa-box::before{
  content:"\f466";
}

.b-fa-venus-mars::before{
  content:"\f228";
}

.b-fa-arrow-pointer::before{
  content:"\f245";
}

.b-fa-mouse-pointer::before{
  content:"\f245";
}

.b-fa-maximize::before{
  content:"\f31e";
}

.b-fa-expand-arrows-alt::before{
  content:"\f31e";
}

.b-fa-charging-station::before{
  content:"\f5e7";
}

.b-fa-shapes::before{
  content:"\f61f";
}

.b-fa-triangle-circle-square::before{
  content:"\f61f";
}

.b-fa-shuffle::before{
  content:"\f074";
}

.b-fa-random::before{
  content:"\f074";
}

.b-fa-person-running::before{
  content:"\f70c";
}

.b-fa-running::before{
  content:"\f70c";
}

.b-fa-mobile-retro::before{
  content:"\e527";
}

.b-fa-grip-lines-vertical::before{
  content:"\f7a5";
}

.b-fa-spider::before{
  content:"\f717";
}

.b-fa-hands-bound::before{
  content:"\e4f9";
}

.b-fa-file-invoice-dollar::before{
  content:"\f571";
}

.b-fa-plane-circle-exclamation::before{
  content:"\e556";
}

.b-fa-x-ray::before{
  content:"\f497";
}

.b-fa-spell-check::before{
  content:"\f891";
}

.b-fa-slash::before{
  content:"\f715";
}

.b-fa-computer-mouse::before{
  content:"\f8cc";
}

.b-fa-mouse::before{
  content:"\f8cc";
}

.b-fa-arrow-right-to-bracket::before{
  content:"\f090";
}

.b-fa-sign-in::before{
  content:"\f090";
}

.b-fa-shop-slash::before{
  content:"\e070";
}

.b-fa-store-alt-slash::before{
  content:"\e070";
}

.b-fa-server::before{
  content:"\f233";
}

.b-fa-virus-covid-slash::before{
  content:"\e4a9";
}

.b-fa-shop-lock::before{
  content:"\e4a5";
}

.b-fa-hourglass-start::before{
  content:"\f251";
}

.b-fa-hourglass-1::before{
  content:"\f251";
}

.b-fa-blender-phone::before{
  content:"\f6b6";
}

.b-fa-building-wheat::before{
  content:"\e4db";
}

.b-fa-person-breastfeeding::before{
  content:"\e53a";
}

.b-fa-right-to-bracket::before{
  content:"\f2f6";
}

.b-fa-sign-in-alt::before{
  content:"\f2f6";
}

.b-fa-venus::before{
  content:"\f221";
}

.b-fa-passport::before{
  content:"\f5ab";
}

.b-fa-thumbtack-slash::before{
  content:"\e68f";
}

.b-fa-thumb-tack-slash::before{
  content:"\e68f";
}

.b-fa-heart-pulse::before{
  content:"\f21e";
}

.b-fa-heartbeat::before{
  content:"\f21e";
}

.b-fa-people-carry-box::before{
  content:"\f4ce";
}

.b-fa-people-carry::before{
  content:"\f4ce";
}

.b-fa-temperature-high::before{
  content:"\f769";
}

.b-fa-microchip::before{
  content:"\f2db";
}

.b-fa-crown::before{
  content:"\f521";
}

.b-fa-weight-hanging::before{
  content:"\f5cd";
}

.b-fa-xmarks-lines::before{
  content:"\e59a";
}

.b-fa-file-prescription::before{
  content:"\f572";
}

.b-fa-weight-scale::before{
  content:"\f496";
}

.b-fa-weight::before{
  content:"\f496";
}

.b-fa-user-group::before{
  content:"\f500";
}

.b-fa-user-friends::before{
  content:"\f500";
}

.b-fa-arrow-up-a-z::before{
  content:"\f15e";
}

.b-fa-sort-alpha-up::before{
  content:"\f15e";
}

.b-fa-chess-knight::before{
  content:"\f441";
}

.b-fa-face-laugh-squint::before{
  content:"\f59b";
}

.b-fa-laugh-squint::before{
  content:"\f59b";
}

.b-fa-wheelchair::before{
  content:"\f193";
}

.b-fa-circle-arrow-up::before{
  content:"\f0aa";
}

.b-fa-arrow-circle-up::before{
  content:"\f0aa";
}

.b-fa-toggle-on::before{
  content:"\f205";
}

.b-fa-person-walking::before{
  content:"\f554";
}

.b-fa-walking::before{
  content:"\f554";
}

.b-fa-l::before{
  content:"L";
}

.b-fa-fire::before{
  content:"\f06d";
}

.b-fa-bed-pulse::before{
  content:"\f487";
}

.b-fa-procedures::before{
  content:"\f487";
}

.b-fa-shuttle-space::before{
  content:"\f197";
}

.b-fa-space-shuttle::before{
  content:"\f197";
}

.b-fa-face-laugh::before{
  content:"\f599";
}

.b-fa-laugh::before{
  content:"\f599";
}

.b-fa-folder-open::before{
  content:"\f07c";
}

.b-fa-heart-circle-plus::before{
  content:"\e500";
}

.b-fa-code-fork::before{
  content:"\e13b";
}

.b-fa-city::before{
  content:"\f64f";
}

.b-fa-microphone-lines::before{
  content:"\f3c9";
}

.b-fa-microphone-alt::before{
  content:"\f3c9";
}

.b-fa-pepper-hot::before{
  content:"\f816";
}

.b-fa-unlock::before{
  content:"\f09c";
}

.b-fa-colon-sign::before{
  content:"\e140";
}

.b-fa-headset::before{
  content:"\f590";
}

.b-fa-store-slash::before{
  content:"\e071";
}

.b-fa-road-circle-xmark::before{
  content:"\e566";
}

.b-fa-user-minus::before{
  content:"\f503";
}

.b-fa-mars-stroke-up::before{
  content:"\f22a";
}

.b-fa-mars-stroke-v::before{
  content:"\f22a";
}

.b-fa-champagne-glasses::before{
  content:"\f79f";
}

.b-fa-glass-cheers::before{
  content:"\f79f";
}

.b-fa-clipboard::before{
  content:"\f328";
}

.b-fa-house-circle-exclamation::before{
  content:"\e50a";
}

.b-fa-file-arrow-up::before{
  content:"\f574";
}

.b-fa-file-upload::before{
  content:"\f574";
}

.b-fa-wifi::before{
  content:"\f1eb";
}

.b-fa-wifi-3::before{
  content:"\f1eb";
}

.b-fa-wifi-strong::before{
  content:"\f1eb";
}

.b-fa-bath::before{
  content:"\f2cd";
}

.b-fa-bathtub::before{
  content:"\f2cd";
}

.b-fa-underline::before{
  content:"\f0cd";
}

.b-fa-user-pen::before{
  content:"\f4ff";
}

.b-fa-user-edit::before{
  content:"\f4ff";
}

.b-fa-signature::before{
  content:"\f5b7";
}

.b-fa-stroopwafel::before{
  content:"\f551";
}

.b-fa-bold::before{
  content:"\f032";
}

.b-fa-anchor-lock::before{
  content:"\e4ad";
}

.b-fa-building-ngo::before{
  content:"\e4d7";
}

.b-fa-manat-sign::before{
  content:"\e1d5";
}

.b-fa-not-equal::before{
  content:"\f53e";
}

.b-fa-border-top-left::before{
  content:"\f853";
}

.b-fa-border-style::before{
  content:"\f853";
}

.b-fa-map-location-dot::before{
  content:"\f5a0";
}

.b-fa-map-marked-alt::before{
  content:"\f5a0";
}

.b-fa-jedi::before{
  content:"\f669";
}

.b-fa-square-poll-vertical::before{
  content:"\f681";
}

.b-fa-poll::before{
  content:"\f681";
}

.b-fa-mug-hot::before{
  content:"\f7b6";
}

.b-fa-car-battery::before{
  content:"\f5df";
}

.b-fa-battery-car::before{
  content:"\f5df";
}

.b-fa-gift::before{
  content:"\f06b";
}

.b-fa-dice-two::before{
  content:"\f528";
}

.b-fa-chess-queen::before{
  content:"\f445";
}

.b-fa-glasses::before{
  content:"\f530";
}

.b-fa-chess-board::before{
  content:"\f43c";
}

.b-fa-building-circle-check::before{
  content:"\e4d2";
}

.b-fa-person-chalkboard::before{
  content:"\e53d";
}

.b-fa-mars-stroke-right::before{
  content:"\f22b";
}

.b-fa-mars-stroke-h::before{
  content:"\f22b";
}

.b-fa-hand-back-fist::before{
  content:"\f255";
}

.b-fa-hand-rock::before{
  content:"\f255";
}

.b-fa-square-caret-up::before{
  content:"\f151";
}

.b-fa-caret-square-up::before{
  content:"\f151";
}

.b-fa-cloud-showers-water::before{
  content:"\e4e4";
}

.b-fa-chart-bar::before{
  content:"\f080";
}

.b-fa-bar-chart::before{
  content:"\f080";
}

.b-fa-hands-bubbles::before{
  content:"\e05e";
}

.b-fa-hands-wash::before{
  content:"\e05e";
}

.b-fa-less-than-equal::before{
  content:"\f537";
}

.b-fa-train::before{
  content:"\f238";
}

.b-fa-eye-low-vision::before{
  content:"\f2a8";
}

.b-fa-low-vision::before{
  content:"\f2a8";
}

.b-fa-crow::before{
  content:"\f520";
}

.b-fa-sailboat::before{
  content:"\e445";
}

.b-fa-window-restore::before{
  content:"\f2d2";
}

.b-fa-square-plus::before{
  content:"\f0fe";
}

.b-fa-plus-square::before{
  content:"\f0fe";
}

.b-fa-torii-gate::before{
  content:"\f6a1";
}

.b-fa-frog::before{
  content:"\f52e";
}

.b-fa-bucket::before{
  content:"\e4cf";
}

.b-fa-image::before{
  content:"\f03e";
}

.b-fa-microphone::before{
  content:"\f130";
}

.b-fa-cow::before{
  content:"\f6c8";
}

.b-fa-caret-up::before{
  content:"\f0d8";
}

.b-fa-screwdriver::before{
  content:"\f54a";
}

.b-fa-folder-closed::before{
  content:"\e185";
}

.b-fa-house-tsunami::before{
  content:"\e515";
}

.b-fa-square-nfi::before{
  content:"\e576";
}

.b-fa-arrow-up-from-ground-water::before{
  content:"\e4b5";
}

.b-fa-martini-glass::before{
  content:"\f57b";
}

.b-fa-glass-martini-alt::before{
  content:"\f57b";
}

.b-fa-rotate-left::before{
  content:"\f2ea";
}

.b-fa-rotate-back::before{
  content:"\f2ea";
}

.b-fa-rotate-backward::before{
  content:"\f2ea";
}

.b-fa-undo-alt::before{
  content:"\f2ea";
}

.b-fa-table-columns::before{
  content:"\f0db";
}

.b-fa-columns::before{
  content:"\f0db";
}

.b-fa-lemon::before{
  content:"\f094";
}

.b-fa-head-side-mask::before{
  content:"\e063";
}

.b-fa-handshake::before{
  content:"\f2b5";
}

.b-fa-gem::before{
  content:"\f3a5";
}

.b-fa-dolly::before{
  content:"\f472";
}

.b-fa-dolly-box::before{
  content:"\f472";
}

.b-fa-smoking::before{
  content:"\f48d";
}

.b-fa-minimize::before{
  content:"\f78c";
}

.b-fa-compress-arrows-alt::before{
  content:"\f78c";
}

.b-fa-monument::before{
  content:"\f5a6";
}

.b-fa-snowplow::before{
  content:"\f7d2";
}

.b-fa-angles-right::before{
  content:"\f101";
}

.b-fa-angle-double-right::before{
  content:"\f101";
}

.b-fa-cannabis::before{
  content:"\f55f";
}

.b-fa-circle-play::before{
  content:"\f144";
}

.b-fa-play-circle::before{
  content:"\f144";
}

.b-fa-tablets::before{
  content:"\f490";
}

.b-fa-ethernet::before{
  content:"\f796";
}

.b-fa-euro-sign::before{
  content:"\f153";
}

.b-fa-eur::before{
  content:"\f153";
}

.b-fa-euro::before{
  content:"\f153";
}

.b-fa-chair::before{
  content:"\f6c0";
}

.b-fa-circle-check::before{
  content:"\f058";
}

.b-fa-check-circle::before{
  content:"\f058";
}

.b-fa-circle-stop::before{
  content:"\f28d";
}

.b-fa-stop-circle::before{
  content:"\f28d";
}

.b-fa-compass-drafting::before{
  content:"\f568";
}

.b-fa-drafting-compass::before{
  content:"\f568";
}

.b-fa-plate-wheat::before{
  content:"\e55a";
}

.b-fa-icicles::before{
  content:"\f7ad";
}

.b-fa-person-shelter::before{
  content:"\e54f";
}

.b-fa-neuter::before{
  content:"\f22c";
}

.b-fa-id-badge::before{
  content:"\f2c1";
}

.b-fa-marker::before{
  content:"\f5a1";
}

.b-fa-face-laugh-beam::before{
  content:"\f59a";
}

.b-fa-laugh-beam::before{
  content:"\f59a";
}

.b-fa-helicopter-symbol::before{
  content:"\e502";
}

.b-fa-universal-access::before{
  content:"\f29a";
}

.b-fa-circle-chevron-up::before{
  content:"\f139";
}

.b-fa-chevron-circle-up::before{
  content:"\f139";
}

.b-fa-lari-sign::before{
  content:"\e1c8";
}

.b-fa-volcano::before{
  content:"\f770";
}

.b-fa-person-walking-dashed-line-arrow-right::before{
  content:"\e553";
}

.b-fa-sterling-sign::before{
  content:"\f154";
}

.b-fa-gbp::before{
  content:"\f154";
}

.b-fa-pound-sign::before{
  content:"\f154";
}

.b-fa-viruses::before{
  content:"\e076";
}

.b-fa-square-person-confined::before{
  content:"\e577";
}

.b-fa-user-tie::before{
  content:"\f508";
}

.b-fa-arrow-down-long::before{
  content:"\f175";
}

.b-fa-long-arrow-down::before{
  content:"\f175";
}

.b-fa-tent-arrow-down-to-line::before{
  content:"\e57e";
}

.b-fa-certificate::before{
  content:"\f0a3";
}

.b-fa-reply-all::before{
  content:"\f122";
}

.b-fa-mail-reply-all::before{
  content:"\f122";
}

.b-fa-suitcase::before{
  content:"\f0f2";
}

.b-fa-person-skating::before{
  content:"\f7c5";
}

.b-fa-skating::before{
  content:"\f7c5";
}

.b-fa-filter-circle-dollar::before{
  content:"\f662";
}

.b-fa-funnel-dollar::before{
  content:"\f662";
}

.b-fa-camera-retro::before{
  content:"\f083";
}

.b-fa-circle-arrow-down::before{
  content:"\f0ab";
}

.b-fa-arrow-circle-down::before{
  content:"\f0ab";
}

.b-fa-file-import::before{
  content:"\f56f";
}

.b-fa-arrow-right-to-file::before{
  content:"\f56f";
}

.b-fa-square-arrow-up-right::before{
  content:"\f14c";
}

.b-fa-external-link-square::before{
  content:"\f14c";
}

.b-fa-box-open::before{
  content:"\f49e";
}

.b-fa-scroll::before{
  content:"\f70e";
}

.b-fa-spa::before{
  content:"\f5bb";
}

.b-fa-location-pin-lock::before{
  content:"\e51f";
}

.b-fa-pause::before{
  content:"\f04c";
}

.b-fa-hill-avalanche::before{
  content:"\e507";
}

.b-fa-temperature-empty::before{
  content:"\f2cb";
}

.b-fa-temperature-0::before{
  content:"\f2cb";
}

.b-fa-thermometer-0::before{
  content:"\f2cb";
}

.b-fa-thermometer-empty::before{
  content:"\f2cb";
}

.b-fa-bomb::before{
  content:"\f1e2";
}

.b-fa-registered::before{
  content:"\f25d";
}

.b-fa-address-card::before{
  content:"\f2bb";
}

.b-fa-contact-card::before{
  content:"\f2bb";
}

.b-fa-vcard::before{
  content:"\f2bb";
}

.b-fa-scale-unbalanced-flip::before{
  content:"\f516";
}

.b-fa-balance-scale-right::before{
  content:"\f516";
}

.b-fa-subscript::before{
  content:"\f12c";
}

.b-fa-diamond-turn-right::before{
  content:"\f5eb";
}

.b-fa-directions::before{
  content:"\f5eb";
}

.b-fa-burst::before{
  content:"\e4dc";
}

.b-fa-house-laptop::before{
  content:"\e066";
}

.b-fa-laptop-house::before{
  content:"\e066";
}

.b-fa-face-tired::before{
  content:"\f5c8";
}

.b-fa-tired::before{
  content:"\f5c8";
}

.b-fa-money-bills::before{
  content:"\e1f3";
}

.b-fa-smog::before{
  content:"\f75f";
}

.b-fa-crutch::before{
  content:"\f7f7";
}

.b-fa-cloud-arrow-up::before{
  content:"\f0ee";
}

.b-fa-cloud-upload::before{
  content:"\f0ee";
}

.b-fa-cloud-upload-alt::before{
  content:"\f0ee";
}

.b-fa-palette::before{
  content:"\f53f";
}

.b-fa-arrows-turn-right::before{
  content:"\e4c0";
}

.b-fa-vest::before{
  content:"\e085";
}

.b-fa-ferry::before{
  content:"\e4ea";
}

.b-fa-arrows-down-to-people::before{
  content:"\e4b9";
}

.b-fa-seedling::before{
  content:"\f4d8";
}

.b-fa-sprout::before{
  content:"\f4d8";
}

.b-fa-left-right::before{
  content:"\f337";
}

.b-fa-arrows-alt-h::before{
  content:"\f337";
}

.b-fa-boxes-packing::before{
  content:"\e4c7";
}

.b-fa-circle-arrow-left::before{
  content:"\f0a8";
}

.b-fa-arrow-circle-left::before{
  content:"\f0a8";
}

.b-fa-group-arrows-rotate::before{
  content:"\e4f6";
}

.b-fa-bowl-food::before{
  content:"\e4c6";
}

.b-fa-candy-cane::before{
  content:"\f786";
}

.b-fa-arrow-down-wide-short::before{
  content:"\f160";
}

.b-fa-sort-amount-asc::before{
  content:"\f160";
}

.b-fa-sort-amount-down::before{
  content:"\f160";
}

.b-fa-cloud-bolt::before{
  content:"\f76c";
}

.b-fa-thunderstorm::before{
  content:"\f76c";
}

.b-fa-text-slash::before{
  content:"\f87d";
}

.b-fa-remove-format::before{
  content:"\f87d";
}

.b-fa-face-smile-wink::before{
  content:"\f4da";
}

.b-fa-smile-wink::before{
  content:"\f4da";
}

.b-fa-file-word::before{
  content:"\f1c2";
}

.b-fa-file-powerpoint::before{
  content:"\f1c4";
}

.b-fa-arrows-left-right::before{
  content:"\f07e";
}

.b-fa-arrows-h::before{
  content:"\f07e";
}

.b-fa-house-lock::before{
  content:"\e510";
}

.b-fa-cloud-arrow-down::before{
  content:"\f0ed";
}

.b-fa-cloud-download::before{
  content:"\f0ed";
}

.b-fa-cloud-download-alt::before{
  content:"\f0ed";
}

.b-fa-children::before{
  content:"\e4e1";
}

.b-fa-chalkboard::before{
  content:"\f51b";
}

.b-fa-blackboard::before{
  content:"\f51b";
}

.b-fa-user-large-slash::before{
  content:"\f4fa";
}

.b-fa-user-alt-slash::before{
  content:"\f4fa";
}

.b-fa-envelope-open::before{
  content:"\f2b6";
}

.b-fa-handshake-simple-slash::before{
  content:"\e05f";
}

.b-fa-handshake-alt-slash::before{
  content:"\e05f";
}

.b-fa-mattress-pillow::before{
  content:"\e525";
}

.b-fa-guarani-sign::before{
  content:"\e19a";
}

.b-fa-arrows-rotate::before{
  content:"\f021";
}

.b-fa-refresh::before{
  content:"\f021";
}

.b-fa-sync::before{
  content:"\f021";
}

.b-fa-fire-extinguisher::before{
  content:"\f134";
}

.b-fa-cruzeiro-sign::before{
  content:"\e152";
}

.b-fa-greater-than-equal::before{
  content:"\f532";
}

.b-fa-shield-halved::before{
  content:"\f3ed";
}

.b-fa-shield-alt::before{
  content:"\f3ed";
}

.b-fa-book-atlas::before{
  content:"\f558";
}

.b-fa-atlas::before{
  content:"\f558";
}

.b-fa-virus::before{
  content:"\e074";
}

.b-fa-envelope-circle-check::before{
  content:"\e4e8";
}

.b-fa-layer-group::before{
  content:"\f5fd";
}

.b-fa-arrows-to-dot::before{
  content:"\e4be";
}

.b-fa-archway::before{
  content:"\f557";
}

.b-fa-heart-circle-check::before{
  content:"\e4fd";
}

.b-fa-house-chimney-crack::before{
  content:"\f6f1";
}

.b-fa-house-damage::before{
  content:"\f6f1";
}

.b-fa-file-zipper::before{
  content:"\f1c6";
}

.b-fa-file-archive::before{
  content:"\f1c6";
}

.b-fa-square::before{
  content:"\f0c8";
}

.b-fa-martini-glass-empty::before{
  content:"\f000";
}

.b-fa-glass-martini::before{
  content:"\f000";
}

.b-fa-couch::before{
  content:"\f4b8";
}

.b-fa-cedi-sign::before{
  content:"\e0df";
}

.b-fa-italic::before{
  content:"\f033";
}

.b-fa-table-cells-column-lock::before{
  content:"\e678";
}

.b-fa-church::before{
  content:"\f51d";
}

.b-fa-comments-dollar::before{
  content:"\f653";
}

.b-fa-democrat::before{
  content:"\f747";
}

.b-fa-z::before{
  content:"Z";
}

.b-fa-person-skiing::before{
  content:"\f7c9";
}

.b-fa-skiing::before{
  content:"\f7c9";
}

.b-fa-road-lock::before{
  content:"\e567";
}

.b-fa-a::before{
  content:"A";
}

.b-fa-temperature-arrow-down::before{
  content:"\e03f";
}

.b-fa-temperature-down::before{
  content:"\e03f";
}

.b-fa-feather-pointed::before{
  content:"\f56b";
}

.b-fa-feather-alt::before{
  content:"\f56b";
}

.b-fa-p::before{
  content:"P";
}

.b-fa-snowflake::before{
  content:"\f2dc";
}

.b-fa-newspaper::before{
  content:"\f1ea";
}

.b-fa-rectangle-ad::before{
  content:"\f641";
}

.b-fa-ad::before{
  content:"\f641";
}

.b-fa-circle-arrow-right::before{
  content:"\f0a9";
}

.b-fa-arrow-circle-right::before{
  content:"\f0a9";
}

.b-fa-filter-circle-xmark::before{
  content:"\e17b";
}

.b-fa-locust::before{
  content:"\e520";
}

.b-fa-sort::before{
  content:"\f0dc";
}

.b-fa-unsorted::before{
  content:"\f0dc";
}

.b-fa-list-ol::before{
  content:"\f0cb";
}

.b-fa-list-1-2::before{
  content:"\f0cb";
}

.b-fa-list-numeric::before{
  content:"\f0cb";
}

.b-fa-person-dress-burst::before{
  content:"\e544";
}

.b-fa-money-check-dollar::before{
  content:"\f53d";
}

.b-fa-money-check-alt::before{
  content:"\f53d";
}

.b-fa-vector-square::before{
  content:"\f5cb";
}

.b-fa-bread-slice::before{
  content:"\f7ec";
}

.b-fa-language::before{
  content:"\f1ab";
}

.b-fa-face-kiss-wink-heart::before{
  content:"\f598";
}

.b-fa-kiss-wink-heart::before{
  content:"\f598";
}

.b-fa-filter::before{
  content:"\f0b0";
}

.b-fa-question::before{
  content:"\?";
}

.b-fa-file-signature::before{
  content:"\f573";
}

.b-fa-up-down-left-right::before{
  content:"\f0b2";
}

.b-fa-arrows-alt::before{
  content:"\f0b2";
}

.b-fa-house-chimney-user::before{
  content:"\e065";
}

.b-fa-hand-holding-heart::before{
  content:"\f4be";
}

.b-fa-puzzle-piece::before{
  content:"\f12e";
}

.b-fa-money-check::before{
  content:"\f53c";
}

.b-fa-star-half-stroke::before{
  content:"\f5c0";
}

.b-fa-star-half-alt::before{
  content:"\f5c0";
}

.b-fa-code::before{
  content:"\f121";
}

.b-fa-whiskey-glass::before{
  content:"\f7a0";
}

.b-fa-glass-whiskey::before{
  content:"\f7a0";
}

.b-fa-building-circle-exclamation::before{
  content:"\e4d3";
}

.b-fa-magnifying-glass-chart::before{
  content:"\e522";
}

.b-fa-arrow-up-right-from-square::before{
  content:"\f08e";
}

.b-fa-external-link::before{
  content:"\f08e";
}

.b-fa-cubes-stacked::before{
  content:"\e4e6";
}

.b-fa-won-sign::before{
  content:"\f159";
}

.b-fa-krw::before{
  content:"\f159";
}

.b-fa-won::before{
  content:"\f159";
}

.b-fa-virus-covid::before{
  content:"\e4a8";
}

.b-fa-austral-sign::before{
  content:"\e0a9";
}

.b-fa-f::before{
  content:"F";
}

.b-fa-leaf::before{
  content:"\f06c";
}

.b-fa-road::before{
  content:"\f018";
}

.b-fa-taxi::before{
  content:"\f1ba";
}

.b-fa-cab::before{
  content:"\f1ba";
}

.b-fa-person-circle-plus::before{
  content:"\e541";
}

.b-fa-chart-pie::before{
  content:"\f200";
}

.b-fa-pie-chart::before{
  content:"\f200";
}

.b-fa-bolt-lightning::before{
  content:"\e0b7";
}

.b-fa-sack-xmark::before{
  content:"\e56a";
}

.b-fa-file-excel::before{
  content:"\f1c3";
}

.b-fa-file-contract::before{
  content:"\f56c";
}

.b-fa-fish-fins::before{
  content:"\e4f2";
}

.b-fa-building-flag::before{
  content:"\e4d5";
}

.b-fa-face-grin-beam::before{
  content:"\f582";
}

.b-fa-grin-beam::before{
  content:"\f582";
}

.b-fa-object-ungroup::before{
  content:"\f248";
}

.b-fa-poop::before{
  content:"\f619";
}

.b-fa-location-pin::before{
  content:"\f041";
}

.b-fa-map-marker::before{
  content:"\f041";
}

.b-fa-kaaba::before{
  content:"\f66b";
}

.b-fa-toilet-paper::before{
  content:"\f71e";
}

.b-fa-helmet-safety::before{
  content:"\f807";
}

.b-fa-hard-hat::before{
  content:"\f807";
}

.b-fa-hat-hard::before{
  content:"\f807";
}

.b-fa-eject::before{
  content:"\f052";
}

.b-fa-circle-right::before{
  content:"\f35a";
}

.b-fa-arrow-alt-circle-right::before{
  content:"\f35a";
}

.b-fa-plane-circle-check::before{
  content:"\e555";
}

.b-fa-face-rolling-eyes::before{
  content:"\f5a5";
}

.b-fa-meh-rolling-eyes::before{
  content:"\f5a5";
}

.b-fa-object-group::before{
  content:"\f247";
}

.b-fa-chart-line::before{
  content:"\f201";
}

.b-fa-line-chart::before{
  content:"\f201";
}

.b-fa-mask-ventilator::before{
  content:"\e524";
}

.b-fa-arrow-right::before{
  content:"\f061";
}

.b-fa-signs-post::before{
  content:"\f277";
}

.b-fa-map-signs::before{
  content:"\f277";
}

.b-fa-cash-register::before{
  content:"\f788";
}

.b-fa-person-circle-question::before{
  content:"\e542";
}

.b-fa-h::before{
  content:"H";
}

.b-fa-tarp::before{
  content:"\e57b";
}

.b-fa-screwdriver-wrench::before{
  content:"\f7d9";
}

.b-fa-tools::before{
  content:"\f7d9";
}

.b-fa-arrows-to-eye::before{
  content:"\e4bf";
}

.b-fa-plug-circle-bolt::before{
  content:"\e55b";
}

.b-fa-heart::before{
  content:"\f004";
}

.b-fa-mars-and-venus::before{
  content:"\f224";
}

.b-fa-house-user::before{
  content:"\e1b0";
}

.b-fa-home-user::before{
  content:"\e1b0";
}

.b-fa-dumpster-fire::before{
  content:"\f794";
}

.b-fa-house-crack::before{
  content:"\e3b1";
}

.b-fa-martini-glass-citrus::before{
  content:"\f561";
}

.b-fa-cocktail::before{
  content:"\f561";
}

.b-fa-face-surprise::before{
  content:"\f5c2";
}

.b-fa-surprise::before{
  content:"\f5c2";
}

.b-fa-bottle-water::before{
  content:"\e4c5";
}

.b-fa-circle-pause::before{
  content:"\f28b";
}

.b-fa-pause-circle::before{
  content:"\f28b";
}

.b-fa-toilet-paper-slash::before{
  content:"\e072";
}

.b-fa-apple-whole::before{
  content:"\f5d1";
}

.b-fa-apple-alt::before{
  content:"\f5d1";
}

.b-fa-kitchen-set::before{
  content:"\e51a";
}

.b-fa-r::before{
  content:"R";
}

.b-fa-temperature-quarter::before{
  content:"\f2ca";
}

.b-fa-temperature-1::before{
  content:"\f2ca";
}

.b-fa-thermometer-1::before{
  content:"\f2ca";
}

.b-fa-thermometer-quarter::before{
  content:"\f2ca";
}

.b-fa-cube::before{
  content:"\f1b2";
}

.b-fa-bitcoin-sign::before{
  content:"\e0b4";
}

.b-fa-shield-dog::before{
  content:"\e573";
}

.b-fa-solar-panel::before{
  content:"\f5ba";
}

.b-fa-lock-open::before{
  content:"\f3c1";
}

.b-fa-elevator::before{
  content:"\e16d";
}

.b-fa-money-bill-transfer::before{
  content:"\e528";
}

.b-fa-money-bill-trend-up::before{
  content:"\e529";
}

.b-fa-house-flood-water-circle-arrow-right::before{
  content:"\e50f";
}

.b-fa-square-poll-horizontal::before{
  content:"\f682";
}

.b-fa-poll-h::before{
  content:"\f682";
}

.b-fa-circle::before{
  content:"\f111";
}

.b-fa-backward-fast::before{
  content:"\f049";
}

.b-fa-fast-backward::before{
  content:"\f049";
}

.b-fa-recycle::before{
  content:"\f1b8";
}

.b-fa-user-astronaut::before{
  content:"\f4fb";
}

.b-fa-plane-slash::before{
  content:"\e069";
}

.b-fa-trademark::before{
  content:"\f25c";
}

.b-fa-basketball::before{
  content:"\f434";
}

.b-fa-basketball-ball::before{
  content:"\f434";
}

.b-fa-satellite-dish::before{
  content:"\f7c0";
}

.b-fa-circle-up::before{
  content:"\f35b";
}

.b-fa-arrow-alt-circle-up::before{
  content:"\f35b";
}

.b-fa-mobile-screen-button::before{
  content:"\f3cd";
}

.b-fa-mobile-alt::before{
  content:"\f3cd";
}

.b-fa-volume-high::before{
  content:"\f028";
}

.b-fa-volume-up::before{
  content:"\f028";
}

.b-fa-users-rays::before{
  content:"\e593";
}

.b-fa-wallet::before{
  content:"\f555";
}

.b-fa-clipboard-check::before{
  content:"\f46c";
}

.b-fa-file-audio::before{
  content:"\f1c7";
}

.b-fa-burger::before{
  content:"\f805";
}

.b-fa-hamburger::before{
  content:"\f805";
}

.b-fa-wrench::before{
  content:"\f0ad";
}

.b-fa-bugs::before{
  content:"\e4d0";
}

.b-fa-rupee-sign::before{
  content:"\f156";
}

.b-fa-rupee::before{
  content:"\f156";
}

.b-fa-file-image::before{
  content:"\f1c5";
}

.b-fa-circle-question::before{
  content:"\f059";
}

.b-fa-question-circle::before{
  content:"\f059";
}

.b-fa-plane-departure::before{
  content:"\f5b0";
}

.b-fa-handshake-slash::before{
  content:"\e060";
}

.b-fa-book-bookmark::before{
  content:"\e0bb";
}

.b-fa-code-branch::before{
  content:"\f126";
}

.b-fa-hat-cowboy::before{
  content:"\f8c0";
}

.b-fa-bridge::before{
  content:"\e4c8";
}

.b-fa-phone-flip::before{
  content:"\f879";
}

.b-fa-phone-alt::before{
  content:"\f879";
}

.b-fa-truck-front::before{
  content:"\e2b7";
}

.b-fa-cat::before{
  content:"\f6be";
}

.b-fa-anchor-circle-exclamation::before{
  content:"\e4ab";
}

.b-fa-truck-field::before{
  content:"\e58d";
}

.b-fa-route::before{
  content:"\f4d7";
}

.b-fa-clipboard-question::before{
  content:"\e4e3";
}

.b-fa-panorama::before{
  content:"\e209";
}

.b-fa-comment-medical::before{
  content:"\f7f5";
}

.b-fa-teeth-open::before{
  content:"\f62f";
}

.b-fa-file-circle-minus::before{
  content:"\e4ed";
}

.b-fa-tags::before{
  content:"\f02c";
}

.b-fa-wine-glass::before{
  content:"\f4e3";
}

.b-fa-forward-fast::before{
  content:"\f050";
}

.b-fa-fast-forward::before{
  content:"\f050";
}

.b-fa-face-meh-blank::before{
  content:"\f5a4";
}

.b-fa-meh-blank::before{
  content:"\f5a4";
}

.b-fa-square-parking::before{
  content:"\f540";
}

.b-fa-parking::before{
  content:"\f540";
}

.b-fa-house-signal::before{
  content:"\e012";
}

.b-fa-bars-progress::before{
  content:"\f828";
}

.b-fa-tasks-alt::before{
  content:"\f828";
}

.b-fa-faucet-drip::before{
  content:"\e006";
}

.b-fa-cart-flatbed::before{
  content:"\f474";
}

.b-fa-dolly-flatbed::before{
  content:"\f474";
}

.b-fa-ban-smoking::before{
  content:"\f54d";
}

.b-fa-smoking-ban::before{
  content:"\f54d";
}

.b-fa-terminal::before{
  content:"\f120";
}

.b-fa-mobile-button::before{
  content:"\f10b";
}

.b-fa-house-medical-flag::before{
  content:"\e514";
}

.b-fa-basket-shopping::before{
  content:"\f291";
}

.b-fa-shopping-basket::before{
  content:"\f291";
}

.b-fa-tape::before{
  content:"\f4db";
}

.b-fa-bus-simple::before{
  content:"\f55e";
}

.b-fa-bus-alt::before{
  content:"\f55e";
}

.b-fa-eye::before{
  content:"\f06e";
}

.b-fa-face-sad-cry::before{
  content:"\f5b3";
}

.b-fa-sad-cry::before{
  content:"\f5b3";
}

.b-fa-audio-description::before{
  content:"\f29e";
}

.b-fa-person-military-to-person::before{
  content:"\e54c";
}

.b-fa-file-shield::before{
  content:"\e4f0";
}

.b-fa-user-slash::before{
  content:"\f506";
}

.b-fa-pen::before{
  content:"\f304";
}

.b-fa-tower-observation::before{
  content:"\e586";
}

.b-fa-file-code::before{
  content:"\f1c9";
}

.b-fa-signal::before{
  content:"\f012";
}

.b-fa-signal-5::before{
  content:"\f012";
}

.b-fa-signal-perfect::before{
  content:"\f012";
}

.b-fa-bus::before{
  content:"\f207";
}

.b-fa-heart-circle-xmark::before{
  content:"\e501";
}

.b-fa-house-chimney::before{
  content:"\e3af";
}

.b-fa-home-lg::before{
  content:"\e3af";
}

.b-fa-window-maximize::before{
  content:"\f2d0";
}

.b-fa-face-frown::before{
  content:"\f119";
}

.b-fa-frown::before{
  content:"\f119";
}

.b-fa-prescription::before{
  content:"\f5b1";
}

.b-fa-shop::before{
  content:"\f54f";
}

.b-fa-store-alt::before{
  content:"\f54f";
}

.b-fa-floppy-disk::before{
  content:"\f0c7";
}

.b-fa-save::before{
  content:"\f0c7";
}

.b-fa-vihara::before{
  content:"\f6a7";
}

.b-fa-scale-unbalanced::before{
  content:"\f515";
}

.b-fa-balance-scale-left::before{
  content:"\f515";
}

.b-fa-sort-up::before{
  content:"\f0de";
}

.b-fa-sort-asc::before{
  content:"\f0de";
}

.b-fa-comment-dots::before{
  content:"\f4ad";
}

.b-fa-commenting::before{
  content:"\f4ad";
}

.b-fa-plant-wilt::before{
  content:"\e5aa";
}

.b-fa-diamond::before{
  content:"\f219";
}

.b-fa-face-grin-squint::before{
  content:"\f585";
}

.b-fa-grin-squint::before{
  content:"\f585";
}

.b-fa-hand-holding-dollar::before{
  content:"\f4c0";
}

.b-fa-hand-holding-usd::before{
  content:"\f4c0";
}

.b-fa-bacterium::before{
  content:"\e05a";
}

.b-fa-hand-pointer::before{
  content:"\f25a";
}

.b-fa-drum-steelpan::before{
  content:"\f56a";
}

.b-fa-hand-scissors::before{
  content:"\f257";
}

.b-fa-hands-praying::before{
  content:"\f684";
}

.b-fa-praying-hands::before{
  content:"\f684";
}

.b-fa-arrow-rotate-right::before{
  content:"\f01e";
}

.b-fa-arrow-right-rotate::before{
  content:"\f01e";
}

.b-fa-arrow-rotate-forward::before{
  content:"\f01e";
}

.b-fa-redo::before{
  content:"\f01e";
}

.b-fa-biohazard::before{
  content:"\f780";
}

.b-fa-location-crosshairs::before{
  content:"\f601";
}

.b-fa-location::before{
  content:"\f601";
}

.b-fa-mars-double::before{
  content:"\f227";
}

.b-fa-child-dress::before{
  content:"\e59c";
}

.b-fa-users-between-lines::before{
  content:"\e591";
}

.b-fa-lungs-virus::before{
  content:"\e067";
}

.b-fa-face-grin-tears::before{
  content:"\f588";
}

.b-fa-grin-tears::before{
  content:"\f588";
}

.b-fa-phone::before{
  content:"\f095";
}

.b-fa-calendar-xmark::before{
  content:"\f273";
}

.b-fa-calendar-times::before{
  content:"\f273";
}

.b-fa-child-reaching::before{
  content:"\e59d";
}

.b-fa-head-side-virus::before{
  content:"\e064";
}

.b-fa-user-gear::before{
  content:"\f4fe";
}

.b-fa-user-cog::before{
  content:"\f4fe";
}

.b-fa-arrow-up-1-9::before{
  content:"\f163";
}

.b-fa-sort-numeric-up::before{
  content:"\f163";
}

.b-fa-door-closed::before{
  content:"\f52a";
}

.b-fa-shield-virus::before{
  content:"\e06c";
}

.b-fa-dice-six::before{
  content:"\f526";
}

.b-fa-mosquito-net::before{
  content:"\e52c";
}

.b-fa-bridge-water::before{
  content:"\e4ce";
}

.b-fa-person-booth::before{
  content:"\f756";
}

.b-fa-text-width::before{
  content:"\f035";
}

.b-fa-hat-wizard::before{
  content:"\f6e8";
}

.b-fa-pen-fancy::before{
  content:"\f5ac";
}

.b-fa-person-digging::before{
  content:"\f85e";
}

.b-fa-digging::before{
  content:"\f85e";
}

.b-fa-trash::before{
  content:"\f1f8";
}

.b-fa-gauge-simple::before{
  content:"\f629";
}

.b-fa-gauge-simple-med::before{
  content:"\f629";
}

.b-fa-tachometer-average::before{
  content:"\f629";
}

.b-fa-book-medical::before{
  content:"\f7e6";
}

.b-fa-poo::before{
  content:"\f2fe";
}

.b-fa-quote-right::before{
  content:"\f10e";
}

.b-fa-quote-right-alt::before{
  content:"\f10e";
}

.b-fa-shirt::before{
  content:"\f553";
}

.b-fa-t-shirt::before{
  content:"\f553";
}

.b-fa-tshirt::before{
  content:"\f553";
}

.b-fa-cubes::before{
  content:"\f1b3";
}

.b-fa-divide::before{
  content:"\f529";
}

.b-fa-tenge-sign::before{
  content:"\f7d7";
}

.b-fa-tenge::before{
  content:"\f7d7";
}

.b-fa-headphones::before{
  content:"\f025";
}

.b-fa-hands-holding::before{
  content:"\f4c2";
}

.b-fa-hands-clapping::before{
  content:"\e1a8";
}

.b-fa-republican::before{
  content:"\f75e";
}

.b-fa-arrow-left::before{
  content:"\f060";
}

.b-fa-person-circle-xmark::before{
  content:"\e543";
}

.b-fa-ruler::before{
  content:"\f545";
}

.b-fa-align-left::before{
  content:"\f036";
}

.b-fa-dice-d6::before{
  content:"\f6d1";
}

.b-fa-restroom::before{
  content:"\f7bd";
}

.b-fa-j::before{
  content:"J";
}

.b-fa-users-viewfinder::before{
  content:"\e595";
}

.b-fa-file-video::before{
  content:"\f1c8";
}

.b-fa-up-right-from-square::before{
  content:"\f35d";
}

.b-fa-external-link-alt::before{
  content:"\f35d";
}

.b-fa-table-cells::before{
  content:"\f00a";
}

.b-fa-th::before{
  content:"\f00a";
}

.b-fa-file-pdf::before{
  content:"\f1c1";
}

.b-fa-book-bible::before{
  content:"\f647";
}

.b-fa-bible::before{
  content:"\f647";
}

.b-fa-o::before{
  content:"O";
}

.b-fa-suitcase-medical::before{
  content:"\f0fa";
}

.b-fa-medkit::before{
  content:"\f0fa";
}

.b-fa-user-secret::before{
  content:"\f21b";
}

.b-fa-otter::before{
  content:"\f700";
}

.b-fa-person-dress::before{
  content:"\f182";
}

.b-fa-female::before{
  content:"\f182";
}

.b-fa-comment-dollar::before{
  content:"\f651";
}

.b-fa-business-time::before{
  content:"\f64a";
}

.b-fa-briefcase-clock::before{
  content:"\f64a";
}

.b-fa-table-cells-large::before{
  content:"\f009";
}

.b-fa-th-large::before{
  content:"\f009";
}

.b-fa-book-tanakh::before{
  content:"\f827";
}

.b-fa-tanakh::before{
  content:"\f827";
}

.b-fa-phone-volume::before{
  content:"\f2a0";
}

.b-fa-volume-control-phone::before{
  content:"\f2a0";
}

.b-fa-hat-cowboy-side::before{
  content:"\f8c1";
}

.b-fa-clipboard-user::before{
  content:"\f7f3";
}

.b-fa-child::before{
  content:"\f1ae";
}

.b-fa-lira-sign::before{
  content:"\f195";
}

.b-fa-satellite::before{
  content:"\f7bf";
}

.b-fa-plane-lock::before{
  content:"\e558";
}

.b-fa-tag::before{
  content:"\f02b";
}

.b-fa-comment::before{
  content:"\f075";
}

.b-fa-cake-candles::before{
  content:"\f1fd";
}

.b-fa-birthday-cake::before{
  content:"\f1fd";
}

.b-fa-cake::before{
  content:"\f1fd";
}

.b-fa-envelope::before{
  content:"\f0e0";
}

.b-fa-angles-up::before{
  content:"\f102";
}

.b-fa-angle-double-up::before{
  content:"\f102";
}

.b-fa-paperclip::before{
  content:"\f0c6";
}

.b-fa-arrow-right-to-city::before{
  content:"\e4b3";
}

.b-fa-ribbon::before{
  content:"\f4d6";
}

.b-fa-lungs::before{
  content:"\f604";
}

.b-fa-arrow-up-9-1::before{
  content:"\f887";
}

.b-fa-sort-numeric-up-alt::before{
  content:"\f887";
}

.b-fa-litecoin-sign::before{
  content:"\e1d3";
}

.b-fa-border-none::before{
  content:"\f850";
}

.b-fa-circle-nodes::before{
  content:"\e4e2";
}

.b-fa-parachute-box::before{
  content:"\f4cd";
}

.b-fa-indent::before{
  content:"\f03c";
}

.b-fa-truck-field-un::before{
  content:"\e58e";
}

.b-fa-hourglass::before{
  content:"\f254";
}

.b-fa-hourglass-empty::before{
  content:"\f254";
}

.b-fa-mountain::before{
  content:"\f6fc";
}

.b-fa-user-doctor::before{
  content:"\f0f0";
}

.b-fa-user-md::before{
  content:"\f0f0";
}

.b-fa-circle-info::before{
  content:"\f05a";
}

.b-fa-info-circle::before{
  content:"\f05a";
}

.b-fa-cloud-meatball::before{
  content:"\f73b";
}

.b-fa-camera::before{
  content:"\f030";
}

.b-fa-camera-alt::before{
  content:"\f030";
}

.b-fa-square-virus::before{
  content:"\e578";
}

.b-fa-meteor::before{
  content:"\f753";
}

.b-fa-car-on::before{
  content:"\e4dd";
}

.b-fa-sleigh::before{
  content:"\f7cc";
}

.b-fa-arrow-down-1-9::before{
  content:"\f162";
}

.b-fa-sort-numeric-asc::before{
  content:"\f162";
}

.b-fa-sort-numeric-down::before{
  content:"\f162";
}

.b-fa-hand-holding-droplet::before{
  content:"\f4c1";
}

.b-fa-hand-holding-water::before{
  content:"\f4c1";
}

.b-fa-water::before{
  content:"\f773";
}

.b-fa-calendar-check::before{
  content:"\f274";
}

.b-fa-braille::before{
  content:"\f2a1";
}

.b-fa-prescription-bottle-medical::before{
  content:"\f486";
}

.b-fa-prescription-bottle-alt::before{
  content:"\f486";
}

.b-fa-landmark::before{
  content:"\f66f";
}

.b-fa-truck::before{
  content:"\f0d1";
}

.b-fa-crosshairs::before{
  content:"\f05b";
}

.b-fa-person-cane::before{
  content:"\e53c";
}

.b-fa-tent::before{
  content:"\e57d";
}

.b-fa-vest-patches::before{
  content:"\e086";
}

.b-fa-check-double::before{
  content:"\f560";
}

.b-fa-arrow-down-a-z::before{
  content:"\f15d";
}

.b-fa-sort-alpha-asc::before{
  content:"\f15d";
}

.b-fa-sort-alpha-down::before{
  content:"\f15d";
}

.b-fa-money-bill-wheat::before{
  content:"\e52a";
}

.b-fa-cookie::before{
  content:"\f563";
}

.b-fa-arrow-rotate-left::before{
  content:"\f0e2";
}

.b-fa-arrow-left-rotate::before{
  content:"\f0e2";
}

.b-fa-arrow-rotate-back::before{
  content:"\f0e2";
}

.b-fa-arrow-rotate-backward::before{
  content:"\f0e2";
}

.b-fa-undo::before{
  content:"\f0e2";
}

.b-fa-hard-drive::before{
  content:"\f0a0";
}

.b-fa-hdd::before{
  content:"\f0a0";
}

.b-fa-face-grin-squint-tears::before{
  content:"\f586";
}

.b-fa-grin-squint-tears::before{
  content:"\f586";
}

.b-fa-dumbbell::before{
  content:"\f44b";
}

.b-fa-rectangle-list::before{
  content:"\f022";
}

.b-fa-list-alt::before{
  content:"\f022";
}

.b-fa-tarp-droplet::before{
  content:"\e57c";
}

.b-fa-house-medical-circle-check::before{
  content:"\e511";
}

.b-fa-person-skiing-nordic::before{
  content:"\f7ca";
}

.b-fa-skiing-nordic::before{
  content:"\f7ca";
}

.b-fa-calendar-plus::before{
  content:"\f271";
}

.b-fa-plane-arrival::before{
  content:"\f5af";
}

.b-fa-circle-left::before{
  content:"\f359";
}

.b-fa-arrow-alt-circle-left::before{
  content:"\f359";
}

.b-fa-train-subway::before{
  content:"\f239";
}

.b-fa-subway::before{
  content:"\f239";
}

.b-fa-chart-gantt::before{
  content:"\e0e4";
}

.b-fa-indian-rupee-sign::before{
  content:"\e1bc";
}

.b-fa-indian-rupee::before{
  content:"\e1bc";
}

.b-fa-inr::before{
  content:"\e1bc";
}

.b-fa-crop-simple::before{
  content:"\f565";
}

.b-fa-crop-alt::before{
  content:"\f565";
}

.b-fa-money-bill-1::before{
  content:"\f3d1";
}

.b-fa-money-bill-alt::before{
  content:"\f3d1";
}

.b-fa-left-long::before{
  content:"\f30a";
}

.b-fa-long-arrow-alt-left::before{
  content:"\f30a";
}

.b-fa-dna::before{
  content:"\f471";
}

.b-fa-virus-slash::before{
  content:"\e075";
}

.b-fa-minus::before{
  content:"\f068";
}

.b-fa-subtract::before{
  content:"\f068";
}

.b-fa-chess::before{
  content:"\f439";
}

.b-fa-arrow-left-long::before{
  content:"\f177";
}

.b-fa-long-arrow-left::before{
  content:"\f177";
}

.b-fa-plug-circle-check::before{
  content:"\e55c";
}

.b-fa-street-view::before{
  content:"\f21d";
}

.b-fa-franc-sign::before{
  content:"\e18f";
}

.b-fa-volume-off::before{
  content:"\f026";
}

.b-fa-hands-asl-interpreting::before{
  content:"\f2a3";
}

.b-fa-american-sign-language-interpreting::before{
  content:"\f2a3";
}

.b-fa-asl-interpreting::before{
  content:"\f2a3";
}

.b-fa-hands-american-sign-language-interpreting::before{
  content:"\f2a3";
}

.b-fa-gear::before{
  content:"\f013";
}

.b-fa-cog::before{
  content:"\f013";
}

.b-fa-droplet-slash::before{
  content:"\f5c7";
}

.b-fa-tint-slash::before{
  content:"\f5c7";
}

.b-fa-mosque::before{
  content:"\f678";
}

.b-fa-mosquito::before{
  content:"\e52b";
}

.b-fa-star-of-david::before{
  content:"\f69a";
}

.b-fa-person-military-rifle::before{
  content:"\e54b";
}

.b-fa-cart-shopping::before{
  content:"\f07a";
}

.b-fa-shopping-cart::before{
  content:"\f07a";
}

.b-fa-vials::before{
  content:"\f493";
}

.b-fa-plug-circle-plus::before{
  content:"\e55f";
}

.b-fa-place-of-worship::before{
  content:"\f67f";
}

.b-fa-grip-vertical::before{
  content:"\f58e";
}

.b-fa-arrow-turn-up::before{
  content:"\f148";
}

.b-fa-level-up::before{
  content:"\f148";
}

.b-fa-u::before{
  content:"U";
}

.b-fa-square-root-variable::before{
  content:"\f698";
}

.b-fa-square-root-alt::before{
  content:"\f698";
}

.b-fa-clock::before{
  content:"\f017";
}

.b-fa-clock-four::before{
  content:"\f017";
}

.b-fa-backward-step::before{
  content:"\f048";
}

.b-fa-step-backward::before{
  content:"\f048";
}

.b-fa-pallet::before{
  content:"\f482";
}

.b-fa-faucet::before{
  content:"\e005";
}

.b-fa-baseball-bat-ball::before{
  content:"\f432";
}

.b-fa-s::before{
  content:"S";
}

.b-fa-timeline::before{
  content:"\e29c";
}

.b-fa-keyboard::before{
  content:"\f11c";
}

.b-fa-caret-down::before{
  content:"\f0d7";
}

.b-fa-house-chimney-medical::before{
  content:"\f7f2";
}

.b-fa-clinic-medical::before{
  content:"\f7f2";
}

.b-fa-temperature-three-quarters::before{
  content:"\f2c8";
}

.b-fa-temperature-3::before{
  content:"\f2c8";
}

.b-fa-thermometer-3::before{
  content:"\f2c8";
}

.b-fa-thermometer-three-quarters::before{
  content:"\f2c8";
}

.b-fa-mobile-screen::before{
  content:"\f3cf";
}

.b-fa-mobile-android-alt::before{
  content:"\f3cf";
}

.b-fa-plane-up::before{
  content:"\e22d";
}

.b-fa-piggy-bank::before{
  content:"\f4d3";
}

.b-fa-battery-half::before{
  content:"\f242";
}

.b-fa-battery-3::before{
  content:"\f242";
}

.b-fa-mountain-city::before{
  content:"\e52e";
}

.b-fa-coins::before{
  content:"\f51e";
}

.b-fa-khanda::before{
  content:"\f66d";
}

.b-fa-sliders::before{
  content:"\f1de";
}

.b-fa-sliders-h::before{
  content:"\f1de";
}

.b-fa-folder-tree::before{
  content:"\f802";
}

.b-fa-network-wired::before{
  content:"\f6ff";
}

.b-fa-map-pin::before{
  content:"\f276";
}

.b-fa-hamsa::before{
  content:"\f665";
}

.b-fa-cent-sign::before{
  content:"\e3f5";
}

.b-fa-flask::before{
  content:"\f0c3";
}

.b-fa-person-pregnant::before{
  content:"\e31e";
}

.b-fa-wand-sparkles::before{
  content:"\f72b";
}

.b-fa-ellipsis-vertical::before{
  content:"\f142";
}

.b-fa-ellipsis-v::before{
  content:"\f142";
}

.b-fa-ticket::before{
  content:"\f145";
}

.b-fa-power-off::before{
  content:"\f011";
}

.b-fa-right-long::before{
  content:"\f30b";
}

.b-fa-long-arrow-alt-right::before{
  content:"\f30b";
}

.b-fa-flag-usa::before{
  content:"\f74d";
}

.b-fa-laptop-file::before{
  content:"\e51d";
}

.b-fa-tty::before{
  content:"\f1e4";
}

.b-fa-teletype::before{
  content:"\f1e4";
}

.b-fa-diagram-next::before{
  content:"\e476";
}

.b-fa-person-rifle::before{
  content:"\e54e";
}

.b-fa-house-medical-circle-exclamation::before{
  content:"\e512";
}

.b-fa-closed-captioning::before{
  content:"\f20a";
}

.b-fa-person-hiking::before{
  content:"\f6ec";
}

.b-fa-hiking::before{
  content:"\f6ec";
}

.b-fa-venus-double::before{
  content:"\f226";
}

.b-fa-images::before{
  content:"\f302";
}

.b-fa-calculator::before{
  content:"\f1ec";
}

.b-fa-people-pulling::before{
  content:"\e535";
}

.b-fa-n::before{
  content:"N";
}

.b-fa-cable-car::before{
  content:"\f7da";
}

.b-fa-tram::before{
  content:"\f7da";
}

.b-fa-cloud-rain::before{
  content:"\f73d";
}

.b-fa-building-circle-xmark::before{
  content:"\e4d4";
}

.b-fa-ship::before{
  content:"\f21a";
}

.b-fa-arrows-down-to-line::before{
  content:"\e4b8";
}

.b-fa-download::before{
  content:"\f019";
}

.b-fa-face-grin::before{
  content:"\f580";
}

.b-fa-grin::before{
  content:"\f580";
}

.b-fa-delete-left::before{
  content:"\f55a";
}

.b-fa-backspace::before{
  content:"\f55a";
}

.b-fa-eye-dropper::before{
  content:"\f1fb";
}

.b-fa-eye-dropper-empty::before{
  content:"\f1fb";
}

.b-fa-eyedropper::before{
  content:"\f1fb";
}

.b-fa-file-circle-check::before{
  content:"\e5a0";
}

.b-fa-forward::before{
  content:"\f04e";
}

.b-fa-mobile::before{
  content:"\f3ce";
}

.b-fa-mobile-android::before{
  content:"\f3ce";
}

.b-fa-mobile-phone::before{
  content:"\f3ce";
}

.b-fa-face-meh::before{
  content:"\f11a";
}

.b-fa-meh::before{
  content:"\f11a";
}

.b-fa-align-center::before{
  content:"\f037";
}

.b-fa-book-skull::before{
  content:"\f6b7";
}

.b-fa-book-dead::before{
  content:"\f6b7";
}

.b-fa-id-card::before{
  content:"\f2c2";
}

.b-fa-drivers-license::before{
  content:"\f2c2";
}

.b-fa-outdent::before{
  content:"\f03b";
}

.b-fa-dedent::before{
  content:"\f03b";
}

.b-fa-heart-circle-exclamation::before{
  content:"\e4fe";
}

.b-fa-house::before{
  content:"\f015";
}

.b-fa-home::before{
  content:"\f015";
}

.b-fa-home-alt::before{
  content:"\f015";
}

.b-fa-home-lg-alt::before{
  content:"\f015";
}

.b-fa-calendar-week::before{
  content:"\f784";
}

.b-fa-laptop-medical::before{
  content:"\f812";
}

.b-fa-b::before{
  content:"B";
}

.b-fa-file-medical::before{
  content:"\f477";
}

.b-fa-dice-one::before{
  content:"\f525";
}

.b-fa-kiwi-bird::before{
  content:"\f535";
}

.b-fa-arrow-right-arrow-left::before{
  content:"\f0ec";
}

.b-fa-exchange::before{
  content:"\f0ec";
}

.b-fa-rotate-right::before{
  content:"\f2f9";
}

.b-fa-redo-alt::before{
  content:"\f2f9";
}

.b-fa-rotate-forward::before{
  content:"\f2f9";
}

.b-fa-utensils::before{
  content:"\f2e7";
}

.b-fa-cutlery::before{
  content:"\f2e7";
}

.b-fa-arrow-up-wide-short::before{
  content:"\f161";
}

.b-fa-sort-amount-up::before{
  content:"\f161";
}

.b-fa-mill-sign::before{
  content:"\e1ed";
}

.b-fa-bowl-rice::before{
  content:"\e2eb";
}

.b-fa-skull::before{
  content:"\f54c";
}

.b-fa-tower-broadcast::before{
  content:"\f519";
}

.b-fa-broadcast-tower::before{
  content:"\f519";
}

.b-fa-truck-pickup::before{
  content:"\f63c";
}

.b-fa-up-long::before{
  content:"\f30c";
}

.b-fa-long-arrow-alt-up::before{
  content:"\f30c";
}

.b-fa-stop::before{
  content:"\f04d";
}

.b-fa-code-merge::before{
  content:"\f387";
}

.b-fa-upload::before{
  content:"\f093";
}

.b-fa-hurricane::before{
  content:"\f751";
}

.b-fa-mound::before{
  content:"\e52d";
}

.b-fa-toilet-portable::before{
  content:"\e583";
}

.b-fa-compact-disc::before{
  content:"\f51f";
}

.b-fa-file-arrow-down::before{
  content:"\f56d";
}

.b-fa-file-download::before{
  content:"\f56d";
}

.b-fa-caravan::before{
  content:"\f8ff";
}

.b-fa-shield-cat::before{
  content:"\e572";
}

.b-fa-bolt::before{
  content:"\f0e7";
}

.b-fa-zap::before{
  content:"\f0e7";
}

.b-fa-glass-water::before{
  content:"\e4f4";
}

.b-fa-oil-well::before{
  content:"\e532";
}

.b-fa-vault::before{
  content:"\e2c5";
}

.b-fa-mars::before{
  content:"\f222";
}

.b-fa-toilet::before{
  content:"\f7d8";
}

.b-fa-plane-circle-xmark::before{
  content:"\e557";
}

.b-fa-yen-sign::before{
  content:"\f157";
}

.b-fa-cny::before{
  content:"\f157";
}

.b-fa-jpy::before{
  content:"\f157";
}

.b-fa-rmb::before{
  content:"\f157";
}

.b-fa-yen::before{
  content:"\f157";
}

.b-fa-ruble-sign::before{
  content:"\f158";
}

.b-fa-rouble::before{
  content:"\f158";
}

.b-fa-rub::before{
  content:"\f158";
}

.b-fa-ruble::before{
  content:"\f158";
}

.b-fa-sun::before{
  content:"\f185";
}

.b-fa-guitar::before{
  content:"\f7a6";
}

.b-fa-face-laugh-wink::before{
  content:"\f59c";
}

.b-fa-laugh-wink::before{
  content:"\f59c";
}

.b-fa-horse-head::before{
  content:"\f7ab";
}

.b-fa-bore-hole::before{
  content:"\e4c3";
}

.b-fa-industry::before{
  content:"\f275";
}

.b-fa-circle-down::before{
  content:"\f358";
}

.b-fa-arrow-alt-circle-down::before{
  content:"\f358";
}

.b-fa-arrows-turn-to-dots::before{
  content:"\e4c1";
}

.b-fa-florin-sign::before{
  content:"\e184";
}

.b-fa-arrow-down-short-wide::before{
  content:"\f884";
}

.b-fa-sort-amount-desc::before{
  content:"\f884";
}

.b-fa-sort-amount-down-alt::before{
  content:"\f884";
}

.b-fa-less-than::before{
  content:"\<";
}

.b-fa-angle-down::before{
  content:"\f107";
}

.b-fa-car-tunnel::before{
  content:"\e4de";
}

.b-fa-head-side-cough::before{
  content:"\e061";
}

.b-fa-grip-lines::before{
  content:"\f7a4";
}

.b-fa-thumbs-down::before{
  content:"\f165";
}

.b-fa-user-lock::before{
  content:"\f502";
}

.b-fa-arrow-right-long::before{
  content:"\f178";
}

.b-fa-long-arrow-right::before{
  content:"\f178";
}

.b-fa-anchor-circle-xmark::before{
  content:"\e4ac";
}

.b-fa-ellipsis::before{
  content:"\f141";
}

.b-fa-ellipsis-h::before{
  content:"\f141";
}

.b-fa-chess-pawn::before{
  content:"\f443";
}

.b-fa-kit-medical::before{
  content:"\f479";
}

.b-fa-first-aid::before{
  content:"\f479";
}

.b-fa-person-through-window::before{
  content:"\e5a9";
}

.b-fa-toolbox::before{
  content:"\f552";
}

.b-fa-hands-holding-circle::before{
  content:"\e4fb";
}

.b-fa-bug::before{
  content:"\f188";
}

.b-fa-credit-card::before{
  content:"\f09d";
}

.b-fa-credit-card-alt::before{
  content:"\f09d";
}

.b-fa-car::before{
  content:"\f1b9";
}

.b-fa-automobile::before{
  content:"\f1b9";
}

.b-fa-hand-holding-hand::before{
  content:"\e4f7";
}

.b-fa-book-open-reader::before{
  content:"\f5da";
}

.b-fa-book-reader::before{
  content:"\f5da";
}

.b-fa-mountain-sun::before{
  content:"\e52f";
}

.b-fa-arrows-left-right-to-line::before{
  content:"\e4ba";
}

.b-fa-dice-d20::before{
  content:"\f6cf";
}

.b-fa-truck-droplet::before{
  content:"\e58c";
}

.b-fa-file-circle-xmark::before{
  content:"\e5a1";
}

.b-fa-temperature-arrow-up::before{
  content:"\e040";
}

.b-fa-temperature-up::before{
  content:"\e040";
}

.b-fa-medal::before{
  content:"\f5a2";
}

.b-fa-bed::before{
  content:"\f236";
}

.b-fa-square-h::before{
  content:"\f0fd";
}

.b-fa-h-square::before{
  content:"\f0fd";
}

.b-fa-podcast::before{
  content:"\f2ce";
}

.b-fa-temperature-full::before{
  content:"\f2c7";
}

.b-fa-temperature-4::before{
  content:"\f2c7";
}

.b-fa-thermometer-4::before{
  content:"\f2c7";
}

.b-fa-thermometer-full::before{
  content:"\f2c7";
}

.b-fa-bell::before{
  content:"\f0f3";
}

.b-fa-superscript::before{
  content:"\f12b";
}

.b-fa-plug-circle-xmark::before{
  content:"\e560";
}

.b-fa-star-of-life::before{
  content:"\f621";
}

.b-fa-phone-slash::before{
  content:"\f3dd";
}

.b-fa-paint-roller::before{
  content:"\f5aa";
}

.b-fa-handshake-angle::before{
  content:"\f4c4";
}

.b-fa-hands-helping::before{
  content:"\f4c4";
}

.b-fa-location-dot::before{
  content:"\f3c5";
}

.b-fa-map-marker-alt::before{
  content:"\f3c5";
}

.b-fa-file::before{
  content:"\f15b";
}

.b-fa-greater-than::before{
  content:"\>";
}

.b-fa-person-swimming::before{
  content:"\f5c4";
}

.b-fa-swimmer::before{
  content:"\f5c4";
}

.b-fa-arrow-down::before{
  content:"\f063";
}

.b-fa-droplet::before{
  content:"\f043";
}

.b-fa-tint::before{
  content:"\f043";
}

.b-fa-eraser::before{
  content:"\f12d";
}

.b-fa-earth-americas::before{
  content:"\f57d";
}

.b-fa-earth::before{
  content:"\f57d";
}

.b-fa-earth-america::before{
  content:"\f57d";
}

.b-fa-globe-americas::before{
  content:"\f57d";
}

.b-fa-person-burst::before{
  content:"\e53b";
}

.b-fa-dove::before{
  content:"\f4ba";
}

.b-fa-battery-empty::before{
  content:"\f244";
}

.b-fa-battery-0::before{
  content:"\f244";
}

.b-fa-socks::before{
  content:"\f696";
}

.b-fa-inbox::before{
  content:"\f01c";
}

.b-fa-section::before{
  content:"\e447";
}

.b-fa-gauge-high::before{
  content:"\f625";
}

.b-fa-tachometer-alt::before{
  content:"\f625";
}

.b-fa-tachometer-alt-fast::before{
  content:"\f625";
}

.b-fa-envelope-open-text::before{
  content:"\f658";
}

.b-fa-hospital::before{
  content:"\f0f8";
}

.b-fa-hospital-alt::before{
  content:"\f0f8";
}

.b-fa-hospital-wide::before{
  content:"\f0f8";
}

.b-fa-wine-bottle::before{
  content:"\f72f";
}

.b-fa-chess-rook::before{
  content:"\f447";
}

.b-fa-bars-staggered::before{
  content:"\f550";
}

.b-fa-reorder::before{
  content:"\f550";
}

.b-fa-stream::before{
  content:"\f550";
}

.b-fa-dharmachakra::before{
  content:"\f655";
}

.b-fa-hotdog::before{
  content:"\f80f";
}

.b-fa-person-walking-with-cane::before{
  content:"\f29d";
}

.b-fa-blind::before{
  content:"\f29d";
}

.b-fa-drum::before{
  content:"\f569";
}

.b-fa-ice-cream::before{
  content:"\f810";
}

.b-fa-heart-circle-bolt::before{
  content:"\e4fc";
}

.b-fa-fax::before{
  content:"\f1ac";
}

.b-fa-paragraph::before{
  content:"\f1dd";
}

.b-fa-check-to-slot::before{
  content:"\f772";
}

.b-fa-vote-yea::before{
  content:"\f772";
}

.b-fa-star-half::before{
  content:"\f089";
}

.b-fa-boxes-stacked::before{
  content:"\f468";
}

.b-fa-boxes::before{
  content:"\f468";
}

.b-fa-boxes-alt::before{
  content:"\f468";
}

.b-fa-link::before{
  content:"\f0c1";
}

.b-fa-chain::before{
  content:"\f0c1";
}

.b-fa-ear-listen::before{
  content:"\f2a2";
}

.b-fa-assistive-listening-systems::before{
  content:"\f2a2";
}

.b-fa-tree-city::before{
  content:"\e587";
}

.b-fa-play::before{
  content:"\f04b";
}

.b-fa-font::before{
  content:"\f031";
}

.b-fa-table-cells-row-lock::before{
  content:"\e67a";
}

.b-fa-rupiah-sign::before{
  content:"\e23d";
}

.b-fa-magnifying-glass::before{
  content:"\f002";
}

.b-fa-search::before{
  content:"\f002";
}

.b-fa-table-tennis-paddle-ball::before{
  content:"\f45d";
}

.b-fa-ping-pong-paddle-ball::before{
  content:"\f45d";
}

.b-fa-table-tennis::before{
  content:"\f45d";
}

.b-fa-person-dots-from-line::before{
  content:"\f470";
}

.b-fa-diagnoses::before{
  content:"\f470";
}

.b-fa-trash-can-arrow-up::before{
  content:"\f82a";
}

.b-fa-trash-restore-alt::before{
  content:"\f82a";
}

.b-fa-naira-sign::before{
  content:"\e1f6";
}

.b-fa-cart-arrow-down::before{
  content:"\f218";
}

.b-fa-walkie-talkie::before{
  content:"\f8ef";
}

.b-fa-file-pen::before{
  content:"\f31c";
}

.b-fa-file-edit::before{
  content:"\f31c";
}

.b-fa-receipt::before{
  content:"\f543";
}

.b-fa-square-pen::before{
  content:"\f14b";
}

.b-fa-pen-square::before{
  content:"\f14b";
}

.b-fa-pencil-square::before{
  content:"\f14b";
}

.b-fa-suitcase-rolling::before{
  content:"\f5c1";
}

.b-fa-person-circle-exclamation::before{
  content:"\e53f";
}

.b-fa-chevron-down::before{
  content:"\f078";
}

.b-fa-battery-full::before{
  content:"\f240";
}

.b-fa-battery::before{
  content:"\f240";
}

.b-fa-battery-5::before{
  content:"\f240";
}

.b-fa-skull-crossbones::before{
  content:"\f714";
}

.b-fa-code-compare::before{
  content:"\e13a";
}

.b-fa-list-ul::before{
  content:"\f0ca";
}

.b-fa-list-dots::before{
  content:"\f0ca";
}

.b-fa-school-lock::before{
  content:"\e56f";
}

.b-fa-tower-cell::before{
  content:"\e585";
}

.b-fa-down-long::before{
  content:"\f309";
}

.b-fa-long-arrow-alt-down::before{
  content:"\f309";
}

.b-fa-ranking-star::before{
  content:"\e561";
}

.b-fa-chess-king::before{
  content:"\f43f";
}

.b-fa-person-harassing::before{
  content:"\e549";
}

.b-fa-brazilian-real-sign::before{
  content:"\e46c";
}

.b-fa-landmark-dome::before{
  content:"\f752";
}

.b-fa-landmark-alt::before{
  content:"\f752";
}

.b-fa-arrow-up::before{
  content:"\f062";
}

.b-fa-tv::before{
  content:"\f26c";
}

.b-fa-television::before{
  content:"\f26c";
}

.b-fa-tv-alt::before{
  content:"\f26c";
}

.b-fa-shrimp::before{
  content:"\e448";
}

.b-fa-list-check::before{
  content:"\f0ae";
}

.b-fa-tasks::before{
  content:"\f0ae";
}

.b-fa-jug-detergent::before{
  content:"\e519";
}

.b-fa-circle-user::before{
  content:"\f2bd";
}

.b-fa-user-circle::before{
  content:"\f2bd";
}

.b-fa-user-shield::before{
  content:"\f505";
}

.b-fa-wind::before{
  content:"\f72e";
}

.b-fa-car-burst::before{
  content:"\f5e1";
}

.b-fa-car-crash::before{
  content:"\f5e1";
}

.b-fa-y::before{
  content:"Y";
}

.b-fa-person-snowboarding::before{
  content:"\f7ce";
}

.b-fa-snowboarding::before{
  content:"\f7ce";
}

.b-fa-truck-fast::before{
  content:"\f48b";
}

.b-fa-shipping-fast::before{
  content:"\f48b";
}

.b-fa-fish::before{
  content:"\f578";
}

.b-fa-user-graduate::before{
  content:"\f501";
}

.b-fa-circle-half-stroke::before{
  content:"\f042";
}

.b-fa-adjust::before{
  content:"\f042";
}

.b-fa-clapperboard::before{
  content:"\e131";
}

.b-fa-circle-radiation::before{
  content:"\f7ba";
}

.b-fa-radiation-alt::before{
  content:"\f7ba";
}

.b-fa-baseball::before{
  content:"\f433";
}

.b-fa-baseball-ball::before{
  content:"\f433";
}

.b-fa-jet-fighter-up::before{
  content:"\e518";
}

.b-fa-diagram-project::before{
  content:"\f542";
}

.b-fa-project-diagram::before{
  content:"\f542";
}

.b-fa-copy::before{
  content:"\f0c5";
}

.b-fa-volume-xmark::before{
  content:"\f6a9";
}

.b-fa-volume-mute::before{
  content:"\f6a9";
}

.b-fa-volume-times::before{
  content:"\f6a9";
}

.b-fa-hand-sparkles::before{
  content:"\e05d";
}

.b-fa-grip::before{
  content:"\f58d";
}

.b-fa-grip-horizontal::before{
  content:"\f58d";
}

.b-fa-share-from-square::before{
  content:"\f14d";
}

.b-fa-share-square::before{
  content:"\f14d";
}

.b-fa-child-combatant::before{
  content:"\e4e0";
}

.b-fa-child-rifle::before{
  content:"\e4e0";
}

.b-fa-gun::before{
  content:"\e19b";
}

.b-fa-square-phone::before{
  content:"\f098";
}

.b-fa-phone-square::before{
  content:"\f098";
}

.b-fa-plus::before{
  content:"\+";
}

.b-fa-add::before{
  content:"\+";
}

.b-fa-expand::before{
  content:"\f065";
}

.b-fa-computer::before{
  content:"\e4e5";
}

.b-fa-xmark::before{
  content:"\f00d";
}

.b-fa-close::before{
  content:"\f00d";
}

.b-fa-multiply::before{
  content:"\f00d";
}

.b-fa-remove::before{
  content:"\f00d";
}

.b-fa-times::before{
  content:"\f00d";
}

.b-fa-arrows-up-down-left-right::before{
  content:"\f047";
}

.b-fa-arrows::before{
  content:"\f047";
}

.b-fa-chalkboard-user::before{
  content:"\f51c";
}

.b-fa-chalkboard-teacher::before{
  content:"\f51c";
}

.b-fa-peso-sign::before{
  content:"\e222";
}

.b-fa-building-shield::before{
  content:"\e4d8";
}

.b-fa-baby::before{
  content:"\f77c";
}

.b-fa-users-line::before{
  content:"\e592";
}

.b-fa-quote-left::before{
  content:"\f10d";
}

.b-fa-quote-left-alt::before{
  content:"\f10d";
}

.b-fa-tractor::before{
  content:"\f722";
}

.b-fa-trash-arrow-up::before{
  content:"\f829";
}

.b-fa-trash-restore::before{
  content:"\f829";
}

.b-fa-arrow-down-up-lock::before{
  content:"\e4b0";
}

.b-fa-lines-leaning::before{
  content:"\e51e";
}

.b-fa-ruler-combined::before{
  content:"\f546";
}

.b-fa-copyright::before{
  content:"\f1f9";
}

.b-fa-equals::before{
  content:"\=";
}

.b-fa-blender::before{
  content:"\f517";
}

.b-fa-teeth::before{
  content:"\f62e";
}

.b-fa-shekel-sign::before{
  content:"\f20b";
}

.b-fa-ils::before{
  content:"\f20b";
}

.b-fa-shekel::before{
  content:"\f20b";
}

.b-fa-sheqel::before{
  content:"\f20b";
}

.b-fa-sheqel-sign::before{
  content:"\f20b";
}

.b-fa-map::before{
  content:"\f279";
}

.b-fa-rocket::before{
  content:"\f135";
}

.b-fa-photo-film::before{
  content:"\f87c";
}

.b-fa-photo-video::before{
  content:"\f87c";
}

.b-fa-folder-minus::before{
  content:"\f65d";
}

.b-fa-store::before{
  content:"\f54e";
}

.b-fa-arrow-trend-up::before{
  content:"\e098";
}

.b-fa-plug-circle-minus::before{
  content:"\e55e";
}

.b-fa-sign-hanging::before{
  content:"\f4d9";
}

.b-fa-sign::before{
  content:"\f4d9";
}

.b-fa-bezier-curve::before{
  content:"\f55b";
}

.b-fa-bell-slash::before{
  content:"\f1f6";
}

.b-fa-tablet::before{
  content:"\f3fb";
}

.b-fa-tablet-android::before{
  content:"\f3fb";
}

.b-fa-school-flag::before{
  content:"\e56e";
}

.b-fa-fill::before{
  content:"\f575";
}

.b-fa-angle-up::before{
  content:"\f106";
}

.b-fa-drumstick-bite::before{
  content:"\f6d7";
}

.b-fa-holly-berry::before{
  content:"\f7aa";
}

.b-fa-chevron-left::before{
  content:"\f053";
}

.b-fa-bacteria::before{
  content:"\e059";
}

.b-fa-hand-lizard::before{
  content:"\f258";
}

.b-fa-notdef::before{
  content:"\e1fe";
}

.b-fa-disease::before{
  content:"\f7fa";
}

.b-fa-briefcase-medical::before{
  content:"\f469";
}

.b-fa-genderless::before{
  content:"\f22d";
}

.b-fa-chevron-right::before{
  content:"\f054";
}

.b-fa-retweet::before{
  content:"\f079";
}

.b-fa-car-rear::before{
  content:"\f5de";
}

.b-fa-car-alt::before{
  content:"\f5de";
}

.b-fa-pump-soap::before{
  content:"\e06b";
}

.b-fa-video-slash::before{
  content:"\f4e2";
}

.b-fa-battery-quarter::before{
  content:"\f243";
}

.b-fa-battery-2::before{
  content:"\f243";
}

.b-fa-radio::before{
  content:"\f8d7";
}

.b-fa-baby-carriage::before{
  content:"\f77d";
}

.b-fa-carriage-baby::before{
  content:"\f77d";
}

.b-fa-traffic-light::before{
  content:"\f637";
}

.b-fa-thermometer::before{
  content:"\f491";
}

.b-fa-vr-cardboard::before{
  content:"\f729";
}

.b-fa-hand-middle-finger::before{
  content:"\f806";
}

.b-fa-percent::before{
  content:"\%";
}

.b-fa-percentage::before{
  content:"\%";
}

.b-fa-truck-moving::before{
  content:"\f4df";
}

.b-fa-glass-water-droplet::before{
  content:"\e4f5";
}

.b-fa-display::before{
  content:"\e163";
}

.b-fa-face-smile::before{
  content:"\f118";
}

.b-fa-smile::before{
  content:"\f118";
}

.b-fa-thumbtack::before{
  content:"\f08d";
}

.b-fa-thumb-tack::before{
  content:"\f08d";
}

.b-fa-trophy::before{
  content:"\f091";
}

.b-fa-person-praying::before{
  content:"\f683";
}

.b-fa-pray::before{
  content:"\f683";
}

.b-fa-hammer::before{
  content:"\f6e3";
}

.b-fa-hand-peace::before{
  content:"\f25b";
}

.b-fa-rotate::before{
  content:"\f2f1";
}

.b-fa-sync-alt::before{
  content:"\f2f1";
}

.b-fa-spinner::before{
  content:"\f110";
}

.b-fa-robot::before{
  content:"\f544";
}

.b-fa-peace::before{
  content:"\f67c";
}

.b-fa-gears::before{
  content:"\f085";
}

.b-fa-cogs::before{
  content:"\f085";
}

.b-fa-warehouse::before{
  content:"\f494";
}

.b-fa-arrow-up-right-dots::before{
  content:"\e4b7";
}

.b-fa-splotch::before{
  content:"\f5bc";
}

.b-fa-face-grin-hearts::before{
  content:"\f584";
}

.b-fa-grin-hearts::before{
  content:"\f584";
}

.b-fa-dice-four::before{
  content:"\f524";
}

.b-fa-sim-card::before{
  content:"\f7c4";
}

.b-fa-transgender::before{
  content:"\f225";
}

.b-fa-transgender-alt::before{
  content:"\f225";
}

.b-fa-mercury::before{
  content:"\f223";
}

.b-fa-arrow-turn-down::before{
  content:"\f149";
}

.b-fa-level-down::before{
  content:"\f149";
}

.b-fa-person-falling-burst::before{
  content:"\e547";
}

.b-fa-award::before{
  content:"\f559";
}

.b-fa-ticket-simple::before{
  content:"\f3ff";
}

.b-fa-ticket-alt::before{
  content:"\f3ff";
}

.b-fa-building::before{
  content:"\f1ad";
}

.b-fa-angles-left::before{
  content:"\f100";
}

.b-fa-angle-double-left::before{
  content:"\f100";
}

.b-fa-qrcode::before{
  content:"\f029";
}

.b-fa-clock-rotate-left::before{
  content:"\f1da";
}

.b-fa-history::before{
  content:"\f1da";
}

.b-fa-face-grin-beam-sweat::before{
  content:"\f583";
}

.b-fa-grin-beam-sweat::before{
  content:"\f583";
}

.b-fa-file-export::before{
  content:"\f56e";
}

.b-fa-arrow-right-from-file::before{
  content:"\f56e";
}

.b-fa-shield::before{
  content:"\f132";
}

.b-fa-shield-blank::before{
  content:"\f132";
}

.b-fa-arrow-up-short-wide::before{
  content:"\f885";
}

.b-fa-sort-amount-up-alt::before{
  content:"\f885";
}

.b-fa-house-medical::before{
  content:"\e3b2";
}

.b-fa-golf-ball-tee::before{
  content:"\f450";
}

.b-fa-golf-ball::before{
  content:"\f450";
}

.b-fa-circle-chevron-left::before{
  content:"\f137";
}

.b-fa-chevron-circle-left::before{
  content:"\f137";
}

.b-fa-house-chimney-window::before{
  content:"\e00d";
}

.b-fa-pen-nib::before{
  content:"\f5ad";
}

.b-fa-tent-arrow-turn-left::before{
  content:"\e580";
}

.b-fa-tents::before{
  content:"\e582";
}

.b-fa-wand-magic::before{
  content:"\f0d0";
}

.b-fa-magic::before{
  content:"\f0d0";
}

.b-fa-dog::before{
  content:"\f6d3";
}

.b-fa-carrot::before{
  content:"\f787";
}

.b-fa-moon::before{
  content:"\f186";
}

.b-fa-wine-glass-empty::before{
  content:"\f5ce";
}

.b-fa-wine-glass-alt::before{
  content:"\f5ce";
}

.b-fa-cheese::before{
  content:"\f7ef";
}

.b-fa-yin-yang::before{
  content:"\f6ad";
}

.b-fa-music::before{
  content:"\f001";
}

.b-fa-code-commit::before{
  content:"\f386";
}

.b-fa-temperature-low::before{
  content:"\f76b";
}

.b-fa-person-biking::before{
  content:"\f84a";
}

.b-fa-biking::before{
  content:"\f84a";
}

.b-fa-broom::before{
  content:"\f51a";
}

.b-fa-shield-heart::before{
  content:"\e574";
}

.b-fa-gopuram::before{
  content:"\f664";
}

.b-fa-earth-oceania::before{
  content:"\e47b";
}

.b-fa-globe-oceania::before{
  content:"\e47b";
}

.b-fa-square-xmark::before{
  content:"\f2d3";
}

.b-fa-times-square::before{
  content:"\f2d3";
}

.b-fa-xmark-square::before{
  content:"\f2d3";
}

.b-fa-hashtag::before{
  content:"\#";
}

.b-fa-up-right-and-down-left-from-center::before{
  content:"\f424";
}

.b-fa-expand-alt::before{
  content:"\f424";
}

.b-fa-oil-can::before{
  content:"\f613";
}

.b-fa-t::before{
  content:"T";
}

.b-fa-hippo::before{
  content:"\f6ed";
}

.b-fa-chart-column::before{
  content:"\e0e3";
}

.b-fa-infinity::before{
  content:"\f534";
}

.b-fa-vial-circle-check::before{
  content:"\e596";
}

.b-fa-person-arrow-down-to-line::before{
  content:"\e538";
}

.b-fa-voicemail::before{
  content:"\f897";
}

.b-fa-fan::before{
  content:"\f863";
}

.b-fa-person-walking-luggage::before{
  content:"\e554";
}

.b-fa-up-down::before{
  content:"\f338";
}

.b-fa-arrows-alt-v::before{
  content:"\f338";
}

.b-fa-cloud-moon-rain::before{
  content:"\f73c";
}

.b-fa-calendar::before{
  content:"\f133";
}

.b-fa-trailer::before{
  content:"\e041";
}

.b-fa-bahai::before{
  content:"\f666";
}

.b-fa-haykal::before{
  content:"\f666";
}

.b-fa-sd-card::before{
  content:"\f7c2";
}

.b-fa-dragon::before{
  content:"\f6d5";
}

.b-fa-shoe-prints::before{
  content:"\f54b";
}

.b-fa-circle-plus::before{
  content:"\f055";
}

.b-fa-plus-circle::before{
  content:"\f055";
}

.b-fa-face-grin-tongue-wink::before{
  content:"\f58b";
}

.b-fa-grin-tongue-wink::before{
  content:"\f58b";
}

.b-fa-hand-holding::before{
  content:"\f4bd";
}

.b-fa-plug-circle-exclamation::before{
  content:"\e55d";
}

.b-fa-link-slash::before{
  content:"\f127";
}

.b-fa-chain-broken::before{
  content:"\f127";
}

.b-fa-chain-slash::before{
  content:"\f127";
}

.b-fa-unlink::before{
  content:"\f127";
}

.b-fa-clone::before{
  content:"\f24d";
}

.b-fa-person-walking-arrow-loop-left::before{
  content:"\e551";
}

.b-fa-arrow-up-z-a::before{
  content:"\f882";
}

.b-fa-sort-alpha-up-alt::before{
  content:"\f882";
}

.b-fa-fire-flame-curved::before{
  content:"\f7e4";
}

.b-fa-fire-alt::before{
  content:"\f7e4";
}

.b-fa-tornado::before{
  content:"\f76f";
}

.b-fa-file-circle-plus::before{
  content:"\e494";
}

.b-fa-book-quran::before{
  content:"\f687";
}

.b-fa-quran::before{
  content:"\f687";
}

.b-fa-anchor::before{
  content:"\f13d";
}

.b-fa-border-all::before{
  content:"\f84c";
}

.b-fa-face-angry::before{
  content:"\f556";
}

.b-fa-angry::before{
  content:"\f556";
}

.b-fa-cookie-bite::before{
  content:"\f564";
}

.b-fa-arrow-trend-down::before{
  content:"\e097";
}

.b-fa-rss::before{
  content:"\f09e";
}

.b-fa-feed::before{
  content:"\f09e";
}

.b-fa-draw-polygon::before{
  content:"\f5ee";
}

.b-fa-scale-balanced::before{
  content:"\f24e";
}

.b-fa-balance-scale::before{
  content:"\f24e";
}

.b-fa-gauge-simple-high::before{
  content:"\f62a";
}

.b-fa-tachometer::before{
  content:"\f62a";
}

.b-fa-tachometer-fast::before{
  content:"\f62a";
}

.b-fa-shower::before{
  content:"\f2cc";
}

.b-fa-desktop::before{
  content:"\f390";
}

.b-fa-desktop-alt::before{
  content:"\f390";
}

.b-fa-m::before{
  content:"M";
}

.b-fa-table-list::before{
  content:"\f00b";
}

.b-fa-th-list::before{
  content:"\f00b";
}

.b-fa-comment-sms::before{
  content:"\f7cd";
}

.b-fa-sms::before{
  content:"\f7cd";
}

.b-fa-book::before{
  content:"\f02d";
}

.b-fa-user-plus::before{
  content:"\f234";
}

.b-fa-check::before{
  content:"\f00c";
}

.b-fa-battery-three-quarters::before{
  content:"\f241";
}

.b-fa-battery-4::before{
  content:"\f241";
}

.b-fa-house-circle-check::before{
  content:"\e509";
}

.b-fa-angle-left::before{
  content:"\f104";
}

.b-fa-diagram-successor::before{
  content:"\e47a";
}

.b-fa-truck-arrow-right::before{
  content:"\e58b";
}

.b-fa-arrows-split-up-and-left::before{
  content:"\e4bc";
}

.b-fa-hand-fist::before{
  content:"\f6de";
}

.b-fa-fist-raised::before{
  content:"\f6de";
}

.b-fa-cloud-moon::before{
  content:"\f6c3";
}

.b-fa-briefcase::before{
  content:"\f0b1";
}

.b-fa-person-falling::before{
  content:"\e546";
}

.b-fa-image-portrait::before{
  content:"\f3e0";
}

.b-fa-portrait::before{
  content:"\f3e0";
}

.b-fa-user-tag::before{
  content:"\f507";
}

.b-fa-rug::before{
  content:"\e569";
}

.b-fa-earth-europe::before{
  content:"\f7a2";
}

.b-fa-globe-europe::before{
  content:"\f7a2";
}

.b-fa-cart-flatbed-suitcase::before{
  content:"\f59d";
}

.b-fa-luggage-cart::before{
  content:"\f59d";
}

.b-fa-rectangle-xmark::before{
  content:"\f410";
}

.b-fa-rectangle-times::before{
  content:"\f410";
}

.b-fa-times-rectangle::before{
  content:"\f410";
}

.b-fa-window-close::before{
  content:"\f410";
}

.b-fa-baht-sign::before{
  content:"\e0ac";
}

.b-fa-book-open::before{
  content:"\f518";
}

.b-fa-book-journal-whills::before{
  content:"\f66a";
}

.b-fa-journal-whills::before{
  content:"\f66a";
}

.b-fa-handcuffs::before{
  content:"\e4f8";
}

.b-fa-triangle-exclamation::before{
  content:"\f071";
}

.b-fa-exclamation-triangle::before{
  content:"\f071";
}

.b-fa-warning::before{
  content:"\f071";
}

.b-fa-database::before{
  content:"\f1c0";
}

.b-fa-share::before{
  content:"\f064";
}

.b-fa-mail-forward::before{
  content:"\f064";
}

.b-fa-bottle-droplet::before{
  content:"\e4c4";
}

.b-fa-mask-face::before{
  content:"\e1d7";
}

.b-fa-hill-rockslide::before{
  content:"\e508";
}

.b-fa-right-left::before{
  content:"\f362";
}

.b-fa-exchange-alt::before{
  content:"\f362";
}

.b-fa-paper-plane::before{
  content:"\f1d8";
}

.b-fa-road-circle-exclamation::before{
  content:"\e565";
}

.b-fa-dungeon::before{
  content:"\f6d9";
}

.b-fa-align-right::before{
  content:"\f038";
}

.b-fa-money-bill-1-wave::before{
  content:"\f53b";
}

.b-fa-money-bill-wave-alt::before{
  content:"\f53b";
}

.b-fa-life-ring::before{
  content:"\f1cd";
}

.b-fa-hands::before{
  content:"\f2a7";
}

.b-fa-sign-language::before{
  content:"\f2a7";
}

.b-fa-signing::before{
  content:"\f2a7";
}

.b-fa-calendar-day::before{
  content:"\f783";
}

.b-fa-water-ladder::before{
  content:"\f5c5";
}

.b-fa-ladder-water::before{
  content:"\f5c5";
}

.b-fa-swimming-pool::before{
  content:"\f5c5";
}

.b-fa-arrows-up-down::before{
  content:"\f07d";
}

.b-fa-arrows-v::before{
  content:"\f07d";
}

.b-fa-face-grimace::before{
  content:"\f57f";
}

.b-fa-grimace::before{
  content:"\f57f";
}

.b-fa-wheelchair-move::before{
  content:"\e2ce";
}

.b-fa-wheelchair-alt::before{
  content:"\e2ce";
}

.b-fa-turn-down::before{
  content:"\f3be";
}

.b-fa-level-down-alt::before{
  content:"\f3be";
}

.b-fa-person-walking-arrow-right::before{
  content:"\e552";
}

.b-fa-square-envelope::before{
  content:"\f199";
}

.b-fa-envelope-square::before{
  content:"\f199";
}

.b-fa-dice::before{
  content:"\f522";
}

.b-fa-bowling-ball::before{
  content:"\f436";
}

.b-fa-brain::before{
  content:"\f5dc";
}

.b-fa-bandage::before{
  content:"\f462";
}

.b-fa-band-aid::before{
  content:"\f462";
}

.b-fa-calendar-minus::before{
  content:"\f272";
}

.b-fa-circle-xmark::before{
  content:"\f057";
}

.b-fa-times-circle::before{
  content:"\f057";
}

.b-fa-xmark-circle::before{
  content:"\f057";
}

.b-fa-gifts::before{
  content:"\f79c";
}

.b-fa-hotel::before{
  content:"\f594";
}

.b-fa-earth-asia::before{
  content:"\f57e";
}

.b-fa-globe-asia::before{
  content:"\f57e";
}

.b-fa-id-card-clip::before{
  content:"\f47f";
}

.b-fa-id-card-alt::before{
  content:"\f47f";
}

.b-fa-magnifying-glass-plus::before{
  content:"\f00e";
}

.b-fa-search-plus::before{
  content:"\f00e";
}

.b-fa-thumbs-up::before{
  content:"\f164";
}

.b-fa-user-clock::before{
  content:"\f4fd";
}

.b-fa-hand-dots::before{
  content:"\f461";
}

.b-fa-allergies::before{
  content:"\f461";
}

.b-fa-file-invoice::before{
  content:"\f570";
}

.b-fa-window-minimize::before{
  content:"\f2d1";
}

.b-fa-mug-saucer::before{
  content:"\f0f4";
}

.b-fa-coffee::before{
  content:"\f0f4";
}

.b-fa-brush::before{
  content:"\f55d";
}

.b-fa-mask::before{
  content:"\f6fa";
}

.b-fa-magnifying-glass-minus::before{
  content:"\f010";
}

.b-fa-search-minus::before{
  content:"\f010";
}

.b-fa-ruler-vertical::before{
  content:"\f548";
}

.b-fa-user-large::before{
  content:"\f406";
}

.b-fa-user-alt::before{
  content:"\f406";
}

.b-fa-train-tram::before{
  content:"\e5b4";
}

.b-fa-user-nurse::before{
  content:"\f82f";
}

.b-fa-syringe::before{
  content:"\f48e";
}

.b-fa-cloud-sun::before{
  content:"\f6c4";
}

.b-fa-stopwatch-20::before{
  content:"\e06f";
}

.b-fa-square-full::before{
  content:"\f45c";
}

.b-fa-magnet::before{
  content:"\f076";
}

.b-fa-jar::before{
  content:"\e516";
}

.b-fa-note-sticky::before{
  content:"\f249";
}

.b-fa-sticky-note::before{
  content:"\f249";
}

.b-fa-bug-slash::before{
  content:"\e490";
}

.b-fa-arrow-up-from-water-pump::before{
  content:"\e4b6";
}

.b-fa-bone::before{
  content:"\f5d7";
}

.b-fa-table-cells-row-unlock::before{
  content:"\e691";
}

.b-fa-user-injured::before{
  content:"\f728";
}

.b-fa-face-sad-tear::before{
  content:"\f5b4";
}

.b-fa-sad-tear::before{
  content:"\f5b4";
}

.b-fa-plane::before{
  content:"\f072";
}

.b-fa-tent-arrows-down::before{
  content:"\e581";
}

.b-fa-exclamation::before{
  content:"\!";
}

.b-fa-arrows-spin::before{
  content:"\e4bb";
}

.b-fa-print::before{
  content:"\f02f";
}

.b-fa-turkish-lira-sign::before{
  content:"\e2bb";
}

.b-fa-try::before{
  content:"\e2bb";
}

.b-fa-turkish-lira::before{
  content:"\e2bb";
}

.b-fa-dollar-sign::before{
  content:"\$";
}

.b-fa-dollar::before{
  content:"\$";
}

.b-fa-usd::before{
  content:"\$";
}

.b-fa-x::before{
  content:"X";
}

.b-fa-magnifying-glass-dollar::before{
  content:"\f688";
}

.b-fa-search-dollar::before{
  content:"\f688";
}

.b-fa-users-gear::before{
  content:"\f509";
}

.b-fa-users-cog::before{
  content:"\f509";
}

.b-fa-person-military-pointing::before{
  content:"\e54a";
}

.b-fa-building-columns::before{
  content:"\f19c";
}

.b-fa-bank::before{
  content:"\f19c";
}

.b-fa-institution::before{
  content:"\f19c";
}

.b-fa-museum::before{
  content:"\f19c";
}

.b-fa-university::before{
  content:"\f19c";
}

.b-fa-umbrella::before{
  content:"\f0e9";
}

.b-fa-trowel::before{
  content:"\e589";
}

.b-fa-d::before{
  content:"D";
}

.b-fa-stapler::before{
  content:"\e5af";
}

.b-fa-masks-theater::before{
  content:"\f630";
}

.b-fa-theater-masks::before{
  content:"\f630";
}

.b-fa-kip-sign::before{
  content:"\e1c4";
}

.b-fa-hand-point-left::before{
  content:"\f0a5";
}

.b-fa-handshake-simple::before{
  content:"\f4c6";
}

.b-fa-handshake-alt::before{
  content:"\f4c6";
}

.b-fa-jet-fighter::before{
  content:"\f0fb";
}

.b-fa-fighter-jet::before{
  content:"\f0fb";
}

.b-fa-square-share-nodes::before{
  content:"\f1e1";
}

.b-fa-share-alt-square::before{
  content:"\f1e1";
}

.b-fa-barcode::before{
  content:"\f02a";
}

.b-fa-plus-minus::before{
  content:"\e43c";
}

.b-fa-video::before{
  content:"\f03d";
}

.b-fa-video-camera::before{
  content:"\f03d";
}

.b-fa-graduation-cap::before{
  content:"\f19d";
}

.b-fa-mortar-board::before{
  content:"\f19d";
}

.b-fa-hand-holding-medical::before{
  content:"\e05c";
}

.b-fa-person-circle-check::before{
  content:"\e53e";
}

.b-fa-turn-up::before{
  content:"\f3bf";
}

.b-fa-level-up-alt::before{
  content:"\f3bf";
}

.sr-only,
.b-fa-sr-only{
  position:absolute;
  width:1px;
  height:1px;
  padding:0;
  margin:-1px;
  overflow:hidden;
  clip:rect(0, 0, 0, 0);
  white-space:nowrap;
  border-width:0;
}

.sr-only-focusable:not(:focus),
.b-fa-sr-only-focusable:not(:focus){
  position:absolute;
  width:1px;
  height:1px;
  padding:0;
  margin:-1px;
  overflow:hidden;
  clip:rect(0, 0, 0, 0);
  white-space:nowrap;
  border-width:0;
}

.b-fa{
  font-family:inherit;
  font-weight:inherit;
  line-height:inherit;
  -webkit-font-smoothing:inherit;
}

.b-fa:before{
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
}

.b-icon:before{
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
  display:inline-block;
  font-style:normal;
  font-variant:normal;
  font-weight:normal;
  line-height:1;
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  display:inline-flex;
  justify-content:center;
  align-items:center;
}

.b-fw-icon:before{
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
  display:inline-block;
  font-style:normal;
  font-variant:normal;
  font-weight:normal;
  line-height:1;
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  vertical-align:0;
}

.b-icon-add:before{
  content:"+";
}

.b-icon-remove:before{
  content:"\f00d";
}

.b-icon-clear:before{
  content:"\f00d";
}

.b-icon-close:before, .b-popup-close:before{
  content:"\f00d";
}

.b-icon-collapse-down:before{
  content:"\f078";
}

.b-icon-collapse-left:before{
  content:"\f053";
}

.b-icon-collapse-right:before{
  content:"\f054";
}

.b-icon-collapse-up:before{
  content:"\f077";
}

.b-icon-filter-disable:before{
  content:"\f05e";
}

.b-icon-search:before{
  content:"\f002";
}

.b-icon-search-plus:before{
  content:"\f00e";
}

.b-icon-search-minus:before{
  content:"\f010";
}

.b-icon-hide:before{
  content:"\f057";
}

.b-icon-trash:before{
  content:"\f1f8";
}

.b-icon-edit:before{
  content:"\f303";
}

.b-icon-rename:before{
  content:"\f044";
}

.b-icon-copy:before{
  content:"\f0c5";
}

.b-icon-cut:before{
  content:"\f0c4";
}

.b-icon-paste:before{
  content:"\f0ea";
}

.b-icon-expand-row:before{
  content:"\f107";
}

.b-icon-expand-column:before{
  content:"\f105";
}

.b-icon-expand, .b-popup-expand:before{
  content:"\f065";
}

.b-icon-first:before{
  content:"\f100";
}

.b-rtl > .b-icon-first:before{
  transform:scaleX(-1);
}

.b-icon-last:before{
  content:"\f101";
}

.b-rtl > .b-icon-last:before{
  transform:scaleX(-1);
}

.b-icon-reload:before{
  content:"\f01e";
}

.b-icon-undo:before{
  content:"\f0e2";
}

.b-icon-redo:before{
  content:"\f01e";
}

.b-icon-compare:before{
  content:"\e13a";
}

.b-icon-split-horizontal:before,
.b-icon-split-vertical:before,
.b-icon-split-both:before{
  min-width:1em !important;
  width:1em;
  height:1em;
  border-radius:2px;
  border:2px solid currentColor;
  overflow:hidden;
}

.b-icon-split-horizontal:before{
  content:"┃";
  transform:rotate(90deg);
  border-left-width:3px;
}

.b-icon-split-vertical:before{
  content:"┃";
  border-top-width:3px;
}

.b-icon-split-both:before{
  content:"╋";
  border-top-width:3px;
}

.b-icon-code:before{
  content:"\f121";
}

.b-icon-clipboard:before{
  content:"\f328";
}

.b-icon-filter:before,
.b-icon-filter-equal:before{
  content:"\f0b0";
}

.b-icon-filter-less:before,
.b-icon-filter-before:before{
  content:"\f053";
}

.b-icon-filter-more:before,
.b-icon-filter-after:before{
  content:"\f054";
}

.b-icon-check:before{
  content:"\f00c";
}

.b-icon-checked:before{
  content:"\f14a";
}

.b-icon-unchecked:before{
  content:"\f0c8";
}

.b-icon-radio:before{
  content:"\f111";
}

.b-icon-radio-checked:before{
  content:"\f192";
}

.b-icon-radio-unchecked:before{
  content:"\f111";
}

.b-icon-calendar:before{
  content:"\f133";
}

.b-icon-calendar-plus:before{
  content:"\f271";
}

.b-icon-calendar-day:before{
  content:"\f783";
}

.b-icon-clock:before{
  content:"\f017";
}

.b-icon-recurring:before{
  content:"\f021";
}

.b-icon-duration:before{
  content:"\f254";
}

.b-icon-milestone:before{
  content:"\f219";
}

.b-icon-locked:before{
  content:"\f023";
}

.b-icon-user:before{
  content:"\f007";
}

.b-icon-menu:before{
  content:"\f0c9";
}

.b-icon-menu-horizontal:before{
  content:"\f141";
}

.b-icon-menu-vertical:before{
  content:"\f142";
}

.b-icon-info:before{
  content:"\f129";
}

.b-icon-sub-menu:before{
  content:"\f054";
}

.b-icon-star:before{
  content:"\f005";
}

.b-icon-warning:before{
  content:"\f071";
}

.b-icon-columns:before{
  content:"\f0db";
}

.b-icon-picker:before{
  content:"\f0d7";
  transition:transform 0.2s;
}

.b-icon-picker-rotated:before{
  content:"\f0d7";
  transform:rotate(180deg);
  transition:transform 0.2s;
}

.b-icon-resize-horizontal:before{
  content:"\f337";
}

.b-icon-fullscreen:before{
  content:"\f0b2";
}

.b-icon-cog:before{
  content:"\f013";
}
.b-linux .b-icon-cog:before, .b-windows .b-icon-cog:before{
  vertical-align:middle;
}

.b-icon-download:before{
  content:"\f019";
}

.b-icon-file-download:before{
  content:"\f56d";
}

.b-icon-sync:before{
  content:"\f2f1";
}

.b-icon-bad-mood-emoji:before{
  content:"\f119";
}

.b-icon-circle:before{
  content:"\f111";
}

.b-icon-square:before{
  content:"\f0c8";
}

.b-icon-merge-cells:before{
  content:"\f5fd";
}

.b-icon-up:before{
  content:"\f062";
}

.b-icon-down:before{
  content:"\f063";
}

.b-icon-before:before,
.b-icon-left:before{
  content:"\f060";
}

.b-rtl .b-icon-before:before{
  transform:scaleX(-1);
}

.b-icon-after:before,
.b-icon-right:before{
  content:"\f061";
}

.b-rtl .b-icon-after:before{
  transform:scaleX(-1);
}

.b-icon-angle-left:before{
  content:"\f104";
}

.b-icon-angle-right:before{
  content:"\f105";
}

.b-icon-previous:before{
  content:"\f053";
}

.b-rtl > .b-icon-previous:before,
.b-rtl.b-icon-previous:before{
  transform:scaleX(-1);
}

.b-icon-next:before{
  content:"\f054";
}

.b-rtl > .b-icon-next:before,
.b-rtl.b-icon-next:before{
  transform:scaleX(-1);
}

.b-icon-move-left-right:before{
  content:"\f337";
}

.b-icon-move-up-down:before{
  content:"\f338";
}

.b-icon-spinner:before{
  content:"\f110";
  animation:rotate 2s infinite linear;
}

.b-icon-column-move-left:before{
  content:"\f0a8";
}

.b-icon-column-move-right:before{
  content:"\f0a9";
}

.b-icon-hide-column:before{
  content:"\f057";
}

.b-icon-sort:before{
  content:"\f0dc";
}

.b-icon-sort-asc:before{
  content:"\f15e";
}

.b-icon-sort-desc:before{
  content:"\f15d";
}

.b-icon-sorted-asc:before{
  content:"\f062";
}

.b-icon-group-asc:before{
  content:"\f885";
}

.b-icon-group-desc:before{
  content:"\f160";
}

.b-icon-group-collapse:before{
  content:"\f078";
}

.b-icon-group-expand:before{
  content:"\f054";
}

.b-icon-grouped-asc:before{
  content:"\f012";
}

.b-icon-tree-expand:before{
  content:"\f105";
}

.b-icon-tree-collapse:before{
  content:"\f107";
}

.b-icon-tree-folder:before{
  content:"\f07b";
}

.b-icon-tree-folder-open:before{
  content:"\f07c";
}

.b-icon-tree-leaf:before{
  content:"\f111";
}

.b-icon-expand-gridregion:before{
  content:"\f054";
}

.b-icon-collapse-gridregion:before{
  content:"\f053";
}

.b-icon-lock-row:before{
  content:"\e67a";
}

.b-icon-unlock-row:before{
  content:"\e691";
}

.b-icon-unassign:before{
  content:"\f506";
}

.b-icon-valid:before{
  content:"\f00c";
}

.b-icon-invalid:before{
  content:"\f05e";
}

.b-icon-checking:before{
  content:"\f110";
}

.b-icon-expand-resource:before{
  content:"\f103";
}

.b-icon-note:before{
  content:"\f249";
}

.b-icon-advanced:before{
  content:"\f085";
}

.b-icon-palette:before{
  content:"\f53f";
}

.b-icon-renumber:before{
  content:"\f884";
}

.b-icon-indent:before{
  content:"\f03c";
}

.b-icon-outdent:before{
  content:"\f03b";
}

.b-icon-subtask::before{
  content:"\e476";
}

.b-icon-predecessor::before{
  content:"\e477";
}

.b-icon-successor::before{
  content:"\e47a";
}

.b-icon-link:before{
  content:"\f0c1";
}

.b-icon-unlink:before{
  content:"\f127";
}

.b-icon-calendar-days:before{
  content:"\f073";
}

.b-icon-calendar-week:before{
  content:"\f784";
}
.b-noselect{
  -webkit-user-select:none;
  user-select:none;
}

:root, :host{
  --rtl-negate:1;
}

.b-rtl{
  --rtl-negate:-1;
}

.b-released,
.b-hide-display,
.b-theme-info{
  display:none !important;
}

.b-hide-visibility{
  visibility:hidden !important;
}

.b-hide-offscreen, .b-scroll-hidden{
  visibility:hidden !important;
  position:absolute !important;
  top:-10000em;
  left:-10000em;
}

.b-yscroll-pad{
  display:none;
}

.b-visible-scrollbar .b-show-yscroll-padding > .b-yscroll-pad{
  display:block;
  order:9999;
  border-inline-start:1px solid #ddd;
}
.b-visible-scrollbar .b-show-yscroll-padding > .b-yscroll-pad .b-yscroll-pad-sizer{
  overflow-x:hidden;
  overflow-y:scroll;
  visibility:hidden;
  margin-inline-start:-1px;
  height:0;
}

.b-fx-highlight{
  z-index:9999 !important;
  animation-name:b-fx-highlight-animation;
  animation-duration:1s;
  animation:b-fx-highlight-animation 1s ease 0s 1;
}

@keyframes b-fx-highlight-animation{
  0%{
    box-shadow:none;
  }
  50%{
    box-shadow:0 0 10px 5px rgba(253, 126, 20, 0.5);
  }
  100%{
    box-shadow:none;
  }
}
@keyframes b-slide-in-from-right{
  30%{
    transform:translateX(-30%);
    opacity:0.1;
  }
  30.01%{
    transform:translateX(30%);
  }
  100%{
    transform:translateX(0);
    opacity:1;
  }
}
@keyframes b-slide-in-from-left{
  30%{
    transform:translateX(30%);
    opacity:0.1;
  }
  30.01%{
    transform:translateX(-30%);
  }
  100%{
    transform:translateX(0);
    opacity:1;
  }
}
@keyframes b-slide-in-from-below{
  30%{
    transform:translateY(-30%);
    opacity:0.1;
  }
  30.01%{
    transform:translateY(30%);
  }
  100%{
    transform:translateY(0);
    opacity:1;
  }
}
@keyframes b-slide-in-from-above{
  30%{
    transform:translateY(30%);
    opacity:0.1;
  }
  30.01%{
    transform:translateY(-30%);
  }
  100%{
    transform:translateY(0);
    opacity:1;
  }
}
.b-slide-vertical.b-slide-in-next{
  animation:b-slide-in-from-below 0.3s ease 0s 1 normal;
}
.b-slide-vertical.b-slide-in-previous{
  animation:b-slide-in-from-above 0.3s ease 0s 1 normal;
}

:not(.b-slide-vertical).b-slide-in-next{
  animation:b-slide-in-from-right 0.3s ease 0s 1 normal;
}
:not(.b-slide-vertical).b-slide-in-previous{
  animation:b-slide-in-from-left 0.3s ease 0s 1 normal;
}

.b-rtl :not(.b-slide-vertical).b-slide-in-next{
  animation:b-slide-in-from-left 0.3s ease 0s 1 normal;
}
.b-rtl :not(.b-slide-vertical).b-slide-in-previous{
  animation:b-slide-in-from-right 0.3s ease 0s 1 normal;
}

.b-sliding-child-element{
  overflow:hidden !important;
  overflow:clip !important;
}
.b-no-resizeobserver.b-resize-monitored{
  position:relative;
}
.b-no-resizeobserver.b-resize-monitored .b-resize-monitors{
  position:absolute;
  left:0;
  top:0;
  width:100%;
  height:100%;
  visibility:hidden;
  overflow:hidden;
}
.b-no-resizeobserver.b-resize-monitored .b-resize-monitors > *{
  width:100%;
  height:100%;
  overflow:hidden;
}
.b-no-resizeobserver.b-resize-monitored .b-resize-monitors > .b-resize-monitor-expand:after{
  content:"";
  display:block;
  width:100000px;
  height:100000px;
}
.b-no-resizeobserver.b-resize-monitored .b-resize-monitors > .b-resize-monitor-shrink:after{
  content:"";
  display:block;
  width:200%;
  height:200%;
  min-width:1px;
  min-height:1px;
}

.b-float-root{
  position:fixed;
  inset:0;
  pointer-events:none;
  overflow:clip;
  z-index:11000;
  contain:strict;
  display:flex;
  justify-content:center;
  align-items:center;
}
.b-float-root.b-safari{
  -webkit-user-select:none;
  user-select:none;
}
.b-float-root > .b-floating{
  position:absolute;
  contain:layout style;
  pointer-events:all;
  transition:opacity 0.2s;
  box-shadow:2px 2px 6px rgba(0, 0, 0, 0.1);
  top:0;
  inset-inline-start:0;
  z-index:11001;
}
.b-firefox .b-float-root > .b-floating{
  contain:layout;
}
.b-float-root > .b-floating.b-hidden{
  opacity:0;
}
.b-float-root > .b-floating.b-aligned-above:not(.b-anchored){
  box-shadow:2px -2px 6px rgba(0, 0, 0, 0.1);
}
.b-gripper{
  position:absolute;
}
.b-gripper:after{
  content:" ";
  position:absolute;
  opacity:0;
}
.b-hover-top .b-gripper.b-gripper-horz, .b-hover-bottom .b-gripper.b-gripper-horz{
  cursor:ns-resize;
  height:1em;
  left:0;
  right:0;
}
.b-hover-bottom .b-gripper.b-gripper-horz{
  bottom:0;
}
.b-gripper.b-gripper-horz:after{
  border-top:1px solid rgba(0, 0, 0, 0.3);
  border-bottom:1px solid rgba(0, 0, 0, 0.3);
  width:0;
  height:0.25em;
  left:50%;
  margin-inline-start:0;
}
.b-hover-top .b-gripper.b-gripper-horz:after{
  top:0.4em;
}
.b-hover-bottom .b-gripper.b-gripper-horz:after{
  bottom:0.4em;
}
.b-hover-anim.b-hover-edge .b-gripper.b-gripper-horz:after{
  transition:opacity 0.2s, margin-inline-start 0.2s, width 0.2s;
  opacity:1;
  margin-inline-start:-0.6em;
  width:1.2em;
}
.b-hover-left .b-gripper.b-gripper-vert, .b-hover-right .b-gripper.b-gripper-vert{
  cursor:ew-resize;
  width:1em;
  top:0;
  bottom:0;
}
.b-hover-right .b-gripper.b-gripper-vert{
  right:0;
}
.b-hover-left .b-gripper.b-gripper-vert{
  left:0;
}
.b-gripper.b-gripper-vert:after{
  background:repeating-linear-gradient(90deg, rgba(0, 0, 0, 0.5), rgba(255, 255, 255, 0.8) 3px);
  height:0;
  width:0.5em;
  top:50%;
  margin-top:0;
}
.b-hover-left .b-gripper.b-gripper-vert:after{
  left:4px;
}
.b-hover-right .b-gripper.b-gripper-vert:after{
  right:4px;
}
.b-hover-anim.b-hover-edge .b-gripper.b-gripper-vert:after{
  transition:opacity 0.2s, margin-top 0.2s, height 0.2s;
  opacity:1;
  margin-top:-6px;
  height:12px;
}

.b-dragging{
  z-index:100;
  pointer-events:none !important;
  opacity:0.8;
  box-sizing:border-box;
}
.b-dragging.b-drag-unified-proxy{
  opacity:0.65;
}
.b-dragging.b-drag-main{
  z-index:101;
  opacity:1;
}
.b-drag-proxy{
  position:absolute !important;
  top:0;
  left:0;
  pointer-events:none !important;
  box-sizing:border-box;
}
.b-drag-proxy *{
  box-sizing:border-box;
}

.b-aborting,
.b-drag-final-transition{
  transition-duration:0.3s;
  transition-property:all !important;
}

.b-drag-unified-animation{
  transition:transform 0.2s;
}

.b-drag-original.b-hidden{
  display:none !important;
}

.b-draghelper-active *{
  -webkit-user-select:none;
  user-select:none;
}

.simulated-mouse{
  position:absolute;
  z-index:100;
  top:10px;
  left:10px;
  transition:top 0.5s, left 0.5s;
  pointer-events:none;
  font-size:16px;
}
.simulated-mouse.quick{
  transition:top 0.05s, left 0.05s;
}
.simulated-mouse:after{
  position:absolute;
  content:"\f245";
  font-family:"Font Awesome 6 Free";
  font-weight:900;
  z-index:102;
}
.simulated-mouse.drag:before, .simulated-mouse.mousedown:before, .simulated-mouse.dblclick:before, .simulated-mouse.click:before{
  position:absolute;
  content:"";
  border:2px solid transparent;
  border-radius:50%;
  animation-name:click;
  animation-duration:0.2s;
  top:0;
  left:0;
  transform:translate(-50%, -50%);
  z-index:101;
  opacity:0.7;
}
.simulated-mouse.drag:after{
  content:"\f25a";
  left:-3px;
}
.simulated-mouse.dblclick:before{
  animation-name:dblclick;
  animation-duration:0.3s;
}
.simulated-mouse.mousedown:before, .simulated-mouse.drag:before{
  animation-name:none;
  width:1.5em;
  height:1.5em;
  border-color:red;
}

@keyframes click{
  0%{
    width:0;
    height:0;
  }
  90%{
    width:1.5em;
    height:1.5em;
    border-color:red;
  }
  100%{
    width:0;
    height:0;
    border-color:transparent;
  }
}
@keyframes dblclick{
  0%{
    width:0;
    height:0;
  }
  40%{
    width:1.5em;
    height:1.5em;
    border-color:red;
  }
  50%{
    width:0;
    height:0;
  }
  90%{
    width:1.5em;
    height:1.5em;
    border-color:red;
  }
  100%{
    width:0;
    height:0;
    border-color:transparent;
  }
}
.b-scroll-hidden{
  transform:translate(0, 0) !important;
}

.b-infinity-scroller{
  overflow:scroll;
}
.b-infinity-scroller.b-infinity-scroller-smooth{
  scroll-behavior:smooth;
}

.b-infinity-scroller-item{
  position:absolute !important;
}
.b-widget{
  --color-indigo:#3f51b5;
  --color-blue:#64b5f6;
  --color-cyan:#3bc9db;
  --color-red:#ef9a9a;
  --color-deep-orange:#ff5722;
  --color-orange:#ffcc80;
  --color-amber:#ffd54f;
  --color-yellow:#fff176;
  --color-green:#a5d6a7;
  --color-teal:#009688;
  --color-light-green:#8bc34a;
  --color-lime:#cddc39;
  --color-purple:#9c27b0;
  --color-pink:#f783ac;
  --color-violet:#9775fa;
  --color-gray:#cccccc;
  --color-light-gray:#f9f9f9;
  --color-dark-gray:#757575;
  --color-white:#fff;
  --color-black:#000;
  display:inline-flex;
  overflow:hidden;
  line-height:initial;
  font-weight:400;
  font-size:1em;
  box-sizing:border-box;
  color-scheme:light;
}
.b-widget.b-measure{
  display:block !important;
}
.b-widget.b-anchored{
  overflow:visible;
}
.b-widget.b-maximized{
  position:fixed;
  transform:none !important;
  width:100% !important;
  height:100% !important;
  max-width:100% !important;
  max-height:100% !important;
  top:0 !important;
  inset-inline-start:0 !important;
}
.b-widget.b-maximized.b-mobile .b-bottom-toolbar{
  order:-1;
  min-height:3.5em;
}
.b-widget.b-maximized.b-mobile .b-bottom-toolbar .b-toolbar-content{
  padding-block:0.5em 0;
  padding-inline-start:1em !important;
}
.b-widget *, .b-widget:before, .b-widget:after,
.b-widget *:before,
.b-widget *:after{
  box-sizing:border-box;
}
.b-widget.b-positioned{
  position:absolute;
  top:0;
  inset-inline-start:0;
}
.b-widget.b-positionable{
  position:absolute;
}
.b-widget.b-floating.b-centered:not(.b-maximized), .b-widget.b-positioned.b-centered:not(.b-maximized){
  inset-inline-start:50%;
  top:50%;
  transform:translate(-50%, -50%);
}
.b-widget > .b-focus-trap{
  position:absolute;
  display:none;
  clip:rect(0, 0, 0, 0);
}
.b-widget.b-focus-trapped.b-contains-focus > .b-focus-trap{
  display:inherit;
}
.b-widget.b-hidden.b-hide-mode-clip{
  clip:rect(0, 0, 0, 0) !important;
}
.b-widget.b-hidden.b-hide-mode-opacity{
  opacity:0 !important;
  pointer-events:none;
}
.b-widget.b-hidden.b-hide-mode-display{
  display:none !important;
}
.b-widget.b-hiding{
  pointer-events:none;
}
.b-widget .b-aria-desc-element{
  position:absolute;
  clip-path:polygon(0 0);
  contain:strict;
}
.b-widget .b-widget-inner{
  width:100%;
  position:relative;
  flex:1;
}
.b-widget .b-anchor{
  position:absolute;
  z-index:-1;
}
.b-widget .b-anchor svg{
  width:16px;
  height:8px;
  position:absolute;
  overflow:visible;
}
.b-widget .b-anchor.b-anchor-top, .b-widget .b-anchor.b-anchor-bottom{
  inset-inline-start:0;
  height:8px;
  width:100%;
}
.b-widget .b-anchor.b-anchor-top{
  bottom:calc(100% - 1px);
}
.b-widget .b-anchor.b-anchor-top svg{
  bottom:0;
}
.b-widget .b-anchor.b-anchor-bottom{
  top:calc(100% - 1px);
}
.b-widget .b-anchor.b-anchor-bottom svg{
  top:0;
  rotate:180deg;
}
.b-widget .b-anchor.b-anchor-start, .b-widget .b-anchor.b-anchor-end{
  top:0;
  width:8px;
  height:100%;
}
.b-widget .b-anchor.b-anchor-start{
  inset-inline-end:calc(100% - 1px);
}
.b-widget .b-anchor.b-anchor-start svg{
  right:0;
  width:8px;
  height:16px;
  rotate:270deg;
  transform-origin:8px 8px;
}
.b-widget .b-anchor.b-anchor-end{
  inset-inline-end:-8px;
}
.b-widget .b-anchor.b-anchor-end svg{
  rotate:90deg;
  width:8px;
  height:16px;
  transform-origin:4px 4px;
}
.b-widget.b-sub-menu .b-anchor.b-anchor-start, .b-widget.b-sub-menu .b-anchor.b-anchor-end{
  width:9px;
}

.b-slidingcontent-content{
  display:flex;
  flex-flow:row nowrap !important;
  align-items:stretch;
  scroll-snap-type:x mandatory;
}
.b-slidingcontent-content .b-carousel-item{
  flex:1 0 100%;
}

.b-widget-scroller{
  overflow:hidden;
  -webkit-overflow-scrolling:touch;
  overflow-anchor:none;
  position:relative;
}

.b-hide-scroll{
  scrollbar-width:none;
}
.b-hide-scroll::-webkit-scrollbar{
  display:none;
}

.b-scroller-stretcher{
  position:absolute;
  height:1px;
  width:1px;
  top:0;
  inset-inline-start:0;
}

.b-text-align-start,
.b-text-align-left{
  text-align:start;
}

.b-text-align-center{
  text-align:center;
}

.b-text-align-end,
.b-text-align-right{
  text-align:end;
}

.b-rtl{
  direction:rtl;
}
.b-rtl .b-anchor.b-anchor-start svg{
  rotate:90deg;
  transform-origin:4px 4px;
}
.b-rtl .b-anchor.b-anchor-end svg{
  rotate:270deg;
  transform-origin:8px 8px;
}

.b-ltr{
  direction:ltr;
}

.b-firefox .b-anchor-end .b-pointer-el{
  margin-left:1px;
}
.b-widget.b-badge{
  overflow:visible;
}

.b-badge::before{
  content:attr(data-badge);
  position:absolute;
  right:-1em;
  top:-1em;
  width:0;
  height:0;
  overflow:visible;
  padding:1em;
  background-color:#ef9a9a;
  color:#616161;
  border-radius:50%;
  font-size:0.6em;
  z-index:5;
  display:flex;
  justify-content:center;
  align-items:center;
  animation-name:b-badge-show;
  animation-duration:0.3s;
}

.b-badge.b-rtl::before{
  left:-1em;
  right:auto;
}

.b-button.b-badge:before{
  font-size:0.7em;
}

@keyframes b-badge-show{
  0%{
    opacity:0;
    transform:scale(0.1) rotate(180deg);
  }
  100%{
    opacity:1;
    transform:scale(1) rotate(0deg);
  }
}
.b-button{
  --widget-primary-color-rgb:143,143,143;
  --button-background-opacity:0.05;
  --button-border-opacity:0.3;
  --button-text-color:var(--widget-primary-color-rgb);
  --button-text-opacity:1;
  --button-hover-background-opacity:var(--button-border-opacity);
  --button-pressed-text-color:255, 255, 255;
  --button-pressed-background-opacity:0.8;
  --button-pressed-hover-background-opacity:0.7;
  --button-pressed-disabled-background-opacity:0.1;
  --button-active-background-opacity:1;
  --button-focus-background-opacity:0.3;
  --button-pressed-focus-background-opacity:1;
  --button-disabled-background-opacity:0;
  --button-disabled-text-opacity:0.3;
  --button-disabled-border-opacity:0.2;
  --button-transparent-background-opacity:0;
  --button-transparent-text-opacity:1;
  --button-transparent-pressed-text-opacity:1;
  --button-transparent-hover-background-opacity:0.1;
  position:relative;
  padding:0 1em;
  min-height:3em;
  cursor:pointer;
  font-family:inherit;
  font-weight:400;
  transition:background-color 200ms, color 200ms, border 200ms;
  white-space:nowrap;
  display:inline-flex;
  align-items:center;
  justify-content:center;
  text-transform:none;
  border:1px solid rgba(var(--widget-primary-color-rgb), var(--button-border-opacity));
  background-color:rgba(var(--widget-primary-color-rgb), var(--button-background-opacity));
  color:rgba(var(--button-text-color), var(--button-text-opacity));
  -webkit-user-select:none;
  user-select:none;
}
.b-button.b-text{
  min-width:3.3em;
}
.b-button.b-transparent{
  --button-background-opacity:var(--button-transparent-background-opacity);
  --button-text-opacity:var(--button-transparent-text-opacity);
  border:none;
}
.b-button.b-pressed{
  --button-background-opacity:var(--button-pressed-background-opacity);
  --button-text-color:var(--button-pressed-text-color);
}
.b-button.b-pressed.b-transparent{
  --button-text-color:var(--widget-primary-color-rgb);
  --button-background-opacity:var(--button-transparent-background-opacity);
  --button-text-opacity:var(--button-transparent-pressed-text-opacity);
}
.b-button.b-pressed:hover:not(.b-disabled){
  --button-background-opacity:var(--button-pressed-hover-background-opacity);
}
.b-button.b-pressed:hover:not(.b-disabled).b-transparent{
  --button-background-opacity:var(--button-transparent-hover-background-opacity);
}
.b-button.b-pressed[data-group]:not(.b-disabled):hover{
  --button-background-opacity:var(--button-pressed-hover-background-opacity);
}
.b-button:hover:not(.b-disabled){
  --button-background-opacity:var(--button-hover-background-opacity);
}
.b-button:hover:not(.b-disabled).b-transparent{
  --button-background-opacity:var(--button-transparent-hover-background-opacity);
}
.b-button:active:not(.b-disabled), .b-button:active:focus:not(.b-disabled){
  --button-background-opacity:var(--button-active-background-opacity);
  --button-text-color:var(--button-pressed-text-color);
}
.b-using-keyboard .b-button:focus, .b-button.b-using-keyboard:focus{
  --button-background-opacity:var(--button-focus-background-opacity);
}
.b-using-keyboard .b-button:focus.b-pressed:not(.b-transparent), .b-button.b-using-keyboard:focus.b-pressed:not(.b-transparent){
  --button-background-opacity:var(--button-pressed-focus-background-opacity);
}
.b-button.b-disabled{
  --button-background-opacity:var(--button-disabled-background-opacity);
  --button-text-opacity:var(--button-disabled-text-opacity);
  --button-border-opacity:var(--button-disabled-border-opacity);
}
.b-button.b-disabled.b-pressed{
  --button-background-opacity:var(--button-pressed-disabled-background-opacity);
}
.b-button:not(.b-tab){
  border-radius:2px;
}
.b-button::-moz-focus-inner{
  border:0;
}
.b-button label{
  cursor:pointer;
  overflow:hidden;
  text-overflow:ellipsis;
}
.b-button.b-rotate-vertical{
  min-height:3em;
  min-width:3em;
  padding:1em 0 1em 0;
}
.b-button.b-rotate-vertical label{
  -webkit-writing-mode:vertical-lr;
  -ms-writing-mode:tb-lr;
  writing-mode:vertical-lr;
}
.b-button.b-rotate-left{
  flex-direction:column-reverse;
}
.b-button.b-rotate-left label{
  transform:rotate(180deg);
}
.b-button.b-rotate-left i{
  transform:rotate(270deg);
}
.b-button.b-rotate-right{
  flex-direction:column;
}
.b-button.b-rotate-right i{
  transform:rotate(90deg);
}
.b-button.b-icon-align-start i:not(.b-button-menu-icon){
  order:-100;
}
.b-button.b-icon-align-start:not(.b-rotate-vertical) label{
  margin-inline-start:0.5em;
}
.b-button.b-icon-align-start.b-rotate-right label{
  margin-inline-end:0.5em;
}
.b-button.b-icon-align-start.b-rotate-left label{
  margin-inline-start:0.5em;
}
.b-button.b-icon-align-end i{
  order:100;
}
.b-button.b-icon-align-end:not(.b-rotate-vertical) label{
  margin-inline-end:0.5em;
}
.b-button.b-icon-align-end.b-rotate-right label{
  margin-inline-start:0.5em;
}
.b-button.b-icon-align-end.b-rotate-left label{
  margin-inline-end:0.5em;
}
.b-button .b-button-menu-icon{
  margin-inline-start:0.5em;
}
.b-button i{
  font-size:1em;
}
.b-button label:empty{
  display:none;
}
.b-button.b-badge{
  overflow:visible !important;
}
.b-button.b-rounded{
  border-radius:50%;
  width:3em;
  height:3em;
}
.b-button:focus{
  outline:none;
}
.b-button:disabled{
  cursor:default;
}
.b-button.b-borderless{
  border:none;
}

.b-button.b-raised{
  --button-background-opacity:1;
  --button-focus-background-opacity:0.7;
  --button-hover-background-opacity:0.8;
  --button-pressed-hover-background-opacity:1;
  color:#fff;
  border:none;
}
.b-button.b-raised:active{
  box-shadow:inset 0 0 5px rgba(0, 0, 0, 0.3);
}
.b-button.b-raised.b-pressed{
  box-shadow:inset 0 0 5px rgba(0, 0, 0, 0.3);
  background-image:linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2));
}
.b-using-keyboard .b-button.b-raised.b-pressed:focus, .b-button.b-raised.b-pressed.b-using-keyboard:focus{
  background-image:linear-gradient(rgba(0, 0, 0, 0.0666666667), rgba(0, 0, 0, 0.0666666667));
}
.b-button.b-raised.b-disabled{
  --widget-primary-color-rgb:249,249,249;
}

.b-popup .b-bottom-toolbar .b-button.b-text{
  min-width:8em;
}

a.b-button{
  text-decoration:none;
}
.b-container{
  --autocontainer-gap:0.6em;
}
.b-container.b-outer, .b-auto-container:not(.b-toolbar-content).b-single-child > .b-container{
  align-self:stretch;
  flex:1 1 auto;
}

body.b-container.b-outer{
  margin:0;
  height:100%;
  width:100%;
  overflow:hidden;
}

.b-container:focus{
  outline:none;
}

.b-auto-container{
  gap:var(--autocontainer-gap);
}

.b-content-element{
  position:relative;
  display:flex;
  flex-flow:row wrap;
}
.b-content-element.b-flex-row{
  align-content:flex-start;
}
.b-content-element.b-text-content{
  display:block;
  overflow:auto;
}
.b-content-element > .b-field.b-half-width{
  flex:0 0 calc(50% - var(--autocontainer-gap) / 2);
}
.b-toolbar{
  --toolbar-min-size:4em;
  --toolbar-content-padding:0.5em;
  background-color:#f9f9f9;
  color:#616161;
  flex-shrink:0;
}
.b-toolbar.b-dock-header, .b-toolbar.b-dock-pre-header{
  background-color:transparent;
}
.b-toolbar.b-dock-header .b-toolbar-content, .b-toolbar.b-dock-pre-header .b-toolbar-content{
  padding:0;
}
.b-toolbar.b-dock-header > .b-button, .b-toolbar.b-dock-pre-header > .b-button{
  margin:0;
}
.b-toolbar.b-dock-header .b-button, .b-toolbar.b-dock-pre-header .b-button{
  min-height:auto;
}
.b-toolbar.b-dock-header{
  margin-inline-start:1em;
}
.b-toolbar.b-dock-pre-header{
  margin-inline-end:1em;
}
.b-toolbar > .b-tool{
  align-self:center;
  background-color:transparent;
}
.b-toolbar > .b-tool.b-pressed{
  background-color:rgba(50, 50, 50, 0.2);
}
.b-toolbar .b-button:not(.b-text){
  flex-shrink:0;
}
.b-toolbar .b-row-start-scroller{
  margin:0;
  margin-inline-end:0.5em;
}
.b-toolbar.b-dock-left .b-row-start-scroller{
  margin:0;
  margin-inline-start:0.5em;
}
.b-toolbar.b-dock-right .b-row-start-scroller{
  margin:0;
  margin-inline-start:0.5em;
}
.b-toolbar .b-row-end-scroller, .b-toolbar .b-row-menu{
  margin:0;
  margin-inline-start:0.5em;
}
.b-toolbar .b-column-start-scroller{
  margin:0 0 0.5em 0;
}
.b-toolbar .b-column-end-scroller, .b-toolbar .b-column-menu{
  margin:0.5em 0 0 0;
}
.b-toolbar > .b-overflow-button{
  margin:0.5em;
}
.b-toolbar.b-rtl > .b-overflow-button::before{
  transform:scaleX(-1);
}
.b-toolbar.b-dock-bottom{
  --toolbar-min-size:3em;
  --toolbar-content-padding:1em;
}
.b-toolbar .b-toolbar-content{
  overflow:hidden;
  padding:var(--toolbar-content-padding);
  gap:0.5em;
}
.b-toolbar .b-toolbar-content .b-field{
  margin-bottom:0;
}
.b-toolbar .b-toolbar-content > .b-toolbar-fill{
  flex:1 1 0%;
}
.b-toolbar .b-toolbar-content .b-html{
  align-items:center;
}
.b-toolbar .b-toolbar-content.b-overflow .b-field, .b-toolbar .b-toolbar-content.b-overflow .b-button{
  flex-shrink:0;
}
.b-toolbar.b-dock-top .b-toolbar-content, .b-toolbar.b-dock-bottom .b-toolbar-content{
  min-height:var(--toolbar-min-size);
}
.b-toolbar.b-dock-top .b-toolbar-content > .b-toolbar-separator, .b-toolbar.b-dock-bottom .b-toolbar-content > .b-toolbar-separator{
  margin-block:0.2em;
  border-style:solid;
  border-width:0 1px 0 1px;
  border-inline-start-color:#e0e0e0;
  border-inline-end-color:#ececec;
}
.b-toolbar.b-dock-top .b-toolbar-content > .b-widget:not(.b-button), .b-toolbar.b-dock-bottom .b-toolbar-content > .b-widget:not(.b-button){
  min-height:0;
}
.b-toolbar.b-dock-top{
  border-bottom:1px solid #e0e0e0;
}
.b-toolbar.b-dock-left .b-toolbar-content, .b-toolbar.b-dock-right .b-toolbar-content{
  min-width:var(--toolbar-min-size);
}
.b-toolbar.b-dock-left .b-toolbar-content > .b-toolbar-separator, .b-toolbar.b-dock-right .b-toolbar-content > .b-toolbar-separator{
  margin:0 0.5em 0.2em 0.2em;
  border-style:solid;
  border-width:1px 0 1px 0;
  border-top-color:#e0e0e0;
  border-bottom-color:#ececec;
}
.b-toolbar.b-dock-left .b-toolbar-content > .b-widget:not(.b-button), .b-toolbar.b-dock-right .b-toolbar-content > .b-widget:not(.b-button){
  min-width:0;
}

.b-tabpanel .b-panel .b-toolbar{
  background-color:#fefefe;
}

.b-toolbar.b-outer{
  background-color:#f9f9f9;
}

.b-toolbar-overflow-menu > .b-panel-body-wrap > .b-menu-content .b-widget{
  flex:0 0 auto !important;
  width:auto;
}

.b-pagingtoolbar .b-toolbar-content{
  justify-content:center;
}
.b-pagingtoolbar .b-toolbar-content .b-numberfield{
  width:unset;
}
.b-pagingtoolbar .b-toolbar-content .b-numberfield input{
  width:4em;
  text-align:center;
}
.b-pagingtoolbar .b-toolbar-content .b-disabled.b-html{
  color:#cccccc;
}
.b-buttongroup{
  overflow:visible;
  flex-shrink:0;
}
.b-buttongroup.b-content-element{
  display:inline-flex;
  flex-flow:row nowrap;
  gap:0;
}
.b-buttongroup.b-content-element.b-rotate-vertical{
  flex-direction:column;
}
.b-buttongroup.b-content-element.b-invalid{
  border-style:solid;
  border-width:1px;
  border-color:#ef9a9a;
}
.b-buttongroup .b-button{
  margin:0;
}
.b-buttongroup .b-button:not(.b-pressed){
  box-shadow:none !important;
}
.b-buttongroup .b-button.b-pressed, .b-buttongroup .b-button:active{
  z-index:2;
}
.b-buttongroup.b-columned.b-content-element{
  flex:1;
  flex-wrap:wrap;
}
.b-buttongroup.b-columned.b-content-element .b-button{
  margin-inline-start:0;
}
.b-buttongroup.b-no-gap .b-button:not(.b-first-visible-child){
  margin-inline-start:-1px;
}
.b-buttongroup.b-no-gap .b-button:not(.b-first-visible-child):not(.b-last-visible-child){
  border-radius:0;
}
.b-buttongroup.b-no-gap .b-button.b-first-visible-child:not(.b-last-visible-child){
  border-top-right-radius:0;
  border-bottom-right-radius:0;
}
.b-buttongroup.b-no-gap .b-button.b-last-visible-child:not(.b-first-visible-child){
  border-top-left-radius:0;
  border-bottom-left-radius:0;
}
.b-buttongroup.b-no-gap.b-rtl .b-button.b-first-visible-child:not(.b-last-visible-child){
  border-top-left-radius:0;
  border-bottom-left-radius:0;
}
.b-buttongroup.b-no-gap.b-rtl .b-button.b-last-visible-child:not(.b-first-visible-child){
  border-top-right-radius:0;
  border-bottom-right-radius:0;
}
.b-editor{
  background-color:#fff;
  border-radius:2px;
}
.b-editor.b-positioned:not(.b-hidden){
  box-shadow:0 0 6px rgba(0, 0, 0, 0.1);
}
.b-editor.b-content-element > .b-widget.b-field{
  margin:0;
}
@keyframes b-field-updated{
  0%{
    color:#262626;
  }
  25%{
    color:#737373;
  }
  75%{
    color:#737373;
  }
  100%{
    color:#262626;
  }
}
.b-field label{
  user-select:none;
}

.b-has-label.b-label-above{
  flex-direction:column;
}
.b-has-label.b-label-before .b-label{
  align-self:center;
  margin-inline-end:1em;
}
.b-has-label.b-label-above .b-label{
  align-self:flex-start;
  flex:none !important;
  margin:0 0 0.4em 0.1em;
}
.b-has-label.b-open .b-label, .b-has-label:focus-within .b-label{
  color:#262626;
}
.b-container .b-has-label .b-label.b-align-end{
  margin-inline-start:1em;
  text-align:left;
}
.b-container .b-has-label.b-open .b-label, .b-container .b-has-label:focus-within .b-label{
  color:#212121;
}
.b-has-label.b-invalid .b-label{
  color:#ef9a9a !important;
}
.b-has-label.b-disabled .b-label{
  color:#cccccc;
}

.b-field-updated{
  animation-name:b-field-updated;
  animation-duration:0.5s;
}

.b-vbox.b-box-justify-stretch > .b-field,
.b-flex-column > .b-field{
  width:auto;
}

.b-flex-row > .b-field{
  align-self:flex-start;
  flex:1 0 100%;
}

.b-vbox > .b-field.b-label-above{
  margin-bottom:1.2em;
}

.b-field-inner{
  display:flex;
  flex:1 1 100%;
  align-items:center;
  background-color:#fff;
  border-radius:2px;
  border-width:1px;
  border-style:solid;
  border-color:rgba(189, 189, 189, 0.5);
  min-width:0;
  position:relative;
}
.b-field-container-inline > .b-field-inner{
  flex:0 1 auto;
}
.b-safari .b-field-inner{
  margin-block-end:0.2px;
}

.b-field-container:not(.b-field-container-inline){
  flex-wrap:wrap;
}

.b-field-container-wrap{
  display:flex;
  flex:1 1 auto;
  overflow:hidden;
  position:relative;
}
.b-field-container-wrap > .b-container{
  width:100%;
}
.b-field.b-collapsed:not(.b-field-container-inline) > .b-field-container-wrap{
  height:0;
}
.b-field.b-collapsed.b-field-container-inline > .b-field-container-wrap{
  opacity:0;
}

.b-field.b-collapsed:not(.b-field-container-inline) > .b-field-container-wrap > .b-container, .b-field.b-collapsing:not(.b-field-container-inline) > .b-field-container-wrap > .b-container{
  position:absolute;
  bottom:0;
}

.b-field-container-inline:not(.b-no-input) > .b-field-container-wrap{
  margin-inline-start:1em;
}

.b-field-hint{
  align-self:stretch;
  overflow:visible;
  pointer-events:none;
  position:relative;
  white-space:nowrap;
  width:0;
}
.b-field-hint .b-field-hint-content{
  position:absolute;
  inset-inline-end:0.6em;
  top:50%;
  transform:translateY(-50%);
  font-size:0.9em;
}
.b-legacy-inset .b-field-hint .b-field-hint-content{
  right:0.6em;
}
.b-legacy-inset .b-field-hint .b-field-hint-content.b-rtl{
  left:0.6em;
}
.b-field-no-hint .b-field-hint{
  display:none;
}
.b-safari .b-field input[autocomplete=off]::-webkit-contacts-auto-fill-button{
  visibility:hidden;
}

.b-numberfield,
.b-textareafield,
.b-textfield{
  align-items:stretch;
  min-width:3em;
  color:#262626;
  border-radius:2px;
  position:relative;
  width:12.5em;
}
.b-numberfield.b-has-width,
.b-textareafield.b-has-width,
.b-textfield.b-has-width{
  width:auto;
}
.b-numberfield.b-contains-focus .b-field-inner,
.b-textareafield.b-contains-focus .b-field-inner,
.b-textfield.b-contains-focus .b-field-inner{
  border-color:#ffcc80;
}
.b-numberfield .b-fieldtrigger,
.b-textareafield .b-fieldtrigger,
.b-textfield .b-fieldtrigger{
  color:rgba(189, 189, 189, 0.5);
  cursor:pointer;
  flex:0 0 auto;
  align-items:center;
}
.b-numberfield .b-fieldtrigger.b-align-start,
.b-textareafield .b-fieldtrigger.b-align-start,
.b-textfield .b-fieldtrigger.b-align-start{
  padding-inline:0.8em 0;
}
.b-numberfield .b-fieldtrigger.b-align-end,
.b-textareafield .b-fieldtrigger.b-align-end,
.b-textfield .b-fieldtrigger.b-align-end{
  padding-inline:0 0.8em;
}
.b-numberfield .b-fieldtrigger:before,
.b-textareafield .b-fieldtrigger:before,
.b-textfield .b-fieldtrigger:before{
  font-size:1.3em;
  transition:transform 0.3s;
}
.b-numberfield .b-spintrigger,
.b-textareafield .b-spintrigger,
.b-textfield .b-spintrigger{
  flex-direction:column;
  font-size:1em;
}
.b-numberfield .b-spintrigger .b-spin-up:before,
.b-textareafield .b-spintrigger .b-spin-up:before,
.b-textfield .b-spintrigger .b-spin-up:before{
  content:"\f0d8";
  vertical-align:bottom;
}
.b-numberfield .b-spintrigger .b-spin-down:before,
.b-textareafield .b-spintrigger .b-spin-down:before,
.b-textfield .b-spintrigger .b-spin-down:before{
  content:"\f0d7";
  vertical-align:top;
}
.b-numberfield.b-hide-spinner .b-spintrigger,
.b-textareafield.b-hide-spinner .b-spintrigger,
.b-textfield.b-hide-spinner .b-spintrigger{
  display:none;
}
.b-numberfield input, .b-numberfield textarea,
.b-textareafield input,
.b-textareafield textarea,
.b-textfield input,
.b-textfield textarea{
  background-color:transparent;
  color:inherit;
  padding:0.8em 0.7em;
  font-weight:400;
  flex:1 1 0;
  border:0 none;
  margin:0;
  font-family:inherit;
  font-size:inherit;
  min-width:1em;
  text-align:inherit;
}
.b-numberfield input:focus, .b-numberfield textarea:focus,
.b-textareafield input:focus,
.b-textareafield textarea:focus,
.b-textfield input:focus,
.b-textfield textarea:focus{
  outline:none;
}
.b-numberfield input::-ms-clear, .b-numberfield textarea::-ms-clear,
.b-textareafield input::-ms-clear,
.b-textareafield textarea::-ms-clear,
.b-textfield input::-ms-clear,
.b-textfield textarea::-ms-clear{
  display:none;
}
.b-numberfield textarea,
.b-textareafield textarea,
.b-textfield textarea{
  align-self:stretch;
}
.b-numberfield ::-webkit-input-placeholder,
.b-textareafield ::-webkit-input-placeholder,
.b-textfield ::-webkit-input-placeholder{
  color:#cccccc;
}
.b-numberfield.b-empty .b-fieldtrigger.b-icon-remove,
.b-textareafield.b-empty .b-fieldtrigger.b-icon-remove,
.b-textfield.b-empty .b-fieldtrigger.b-icon-remove{
  visibility:hidden;
}
.b-numberfield:focus-within .b-label i,
.b-textareafield:focus-within .b-label i,
.b-textfield:focus-within .b-label i{
  color:#ffcc80;
}
.b-numberfield:not(.b-disabled):hover .b-label i,
.b-textareafield:not(.b-disabled):hover .b-label i,
.b-textfield:not(.b-disabled):hover .b-label i{
  color:#ffcc80;
}
.b-numberfield:not(.b-disabled):hover .b-field-inner,
.b-textareafield:not(.b-disabled):hover .b-field-inner,
.b-textfield:not(.b-disabled):hover .b-field-inner{
  border-color:#ffcc80;
}
.b-numberfield.b-invalid .b-field-inner,
.b-textareafield.b-invalid .b-field-inner,
.b-textfield.b-invalid .b-field-inner{
  border-color:#ef9a9a !important;
}
.b-numberfield.b-disabled,
.b-textareafield.b-disabled,
.b-textfield.b-disabled{
  color:#cccccc;
  cursor:default;
}
.b-safari .b-numberfield.b-disabled,
.b-safari .b-textareafield.b-disabled,
.b-safari .b-textfield.b-disabled{
  color:inherit;
}
.b-numberfield.b-disabled input,
.b-textareafield.b-disabled input,
.b-textfield.b-disabled input{
  cursor:text;
}
.b-numberfield.b-disabled .b-fieldtrigger,
.b-textareafield.b-disabled .b-fieldtrigger,
.b-textfield.b-disabled .b-fieldtrigger{
  color:rgba(240, 240, 240, 0.5);
}
.b-numberfield.b-disabled .b-fieldtrigger,
.b-textareafield.b-disabled .b-fieldtrigger,
.b-textfield.b-disabled .b-fieldtrigger{
  cursor:default;
}
.b-numberfield.b-disabled .b-field-inner,
.b-textareafield.b-disabled .b-field-inner,
.b-textfield.b-disabled .b-field-inner{
  border-style:solid;
}
.b-numberfield.b-readonly,
.b-textareafield.b-readonly,
.b-textfield.b-readonly{
  cursor:default;
}
.b-numberfield.b-readonly input,
.b-textareafield.b-readonly input,
.b-textfield.b-readonly input{
  cursor:text;
}
.b-numberfield.b-readonly .b-fieldtrigger,
.b-textareafield.b-readonly .b-fieldtrigger,
.b-textfield.b-readonly .b-fieldtrigger{
  color:rgba(240, 240, 240, 0.5);
  cursor:default;
}

.b-numberfield input[type=number]::-webkit-inner-spin-button,
.b-numberfield input[type=number]::-webkit-outer-spin-button{
  -webkit-appearance:none;
  appearance:none;
  margin:0;
}
.b-numberfield input[type=number]{
  -moz-appearance:textfield;
  appearance:textfield;
}
.b-numberfield .b-field-inner .b-fieldtrigger.b-icon-remove{
  font-size:0.8em;
}

div.b-tooltip.b-field-error-tip{
  border:1px solid #ef9a9a;
}
div.b-tooltip.b-field-error-tip .b-panel-body-wrap{
  background-color:#fafafa;
  color:#ef9a9a;
  font-weight:bold;
  padding-block:0.1em;
}
div.b-tooltip.b-field-error-tip .b-panel-body-wrap .b-popup-content{
  background-color:#fafafa;
  color:#ef9a9a;
  font-weight:bold;
}

.b-divider{
  position:relative;
  justify-content:center;
  margin:1em 0 1.5em 0;
}
.b-divider::before{
  content:"";
  width:100%;
  border-bottom:1px solid rgba(189, 189, 189, 0.5);
  position:absolute;
  top:50%;
}
.b-divider::after{
  display:flex;
  content:attr(data-text);
  color:#616161;
  background:#fefefe;
  padding:0 1em;
  z-index:1;
  font-size:0.8em;
}
.b-checkbox{
  --widget-primary-color-rgb:204,204,204;
  --checkbox-unchecked-check-opacity:0.1;
  --checkbox-unchecked-check-color:rgb(var(--widget-primary-color-rgb), var(--checkbox-unchecked-check-opacity));
  --checkbox-unchecked-background-color:transparent;
  --checkbox-unchecked-border-color:rgb(var(--widget-primary-color-rgb));
  --checkbox-checked-check-color:#fff;
  --checkbox-checked-background-color:rgb(var(--widget-primary-color-rgb));
  --checkbox-checked-border-color:rgb(var(--widget-primary-color-rgb));
  --checkbox-disabled-unchecked-check-color:transparent;
  --checkbox-disabled-checked-check-color:rgba(128, 128, 128, 0.15);
  --checkbox-disabled-background-color:rgba(128, 128, 128, 0.1);
  --checkbox-disabled-border-color:rgba(128, 128, 128, 0.15);
  --checkbox-check-color:var(--checkbox-unchecked-check-color);
  --checkbox-background-color:var(--checkbox-unchecked-background-color);
  --checkbox-border-color:var(--checkbox-unchecked-border-color);
  --checkbox-label-transition:color 0.2s, background-color 0.2s;
  --checkbox-input-transition:color 0.5s, background-color 0.5s;
  position:relative;
  align-items:center;
}
.b-checkbox.b-field-container > .b-field-inner{
  min-height:5.4ex;
}
.b-checkbox > .b-field-inner{
  border:none;
  background-color:transparent;
  white-space:nowrap;
  word-break:break-all;
}
.b-checkbox > .b-field-inner .b-checkbox-label{
  position:relative;
  font-size:inherit;
  cursor:pointer;
  color:#262626;
  white-space:normal;
}
.b-container .b-checkbox > .b-field-inner .b-checkbox-label{
  color:#616161;
}
.b-checkbox > .b-field-inner .b-checkbox-label:before{
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  display:inline-flex;
  justify-content:center;
  align-items:center;
  margin-inline-end:0.4em;
  font-size:inherit;
  width:1.6em;
  height:1.6em;
  padding:0.15em;
  border-radius:2px;
  transition:var(--checkbox-label-transition);
  background-color:var(--checkbox-background-color);
  border:1px solid var(--checkbox-border-color);
}
.b-checkbox > .b-field-inner .b-checkbox-label:not(.b-radio-label):before{
  content:"\f00c";
  color:var(--checkbox-check-color);
}
.b-checkbox > .b-field-inner input[type=checkbox]{
  z-index:1;
  opacity:0;
  width:2.2em;
  height:2.2em;
  position:absolute;
  margin:0;
  cursor:pointer;
  transition:var(--checkbox-input-transition);
}
.b-checkbox > .b-field-inner input[type=checkbox]:checked + .b-checkbox-label:before{
  --checkbox-check-color:var(--checkbox-checked-check-color);
  --checkbox-background-color:var(--checkbox-checked-background-color);
  --checkbox-border-color:var(--checkbox-checked-border-color);
}
.b-checkbox.b-disabled > .b-field-inner{
  --checkbox-check-color:var(--checkbox-disabled-unchecked-check-color);
  --checkbox-background-color:var(--checkbox-disabled-background-color);
  --checkbox-border-color:var(--checkbox-disabled-border-color);
}
.b-checkbox.b-disabled > .b-field-inner .b-checkbox-label{
  color:#cccccc;
}
.b-checkbox.b-disabled > .b-field-inner input[type=checkbox]:checked + .b-checkbox-label:before{
  --checkbox-check-color:var(--checkbox-disabled-checked-check-color);
  --checkbox-background-color:var(--checkbox-disabled-background-color);
  --checkbox-border-color:var(--checkbox-disabled-border-color);
}
.b-checkbox.b-contains-focus input[type=checkbox]{
  outline:1px solid #ffcc80;
}

.b-container .b-checkbox-label:before{
  margin:0.675em;
  margin-inline-end:0.4em;
  margin-inline-start:0;
  margin-top:0.5em;
}

.b-checkbox.b-field-container:not(.b-field-container-inline) > .b-field-container-wrap{
  margin-inline-start:2.2em;
}
.b-colorbox{
  width:1em;
  height:1em;
  border-radius:3px;
  background:currentColor;
  margin-inline-end:0.5em;
}

.b-colorfield:not(.b-colorless) .b-colorbox,
.b-colorboxcombo:not(.b-colorless) .b-colorbox{
  margin-inline:0.8em 0.4em;
}
.b-colorfield:not(.b-colorless) .b-colorbox.b-no-color,
.b-colorboxcombo:not(.b-colorless) .b-colorbox.b-no-color{
  background:none;
  border:1px solid rgba(189, 189, 189, 0.5);
}
.b-colorfield:not(.b-colorless) input,
.b-colorboxcombo:not(.b-colorless) input{
  padding-inline-start:0;
}
.b-colorfield.b-colorless .b-colorbox,
.b-colorboxcombo.b-colorless .b-colorbox{
  display:none;
}
.b-colorfield.b-empty,
.b-colorboxcombo.b-empty{
  color:#ccc;
}
.b-colorfield.b-empty ::-webkit-input-placeholder,
.b-colorboxcombo.b-empty ::-webkit-input-placeholder{
  color:#262626;
}
.b-colorfield.b-empty .b-colorbox,
.b-colorboxcombo.b-empty .b-colorbox{
  background:none;
  border:1px solid currentColor;
}
.b-colorpicker.b-list.b-widget{
  display:grid;
  grid-gap:0.8em;
  padding:1em;
}
.b-colorpicker.b-list.b-widget .b-list-item{
  border:none;
  width:2em;
  height:2em;
  border-radius:50%;
  padding:0;
  min-width:0;
  justify-content:center;
}
.b-colorpicker.b-list.b-widget .b-list-item.b-no-color{
  border:1px solid rgba(189, 189, 189, 0.5);
}
.b-colorpicker.b-list.b-widget .b-list-item.b-no-color.b-selected:after{
  color:#999;
}
.b-colorpicker.b-list.b-widget .b-list-item.b-no-color:before{
  content:"";
  border-right:1px solid rgba(189, 189, 189, 0.5);
  height:100%;
  transform:rotate(135deg);
  position:absolute;
}
.b-colorpicker.b-list.b-widget .b-list-item.b-color-active{
  outline:2px solid #999;
  outline-offset:2px;
}
.b-colorpicker.b-list.b-widget .b-list-item.b-selected:after{
  content:"\f00c";
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  color:#fff;
  text-shadow:0 0 1px #000;
  font-size:1.2em;
}

.b-popup .b-colorpicker{
  background:transparent;
}
.b-combo.b-open .b-fieldtrigger.b-icon-picker:before{
  transform:rotate(180deg);
}
.b-combo.b-inline-picker:not(.b-label-above){
  flex-wrap:wrap;
  align-content:flex-start;
}
.b-combo.b-inline-picker:not(.b-label-above) .b-field-inner{
  flex-shrink:0;
  align-self:flex-start;
}
.b-combo.b-inline-picker:not(.b-label-above) .b-combo-picker{
  flex:1 0 100%;
}
.b-combo .b-field-inner .b-fieldtrigger.b-icon-remove{
  font-size:0.8em;
}
.b-combo.b-hide-trigger .b-fieldtrigger.b-icon-picker, .b-combo.b-readonly .b-fieldtrigger.b-icon-picker{
  display:none;
}
.b-combo.b-uses-chipview .b-field-inner{
  align-self:stretch;
}
.b-combo.b-uses-chipview .b-chipview{
  align-self:stretch;
  display:flex;
  flex-flow:wrap;
  flex:1 1 auto;
  align-items:center;
  margin:0.16em 0.3em;
}
.b-combo.b-uses-chipview .b-chipview:not(.b-empty) input{
  padding-inline-start:0.2em;
  order:99999;
}
.b-combo.b-uses-chipview.b-not-editable input{
  order:-1;
  min-width:0;
  padding-inline:0;
  flex:0 1 0;
  margin-inline-start:-0.3em;
}
.b-combo.b-uses-chipview.b-not-editable:not(.b-empty) .b-chipview .b-chip{
  padding-block:calc(0.3em + 0.5px);
  padding-inline:0.7em;
}
.b-combo.b-uses-chipview.b-not-editable:not(.b-empty) input{
  padding-inline:0;
}

.b-list.b-combo-picker.b-empty:not(.b-masked)[data-add-new-value]::after{
  content:attr(data-add-new-value);
  display:block;
  padding:0.8em;
  color:#616161;
  cursor:pointer;
}
.b-fieldfilterpicker .b-fieldfilterpicker-inputs{
  flex-wrap:wrap;
  gap:0.25em 0.5em;
}
.b-fieldfilterpicker .b-fieldfilterpicker-property,
.b-fieldfilterpicker .b-fieldfilterpicker-operator,
.b-fieldfilterpicker .b-fieldfilterpicker-values{
  flex:1 0 8em;
}
.b-fieldfilterpicker .b-fieldfilterpicker-values-number{
  flex:1 0 3em;
}
.b-fieldfilterpicker .b-fieldfilterpicker-values-duration{
  flex:1 0 6em;
}
.b-fieldfilterpicker .b-fieldfilterpicker-values{
  overflow:hidden;
}
.b-fieldfilterpicker .b-fieldfilterpicker-values.b-hidden{
  display:none;
}
.b-fieldfilterpicker .b-fieldfilterpicker-values-multiple{
  flex:0 0 100%;
}
.b-fieldfilterpicker .b-fieldfilterpicker-combo-locked > .b-field-inner{
  border:none;
  background:none;
}
.b-fieldfilterpicker .b-fieldfilterpicker-combo-locked > .b-field-inner input{
  padding-inline-start:0;
}
.b-fieldfilterpicker .b-fieldfilterpicker-values{
  display:flex;
}
.b-fieldfilterpicker .b-fieldfilterpicker-values-multiple{
  width:100%;
}
.b-fieldfilterpicker .b-fieldfilterpicker-values > .b-field{
  flex:1;
  margin-block-end:0;
  align-self:baseline;
}
.b-fieldfilterpicker .b-fieldfilterpicker-value-separator{
  flex:none;
  padding:0 0.4em;
  align-self:center;
}
.b-fieldfilterpicker .b-fieldfilterpicker-values > .b-field:last-child::after{
  display:none;
}

.b-fieldfilterpickergroup .b-checkbox[data-ref=enableAllCheckbox] .b-checkbox-label::before{
  margin-inline-end:0.5em;
}
.b-fieldfilterpickergroup .b-fieldfilterpickergroup-row{
  padding-top:2.2em;
  position:relative;
  flex-shrink:0;
  align-items:baseline;
}
.b-fieldfilterpickergroup .b-fieldfilterpickergroup-row:first-child{
  padding-top:0;
}
.b-fieldfilterpickergroup .b-fieldfilterpickergroup-row:first-child::before{
  display:none;
}
.b-fieldfilterpickergroup .b-fieldfilterpickergroup-row::before{
  content:attr(data-separator-text);
  font-size:85%;
  position:absolute;
  text-align:center;
  line-height:2.2em;
  width:100%;
  height:2.2em;
  top:0;
  background-image:linear-gradient(to right, #e0e0e7, #e0e0e7 44%, transparent 46%, transparent 54%, #e0e0e7 56%);
  background-position:0 50%;
  background-size:100% 1px;
  background-repeat:no-repeat;
}
.b-fieldfilterpickergroup .b-fieldfilterpickergroup-row:not(.b-fieldfilterpickergroup-row-removable){
  padding-inline-end:2.25em;
}
.b-fieldfilterpickergroup .b-fieldfilterpickergroup-filter-active{
  flex:0 0 2rem;
  align-self:baseline;
  position:relative;
  top:1px;
}
.b-fieldfilterpickergroup .b-fieldfilterpickergroup-filter-active.b-slidetoggle{
  flex-basis:2.5rem;
}
.b-fieldfilterpickergroup .b-fieldfilterpickergroup-other-filters{
  padding:0.5em 0;
}
.b-fieldfilterpickergroup .b-fieldfilterpickergroup-add-button{
  align-self:center;
  margin-top:1.1em;
}
.b-fieldfilterpickergroup .b-fieldfilterpickergroup-remove{
  inline-size:2em;
  margin-inline-start:0.25em;
}

.b-filepicker{
  overflow:visible;
}
.b-filepicker.b-content-element{
  align-content:stretch;
}

.b-displayfield{
  color:inherit;
}
.b-displayfield .b-field-inner{
  border:0;
  background:transparent;
}
.b-displayfield .b-field-inner span{
  padding:0.8em;
}

.b-pickerfield.b-open .b-field-inner{
  border-color:#64b5f6;
}
.b-pickerfield:not(.b-readonly):not(.b-disabled) input[readonly]{
  cursor:pointer;
}
.b-datefield.b-open .b-fieldtrigger{
  color:#64b5f6 !important;
}
.b-datefield.b-no-steppers .b-step-trigger{
  display:none;
}
.b-datefield.b-rtl .b-step-trigger::before{
  transform:scaleX(-1);
}
.b-datetimefield{
  align-items:center;
  position:relative;
}
.b-datetimefield input{
  text-align:center;
}
.b-datetimefield .b-field-inner{
  height:100%;
}
.b-datetimefield .b-datefield{
  flex:1 1 55%;
  border-top-right-radius:0;
  border-bottom-right-radius:0;
}
.b-datetimefield .b-datefield .b-field-inner{
  border:none;
  border-inline-end:1px solid rgba(209, 209, 209, 0.5);
  border-top-right-radius:0;
  border-bottom-right-radius:0;
}
.b-datetimefield .b-timefield{
  flex:1 1 45%;
  border-top-left-radius:0;
  border-bottom-left-radius:0;
}
.b-datetimefield .b-timefield .b-field-inner{
  border:none;
  border-top-left-radius:0;
  border-bottom-left-radius:0;
}
.b-datetimefield:not(.b-disabled):hover .b-field-inner, .b-datetimefield:focus-within .b-field-inner{
  border-color:#ffcc80;
}
.b-datetimefield:not(.b-disabled):hover .b-datefield .b-field-inner, .b-datetimefield:focus-within .b-datefield .b-field-inner{
  border-inline-end:1px solid rgba(209, 209, 209, 0.5);
}
.b-label{
  align-items:center;
  color:#262626;
  font-size:0.9em;
  white-space:nowrap;
  text-overflow:ellipsis;
}
.b-container .b-label{
  color:#616161;
}
.b-list{
  display:block;
  background-color:white;
  outline:none;
  padding:0;
  margin:0;
  -webkit-user-select:none;
  user-select:none;
}
.b-list.b-floating{
  border-radius:2px;
}
.b-list.b-empty:not(.b-masked){
  min-height:initial !important;
}
.b-list.b-empty:not(.b-masked)[data-empty-text]::after{
  content:attr(data-empty-text);
  display:block;
  padding:0.8em;
  color:#616161;
}
.b-list.b-masked{
  padding:2em 0;
}
.b-list .b-selected-icon{
  margin-inline-end:0.5em;
  display:none;
}
.b-list .b-select-all-item{
  border-bottom:1px solid;
  font-weight:500;
  position:sticky;
  top:0;
  background-color:white;
  z-index:1;
}
:has(.b-list-title) > .b-list .b-select-all-item{
  top:2em;
}
.b-list .b-list-title{
  position:sticky;
  top:0;
  font-weight:500;
  background-color:white;
  z-index:1;
  height:2em;
}
.b-list.b-multiselect .b-list-item .b-selected-icon{
  display:initial;
  visibility:hidden;
}
.b-list.b-multiselect .b-list-item.b-selected .b-selected-icon{
  visibility:visible;
}

.b-list-item{
  position:relative;
  padding:0.8em;
  color:#616161;
  cursor:pointer;
  background-color:transparent;
  transition:background-color 0.2s, color 0.2s;
  border-top:1px dotted rgba(224, 224, 224, 0.8);
  min-width:100px;
  overflow:hidden;
  overflow:clip;
  white-space:nowrap;
  display:flex;
  flex-direction:row;
  align-items:center;
}
.b-list-itemli{
  padding-inline-start:calc(var(--tree-level, 0) * 1em + 0.8em);
}
.b-list-item.b-hidden{
  display:none;
}
.b-list-item:first-child{
  border-top:none;
  border-top-right-radius:2px;
  border-top-left-radius:2px;
}
.b-list-item:last-child{
  border-bottom-right-radius:2px;
  border-bottom-left-radius:2px;
}
.b-list-item.b-active, .b-list-item:focus{
  outline:none;
}
.b-list-item.b-active:not(.b-disabled), .b-list-item:focus:not(.b-disabled){
  background-color:#64b5f6;
  color:#616161;
}
.b-list-item:is(.b-list-item-group-header, .b-list-item-tree-parent){
  font-weight:500;
}
.b-list-item:is(.b-list-item-group-header, .b-list-item-tree-parent) .b-icon-check{
  display:none;
}
.b-list-item:is(.b-list-item-group-header, .b-list-item-tree-parent) .b-list-expander-icon{
  margin-inline-start:auto;
  margin-inline-end:0;
  font-size:1.1em;
  min-width:0;
  min-height:0;
  padding:0;
  height:1.1em;
  width:1.1em;
  opacity:0.8;
  color:var(--button-text-color);
}
@media (pointer: coarse){
  .b-list-item:is(.b-list-item-group-header, .b-list-item-tree-parent) .b-list-expander-icon{
    font-size:1.4em;
    width:1.4em;
  }
}
.b-list-item:is(.b-list-item-group-header, .b-list-item-tree-parent) .b-list-expander-icon:hover{
  opacity:1;
}
.b-grouped .b-list-item:not(.b-list-item-group-header, .b-list-item-tree-parent){
  padding-inline-start:2em !important;
}

.b-virtualized .b-list-item{
  height:1.5em;
  box-sizing:content-box;
}

.b-chip{
  display:flex;
  align-items:center;
  padding-block:0.3em;
  padding-inline:0.7em;
  border-radius:1em;
  background-color:#f9f9f9;
  color:#333333;
  transition:background-color 0.3s, color 0.3s;
}
.b-chip .b-icon{
  height:1.5em;
  width:1.5em;
  border-radius:50%;
  display:flex;
  align-items:center;
  justify-content:center;
  background-color:white;
  color:#757575;
}
.b-chip .b-icon:first-child:not(.b-close-icon){
  margin-inline-end:0.5em;
  margin-inline-start:-0.4em;
}
.b-chip:hover{
  background-color:#ececec;
  color:#262626;
}
.b-chip:hover .b-icon{
  background-color:white;
  color:#757575;
}
.b-chip.b-selected{
  background-color:#64b5f6;
  color:#616161;
}
.b-chip.b-selected .b-icon{
  background-color:white;
  color:#64b5f6;
}
.b-chip.b-selected:hover{
  background-color:#4ca9f5;
  color:#616161;
}
.b-chip.b-selected:hover .b-icon{
  background-color:white;
  color:#4ca9f5;
}
.b-chip.b-active{
  outline:1px solid #ffcc80;
  outline-offset:2px;
}
.b-chip .b-close-icon{
  margin-inline-start:0.5em;
  cursor:pointer;
}

.b-chipview{
  background-color:transparent;
  gap:0.3em;
}
.b-chipview.b-empty::after{
  display:none;
}
.b-chipview input{
  padding-top:0.6em;
  padding-bottom:calc(0.6em + 1px);
}
.b-chipview.b-chips-closable .b-chip{
  padding-inline-end:0.3em;
}
@keyframes maskOpacity{
  0%{
    opacity:0;
  }
  100%{
    opacity:1;
  }
}
.b-masked{
  position:relative;
}

.b-mask{
  position:absolute;
  top:0;
  left:0;
  bottom:0;
  right:0;
  z-index:10000;
  background-color:transparent;
}
.b-mask:not(.b-visible){
  opacity:0;
  pointer-events:none;
}
.b-mask:not(.b-prevent-transitions){
  transition:opacity 1s;
}
.b-mask:not(.b-prevent-transitions):not(.b-delayed-show){
  animation:maskOpacity 0.3s ease 0s 1;
}
.b-mask:not(.b-prevent-transitions).b-hidden{
  animation:maskOpacity 0.2s ease 0s 1 reverse;
}
.b-mask.b-delayed-show{
  opacity:0;
}
.b-mask.b-mask-bright, .b-mask.b-mask-bright-blur{
  background-color:rgba(255, 255, 255, 0.6980392157);
}
.b-mask.b-mask-dark, .b-mask.b-mask-dark-blur{
  background-color:rgba(0, 0, 0, 0.2980392157);
}

.b-mask-content{
  position:absolute;
  top:50%;
  left:50%;
  transform:translate(-50%, -50%);
  display:flex;
  flex-direction:column;
  color:#616161;
  background-color:#64b5f6;
  border-radius:2px;
}

.b-mask-text{
  padding:1em;
  display:flex;
  align-items:center;
}

.b-mask-progress-bar{
  height:3px;
  background-color:#ffcc80;
}

.b-mask-icon{
  margin-inline-end:0.5em;
}

.b-masked-dark-blur,
.b-masked-bright-blur{
  filter:blur(3px);
  transform:scale(1.01);
  transition:all 0.5s;
}
.b-panel.b-floating{
  border-radius:2px;
}
.b-panel.b-floating:not(.b-panel-has-header) > .b-panel-body-wrap, .b-panel.b-floating:not(.b-panel-has-header) > .b-panel-body-wrap > .b-panel-content{
  border-radius:inherit;
}
.b-panel.b-floating > .b-panel-header.b-dock-top, .b-panel.b-floating:not(.b-has-header) > .b-panel-body-wrap > .b-toolbar.b-dock-top{
  border-top-left-radius:2px;
  border-top-right-radius:2px;
}
.b-panel.b-floating > .b-panel-body-wrap > .b-toolbar.b-dock-bottom{
  border-bottom-left-radius:2px;
  border-bottom-right-radius:2px;
}
.b-panel.b-floating.b-panel-has-header > .b-panel-body-wrap > .b-panel-content, .b-panel.b-floating.b-panel-has-top-toolbar > .b-panel-body-wrap > .b-panel-content{
  border-top-left-radius:0;
  border-top-right-radius:0;
}
.b-panel.b-floating.b-panel-has-bottom-toolbar > .b-panel-body-wrap > .b-panel-content{
  border-bottom-left-radius:0;
  border-bottom-right-radius:0;
}

.b-panel{
  --panel-background-color:#fafafa;
  padding:0;
  color:#616161;
  background-color:var(--panel-background-color);
  z-index:0;
}
.b-panel > .b-positionable{
  z-index:10;
}
.b-panel .b-panel-body-wrap{
  background-color:var(--panel-background-color);
  overflow:hidden;
}
.b-panel .b-auto-container-panel .b-toolbar.b-innermost.b-dock-top .b-toolbar-content{
  padding-bottom:0;
}
.b-panel .b-auto-container-panel .b-toolbar.b-innermost.b-dock-right .b-toolbar-content{
  padding-left:0;
}
.b-panel .b-auto-container-panel .b-toolbar.b-innermost.b-dock-bottom .b-toolbar-content{
  padding-top:0;
}
.b-panel .b-auto-container-panel .b-toolbar.b-innermost.b-dock-left .b-toolbar-content{
  padding-right:0;
}
.b-panel.b-html .b-panel-content{
  padding-block:3px;
  margin-block:1em;
}
.b-panel > .b-panel-overlay,
.b-panel > .b-panel-body-wrap{
  z-index:0;
}
.b-panel > .b-panel-collapse-size-locker{
  position:absolute !important;
}
.b-panel:not(.b-panel-collapsible-overlay).b-collapsed > .b-panel-collapse-size-locker{
  clip:rect(0, 0, 0, 0);
}
.b-panel:not(.b-panel-collapsible-overlay).b-panel-collapse-down:not(.b-panel-has-header) > .b-panel-collapse-size-locker, .b-panel:not(.b-panel-collapsible-overlay).b-header-dock-bottom.b-panel-collapse-down > .b-panel-collapse-size-locker, .b-panel:not(.b-panel-collapsible-overlay).b-header-dock-bottom.b-panel-collapse-up > .b-panel-collapse-size-locker{
  top:0;
}
.b-panel:not(.b-panel-collapsible-overlay).b-panel-collapse-up:not(.b-panel-has-header) > .b-panel-collapse-size-locker, .b-panel:not(.b-panel-collapsible-overlay).b-header-dock-top.b-panel-collapse-up > .b-panel-collapse-size-locker, .b-panel:not(.b-panel-collapsible-overlay).b-header-dock-top.b-panel-collapse-down > .b-panel-collapse-size-locker{
  bottom:0;
}
.b-panel:not(.b-panel-collapsible-overlay).b-panel-collapse-left:not(.b-panel-has-header) > .b-panel-collapse-size-locker, .b-panel:not(.b-panel-collapsible-overlay).b-header-dock-left.b-panel-collapse-left > .b-panel-collapse-size-locker, .b-panel:not(.b-panel-collapsible-overlay).b-header-dock-left.b-panel-collapse-right > .b-panel-collapse-size-locker{
  right:0;
}
.b-panel:not(.b-panel-collapsible-overlay).b-panel-collapse-right:not(.b-panel-has-header) > .b-panel-collapse-size-locker, .b-panel:not(.b-panel-collapsible-overlay).b-header-dock-right.b-panel-collapse-left > .b-panel-collapse-size-locker, .b-panel:not(.b-panel-collapsible-overlay).b-header-dock-right.b-panel-collapse-right > .b-panel-collapse-size-locker{
  left:0;
}
.b-panel.b-panel-collapsible-overlay:not(.b-collapsing, .b-expanding, .b-panel-overlay-revealed, .b-panel-overlay-revealing) > .b-panel-overlay > .b-panel-overlay-header{
  display:none;
}
.b-panel.b-panel-collapsible-overlay.b-panel-overlay-revealed > .b-panel-overlay{
  box-shadow:0 0 10px 0 rgba(0, 0, 0, 0.3);
  overflow:visible;
}
.b-panel.b-panel-collapsible-overlay > .b-panel-header{
  transition:transform 0.2s ease-in-out;
}
.b-panel.b-panel-collapsible-overlay.b-collapsed > .b-panel-header > .b-collapsify-hide{
  display:none;
}
.b-panel.b-panel-collapsible-overlay.b-collapsing > .b-panel-header, .b-panel.b-panel-collapsible-overlay.b-expanding > .b-panel-header{
  opacity:0;
}
.b-panel.b-panel-collapsible-overlay:not(.b-collapsed, .b-collapsing) > .b-panel-overlay > .b-panel-overlay-header{
  display:none;
}
.b-panel.b-panel-collapsible-overlay.b-collapsing, .b-panel.b-panel-collapsible-overlay.b-expanding, .b-panel.b-panel-collapsible-overlay.b-panel-overlay-revealing, .b-panel.b-panel-collapsible-overlay.b-panel-overlay-revealed{
  overflow:visible;
  z-index:1;
}
.b-panel.b-panel-collapsible-overlay.b-collapsing{
  overflow:visible;
}
.b-panel.b-panel-collapsible-overlay.b-collapsing > .b-panel-header{
  z-index:-1;
}
.b-panel.b-panel-collapsible-overlay.b-collapsing.b-panel-collapse-up > .b-panel-header{
  transform:translate(0, -100%);
}
.b-panel.b-panel-collapsible-overlay.b-collapsing.b-panel-collapse-down > .b-panel-header{
  transform:translate(0, 100%);
}
.b-panel.b-panel-collapsible-overlay.b-collapsing.b-panel-collapse-right > .b-panel-header{
  transform:translate(100%, 0);
}
.b-panel.b-panel-collapsible-overlay.b-collapsing.b-panel-collapse-left > .b-panel-header{
  transform:translate(-100%, 0);
}
.b-panel.b-panel-collapsible-overlay.b-collapsed:not(.b-expanding) > .b-panel-collapse-size-locker{
  transition:transform 0.2s ease-in-out, clip-path 0.2s ease-in-out, top 0.2s ease-in-out, right 0.2s ease-in-out, bottom 0.2s ease-in-out, left 0.2s ease-in-out;
}
.b-panel.b-panel-collapsible-overlay.b-collapsed:not(.b-expanding).b-panel-collapse-up > .b-panel-collapse-size-locker{
  transform:translate(0, -100%);
}
.b-panel.b-panel-collapsible-overlay.b-collapsed:not(.b-expanding).b-panel-collapse-up.b-panel-overlay-revealed > .b-panel-collapse-size-locker{
  clip-path:inset(0 0 -10px 0);
  transform:translate(0, 0);
}
.b-panel.b-panel-collapsible-overlay.b-collapsed:not(.b-expanding).b-panel-collapse-down > .b-panel-collapse-size-locker{
  transform:translate(0, 100%);
}
.b-panel.b-panel-collapsible-overlay.b-collapsed:not(.b-expanding).b-panel-collapse-down.b-panel-overlay-revealed > .b-panel-collapse-size-locker{
  clip-path:inset(-10px 0 0 0);
  transform:translate(0, 0);
}
.b-panel.b-panel-collapsible-overlay.b-collapsed:not(.b-expanding).b-panel-collapse-left > .b-panel-collapse-size-locker{
  transform:translate(-100%, 0);
}
.b-panel.b-panel-collapsible-overlay.b-collapsed:not(.b-expanding).b-panel-collapse-left.b-panel-overlay-revealed > .b-panel-collapse-size-locker{
  clip-path:inset(0 -10px 0 0);
  transform:translate(0, 0);
}
.b-panel.b-panel-collapsible-overlay.b-collapsed:not(.b-expanding).b-panel-collapse-right > .b-panel-collapse-size-locker{
  transform:translate(100%, 0);
}
.b-panel.b-panel-collapsible-overlay.b-collapsed:not(.b-expanding).b-panel-collapse-right.b-panel-overlay-revealed > .b-panel-collapse-size-locker{
  clip-path:inset(0 0 0 -10px);
  transform:translate(0, 0);
}
.b-panel.b-panel-collapsible-overlay.b-expanding{
  flex:none !important;
  overflow:visible;
}
.b-panel.b-header-dock-right, .b-panel.b-header-dock-bottom{
  justify-content:flex-end;
}
.b-panel.b-collapsed:not(.b-expanding):not(.b-panel-overlay-revealed):not(.b-panel-overlay-revealing){
  visibility:hidden;
  flex:0 1 0px;
}
.b-panel.b-collapsed > .b-panel-collapse-revealer{
  visibility:visible;
  cursor:pointer;
}
.b-panel .b-panel-collapser-header{
  display:none;
}
.b-panel.b-collapse-unflex{
  flex-grow:unset !important;
  flex-basis:unset !important;
  flex-shrink:0 !important;
}
.b-panel.b-collapsed .b-panel-collapser-header, .b-panel.b-collapsing .b-panel-collapser-header{
  display:flex;
}
.b-panel.b-collapsed.b-header-dock-top, .b-panel.b-collapsed.b-header-dock-bottom, .b-panel.b-collapsing.b-header-dock-top, .b-panel.b-collapsing.b-header-dock-bottom{
  min-height:auto !important;
}
.b-panel.b-collapsed.b-header-dock-top.b-panel-collapsible-overlay, .b-panel.b-collapsed.b-header-dock-bottom.b-panel-collapsible-overlay, .b-panel.b-collapsing.b-header-dock-top.b-panel-collapsible-overlay, .b-panel.b-collapsing.b-header-dock-bottom.b-panel-collapsible-overlay{
  height:unset !important;
}
.b-vbox > .b-panel.b-collapsed.b-header-dock-top, .b-vbox > .b-panel.b-collapsed.b-header-dock-bottom, .b-vbox > .b-panel.b-collapsing.b-header-dock-top, .b-vbox > .b-panel.b-collapsing.b-header-dock-bottom{
  flex:none !important;
}
.b-panel.b-collapsed.b-header-dock-right, .b-panel.b-collapsed.b-header-dock-left, .b-panel.b-collapsing.b-header-dock-right, .b-panel.b-collapsing.b-header-dock-left{
  min-width:auto !important;
}
.b-panel.b-collapsed.b-header-dock-right.b-panel-collapsible-overlay, .b-panel.b-collapsed.b-header-dock-left.b-panel-collapsible-overlay, .b-panel.b-collapsing.b-header-dock-right.b-panel-collapsible-overlay, .b-panel.b-collapsing.b-header-dock-left.b-panel-collapsible-overlay{
  width:unset !important;
}
.b-hbox > .b-panel.b-collapsed.b-header-dock-right, .b-hbox > .b-panel.b-collapsed.b-header-dock-left, .b-hbox > .b-panel.b-collapsing.b-header-dock-right, .b-hbox > .b-panel.b-collapsing.b-header-dock-left{
  flex:none !important;
}
.b-panel.b-collapsed:not(.b-expanding).b-header-dock-right, .b-panel.b-collapsed:not(.b-expanding).b-header-dock-left{
  width:unset !important;
  flex:0 0 auto !important;
}
.b-panel.b-header-dock-top .b-panel-content{
  border-top-right-radius:0;
  border-top-left-radius:0;
}
.b-panel.b-header-dock-right .b-panel-content{
  border-top-right-radius:0;
  border-bottom-right-radius:0;
}
.b-panel.b-header-dock-bottom .b-panel-content{
  border-bottom-left-radius:0;
  border-bottom-right-radius:0;
}
.b-panel.b-header-dock-left .b-panel-content{
  border-top-left-radius:0;
  border-bottom-left-radius:0;
}
.b-panel.b-panel-has-bottom-toolbar .b-panel-content{
  border-bottom-left-radius:0;
  border-bottom-right-radius:0;
}
.b-panel.b-floating > .b-dock-top{
  border-top-right-radius:2px;
  border-top-left-radius:2px;
}
.b-panel.b-floating > .b-dock-right{
  border-top-right-radius:2px;
  border-bottom-right-radius:2px;
}
.b-panel.b-floating > .b-dock-bottom{
  position:static;
  border-bottom-left-radius:2px;
  border-bottom-right-radius:2px;
}
.b-panel.b-floating > .b-dock-left{
  border-top-left-radius:2px;
  border-bottom-left-radius:2px;
}
.b-panel.b-panel-ui-plain, .b-panel.b-panel-ui-plain .b-panel-overlay{
  background-color:var(--panel-background-color);
}
.b-panel.b-panel-ui-plain .b-toolbar{
  background:transparent;
}

.b-panel-content{
  justify-content:space-between;
  overflow:hidden;
  padding:1em;
}
.b-panel-ui-plain .b-panel-content{
  padding-block:0;
}
.b-panel-content.b-auto-container.b-single-child > .b-container{
  flex:1 1 auto;
}
.b-panel-content.b-fit-container{
  padding:0;
}

.b-tabpanel .b-panel-content{
  background-color:#fefefe;
}

.b-panel-collapser{
  z-index:0;
}

.b-panel-header{
  display:flex;
  background-color:#64b5f6;
  color:#fff;
  padding:1em;
  flex:0 0 auto;
  align-items:center;
  z-index:1;
}
.b-panel-header .b-tool{
  color:#fff;
}
.b-panel-header.b-panel-ui-toolbar{
  background-color:#f9f9f9;
  color:#616161;
  padding:0.5em;
}
.b-panel-header.b-panel-ui-toolbar .b-header-title{
  font-size:unset;
  font-weight:unset;
}
.b-panel-header.b-panel-ui-toolbar .b-tool{
  color:inherit;
}
.b-panel-header.b-panel-ui-toolbar.b-dock-top{
  border-bottom:1px solid #e0e0e0;
}
.b-panel-header.b-panel-ui-toolbar.b-panel-ui-plain{
  border:none;
}
.b-panel-header.b-dock-right{
  flex-flow:column nowrap;
}
.b-panel-header.b-dock-left{
  flex-flow:column-reverse nowrap;
}
.b-panel-header.b-dock-left .b-header-title{
  transform:rotate(180deg);
}
.b-panel-header.b-panel-ui-plain{
  background-color:transparent;
  border:none;
  color:inherit;
}
.b-panel-header .b-header-title{
  flex:1 1 auto;
  text-align:center;
  justify-content:center;
  white-space:nowrap;
  overflow:hidden;
  text-overflow:ellipsis;
}
.b-panel-header .b-header-title:not(:last-child){
  padding-inline-end:1em;
}
.b-panel-header .b-header-title.b-align-start{
  text-align:start;
  justify-content:flex-start;
}
.b-panel-header .b-header-title.b-align-center{
  text-align:center;
  justify-content:center;
  padding-inline-end:0;
}
.b-panel-header .b-header-title.b-align-end{
  text-align:end;
  justify-content:flex-end;
}
.b-panel-header .b-header-title.b-align-end:not(:last-child){
  margin-inline-end:1em;
}
.b-panel.b-panel-ui-plain > .b-panel-header .b-header-title.b-panel-ui-plain{
  font-size:1.2em;
  font-weight:600;
}
.b-panel-header:after{
  content:" ";
  font-size:0.9em;
  height:2em;
  width:1px;
  visibility:hidden;
  display:inline;
}
.b-panel-header.b-dock-right .b-header-title, .b-panel-header.b-dock-left .b-header-title{
  -webkit-writing-mode:vertical-lr;
  writing-mode:vertical-lr;
  -ms-writing-mode:tb-lr;
}
.b-panel-header.b-dock-right:after, .b-panel-header.b-dock-left:after{
  height:1px;
  width:2em;
}
.b-panel-header.b-dock-right, .b-panel-header.b-dock-bottom{
  order:100;
}

.b-button.b-tool,
.b-tool{
  cursor:pointer;
  color:#616161;
  height:2em;
  width:2em;
  font-size:0.9em;
  display:flex;
  align-items:center;
  justify-content:center;
  border-radius:50%;
  border:0 none;
  background-color:transparent;
  flex-shrink:0;
  contain:paint;
}
.b-button.b-tool::-moz-focus-inner,
.b-tool::-moz-focus-inner{
  border:0;
}
.b-button.b-tool:focus,
.b-tool:focus{
  outline:none;
}
.b-using-keyboard .b-button.b-tool:focus,
.b-using-keyboard .b-tool:focus{
  background-color:rgba(220, 220, 220, 0.5);
}
.b-button.b-tool.b-icon:before,
.b-tool.b-icon:before{
  width:1em;
  height:1em;
  text-align:center;
  line-height:inherit;
}
.b-button.b-tool:hover,
.b-tool:hover{
  opacity:0.8;
}
.b-button.b-tool.b-disabled,
.b-tool.b-disabled{
  opacity:0.4;
}
.b-button.b-tool.b-rotate-left:before,
.b-tool.b-rotate-left:before{
  transform:rotate(270deg);
}
.b-button.b-tool.b-rotate-right:before,
.b-tool.b-rotate-right:before{
  transform:rotate(90deg);
}
.b-button.b-tool i,
.b-tool i{
  display:flex;
}
.b-button.b-tool a,
.b-tool a{
  color:#fff;
}

.b-button.b-tool{
  min-height:2em;
  min-width:2em;
}

.b-collapsetool:not(.b-collapsing):before{
  transition:all 0.2s ease-in-out;
}
.b-collapsetool.b-rotate-left:before, .b-collapsetool.b-rotate-right:before{
  transform:rotate(0deg);
}
.b-collapsetool.b-collapsed:before{
  transform:rotate(180deg);
}
.b-panel-ui-toolbar.b-panel-collapse-right .b-collapsible-tr.b-dock-right .b-collapsetool{
  margin-block-start:0.6em;
}
.b-panel-ui-toolbar.b-panel-collapse-right .b-collapsible-tr.b-dock-top .b-collapsetool{
  margin-inline-end:0.7em;
}

.b-dock-top .b-header-title:not(:last-child), .b-dock-bottom .b-header-title:not(:last-child){
  padding-inline-end:0.2em;
}
.b-dock-top .b-tool.b-align-start, .b-dock-top .b-tool.b-align-end, .b-dock-bottom .b-tool.b-align-start, .b-dock-bottom .b-tool.b-align-end{
  margin-block:0;
}

.b-dock-right .b-header-title:not(:last-child), .b-dock-left .b-header-title:not(:last-child){
  padding:0.2em 0;
}
.b-dock-right .b-tool.b-align-start, .b-dock-left .b-tool.b-align-start{
  margin-block-end:0.4em;
}
.b-dock-right .b-tool.b-align-end, .b-dock-left .b-tool.b-align-end{
  margin-block-start:0.4em;
}
.b-panel .b-bryntumcodeeditor-body-wrap{
  background:#fff;
}
.b-panel .b-bryntumcodeeditor-body-wrap .monaco-editor .scroll-decoration{
  box-shadow:none;
}

.b-codeeditor-content{
  padding:0;
}
.b-codeeditor-content .line-numbers:not(.active-line-number){
  opacity:0.3;
}
.b-codeeditor-content .folded-background,
.b-codeeditor-content .monaco-editor .view-overlays .current-line{
  background:transparent !important;
  border:0 !important;
}
.b-codeeditor-content .monaco-editor .lines-content .core-guide-indent{
  opacity:0.4;
}

.b-widget.b-democodeeditor{
  flex:unset;
  width:450px;
  gap:0;
}
@media (min-width: 2000px){
  .b-widget.b-democodeeditor{
    width:700px;
  }
}
.b-widget.b-democodeeditor .b-header-title{
  font-size:18px;
  font-weight:normal;
}
.b-widget.b-democodeeditor .demo-header .b-button.b-text{
  min-height:2.5em;
}
.b-widget.b-democodeeditor .demo-header .title-container{
  align-items:center;
}
.b-widget.b-democodeeditor .demo-header .title{
  background:none;
  padding-inline-start:0;
  font-size:18px;
}
.b-widget.b-democodeeditor .demo-header .b-tool{
  color:#fff;
  height:2.5em;
  font-size:1.1em;
}
.b-widget.b-democodeeditor [data-ref=tbar] .b-toolbar-content{
  justify-content:flex-end;
}
.b-widget.b-democodeeditor:not(.b-collapsed){
  border-inline-start:5px solid #d8d9da;
}
body.b-theme-classic-dark .b-widget.b-democodeeditor:not(.b-collapsed){
  border-inline-start-color:#111;
}
.b-widget.b-democodeeditor.b-collapsed .b-panel-collapse-revealer, .b-widget.b-democodeeditor.b-collapsing .b-panel-collapse-revealer{
  display:none;
}
.b-widget.b-democodeeditor:not(.b-resizing){
  transition:border-width 0.1s;
}
.b-widget.b-democodeeditor .b-toolbar{
  flex:0 0 auto;
  height:4em;
}
.b-widget.b-democodeeditor .b-democodeeditor-body-wrap, .b-widget.b-democodeeditor .b-panel-overlay{
  overflow:hidden;
}
.b-widget.b-democodeeditor .b-panel-content{
  direction:ltr;
}
.b-widget.b-democodeeditor.b-resizing *{
  user-select:none;
}
.b-widget.b-democodeeditor.b-over-resize-handle{
  cursor:ew-resize;
}
.b-widget.b-democodeeditor .b-header-title i{
  margin-inline-end:0.5em;
}
.b-widget.b-democodeeditor.b-hidden{
  display:flex !important;
  border-inline-start-width:0;
}
.b-widget.b-democodeeditor .b-democodeeditor-header{
  background-color:#0076f8;
}
.b-widget.b-democodeeditor .b-bottom-toolbar{
  background-color:#f3f4f5;
  color:#4f5964;
  transition:color 0.2s, background-color 0.2s;
  height:3em;
}
.readonly .b-widget.b-democodeeditor .b-bottom-toolbar{
  background-color:#ff8d46;
}
.b-widget.b-democodeeditor .b-bottom-toolbar [data-ref=cursorPos]{
  margin-inline-start:auto;
}
.b-widget.b-democodeeditor.invalid .b-democodeeditor-header i{
  color:rgba(144, 1, 1, 0.6784313725);
}
.b-widget.b-democodeeditor.invalid .b-bottom-toolbar{
  color:#fff;
  background:#b71c1c;
}
.b-widget.b-democodeeditor .b-bottom-toolbar .b-toolbar-content{
  padding:0.5em 1em !important;
}
.b-widget.b-democodeeditor .b-panel-header.b-dock-top{
  border-bottom:none;
}
.b-widget.b-democodeeditor .b-panel-header .b-tool{
  height:2.5em;
  width:2.5em;
}
.b-widget.b-democodeeditor .b-combo-picker .b-editor-file-type{
  margin-inline-end:0.5em;
}
.b-widget.b-democodeeditor .b-combo-picker .b-editor-folder{
  color:#a0a0a0;
}

@media (max-width: 450px){
  .b-democodeeditor .b-icon-download{
    display:none !important;
  }
}
.b-carousel-content:not(.b-carousel-empty){
  padding:0;
}
.b-carousel-content > .b-carousel-content{
  overflow:hidden;
}
.b-carousel-content.b-carousel-empty > .b-carousel-inner-ct, .b-carousel-content:not(.b-carousel-empty) > .b-carousel-empty-text{
  display:none;
}

.b-calendarpanel{
  -webkit-user-select:none;
  user-select:none;
  flex-shrink:0;
}
.b-calendarpanel.b-outer{
  display:inline-flex;
}
.b-calendarpanel .b-week-number-cell{
  display:none;
}
.b-calendarpanel.b-show-week-column .b-week-number-cell{
  display:flex;
  flex-direction:column;
}
.b-calendarpanel.b-hide-othermonth-cells .b-calendar-cell.b-other-month{
  visibility:hidden;
  pointer-events:none;
}
.b-calendarpanel.b-disable-othermonth-cells .b-calendar-cell.b-other-month{
  opacity:0.7;
  pointer-events:none;
}
.b-calendarpanel.b-disable-othermonth-cells .b-calendar-cell.b-other-month.b-first-visible-cell .b-cal-event-wrap{
  pointer-events:all;
}
.b-calendarpanel .b-panel-body-wrap{
  background-color:#fafafa;
}
.b-calendarpanel .b-calendar-weekdays .b-calendar-day-header{
  min-width:var(--min-column-width);
  flex:1 0 0;
  text-align:center;
}
.b-calendarpanel .b-calendarpanel-content{
  align-items:stretch;
}
.b-calendarpanel .b-calendarpanel-content.b-hide-nonworking-days .b-calendar-day-header.b-nonworking-day, .b-calendarpanel .b-calendarpanel-content.b-hide-nonworking-days .b-calendar-cell.b-nonworking-day{
  display:none;
}
.b-calendarpanel .b-calendar-row{
  display:flex;
}
.b-calendarpanel .b-calendar-cell{
  min-width:var(--min-column-width);
  display:flex;
  text-align:center;
  flex-direction:column;
  justify-content:center;
}
.b-calendarpanel .b-calendar-days{
  flex:1 1 auto;
  display:flex;
  justify-content:space-around;
  overflow:hidden;
}
.b-calendarpanel .b-weeks-container{
  display:flex;
  flex-direction:column;
}
.b-calendarpanel .b-weeks-container .b-calendar-row{
  min-height:var(--min-row-height);
}
.b-calendarpanel .b-weeks-container.b-min-columnwidth.b-horizontal-overflow{
  align-items:flex-start;
}
.b-calendarpanel .b-weeks-container.b-min-columnwidth.b-horizontal-overflow .b-calendar-row{
  overflow:hidden;
}
.b-datepicker{
  --datepicker-selected-cell-background-color:#64b5f6;
  --datepicker-selected-range-background-color:rgba(100, 181, 246, 0.15);
}
.b-datepicker:focus{
  outline:0;
}
.b-datepicker.b-highlight-selected-week .b-calendar-week{
  border:1px solid transparent;
}
.b-datepicker.b-highlight-selected-week .b-calendar-week:has(.b-selected-date){
  background-color:#f5fafe;
  border-color:#64b5f6;
}
.b-datepicker .b-datepicker-title{
  font-weight:bold;
  gap:0;
  justify-content:center;
  margin:0 !important;
  flex-wrap:nowrap;
}
.b-datepicker .b-field{
  background-color:transparent;
  border:0 none;
  color:#757575;
  min-width:0;
  display:inline-flex;
  flex:none !important;
  width:auto;
  margin-bottom:0 !important;
  height:100%;
}
.b-datepicker .b-field:not(.b-readonly):not(.b-disabled){
  cursor:pointer;
}
.b-datepicker .b-field:not(.b-readonly):not(.b-disabled):hover{
  opacity:0.7;
}
.b-datepicker .b-field .b-field-inner{
  flex:none;
  border:0 none;
  background-color:transparent;
  align-self:stretch;
  padding:0;
  box-shadow:none;
}
.b-datepicker .b-field div[type=text]{
  outline:none;
}
.b-datepicker .b-toolbar.b-dock-top{
  background-color:transparent;
  color:#757575;
  border-bottom:none;
  font-size:1.2em;
}
.b-datepicker .b-toolbar.b-dock-top .b-toolbar-content{
  min-height:0;
  padding:0.5em 0.5ex !important;
  gap:0.5ex;
  justify-content:space-around;
}
.b-datepicker .b-toolbar.b-dock-top .b-datepicker-title{
  flex:1 1 auto;
}
.b-datepicker .b-calendar-weekdays{
  background-color:transparent;
  color:#757575;
  font-size:0.85em;
}
.b-datepicker .b-calendar-weekdays .b-calendar-day-header{
  padding:0.4em 0;
}
.b-datepicker.b-show-week-column .b-week-number-cell{
  line-height:2.35em;
  width:2.35em;
  margin:5px 4px;
  text-align:center;
}
.b-datepicker .b-calendarpanel-content{
  padding:0;
  justify-content:flex-start;
}
.b-datepicker .b-week-number-cell{
  color:#76c579;
}
.b-datepicker .b-calendar-cell{
  outline:0 none;
  position:relative;
  flex:1 0 2.35em;
  padding:0 4px;
  margin:5px 0;
  transition:background-color 0.2s, color 0.2s;
  align-items:center;
}
.b-datepicker .b-calendar-cell > .b-datepicker-cell-inner{
  position:relative;
  display:flex;
  flex-direction:column;
  align-items:center;
  justify-content:center;
  height:2.35em;
  width:2.35em;
  border-width:1px;
  border-style:solid;
  border-color:transparent;
  border-radius:50%;
}
.b-datepicker .b-calendar-cell.b-today > .b-datepicker-cell-inner{
  border-color:#ffcc80;
  border-width:1px;
}
.b-datepicker .b-calendar-cell.b-active-date:not(.b-selected-date) > .b-datepicker-cell-inner{
  border-color:#64b5f6;
}
.b-datepicker .b-calendar-cell.b-selected-date:not(.b-in-range) > .b-datepicker-cell-inner{
  color:#fff;
  background-color:var(--datepicker-selected-cell-background-color);
  border-color:#64b5f6;
}
.b-datepicker .b-calendar-cell.b-other-month > .b-datepicker-cell-inner{
  color:#bbb;
}
.b-datepicker .b-calendar-cell:not(.b-disabled-date, .b-selected-date) > .b-datepicker-cell-inner{
  cursor:pointer;
}
.b-datepicker .b-calendar-cell:not(.b-disabled-date, .b-selected-date) > .b-datepicker-cell-inner:hover{
  background-color:rgba(100, 181, 246, 0.15);
  border-color:transparent;
}
.b-datepicker .b-calendar-cell:not(.b-disabled-date, .b-selected-date) > .b-datepicker-cell-inner:hover.b-today{
  background-color:#ffcc80;
  border-color:#ffcc80;
}
.b-datepicker .b-calendar-cell.b-out-of-range > .b-datepicker-cell-inner{
  color:#aaa;
}
.b-datepicker .b-calendar-cell.b-disabled-date > .b-datepicker-cell-inner{
  color:#aaa;
  opacity:0.4;
}
.b-datepicker .b-calendar-cell.b-in-range{
  background-color:var(--datepicker-selected-range-background-color);
  border-radius:0 !important;
}
.b-datepicker .b-calendar-cell.b-in-range.b-first-visible-cell{
  clip-path:polygon(0 50%, 8px 0, 100% 0, 100% 100%, 8px 100%);
}
.b-datepicker .b-calendar-cell.b-in-range.b-last-visible-cell{
  clip-path:polygon(0 0, calc(100% - 8px) 0, 100% 50%, calc(100% - 8px) 100%, 0 100%);
}
.b-datepicker .b-calendar-cell.b-range-start, .b-datepicker .b-calendar-cell.b-range-end{
  z-index:0;
}
.b-datepicker .b-calendar-cell.b-range-start:before, .b-datepicker .b-calendar-cell.b-range-end:before{
  content:" ";
  background-color:var(--datepicker-selected-range-background-color);
  position:absolute;
  left:0;
  top:0;
  right:0;
  bottom:0;
  z-index:-1;
}
.b-datepicker .b-calendar-cell.b-range-start > .b-datepicker-cell-inner, .b-datepicker .b-calendar-cell.b-range-end > .b-datepicker-cell-inner{
  position:relative;
  background-color:#fff;
}
.b-datepicker .b-calendar-cell.b-range-start > .b-datepicker-cell-inner:after, .b-datepicker .b-calendar-cell.b-range-end > .b-datepicker-cell-inner:after{
  content:" ";
  background-color:var(--datepicker-selected-range-background-color);
  border-radius:50%;
  position:absolute;
  left:0;
  top:0;
  right:0;
  bottom:0;
  z-index:-1;
}
.b-datepicker .b-calendar-cell.b-range-start:before{
  left:50%;
}
.b-datepicker .b-calendar-cell.b-range-end:before{
  right:50%;
}
.b-datepicker:not(.b-contains-focus) .b-calendar-cell.b-active-date:not(.b-selected-date) > .b-datepicker-cell-inner{
  border-color:transparent;
}
.b-datepicker.b-multiselect .b-calendar-cell{
  flex-grow:1 !important;
  margin-inline:0 !important;
}
.b-datepicker.b-rtl .b-icon::before{
  transform:scaleX(-1);
}
.b-datepicker .b-yearpicker{
  width:100%;
  height:100%;
}
.b-datepicker .b-datepicker-yearbutton{
  margin-inline-start:0.5ex;
  padding:0;
  border:0 none;
  color:inherit !important;
  min-height:unset;
  min-width:unset;
  background-color:unset;
  font-weight:400;
}
.b-datepicker .b-datepicker-yearbutton:not(.b-disabled):not(.b-tab):hover{
  opacity:0.7;
  background-color:unset;
}

.b-datepicker-nav-button.b-icon, .b-icon.b-multidatepicker-nav-button{
  border:none;
  border-radius:50%;
  min-width:0;
  min-height:0;
  width:2em;
  height:2em;
}
.b-datepicker-nav-button.b-icon:not(.b-multidatepicker-nav-button), .b-icon.b-multidatepicker-nav-button:not(.b-multidatepicker-nav-button){
  font-size:80%;
}
.b-datepicker-nav-button.b-icon:before, .b-icon.b-multidatepicker-nav-button:before{
  line-height:normal;
}
.b-datepicker-nav-button.b-icon, .b-icon.b-multidatepicker-nav-button, .b-datepicker-nav-button.b-icon:not(.b-tab):active:not([disabled]), .b-datepicker-nav-button.b-icon:not(.b-tab):active:focus:not([disabled]){
  color:#757575;
}
.b-datepicker-nav-button.b-icon:hover, .b-icon.b-multidatepicker-nav-button:hover{
  opacity:0.7;
}
.b-datepicker-nav-button.b-icon:not(.b-contains-focus), .b-icon.b-multidatepicker-nav-button:not(.b-contains-focus){
  background:transparent;
}

.b-readonly-combo-list.b-empty{
  display:none;
}
.b-readonly-combo-list .b-list-item{
  min-width:auto;
}
.b-visible-scrollbar .b-readonly-combo-list .b-list-item{
  padding-inline-end:2em;
}
.b-fieldset.b-has-label{
  align-items:center;
}
.b-fieldset.b-has-label.b-label-before > .b-panel-body-wrap{
  flex:1 1 100%;
}
.b-fieldset.b-has-label > label{
  flex-shrink:0;
}

.b-fieldset-header{
  background-color:transparent;
  padding:0.5em;
  color:#262626;
}
.b-fieldset-header .b-header-title{
  font-weight:600;
}

fieldset.b-fieldset-content{
  border:none;
  background-color:transparent;
  margin-inline-start:0;
  margin-inline-end:0;
  overflow:unset;
  padding-block-end:0;
}
.b-panel-has-header > .b-radiogroup-body-wrap > fieldset.b-fieldset-content{
  padding-block-start:1em;
  padding-inline-start:1em;
  padding-inline-end:1em;
}
.b-fieldset:not(.b-panel-has-header) > .b-radiogroup-body-wrap > fieldset.b-fieldset-content{
  padding:0;
}
fieldset.b-fieldset-content.b-inline > .b-field:not(:first-child){
  margin-inline-start:1em;
}
fieldset.b-fieldset-content > legend.b-fieldset-legend{
  position:absolute;
  top:-10000px;
  clip:rect(0, 0, 0, 0);
}
.b-popup{
  --panel-background-color:#f9f9f9;
  color:#616161;
}
.b-popup.b-text-popup{
  max-width:25em;
}
.b-popup.b-positioned{
  box-shadow:2px 2px 6px rgba(0, 0, 0, 0.1);
}

.b-popup-content a{
  color:#2e2e2e;
}

.b-popup-header{
  background-color:#64b5f6;
  color:#fff;
}

.b-modal-mask{
  background-color:rgba(100, 100, 100, 0.5);
  position:fixed;
  top:0;
  left:0;
  bottom:0;
  right:0;
  pointer-events:all;
  z-index:11001;
}
.b-modal-mask.b-modal-transparent{
  background-color:transparent;
}

@media (max-width: 480px){
  .b-popup{
    max-width:100% !important;
  }
}
.b-messagedialog.b-popup{
  min-width:18em;
}
.b-messagedialog.b-popup .b-button{
  min-width:9em;
}
.b-messagedialog.b-popup .b-messagedialog-content{
  padding:1em;
}
.b-messagedialog.b-popup .b-messagedialog-content.b-panel-content{
  border-radius:0;
  min-height:4em;
}
.b-messagedialog.b-popup .b-messagedialog-input,
.b-messagedialog.b-popup .b-messagedialog-okbutton,
.b-messagedialog.b-popup .b-messagedialog-cancelbutton{
  display:none;
}
.b-messagedialog.b-popup.b-messagedialog-prompt .b-messagedialog-okbutton,
.b-messagedialog.b-popup.b-messagedialog-prompt .b-messagedialog-cancelbutton, .b-messagedialog.b-popup.b-messagedialog-confirm .b-messagedialog-okbutton,
.b-messagedialog.b-popup.b-messagedialog-confirm .b-messagedialog-cancelbutton{
  display:initial;
}
.b-messagedialog.b-popup.b-messagedialog-confirm .b-messagedialog-message, .b-messagedialog.b-popup.b-messagedialog-alert .b-messagedialog-message{
  display:block;
}
.b-messagedialog.b-popup.b-messagedialog-prompt .b-messagedialog-input{
  display:initial;
  margin:1em 0 0 0;
}
.b-messagedialog.b-popup.b-messagedialog-alert .b-messagedialog-okbutton{
  display:initial;
}
.b-messagedialog.b-popup .b-panel-body-wrap{
  background:#f9f9f9;
}
.b-messagedialog.b-popup .b-popup-content,
.b-messagedialog.b-popup .b-toolbar{
  background:transparent;
}
.b-messagedialog.b-popup .b-toolbar .b-toolbar-content{
  justify-content:flex-end;
}

.b-confirmationbar .b-toolbar-content{
  justify-content:flex-end;
}
.b-confirmationbar .b-toolbar-content .b-button:not(:first-child){
  margin-inline-start:1em;
}

.b-multidatepicker-nav-button{
  top:0.25em;
  transition:opacity 0.2s ease;
}
.b-panel-has-header.b-header-dock-top > .b-multidatepicker-nav-button{
  top:4.5em;
}
.b-multidatepicker:not(.b-multidatepicker-nav-buttons) > .b-multidatepicker-nav-button{
  display:none;
}

.b-multidatepicker-next-button{
  right:0.3em;
}

.b-multidatepicker-prev-button{
  left:0.3em;
}

.b-multidatepicker-nav-floating{
  overflow:visible;
}
.b-multidatepicker-nav-floating > .b-button.b-multidatepicker-nav-button{
  top:50%;
  box-shadow:0 1px 2px 0 rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15);
  background-color:#fafafa;
  padding:1.4em;
}
.b-multidatepicker-nav-floating > .b-button.b-multidatepicker-nav-button:focus, .b-multidatepicker-nav-floating > .b-button.b-multidatepicker-nav-button:hover{
  background-color:white;
  opacity:unset;
}
.b-multidatepicker-nav-floating > .b-multidatepicker-next-button{
  right:0;
  translate:50% -50%;
}
.b-multidatepicker-nav-floating > .b-multidatepicker-prev-button{
  left:0;
  translate:-50% -50%;
}

.b-multidatepicker-content > .b-carousel-inner-ct .b-datepicker-content .b-calendar-week,
.b-multidatepicker-content > .b-carousel-inner-ct .b-datepicker-content .b-calendar-weekdays{
  padding-inline:1.5em;
}
.b-multidatepicker-content > .b-carousel-inner-ct .b-datepicker-content .b-weeks-container{
  padding-block:0.4em;
}
.b-multidatepicker-content > .b-carousel-inner-ct .b-other-month{
  visibility:hidden;
  pointer-events:none;
}
.b-multidatepicker-content > .b-carousel-inner-ct [data-ref=nextMonth],
.b-multidatepicker-content > .b-carousel-inner-ct [data-ref=prevMonth],
.b-multidatepicker-content > .b-carousel-inner-ct [data-ref=prevYear],
.b-multidatepicker-content > .b-carousel-inner-ct [data-ref=nextYear]{
  display:none;
}
.b-multidatepicker-content > .b-carousel-inner-ct .b-datepicker.b-carousel-visible.b-carousel-first > .b-top-focus-trap,
.b-multidatepicker-content > .b-carousel-inner-ct .b-datepicker.b-carousel-visible.b-carousel-last > .b-end-focus-trap{
  display:none;
}
.b-multidatepicker-content > .b-carousel-inner-ct .b-datepicker.b-carousel-reserve [data-ref=monthField]{
  pointer-events:none;
}
.b-multidatepicker-content > .b-carousel-inner-ct .b-datepicker.b-carousel-reserve [data-ref=yearButton]{
  display:none;
}
.b-multidatepicker-content > .b-carousel-inner-ct .b-datepicker .b-calendar-cell > .b-datepicker-cell-inner{
  cursor:pointer;
}

.b-daterangepicker .b-start-date,
.b-daterangepicker .b-end-date{
  width:13em;
}
.b-daterangepicker.b-daterangefield-picker .b-end-date{
  width:11em;
}
.b-daterangepicker.b-picking-start-date .b-range-end:not(.b-range-start), .b-daterangepicker:not(.b-picking-start-date) .b-range-start:not(.b-range-end){
  --datepicker-selected-cell-background-color:#cde5f9;
}

.b-daterangefield{
  width:unset;
}
.b-daterangefield > .b-field-container-wrap > .b-fieldcontainer{
  min-width:24.5em;
}
.b-daterangefield > .b-field-container-wrap > .b-fieldcontainer > .b-widget:not(:first-child){
  margin-inline-start:0.5em;
}
.b-daterangefield.b-pick-time > .b-field-container-wrap > .b-fieldcontainer{
  min-width:39.5em;
}
.b-daterangefield .b-start-date{
  flex:7 0 auto;
  width:13em;
}
.b-daterangefield .b-end-date{
  flex:5 0 auto;
  width:11em;
}
.b-daterangefield .b-end-time,
.b-daterangefield .b-start-time{
  flex:7 7 auto;
}
.b-menu{
  min-height:20px;
  outline:none;
  display:flex;
  flex-direction:column;
  align-items:stretch;
  -webkit-user-select:none;
  user-select:none;
}
.b-menu .b-panel-content{
  background-color:white;
}
.b-menu.b-empty::after{
  content:attr(data-empty-text);
  display:block;
  padding:0.8em;
  color:#616161;
}
.b-menu .b-icon:before, .b-menu .b-icon-fw:before{
  min-width:1.3em;
}
.b-menu.b-menu-with-submenu .b-menuitem:not(.b-has-submenu) .b-menu-text{
  margin-inline-end:2em;
}

.b-menu-content{
  justify-content:flex-start;
  align-items:stretch;
  padding:0;
}
.b-menu-content > :not(.b-menuitem){
  margin:0.2em 0.6em;
  display:flex;
}
.b-menu-content > :not(.b-menuitem):first-child{
  margin-top:0.4em;
}
.b-menu-content > :not(.b-menuitem):last-child{
  margin-bottom:0.4em;
}
.b-menu-content > .b-button{
  justify-content:flex-start;
}

.b-menuitem{
  flex-shrink:0;
  padding:0.8em;
  color:#616161;
  cursor:pointer;
  transition:background-color 0.2s, color 0.2s;
  border-top:1px dotted rgba(224, 224, 224, 0.8);
  font-size:1em;
  min-width:7em;
  display:flex;
  align-items:center;
}
.b-menuitem.b-disabled{
  opacity:0.5;
}
.b-menuitem:first-child{
  border-top:none;
  border-top-right-radius:2px;
  border-top-left-radius:2px;
}
.b-menuitem:last-child{
  border-bottom-right-radius:2px;
  border-bottom-left-radius:2px;
}
.b-menuitem.b-separator:not(.b-first-visible-child){
  border-top:1px solid #f9f9f9;
}
.b-menuitem.b-contains-focus, .b-menuitem:focus{
  outline:none;
  background-color:#64b5f6;
  color:#616161;
}
.b-menuitem span.b-menu-text{
  flex:1 1 auto;
  white-space:nowrap;
  margin-inline-start:0.6em;
  overflow:hidden;
  text-overflow:ellipsis;
  min-width:5em;
}
.b-menuitem.b-has-submenu .b-icon-sub-menu{
  margin-inline-start:0.3em;
}
.b-menuitem.b-rtl .b-icon-sub-menu::before{
  transform:scaleX(-1);
}

a.b-menuitem:hover .b-menu-text{
  text-decoration:underline;
}

.b-menu-with-icon .b-menuitem-icon{
  width:1.25em;
  text-align:center;
  flex-shrink:0;
  margin-inline-end:0.5em;
}
.b-menu-with-icon span.b-menu-text{
  margin-inline-start:1.75em;
}
.b-menu-with-icon .b-menuitem-icon ~ span.b-menu-text{
  margin-inline-start:0;
}
.b-radio{
  --radio-background-color:#fff;
  --radio-dot-color:var(----radio-background-color);
  --radio-border-color:rgb(var(--widget-primary-color-rgb));
  --radio-checked-dot-color:rgb(var(--widget-primary-color-rgb));
  --radio-disabled-color:#f9f9f9;
}
.b-radio.b-disabled{
  --radio-border-color:var(--radio-disabled-color);
  --radio-checked-dot-color:var(--radio-disabled-color);
}
.b-radio input[type=radio]:checked{
  --radio-dot-color:var(--radio-checked-dot-color);
}
.b-radio input[type=radio]{
  border-radius:50%;
  box-shadow:0 0 0 1px var(--radio-border-color);
  border:0.25em solid var(--radio-background-color);
  background-color:var(--radio-dot-color);
  height:1.3em;
  width:1.3em;
  min-width:1.3em;
  margin:1px;
  z-index:1;
  -webkit-appearance:none;
  appearance:none;
  cursor:pointer;
}
.b-radio input[type=radio]:focus-visible{
  outline:1px solid #ffcc80;
  outline-offset:0;
}
.b-radio > .b-field-inner label.b-radio-label{
  margin-inline-start:0.4em;
}
.b-radio > .b-field-inner label.b-radio-label:before{
  content:none;
}

@media screen and (min-resolution: 120dpi){
  .b-firefox .b-radio input{
    transform:rotate(1deg);
    margin:calc(1px + 0.5px);
  }
}
.b-panel.b-radiogroup{
  --panel-background-color:transparent;
}
.b-panel.b-radiogroup .b-fieldset-content{
  gap:0.75em;
}

.b-ripple{
  display:none;
}
.b-slider{
  background-color:transparent;
  column-gap:0.5em;
  align-items:center;
  overflow:visible;
}
.b-slider.b-has-label{
  flex-direction:column;
}
.b-slider.b-has-label.b-has-field-label{
  display:grid;
  grid-template-columns:auto auto;
}
.b-slider .b-slider-value{
  grid-column:1/-1;
  grid-row:2;
  justify-self:center;
  transition:color 0.2s;
}
.b-slider.b-rtl > [type=range]{
  direction:rtl;
}
.b-slider [type=range]{
  flex:1;
  align-self:stretch;
  max-width:100%;
  min-width:0;
  -webkit-appearance:none;
  margin:10px 0;
  padding:0;
  font-size:inherit;
  background-color:transparent;
}
.b-slider [type=range]:focus{
  outline:0;
}
.b-slider [type=range]:focus::-webkit-slider-runnable-track{
  background:#c4e3fc;
}
.b-slider [type=range]:focus::-moz-range-track{
  background:#c4e3fc;
}
.b-slider [type=range]:focus::-ms-fill-lower, .b-slider [type=range]:focus::-ms-fill-upper{
  background:#c4e3fc;
}
.b-slider [type=range]:focus::-webkit-slider-thumb{
  box-shadow:0 2px 4px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.2);
}
.b-slider [type=range]::-moz-focus-outer{
  border:0;
}
.b-slider [type=range]::-ms-tooltip{
  display:none;
}
.b-slider [type=range]::-webkit-slider-runnable-track{
  cursor:pointer;
  height:8px;
  transition:all 0.2s ease;
  width:100%;
  background:#e6e6e6;
  border:none;
  border-radius:2px;
}
.b-slider [type=range]::-webkit-slider-thumb{
  background:#64b5f6;
  border:none;
  border-radius:50%;
  cursor:pointer;
  height:20px;
  width:20px;
  -webkit-appearance:none;
  margin-top:-6px;
  transition:all 0.5s;
}
.b-slider [type=range]::-moz-range-track{
  cursor:pointer;
  height:8px;
  transition:all 0.2s ease;
  width:100%;
  background:#e6e6e6;
  border:none;
  border-radius:2px;
}
.b-slider [type=range]::-moz-range-thumb{
  background:#64b5f6;
  border:none;
  border-radius:50%;
  cursor:pointer;
  height:20px;
  width:20px;
}
.b-slider [type=range]::-ms-track{
  cursor:pointer;
  height:8px;
  transition:all 0.2s ease;
  width:100%;
  background:transparent;
  border-color:transparent;
  border-width:10px 0;
  color:transparent;
}
.b-slider [type=range]::-ms-fill-lower, .b-slider [type=range]::-ms-fill-upper{
  background:#e6e6e6;
  border:none;
  border-radius:4px;
}
.b-slider [type=range]::-ms-thumb{
  background:#64b5f6;
  border:none;
  border-radius:50%;
  cursor:pointer;
  height:20px;
  width:20px;
  margin-top:0;
}
.b-slider.b-disabled [type=range]::-ms-thumb, .b-slider.b-disabled [type=range]::-ms-fill-lower, .b-slider.b-disabled [type=range]::-ms-fill-upper{
  background:#d9d9d9;
  cursor:default;
}
.b-slider.b-disabled [type=range]::-moz-range-thumb, .b-slider.b-disabled [type=range]::-moz-range-track{
  background:#d9d9d9;
  cursor:default;
}
.b-slider.b-disabled [type=range]::-webkit-slider-thumb, .b-slider.b-disabled [type=range]::-webkit-slider-runnable-track{
  background:#d9d9d9;
  cursor:default;
}
.b-grid-cell .b-slider{
  flex:1;
}

.b-container .b-slider label{
  margin:0;
}
.b-slidetoggle{
  --widget-primary-color-rgb:100,181,246;
  --slidetoggle-background-color:rgb(var(--widget-primary-color-rgb));
  --slidetoggle-toggle-background-opacity:0.5;
  --slidetoggle-toggle-background-color:rgba(var(--widget-primary-color-rgb), var(--slidetoggle-toggle-background-opacity));
  height:3.25em;
}
.b-slidetoggle.b-disabled{
  --slidetoggle-toggle-background-color:#f9f9f9;
  --slidetoggle-background-color:#f9f9f9;
}
.b-slidetoggle .b-field-inner::before{
  width:2.4em;
}
.b-slidetoggle input,
.b-slidetoggle .b-slidetoggle-toggle{
  width:2.4em;
  height:1.4em;
  font-size:inherit;
  flex-shrink:0;
}
.b-slidetoggle input{
  top:-0.4em;
}
.b-slidetoggle input:checked + .b-slidetoggle-toggle{
  opacity:1;
  background-color:var(--slidetoggle-background-color);
}
.b-slidetoggle input:checked + .b-slidetoggle-toggle .b-slidetoggle-thumb{
  background-color:#fff;
  transform:translate(1.2em, 0.2em);
}
.b-slidetoggle .b-slidetoggle-toggle{
  transition:background-color 0.2s, opacity 0.2s;
  opacity:0.7;
  border-radius:0.7em;
  margin-inline-end:0.4em;
  background-color:rgba(204, 204, 204, 0.6);
}
.b-slidetoggle:hover .b-slidetoggle-toggle{
  opacity:1;
}
.b-slidetoggle .b-slidetoggle-thumb{
  height:1em;
  width:1em;
  background-color:white;
  border-radius:0.5em;
  position:absolute;
  transform:translate(0.2em, 0.2em);
  transition:transform 0.3s ease;
}
.b-slidetoggle.b-rtl .b-slidetoggle-thumb{
  transform:translate(-0.2em, 0.2em);
}
.b-slidetoggle.b-rtl input:checked + .b-slidetoggle-toggle .b-slidetoggle-thumb{
  transform:translate(-1.2em, 0.2em);
}

.b-slidetoggle-label{
  cursor:pointer;
}

.b-container .b-slidetoggle .b-slidetoggle-label{
  margin-inline-start:0;
}
.b-splitter{
  background-color:#f0f0f0;
  flex:0 0 0.5em;
  position:relative;
  touch-action:none;
}
@media (pointer: coarse){
  .b-splitter{
    --splitter-inner-size:2em;
  }
}
@media (pointer: fine){
  .b-splitter{
    --splitter-inner-size:0;
  }
}
.b-splitter.b-disabled{
  flex:0 0 1px;
  pointer-events:none;
}
.b-splitter:not(.b-disabled){
  overflow:visible;
}
.b-splitter::after{
  content:"";
  position:absolute;
  left:0;
  top:0;
  z-index:1;
}
.b-splitter:hover::after, .b-splitter.b-hover::after, .b-splitter.b-moving::after{
  background-color:#f0f0f0;
}
.b-splitter.b-horizontal{
  cursor:ns-resize;
  min-height:0.5em;
}
.b-splitter.b-horizontal.b-disabled{
  min-height:1px;
}
.b-splitter.b-horizontal::after{
  height:var(--splitter-inner-size);
  width:100%;
}
.b-splitter.b-horizontal:hover::after, .b-splitter.b-horizontal.b-hover::after, .b-splitter.b-horizontal.b-moving::after{
  top:calc((10px - 0.5em) / -2);
  height:10px;
}
.b-splitter.b-vertical{
  cursor:ew-resize;
  min-width:0.5em;
}
.b-splitter.b-vertical.b-disabled{
  min-width:1px;
}
.b-splitter.b-vertical::after{
  width:var(--splitter-inner-size);
  height:100%;
}
.b-splitter.b-vertical:hover::after, .b-splitter.b-vertical.b-hover::after, .b-splitter.b-vertical.b-moving::after{
  left:calc((10px - 0.5em) / -2);
  width:10px;
}
.b-splitter:last-child, .b-splitter:first-child{
  visibility:hidden;
  flex:0;
  min-width:0;
}
.b-timefield.b-open .b-icon-clock-live{
  background-color:#64b5f6 !important;
}
.b-timefield.b-empty .b-fieldtrigger{
  animation-delay:-300s;
}
.b-timefield.b-disabled .b-icon-clock-live, .b-timefield.b-readonly .b-icon-clock-live{
  background-color:rgba(240, 240, 240, 0.5);
}
.b-timefield:not(.b-disabled):not(.b-readonly) .b-icon-angle-left:hover,
.b-timefield:not(.b-disabled):not(.b-readonly) .b-icon-angle-right:hover{
  color:#64b5f6;
}
.b-timefield.b-no-steppers .b-step-trigger{
  display:none;
}
.b-timefield .b-step-trigger::before{
  transition:color 0.3s;
}
.b-timefield.b-rtl .b-step-trigger::before{
  transform:scaleX(-1);
}
.b-timepicker .b-panel-content{
  background-color:#fafafa;
  align-items:stretch;
  padding:0.5em;
  gap:0.5em;
}
.b-timepicker .b-panel-content .b-widget{
  margin:0;
  flex:0 0 auto;
  width:auto;
}
.b-timepicker .b-panel-content .b-numberfield.b-has-label{
  margin-top:0;
}
.b-timepicker .b-panel-content .b-numberfield > .b-label{
  clip-path:polygon(0 0);
  position:absolute;
  contain:strict;
}
.b-timepicker .b-panel-content .b-numberfield:not(.b-first-visible-child)::before{
  content:":";
  align-self:center;
  margin-inline-end:0.5em;
  font-weight:bold;
}
.b-timepicker .b-panel-content .b-numberfield input{
  width:2.8em;
}
.b-timepicker .b-panel-content .b-button{
  flex:0 0 3em;
  padding:0;
}
.b-timepicker .b-panel-content .b-button label{
  text-overflow:clip;
}
@keyframes progress{
  0%{
    width:0;
  }
  100%{
    width:100%;
  }
}
.b-toast{
  --widget-primary-color-rgb:250,250,250;
  --toast-background-color:rgb(var(--widget-primary-color-rgb));
  --toast-color:#616161;
  --toast-progress-background-color:#64b5f6;
  background-color:var(--toast-background-color);
  color:var(--toast-color);
}

.b-float-root > .b-floating.b-toast{
  display:inline-block;
  overflow-x:hidden;
  overflow-y:auto;
  top:auto;
  inset-inline-end:2em;
  inset-inline-start:auto;
  max-width:60%;
  max-height:60%;
  line-height:1.4em;
  transition:var(--side) 0.25s ease-in, transform 0.25s ease-in;
  padding:1em;
  border-radius:2px;
  cursor:pointer;
}
.b-float-root > .b-floating.b-toast:is(.b-side-top-start, .b-side-top-end){
  top:0;
}
.b-legacy-inset .b-float-root > .b-floating.b-toast{
  left:auto;
  right:2em;
}
.b-legacy-inset .b-float-root > .b-floating.b-toast.b-rtl{
  left:2em;
  right:auto;
}
.b-float-root > .b-floating.b-toast:is(.b-side-top-start, .b-side-bottom-start){
  inset-inline-start:2em;
  inset-inline-end:auto;
}
.b-float-root > .b-floating.b-toast.b-toast-hide:is(.b-side-bottom-end, .b-side-bottom-start){
  bottom:0 !important;
  transform:translateY(100%) !important;
}
.b-float-root > .b-floating.b-toast.b-toast-hide:is(.b-side-top-end, .b-side-top-start){
  top:0 !important;
  transform:translateY(-100%) !important;
}
.b-float-root > .b-floating.b-toast.b-icon:before{
  margin-inline-end:0.5em;
}

.b-toast-progress{
  position:absolute;
  top:0;
  inset-inline-start:0;
  height:3px;
  background:var(--toast-progress-background-color);
  animation-name:progress;
  animation-timing-function:linear;
}
.b-legacy-inset .b-toast-progress{
  left:0;
}
.b-legacy-inset .b-toast-progress.b-rtl{
  right:0;
}
.b-tooltip{
  --panel-background-color:#fffef6;
}
.b-tooltip:not(.b-allow-over){
  -webkit-user-select:none;
  user-select:none;
}
.b-tooltip .b-tooltip-loading .b-icon{
  display:inline-block;
  margin-inline-end:0.5em;
}
.b-tooltip .b-panel-content.b-no-child-elements{
  padding-block:0.25em;
  margin-block:0.5em;
}

.b-tooltip-content{
  background:inherit;
  color:#616161;
  font-size:1em;
  line-height:1.4em;
  padding:1em;
}
.b-tooltip-content a{
  color:#616161;
}

.b-tooltip .b-tooltip-header .b-tool{
  color:#fff;
}

.b-textareafield textarea,
.b-textareapickerfield textarea{
  padding:0.8em;
  align-self:stretch;
}

.b-textareapickerfield-picker{
  display:flex;
  flex:1;
  min-height:10em;
}
.b-textareapickerfield-picker:focus{
  outline:none;
  border-color:#ffcc80;
}
.b-tabpanel-tabs{
  display:flex;
  flex:0 0 auto;
  flex-direction:row;
  margin-bottom:0;
  background-color:transparent;
}
.b-toolbar .b-tabpanel-tabs{
  gap:0;
}
.b-tabpanel-tab{
  display:flex;
  padding:0.6em 1em;
  cursor:pointer;
  transition:background-color 0.2s, color 0.2s;
  align-items:center;
  justify-content:center;
  font-weight:400;
  text-transform:none;
  color:#777;
  background-color:transparent;
  border-width:0;
  border-style:solid;
  border-color:transparent;
  border-top-left-radius:2px;
  border-top-right-radius:2px;
}
.b-tabpanel-tab.b-rotate-vertical{
  min-width:auto;
  min-height:3em;
}
.b-tabpanel-tab:not(.b-rotate-vertical){
  min-height:auto;
  min-width:3em;
}
.b-tabpanel-tab.b-active{
  background-color:#fefefe;
  border-color:transparent;
  color:#616161;
  z-index:1;
  --b-tabpanel-tabs-pseudo-border:none;
}
.b-dock-top > .b-tabpanel-tabs > .b-tabpanel-tab::before{
  content:"";
  position:absolute;
  bottom:0;
  inset-inline:0;
  border-bottom:var(--b-tabpanel-tabs-pseudo-border);
}
.b-dock-left > .b-tabpanel-tabs > .b-tabpanel-tab{
  border-width:0 0 0 0;
}
.b-dock-right > .b-tabpanel-tabs > .b-tabpanel-tab{
  border-width:0 0 0 0;
}
.b-dock-bottom > .b-tabpanel-tabs > .b-tabpanel-tab{
  border-width:0 0 0 0;
}
.b-tabpanel-tab.b-hidden{
  display:none !important;
}
.b-tabpanel-tab.b-disabled{
  background-color:transparent;
  color:rgba(119, 119, 119, 0.3);
  border-color:rgba(0, 0, 0, 0);
}
.b-tabpanel-tab:focus{
  outline:none;
}
.b-tabpanel-tab:hover:not(.b-active, .b-disabled), .b-tabpanel-tab:focus:not(.b-active, .b-disabled){
  color:#616161;
  --b-tabpanel-tabs-pseudo-border:none;
}
.b-tabpanel-tab:hover:not(.b-active, .b-disabled){
  background-color:#f4f4f4;
}
.b-tabpanel-body.b-card-container{
  background-color:#fefefe;
  border-radius:2px;
  border:0 solid transparent;
  flex:1 0 auto;
  padding:1em;
}
.b-tabpanel[data-active-index="0"] .b-tabpanel-body{
  border-top-left-radius:0;
}
.b-tabpanel .b-html{
  color:#616161;
}

.b-toolbar.b-tabbar{
  background-color:var(--panel-background-color);
}
.b-toolbar.b-tabbar > .b-tabpanel-tabs{
  min-height:auto;
  padding:0;
}
.b-toolbar.b-tabbar.b-dock-top{
  border-bottom:none;
}
.b-toolbar.b-tabbar.b-dock-top::before{
  content:"";
  position:absolute;
  bottom:0;
  inset-inline:0;
}
.b-tabbar.b-dock-top .b-tabpanel-tabs > .b-widget:not(.b-last-visible-child), .b-tabbar.b-dock-bottom .b-tabpanel-tabs > .b-widget:not(.b-last-visible-child), .b-tabbar.b-dock-left .b-tabpanel-tabs > .b-widget:not(.b-last-visible-child), .b-tabbar.b-dock-right .b-tabpanel-tabs > .b-widget:not(.b-last-visible-child){
  margin-inline-end:0;
  margin-bottom:0;
}

.b-toolbar.b-undoredo{
  padding:0;
  gap:0;
}
.b-toolbar.b-undoredo > *{
  border-radius:0;
}
.b-buttongroup .b-toolbar.b-undoredo:not(:first-child) > button:first-child{
  margin-inline-start:-1px;
}
.b-buttongroup .b-toolbar.b-undoredo > .b-widget{
  margin-inline-end:0;
}
.b-toolbar.b-undoredo .b-widget.b-combo{
  flex:1 1 250px;
  margin:0 0.5em;
}
.b-theme-material .b-toolbar.b-undoredo .b-widget.b-combo .b-field-inner{
  padding-inline:0.7em;
}

.b-yearpicker{
  width:max-content;
}
.b-yearpicker .b-toolbar.b-dock-top{
  background-color:transparent;
  color:#757575;
  border-bottom:none;
  min-height:0;
  font-size:1.2em;
}
.b-yearpicker .b-toolbar.b-dock-top .b-toolbar-content{
  padding:0.5em 0.5ex !important;
  gap:0.5ex;
}
.b-yearpicker .b-toolbar.b-dock-top .b-icon:before{
  line-height:normal;
}
.b-yearpicker .b-toolbar.b-dock-top button{
  border:none;
  min-height:0;
}
.b-yearpicker .b-toolbar.b-dock-top button, .b-yearpicker .b-toolbar.b-dock-top button:not(.b-tab):active:not([disabled]), .b-yearpicker .b-toolbar.b-dock-top button:not(.b-tab):active:focus:not([disabled]){
  color:#757575;
}
.b-yearpicker .b-toolbar.b-dock-top .b-tool{
  color:#757575;
  font-size:80%;
}
.b-yearpicker .b-toolbar.b-dock-top .b-tool:hover{
  opacity:0.7;
}
.b-yearpicker .b-toolbar.b-dock-top .b-tool:not(.b-contains-focus){
  background:transparent !important;
}
.b-yearpicker .b-toolbar.b-dock-top .b-yearpicker-title{
  margin-inline:auto;
  color:inherit;
  font-weight:400;
  background-color:unset;
}
.b-yearpicker .b-toolbar.b-dock-top .b-yearpicker-title[data-item-index="0"]{
  margin-inline:0.5em auto !important;
}
.b-yearpicker .b-toolbar.b-dock-top .b-yearpicker-title:not(.b-disabled):not(.b-tab):hover{
  opacity:0.7;
  background-color:unset;
}
.b-yearpicker .b-yearpicker-body-wrap{
  background-color:#fafafa;
}
.b-yearpicker .b-yearpicker-content{
  display:grid;
  align-items:center;
  grid-template-columns:repeat(4, 1fr);
  padding:1em;
  gap:1em;
}
.b-yearpicker .b-yearpicker-content button.b-yearpicker-year{
  border-width:1px;
  border-style:solid;
  border-color:transparent;
  border-radius:1em;
  height:2em;
  background:transparent;
  font-size:1em;
  cursor:pointer;
  color:#616161;
  font-family:inherit;
  font-weight:400;
  padding-inline:0;
}
.b-yearpicker .b-yearpicker-content button.b-yearpicker-year:hover{
  background-color:rgba(100, 181, 246, 0.15);
  border-color:transparent;
}
.b-yearpicker .b-yearpicker-content button.b-yearpicker-year.b-selected{
  color:#fff;
  background-color:#64b5f6;
  border-color:#64b5f6;
}
.b-yearpicker.b-outer, .b-yearpicker.b-floating{
  min-width:20.4em;
  min-height:18.2em;
}
.b-histogram{
  background-color:#fff;
  padding:1px;
  contain:strict;
}
.b-histogram svg *{
  vector-effect:non-scaling-stroke;
}
.b-histogram rect{
  fill:#a5d6a7;
  transition-property:x, y, width, height, fill;
  transition-duration:1000ms;
}
.b-histogram rect.b-series-index-0{
  fill:#a5d6a7;
}
.b-histogram rect.b-series-index-1{
  fill:#64b5f6;
}
.b-histogram rect.b-series-index-2{
  fill:#3f51b5;
}
.b-histogram rect.b-series-index-3{
  fill:#009688;
}
.b-histogram rect.b-series-index-4{
  fill:#ffcc80;
}
.b-histogram rect.b-series-index-5{
  fill:#cddc39;
}
.b-histogram rect.b-exceeds-top{
  fill:#ef9a9a;
}
.b-histogram text.b-bar-legend{
  writing-mode:tb;
  text-anchor:end;
  font-size:75%;
  pointer-events:none;
}
.b-histogram svg{
  overflow:visible;
}
.b-histogram path{
  fill:transparent;
  stroke:#f99;
  transition:d 1000ms;
  pointer-events:none;
}
.b-scale{
  contain:strict;
}
.b-scale svg *{
  vector-effect:non-scaling-stroke;
}
.b-scale.b-scale-vertical text.b-scale-tick-label{
  transform:translateY(0.3em);
}
.b-scale.b-scale-vertical.b-align-right{
  text-anchor:end;
}
.b-scale.b-scale-horizontal text.b-scale-tick-label{
  text-anchor:middle;
}
.b-scale path{
  stroke:#9c9c9c;
  pointer-events:none;
}
.b-scale text{
  fill:#808080;
}

.b-hbox,
.b-vbox{
  display:flex;
  align-items:stretch;
  justify-content:flex-start;
  position:relative;
}
.b-hbox > .b-box-center,
.b-vbox > .b-box-center{
  flex:1 1 auto;
  overflow:hidden;
}

.b-hbox{
  flex-flow:row nowrap;
}

.b-vbox{
  flex-flow:column nowrap;
}

.b-box-justify-stretch{
  justify-content:stretch;
}

.b-card-container{
  display:flex;
  flex-flow:row nowrap;
  align-items:stretch;
  overflow:hidden;
  padding:0;
  position:relative;
}
.b-card-container.b-animating{
  overflow:hidden;
}
.b-card-container > .b-card-item{
  flex:1 0 100%;
  align-items:stretch;
  max-width:100%;
}
.b-card-container.b-hide-child-headers > .b-panel:not(.b-positioned) > .b-panel-header{
  display:none;
}

@keyframes b-card-slide-in-left{
  0%{
    transform:translateX(-100%);
  }
  100%{
    transform:translateX(0);
  }
}
@keyframes b-card-slide-out-right{
  0%{
    transform:translateX(-100%);
  }
  100%{
    transform:translateX(0);
  }
}
@keyframes b-card-slide-in-right{
  0%{
    transform:translateX(0);
  }
  100%{
    transform:translateX(-100%);
  }
}
@keyframes b-card-slide-out-left{
  0%{
    transform:translateX(0);
  }
  100%{
    transform:translateX(-100%);
  }
}
.b-slide-in-left{
  animation:b-card-slide-in-left 0.3s ease 0s 1;
}

.b-slide-out-right{
  pointer-events:none;
  left:1em;
  animation:b-card-slide-out-right 0.3s ease 0s 1;
}

.b-slide-in-right{
  animation:b-card-slide-in-right 0.3s ease 0s 1;
}

.b-slide-out-left{
  pointer-events:none;
  margin-inline-start:-1em;
  margin-inline-end:1em;
  animation:b-card-slide-out-left 0.3s ease 0s 1;
}

.b-fit-container{
  display:flex;
  flex-flow:row nowrap;
  align-items:stretch;
  overflow:hidden;
  position:relative;
}
.b-fit-container > .b-fit-item{
  flex:1 0 auto;
  margin:0;
  max-width:100%;
  align-self:stretch !important;
}
.b-resource-avatar{
  touch-action:pan-x pan-y;
  display:flex;
  align-items:center;
  justify-content:center;
  width:2.2em;
  height:2.2em;
  border-radius:50%;
  flex-shrink:0;
}
.b-resource-avatar.b-resource-icon:before{
  font-size:1.5em;
}

.b-resource-initials{
  background:#64b5f6;
  color:#fff;
  text-transform:uppercase;
}

@keyframes fadeInOpacity{
  0%{
    opacity:0;
  }
  100%{
    opacity:1;
  }
}
.b-color-indigo{
  background-color:#3f51b5;
}

.b-color-blue{
  background-color:#64b5f6;
}

.b-color-cyan{
  background-color:#3bc9db;
}

.b-color-red{
  background-color:#ef9a9a;
}

.b-color-deep-orange{
  background-color:#ff5722;
}

.b-color-orange{
  background-color:#ffcc80;
}

.b-color-amber{
  background-color:#ffd54f;
}

.b-color-yellow{
  background-color:#fff176;
}

.b-color-teal{
  background-color:#009688;
}

.b-color-green{
  background-color:#a5d6a7;
}

.b-color-light-green{
  background-color:#8bc34a;
}

.b-color-lime{
  background-color:#cddc39;
}

.b-color-purple{
  background-color:#9c27b0;
}

.b-color-violet{
  background-color:#9775fa;
}

.b-color-pink{
  background-color:#f783ac;
}

.b-color-dark-gray{
  background-color:#757575;
}

.b-color-gray{
  background-color:#cccccc;
}

.b-color-light-gray{
  background-color:#f9f9f9;
}

.b-color-white{
  background-color:#fff;
}

.b-indigo{
  --widget-primary-color-rgb:63,81,181;
}

.b-blue{
  --widget-primary-color-rgb:100,181,246;
}

.b-cyan{
  --widget-primary-color-rgb:59,201,219;
}

.b-red{
  --widget-primary-color-rgb:239,154,154;
}

.b-deep-orange{
  --widget-primary-color-rgb:255,87,34;
}

.b-orange{
  --widget-primary-color-rgb:255,204,128;
}

.b-amber{
  --widget-primary-color-rgb:255,213,79;
}

.b-yellow{
  --widget-primary-color-rgb:255,241,118;
}

.b-teal{
  --widget-primary-color-rgb:0,150,136;
}

.b-green{
  --widget-primary-color-rgb:165,214,167;
}

.b-light-green{
  --widget-primary-color-rgb:139,195,74;
}

.b-lime{
  --widget-primary-color-rgb:205,220,57;
}

.b-purple{
  --widget-primary-color-rgb:156,39,176;
}

.b-violet{
  --widget-primary-color-rgb:151,117,250;
}

.b-pink{
  --widget-primary-color-rgb:247,131,172;
}

.b-dark-gray{
  --widget-primary-color-rgb:117,117,117;
}

.b-gray{
  --widget-primary-color-rgb:204,204,204;
}

.b-light-gray{
  --widget-primary-color-rgb:249,249,249;
}

.b-white{
  --widget-primary-color-rgb:255,255,255;
}

.b-icon-clock-live{
  background-color:rgba(189, 189, 189, 0.5);
  border-radius:50%;
  width:1em;
  height:1em;
  position:relative;
  animation-delay:0s;
}
.b-icon-clock-live:before, .b-icon-clock-live:after{
  position:absolute;
  display:block;
  font-size:1em !important;
  content:"";
  width:0.1em !important;
  left:0.44em;
  background:#fff;
  border-radius:0.5em;
}
.b-icon-clock-live:before{
  top:0.15em;
  height:0.4em !important;
  transform-origin:0.05em 0.35em;
  animation:rotate 6s infinite linear;
  animation-play-state:paused;
  animation-delay:inherit;
}
.b-icon-clock-live:after{
  top:0.22em;
  height:0.33em;
  transform-origin:0.05em 0.3em;
  animation:rotate 72s infinite linear;
  animation-play-state:paused;
  animation-delay:inherit;
}
@keyframes rotate{
  0%{
    transform:rotate(0deg);
  }
  100%{
    transform:rotate(360deg);
  }
}

/*# sourceMappingURL=core.classic-light.thin.css.map */