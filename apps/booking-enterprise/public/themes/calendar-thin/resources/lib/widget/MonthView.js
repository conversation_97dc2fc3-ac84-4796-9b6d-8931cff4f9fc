var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import CalendarMixin from "./mixin/CalendarMixin.js";
import DayCellCollecter from "./mixin/DayCellCollecter.js";
import DayCellRenderer from "./mixin/DayCellRenderer.js";
import CalendarPanel from "@bryntum/core-thin/lib/widget/CalendarPanel.js";
import DH from "@bryntum/core-thin/lib/helper/DateHelper.js";
import DomHelper from "@bryntum/core-thin/lib/helper/DomHelper.js";
import Scroller from "@bryntum/core-thin/lib/helper/util/Scroller.js";
import EventHelper from "@bryntum/core-thin/lib/helper/EventHelper.js";
import EventSorter from "../util/EventSorter.js";
const evRegexp = /^(\d+)ev$/, expandGestures = {
  shrinkwrap: 1,
  expand: 1
};
class MonthView extends CalendarPanel.mixin(CalendarMixin, DayCellCollecter, DayCellRenderer) {
  static get configurable() {
    return {
      eventSorter: EventSorter.interDaySorterFn,
      localizableProperties: ["title", "stepUnit"],
      title: "L{Month}",
      stepUnit: "L{monthUnit}",
      dragUnit: "day",
      localeClass: this,
      descriptionFormat: "MMMM YYYY",
      dayNumberCentered: null,
      /**
       * The height of event bars in this view. This can be a numeric value in pixels or a CSS unit measure such
       * as `'2em'`.
       * @config {Number|String}
       * @default
       */
      eventHeight: 20,
      minHeight: 485,
      /**
       * By default, weeks rows all flex to share the available height equally.
       *
       * To make them shrinkwrap their events to show all events in every row, configure this as `true`
       * @prp {Boolean}
       * @default false
       */
      autoRowHeight: {
        $config: "lazy",
        value: false
      },
      /**
       * By default, rows which have been modified by the {@link Calendar.feature.WeekExpander}
       * feature, __not by the {@link #config-autoRowHeight} setting__, to shinkwrap large content
       * are reset to flexed height on month change.
       *
       * To have rows persist their shrinkwrapped status across month changes, confgure this as `true`.
       *
       * If {@link #config-autoRowHeight} is set, then the new month always has auto heighted rows.
       * @prp {Boolean}
       * @default false
       */
      persistShrinkWrappedRows: null,
      /**
       * The maximum number of events to show in a cell when the row is shrinkwrapped.
       * Use this to keep rows to a sane size when using {@link #config-autoRowHeight},
       * or the {@link Calendar.feature.WeekExpander} feature.
       * @config {Number}
       * @default
       */
      maxEventsPerCell: 100,
      /**
       * By default, week rows flex to share available Panel height equally.
       *
       * This may be configured as a number, in which case it means pixels, or a CSS length.
       *
       * The non-standard unit `ev` may also be specified to mean "events". For example
       * `'3ev'` means rows will always be three events bars (plus the day header)
       * tall.
       *
       * This is a useful config when using {@link #config-autoRowHeight}, or using
       * {@link #config-overflowClickAction} when rows may be switched to shrinkwrapping
       * their event content and may shrink in height.
       *
       * Setting this config causes the month grid to become scrollable in the `Y` axis.
       * @config {Number|String}
       */
      minRowHeight: null,
      /**
       * How the view responds to clicking on a `+n more` button in an overflowing day cell.
       *
       * The default value, `'popup'`, means that a small dialog box showing the full complement
       * of events for that cell is shown aligned to the cell.
       *
       * When set to `'expand'`, then clicking the `+n more` button causes the encapsulating
       * row to expand to accommodate all events in that row with no overflow.
       *
       * Navigating to a new month resets the row to its default, flexed height.
       * @config {'popup'|'expand'} overflowClickAction
       * @default
       */
      overflowClickAction: "popup",
      // So that when clicking the prev and next buttons, the UI will change
      // even if a cell for the new date is present.
      alwaysRefreshOnMonthChange: true
    };
  }
  get eventContainerHeight() {
    const me = this, { classList } = me.weeksElement;
    if (me._eventContainerHeight == null && me.isVisible && !me.isConfiguring) {
      classList.add("b-measuring-container-height");
      me._eventContainerHeight = super.eventContainerHeight;
      classList.remove("b-measuring-container-height");
    }
    return me._eventContainerHeight;
  }
  onCalendarStoreChange() {
    super.onCalendarStoreChange(...arguments);
    this.syncShrinkwrappedRows();
  }
  onDateChange({ changes }) {
    if (changes.m) {
      const me = this, { autoRowHeight } = me;
      if (autoRowHeight) {
        me.weekElements.forEach(({ classList }) => classList.add("b-shrinkwrapped"));
      }
      if (me.persistShrinkWrappedRows || autoRowHeight) {
        me.syncShrinkwrappedRows();
      } else {
        me.shrinkwrappedRows.forEach((r) => me.flexWeekRow(r));
      }
    }
  }
  /**
   * Returns the resource associated with this month view when used inside a {@link Calendar.widget.ResourceView}
   * @readonly
   * @member {Scheduler.model.ResourceModel} resource
   */
  // Override from DayCellRenderer
  // Called automatically on the CellOverflow${overflowPopupTrigger} event because of callOnFunctions
  onCellOverflowGesture({ date }) {
    if (expandGestures[this.overflowClickAction.toLowerCase()]) {
      this.shrinkwrapWeekRow(date);
    } else {
      super.onCellOverflowGesture(...arguments);
    }
  }
  // addCellHeaderContent mutates the cellHeader DomConfig block.
  // And if we are to have a day name element, returns the DomConfig for it.
  // It's called from DayCellRenderer#getCellDomConfig
  addCellHeaderContent(cellHeader, cellData) {
    const dayName = {
      className: {
        "b-day-name": true
      }
    };
    cellHeader.children = [
      cellData.visibleColumnIndex || this.showWeekColumn ? null : {
        className: "b-week-num",
        text: cellData.week[1]
      },
      dayName
    ];
    return dayName;
  }
  get shrinkwrappedRows() {
    return this.weeksElement.querySelectorAll(".b-shrinkwrapped");
  }
  get shrinkwrapRowHeights() {
    const me = this, rowHeights = [], { cellMap } = me;
    me.month.eachWeek((week, [date]) => {
      let eventCount = 0;
      for (let i = 0; i < 7; i++, date.setDate(date.getDate() + 1)) {
        const cellData = cellMap.get(DH.makeKey(date));
        if (cellData) {
          eventCount = Math.max(eventCount, cellData.renderedEvents.length);
        }
      }
      rowHeights.push(eventCount);
    });
    return rowHeights.map((maxEventCount) => me.eventHeightInPixels * maxEventCount + me.eventSpacing * (maxEventCount + 1) + Math.ceil(me._eventContainerTop));
  }
  /**
   * Returns the number of complete event bars which will fit inside the referenced cell.
   *
   * It's only in MonthView when some rows are shrinkwrapped round their event content (meaning
   * either expanded or contracted away from the 1/6 height default) that there may be a customized
   * eventsPerCell for a certain date.
   * @internal
   */
  getEventsPerCell(date) {
    const me = this;
    if (me.hasShrinkwrappedRows) {
      const rowIndex = Math.floor(DH.diff(me.startDate, date, "d") / 7);
      if (me.weekElements[rowIndex].classList.contains("b-shrinkwrapped")) {
        return me.maxEventsPerCell;
      } else {
        const firstCell = me.weekElements[rowIndex].querySelector(me.visibleCellSelector), eventContainerHeight = firstCell.offsetHeight - me.eventContainerTop;
        return Math.floor((eventContainerHeight + me.eventSpacing) / (me.eventHeightInPixels + me.eventSpacing));
      }
    } else {
      return me.eventsPerCell;
    }
  }
  getMaxEventsForWeek(week) {
    const { row } = this.getWeekContext(week);
    return Math.max(...Array.from(row.querySelectorAll(this.visibleCellSelector)).map((c) => {
      const cellData = this.cellMap.get(c.dataset.date);
      return (cellData == null ? void 0 : cellData.renderedEvents.length) || 0;
    }));
  }
  getWeekContext(week) {
    let weekStart, visibleWeekStart, rowIndex;
    if (typeof week === "number") {
      rowIndex = week;
      visibleWeekStart = weekStart = DH.parseKey(this.weekElements[week].querySelector(this.visibleCellSelector).dataset.date);
    } else if (week.nodeType === 1) {
      visibleWeekStart = weekStart = DH.parseKey(week.closest(".b-calendar-row").querySelector("[data-date]").dataset.date);
      rowIndex = Math.floor(DH.diff(this.startDate, weekStart, "d") / 7);
    } else {
      const incr = (week.getDay(week) - DH.weekStartDay + 7) % 7;
      visibleWeekStart = weekStart = DH.add(DH.clearTime(week), -incr, "d");
      rowIndex = Math.floor(DH.diff(this.startDate, week, "d") / 7);
    }
    while (this.hiddenNonWorkingDays[visibleWeekStart.getDay()]) {
      visibleWeekStart.setDate(weekStart.getDate() + 1);
    }
    return {
      rowIndex,
      weekStart,
      visibleWeekStart,
      row: this.weekElements[rowIndex]
    };
  }
  /**
   * Causes the week row referenced by the parameter (Either a Date, or the **zero based** row index)
   * to size itself to exactly wrap the maximum number of events for any day of that week.
   *
   * If there are a *lot* of events, the row may grow in height. If few, or none, the row will shrink
   * in height. The day name header along the top will always be visible by default.
   *
   * The row has the CSS class `'b-shrinkwrapped'` added when it is in the shrinkwrapped state
   * to allow querying, and custom styling.
   *
   * See {@link #function-flexWeekRow} for the converse operation.
   *
   * @param {Date|Number} week Either the date of a day within the week, or the **zero based** week row
   * to shrinkwrap.
   */
  shrinkwrapWeekRow(week, isLastCall = true) {
    const me = this, {
      weekStart,
      row
    } = me.getWeekContext(week), {
      maxEventsPerCell,
      eventContainerTop
    } = me, wasShrinkwrapped = row.classList.contains("b-shrinkwrapped"), maxEventsForWeek = me.getMaxEventsForWeek(week), maxEventCount = maxEventsPerCell ? Math.min(maxEventsPerCell, maxEventsForWeek) : maxEventsForWeek, shrinkwrapHeight = me.eventHeightInPixels * maxEventCount + me.eventSpacing * (maxEventCount + 1) + Math.ceil(eventContainerTop), expanded = maxEventsForWeek > me.eventsPerCell, t = row.querySelector(".b-week-toggle-tool");
    if (isLastCall) {
      me.scrollable = {
        overflowY: "auto"
      };
    }
    row.classList.add("b-shrinkwrapped");
    if (row.classList.contains("b-empty-row")) {
      row.style.flex = "";
    } else {
      t && (t.dataset.btip = me.L("L{WeekExpander.collapseTip}"));
      row.classList.remove("b-has-overflow");
      row.classList.toggle("b-expanded", expanded);
      row.style.flex = expanded ? `0 0 ${shrinkwrapHeight}px` : `1 0 ${shrinkwrapHeight}px`;
    }
    me.hasShrinkwrappedRows = true;
    if (isLastCall) {
      me.refresh();
    }
    if (!wasShrinkwrapped) {
      me.trigger("weekShrinkwrap", {
        weekStart,
        element: row
      });
    }
    if (isLastCall) {
      if (!me.isAnimating) {
        me.isAnimating = true;
      }
      return new Promise((resolve) => {
        EventHelper.onTransitionEnd({
          element: row,
          property: "flex-basis",
          handler: "onAllWeekElementsExpanded",
          thisObj: me,
          args: [resolve]
        });
      });
    }
  }
  onAllWeekElementsExpanded(element, property, resolve) {
    this.isAnimating = false;
    this.syncCalendarWeekDaysWithScrollable();
    resolve();
  }
  /**
   * Causes the week row referenced by the parameter (Either a Date, or the **zero-based** row index)
   * to become flexed in height to share the available height of the Calendar equally with other
   * flexed rows.
   *
   * See {@link #function-shrinkwrapWeekRow} for the converse operation.
   *
   * @param {Date|Number} date Either the date of a day within the week, or the **zero based** week row
   * to flex.
   */
  async flexWeekRow(date, isLastCall = true, allRows = false) {
    const me = this, {
      weekStart,
      row
    } = me.getWeekContext(date), t = row.querySelector(".b-week-toggle-tool");
    if (row.classList.contains("b-shrinkwrapped")) {
      row.style.flex = "";
      row.classList.add("b-flexing");
      t && (t.dataset.btip = this.L("L{WeekExpander.expandTip}"));
      me.trigger("weekFlex", {
        weekStart,
        element: row
      });
      me._autoRowHeight = false;
      if (isLastCall && !me.isAnimating) {
        await me.executeAndAwaitAnimations(me.element, () => me.isAnimating = true);
      }
      if (isLastCall) {
        me.onAllWeekElementsFlexed(row, allRows);
      } else {
        me.onWeekElementFlexed(row);
      }
    }
  }
  onWeekElementFlexed(weekElement) {
    weekElement.classList.remove("b-shrinkwrapped", "b-flexing", "b-expanded");
  }
  onAllWeekElementsFlexed(weekElement, allRows) {
    const me = this;
    me.scrollable.overflowY = me.scrollable.hasOverflow("y");
    if (allRows) {
      me.shrinkwrappedRows.forEach((r) => r.classList.remove("b-shrinkwrapped", "b-flexing", "b-expanded"));
    } else {
      weekElement.classList.remove("b-shrinkwrapped", "b-flexing", "b-expanded");
    }
    me.isAnimating = false;
    me.hasShrinkwrappedRows = me.shrinkwrappedRows.length;
    me.refresh();
    me.syncCalendarWeekDaysWithScrollable();
  }
  // The header must allow a scrollbar width if the platform displays scrollbars
  syncCalendarWeekDaysWithScrollable() {
    var _a;
    this.weekdaysHeader.classList[((_a = this.scrollable) == null ? void 0 : _a.hasScrollbar()) ? "add" : "remove"]("b-show-yscroll-padding");
  }
  updateHideOtherMonthCells() {
    super.updateHideOtherMonthCells(...arguments);
    this.refresh();
  }
  updateEventHeight(height, oldHeight) {
    var _a;
    const me = this;
    super.updateEventHeight(height, oldHeight);
    if (!me.isConfiguring) {
      if ((_a = me.minRowHeight) == null ? void 0 : _a.match(evRegexp)) {
        me.updateMinRowHeight(me._minRowHeight);
      }
      me.syncShrinkwrappedRows();
      const padding = DomHelper.getEdgeSize(me.element, "padding", "tb");
      me.minHeight = // Month is usually 6 weeks
      ((me.eventHeightInPixels + 1) * 2 + me.eventSpacing * 3 + Math.ceil(me.eventContainerTop)) * 6 + // Add header height with borders
      me.weekdaysHeader.offsetHeight + 7 + // And view padding
      padding.height;
    }
  }
  updateMinRowHeight(minRowHeight) {
    var _a, _b;
    const me = this, eventCount = parseInt((_b = (_a = minRowHeight == null ? void 0 : minRowHeight.match) == null ? void 0 : _a.call(minRowHeight, evRegexp)) == null ? void 0 : _b[1]);
    if (!isNaN(eventCount)) {
      if (me.isConfiguring) {
        return me.ion({
          paint: "updateMinRowHeight",
          args: [minRowHeight],
          once: true
        });
      }
      minRowHeight = me.eventHeightInPixels * eventCount + me.eventSpacing * (eventCount + 1) + Math.ceil(me.eventContainerTop);
    }
    super.updateMinRowHeight(minRowHeight);
    if (me.isAnimating) {
      me.ion({
        animationEnd: "performResizeRefresh",
        thisObj: me,
        args: [me._eventsPerCell, me._eventContainerTop],
        once: true
      });
    } else {
      me.performResizeRefresh(me._eventsPerCell, me._eventContainerTop);
    }
  }
  changeMaxEventsPerCell(maxEventsPerCell) {
    return maxEventsPerCell == null ? this.constructor.configurable.maxEventsPerCell : maxEventsPerCell;
  }
  updateMaxEventsPerCell() {
    if (!this.isConfiguring) {
      this.syncShrinkwrappedRows();
    }
  }
  async updateAutoRowHeight(autoRowHeight, wasAutoRowHeight) {
    var _a, _b;
    if (this.initializingAutoRowHeight && autoRowHeight === Boolean(wasAutoRowHeight)) {
      return;
    }
    const me = this, { visibleWeekCount } = me, weekExpander = (_b = me.features || ((_a = me.calendar) == null ? void 0 : _a.features)) == null ? void 0 : _b.weekExpander;
    let finalPromise;
    if (weekExpander && autoRowHeight) {
      weekExpander.disabled = weekExpander.disabledByAutoRowHeight = true;
    }
    if (autoRowHeight) {
      for (let i = 0; i < visibleWeekCount; i++) {
        finalPromise = me.shrinkwrapWeekRow(i, i === visibleWeekCount - 1);
      }
    } else {
      for (let i = 0; i < visibleWeekCount; i++) {
        finalPromise = me.flexWeekRow(i, i === visibleWeekCount - 1, true);
      }
    }
    await finalPromise;
    if (weekExpander && autoRowHeight && weekExpander.disabledByAutoRowHeight) {
      weekExpander.disabled = weekExpander.disabledByAutoRowHeight = false;
    }
    if (!autoRowHeight) {
      me._eventContainerHeight = me._eventsPerCell = null;
      me.refresh();
    }
  }
  // When data changes or eventHeight changes, any shrinkwrapped rows need to be
  // kept in the correct shape;
  syncShrinkwrappedRows() {
    if (this.isVisible) {
      const { shrinkwrappedRows } = this;
      for (let i = 0, length = Math.min(shrinkwrappedRows.length, this.visibleWeekCount); i < length; i++) {
        this.shrinkwrapWeekRow(shrinkwrappedRows[i], i === length - 1);
      }
    } else {
      this.whenVisible(this.syncShrinkwrappedRows);
    }
  }
  changeScrollable(scrollable, oldScrollable) {
    scrollable = super.changeScrollable(scrollable, oldScrollable);
    if (scrollable == null ? void 0 : scrollable.overflowX) {
      this.weekdaysScrollable || (this.weekdaysScrollable = new Scroller({
        widget: this,
        element: this.weekdaysHeader,
        overflowX: "hidden-scroll"
      }));
      scrollable.addPartner(this.weekdaysScrollable, "x");
    }
    return scrollable;
  }
  collectEvents(options) {
    if (this.hideOtherMonthCells) {
      const { year, month } = this.month;
      options.startDate = new Date(year, month, 1);
      options.endDate = new Date(year, month + 1, 1);
    }
    options.getDateIndex = (date) => Number(date) === Number(this.firstVisibleDate || this.startDate) ? "date" : "startDate";
    return super.collectEvents(options);
  }
  getDayElement(date, strict) {
    if (typeof date !== "string") {
      date = DH.makeKey(date);
    }
    if (strict && parseInt(date.substr(5, 2)) !== this.month.month + 1) {
      return;
    }
    return super.getDayElement(date);
  }
  /**
   * Determines what is under the cursor of the specified event or what is described by the given element.
   * @param {Event|Element} domEvent The event or element
   * @returns {CalendarHit}
   */
  calendarHitTest(domEvent) {
    var _a, _b;
    const hit = super.calendarHitTest(domEvent), target = DomHelper.getEventElement(domEvent);
    if (hit) {
      if (hit.date.getMonth() !== this.month.month && (this.disableOtherMonthCells || this.hideOtherMonthCells)) {
        return;
      }
      const weekElement = target.closest(".b-calendar-week"), week = (_a = weekElement == null ? void 0 : weekElement.dataset.week) == null ? void 0 : _a.split(",").map(Number);
      if (week) {
        hit.cell = hit.cell || target.closest(".b-calendar-cell");
        hit.dayNumber = Number((_b = hit.cell) == null ? void 0 : _b.dataset.columnIndex);
        hit.week = week;
        hit.weekElement = weekElement;
        hit.weekNumber = week[1];
        hit.weekOffset = week[1] - Number(this.weeksElement.firstElementChild.dataset.week.split(",")[1]);
      }
    }
    return hit;
  }
  getDateFromPosition(clientX, clientY) {
    const me = this, weekEls = me.weeksElement.childNodes;
    for (let rect, i = 0; i < weekEls.length; ++i) {
      rect = weekEls[i].getBoundingClientRect();
      if (rect.top <= clientY && clientY < rect.bottom) {
        if (rect.left <= clientX && clientX < rect.right) {
          const dx = me.rtl ? rect.right - clientX : clientX - rect.x, column = Math.floor(dx * me.weekLength / rect.width);
          if (me.hideNonWorkingDays) {
            const cellDates = Array.from(weekEls[i].querySelectorAll(me.visibleCellSelector)).map((e) => me.getDateFromElement(e));
            return cellDates[column];
          } else {
            const date = me.getDateFromElement(weekEls[i].querySelector(me.visibleCellSelector));
            date.setDate(date.getDate() + column);
            return date;
          }
        }
      }
    }
    return null;
  }
  /**
   * Determines the week container element of the specified event or the given element.
   * @param {Event|Element} domEvent The event or element
   * @returns {Element}
   * @internal
   */
  getWeekElementFor(domEvent) {
    const target = DomHelper.getEventElement(domEvent);
    return (target == null ? void 0 : target.closest(".b-calendar-week")) || null;
  }
  updateEventStore(eventStore, was) {
    var _a;
    (_a = super.updateEventStore) == null ? void 0 : _a.call(this, eventStore, was);
    CalendarPanel.prototype.doRefresh.call(this);
  }
  updateOverflowClickAction() {
    this.refresh();
  }
  updateSixWeeks() {
    this._eventsPerCell = this._eventContainerTop = this._eventContainerHeight = null;
    super.updateSixWeeks(...arguments);
  }
  onMonthDateChange({ changes }) {
    if (changes.r && !this.sixWeeks) {
      this._eventsPerCell = this._eventContainerTop = this._eventContainerHeight = null;
    }
    super.onMonthDateChange(...arguments);
  }
  doRefresh() {
    var _a;
    const me = this, {
      weekElements
    } = me;
    if (!me.isConfiguring) {
      me.getConfig("autoRowHeight");
      (_a = me._cellMap) == null ? void 0 : _a.clear();
    }
    const result = super.doRefresh();
    for (let i = 0, { length } = weekElements; i < length; i++) {
      const row = weekElements[i], maxEvents = Array.prototype.reduce.call(row.querySelectorAll(me.visibleCellSelector), (result2, cell) => {
        const cellData = me.cellMap.get(cell.dataset.date);
        return result2 + ((cellData == null ? void 0 : cellData.renderedEvents.length) || 0);
      }, 0);
      row.classList.toggle("b-empty-row", !maxEvents);
    }
    me.syncCalendarWeekDaysWithScrollable();
    return result;
  }
  showEvent(eventRecord) {
    this.setDate(eventRecord.startDate);
  }
  changeDayNumberCentered(dayNumberCentered) {
    return Boolean(dayNumberCentered);
  }
  updateShowWeekColumn(showWeekColumn) {
    if (!showWeekColumn && !this.element.querySelector(".b-week-num")) {
      this.doRefresh();
    }
    super.updateShowWeekColumn(showWeekColumn);
  }
  updateHideNonWorkingDays(hideNonWorkingDays) {
    var _a;
    (_a = super.updateHideNonWorkingDays) == null ? void 0 : _a.call(this, hideNonWorkingDays);
    if (!this.isConfiguring) {
      this.refresh();
    }
  }
  updateDayNumberCentered(dayNumberCentered) {
    const me = this;
    if (!me._dayNumberCentered && !("nonCenteredDayNumShowWeekColumn" in me)) {
      me.nonCenteredDayNumShowWeekColumn = me.showWeekColumn;
    }
    me._dayNumberCentered = dayNumberCentered;
    me.element.classList[dayNumberCentered ? "add" : "remove"]("day-number-center");
    me.showWeekColumn = dayNumberCentered ? true : me.nonCenteredDayNumShowWeekColumn;
  }
  get dayNameSelector() {
    return this.showWeekColumn ? ".b-cal-cell-header" : super.dayNameSelector;
  }
  set dayNameSelector(dayNameSelector) {
    this._dayNameSelector = dayNameSelector;
  }
  isValidTargetDate(date) {
    var _a, _b;
    const newMonth = date.getMonth();
    if (newMonth !== this.month.month) {
      const minDate = this.minDate || ((_a = this.calendar) == null ? void 0 : _a.minDate), maxDate = this.maxDate || ((_b = this.calendar) == null ? void 0 : _b.maxDate);
      if (!isNaN(minDate) || !isNaN(maxDate)) {
        const { cellMonth } = this;
        cellMonth.date = date;
        if (!isNaN(minDate)) {
          if (cellMonth.startDate < minDate) {
            return false;
          }
        }
        if (!isNaN(maxDate)) {
          if (DH.add(cellMonth.endDate, 1, "d") > maxDate) {
            return false;
          }
        }
      }
    }
    return true;
  }
  set startDate(date) {
    this.date = date;
  }
  get startDate() {
    return super.startDate;
  }
  next() {
    this.date = DH.add(this.date || this.startDate, 1, "month");
  }
  previous() {
    this.date = DH.add(this.date || this.startDate, -1, "month");
  }
}
__publicField(MonthView, "$name", "MonthView");
__publicField(MonthView, "type", "monthview");
__publicField(MonthView, "delayable", {
  syncCalendarWeekDaysWithScrollable: {
    type: "raf",
    cancelOutstanding: true
  },
  // Need to handle cleanup after the row collapse animation
  // in the next AF so that all scrolling has been recalculated
  // and the overflowY can be set accurately
  onAllWeekElementsFlexed: {
    type: "raf",
    cancelOutstanding: true
  }
});
MonthView.initClass();
MonthView._$name = "MonthView";
export {
  MonthView as default
};
