var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Base from "@bryntum/core-thin/lib/Base.js";
import Config from "@bryntum/core-thin/lib/Config.js";
import Featureable from "@bryntum/core-thin/lib/mixin/Featureable.js";
import StringHelper from "@bryntum/core-thin/lib/helper/StringHelper.js";
import DomHelper from "@bryntum/core-thin/lib/helper/DomHelper.js";
import EventHelper from "@bryntum/core-thin/lib/helper/EventHelper.js";
import ObjectHelper from "@bryntum/core-thin/lib/helper/ObjectHelper.js";
import Responsive from "@bryntum/core-thin/lib/widget/mixin/Responsive.js";
import Rectangle from "@bryntum/core-thin/lib/helper/util/Rectangle.js";
import DH from "@bryntum/core-thin/lib/helper/DateHelper.js";
import CalendarFeature from "../../feature/CalendarFeature.js";
import CalendarStores from "../../mixin/CalendarStores.js";
import SchedulerInterface from "../../mixin/SchedulerInterface.js";
import EventRenderer from "./EventRenderer.js";
import AvatarRendering from "@bryntum/core-thin/lib/widget/util/AvatarRendering.js";
import Describable from "@bryntum/scheduler-thin/lib/view/mixin/Describable.js";
import "../../localization/En.js";
import EventSorter from "../../util/EventSorter.js";
const immediatePromise = Promise.resolve(), emptyObject = Object.freeze({}), { eventNameMap } = EventHelper, isFocusedCalendarMixin = (w) => w.isCalendarMixin && w.containsFocus;
var CalendarMixin_default = (Target) => {
  var _a;
  return _a = class extends (Target || Base).mixin(
    Describable,
    SchedulerInterface,
    Featureable,
    CalendarStores,
    EventRenderer,
    Responsive
  ) {
    static get configurable() {
      return {
        //region Hidden configs
        /**
         * @hideevents eventSelectionChange
         */
        /**
         * @hideconfigs htmlCls, autoUpdateRecord, record, textContent, content, html
         */
        /**
         * @hideproperties  content, html
         */
        /**
         * A String which describes how much the {@link #function-next} and {@link #function-previous}
         * methods will move this view forwards or backwards in time.
         *
         * This is used to create the tooltip hints for the `nextButton` and `prevButton` in the
         * {@link Calendar.view.Calendar#property-tbar Calendar's toolbar}. If this property
         * is not defined, the `nextButton` and `prevButton` will be disabled when this view is active.
         *
         * Note that {@link Calendar.widget.WeekView} and {@link Calendar.widget.YearView} use a localized
         * string property to yield this value. Other view types implement a `get stepUnit` getter because
         * their step increments are variable.
         * @member {String} stepUnit
         * @readonly
         */
        //endregion
        localizableProperties: [
          "autoCreate.newName",
          "timeFormat",
          "shortDateFormat",
          "shortDateTimeFormat"
        ],
        eventStore: null,
        resourceStore: null,
        /**
         * A function which compares events which some views use to decide upon rendering order.
         *
         * Default sorter function are provided from the {@link Calendar.util.EventSorter} class.
         *
         * A custom sort function may be configured.
         *
         * Note that the two objects to compare may either be {@link Scheduler.model.EventModel}s
         * or {@link EventBar}s which contain an `eventRecord` property which is the {@link Scheduler.model.EventModel}.
         * @config {Function}
         * @param {Scheduler.model.EventModel|EventBar} lhs The left side value to conpare
         * @param {Scheduler.model.EventModel|EventBar} rhs The right side value to conpare
         * @returns {Number}
         */
        eventSorter: EventSorter.defaultSorterFn,
        responsive: {},
        // brand as responsive so b-responsive-xxx CSS classes get added
        /**
         * Configure as `true` to hide {@link #config-nonWorkingDays}
         * @prp {Boolean}
         */
        hideNonWorkingDays: null,
        hideNonWorkingDaysCls: "b-hide-nonworking-days",
        /**
         * The week start day, 0 meaning Sunday, 6 meaning Saturday.
         * Defaults to {@link Core.helper.DateHelper#property-weekStartDay-static}.
         * @config {Number}
         */
        weekStartDay: DH.weekStartDay,
        /**
         * Non-working days as an object where keys are day indices, 0-6 (Sunday-Saturday), and the value is `true`.
         * Defaults to {@link Core.helper.DateHelper#property-nonWorkingDays-static}.
         * @config {Object<Number,Boolean>}
         */
        nonWorkingDays: {
          value: DH.nonWorkingDays,
          $config: {
            merge: "replace"
          }
        },
        /**
         * The class name to add to calendar cells which are non working days.
         * @config {String}
         * @private
         */
        nonWorkingDayCls: "b-nonworking-day",
        /**
         * The class name to add to calendar cells.
         * @member {String} dayCellCls
         */
        /**
         * The class name to add to calendar cells.
         * @config {String}
         * @private
         */
        dayCellCls: "b-calendar-cell",
        /**
         * The class name to add to calendar cells which are weekend days.
         * @config {String}
         * @private
         */
        weekendCls: "b-weekend",
        todayCls: "b-today",
        pastEventCls: "b-past-event",
        /**
         * The class name to add to events which have duration less than or equal to
         * {@link #config-shortEventDuration}.
         * @config {String}
         */
        shortEventCls: "b-short-event",
        /**
         * The duration at which below and equal to this value, an event's encapsulating element gets
         * the {@link #config-shortEventCls} added to it so that small event bars can have style rearrangements.
         *
         * In {@link Calendar.widget.DayView}s, short events have compressed layout so that the event name is
         * visible on the top line next to the start time.
         *
         * This may be a string in the format required by {@link Core.helper.DateHelper#function-parseDuration-static}.
         *
         * It may also be configured as a millisecond value.
         * @config {String|Number}
         * @default
         */
        shortEventDuration: "30 minutes",
        /**
         * The height of event bars if this view creates event bars.
         *
         * {@link Calendar.widget.MonthView MonthView}, {@link Calendar.widget.MonthView CalendarRow}
         * (the {@link Calendar.widget.DayView#config-allDayEvents all day row} in a
         * {@link Calendar.widget.WeekView WeekView}) and {@link Calendar.widget.AgendaView AgendaView}
         * use this config.
         *
         * In {@link Calendar.widget.DayView DayView} and {@link Calendar.widget.WeekView WeekView},
         * the event element's height is part of the widget's layout and signifies the event's duration,
         * so these use a default value of `'auto'`.
         * @config {Number|String}
         * @default
         */
        eventHeight: 25,
        eventSpacing: 2,
        intradayCls: "b-intraday",
        alldayCls: "b-allday",
        solidBarCls: "b-solid-bar",
        dayNameSelector: ".b-day-name",
        // MonthView widens this to be the whole cell header
        // if it's showing a separate week number column.
        // CalendarRow always widens this to the cell header.
        showTime: false,
        /**
         * A {@link Core.helper.DateHelper} format string used to format the time displayed in events
         *
         * @config {String}
         * @default 'LT'
         */
        timeFormat: {
          value: "LT",
          $config: {
            localeKey: "L{timeFormat}"
          }
        },
        /**
         * __Not applicable in a `DayView`__
         *
         * This specifies whether to show a circular "bullet" icon if there is no
         * {@link Scheduler.model.EventModel#field-iconCls} defined for an event.
         *
         * By default, events which are rendered as solid blocks of colour (such as all day events) do __not__
         * show the bullet icon.
         *
         * By default, events which do not show as a colour block show the bullet icon as a means of showing
         * the event's defined `eventColor`.
         *
         * This property may contain two properties which define whether to show the bullet icon for both
         * event types.
         *
         * If configured as `true`, all event bars will show a bullet icon if they do not have an
         * {@link Scheduler.model.EventModel#field-iconCls}
         * @config {Boolean|Object}
         * @param {Boolean} [bar] This is `false` by default. Set this to `true` in modes where a solid event
         * bar should show a bullet icon
         * @param {Boolean} [noBar] This is `true` by default. Events with no background colour, use this to
         * show the event's defined `eventColor`
         * @default
         */
        showBullet: {
          bar: false,
          noBar: true
        },
        eventColourStyleProperty: "color",
        // By default the event color becomes the colour
        // DayView uses backgroundColor
        handlePointerInteraction: true,
        /**
         * A {@link Core.helper.DateHelper} format string to use to create date output for
         * abbreviated view descriptions.
         * @prp {String}
         * @default 'll'
         */
        shortDateFormat: {
          value: "ll",
          $config: {
            localeKey: "L{shortDateFormat}"
          }
        },
        /**
         * A title text used by the Calendar's mode selector button
         * @config {String}
         */
        title: null,
        /**
         * A {@link Core.helper.DateHelper} format string to use to create date and time output for
         * abbreviated view descriptions.
         * @prp {String}
         * @default 'll LT'
         */
        shortDateTimeFormat: {
          value: "ll LT",
          $config: {
            localeKey: "L{shortDateTimeFormat}"
          }
        },
        /**
         * Configure as `true` to make the view read-only, by disabling any UIs for modifying data.
         *
         * __Note that checks MUST always also be applied at the server side.__
         * @config {Boolean}
         */
        readOnly: null,
        /**
         * If this config is set, then the `gesture` configured (which defaults to `dblclick`) creates a
         * new event at the mouse or touch event's time point.
         *
         * The exact time is rounded to the closest specified `step` value. This value is also used
         * for the {@link Core.widget.TimeField#property-step} value in the {@link Calendar.feature.EventEdit}'s
         * time input field.
         *
         * The duration of the created event is the specified `duration` value.
         *
         * If this is specified as `true`, the `gesture` becomes `dblclick`, and the other properties
         * are the default values listed below.
         *
         * If this is specified as a string, the string becomes the `gesture`, and the other properties
         * are the default values listed below.
         *
         * @prp {Object}
         * @property {String} [gesture='dblclick'] The DOM event name which initiates event creation at the event's position.
         * @property {Function|String} [newName='New Event'] The name of an event created using `autoCreate` or a function to call which yields the name.
         * When a function is specified, the following parameters are passed:
         * - <span class="name">view : <span class="type">CalendarMixin</span></span><div class="description">The view in which the event is being created.</div>
         * - <span class="name">startDate : <span class="type">String</span></span><div class="description">The start date of the event being created.</div>
         * - <span class="name">resourceRecord : <span class="type">ResourceModel</span></span><div class="description">The resource of the event being created.</div>
         * @property {String} [step='15 minutes'] The time unit by which to snap the start click point of auto created events.
         * __Only for views which have a granularity of less than one day such as `WeekView` and `DayView`__.
         *
         * For views which show whole days, the start defaults to 8am.
         *
         * This is a string in the format required by {@link Core.helper.DateHelper#function-parseDuration-static}.
         *
         * This value is also used for the {@link Core.widget.TimeField#property-step} value in the
         * {@link Calendar.feature.EventEdit}'s time input field.
         *
         * @property {'round'|'ceil'|'floor'} [snapType='round'] How to snap a precise gesture time to a boundary specified by the `step` property.
         * __Only for views which have a granularity of less than one day such as `WeekView` and `DayView`__.
         * @property {String} [duration='1 hour'] The default start hour for auto created events in the form accepted by {@link Core.helper.DateHelper#function-parseDuration-static}
         * @property {Number} [startHour=8] The default start hour for auto created events
         * in views where the time granularity is one day. In a `DayView` or `WeekView` where a mouse event position
         * will translate to a time of day, this is not used.
         *
         * This is the hour of the day to start the event at. It may be fractional.
         * @accepts {Object|String|Boolean}
         * @default
         */
        autoCreate: {
          gesture: "dblclick",
          newName: "L{Object.newEvent}",
          step: "15 minutes",
          snapType: "round",
          duration: "1 hour",
          startHour: 8
        },
        /**
         * The {@link Scheduler.model.EventModel#field-durationUnit} to use when drag-creating events
         * in this view.
         *
         * For {@link Calendar.widget.DayView}s, this is normally `'hour'`, for views with a granularity
         * level of one day, the default is `'day'`.
         * @config {String}
         */
        dragUnit: "hour",
        autoRefresh: {
          $config: {
            merge: "classList"
          },
          value: null
        },
        /**
         * Set to `false` if you don't want to allow events overlapping times for any one resource (defaults to true).
         * @config {Boolean}
         * @default
         * @private
         */
        allowOverlap: true,
        /**
         * The converse of {@link #property-syncCalendarDate}
         *
         * When used as a {@link Calendar.view.Calendar#config-modes mode} of a Calendar, this
         * view's date will automatically be kept synced with the Calendar's
         * {@link Calendar.view.Calendar#property-date}. So this view will move to encapsulate
         * the Calendar's date.
         *
         * Configure this as `false` to opt out of this.
         *
         * __Note that this places the onus on the application developer to control the
         * viewed date range in this widget.__
         * @prp {Boolean} syncViewDate
         * @default
         */
        syncViewDate: true,
        /**
         * The converse of {@link #property-syncViewDate}
         *
         * When a date, or an event is clicked in a calendar view, the Calendar's {@link Calendar.view.Calendar#property-date}
         * is set to the clicked date to indicate that the user has expressed interest in that date.
         *
         * This is for convenience, so that when switching modes, by default, the time span encapsulating
         * the last date of interest is always shown.
         *
         * To disable syncing the calendar's date with this view's date, configure this property as `false`.
         * @prp {Boolean} syncCalendarDate
         * @default
         */
        syncCalendarDate: true,
        // For views which are Panels, make them not include a tabIndex
        focusable: false,
        // Allow an AvatarRendering instance to be specified
        avatarRendering: {
          $config: "lazy",
          value: null
        },
        /**
         * Configure as `true` to show avatars of the assigned resources (calendars) at the
         * start of the event bar.
         *
         * Configure as `'last'` to show avatars of the assigned resources (calendars) at the
         * end of the event bar.
         *
         * Note that the avatars are `2.22em` diameter circles, and this may not be suitable
         * for rendering in short events inside a DayView.
         *
         * In a view which renders event bars, the {@link #config-eventHeight} should be
         * increased from the default to accommodate the extra information.
         *
         * Note that you must set {@link #config-resourceImagePath} in order that the system
         * knows where to access the resource's image file from.
         *
         * If no image is set, or the image is not found, the resource's initials are shown instead.
         *
         * By default it is inherited from the owning Calendar:
         * ```javascript
         * new Calendar({
         *     resourceImagePath   : 'images/resources/'
         *     modes : {
         *         month : {
         *             showResourceAvatars : true,
         *         },
         *         week : {
         *             // Images go at the end of the body with name first
         *             showResourceAvatars : 'last,
         *         }
         *     }
         * });
         * ```
         * @config {Boolean|String}
         * @default false
         */
        showResourceAvatars: null,
        /**
         * Path to load resource images from. Used by the {@link #config-showResourceAvatars} config
         * to create URLs using the resource's
         * {@link @bryntum/scheduler-thin/lib/model/ResourceModel#field-image} or
         * {@link @bryntum/scheduler-thin/lib/model/ResourceModel#field-imageUrl} fields:
         *
         * * `image` represents image name inside the specified `resourceImagePath`,
         * * `imageUrl` represents fully qualified image URL.
         *
         * **NOTE**: The path should end with a `/`:
         *
         * ```javascript
         * new Calendar({
         *     modeDefaults : {
         *         showResourceAvatars : true,
         *         resourceImagePath   : 'images/resources/'
         *     }
         * });
         * ```
         * @config {String}
         */
        resourceImagePath: null,
        /**
         * The minimum date to which the `startDate` of this view may be navigated.
         * @member {Date} minDate
         */
        /**
         * The minimum date to which the `startDate` of this view may be navigated.
         * @config {Date|String}
         */
        minDate: null,
        /**
         * The maximum date to which the `endDate` of this view may be navigated.
         * @member {Date} maxDate
         */
        /**
         * The maximum date to which the `endDate` of this view may be navigated.
         * @config {Date|String}
         */
        maxDate: null,
        /**
         * By default, when navigating through time, the next time
         * block will be animated in from the appropriate direction.
         *
         * Configure this as `false` to disable this.
         * @prp {Boolean} animateTimeShift
         * @default
         */
        animateTimeShift: true,
        // Private at this level, it's only processed for a ResourceView
        includeTimeRanges: null,
        testConfig: {
          animateTimeShift: false
        }
      };
    }
    static get delayable() {
      return {
        refreshSoon: {
          type: "raf",
          cancelOutstanding: true
        }
      };
    }
    static get featureable() {
      return {
        factory: CalendarFeature
      };
    }
    construct(config) {
      var _a2;
      const me = this;
      super.construct(config);
      if (!me.isYearView && me.element.tabIndex !== -1 && ((_a2 = me.contentElement) == null ? void 0 : _a2.tabIndex) !== -1) {
        (me.contentElement || me.element).tabIndex = -1;
      }
      me.getConfig("avatarRendering");
      EventHelper.on({
        element: me.element,
        keydown: "onCalendarKeyDown",
        thisObj: me
      });
      if (me.handlePointerInteraction) {
        EventHelper.on({
          element: me.element,
          mouseover: "onEventMouseOverOut",
          mouseout: "onEventMouseOverOut",
          mousedown: "onCalendarPointerInteraction",
          mouseup: "onCalendarPointerInteraction",
          // Block subsequent clicks before 300ms has elapsed
          click: {
            handler: "onCalendarPointerInteraction",
            block: 300
          },
          dblclick: "onCalendarPointerInteraction",
          contextmenu: "onCalendarPointerInteraction",
          thisObj: me
        });
        EventHelper.on({
          element: me.element,
          mouseenter: "onEventMouseEnterLeave",
          mouseleave: "onEventMouseEnterLeave",
          capture: true,
          thisObj: me
        });
      }
    }
    /**
     * For use by the {@link Calendar.feature.TimeRanges} feature. This yields the set of
     * {@link Calendar.model.TimeRangeModel}s and {@link Scheduler.model.ResourceTimeRangeModel}s
     * to be rendered in the passed date range.
     * @param {Date} startDate The start date of the range to be returned
     * @param {Date} endDate The end date of the range to be returned.
     * @returns {Calendar.model.TimeRangeModel[]}
     * @private
     */
    getTimeRanges(startDate, endDate) {
      const {
        resourceId,
        project
      } = this, includeTimeRanges = resourceId ? this.owner.includeTimeRanges : true, ranges = resourceId == null || includeTimeRanges ? project == null ? void 0 : project.getTimeRanges(startDate, endDate) : [];
      if (resourceId != null) {
        const resourceRanges = project == null ? void 0 : project.getResourceTimeRanges(startDate, endDate).filter((r) => r.resourceId == resourceId);
        resourceRanges.forEach((r) => {
          if (!r.color) {
            r.color = this.resource.eventColor;
          }
        });
        ranges.push(...resourceRanges);
      }
      return ranges;
    }
    updateIncludeTimeRanges() {
      if (!this.isConfiguring) {
        this.refresh();
      }
    }
    onConfigChange(info) {
      var _a2;
      if ((_a2 = this.autoRefresh) == null ? void 0 : _a2[info == null ? void 0 : info.name]) {
        this.refreshSoon();
      }
      super.onConfigChange(info);
    }
    changeAvatarRendering(avatarRendering) {
      return AvatarRendering.new({
        element: this.element
      }, avatarRendering);
    }
    updateShowResourceAvatars(showResourceAvatars) {
      if (showResourceAvatars) {
        this.avatarRendering || (this.avatarRendering = true);
      }
      this.refresh();
    }
    getResourceAvatar(resourceRecord) {
      return this.avatarRendering.getResourceAvatar({
        resourceRecord,
        imageUrl: resourceRecord.image === false ? null : resourceRecord.imageUrl || resourceRecord.image && this.resourceImagePath + resourceRecord.image,
        color: resourceRecord.eventColor,
        initials: resourceRecord.initials,
        dataset: {
          btip: StringHelper.encodeHtml(resourceRecord.name),
          resourceId: resourceRecord.id
        }
      });
    }
    updateEventHeight(eventHeight) {
      const { style } = this.element;
      this._eventHeightInPixels = null;
      style.setProperty("--event-height", DomHelper.setLength(eventHeight));
      style.setProperty("--arrow-width", "calc(var(--event-height) / 3)");
      style.setProperty("--arrow-margin", "calc(var(--event-height) / -3)");
      if (!this.isConfiguring) {
        this.refreshSoon();
      }
    }
    /**
     * Returns the pixel value of the {@link #config-eventHeight} in case it was configured as a
     * CSS measurement in other units.
     * @private
     */
    get eventHeightInPixels() {
      const me = this, { eventHeight } = me;
      let eventHeightInPixels = me._eventHeightInPixels;
      if (eventHeight !== "auto") {
        if (!eventHeightInPixels) {
          eventHeightInPixels = me._eventHeight;
          if (typeof eventHeightInPixels === "string") {
            eventHeightInPixels = DomHelper.measureSize(eventHeightInPixels, me.contentElement.querySelector(`.${me.eventBarContainerCls}`), false);
          }
          me._eventHeightInPixels = eventHeightInPixels;
        }
      }
      return eventHeightInPixels;
    }
    /**
     * This property yields the base selector to use to find visible cell elements in this view.
     *
     * It's based upon the {@link #property-dayCellCls}, but also takes into account the
     * {@link #config-hideNonWorkingDays} setting.
     *
     * If this is a MonthView, it also takes into account the
     * {@link Calendar.widget.MonthView#config-hideOtherMonthCells} setting.
     * @property {String}
     * @readonly
     */
    get visibleCellSelector() {
      const excludes = [];
      if (this.hideOtherMonthCells) {
        excludes.push(`.${this.otherMonthCls}`);
      }
      if (this.hideNonWorkingDays) {
        excludes.push(`.${this.nonWorkingDayCls}`);
      }
      return `.${this.dayCellCls}${excludes.length ? `:not(${excludes.join(",")})` : ""}`;
    }
    /**
     * This property yields this widget. This is to enable Calendar Features to be able to attach
     * to standalone Calendar widgets as their owning client, and to access a currently active view
     * in a standard way.
     * @property {Calendar.widget.mixin.CalendarMixin}
     * @typings {typeof CalendarMixin}
     * @readonly
     * @internal
     */
    get activeView() {
      return this;
    }
    /**
     * This property yields this widget. This is to enable Calendar Features to be able to attach
     * to standalone Calendar widgets as their owning client, and to access a currently active view
     * in a standard way.
     * @property {Calendar.widget.mixin.CalendarMixin}
     * @typings {typeof CalendarMixin}
     * @readonly
     * @internal
     */
    get activeSubView() {
      var _a2;
      const { items } = this, activeSubView = (_a2 = items.filter(isFocusedCalendarMixin)) == null ? void 0 : _a2[0];
      return activeSubView || this;
    }
    /**
     * Calendar mode that this view represents (eg. "day", "month" etc). Only accessible when used within a Calendar.
     * @member {String} modeName
     * @readonly
     */
    /**
     * This function allows a Calendar widget to act as a Feature host by exposing the same interface
     * as a {@link Calendar.view.Calendar}. It executes the passed function on this widget.
     * @internal
     * @param {Function} fn The function to call.
     * @param {Object[]} [args] The arguments to pass. Defaults to this view.
     * @param {Object} [thisObj] The `this` reference for the function. Defaults to this view.
     */
    eachView(fn, args, thisObj = null) {
      this.callback(fn, thisObj || this, args || [this]);
    }
    get focusElement() {
      const { calendar } = this;
      if (calendar) {
        const { navigator } = calendar;
        return navigator.activeItem || navigator.previousActiveItem || this.element.querySelector(navigator.itemSelector) || super.focusElement;
      }
    }
    captureFocusItem(activeElement) {
      const activeEvent = this.getEventRecord(activeElement), base = super.captureFocusItem(activeElement);
      return (scrollIntoView = true) => {
        const newEl = activeEvent && this.getEventElement(activeEvent);
        if (newEl) {
          scrollIntoView ? newEl.focus() : newEl.focus({ preventScroll: true });
        } else {
          base == null ? void 0 : base(scrollIntoView);
        }
      };
    }
    /**
     * Refreshes the UI after a change to the EventStore, or to a configuration that requires
     * the UI to change.
     *
     * Only updates the UI if this widget is visible. If it is not visible, the refresh is
     * deferred until it next becomes visible.
     */
    refresh() {
      this.refreshSoon.cancel();
      this.month && this.element.style.setProperty("--week-length", this.month.weekLength);
      this.whenVisible("refreshNow");
    }
    refreshNow() {
      const refocus = this.captureFocus();
      this.doRefresh();
      refocus();
    }
    /**
     * Executes the passed callback after the next refresh, but waits only for a maximum number of
     * milliseconds before optionally performing a refresh and executing the callback.
     *
     * When awaited, the function resolves after the refresh or timeout and after any callback have been executed
     * and the Promise yields `true` if a refresh has been performed.
     *
     * @param {String|Function} [callback] A function or the name of a function in the ownership hierarchy
     * to run after the next refresh operation. May be omitted, and `options` passed as the sole parameter.
     * @param {Object} [options] How long to wait and what to do on timeout.
     * @param {Number} [options.delay=100] The number of milliseconds to wait for a refresh.
     * @param {Boolean} [options.forceRefresh=false] If the refresh does not happen within the timer, call refresh.
     * @async
     * @internal
     */
    afterRefresh(callback, options = { delay: 100, forceRefresh: false }) {
      if (typeof callback === "object") {
        options = callback;
        callback = null;
      } else if (callback) {
        callback = this.resolveCallback(callback, this);
        callback = callback.handler.bind(callback.thisObj);
      }
      if (typeof options === "number") {
        options = { delay: options };
      }
      return new Promise((resolve) => {
        this.ion({
          // expires uses the event name or `name` as the timeout id
          // so in order to be unique, we need to give this a name
          name: this.constructor.generateId("refresh-expiry-timer-"),
          refresh: () => {
            callback == null ? void 0 : callback();
            resolve(true);
          },
          once: true,
          expires: {
            delay: options.delay || 100,
            alt: () => {
              options.forceRefresh && this.refresh();
              callback == null ? void 0 : callback();
              resolve(options.forceRefresh);
            }
          }
        });
      });
    }
    get displayName() {
      return StringHelper.capitalize(this._displayName || this.title || this.type);
    }
    get hiddenNonWorkingDays() {
      return this.hideNonWorkingDays ? this.nonWorkingDays || this.month.nonWorkingDays : emptyObject;
    }
    changeAutoCreate(autoCreate) {
      const defaults = _a.$meta.config.autoCreate;
      if (autoCreate === true) {
        return defaults;
      }
      if (typeof autoCreate === "string") {
        autoCreate = {
          gesture: autoCreate
        };
      }
      return Config.merge(autoCreate, defaults);
    }
    updateDateSeparator() {
      this.refreshCalendarDescription();
    }
    updateDescriptionFormat() {
      this.refreshCalendarDescription();
    }
    refreshCalendarDescription() {
      const { calendar } = this;
      if ((calendar == null ? void 0 : calendar.isPainted) && calendar.activeView === this) {
        calendar.updateViewDescription();
      }
    }
    changeShortEventDuration(shortEventDuration) {
      return isNaN(shortEventDuration) ? DH.as("ms", shortEventDuration) : Number(shortEventDuration);
    }
    updateShortEventDuration() {
      if (!this.isConfiguring) {
        this.refresh();
      }
    }
    updateLocalization() {
      if (!("weekStartDay" in this.initialConfig)) {
        this.weekStartDay = DH.weekStartDay;
      }
      if (!("nonWorkingDays" in this.initialConfig)) {
        this.nonWorkingDays = DH.nonWorkingDays;
      }
      super.updateLocalization();
      this.refreshCalendarDescription();
    }
    updateAutoCreate(autoCreate) {
      this.updateLocalization();
    }
    updateWeekStartDay(weekStartDay) {
      var _a2;
      const { refreshCount, month } = this;
      (_a2 = super.updateWeekStartDay) == null ? void 0 : _a2.call(this, weekStartDay);
      if (month) {
        month.weekStartDay = weekStartDay;
      }
      if (this.isPainted && this.refreshCount === refreshCount) {
        this.refresh();
      }
    }
    changeNonWorkingDays(nonWorkingDays) {
      const me = this, result = new Proxy(ObjectHelper.assign({}, nonWorkingDays), {
        set(target) {
          const result2 = Reflect.set(...arguments);
          me.updateNonWorkingDays(target);
          return result2;
        },
        deleteProperty(target) {
          const result2 = Reflect.deleteProperty(...arguments);
          me.updateNonWorkingDays(target);
          return result2;
        }
      });
      return result;
    }
    updateNonWorkingDays(nonWorkingDays) {
      var _a2;
      const { refreshCount, month } = this;
      (_a2 = super.updateNonWorkingDays) == null ? void 0 : _a2.call(this, nonWorkingDays);
      if (month) {
        month.nonWorkingDays = nonWorkingDays;
      }
      if (this.isPainted && this.refreshCount === refreshCount) {
        this.refresh();
      }
    }
    dayOfDate(date) {
      return DH.clearTime(date);
    }
    ingestDate(date) {
      date = typeof date === "string" ? DH.parse(date) : new Date(date);
      if (isNaN(date)) {
        throw new Error("Calendar widget date ingestion must be passed a Date, or a YYYY-MM-DD date string");
      }
      return this.dayOfDate(date);
    }
    changeDate(date, oldDate) {
      date = this.ingestDate(date);
      if (!this.isInIsValidTargetDate && !this.isValidTargetDate(date)) {
        return;
      }
      if (arguments.length === 1) {
        return date;
      }
      if (!oldDate || date - oldDate) {
        if (this.trigger("beforeDateChange", { date, oldDate }) !== false) {
          return date;
        }
      }
    }
    isValidTargetDate(date, end) {
      var _a2, _b;
      const me = this, minDate = me.minDate || ((_a2 = me.calendar) == null ? void 0 : _a2.minDate), maxDate = me.maxDate || ((_b = me.calendar) == null ? void 0 : _b.maxDate);
      if (!isNaN(minDate) || !isNaN(maxDate)) {
        this.isInIsValidTargetDate = true;
        const newDate = end ? me[`change${StringHelper.capitalize(end)}Date`](date, null) : date;
        me.isInIsValidTargetDate = false;
        if (!isNaN(minDate) && newDate < minDate) {
          return false;
        }
        if (!isNaN(maxDate)) {
          return !(end === "end" ? newDate > maxDate : newDate >= maxDate);
        }
      }
      return true;
    }
    changeStartDate(startDate) {
      if (startDate) {
        startDate = this.ingestDate(startDate);
        if (this.isInIsValidTargetDate || this.isValidTargetDate(startDate, "start")) {
          return startDate;
        }
      }
    }
    changeEndDate(endDate) {
      if (endDate) {
        endDate = this.ingestDate(endDate);
        if (this.isInIsValidTargetDate || this.isValidTargetDate(endDate, "end")) {
          return endDate;
        }
      }
    }
    /**
     * Brings an event or a time into view. Optionally visually highlights the target.
     *
     * __This may change the date range encompassed by this view to bring the date or event into its
     * ownership__.
     *
     * Scrolling may or may not be required, depending on the type and size constraints of the view.
     * @param {Scheduler.model.EventModel|Date} target The event or Date to scroll to.
     * @param {Object} [options] How to scroll.
     * @param {'start'|'end'|'center'|'nearest'} [options.block] How far to scroll the target.
     * @param {Number} [options.edgeOffset] edgeOffset A margin around the target to bring into view.
     * @param {Object|Boolean|Number} [options.animate] Set to `true` to animate the scroll by 300ms,
     * or the number of milliseconds to animate over, or an animation config object.
     * @param {Number} [options.animate.duration] The number of milliseconds to animate over.
     * @param {String} [options.animate.easing] The name of an easing function.
     * @param {Boolean|Function} [options.highlight] Set to `true` to highlight the resulting element
     * when it is in view. May be a function which is called passing the resulting element
     * to provide customized highlighting.
     * @param {Boolean} [options.focus] Set to `true` to focus the element when it is in view.
     * @param {Boolean} [options.x] Pass as `false` to disable scrolling in the `X` axis.
     * @param {Boolean} [options.y] Pass as `false` to disable scrolling in the `Y` axis.
     * @returns {Promise} A promise which is resolved when the target has been scrolled into view.
     */
    async scrollTo(target, options = { animate: true }) {
      const me = this, { scrollable } = me;
      let promise = immediatePromise;
      if (me.scrollPromise) {
        await me.scrollPromise;
      }
      if (target.isEvent) {
        const eventRecord = target;
        if (!DH.intersectSpans(me.startDate, me.endDate, target.startDate, target.endDate)) {
          me.date = target.startDate;
        }
        target = me.getEventElement(target);
        if (!target) {
          me.refresh();
          target = me.getEventElement(eventRecord);
        }
      } else {
        target = me.changeDate(target);
        if (!DH.betweenLesser(target, me.startDate, me.endDate) || !me.getDayElement(target, true)) {
          me.date = target;
        }
        target = me.getDayElement(target);
      }
      if (target == null) {
        return;
      }
      if (scrollable) {
        promise = scrollable.scrollIntoView(target, options);
      } else if (options.highlight) {
        if (typeof options.highlight === "boolean") {
          DomHelper.highlight(Rectangle.from(target));
        } else {
          me.callback(options.highlight, me, [target, me]);
        }
      }
      return (me.scrollPromise = promise).then(() => me.scrollPromise = null);
    }
    async checkAutoCreateGesture(domEvent, date, resourceRecord) {
      var _a2;
      const me = this, { autoCreate } = me;
      if (date && me.isVisible && !me.readOnly && domEvent.type === ((_a2 = autoCreate == null ? void 0 : autoCreate.gesture) == null ? void 0 : _a2.toLowerCase())) {
        const dateStart = DH.startOf(date, void 0, void 0, me.weekStartDay), startHourMS = isNaN(autoCreate.startHour) ? DH.getTimeOfDay(DH.parse(autoCreate.startHour, "HH:mm:ss")) : autoCreate.startHour * 1e3 * 60 * 60;
        if (me.trigger("beforeAutoCreate", {
          view: me,
          domEvent,
          date: me.isDayView ? DH[autoCreate.snapType](date, autoCreate.step) : DH.add(dateStart, startHourMS),
          resourceRecord
        }) !== false) {
          return me.createEvent(date, resourceRecord, true);
        }
      }
    }
    /**
     * Creates an event on the specified date which conforms to this view's {@link #config-autoCreate}
     * setting.
     *
     * This method may be called programmatically by application code if the `autoCreate` setting
     * is `false`, in which case the default values for `autoCreate` will be used.
     *
     * If the {@link Calendar.feature.EventEdit EventEdit} feature is active, the new event
     * will be displayed in the event editor.
     * @param {Date} date The date to add the event at. If there's no time component, the
     * {@link #config-autoCreate}'s `startHour` will be used.
     */
    createEvent(date, resourceRecord, isAutoCreate) {
      var _a2;
      const handler = this.calendar || ((_a2 = this.owner) == null ? void 0 : _a2.calendar) || this;
      handler.doCreateEvent(date, resourceRecord, this, isAutoCreate);
    }
    async doCreateEvent(date, resourceRecord, editingView = this, isAutoCreate) {
      var _a2;
      resourceRecord = resourceRecord != null ? resourceRecord : this.defaultCalendar;
      const me = this, { isDayView } = me.viewType || me, calendar = me.calendar || ((_a2 = me.owner) == null ? void 0 : _a2.calendar), eventStore = (calendar == null ? void 0 : calendar.eventStore) || me.eventStore, autoCreate = editingView.autoCreate || editingView.changeAutoCreate(true), { modelClass } = eventStore, { newName } = autoCreate;
      if (date == null || !isDayView && isAutoCreate) {
        const { dayStartHour, dayEndHour } = me.activeView, startHour = Math.min(Math.max(autoCreate.startHour, dayStartHour), dayEndHour), pickedValue = startHour === dayEndHour ? dayStartHour : startHour, startHourMS = isNaN(pickedValue) ? isNaN(autoCreate.startHour) ? DH.getTimeOfDay(DH.parse(autoCreate.startHour, "HH:mm:ss")) : autoCreate.startHour * 36e5 : pickedValue * 36e5;
        date = DH.add(date || me.date, startHourMS);
      }
      const d = DH.parseDuration(autoCreate.duration), dateStart = new Date(date.getTime() + (me.dayStartShift || 0)), startDate = isDayView ? DH[autoCreate.snapType](date, autoCreate.step) : dateStart, duration = isDayView && isAutoCreate ? {
        magnitude: me.dayTime.startShift ? Math.min(d.magnitude, DH.diff(startDate, DH.add(me.dayTime.startOfDay(date), me.dayTime.duration(), "ms"), d.unit)) : Math.min(d.magnitude, DH.diff(startDate, DH.add(DH.startOf(date), me.dayTime.timeEnd), d.unit)),
        unit: d.unit
      } : d, endDate = DH.add(startDate, duration.magnitude, duration.unit), name = me.resolveCallback(newName, me, false) ? me.callback(newName, me, [me, startDate, resourceRecord]) : newName, recordData = {
        [modelClass.getFieldDataSource("name")]: name,
        [modelClass.getFieldDataSource("startDate")]: startDate,
        [modelClass.getFieldDataSource("endDate")]: endDate,
        [modelClass.getFieldDataSource("duration")]: duration.magnitude,
        [modelClass.getFieldDataSource("durationUnit")]: duration.unit,
        // If the view's settings resulted in a midnight to midnight event, flag it as allDay
        allDay: DH.diff(startDate, endDate, "day") === 1
      }, dateContainmentFn = editingView.isEventList && !editingView.range ? "betweenLesserEqual" : "betweenLesser", newRecord = eventStore.createRecord(recordData);
      if ((calendar == null ? void 0 : calendar.features.eventEdit) && !calendar.features.eventEdit.disabled) {
        newRecord.isCreating = true;
      }
      if (resourceRecord) {
        eventStore.assignmentStore.assignEventToResource(newRecord, resourceRecord);
      }
      await eventStore.addAsync(newRecord);
      if (DH[dateContainmentFn](startDate, editingView.startDate, editingView.endDate)) {
        if (editingView.getEventElement(newRecord, startDate)) {
          editingView.editAutoCreatedEvent(newRecord);
        } else {
          editingView.ion({
            refresh({ source }) {
              var _a3;
              (_a3 = source.editAutoCreatedEvent) == null ? void 0 : _a3.call(source, newRecord);
            },
            once: true,
            prio: -1e4,
            buffer: 100,
            expires: 500
          });
        }
      }
    }
    editAutoCreatedEvent(eventRecord) {
      this.trigger("eventAutoCreated", {
        eventRecord
      });
    }
    get duration() {
      return this.endDate ? this.calculateDuration(this.startDate, this.endDate) : 1;
    }
    calculateDuration(startDate, endDate) {
      return DH.diff(startDate, endDate, "day");
    }
    /**
     * Moves this view forwards in time by its configured (or intrinsic if it's a
     * {@link Calendar.widget.WeekView} or a {@link Calendar.widget.YearView}) duration.
     */
    next() {
      this.date = DH.add(this.date, this.duration, "day");
    }
    /**
     * Moves this view backwards in time by its configured (or intrinsic if it's a
     * {@link Calendar.widget.WeekView} or a {@link Calendar.widget.YearView}) duration.
     */
    previous() {
      this.date = DH.add(this.date, -this.duration, "day");
    }
    get eventContentElement() {
      return this.contentElement;
    }
    /**
     * The number of __visible__&nbsp;events that this view currently displays in its date range.
     *
     * Filtered out events are not incuded in the count.
     *
     * Events which are in {@link #config-hideNonWorkingDays hidden non-working days} are
     * __not__&nbsp;included in the count.
     *
     * This value may be used when creating a view description string.
     * @member {Number}
     * @readonly
     */
    get eventCount() {
      var _a2;
      const me = this;
      if (me.views) {
        return me.views.reduce((r, c) => r += c.eventCount || 0, 0);
      }
      if (me.cellMap) {
        return [...me.cellMap.values(), ...((_a2 = me.allDayEvents) == null ? void 0 : _a2.cellMap.values()) || []].reduce((r, c) => {
          if (!me.hiddenNonWorkingDays[c.day]) {
            r += c.events.length;
          }
          return r;
        }, 0);
      }
      return me.eventStore.getEvents({
        startDate: me.startDate,
        endDate: me.endDate
      }).length;
    }
    /**
     * The first *visible* event-bearing element in this view. So if the first day defined in the
     * range is a Sunday, and {@link #config-hideNonWorkingDays} is set, then the first visible
     * cell will be for the Monday.
     * @property {HTMLElement}
     */
    get firstVisibleCell() {
      return this.eventContentElement.querySelector(this.visibleCellSelector);
    }
    /**
     * The last *visible* event-bearing element in this view. So if the last day defined in the
     * range is a Sunday, and {@link #config-hideNonWorkingDays} is set, then the last visible
     * cell will be for the Friday.
     * @property {HTMLElement}
     */
    get lastVisibleCell() {
      const visibleCells = this.contentElement.querySelectorAll(this.visibleCellSelector);
      return visibleCells[visibleCells.length - 1];
    }
    /**
     * The date of the first *visible* event-bearing element in this view. So if the first day defined
     * in the range is a Sunday, and {@link #config-hideNonWorkingDays} is set, then the first visible
     * date will be the date of the Monday.
     * @property {Date}
     */
    get firstVisibleDate() {
      const me = this, date = new Date(me.startDate), { month } = me.month;
      while (me.hideOtherMonthCells && date.getMonth() !== month || me.hiddenNonWorkingDays[date.getDay()]) {
        date.setDate(date.getDate() + 1);
      }
      return date;
    }
    /**
     * The date of the last *visible* event-bearing element in this view. So if the last day defined
     * in the range is a Sunday, and {@link #config-hideNonWorkingDays} is set, then the last visible
     * date will be the date of the Friday.
     * @property {Date}
     */
    get lastVisibleDate() {
      const me = this, date = DH.add(me.endDate, -1, "d"), { month } = me.month;
      while (me.hideOtherMonthCells && date.getMonth() !== month || me.hiddenNonWorkingDays[date.getDay()]) {
        date.setDate(date.getDate() - 1);
      }
      return date;
    }
    updateHideNonWorkingDays(hideNonWorkingDays) {
      var _a2, _b;
      const me = this, {
        month,
        calendar
      } = me;
      (_a2 = me.contentElement) == null ? void 0 : _a2.classList[hideNonWorkingDays ? "add" : "remove"](me.hideNonWorkingDaysCls);
      if (month == null) {
        return;
      }
      let activeColumnIndex, date, activeDay;
      if (!me.isConfiguring && hideNonWorkingDays) {
        date = me.date;
        activeDay = date == null ? void 0 : date.getDay();
        if (date && me.nonWorkingDays[activeDay] && me.getDayElement(date)) {
          activeColumnIndex = month.visibleDayColumnIndex[activeDay];
        }
      }
      month.hideNonWorkingDays = hideNonWorkingDays;
      (_b = super.updateHideNonWorkingDays) == null ? void 0 : _b.call(this, hideNonWorkingDays);
      if (typeof activeColumnIndex === "number") {
        const weekStart = month.getWeekStart(month.getWeekNumber(date)), newActiveColumnIndex = Math.min(activeColumnIndex, month.visibleColumnCount - 1);
        for (let i = -1; ; weekStart.setDate(weekStart.getDate() + 1)) {
          if (typeof month.visibleDayColumnIndex[weekStart.getDay()] === "number") {
            if (++i === newActiveColumnIndex) {
              break;
            }
          }
        }
        me.date = weekStart;
        calendar && (calendar.date = date);
      }
    }
    onCalendarStoreChange({ source, action }) {
      var _a2;
      const me = this;
      if (action === "dataset" && !source.isChained) {
        return;
      }
      if (me.project.isInitialCommitPerformed && !me.project.isWritingData && me.project.isEngineReady()) {
        (_a2 = me._cellMap) == null ? void 0 : _a2.clear();
        me.refreshSoon();
      }
    }
    /**
     * Schedules a refresh of the UI for the next animation frame. This is a useful method to call when
     * making multiple data changes, so that each change merely *schedules* a refresh for the next AF and
     * DOM churn is kept to a minimum.
     *
     * Calling {@link #function-refresh} directly cancels any scheduled refresh operation and updates
     * the UI immediately
     */
    refreshSoon() {
      this.refresh();
    }
    /**
     * Called when new event is created.
     * Сan be overridden to supply default record values etc.
     * @param {Scheduler.model.EventModel} eventRecord Newly created event
     */
    onEventCreated(eventRecord) {
    }
    /**
     * Returns the event record for a DOM element or DOM event.
     * @param {HTMLElement|Event} elementOrEvent The DOM node to lookup, or a DOM event whose target to lookup.
     * @returns {Scheduler.model.EventModel} The event record
     */
    getEventRecord(elementOrEvent) {
      var _a2;
      let element = elementOrEvent instanceof Event ? elementOrEvent.target : elementOrEvent;
      element = (_a2 = element == null ? void 0 : element.closest) == null ? void 0 : _a2.call(element, "[data-event-id]");
      return element && this.eventStore.getById(element.dataset.eventId);
    }
    /**
     * Returns the resource record for a DOM element or DOM event if the element is inside a view
     * which displays events for one resource such as a {@link Calendar.widget.ResourceView}
     * or a {@link Calendar.widget.DayResourceView}.
     * @param {HTMLElement|Event} elementOrEvent The DOM node to lookup, or a DOM event whose target to lookup.
     * @returns {Scheduler.model.ResourceModel} The resource record
     */
    getResourceRecord(elementOrEvent) {
      let element = elementOrEvent instanceof Event ? elementOrEvent.target : elementOrEvent;
      element = (element == null ? void 0 : element.closest("[data-resource-id]")) || null;
      return element && this.resourceStore.getById(element.dataset.resourceId);
    }
    /**
     * Returns the event record for a DOM element or DOM event.
     * @param {HTMLElement|Event} elementOrEvent The DOM node to lookup, or a DOM event whose target to lookup.
     * @returns {Scheduler.model.EventModel} The event record
     */
    resolveEventRecord(elementOrEvent) {
      return this.getEventRecord(elementOrEvent);
    }
    getDateFromElement(element, keyParser = null, raw = false) {
      var _a2;
      let dateElement = element == null ? void 0 : element.closest("[data-date],[data-header-date],[data-month-date]");
      if (dateElement) {
        const rawDate = dateElement.dataset.date || dateElement.dataset.headerDate || dateElement.dataset.monthDate;
        return raw ? rawDate : (keyParser || (this.isDayView ? this.dayTime : DH)).parseKey(rawDate);
      }
      dateElement = element == null ? void 0 : element.closest("[data-week]");
      if (dateElement) {
        return (_a2 = this.month) == null ? void 0 : _a2.getWeekStart(dateElement.dataset.week.split(",").map(Number));
      }
    }
    getDateFromDomEvent(domEvent) {
      return this.getDateFromElement(DomHelper.getEventElement(domEvent));
    }
    getDateFromPosition() {
      return null;
    }
    dateKey(date) {
      return DH.makeKey(date);
    }
    /**
     * Returns the cell associated with the passed date.
     *
     * In certain views, the strict definition if whether the view owns the date may be optionally enforced.
     *
     * For example, in a YearView or MonthView, dates outside the configured year or month may be displayed.
     *
     * To exclude these, pass the `strict` parameter as `true`
     * @param {Date|String} date The date to find the element for or a key in the format `YYYY-MM-DD`
     * @param {Boolean} strict Only return the element if this view *owns* the date. (MonthView and YearView)
     */
    getDayElement(date, strict) {
      if (typeof date !== "string") {
        date = this.dateKey(date);
      }
      return this.eventContentElement.querySelector(`[data-date="${date}"]`);
    }
    // Used by DayView and CalendarRow to see which day cell the X position relates to.
    getDayElementFromX(x) {
      const dayCells = this.eventContentElement.querySelectorAll("[data-date]");
      for (let rect, el, i = 0, { length } = dayCells; i < length; i++) {
        rect = (el = dayCells[i]).getBoundingClientRect();
        if (x >= rect.x && x <= rect.x + rect.width) {
          return el;
        }
      }
      return dayCells[0];
    }
    /**
     * Returns the outermost element which represents the first block of the passed event in the view. *If the
     * event is represented within the view*.
     *
     * *Note* if the event covers multiple weeks, this will only return the first element.
     *
     * To return all elements use {@link #function-getEventElements}.
     *
     * To return an event element at a particular date, pass the date as the second parameter.
     * @param {Scheduler.model.EventModel|String|Number} eventRecord The event, or event ID to find the element for.
     * @param {Date} [date] Optionally, the event element at the specified date.
     * @returns {HTMLElement} The first element which corresponds to the event. Note that *some* views,
     * such as {@link Calendar.widget.MonthView MonthView} and {@link Calendar.widget.CalendarRow CalendarRow}
     * may render multiple elements for long events.
     */
    getEventElement(eventRecord, date = Math.max(eventRecord.startDate, this.firstVisibleDate || this.startDate)) {
      var _a2;
      const me = this, activeEventElement = (_a2 = me.calendar) == null ? void 0 : _a2.navigator.activeItem, activeDate = me.getDateFromElement(activeEventElement), eventId = me.eventStore.modelClass.asId(eventRecord);
      if (document.contains(activeEventElement) && (activeEventElement == null ? void 0 : activeEventElement.dataset.eventId) === String(eventId) && (activeDate && !(date - activeDate))) {
        return activeEventElement;
      }
      if (date) {
        const dayCell = me.getDayElement(date);
        if (dayCell) {
          return DomHelper.down(dayCell, `[data-event-id="${eventId}"]`);
        }
      }
      return me.getEventElements(eventRecord)[0];
    }
    /**
     * Returns all outermost elements which represents the passed event in the view. *If the
     * event is represented within the view*
     * @param {Scheduler.model.EventModel|String|Number} eventRecord The event, or event ID to find the elements for.
     * @returns {HTMLElement[]} The elements which corresponds to the event. Note that *some* views,
     * such as {@link Calendar.widget.MonthView MonthView} and {@link Calendar.widget.CalendarRow CalendarRow}
     * may render multiple elements for long events.
     */
    getEventElements(eventRecord) {
      const eventId = this.eventStore.modelClass.asId(eventRecord);
      return this.eventContentElement.querySelectorAll(`[data-event-id="${eventId}"]`);
    }
    onEventMouseOverOut(domEvent) {
      const me = this, {
        currentOverEventEl
      } = me, isOut = domEvent.type === "mouseout", toElement = domEvent[isOut ? "relatedTarget" : "target"], toEventEl = (toElement == null ? void 0 : toElement.closest(".b-cal-event-wrap")) || null, isChange = toEventEl !== (currentOverEventEl || null);
      if (isChange) {
        if (isOut) {
          me.currentOverEventEl = null;
          if (currentOverEventEl) {
            Object.defineProperty(domEvent, "target", {
              configurable: true,
              get: () => currentOverEventEl
            });
          }
          return me.onCalendarPointerInteraction(domEvent);
        } else {
          me.currentOverEventEl = toEventEl;
          return me.onCalendarPointerInteraction(domEvent);
        }
      }
    }
    onEventMouseEnterLeave(domEvent) {
      if (domEvent.target.classList.contains("b-cal-event")) {
        return this.onCalendarPointerInteraction(domEvent);
      }
    }
    /**
     * Determines what is under the cursor of the specified event or what is described by the given element.
     * @param {Event|Element} domEvent The event or element
     * @returns {CalendarHit}
     */
    calendarHitTest(domEvent) {
      const me = this, { monthSelector } = me, date = me.getDateFromDomEvent(domEvent), target = DomHelper.getEventElement(domEvent);
      let ret = null, closest, eventRecord;
      if ((closest = target.closest(".b-cal-event-wrap")) && (eventRecord = me.eventStore.getById(closest.dataset.eventId))) {
        ret = {
          type: "event",
          eventElement: closest,
          eventRecord
        };
      } else if (closest = target.closest(me.dayNameSelector)) {
        ret = {
          type: "dayNumber",
          dayNumberElement: closest
        };
      } else if (closest = target.closest(".b-week-num,.b-week-number-cell")) {
        const weekElement = target.closest(".b-calendar-week");
        if (weekElement && weekElement.dataset.week) {
          ret = {
            type: "weekNumber",
            week: weekElement.dataset.week.split(",").map(Number),
            weekNumberElement: closest,
            weekElement
          };
        }
      } else if (monthSelector && target.closest(monthSelector)) {
        ret = {
          type: "monthName",
          month: date.getMonth(),
          date
        };
      }
      if (!ret) {
        if (closest = target.closest(".b-cal-cell-overflow")) {
          ret = {
            type: "cellOverflow",
            cellOverflowElement: closest
          };
        } else if (date) {
          ret = {
            type: "schedule"
          };
        }
      }
      if (ret) {
        ret.resource = me.getResourceRecord(domEvent);
        ret.cell = target.closest(".b-calendar-cell");
        ret.date = date;
        ret.view = me;
      }
      return ret;
    }
    onCalendarPointerInteraction(domEvent) {
      const me = this, { monthSelector } = me, { target } = domEvent, fromOverflowPopup = Boolean(target.closest(".b-overflowpopup")), domEventName = eventNameMap[domEvent.type], eventWrap = target.closest(".b-cal-event-wrap"), eventRecord = eventWrap ? me.eventStore.getById(eventWrap.dataset.eventId) : me.getEventRecord(target), date = domEvent.key ? eventRecord == null ? void 0 : eventRecord.startDate : me.getDateFromDomEvent(domEvent), eventElement = eventWrap || eventRecord && me.getEventElement(eventRecord, date), resourceElement = target.closest("[data-resource-id]"), resourceRecord = resourceElement && me.resourceStore.getById(resourceElement.dataset.resourceId);
      let result;
      if (resourceRecord) {
        result = me.trigger(`resource${domEventName}`, {
          domEvent,
          date,
          eventElement,
          eventRecord,
          resourceRecord,
          fromOverflowPopup
        });
      }
      if (target.closest(me.dayNameSelector)) {
        result = me.trigger(`dayNumber${domEventName}`, {
          domEvent,
          date,
          cellData: me.cellMap.get(date) || me.createCellData(date),
          resourceRecord,
          fromOverflowPopup
        });
        if (result === false) {
          return result;
        }
      }
      if (!fromOverflowPopup && !me.eventContentElement.contains(target)) {
        return;
      }
      if (result !== false && eventRecord) {
        const eventResult = me.trigger(`event${domEventName}`, {
          domEvent,
          date,
          eventElement,
          eventRecord,
          resourceRecord,
          fromOverflowPopup
        });
        if (eventResult) {
          result = eventResult;
        }
      }
      if (eventRecord) {
        return result;
      }
      if (target.closest(".b-week-num,.b-week-number-cell")) {
        const weekElement = domEvent.target.closest("[data-week]");
        if (weekElement) {
          return me.trigger(`weekNumber${domEventName}`, {
            domEvent,
            week: weekElement.dataset.week.split(",").map(Number),
            date: me.getDateFromElement(weekElement.querySelector(".b-calendar-cell")),
            fromOverflowPopup
          });
        }
      }
      if (monthSelector && target.closest(monthSelector)) {
        return me.trigger(`monthName${domEventName}`, {
          domEvent,
          month: date.getMonth(),
          date,
          fromOverflowPopup
        });
      }
      if (target.closest(".b-cal-cell-overflow")) {
        if (me.trigger(`cellOverflow${domEventName}`, {
          domEvent,
          date,
          fromOverflowPopup,
          resourceRecord
        }) !== false) {
          return;
        }
      }
      if (date && me.dayCellCls && domEvent.target.closest(`.${me.dayCellCls}`)) {
        result = me.trigger(`schedule${domEventName}`, {
          domEvent,
          date,
          fromOverflowPopup,
          resourceRecord
        });
        if (result === false) {
          return result;
        }
      }
      me.checkAutoCreateGesture(domEvent, me.getDateFromDomEvent(domEvent, true), resourceRecord || void 0);
      return result;
    }
    onCalendarKeyDown(keyEvent) {
      var _a2, _b, _c;
      if (keyEvent.ctrlKey && keyEvent.key.toLowerCase() === "z" && ((_a2 = this.calendar) == null ? void 0 : _a2.enableUndoRedoKeys)) {
        (_c = (_b = this.project) == null ? void 0 : _b.stm) == null ? void 0 : _c.onUndoKeyPress(keyEvent);
      } else {
        this.onCalendarPointerInteraction(keyEvent);
      }
    }
    isAllDayEvent(eventRecord) {
      return eventRecord.allDay || (eventRecord.isScheduled && this.dayTime ? this.dayTime.isInterDay(eventRecord) : eventRecord.isInterDay);
    }
    /**
     * Sort the given array of `events` in the desired order for this view.
     * @param {Scheduler.model.EventModel[]} events
     * @internal
     */
    sortEvents(events) {
      events.sort(this.eventSorter);
    }
    //region Extract configs
    // These functions are not meant to be called by any code other than Base#getCurrentConfig().
    // This excludes project and calendar from being serialized,
    // they are always assigned on creation not actually configurable
    preProcessCurrentConfigs(configs) {
      super.preProcessCurrentConfigs(configs);
      delete configs.calendar;
      delete configs.project;
    }
    // Extracts the current configs for the calendar view, with special handling to exclude project
    getCurrentConfig(options) {
      const result = super.getCurrentConfig(options);
      delete result.project;
      return result;
    }
    //endregion
  }, __publicField(_a, "$name", "CalendarMixin"), _a;
};
export {
  CalendarMixin_default as default
};
