import LocaleHelper from "@bryntum/core-thin/lib/localization/LocaleHelper.js";
import "@bryntum/scheduler-thin/lib/localization/Hu.js";
const locale = {
  localeName: "Hu",
  localeDesc: "<PERSON>gy<PERSON>",
  localeCode: "hu",
  EventEdit: {
    Calendar: "Napt\xE1r",
    "All day": "Eg\xE9sz nap",
    day: "Nap",
    week: "H\xE9t",
    month: "H\xF3nap",
    year: "\xC9v",
    decade: "\xC9vtized"
  },
  EventMenu: {
    duplicateEvent: "Esem\xE9ny duplik\xE1l\xE1sa",
    copy: "m\xE1sol\xE1s"
  },
  Calendar: {
    toggleSidebar: 'Oldals\xE1v l\xE1that\xF3s\xE1g\xE1nak v\xE1lt\xE1sa"',
    Today: "M\xE1sol\xE1s",
    next: (range) => `K\xF6vetkez\u0151 ${range}`,
    previous: (range) => `El\u0151z\u0151 ${range}`,
    plusMore: (value) => `+${value} tov\xE1bbi`,
    allDay: "Eg\xE9sz nap",
    endsOn: (d) => `V\xE9ge ${d}`,
    weekOfYear: ([y, w]) => `H\xE9t ${w}, ${y}`,
    loadFail: "A napt\xE1radatok bet\xF6lt\xE9se sikertelen. Forduljon a rendszergazd\xE1hoz."
  },
  CalendarDrag: {
    holdCtrlForRecurrence: "Ism\xE9tl\u0151d\u0151 esem\xE9nyhez nyomja a CTRL-t"
  },
  CalendarMixin: {
    eventCount: (count) => `${count || "Nincs"} esem\xE9ny`
  },
  EventTip: {
    "Edit event": "Esem\xE9ny szerkeszt\xE9se",
    timeFormat: "LST"
  },
  ModeSelector: {
    includeWeekends: "H\xE9tv\xE9g\xE9k besz\xE1m\xEDt\xE1sa",
    weekends: "H\xE9tv\xE9g\xE9k"
  },
  AgendaView: {
    Agenda: "Napirend"
  },
  MonthView: {
    Month: "H\xF3nap",
    monthUnit: "h\xF3nap"
  },
  WeekView: {
    weekUnit: "h\xE9t"
  },
  YearView: {
    Year: "\xC9v",
    yearUnit: "\xE9v",
    noEvents: "Nincsenek esem\xE9nyek"
  },
  EventList: {
    List: "Lista",
    Start: "Kezd\xE9s",
    Finish: "V\xE9ge",
    days: (count) => `${count > 1 ? `${count} ` : ""}nap${count === 1 ? "" : "ok"}`
  },
  DayView: {
    Day: "Nap",
    dayUnit: "nap",
    daysUnit: "nap",
    expandAllDayRow: "Eg\xE9sz nap r\xE9sz kibont\xE1sa",
    collapseAllDayRow: "Eg\xE9sz nap r\xE9sz \xF6sszecsuk\xE1sa",
    timeFormat: "LST",
    timeAxisTimeFormat: "LST"
  },
  DayResourceView: {
    dayResourceView: "Napi er\u0151forr\xE1sok"
  },
  Sidebar: {
    "Filter events": "Esem\xE9nyek sz\u0171r\xE9se"
  },
  WeekExpander: {
    expandTip: "Kattintson a sor kibont\xE1s\xE1hoz",
    collapseTip: "Kattintson a sor \xF6sszecsuk\xE1s\xE1hoz"
  }
};
var Hu_default = LocaleHelper.publishLocale(locale);
export {
  Hu_default as default
};
