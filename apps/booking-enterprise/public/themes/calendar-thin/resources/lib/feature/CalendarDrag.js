var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Base from "@bryntum/core-thin/lib/Base.js";
import CalendarFeature from "./CalendarFeature.js";
import DH from "@bryntum/core-thin/lib/helper/DateHelper.js";
import DomSync from "@bryntum/core-thin/lib/helper/DomSync.js";
import DomHelper from "@bryntum/core-thin/lib/helper/DomHelper.js";
import EventHelper from "@bryntum/core-thin/lib/helper/EventHelper.js";
import Rectangle from "@bryntum/core-thin/lib/helper/util/Rectangle.js";
import DomClassList from "@bryntum/core-thin/lib/helper/util/DomClassList.js";
import ObjectHelper from "@bryntum/core-thin/lib/helper/ObjectHelper.js";
import StringHelper from "@bryntum/core-thin/lib/helper/StringHelper.js";
import Draggable from "@bryntum/core-thin/lib/mixin/Draggable.js";
import Droppable from "@bryntum/core-thin/lib/mixin/Droppable.js";
import Hoverable from "@bryntum/core-thin/lib/mixin/Hoverable.js";
import Widget from "@bryntum/core-thin/lib/widget/Widget.js";
import RecurrenceConfirmationPopup from "@bryntum/scheduler-thin/lib/view/recurrence/RecurrenceConfirmationPopup.js";
import CalendarZone from "./CalendarZone.js";
import "@bryntum/core-thin/lib/util/drag/DragTipProxy.js";
import "../widget/EventTip.js";
const tentativeCls = "b-cal-tentative-event", SECONDS = 1e3, MINUTES = 60 * SECONDS, YYYY_MM_DD = "YYYY-MM-DD", edgeRe = /^b-hover-(top|right|bottom|left)$/, eventDragSym = Symbol("eventDragMode"), appendEventFooter = (domConfig, footer) => {
  let ret;
  if (domConfig.className === "b-cal-event-body") {
    domConfig.children.push(ret = footer);
  } else if (Array.isArray(domConfig.children)) {
    domConfig.children.forEach((c) => {
      if (!ret && c) {
        ret = appendEventFooter(c, footer);
      }
    });
  }
  return ret;
}, makeMode = (type) => Object.freeze({
  type,
  create: false,
  move: false,
  resize: false,
  [type]: true
}), modeDescriptor = {
  create: {
    finisher: "finishDropCreate",
    mode: makeMode("create"),
    validatorFn: "validateCreateFn"
  },
  move: {
    finisher: "finishDropMove",
    mode: makeMode("move"),
    validatorFn: "validateMoveFn"
  },
  resize: {
    finisher: "finishDropResize",
    mode: makeMode("resize"),
    validatorFn: "validateResizeFn"
  }
}, isAllDayView = {
  calendarrow: true,
  monthview: true,
  dayresourcecalendarrow: true
}, metaProp = Symbol("meta");
class Zone extends CalendarZone.mixin(Draggable, Droppable, Hoverable) {
  static get $name() {
    return "Zone";
  }
  static get configurable() {
    return {
      // We limit dropping to all *children* of the Droppable, not the Droppable element itself.
      // So that we avoid triggering "over" or "drop" on borders which would give incorrect
      // positional calculations.
      droppableSelector: "*",
      droppable: true,
      hoverable: null,
      hoverAnimationCls: "b-hover-anim",
      days: {
        $config: {
          // DayResourceView includes the resource as part of the days config.
          equal: (d1, d2) => ObjectHelper.isEqual(d1, d2) && (d1 == null ? void 0 : d1.resource) === (d2 == null ? void 0 : d2.resource)
        },
        value: null
      },
      dragProxy: {
        type: "tip",
        tooltip: null
        // borrowed from the Feature instance's tooltip config
      },
      eventRecord: null,
      hit: null,
      dragItemSelector: ".b-cal-event-wrap",
      overflow: {
        $config: "nullify",
        value: null
      },
      rootElement: null
    };
  }
  get dayTime() {
    var _a;
    return (_a = this.view) == null ? void 0 : _a.dayTime;
  }
  clearTime(date) {
    return this.dayTime.startOfDay(date);
  }
  findRootElement(view) {
    return view.contentElement;
  }
  getDateFromPosition(clientX, clientY) {
    return this.view.getDateFromPosition(clientX, clientY, false, this.view.dayTime);
  }
  // Draggable behaviors
  get dragEventer() {
    return this.owner;
  }
  beforeDrag(drag) {
    const me = this, { owner } = me, hit = me.hitTest(drag), eventRecord = hit == null ? void 0 : hit.eventRecord, insetProp = me.view.rtl ? "right" : "left";
    if (!hit || (eventRecord == null ? void 0 : eventRecord.readOnly) || (eventRecord == null ? void 0 : eventRecord.isCreating)) {
      return false;
    }
    hit.date && drag.set("date", hit.date);
    hit.resource && drag.set("resource", hit.resource);
    let mode, veto;
    switch (hit.type) {
      case "event":
        drag.set("eventRecord", eventRecord);
        drag.set("eventSourceHit", hit);
        drag.set("eventInset", hit.eventElement.style[insetProp]);
        drag.set("eventWidth", hit.eventElement.offsetWidth);
        mode = "move";
        veto = !owner.draggable || !eventRecord.isDraggable || me.draggable === false;
        if (!veto) {
          me.captureDragOffset(eventRecord, hit, drag);
        }
        break;
      case "resize":
        drag.set("eventRecord", eventRecord);
        drag.set("eventSourceHit", hit);
        drag.set("eventInset", hit.eventWrap.style[insetProp]);
        drag.set("eventWidth", hit.eventWrap.offsetWidth);
        mode = "resize";
        veto = !owner.resizable || !eventRecord.resizable || me.resizable === false;
        break;
      case "schedule":
      case "dayNumber":
        drag.set("eventCreate", hit);
        mode = "create";
        veto = !owner.creatable || me.creatable === false;
        break;
      default:
        return false;
    }
    if (veto) {
      return false;
    }
    drag.set("eventDragMode", drag[eventDragSym] = modeDescriptor[mode].mode);
  }
  dragStart() {
    var _a, _b;
    const me = this, { dragging: drag, owner, view } = me, { client } = owner, callback = () => {
      drag.lastMoveEvent && drag.move(drag.lastMoveEvent);
    }, config = {
      scrollables: client.scrollManager ? [
        {
          element: client.viewContainer.element,
          callback
        }
      ] : []
    };
    if (me.isResizing || me.isMoving) {
      me.refocus = owner.client.captureFocus();
    }
    if (me.isResizing) {
      (_a = drag.itemElement) == null ? void 0 : _a.classList.add("b-resizing");
    }
    if (view.scrollable) {
      config.scrollables.push({
        element: view.scrollable.element,
        callback
      });
    }
    if (view.horizontalScroller) {
      config.scrollables.push({
        element: view.horizontalScroller.element,
        callback
      });
    }
    (_b = client.scrollManager) == null ? void 0 : _b.startMonitoring(config);
  }
  captureDragOffset() {
  }
  cleanupDrag() {
    var _a;
    const me = this, { dragProxy, dragging } = me, { tooltip } = dragProxy, view = me.view.isOverflowPopup ? me.view.owner : me.view, { client } = me.owner, activeElement = DomHelper.getActiveElement(client);
    (_a = dragging == null ? void 0 : dragging.itemElement) == null ? void 0 : _a.classList.remove("b-resizing");
    if (dragging == null ? void 0 : dragging.aborted) {
      me.days = null;
    }
    view.afterRefresh(() => {
      var _a2;
      me.eventRecord && (me.days = null);
      if (DomHelper.getActiveElement(client) === activeElement) {
        (_a2 = me.refocus) == null ? void 0 : _a2.call(me, false, true);
      }
      me.refocus = null;
    });
    if (tooltip) {
      tooltip.hide();
      dragProxy.tooltip = null;
    }
  }
  dragEnd() {
    var _a, _b;
    (_b = (_a = this.owner.client) == null ? void 0 : _a._scrollManager) == null ? void 0 : _b.stopMonitoring();
    this.cleanupDrag();
  }
  makeDays(startDate, endDate) {
    const { dayTime, firstVisibleDate, lastVisibleDate } = this.view, days = [];
    endDate = DH.add(endDate, -1, "d");
    [startDate, endDate] = [startDate, endDate].map(
      (d) => d < firstVisibleDate ? firstVisibleDate : lastVisibleDate < d ? lastVisibleDate : d
    );
    let date = dayTime.startOfDay(startDate);
    for (const end = dayTime.ceil(endDate); !(end < date); date = DH.add(date, 1, "day")) {
      days.push(dayTime.dateKey(date));
    }
    return days;
  }
  onShowOverflowPopup({ overflowPopup }) {
    if (!this.overflow) {
      this.overflow = new OverflowZone({
        owner: this.owner,
        view: overflowPopup
      });
    }
  }
  // Droppable behaviors
  get calendarCellSelector() {
    return this.view.visibleCellSelector;
  }
  get calendarCells() {
    const container = this.dropRootElement;
    return container && DomHelper.children(container, this.calendarCellSelector);
  }
  get isCreating() {
    var _a, _b;
    return (_b = (_a = this.dropping) == null ? void 0 : _a[eventDragSym]) == null ? void 0 : _b.create;
  }
  get isMoving() {
    var _a, _b, _c;
    const mode = ((_a = this.dropping) == null ? void 0 : _a[eventDragSym]) || ((_b = this.dragging) == null ? void 0 : _b[eventDragSym]);
    return mode ? mode.move : Boolean((_c = this.dropping) == null ? void 0 : _c.peek("eventRecord"));
  }
  /**
   * Returns true if a resize operation is active
   * @property {Boolean}
   * @readonly
   */
  get isResizing() {
    var _a, _b;
    return (_b = (_a = this.dragging) == null ? void 0 : _a[eventDragSym]) == null ? void 0 : _b.resize;
  }
  get recurrable() {
    return this.isDayZone || this.isMonthZone;
  }
  cleanupDrop() {
    this.eventRecord = this.eventDom = this.days = null;
    this.noTip = false;
  }
  createEvent(data, dropping = this.dropping) {
    const mode = (dropping == null ? void 0 : dropping[eventDragSym]) || modeDescriptor.create.mode;
    this.setupEvent(data, mode.create);
  }
  async dragDrop(drag) {
    if (this.isResizing || drag.target === this) {
      return drag.finalizer = this.finishDrop(drag);
    }
  }
  dragEnter(drag) {
    const me = this, mode = drag[eventDragSym], hit = drag.source === me && drag.peek("eventSourceHit") || me.hitTest(drag);
    if (me.isMoving) {
      me._hit = hit;
      me.startMove(drag.peek("eventRecord"));
    } else if (!mode || drag.source !== me) {
      return false;
    } else if (mode.resize) {
      me._hit = hit;
      me.startResize(hit);
    } else if (mode.create) {
      me._hit = hit;
      me.startCreate(drag.peek("date"), drag.peek("eventCreate"));
    } else {
      return false;
    }
  }
  dragLeave() {
    if (!this.isResizing) {
      this.cleanupDrop();
    }
  }
  dragMove(drag) {
    this.hit = this.pickDropTarget(drag);
  }
  dropHitMove(drag, hit, eventRecord) {
    const me = this, { endDate, startDate } = eventRecord.isScheduled ? eventRecord : me.eventRecord, durationSec = DH.diff(startDate, endDate, "s");
    let { date, resource } = hit;
    date = me.clearTime(date);
    if (!eventRecord.allDay) {
      date = DH.add(date, DH.diff(me.clearTime(startDate), startDate, "s"), "s");
    }
    date = me.applyDragOffset(date, drag);
    const data = {
      startDate: date,
      endDate: DH.add(date, durationSec, "s")
    };
    if (resource == null ? void 0 : resource.eventColor) {
      data.eventColor = resource.eventColor;
    }
    me.setEventData(data);
  }
  dropHitNowhere() {
    this.days = null;
  }
  dropHitResize(drag, hit, eventHit) {
    const me = this, { eventRecord } = me, { startDate, endDate } = eventRecord, date = me.clearTime(hit.date);
    let changes, end;
    if (eventHit.atEnd) {
      end = DH.add(date, 1, "d");
      changes = {
        startDate,
        endDate: startDate < end ? end : DH.add(startDate, 1, "d")
      };
    } else {
      changes = {
        startDate: date < endDate ? date : DH.add(endDate, -1, "d"),
        endDate
      };
    }
    changes.duration = DH.diff(changes.startDate, changes.endDate, eventRecord.durationUnit);
    me.setEventData(changes);
  }
  async finishDrop(drag) {
    const me = this, {
      eventRecord,
      resource,
      owner,
      view
    } = me, { eventStore } = view, mode = drag[eventDragSym], descriptor = modeDescriptor[(mode == null ? void 0 : mode.type) || "move"];
    if (descriptor && me.hit) {
      const validation = descriptor.validatorFn ? await owner.callback(owner[descriptor.validatorFn], owner, [{
        drag,
        eventRecord,
        event: drag.event
      }]) : true;
      if (validation !== false) {
        await me[descriptor.finisher](
          drag,
          owner,
          eventRecord,
          eventStore,
          validation,
          async (eventName, callback, callbackFalse) => {
            let info = {
              drag,
              eventRecord,
              newStartDate: eventRecord.startDate,
              newEndDate: eventRecord.endDate,
              resourceRecord: resource,
              validation,
              event: drag.event,
              feature: owner,
              view: drag[me.isResizing ? "source" : "target"].view
            };
            const result = await owner.client.trigger("before" + StringHelper.capitalize(eventName), info);
            if (result === false) {
              info = false;
              await (callbackFalse == null ? void 0 : callbackFalse());
            } else {
              await (callback == null ? void 0 : callback());
              delete info.newStartDate;
              delete info.newEndDate;
              owner.client.trigger(eventName, info);
            }
            return info;
          }
        );
      }
    }
    me.isFinishing = true;
    me.cleanupDrop();
  }
  async finishDropCreate(drag, owner, eventRecord, eventStore, validation, triggerFn) {
    const add = (validation == null ? void 0 : validation.add) !== false;
    const result = await triggerFn("dragCreateEnd");
    if (result === false) {
      if (add) {
        eventStore.remove(eventRecord);
      }
    } else if (add && !eventStore.includes(eventRecord)) {
      eventStore.add(eventRecord);
    }
  }
  async finishDropMove(drag, owner, eventRecord, eventStore, validation, triggerFn) {
    var _a;
    let dropRec = await drag.get("eventRecord");
    const me = this, { view } = me, storeRec = dropRec.isOccurrence ? dropRec.recurringTimeSpan : dropRec, { source } = drag, fromResource = await drag.get("resource"), toResource = me.hit.resource, interView = source !== me, sourceStore = interView && ((_a = source.view) == null ? void 0 : _a.eventStore) && view.eventStore && source.view.eventStore !== view.eventStore ? source.view.eventStore : await drag.get("sourceStore"), isReassign = toResource && toResource !== fromResource;
    if (!isReassign && drag.source === drag.target && DH.isEqual(eventRecord.startDate, dropRec.startDate) && DH.isEqual(eventRecord.endDate, dropRec.endDate)) {
      if (dropRec.eventStore === eventStore) {
        return;
      }
    }
    await triggerFn("dragMoveEnd", async () => {
      if (!eventStore.getByInternalId(storeRec.internalId)) {
        if (sourceStore) {
          if (me.owner.removeFromExternalStore) {
            sourceStore.remove(dropRec);
          } else {
            dropRec = dropRec.copy();
          }
        } else {
          dropRec = eventRecord;
        }
        dropRec.setData("resourceId", null);
        eventStore.add(dropRec);
        await me.moveEventTo(drag, dropRec);
        const resource = toResource != null ? toResource : !isReassign && !view.project.resourceStore.includes(dropRec.resource) && me.owner.client.defaultCalendarId;
        if (resource != null && view.project.resourceStore.includes(resource)) {
          dropRec.assign(resource);
        } else if (!view.project.resourceStore.includes(dropRec.resource)) {
          dropRec.resources = [];
        }
      } else {
        dropRec = await me.finishDropConfirm(dropRec);
        if (dropRec) {
          await me.moveEventTo(drag, dropRec);
          if (isReassign) {
            dropRec.assign(toResource);
            dropRec.unassign(fromResource);
          }
        }
      }
      await view.project.commitAsync();
    });
  }
  async finishDropResize(drag, owner, eventRecord, eventStore, validation, triggerFn) {
    const me = this, { view } = me;
    await triggerFn("dragResizeEnd", async () => {
      let { eventRecord: editRec } = await drag.get("eventSourceHit");
      editRec = await this.finishDropConfirm(editRec);
      editRec && editRec.set({
        startDate: eventRecord.startDate,
        endDate: eventRecord.endDate,
        duration: DH.diff(eventRecord.startDate, eventRecord.endDate, editRec.durationUnit)
      });
      await view.project.commitAsync();
    });
  }
  async finishDropConfirm(eventRecord) {
    if (eventRecord.isOccurrence || eventRecord.isRecurring) {
      return new Promise((resolve) => {
        const dialog = new RecurrenceConfirmationPopup({
          owner: this.owner
        });
        dialog.confirm({
          actionType: "update",
          eventRecord,
          cancelFn() {
            resolve(null);
          },
          changerFn(eventRec) {
            resolve(eventRec);
          }
        });
      });
    }
    return eventRecord;
  }
  async moveEventTo(drag, eventRecord) {
    const me = this;
    let date = me.view.getDateFromElement(drag.targetElement);
    if (eventRecord.startDate) {
      date = DH.copyTimeValues(me.clearTime(date), eventRecord.startDate);
    }
    date = me.applyDragOffset(date, drag);
    if (drag.peek("sourceStore") !== me.view.eventStore && !eventRecord.startDate) {
      const autoCreate = drag.target.view.autoCreate || me.owner.client.autoCreate;
      date.setHours((autoCreate == null ? void 0 : autoCreate.startHour) || 8);
    }
    await me.setStartDate(eventRecord, date);
  }
  pickDropTarget(drag) {
    let hit = this.hitTest(drag);
    if (!hit && this.isResizing) {
      hit = this.hit;
    }
    return (hit == null ? void 0 : hit.date) ? hit : null;
  }
  setStartDate(eventRecord, date) {
    return eventRecord.set({
      startDate: date,
      endDate: DH.add(date, eventRecord.fullDuration)
    });
  }
  // Hoverable
  getHoverHandleCls(other) {
    return other ? "" : "b-gripper-vert";
  }
  hoverEnter() {
    var _a, _b;
    const me = this, hit = me.hitTest(me.hoverTarget), { gripper } = me.owner, cls = me.getHoverHandleCls(), otherCls = me.getHoverHandleCls(true);
    otherCls && gripper.classList.remove(otherCls);
    if (((_a = hit == null ? void 0 : hit.eventRecord) == null ? void 0 : _a.resizable) !== false && !((_b = hit == null ? void 0 : hit.eventRecord) == null ? void 0 : _b.readOnly)) {
      cls && gripper.classList.add(cls);
      me.hoverTarget.appendChild(gripper);
    }
  }
  hoverLeave(leaving) {
    const me = this, { gripper } = me.owner, cls = me.getHoverHandleCls();
    if (gripper.parentNode === leaving) {
      cls && gripper.classList.remove(cls);
      leaving.removeChild(gripper);
    }
  }
  // Misc
  applyDragOffset(date, drag) {
    var _a;
    const eventOffset = drag.peek("eventOffset");
    if (date && (eventOffset == null ? void 0 : eventOffset[0])) {
      if (drag.source.constructor === ((_a = drag.target) == null ? void 0 : _a.constructor)) {
        date = DH.add(date, -eventOffset[0], eventOffset[1]);
      }
    }
    return date;
  }
  hitTest(at) {
    const me = this, isDragContext = at == null ? void 0 : at.isDragContext, event = isDragContext ? at.event : at, target = isDragContext && at.targetElement || DomHelper.getEventElement(event);
    let src = me.view, hit = null, edge, wrapEl;
    if (target) {
      if (!src.calendarHitTest) {
        src = me.owner.client;
      }
      const horizontalStartEdge = src.rtl ? "right" : "left";
      hit = src.calendarHitTest(target);
      if (hit) {
        hit.eventWrap = wrapEl = target.closest(".b-cal-event-wrap");
        if (target.classList.contains("b-gripper")) {
          hit = {
            type: "resize",
            cell: hit.cell,
            date: hit.date,
            edge: edge = {},
            eventRecord: hit.eventRecord,
            resource: hit.resource,
            eventWrap: wrapEl,
            gripper: target,
            view: hit.view
          };
          DomClassList.normalize(wrapEl.className, "array").forEach((c) => {
            c = edgeRe.exec(c);
            c && (edge[c[1]] = true);
          });
          hit.atEnd = !(edge.top || edge[horizontalStartEdge]);
        } else if (!hit.eventRecord) {
          wrapEl = null;
        }
        if (wrapEl) {
          hit.eventTop = wrapEl.style.top;
        }
        if (target !== event) {
          hit.date = me.getDateFromPosition(event.clientX, event.clientY) || hit.date;
        }
      }
    }
    return hit;
  }
  renderEvent(eventRecord, first, last) {
    var _a, _b;
    const { view, owner } = this;
    Object.defineProperty(eventRecord, "assigned", this.assignmentsProperty);
    const dom = view.createEventDomConfig({
      eventRecord,
      isAllDay: isAllDayView[view.type],
      asSelected: eventRecord.data.realEventId && ((_b = (_a = owner.client).isEventSelected) == null ? void 0 : _b.call(_a, eventRecord.data.realEventId))
    });
    dom.className["b-cal-tentative-event"] = 1;
    dom.className["b-cal-tentative-event-first"] = first;
    dom.className["b-cal-tentative-event-last"] = last;
    delete eventRecord.assigned;
    if (last) {
      const { footer } = owner;
      if (footer && !view.showTime.endTime) {
        appendEventFooter(dom, ObjectHelper.assign({
          html: DH.format(eventRecord.endDate, view.timeFormat)
        }, footer));
      }
    }
    return dom;
  }
  setEventData(data, creating) {
    const me = this, { eventRecord, view } = me, { duration, startDate } = data;
    if (startDate && duration != null && !data.endDate) {
      data = {
        ...data,
        endDate: DH.add(startDate, duration, eventRecord.durationUnit)
      };
    }
    eventRecord.set(data);
    if (me.eventDom) {
      me.eventDom.className[view.shortEventCls] = eventRecord.durationMS <= view.shortEventDuration;
    } else {
      if (creating) {
        let name, { newName } = me.owner;
        if (newName) {
          const newNameFn = me.view.resolveCallback(newName, view, false);
          name = (newNameFn == null ? void 0 : newNameFn.handler.call(view, eventRecord)) || newName;
        } else {
          newName = view.autoCreate.newName;
          name = view.resolveCallback(newName, view, false) ? view.callback(newName, view, [view, eventRecord.startDate, view.resource || view.defaultCalendar]) : newName;
        }
        eventRecord.name = name;
      }
      me.eventDom = me.renderEvent(eventRecord);
    }
  }
  setupEvent(data, creating) {
    var _a;
    const me = this, { owner } = me, {
      defaultCalendar,
      eventStore
    } = me.view, { tooltip } = owner, eventRecord = me.eventRecord = owner.eventRecord = eventStore.createRecord({
      // must pass empty object for CalendarStores hook of createRecord() to set resourceId
      // and because the data passed is field *names*, not dataSources
    }), assignments = creating ? /* @__PURE__ */ new Set([new eventStore.assignmentStore.modelClass({
      event: eventRecord,
      resource: defaultCalendar
    })]) : data.assigned;
    me.assignmentsProperty = {
      configurable: true,
      value: assignments
    };
    delete data.assigned;
    if (data[metaProp]) {
      eventRecord.meta = data[metaProp];
    }
    me.setEventData(data, creating);
    if (me.isCreating && defaultCalendar) {
      eventStore.assignmentStore.assignEventToResource(eventRecord, defaultCalendar);
    }
    const { eventDom } = me;
    eventDom.className[tentativeCls] = 1;
    if (!me.noTip && !tooltip.disabled && ((_a = me.dragging) == null ? void 0 : _a.has("eventCreate"))) {
      tooltip.eventRecord = eventRecord;
      tooltip.recurrenceHint = creating && me.recurrable ? owner.recurrenceTip : "";
      me.dragProxy.tooltip = tooltip;
    }
    return eventDom;
  }
  startCreate(date) {
    this.createEvent({
      allDay: true,
      startDate: date,
      durationUnit: this.durationUnit,
      endDate: DH.add(date, 1, "d")
    });
  }
  get durationUnit() {
    return this.view.dragUnit || this.owner.durationUnit;
  }
  startMove(eventRecord) {
    var _a, _b, _c, _d;
    const me = this, data = ObjectHelper.clone(eventRecord.dataByFieldName), drag = me.dropping, { hit } = me, color = (_c = (_a = hit == null ? void 0 : hit.resource) == null ? void 0 : _a.eventColor) != null ? _c : (_b = drag.peek("resource")) == null ? void 0 : _b.eventColor;
    if (color && !data.eventColor) {
      data.eventColor = color;
    }
    data.assigned = eventRecord.assigned;
    if (!data.startDate) {
      data.startDate = (hit == null ? void 0 : hit.date) || /* @__PURE__ */ new Date();
      data.endDate = DH.add(data.startDate, eventRecord.duration || 1, eventRecord.durationUnit || "h");
    }
    if (hit == null ? void 0 : hit.resource) {
      data.resourceId = hit.resource.id;
    }
    if (!data.resourceId) {
      data.resourceId = drag.peek("resourceId") || ((_d = me.owner.client) == null ? void 0 : _d.defaultCalendarId);
    }
    data.realEventId = data.id;
    delete data.id;
    data[metaProp] = ObjectHelper.clone(eventRecord.meta);
    me.setupEvent(data);
  }
  startResize(eventHit) {
    var _a;
    const { eventRecord } = eventHit, data = ObjectHelper.clone(eventRecord.dataByFieldName);
    data[metaProp] = ObjectHelper.clone(eventRecord.meta);
    this.createEvent({
      ...data,
      // Include the Set of assignments in the data for the tentative event
      assigned: eventRecord.assigned,
      id: `dragResize-event-${data.id}`,
      eventColor: data.eventColor || ((_a = eventRecord.resource) == null ? void 0 : _a.eventColor),
      recurrenceRule: null,
      realEventId: data.id
    });
  }
  // Configs
  configureListeners(drag) {
    const listeners = super.configureListeners(drag);
    listeners.element = this.view.rootElement;
    return listeners;
  }
  updateHit(hit) {
    const me = this, drag = me.dropping, mode = hit && drag[eventDragSym];
    if (hit) {
      if (me.isMoving) {
        me.dropHitMove(drag, hit, drag.peek("eventRecord"));
      } else if (mode == null ? void 0 : mode.create) {
        me.dropHitCreate(drag, hit, drag.peek("eventCreate"));
      } else if (mode == null ? void 0 : mode.resize) {
        me.dropHitResize(drag, hit, drag.peek("eventSourceHit"));
      }
    } else {
      me.dropHitNowhere(drag);
    }
  }
  updateDays(days) {
    const me = this, { calendarCells, eventDom, dayValues } = me, newDayValues = calendarCells && {};
    let first = true, cell, date, dayValue, i, lastCell;
    me.dayValues = newDayValues;
    if (calendarCells && eventDom) {
      for (i = 0; i < calendarCells.length; ++i) {
        cell = calendarCells[i];
        if (days == null ? void 0 : days.includes(cell.dataset.date)) {
          lastCell = cell;
        }
      }
      for (i = 0; i < calendarCells.length; ++i) {
        cell = calendarCells[i];
        date = cell.dataset.date;
        if (days == null ? void 0 : days.includes(date)) {
          if (!(dayValue = dayValues == null ? void 0 : dayValues[date])) {
            dayValue = me.includeDay(date, cell, first, cell === lastCell) || true;
            first = false;
          } else {
            delete dayValues[date];
          }
          newDayValues[date] = dayValue;
        }
      }
      if (dayValues) {
        const cleanDom = () => {
          for (i in dayValues) {
            me.removeDay(i, dayValues[i], me.isCreating);
          }
        };
        if (!days && me.isCreating && !me.dragging.aborted) {
          me.view.afterRefresh(cleanDom);
        } else {
          cleanDom();
        }
      }
    }
  }
  updateEventRecord(record) {
    this.owner.eventRecord = record;
  }
  updateOverflow(value, instance) {
    if (!value && instance) {
      instance.destroy();
    }
    return value;
  }
  updateOwner(owner) {
    this.hoverIgnoreElement = owner == null ? void 0 : owner.gripper;
  }
  updateRootElement(rootEl) {
    const me = this;
    me.dragRootElement = rootEl;
    me.dropRootElement = me.droppable ? rootEl : null;
    if (!me.owner.resizable) {
      me.hoverable = false;
    }
    me.hoverRootElement = me.hoverable ? rootEl : null;
  }
  updateView(view, was) {
    var _a;
    super.updateView(view, was);
    const me = this;
    me.rootElement = view ? me.findRootElement(view) : null;
    (_a = me._overflowDetacher) == null ? void 0 : _a.call(me);
    if (view == null ? void 0 : view.isDayCellRenderer) {
      me._overflowDetacher = view.ion({
        thisObj: me,
        showOverflowPopup: "onShowOverflowPopup"
      });
    }
  }
}
Zone.prototype._eventRecord = null;
class BaseDayZone extends Zone {
  static get $name() {
    return "BaseDayZone";
  }
  static get configurable() {
    return {
      hoverSelector: ".b-cal-event-wrap",
      draggingClsSelector: ".b-dayview-content"
    };
  }
  getHoverHandleCls(other) {
    let vert = this.isAllDayZone;
    if (other) {
      vert = !vert;
    }
    return `b-gripper-${vert ? "vert" : "horz"}`;
  }
}
class AllDayZone extends BaseDayZone {
  static get $name() {
    return "AllDayZone";
  }
  static get configurable() {
    return {
      hoverEdges: "lr"
    };
  }
  // Drag handling
  dragEnter(drag) {
    if (!this.view.eventsPerCell) {
      this.view.expandGutter();
    }
    return super.dragEnter(drag);
  }
  dragLeave(drag) {
    this.view.collapseGutter();
    super.dragLeave(drag);
  }
  captureDragOffset(eventRecord, hit, drag) {
    drag.set("eventOffset", [
      Math.max(Math.floor(DH.diff(eventRecord.startDate, hit.date, "d")), 0),
      "d"
    ]);
  }
  // Drop handling
  dropHitCreate(drag, hit, dragFrom) {
    let endDate = this.clearTime(hit.date), startDate = this.clearTime(dragFrom.date);
    if (endDate < startDate) {
      [startDate, endDate] = [endDate, startDate];
    }
    this.setEventData({
      startDate,
      duration: DH.as(this.eventRecord.durationUnit, DH.diff(startDate, endDate, "d") + 1, "d")
    });
  }
  async moveEventTo(drag, dropRec) {
    const me = this, hit = me.hitTest(drag), date = me.applyDragOffset(hit == null ? void 0 : hit.date, drag), newDate = new Date(dropRec.startDate);
    if (date) {
      newDate.setFullYear(date.getFullYear());
      newDate.setMonth(date.getMonth());
      newDate.setDate(date.getDate());
      if (me.view.dayTime.startShift) {
        newDate.setHours(date.getHours());
        newDate.setMinutes(date.getMinutes());
        newDate.setSeconds(date.getSeconds());
        dropRec.duration = 1;
      } else {
        if (!me.view.isAllDayEvent(dropRec)) {
          dropRec.allDay = true;
        }
      }
      await me.setStartDate(dropRec, newDate);
    }
  }
  // Misc
  setEventData(data, creating) {
    super.setEventData(data, creating);
    const me = this, { eventRecord } = me;
    const { startDate, endDate } = eventRecord;
    if (creating && !me.view.dayTime.startShift) {
      eventRecord.allDay = true;
    }
    me.days = me.makeDays(startDate, endDate);
  }
  // Configs
  updateDays(days) {
    var _a, _b;
    const me = this;
    let { eventEl } = me;
    if (days == null ? void 0 : days.length) {
      if (!eventEl) {
        me.eventEl = eventEl = DomHelper.createElement({
          ...me.eventDom
        });
        eventEl.classList.add("b-allday");
      }
      const { dropRootElement } = me, { visibleCellSelector, weekLength } = me.view, eventTop = (_b = (_a = me.dragging) == null ? void 0 : _a.peek("eventSourceHit")) == null ? void 0 : _b.eventTop, cells = DomHelper.children(dropRootElement, visibleCellSelector), cell = DomHelper.down(dropRootElement, `${visibleCellSelector}[data-date='${days[days.length - 1]}']`);
      DomHelper.down(cell, ".b-cal-event-bar-container").appendChild(eventEl);
      eventEl.style[me.view.rtl ? "right" : "left"] = DomHelper.percentify(100 * (cells.indexOf(cell) - days.length + 1) / weekLength);
      eventEl.style.width = DomHelper.percentify(100 * days.length / weekLength);
      if (eventTop) {
        eventEl.style.top = eventTop;
      }
      me.view.scrollable.scrollIntoView(eventEl, true);
    } else if (eventEl) {
      if (eventEl.classList.contains("b-cal-tentative-event")) {
        eventEl.remove();
      }
      me.eventEl = null;
    }
  }
  updateView(view, was) {
    if (view) {
      const multiDay = DH.diff(view.startDate, view.endDate, "d") > 1;
      this.hoverable = multiDay;
      this.draggable = multiDay || this.view.owner.isDayView;
    }
    super.updateView(view, was);
  }
}
class DayZone extends BaseDayZone {
  static get $name() {
    return "DayZone";
  }
  static get configurable() {
    return {
      dragEventId: null,
      hoverable: true,
      hoverEdges: "tb",
      times: {
        $config: {
          equal: "array"
        },
        value: null
      },
      alDayZoneClass: AllDayZone,
      // When dragging events, configure the DayZone with constrainToDay : true
      // to prevent dragging events so that they cross the day start or end boundaries.
      constrainToDay: false
    };
  }
  construct(...args) {
    super.construct(...args);
    const me = this, { allDayEvents } = me.view;
    if (allDayEvents && me.view.showAllDayHeader !== false) {
      me.allDayZone = new me.alDayZoneClass({
        active: me.active,
        owner: me.owner,
        view: allDayEvents,
        resource: me.resource
      });
    }
  }
  syncDraggingElements(eventId, active) {
    var _a;
    const { dragging: drag } = this, { draggingItemCls } = drag.source, containerEl = drag.itemElement.closest(".b-dayview-day-container"), elements = (_a = containerEl == null ? void 0 : containerEl.querySelectorAll(`[data-event-id="${eventId}"]`)) != null ? _a : [];
    for (const el of elements) {
      el.classList.toggle(draggingItemCls, active);
    }
  }
  hitTest(at) {
    const { view } = this, { dayTime } = view, hit = super.hitTest(at);
    if (hit) {
      hit.overDate = (at == null ? void 0 : at.isDragContext) ? view.getDateFromPosition(at.event.clientX, Rectangle.fromScreen(view.dayContainerElement).y, false, dayTime) : dayTime.startOfDay(hit.date);
    }
    return hit;
  }
  updateDragEventId(eventId, previousEventId) {
    previousEventId && this.syncDraggingElements(previousEventId, false);
    eventId && this.syncDraggingElements(eventId, true);
  }
  get eventRecord() {
    var _a;
    return super.eventRecord || ((_a = this.allDayZone) == null ? void 0 : _a.eventRecord);
  }
  set eventRecord(value) {
    super.eventRecord = value;
  }
  get recurring() {
    var _a;
    return this.isCreating && ((_a = this.eventRecord) == null ? void 0 : _a.recurrenceRule) != null;
  }
  get droppingAllDay() {
    var _a, _b;
    const eventRecord = (_a = this.dropping) == null ? void 0 : _a.peek("eventRecord");
    return (eventRecord == null ? void 0 : eventRecord.startDate) && ((_b = this.view) == null ? void 0 : _b.isAllDayEvent(eventRecord)) || false;
  }
  get useAllDay() {
    return this.droppingAllDay && this.view.showAllDayHeader && this.dropping.source !== this.allDayZone;
  }
  get wasAllDay() {
    return this.droppingAllDay && this.dropping.source === this.allDayZone;
  }
  doDestroy() {
    var _a;
    (_a = this.allDayZone) == null ? void 0 : _a.destroy();
    super.doDestroy();
  }
  dragStart() {
    var _a, _b;
    super.dragStart();
    this.dragEventId = (_b = (_a = this.dragging.peek("eventRecord")) == null ? void 0 : _a.id) != null ? _b : null;
  }
  findRootElement(view) {
    return view.eventContentElement;
  }
  // Drag handling
  captureDragOffset(eventRecord, hit, drag) {
    drag.set("eventOffset", [
      Math.floor(DH.diff(eventRecord.startDate, hit.date, "m")),
      "m"
    ]);
  }
  cleanupDrag() {
    var _a;
    this.dragEventId = null;
    super.cleanupDrag();
    (_a = this.allDayZone) == null ? void 0 : _a.cleanupDrag();
  }
  // Drop handling
  cleanupDrop() {
    var _a, _b, _c;
    super.cleanupDrop();
    (_b = (_a = this.owner.client) == null ? void 0 : _a._scrollManager) == null ? void 0 : _b.stopMonitoring();
    (_c = this.allDayZone) == null ? void 0 : _c.cleanupDrop();
  }
  dropHitCreate(drag, hit) {
    const me = this, { durationUnit } = me.eventRecord;
    let endTime = hit.date, startTime = drag.peek("eventCreate").date, endDate = endTime, startDate = startTime, duration, recurrenceCount;
    const sameDay = !(me.clearTime(startTime) - me.clearTime(endTime)), recurring = drag.ctrlKey && !sameDay;
    if (recurring || sameDay) {
      endDate = me.clearTime(endTime);
      startDate = me.clearTime(startTime);
      startTime = startTime - startDate;
      endTime = endTime - endDate;
      if (endDate < startDate) {
        [startDate, endDate] = [endDate, startDate];
      }
      if (endTime < startTime) {
        [startTime, endTime] = [endTime, startTime];
      }
      startDate.setTime(startDate.getTime() + startTime);
      duration = DH.as(durationUnit, Math.max(me.view.increment, Math.floor(endTime - startTime)));
      if (recurring) {
        recurrenceCount = DH.diff(me.clearTime(startDate), me.clearTime(endDate), "d") + 1;
      }
    } else {
      if (endDate < startDate) {
        [startDate, endDate] = [endDate, startDate];
      }
      duration = DH.as(durationUnit, Math.floor((endDate - startDate) / MINUTES), "m");
    }
    me.setEventData({
      startDate,
      duration,
      recurrenceRule: recurring ? `FREQ=DAILY;COUNT=${recurrenceCount}` : null
    });
  }
  dropHitMove(drag, hit, eventRecord) {
    let startDate = hit.date;
    const me = this, { view } = me, dayStart = view.dayTime.startOfDay(startDate);
    if (me.useAllDay) {
      me.allDayZone.dropHitMove(drag, hit, eventRecord);
    } else {
      eventRecord = me.eventRecord;
      startDate = me.applyDragOffset(startDate, drag);
      if (view.showAllDayHeader && startDate < dayStart) {
        startDate = dayStart;
      } else if (!view.snapRelativeToEventStartDate) {
        const snapType = drag.event.clientX > drag.startEvent.clientX ? "ceil" : "floor";
        startDate = DH[snapType](startDate, view.increment);
      }
      me.setEventData({
        startDate,
        endDate: DH.add(startDate, eventRecord.duration, eventRecord.durationUnit),
        duration: eventRecord.duration
      });
    }
  }
  applyDragOffset(date, drag) {
    let result = super.applyDragOffset(date, drag);
    if (this.constrainToDay) {
      const { overDate } = this.hit;
      result = new Date(
        Math.max(
          Math.min(result, overDate.getTime() + this.view.dayTime.timeEnd - this.eventRecord.durationMS),
          overDate
        )
      );
    }
    return result;
  }
  dropHitResize(drag, hit, eventHit) {
    const me = this, { eventRecord } = me, date = hit.date;
    if (eventHit.atEnd) {
      if (eventRecord.startDate < date) {
        me.setEventData({
          endDate: date
        });
      }
    } else if (date < eventRecord.endDate) {
      me.setEventData({
        startDate: date
      });
    }
  }
  async moveEventTo(drag, dropRec) {
    const me = this, hit = me.hitTest(drag), date = hit == null ? void 0 : hit.date;
    if (date) {
      if (me.useAllDay) {
        await me.allDayZone.moveEventTo(drag, dropRec);
      } else {
        const { view } = me, newDate = me.applyDragOffset(date, drag), snapType = drag.event.clientX > drag.startEvent.clientX ? "ceil" : "floor", newStartDate = view.snapRelativeToEventStartDate ? newDate : DH[snapType](newDate, view.increment);
        if (me.wasAllDay) {
          const endOfDay = DH.add(view.dayTime.startOfDay(newStartDate), 24, "h"), newEndDate = DH.add(newStartDate, dropRec.duration, dropRec.durationUnit);
          dropRec.allDay = false;
          if (newEndDate > endOfDay) {
            const defaultDuration = DH.parseDuration(view.autoCreate.duration), newEndDate2 = new Date(Math.min(DH.add(newStartDate, defaultDuration.magnitude, defaultDuration.unit), endOfDay)), newDuration = DH.diff(newStartDate, newEndDate2, dropRec.durationUnit);
            dropRec.setDuration(newDuration, dropRec.unit);
          }
        }
        await me.setStartDate(dropRec, newStartDate);
      }
    }
  }
  startCreate(date) {
    this.createEvent({
      duration: 0,
      durationUnit: this.durationUnit,
      startDate: date,
      // It's only a provisional event until gesture is completed (possibly longer if an editor dialog is shown after)
      isCreating: true
    });
  }
  // Misc
  includeDay(date, cell, first, last) {
    return DomHelper.createElement({
      parent: cell.querySelector(".b-dayview-event-container"),
      ...this.renderEvent(this.eventRecord, first, last)
    });
  }
  removeDay(date, value) {
    if (value.classList.contains("b-cal-tentative-event")) {
      value.remove();
    }
  }
  setEventData(data, creating) {
    const me = this;
    if (me.useAllDay) {
      me.allDayZone.setEventData(data, creating);
      return;
    }
    super.setEventData(data, creating);
    const { eventRecord } = me, { endDate, startDate } = eventRecord, { dayTime } = me.view;
    let lastDate = endDate;
    if (me.recurring) {
      lastDate = DH.add(lastDate, eventRecord.recurrence.count - 1, "d");
    }
    me.days = me.makeDays(startDate, lastDate);
    me.times = [
      dayTime.delta(startDate, "s"),
      dayTime.delta(endDate, "s")
    ];
  }
  setupEvent(data, creating) {
    const me = this;
    if (me.useAllDay) {
      me.allDayZone.setupEvent(data, creating);
    } else {
      if (this.wasAllDay) {
        data.allDay = false;
        data.endDate = DH.add(data.startDate, data.duration = 1, data.durationUnit = "hour");
      }
      super.setupEvent(data, creating);
    }
  }
  // Configs
  updateDays(days, was) {
    super.updateDays(days, was);
    this.updateTimes(this.times, null);
  }
  updateTimes(times) {
    if (!times) {
      return;
    }
    const me = this, { allDayZone, dayValues, dragging, eventRecord, isResizing, recurring, view } = me, { dayTime, eventSpacing } = view, { endDate, startDate } = eventRecord, [startOffset, endOffset] = times, eventInset = !(dragging == null ? void 0 : dragging.aborted) && (dragging == null ? void 0 : dragging.peek("eventInset")), eventWidth = !(dragging == null ? void 0 : dragging.aborted) && (dragging == null ? void 0 : dragging.peek("eventWidth")), insetProp = view.rtl ? "right" : "left", firstDay = dayTime.dateKey(startDate), lastDay = dayTime.dateKey(endDate), multiDay = dayTime.startOfDay(startDate) < dayTime.startOfDay(endDate), heightScale = 100 / dayTime.duration("s");
    let { days } = me, date, dayEl, first, height, rect1, rect2, style, top;
    if (!days) {
      return;
    }
    for (date in dayValues) {
      dayEl = dayValues[date];
      first = date === firstDay;
      style = dayEl.style;
      top = startOffset * heightScale;
      height = (endOffset - startOffset) * heightScale;
      DomSync.sync({
        targetElement: dayEl,
        domConfig: me.renderEvent(me.eventRecord, first, date === days[days.length - 1])
      });
      if (!recurring && multiDay) {
        if (first) {
          height = 100 - top;
        } else if (date === lastDay) {
          height = top + height;
        } else {
          height = 100;
        }
      }
      style.top = recurring || first ? DomHelper.percentify(top) : 0;
      style.height = DomHelper.percentify(height);
      style.paddingBottom = DomHelper.setLength(eventSpacing);
      if (isResizing) {
        if (eventInset) {
          style[insetProp] = eventInset;
        }
        if (eventWidth) {
          style.width = `${eventWidth}px`;
        }
      }
      rect1 = Rectangle.from(dayEl.querySelector(".b-cal-event-desc"));
      if (rect1) {
        rect2 = Rectangle.from(dayEl.querySelector(".b-cal-event-footer"));
        rect2 && dayEl.classList.toggle("b-cal-event-footer-desc-overlap", Boolean(rect1.intersect(rect2)));
      }
    }
    if (!days || days.length < 2 || recurring) {
      allDayZone == null ? void 0 : allDayZone.cleanupDrop();
      days = null;
    } else if (allDayZone && view.showAllDayHeader) {
      if (!allDayZone.eventRecord) {
        allDayZone.noTip = true;
        const eventData = {
          startDate: eventRecord.startDate,
          endDate: eventRecord.endDate
        };
        if (isResizing) {
          const resizeEvent = dragging.peek("eventRecord");
          eventData.assigned = resizeEvent.assigned;
          eventData.eventColor = resizeEvent.eventColor;
        }
        allDayZone.createEvent(eventData, me.dropping);
      }
      allDayZone.eventRecord.set({
        startDate: eventRecord.startDate,
        endDate: DH.add(eventRecord.startDate, days.length - 1, "d")
      });
      allDayZone.days = days;
    }
  }
}
class ResourceCalendarRowZone extends AllDayZone {
  // Disable Hoverable behaviour. Resizing is not allowed.
  // Event bars are discontiguous, so resizing an event across multiple
  // cells has ambiguous semantics. We just disallow it.
  // Events in this view may only be *moved*
  syncHoverListeners() {
  }
  beforeDrag(drag) {
    const hit = this.hitTest(drag);
    if (!(hit == null ? void 0 : hit.resource) || hit.type === "resize" || hit.type === "schedule") {
      return false;
    }
    return super.beforeDrag(drag);
  }
  setupEvent() {
    this.view.defaultCalendar = (this.dragging || this.dropping).peek("resource");
    return super.setupEvent(...arguments);
  }
  updateHit(hit, was) {
    if (!hit || hit.resource) {
      super.updateHit(hit, was);
    }
  }
  makeDays(startDate) {
    var _a;
    const me = this, { isMoving } = me, drag = me.dragging || me.dropping, date = isMoving ? new Date(Math.max(startDate, me.view.firstVisibleDate)) : drag.peek("date"), result = super.makeDays(date, DH.add(date, 1, "d"));
    result.resource = (_a = me.hit) == null ? void 0 : _a.resource;
    return result;
  }
  updateDays(days) {
    var _a, _b;
    const me = this;
    let { view, eventEl } = me;
    if (days == null ? void 0 : days.length) {
      if (!eventEl) {
        me.eventEl = eventEl = DomHelper.createElement({
          ...me.eventDom
        });
        eventEl.classList.add("b-allday");
      }
      const eventTop = (_b = (_a = me.dragging) == null ? void 0 : _a.peek("eventSourceHit")) == null ? void 0 : _b.eventTop, resourceCell = DomHelper.down(
        me.dropRootElement,
        `${view.visibleCellSelector}:is(${days.map((d) => `[data-date="${d}"]`).join(",")}) [data-resource-id="${days.resource.id}"] > .b-cal-event-bar-container`
      );
      resourceCell.appendChild(eventEl);
      eventEl.style.width = "100%";
      if (eventTop) {
        eventEl.style.top = eventTop;
      }
      view.scrollable.scrollIntoView(eventEl, true);
    } else if (eventEl) {
      if (eventEl.classList.contains("b-cal-tentative-event")) {
        eventEl.remove();
      }
      me.eventEl = null;
    }
  }
}
__publicField(ResourceCalendarRowZone, "$name", "ResourceCalendarRowZone");
__publicField(ResourceCalendarRowZone, "configurable", {
  droppableSelector: ".b-calendarrow-cell-container,.b-dayresourcecalendarrow-cell-resources,.b-dayresource-allday.b-cal-cell-header"
});
class DayResourceZone extends DayZone {
  beforeDrag(drag) {
    const hit = this.hitTest(drag);
    if (!(hit == null ? void 0 : hit.resource)) {
      return false;
    }
    return super.beforeDrag(drag);
  }
  setupEvent() {
    this.view.defaultCalendar = (this.dragging || this.dropping).peek("resource");
    return super.setupEvent(...arguments);
  }
  hitTest(at) {
    const drag = this.dragging || this.dropping, result = super.hitTest(at);
    if (result && drag && !this.isMoving) {
      const fromDate = drag.peek("date");
      result.resource = drag.peek("resource");
      result.date.setDate(fromDate.getDate());
      result.date.setMonth(fromDate.getMonth());
      result.date.setFullYear(fromDate.getFullYear());
    }
    return result;
  }
  updateHit(hit, was) {
    if (!hit || hit.resource) {
      super.updateHit(hit, was);
    }
  }
  makeDays(startDate, endDate) {
    var _a;
    const me = this, { isMoving } = me, drag = me.dragging || me.dropping, date = isMoving ? startDate : drag.peek("date"), result = super.makeDays(date, DH.add(date, 1, "d"));
    result.resource = (_a = me.hit) == null ? void 0 : _a.resource;
    return result;
  }
  updateDays(days) {
    const me = this;
    let { eventEl } = me;
    if (days == null ? void 0 : days.length) {
      const cell = DomHelper.down(
        me.dropRootElement,
        `:is(${days.map((d) => `[data-date="${d}"]`).join(",")}) ${me.view.visibleCellSelector}[data-resource-id="${days.resource.id}"] .b-dayview-event-container`
      );
      if (!eventEl) {
        me.eventEl = eventEl = DomHelper.createElement({
          ...me.eventDom
        });
      }
      cell.appendChild(eventEl);
    } else if (eventEl) {
      if (eventEl.classList.contains("b-cal-tentative-event")) {
        me.view.setTimeout(() => eventEl.remove(), 100);
      }
      me.eventEl = null;
    }
    me._times = null;
  }
  updateTimes(times) {
    if (!times || !this.days) {
      return;
    }
    const me = this, { dayTime, eventSpacing } = me.view, [startOffset, endOffset] = times, heightScale = 100 / dayTime.duration("s"), { style } = me.eventEl, top = startOffset * heightScale, height = (endOffset - startOffset) * heightScale;
    DomSync.sync({
      targetElement: me.eventEl,
      domConfig: me.renderEvent(me.eventRecord, true, true)
    });
    style.top = DomHelper.percentify(top);
    style.height = DomHelper.percentify(height);
    style.paddingBottom = DomHelper.setLength(eventSpacing);
    style.width = "100%";
  }
}
__publicField(DayResourceZone, "$name", "DayResourceZone");
__publicField(DayResourceZone, "configurable", {
  alDayZoneClass: ResourceCalendarRowZone
});
class MonthZone extends Zone {
  static get $name() {
    return "MonthZone";
  }
  static get configurable() {
    return {
      coverage: {
        $config: {
          equal: ObjectHelper.isEqual
        },
        value: null
      },
      hoverable: true,
      hoverEdges: "lr",
      hoverSelector: ".b-cal-event-wrap.b-allday"
    };
  }
  findRootElement(view) {
    return view.weeksElement;
  }
  // Drag handling
  captureDragOffset(eventRecord, hit, drag) {
    drag.set("eventOffset", [
      Math.floor(DH.diff(this.clearTime(eventRecord.startDate), hit.date, "d")),
      "d"
    ]);
  }
  cleanupDrag() {
    super.cleanupDrag();
    const cleanDom = () => this.coverage = null;
    if (this.isCreating) {
      this.view.afterRefresh(cleanDom);
    } else {
      cleanDom();
    }
  }
  // Drop handling
  cleanupDrop() {
    var _a, _b;
    super.cleanupDrop();
    (_b = (_a = this.owner.client) == null ? void 0 : _a._scrollManager) == null ? void 0 : _b.stopMonitoring();
    const cleanDom = () => this.coverage = null;
    if (this.isCreating) {
      this.view.afterRefresh(cleanDom);
    } else {
      cleanDom();
    }
  }
  dropHitCreate(drag, hit) {
    const me = this, recurring = drag.ctrlKey, dragFrom = drag.peek("eventCreate");
    let recurrenceRule = null, count, day1, day2, endDate, startDate, week1, week2;
    endDate = me.clearTime(hit.date);
    startDate = me.clearTime(dragFrom.date);
    day1 = dragFrom.dayNumber;
    day2 = hit.dayNumber;
    week1 = dragFrom.weekOffset;
    week2 = hit.weekOffset;
    if (week2 < week1) {
      [week1, week2] = [week2, week1];
    }
    if (recurring) {
      count = week2 - week1 + 1;
      recurrenceRule = count > 1 ? `FREQ=WEEKLY;COUNT=${count}` : null;
      if (endDate < startDate) {
        startDate = endDate;
        if (day1 < day2) {
          startDate = DH.add(startDate, day1 - day2, "d");
        }
      } else if (day2 < day1) {
        startDate = DH.add(startDate, day2 - day1, "d");
      }
      if (day2 < day1) {
        [day1, day2] = [day2, day1];
      }
    } else if (endDate < startDate) {
      [startDate, endDate] = [endDate, startDate];
      [day1, day2] = [day2, day1];
    }
    me.setEventData({
      startDate,
      duration: DH.as(me.eventRecord.durationUnit, (recurring ? day2 - day1 : DH.diff(startDate, endDate, "d")) + 1, "d"),
      recurrenceRule
    });
  }
  dropHitNowhere(drag) {
    super.dropHitNowhere(drag);
    this.coverage = null;
  }
  // Misc
  setEventData(data, creating) {
    super.setEventData(data, creating);
    const { dropping, eventRecord, view } = this, { visibleCellSelector } = view, weekEls = DomHelper.children(view.weeksElement, "> .b-calendar-week"), coverage = {
      // weekNumber : String[] describing the days for a weekEl (in order of weekEls)
    }, add = (event) => {
      const { startDate, endDate } = event;
      for (let cells, cover, k, n, i = 0; i < weekEls.length; ++i) {
        cells = DomHelper.children(weekEls[i], visibleCellSelector);
        n = cells.length;
        cover = "";
        for (k = 0; k < n; ++k) {
          const dayStart = view.getDateFromElement(cells[k]), dayEnd = DH.add(dayStart, 1, "d");
          if (startDate < dayEnd && dayStart < endDate) {
            if (!k && startDate < dayStart) {
              cover = "<";
            }
            cover += k;
            if (k === n - 1 && dayEnd < endDate) {
              cover += ">";
            }
          }
        }
        if (cover) {
          (coverage[i] || (coverage[i] = [])).push(cover);
        }
      }
    };
    if ((dropping == null ? void 0 : dropping.has("eventRecord")) || !eventRecord.recurrence) {
      add(eventRecord);
    } else {
      eventRecord.recurrence.forEachOccurrence(view.startDate, view.endDate, add);
    }
    this.coverage = coverage;
  }
  // Configs
  updateCoverage(coverage) {
    var _a;
    const me = this, {
      dragging,
      weekValues,
      view
    } = me, { rtl } = view, {
      visibleCellSelector
    } = view, eventSourceHit = coverage && (dragging == null ? void 0 : dragging.peek("eventSourceHit")), eventTop = eventSourceHit == null ? void 0 : eventSourceHit.eventTop, newWeekValues = {}, eventRow = view.getWeekElementFor(eventSourceHit == null ? void 0 : eventSourceHit.eventElement), { weekElements } = view;
    let cell, cells, cov, cover, el, eventEl, extL, extR, i, k, weekEl;
    me.weekValues = newWeekValues;
    for (i = 0; i < weekElements.length; ++i) {
      if (!(cover = coverage == null ? void 0 : coverage[i])) {
        continue;
      }
      weekEl = weekElements[i];
      cells = DomHelper.children(weekEl, visibleCellSelector);
      for (k = 0; k < cover.length; ++k) {
        if (!(eventEl = (_a = weekValues == null ? void 0 : weekValues[i]) == null ? void 0 : _a.shift())) {
          eventEl = DomHelper.createElement(me.eventDom);
        }
        (newWeekValues[i] || (newWeekValues[i] = [])).push(eventEl);
        cov = cover[k];
        extL = cov.includes("<") ? 1 : 0;
        extR = cov.includes(">") ? 1 : 0;
        cov = cov.substr(extL, cov.length - extR - extL);
        eventEl.classList[extL ? "add" : "remove"]("b-continues-past");
        eventEl.classList[extR ? "add" : "remove"]("b-continues-future");
        eventEl.style[rtl ? "right" : "left"] = DomHelper.percentify(100 * Number(cov[0]) / cells.length);
        eventEl.style.width = DomHelper.percentify(100 * cov.length / cells.length);
        if (eventTop && weekEl === eventRow) {
          eventEl.style.top = eventTop;
        }
        cell = cells[Number(cov[cov.length - 1])];
        el = DomHelper.down(cell, ".b-cal-event-bar-container");
        if (el !== eventEl.parentNode) {
          el.appendChild(eventEl);
        }
      }
    }
    if (weekValues) {
      for (i in weekValues) {
        weekValues[i].forEach((el2) => {
          if (el2.classList.contains("b-cal-tentative-event")) {
            el2.remove();
          }
        });
      }
    }
  }
}
class OverflowZone extends Zone {
  static get $name() {
    return "OverflowZone";
  }
  static get configurable() {
    return {
      droppable: false,
      dragProxy: {
        type: "default",
        open(drag) {
          const me = this, { owner } = drag.source.view, sourceEl = drag.element.closest(".b-cal-event-wrap");
          if (owner.isYearView) {
            me.proxyEl = sourceEl.cloneNode(true);
            me.proxyEl.classList.add("b-cal-drag-proxy");
            me.proxyEl.style.width = `${sourceEl.offsetWidth}px`;
            me.proxyOffset = EventHelper.getClientPoint(drag.startEvent).getDelta(Rectangle.from(sourceEl));
            owner.contentElement.appendChild(me.proxyEl);
          }
        },
        dragMove(drag) {
          if (this.proxyEl) {
            DomHelper.alignTo(this.proxyEl, EventHelper.getClientPoint(drag.event).translate(10, 10), {
              align: "t0-t0"
            });
          }
        },
        close() {
          var _a;
          (_a = this.proxyEl) == null ? void 0 : _a.remove();
        }
      }
    };
  }
  findRootElement(view) {
    return view.contentElement;
  }
  beforeDrag(drag) {
    const hit = this.hitTest(drag);
    if ((hit == null ? void 0 : hit.type) !== "event" || !this.owner.draggable || !hit.eventRecord.isDraggable) {
      return false;
    }
    drag.set("eventRecord", hit.eventRecord);
    drag.set("eventDragMode", drag[eventDragSym] = modeDescriptor.move.mode);
  }
  dragStart() {
    this.view.hide();
  }
}
class YearZone extends Zone {
  static get $name() {
    return "YearZone";
  }
  startCreate() {
    var _a;
    (_a = this.view._overflowPopup) == null ? void 0 : _a.hide();
    super.startCreate(...arguments);
  }
  // Drop handling
  dragEnter(drag) {
    const result = super.dragEnter(drag);
    if (result !== false) {
      this.view.contentElement.classList.add(this.draggingCls);
    }
    return result;
  }
  dragLeave(drag) {
    super.dragLeave(drag);
    this.view.contentElement.classList.remove(this.draggingCls);
  }
  dropHitCreate(drag, hit, dragFrom) {
    const me = this;
    let endDate = me.clearTime(hit.date), startDate = me.clearTime(dragFrom.date);
    if (endDate < startDate) {
      [startDate, endDate] = [endDate, startDate];
    }
    me.setEventData({
      startDate,
      endDate: DH.add(endDate, 1, "d")
    });
    me.days = me.makeDayRange(startDate, endDate);
  }
  dropHitMove(drag, hit, eventRecord) {
    super.dropHitMove(drag, hit, eventRecord);
    const me = this, tempRec = me.eventRecord;
    let { endDate } = tempRec;
    if (tempRec.allDay) {
      endDate = DH.add(endDate, -1, "d");
    }
    me.days = me.makeDayRange(tempRec.startDate, endDate);
  }
  // Misc
  includeDay(date) {
    const els = DomHelper.children(this.view.bodyElement, `[data-date='${date}']`);
    els.forEach((e) => e.classList.add(`b-cal-tentative-event${this.view.hideNonWorkingDays ? ":not(.b-nonworking-day)" : ""}`));
    return els;
  }
  makeDayRange(startDate, endDate) {
    const days = [];
    for (let date = startDate; date <= endDate; date = DH.add(date, 1, "d")) {
      days.push(DH.format(date, YYYY_MM_DD));
    }
    return days;
  }
  removeDay(date, els) {
    els.forEach((e) => e.classList.remove("b-cal-tentative-event"));
  }
}
class ResourceViewZone extends Base {
  static get configurable() {
    return {
      view: null,
      zones: {
        $config: ["nullify"],
        value: []
      }
    };
  }
  updateView(view) {
    view.eachView((view2) => {
      this.onResourceViewViewCreate({ view: view2 });
    });
    view.ion({
      viewCreate: "onResourceViewViewCreate",
      thisObj: this
    });
  }
  onResourceViewViewCreate({ view }) {
    const me = this, {
      zones,
      owner
    } = me, modes = owner.client.constructor.Modes, type = owner.getViewZoneType(modes.resolveType(view.type));
    type && zones.push(owner.createZone(type, {
      view,
      resource: view.defaultCalendar
    }));
  }
  changeZones(zones, oldZones) {
    if ((oldZones == null ? void 0 : oldZones.length) && !zones) {
      for (let i = 0, { length } = oldZones; i < length; i++) {
        oldZones[i].destroy();
      }
    }
    return zones;
  }
}
class CalendarDrag extends CalendarFeature {
  constructor() {
    super(...arguments);
    __publicField(this, "callOnFunctions", true);
  }
  static get $name() {
    return "CalendarDrag";
  }
  static get type() {
    return "drag";
  }
  static get configurable() {
    return {
      disableOnReadOnly: true,
      localizableProperties: [
        "newName",
        "recurrenceTip"
      ],
      /**
       * Specify `false` to disallow creating events by drag gestures.
       * @config {Boolean}
       */
      creatable: true,
      /**
       * Specify `false` to disallow dragging events to new times or days.
       * @config {Boolean}
       */
      draggable: true,
      /**
       * The {@link Scheduler.model.EventModel#field-durationUnit} to use when drag-creating events.
       *
       * If not specified, the `dragUnit` property of the active view's
       * {@link Calendar.widget.mixin.CalendarMixin#property-autoCreate} is used.
       *
       * For {@link Calendar.widget.DayView}s, this is normally `'hour'`, for views with a granularity
       * level of one day, the default is `'day'`.
       * @config {String}
       */
      durationUnit: null,
      /**
       * A {@link Core.helper.DomHelper#typedef-DomConfig DOM config} object used to create an extra element
       * during event drag to contain the end time of the tentative event. This element contains the CSS class
       * `'b-cal-event-footer'` which can be used for styling.
       *
       * Set this to `null` to remove the end time rendering during drag operations.
       * @config {DomConfig}
       * @default
       */
      footer: {
        className: "b-cal-event-footer"
      },
      /**
       * This is configured as a {@link Core.helper.DomHelper#function-createElement-static DomHelper}
       * specification and is promoted to an `HTMLElement` during initialization. This element is moved between
       * calendar event elements on hover in order to show drag handles on the event under the mouse.
       * @config {HTMLElement|DomConfig}
       * @private
       */
      gripper: {
        class: "b-gripper"
      },
      /**
       * The name of new events or a function to call with the event record that will return the event name.
       *
       * If a function is supplied, it is called in the scope of (the `this` reference is set to) the
       * view being dragged within.
       *
       * Cpnfigure as `null` to use the dragged-in view's {@link Calendar.widget.mixin.CalendarMixin#config-autoCreate}
       * `newName` property.
       * @config {String|Function}
       * @param {Scheduler.model.EventModel} eventRecord The record being drag-created.
       * @returns {String} Name of new event
       */
      newName: "L{newEvent}",
      /**
       * The text to display as a hint for creating recurring events during drag. This tip is displayed in the
       * {@link #config-tooltip} in the same place as the recurrence summary (when there is no recurrence to
       * display).
       * @config {String}
       */
      recurrenceTip: "(L{holdCtrlForRecurrence})",
      /**
       * Specify `false` to disallow dragging the edges of events to change their start or end.
       * @config {Boolean}
       */
      resizable: true,
      /**
       * The tooltip to display during a drag create process. Disabled by
       * default, set to `true`, or provide a tooltip / config object, to enable it.
       * @config {Boolean|EventTipConfig|Calendar.widget.EventTip}
       */
      tooltip: {
        $config: ["lazy", "nullify"],
        value: {
          type: "eventTip",
          disabled: true,
          forSelector: null,
          tools: null
        }
      },
      /**
       * An empty function by default that allows you to perform custom validation on an event being created by
       * a drag gesture.
       *
       * The `drag` context contains the following data items (see {@link Core.util.drag.DragContext#function-get}):
       *
       *  - `eventDragMode` : The {@link #typedef-CalendarDragMode} object describing the drag operation.
       *  - `eventCreate` : The {@link Calendar.view.Calendar#typedef-CalendarHit} object that describes the target of the drag operation.
       *
       * Return `false` to cancel the create operation.
       *
       * This function can return a `Promise` (i.e., it can be `async`).
       *
       * Example:
       * ```javascript
       *  let calendar = new Calendar({
       *      features : {
       *          drag : {
       *              async validateCreateFn({ eventRecord, drag }) {
       *                  // This method can be async so it can make ajax requests or interact
       *                  // with the user...
       *
       *                  // if we return false, the event will be discarded
       *
       *                  // The following is equivalent to returning false:
       *                  //
       *                  // return {
       *                  //     // Do not add the event to the store
       *                  //     add  : false,
       *                  //     // Do not display the edit dialog (in the eventEdit feature):
       *                  //     edit : false
       *                  // };
       *                  //
       *                  // This simply adds the event and does not display the editor:
       *                  //
       *                  return {
       *                      edit : false
       *                  };
       *
       *                  // To do delay adding the event until the editor is done (and
       *                  // not via Cancel):
       *                  // return {
       *                  //     add : false
       *                  // };
       *              }
       *          }
       *      }
       *  });
       * ```
       * or:
       * ```javascript
       *  let calendar = new Calendar({
       *      features : {
       *          drag : {
       *              // Will resolve on the Calendar
       *              validateCreateFn : 'up.validateCreate'
       *          }
       *      },
       *      validateCreate{ eventRecord, drag } {
       *          ...
       *      }
       *  });
       * ```
       *
       * Return `true` if the event should be added to the event store and to inform the
       * {@link Calendar.feature.EventEdit eventEdit} feature to display the edit dialog.
       *
       * If this function returns an object, the `add` property can be set to `false`
       * to prevent adding to the event store, and the `edit` property can be set to `false` to inform the
       * `eventEdit` feature not to display the edit dialog.
       *
       * @config {Function|String}
       * @param {Object} info
       * @param {Core.util.drag.DragContext} info.drag The drag create context.
       * @param {Event} info.event The browser event object.
       * @param {Scheduler.model.EventModel} info.eventRecord The Event record.
       * @returns {Boolean|ValidateCreateResult} Return `false` if this event should be rejected.
       */
      validateCreateFn: () => {
      },
      /**
       * An empty function by default that allows you to perform custom validation on the event being moved to a
       * new date or time via a drag gesture.
       *
       * The `drag` context contains the following data items (see {@link Core.util.drag.DragContext#function-get}):
       *
       *  - `eventDragMode` : The {@link #typedef-CalendarDragMode} object describing the drag operation.
       *  - `eventRecord` : The {@link Scheduler.model.EventModel event record} being moved.
       *  - `eventSourceHit` : The {@link Calendar.view.Calendar#typedef-CalendarHit} object that describes the source of the drag operation.
       *
       * Return `false` to cancel the operation.
       *
       * This function can return a `Promise` (i.e., it can be `async`).
       *
       * Example:
       * ```javascript
       *  let calendar = new Calendar({
       *      features : {
       *          drag : {
       *              async validateMoveFn({ eventRecord, drag }) {
       *                  // This method can be async so it can make ajax requests or interact
       *                  // with the user...
       *
       *                  // if we return false, the event move will be discarded
       *              }
       *          }
       *      }
       *  });
       * ```
       * or:
       * ```javascript
       *  let calendar = new Calendar({
       *      features : {
       *          drag : {
       *              // Will resolve on the Calendar
       *              validateMoveFn : 'up.validateMove'
       *          }
       *      },
       *      validateMove{ eventRecord, drag } {
       *          ...
       *      }
       *  });
       * ```
       *
       * @config {Function|String}
       * @param {Object} info
       * @param {Core.util.drag.DragContext} info.drag The drag create context.
       * @param {Event} info.event The browser event object.
       * @param {Scheduler.model.EventModel} info.eventRecord The Event record.
       * @returns {Boolean} Return `false` if this event change should be rejected.
       */
      validateMoveFn: () => {
      },
      /**
       * An empty function by default that allows you to perform custom validation on the event whose `startDate`
       * or `endDate` is being modified via drag gesture.
       *
       * The `drag` context contains the following data items (see {@link Core.util.drag.DragContext#function-get}):
       *
       *  - `eventDragMode` : The {@link #typedef-CalendarDragMode} object describing the drag operation.
       *  - `eventSourceHit` : The {@link Calendar.view.Calendar#typedef-CalendarHit} object that describes the source of the drag operation.
       *
       * Return `false` to cancel the operation.
       *
       * This function can return a `Promise` (i.e., it can be `async`).
       *
       * Example:
       * ```javascript
       *  let calendar = new Calendar({
       *      features : {
       *          drag : {
       *              async validateResizeFn({ eventRecord, drag }) {
       *                  // This method can be async so it can make ajax requests or interact
       *                  // with the user...
       *
       *                  // if we return false, the event change will be discarded
       *              }
       *          }
       *      }
       *  });
       * ```
       * or:
       * ```javascript
       *  let calendar = new Calendar({
       *      features : {
       *          drag : {
       *              // Will resolve on the Calendar
       *              validateResizeFn : 'up.validateResize'
       *          }
       *      },
       *      validateResize{ eventRecord, drag } {
       *          ...
       *      }
       *  });
       * ```
       *
       * @config {Function|String}
       * @param {Object} info
       * @param {Core.util.drag.DragContext} info.drag The drag create context.
       * @param {Event} info.event The browser event object.
       * @param {Scheduler.model.EventModel} info.eventRecord The Event record.
       * @returns {Boolean|Promise} Return `false` if this event change should be rejected.
       */
      validateResizeFn: () => {
      },
      zoneTypes: {
        day: DayZone,
        // also covers WeekView
        month: MonthZone,
        year: YearZone,
        resource: ResourceViewZone,
        dayresource: DayResourceZone
        // AgendaView is not supported (though it could be a Draggable just not a Droppable)
      },
      /**
       * By default, when an event is dragged from an external source, the event is removed from the
       * source EventStore. Configure this as `false` to leave the event in place to allow for the dragging
       * in of the same event repeatedly.
       * @prp {Boolean}
       * @default
       */
      removeFromExternalStore: true
    };
  }
  // Called as a callOnFunctions function by the firing of the beforeDragStart event.
  // The beforeAutoCreate event is also triggered by CalendarMixin's detection of its own
  // autoCreate gesture. This event gives a common point for validation of UI-initiated
  // event creation.
  onBeforeDragStart(props) {
    var _a;
    const { client } = this, {
      drag,
      event: domEvent
    } = props, { view } = drag.source, operation = drag[eventDragSym].type, resourceRecord = (_a = view.getResourceRecord) == null ? void 0 : _a.call(view, drag.element), date = drag.peek("date"), event = {
      drag,
      domEvent,
      view,
      date,
      resourceRecord,
      eventRecord: drag.peek("eventRecord"),
      feature: this.owner
    };
    if (client.trigger(`beforeDrag${StringHelper.capitalize(operation)}`, event) === false) {
      return false;
    }
    if (drag.peek("eventDragMode").type === "create") {
      return client.trigger("beforeAutoCreate", { view, domEvent, date, resourceRecord });
    }
  }
  changeGripper(gripper, was) {
    was == null ? void 0 : was.remove();
    return gripper && DomHelper.createElement(gripper);
  }
  changeTooltip(config, existing) {
    if (config) {
      config = config === true ? this.constructor.configurable.tooltip.value : config;
      if (this.initialConfig.tooltip) {
        config.disabled = false;
      }
      config.ownerFeature = this;
    }
    return Widget.reconfigure(
      existing,
      config,
      /* owner = */
      this
    );
  }
}
CalendarDrag.initClass();
CalendarDrag._$name = "CalendarDrag";
export {
  CalendarDrag as default
};
