.b-dayresourcecalendarrow {
    .b-dayresourcecalendarrow-column {
        position  : relative;
        display   : flex;
        flex-flow : row;
        flex      : 1 0 auto;

        &:not(:last-child) {
            border-inline-end : $monthview-cell-border-width solid $dayresourcecalendarrow-border-color;
        }

        .b-resourcecalendarrow-column-resource-cell {
            flex      : 1 1 100%;
            min-width : var(--min-resource-width);
            display   : flex;
            flex-flow : column nowrap;

            &:not(:last-child) {
                border-inline-end : $monthview-cell-border-width solid $dayview-border-color;
            }
        }

        .b-cal-event-bar-container {
            position : relative;
        }
    }

    .b-cal-cell-header {
        align-items           : stretch;
        padding-bottom        : 0;

        // It's widthed by either the minDayWidth or the minResourceWidths of the resource cells.
        flex                  : 1 0 var(--min-day-width, auto);

        display               : grid;
        grid-template-columns : repeat(var(--visible-resource-count), 1fr);

        &:not(:last-child) {
            border-inline-end : $monthview-cell-border-width solid $dayresourcecalendarrow-border-color;
        }
    }

    .b-dayname-date {
        grid-row     : 1;
        grid-column  : 1/-1;
        display      : flex;
        flex-flow    : column nowrap;
        align-items  : center;
        padding      : .5em 0;
        max-width    : var(--min-resource-width);

        // This element must hang out in the center.
        // Owning grid can't justify-items : center because it has to flex three cells in the second row.
        justify-self : center;
    }

    .b-day-name, .b-day-date {
        flex            : 1;
        display         : flex;
        justify-content : center;
    }

    .b-dayresourcecalendarrow-resource-header {
        grid-row        : 2/2;
        border-top      : $monthview-cell-border-width solid $calendar-border-color;
        min-width       : var(--min-resource-width);
        display         : flex;
        gap             : .8em;
        justify-content : center;
        align-items     : center;
        padding-block   : 1em;
        padding-inline  : 0.5em;
        overflow        : hidden;
        contain         : inline-size layout;

        .b-resource-avatar {
            border-color : $calendar-border-color;
        }

        &.b-avatar-after {
            .b-resource-avatar {
                order : 1;
            }
        }

        &:not(:last-child) {
            border-inline-end : $monthview-cell-border-width solid $calendar-border-color;
        }
    }

    // Hide the resource avatar is the header cell gets narrow
    &.b-narrow-resource-header {
        .b-dayresourcecalendarrow-resource-header {
            .b-resource-avatar {
                display : none;
            }
        }
        // Alternatively, may be configured to hid the resource *name*
        &.b-hide-resource-name-when-narrow {
            .b-dayresourcecalendarrow-resource-header {
                .b-resource-avatar {
                    display : flex;
                }
                .b-dayresourcecalendarrow-resource-name {
                    display : none;
                }
            }
        }
    }

    .b-dayresourcecalendarrow-resource-name {
        white-space   : nowrap;
        text-overflow : ellipsis;
        overflow      : hidden;
        line-height   : $avatar-size;
    }

    // The future bars which, in a continuous CalendarRow are opacity:0 and inactivated
    // because the main bar starting at the startDate covers it are active in this view.
    // Each occurrence in each column must be interactive. This rule must not be more specific
    // because when dragging (b-draggable-started added to view), the events are inactivated
    // with a rule that applies pointer-events:none
    .b-cal-event-wrap {
        max-width      : 100%;
        opacity        : 1;
        pointer-events : all;
    }
}
