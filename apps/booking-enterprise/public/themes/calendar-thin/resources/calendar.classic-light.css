:root, :host{
  --b-fa-style-family-classic:"Font Awesome 6 Free";
  --b-fa-font-solid:normal 900 1em/1 "Font Awesome 6 Free";
}

@font-face{
  font-family:"Font Awesome 6 Free";
  font-style:normal;
  font-weight:900;
  font-display:block;
  src:url("../../core-thin/resources/fonts/fa-solid-900.woff2") format("woff2"), url("../../core-thin/resources/fonts/fa-solid-900.ttf") format("truetype");
}
.fas,
.b-fa-solid{
  font-weight:900;
}

.b-content-icon{
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
  display:inline-block;
  font-style:normal;
  font-variant:normal;
  font-weight:normal;
  line-height:1;
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  vertical-align:0;
}

.b-theme-info:before{
  content:'{"name":"Classic-Light"}';
}
.cal-view-readonly{
  filter:grayscale(75%);
  transition:filter 0.5s;
}

.b-show-events-dots{
  --event-count-dot-size:4px;
}

.b-cal-event-bar-container{
  flex:1;
  display:flex;
  flex-flow:column;
}
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-cal-event-reveal .b-cal-event, .b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-editing .b-cal-event, .b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-selected .b-cal-event, .b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-active .b-cal-event{
  background:var(--cal-event-color);
}
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-cal-event-reveal .b-cal-event:hover, .b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-editing .b-cal-event:hover, .b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-selected .b-cal-event:hover, .b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-active .b-cal-event:hover{
  background:var(--cal-event-color);
}
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-cal-event-reveal .b-cal-event .b-cal-event-icon,
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-cal-event-reveal .b-cal-event .b-cal-recurrence-icon,
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-cal-event-reveal .b-cal-event .b-cal-event-body, .b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-editing .b-cal-event .b-cal-event-icon,
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-editing .b-cal-event .b-cal-recurrence-icon,
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-editing .b-cal-event .b-cal-event-body, .b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-selected .b-cal-event .b-cal-event-icon,
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-selected .b-cal-event .b-cal-recurrence-icon,
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-selected .b-cal-event .b-cal-event-body, .b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-active .b-cal-event .b-cal-event-icon,
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-active .b-cal-event .b-cal-recurrence-icon,
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar).b-active .b-cal-event .b-cal-event-body{
  color:#fff;
}
.b-cal-event-bar-container .b-cal-event-wrap.b-solid-bar{
  opacity:0.8;
}
.b-cal-event-bar-container .b-cal-event-wrap.b-solid-bar.b-cal-event-reveal .b-cal-event-bar-container .b-cal-event-wrap.b-solid-bar.b-editing, .b-cal-event-bar-container .b-cal-event-wrap.b-solid-bar:hover{
  opacity:1;
}
.b-cal-event-bar-container .b-cal-event-wrap:not(.b-solid-bar) .b-cal-event .b-icon{
  color:var(--cal-event-color);
}
.b-cal-event-bar-container .b-cal-event{
  padding:0 0.3em;
  gap:0.3em;
}
.b-cal-event-bar-container .b-event-name{
  display:flex;
  align-items:center;
  gap:0.4em;
}
.b-cal-event-bar-container .b-solid-bar .b-event-time, .b-cal-event-bar-container .b-solid-bar .b-cal-event-desc, .b-cal-event-bar-container .b-solid-bar .b-icon{
  color:#fdfdfd;
}

.b-daycellrenderer .b-cal-event-bar-container{
  flex-grow:1;
  contain:style;
}
.b-daycellrenderer .b-cal-event-bar-container .b-cal-event-wrap{
  position:absolute;
  padding-right:2px;
  padding-left:1px;
  z-index:1;
}
.b-daycellrenderer .b-cal-event-bar-container .b-cal-event-wrap.b-overflow{
  opacity:0;
  pointer-events:none;
  max-width:14%;
}
.b-daycellrenderer .b-cal-cell-overflow:not(.b-calendar-cell), .b-daycellrenderer .b-cal-cell-no-content{
  flex:0 0 auto;
  align-self:stretch;
  text-align:center;
  white-space:nowrap;
  cursor:pointer;
  color:#888;
  margin:0 1px;
  font-family:inherit;
  padding:0 0.3em;
  display:flex;
  align-items:center;
  justify-content:center;
  gap:0.3em;
  border:0 none;
  background-color:transparent;
  text-overflow:ellipsis;
  overflow:hidden;
  border-radius:4px;
}
.b-daycellrenderer .b-cal-cell-overflow:not(.b-calendar-cell):hover, .b-daycellrenderer .b-cal-cell-overflow:not(.b-calendar-cell):focus-within, .b-daycellrenderer .b-cal-cell-no-content:hover, .b-daycellrenderer .b-cal-cell-no-content:focus-within{
  background-color:#dcdce2;
}
.b-daycellrenderer .b-cal-cell-overflow:not(.b-calendar-cell).b-active, .b-daycellrenderer .b-cal-cell-no-content.b-active{
  outline:0 none;
  font-weight:bold;
  color:#2f2f2f;
}
.b-daycellrenderer .b-cal-cell-no-content{
  padding:0.2em 0;
  white-space:normal;
  display:inline-block;
}
.b-daycellrenderer .b-calendar-cell:last-child .b-cal-event-bar-container .b-cal-event-wrap.b-overflow{
  right:0;
}

.b-cal-color-red{
  --cal-event-color:var(--cal-color-red);
}

.b-cal-color-pink{
  --cal-event-color:var(--cal-color-pink);
}

.b-cal-color-purple{
  --cal-event-color:var(--cal-color-purple);
}

.b-cal-color-violet{
  --cal-event-color:var(--cal-color-violet);
}

.b-cal-color-blue{
  --cal-event-color:var(--cal-color-blue);
}

.b-cal-color-cyan{
  --cal-event-color:var(--cal-color-cyan);
}

.b-cal-color-teal{
  --cal-event-color:var(--cal-color-teal);
}

.b-cal-color-green{
  --cal-event-color:var(--cal-color-green);
}

.b-cal-color-lime{
  --cal-event-color:var(--cal-color-lime);
}

.b-cal-color-yellow{
  --cal-event-color:var(--cal-color-yellow);
}

.b-cal-color-orange{
  --cal-event-color:var(--cal-color-orange);
}

.b-cal-color-deep-orange{
  --cal-event-color:var(--cal-color-deep-orange);
}

.b-cal-color-gray{
  --cal-event-color:var(--cal-color-gray);
}

.b-cal-color-gantt-green{
  --cal-event-color:var(--cal-color-gantt-green);
}

.b-calendar,
:not(.b-calendar-viewcontainer) > .b-calendarmixin{
  --cal-color-red:#ff8787;
  --cal-color-pink:#f783ac;
  --cal-color-purple:#ea80dc;
  --cal-color-violet:#9775fa;
  --cal-color-indigo:#748ffc;
  --cal-color-blue:#4dadf7;
  --cal-color-cyan:#3bc9db;
  --cal-color-magenta:#ff4dff;
  --cal-color-teal:#38d9a9;
  --cal-color-green:#69db7c;
  --cal-color-lime:#a9e34b;
  --cal-color-yellow:#fdd835;
  --cal-color-orange:#ffa94d;
  --cal-color-deep-orange:#ff7043;
  --cal-color-light-gray:#e0e0e7;
  --cal-color-gray:#a0a0a0;
  --cal-color-black:#000;
  --cal-color-gantt-green:#d2ebd3;
  --cal-event-color:#59b53b;
}
.b-agendaview.b-gridbase.b-enable-sticky .b-grid-subgrid.b-grid-horizontal-scroller{
  overflow:visible !important;
}
.b-agendaview.b-gridbase.b-enable-sticky .b-virtual-scrollers{
  display:none !important;
}
.b-agendaview.b-gridbase.b-enable-sticky .b-cal-agenda-date{
  position:sticky;
  top:top(1.5em 0)top();
  z-index:12;
}
.b-agendaview.b-gridbase.b-enable-sticky.b-responsive-small .b-agendacolumn-cell{
  padding-top:0;
}
.b-agendaview.b-gridbase.b-enable-sticky.b-responsive-small .b-agendacolumn-cell .b-cal-agenda-date{
  top:0;
  min-height:4em;
  background-color:#fff;
}
.b-agendaview.b-gridbase.b-enable-sticky.b-responsive-small .b-agendacolumn-cell.b-nonworking-day .b-cal-agenda-date{
  background-color:#fafbfb;
}
.b-agendaview .b-grid-cell::after{
  content:"";
  display:table;
  clear:both;
}
.b-agendaview .b-cal-eventlist-event-time{
  flex:0 0 12em;
  min-height:auto;
  white-space:nowrap;
}
.b-agendaview.b-responsive-small .b-cal-event-bar-container{
  margin-inline-start:2em;
  margin-block-start:1em;
}
.b-agendaview.b-responsive-small .b-cal-eventlist-event-time{
  flex:0 0 7.5em;
}
.b-agendaview.b-responsive-small .b-cal-agenda-grid-row .b-calendar-cell{
  display:block;
}

.b-agendaview-dayselector .b-calendarrow-body{
  display:none;
}

.b-cal-agenda-grid-row.b-selected, .b-grid-body-container:focus .b-cal-agenda-grid-row.b-selected, .b-gridbase .b-cal-agenda-grid-row.b-grid-row.b-hover .b-grid-cell.b-calendar-cell, .b-gridbase .b-cal-agenda-grid-row.b-grid-row.b-hover .b-grid-cell.b-calendar-cell:not(.b-nonworking-day):hover{
  background-color:transparent;
}
.b-cal-agenda-grid-row.b-selected .b-calendar-cell.b-nonworking-day, .b-grid-body-container:focus .b-cal-agenda-grid-row.b-selected .b-calendar-cell.b-nonworking-day, .b-gridbase .b-cal-agenda-grid-row.b-grid-row.b-hover .b-grid-cell.b-calendar-cell .b-calendar-cell.b-nonworking-day, .b-gridbase .b-cal-agenda-grid-row.b-grid-row.b-hover .b-grid-cell.b-calendar-cell:not(.b-nonworking-day):hover .b-calendar-cell.b-nonworking-day{
  background-color:#fafbfb;
}
.b-cal-agenda-grid-row .b-calendar-cell{
  flex:1 1 100%;
  padding:1.5em 0;
  align-items:flex-start;
  color:#606060;
}
.b-cal-agenda-grid-row .b-calendar-cell.b-nonworking-day{
  background-color:#fafbfb;
}
.b-cal-agenda-grid-row .b-calendar-cell:focus-within{
  outline:0 none !important;
}

.b-cal-agenda-event-row{
  flex:1;
  display:flex;
  flex-flow:row nowrap;
  align-self:stretch;
}
.b-cal-agenda-event-row:not(:last-child){
  margin-bottom:var(--event-row-spacing);
}

.b-grid-cell.b-calendar-cell{
  font-weight:inherit;
}
.b-agendaview-with-dayselector .b-grid-cell.b-calendar-cell{
  padding-inline-start:1.5em;
}

.b-cal-agenda-date{
  display:flex;
  flex-flow:row nowrap;
  flex:0 0 14em;
  padding:0.2em 1em 0 1em;
  align-items:center;
  height:2.6em;
}
.b-agendaview-with-dayselector .b-cal-agenda-date{
  display:none;
}

.b-cal-agenda-date-date-number{
  margin:0 0.1em 0 0;
  font-size:3em;
  display:flex;
  justify-content:center;
  align-items:center;
  cursor:pointer;
  width:1.5em;
  height:1.5em;
  border-radius:50%;
}
.b-cal-agenda-date-date-number:hover{
  background-color:#f0f0f0;
}
.b-today .b-cal-agenda-date-date-number{
  background-color:#5fa2dd;
  color:#fff;
}

.b-cal-agenda-header{
  flex:1;
  display:flex;
  flex-flow:row nowrap;
  justify-content:flex-start;
  padding:0.5em 0 0.5em 0.5em;
}

.b-cal-agenda-header-date{
  width:14em;
}

.b-cal-agenda-header-time{
  width:12em;
}

.b-resourcechipview{
  display:flex;
  flex-flow:row wrap;
}
.b-resourcechipview .b-chip{
  gap:0.5em;
  border-radius:2.5em;
  padding-inline-start:0.3em;
  white-space:nowrap;
}

@media print{
  .b-cal-widget-settings-button{
    display:none;
  }
}
.b-eventlist .b-grid-panel-body{
  background-color:#fff;
  position:relative;
}
.b-eventlist .b-grid-panel-body .b-cal-widget-settings-button{
  position:absolute;
  font-size:110%;
  border-radius:50%;
  left:auto;
  right:0;
  margin-top:0.75em;
  margin-inline-end:0.75em;
  z-index:100;
  width:3em;
  height:3em;
}
.b-rtl .b-eventlist .b-grid-panel-body .b-cal-widget-settings-button{
  right:auto;
  left:0;
}
.b-eventlist .b-grid-panel-body.b-has-scrollbar .b-cal-widget-settings-button{
  margin-inline-end:1.75em;
}

.b-calendarevents-cell{
  padding:1em;
  gap:1em;
  flex-wrap:wrap;
  align-items:flex-start;
}
.b-calendarevents-cell .b-cal-event-resource-avatars{
  flex-basis:30%;
  min-width:2.2em;
  flex-direction:column;
  gap:0.3em;
}

.b-cal-event-resource-avatar-row{
  display:flex;
  flex-flow:row nowrap;
  gap:1em;
  align-items:center;
}

.b-cal-event-resource-avatar-desc{
  display:flex;
  flex-flow:column;
  gap:0.3em;
}

.b-cal-event-column-event-desc{
  margin-inline-start:auto;
  display:flex;
  flex-direction:column !important;
  flex-flow:row nowrap;
  height:2.2em;
  justify-content:center;
}

.b-cal-event-column-times{
  display:flex;
  flex-direction:column;
}

.b-event-column-resource-role{
  font-size:80%;
  font-weight:300;
}

.b-cal-eventlist-event-time{
  display:flex;
  min-height:2.2em;
  align-items:center;
}

.b-cal-event-column-event-name{
  font-weight:bold;
}

.b-calendardatepicker .b-show-events-dots .b-calendar-cell.b-selected-date:not(.b-in-range) > .b-datepicker-cell-inner{
  color:inherit;
  background-color:rgba(95, 162, 221, 0.2);
}
.b-calendarrow{
  display:flex;
  flex-flow:column nowrap;
  align-items:stretch;
  overflow:hidden;
  flex:1 0 0%;
  background-color:#fff;
}
.b-calendarrow.b-animating:not(.b-dayview-initializing) .b-calendarrow-cell-container{
  transition:height 300ms;
}
.b-calendarrow .b-calendarrow-header, .b-calendarrow .b-calendarrow-body{
  flex:0 0 auto;
  display:flex;
  flex-flow:row nowrap;
}
.b-calendarrow .b-calendarrow-body{
  flex:1 1 0%;
  overflow:hidden;
}
.b-calendarrow .b-calendarrow-body.b-zero-height{
  max-height:0px;
}
.b-calendarrow .b-calendarrow-body:not(.b-zero-height){
  border-top:1px solid #ddd;
}
.b-calendarrow .b-calendarrow-header-container, .b-calendarrow .b-calendarrow-cell-container{
  flex:1 0 0%;
  display:flex;
  flex-flow:row nowrap;
  align-items:stretch;
  overflow:hidden;
}
.b-calendarrow .b-calendarrow-cell-container{
  position:relative;
  overflow:var(--cell-container-overflow, hidden);
}
.b-calendarrow .b-cal-cell-header{
  padding:1.2em 0 0.2em;
  justify-content:center;
  align-items:center;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
  background-color:#fff;
  cursor:pointer;
}
.b-calendarrow .b-cal-cell-header .b-week-num{
  display:none;
}
.b-calendarrow .b-cal-cell-header:hover .b-day-name-date{
  background-color:#f0f0f0;
}
.b-draggable-started .b-calendarrow .b-cal-cell-header:hover .b-day-name-date{
  background-color:initial;
}
.b-calendarrow .b-cal-cell-header .b-day-name-day{
  font-size:0.8em;
  margin-bottom:0.2em;
}
.b-calendarrow .b-cal-cell-header .b-day-name-day.b-day-name-short{
  font-size:1.1em;
}
.b-calendarrow .b-cal-cell-header .b-day-name-date{
  font-size:1.8em;
  border-radius:50%;
  width:1.8em;
  height:1.8em;
  display:flex;
  align-items:center;
  justify-content:center;
  margin:0.1em 0 0.2em 0;
  transition:background-color 0.4s;
}
.b-no-transitions .b-calendarrow .b-cal-cell-header .b-day-name-date{
  transition:none;
}
.b-calendarrow .b-cal-cell-header.b-weekend .b-day-name-part{
  color:#ef9a9a;
}
.b-calendarrow .b-cal-cell-header.b-current-date .b-day-name-date{
  color:#fff;
  background-color:#64b5f6;
}
.b-calendarrow .b-cal-cell-header.b-current-date:hover .b-day-name-date{
  background-color:rgba(100, 181, 246, 0.15);
  border-color:transparent;
}
.b-calendarrow .b-cal-cell-header.b-today .b-day-name-date{
  background-color:#5fa2dd;
  color:#fff;
}
.b-calendarrow .b-cal-cell-header.b-today:hover .b-day-name-date{
  background-color:#2c84d0;
}
.b-calendarrow.b-shifted-day .b-day-name-date{
  width:3em;
}
.b-calendarrow.b-hide-allday-header .b-cal-cell-header:not(:last-child){
  border-inline-end:1px solid #ddd;
}
.b-calendarrow .b-cal-cell-header, .b-calendarrow .b-calendar-cell{
  flex:1 1 100%;
  min-width:var(--min-day-width, 0);
  color:#606060;
  display:flex;
  flex-flow:column nowrap;
}
.b-calendarrow .b-calendar-cell{
  overflow:visible;
}
.b-calendarrow .b-calendar-cell.b-nonworking-day{
  background-color:rgba(243, 244, 245, 0.4);
}
.b-calendarrow .b-calendar-cell.b-weekend{
  color:#606060;
}
.b-calendarrow .b-calendar-cell.b-today .b-day-num{
  border-radius:50%;
  background-color:#5fa2dd;
  color:#fff;
  font-weight:bold;
}
.b-calendarrow .b-calendar-cell.b-nonworking-day .b-cal-event-bar-container{
  background-color:transparent;
}
.b-calendarrow .b-calendar-cell:not(:last-child){
  border-inline-end:1px solid #ddd;
}
.b-calendarrow .b-cal-event-bar-container{
  flex:1 1 100%;
}
.b-calendarrow.b-responsive-small .b-cal-cell-header{
  padding-block-start:0.4em;
}
.b-calendarrow.b-responsive-small .b-cal-cell-header .b-day-name-date{
  font-size:1.5em;
}
.b-calendarrow.b-responsive-small .b-cal-cell-header .b-dayname-date{
  flex-direction:row;
  justify-content:center;
}
.b-calendarrow.b-responsive-small .b-cal-cell-header .b-dayname-date > *{
  font-size:1.5em;
}
.b-dayview{
  --dayview-border-color:#ddd;
  --dayview-background-color:#fff;
  --panel-background-color:#fff;
  --dayview-hour-line-color:#ddd;
  --dayview-half-hour-line-color:#f0f0f0;
  --dayview-outside-core-hours-color:rgba(135, 135, 135, 0.1);
  --dayview-body-background-color:rgba(255, 255, 255, 0.9);
  --dayview-body-hover-mask-color:rgba(100, 100, 100, 0.1);
  --dayview-body-selected-mask-color:rgba(50, 50, 50, 0.1);
}
.b-dayview.b-dayview-with-dayselector.b-has-allday-events .b-calendarrow-body{
  border-top-width:0;
}
.b-dayview.b-dayview-with-dayselector .b-dayview-content .b-calendarrow-header{
  display:none;
}
.b-dayview .b-cal-event-desc{
  line-height:1.2;
}
.b-dayview .b-event-action-buttons{
  position:absolute;
  inset-inline-end:0.25em;
  inset-block-start:0;
  display:flex;
  flex-flow:row wrap;
}
.b-dayview .b-event-action-buttons .b-tool{
  font-size:calc(150% - 25% * min(var(--range-magnitude), 2));
}

.b-daycellcollecter .b-dayview-content{
  --current-time-position:calc(var(--current-time-seconds) * var(--second-height) - 1px);
  display:flex;
  align-items:stretch;
  padding:0;
  flex:1 1 0%;
  overflow:hidden;
  background-color:#fff;
  color:#606060;
  outline:none;
}
.b-daycellcollecter .b-dayview-content .b-virtual-scrollers{
  padding-inline-start:var(--time-axis-width);
  background-color:#fff;
}
.b-daycellcollecter .b-dayview-content .b-virtual-scrollers .b-virtual-scroller{
  border-inline-start:1px solid #ddd;
  flex:1 1 0%;
}
.b-daycellcollecter .b-dayview-content .b-virtual-scrollers .b-virtual-scroller .b-scroller-stretcher{
  position:relative;
}
.b-daycellcollecter .b-dayview-content .b-dayview-day-detail{
  z-index:0;
  display:flex;
  position:relative;
  padding-inline-end:var(--dayview-cell-gutter);
}
.b-daycellcollecter .b-dayview-content .b-dayview-day-detail.b-dayview-inset-before .b-dayview-inset::before{
  content:" ";
  left:0;
}
.b-rtl .b-daycellcollecter .b-dayview-content .b-dayview-day-detail.b-dayview-inset-before .b-dayview-inset::before{
  left:unset;
  right:0;
}
.b-daycellcollecter .b-dayview-content .b-dayview-day-detail.b-dayview-inset-before .b-dayview-event-container{
  margin-inline-start:calc(var(--dayview-cell-inset-size));
}
.b-daycellcollecter .b-dayview-content .b-dayview-day-detail.b-dayview-inset-after .b-dayview-inset::after{
  content:" ";
  right:0;
}
.b-rtl .b-daycellcollecter .b-dayview-content .b-dayview-day-detail.b-dayview-inset-after .b-dayview-inset::after{
  left:0;
  right:unset;
}
.b-daycellcollecter .b-dayview-content .b-dayview-day-detail.b-dayview-inset-after .b-dayview-event-container{
  margin-inline-end:calc(var(--dayview-cell-inset-size));
}
.b-daycellcollecter .b-dayview-content .b-dayview-inset{
  position:absolute;
  width:calc(100% - var(--dayview-cell-gutter));
  top:0;
  bottom:0;
}
.b-daycellcollecter .b-dayview-content .b-dayview-inset::after, .b-daycellcollecter .b-dayview-content .b-dayview-inset::before{
  background-color:rgba(244, 245, 246, 0.4);
  position:absolute;
  width:calc(var(--dayview-cell-inset-size));
  top:0;
  bottom:0;
  z-index:-1;
}
.b-daycellcollecter .b-dayview-content .b-dayview-event-container{
  flex:1;
  position:relative;
}
.b-daycellcollecter .b-dayview-content .b-dayview-inset-after{
  width:calc(var(--dayview-cell-inset-after) * var(--dayview-cell-inset-after-enabled));
}
.b-daycellcollecter .b-dayview-content .b-cal-event-wrap:is(.b-starts-above, .b-ends-below):before, .b-daycellcollecter .b-dayview-content .b-cal-event-wrap:is(.b-starts-above, .b-ends-below):after{
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  transform:translate(-50%, -2px);
  margin-inline-start:50%;
  position:absolute;
  font-size:80%;
  opacity:0.7;
  z-index:1;
  pointer-events:none;
}
.b-rtl .b-daycellcollecter .b-dayview-content .b-cal-event-wrap:is(.b-starts-above, .b-ends-below):before, .b-rtl .b-daycellcollecter .b-dayview-content .b-cal-event-wrap:is(.b-starts-above, .b-ends-below):after{
  transform:translate(50%, -2px);
}
.b-daycellcollecter .b-dayview-content .b-cal-event-wrap.b-starts-above:before{
  content:"\f106";
  top:0;
}
.b-daycellcollecter .b-dayview-content .b-cal-event-wrap.b-ends-below:after{
  content:"\f107";
  bottom:0;
}
.b-daycellcollecter .b-dayview-content .b-cal-event-desc-complex{
  display:flex;
  flex-direction:column;
  flex:1;
  align-items:normal;
}

.b-dayview-allday-row{
  display:flex;
  flex-flow:row nowrap;
  max-height:50%;
  box-shadow:0px 3px 3px rgba(221, 221, 221, 0.3);
  z-index:2;
}

.b-dayview-day-detail .b-cal-event-wrap{
  min-height:var(--dayview-min-event-height);
}
.b-dayview-day-detail .b-cal-event-wrap.b-cal-tentative-event{
  min-height:10px;
}

.b-dayview-allday-row-start{
  display:flex;
  flex-flow:column nowrap;
  align-items:center;
  justify-content:flex-end;
  flex:0 0 var(--time-axis-width);
  min-width:var(--time-axis-width);
}
.b-dayview-allday-row-start .b-dayview-allday-text{
  flex:1 1 auto;
  display:none;
  flex-flow:column nowrap;
  justify-content:center;
  font-size:0.7em;
  color:#b0b0b0;
}
.b-dayview-allday-row-start .b-expand-allday-button{
  display:none;
}
.b-dayview-allday-row-start .b-expand-allday-button :before{
  line-height:inherit;
}
.b-dayview-allday-row-start.b-has-cell-overflow:not(.b-dayview-allday-autoheight){
  padding:0.5em 0;
  cursor:pointer;
}
.b-dayview-allday-row-start.b-has-cell-overflow:not(.b-dayview-allday-autoheight) .b-expand-allday-button{
  transition:transform 0.3s;
  display:flex;
  height:1.5em;
  width:1.5em;
  justify-content:center;
  align-items:center;
  border-radius:50%;
  cursor:inherit;
  border:0 none;
  background-color:transparent;
  color:#b0b0b0;
}
.b-dayview-allday-row-start.b-has-cell-overflow:not(.b-dayview-allday-autoheight) .b-expand-allday-button:focus{
  outline:none;
  background-color:#e8e8e8;
}
.b-dayview-allday-row-start.b-has-cell-overflow:not(.b-dayview-allday-autoheight).b-expanded .b-expand-allday-button{
  transform:rotate(-180deg);
}

.b-dayview-schedule-container{
  border-inline-start:1px solid #ddd;
  flex:1 0 0%;
  contain:style;
}

.b-dayview-day-content{
  --tick-height:var(--half-hour-height);
  --tick-background:linear-gradient(to bottom,
          transparent,
          transparent calc(var(--hour-height) - 1px),
          var(--dayview-hour-line-color) 1px);
  --subtick-background:repeating-linear-gradient(to bottom,
          transparent,
          transparent calc(var(--tick-height)),
          var(--dayview-half-hour-line-color) var(--tick-height),
          transparent calc(var(--tick-height) + 1px));
  --dashed-subtick-background:conic-gradient(at 50% 1px, transparent 75%, var(--dayview-hour-line-color) 0turn);
  display:flex;
  flex-flow:row nowrap;
  flex:1 1 0;
  border-top:1px solid #ddd;
  align-items:flex-start;
  outline:none;
  position:relative;
  z-index:1;
}
.b-dayview-day-content:not(.b-hide-current-time) .b-calendar-cell.b-today::before{
  content:"";
  position:absolute;
  top:var(--current-time-position);
  border-top:2px solid #ef9a9a;
  width:calc(100% + 1px);
  inset-inline-start:-1px;
}
.b-dayview-day-content:not(.b-hide-current-time) .b-calendar-cell.b-today:after{
  content:"";
  top:calc(var(--current-time-position) + 2px);
  height:10px;
  width:10px;
  border-radius:50%;
  position:absolute;
  background-color:#ef9a9a;
  margin-top:-6px;
  margin-inline-start:-5px;
}
.b-dayview-day-content.b-show-current-time .b-timeaxis-container{
  overflow:visible;
  z-index:1;
}
.b-dayview-day-content.b-show-current-time .b-calendar-cell:first-of-type::after{
  display:none;
}
.b-dayview-day-content.b-show-time-full-width:has(.b-today) .b-calendar-cell::before{
  content:"";
  position:absolute;
  border-top:2px solid #ef9a9a;
  width:calc(100% + 1px);
  top:var(--current-time-position);
  inset-inline-start:-1px;
}
.b-dayview-day-content.b-show-time-on-top .b-calendar-cell::before, .b-dayview-day-content.b-show-time-on-top .b-calendar-cell::after{
  z-index:3;
}
.b-dayview-day-content .b-time-axis-current-time{
  position:absolute;
  pointer-events:none;
  width:100%;
  transform:translateY(calc(var(--current-time-position) - 50% + 1px));
  z-index:1;
}
.b-dayview-day-content .b-time-axis-current-time .b-current-time-text{
  border-radius:0.7em;
  background-color:#ef9a9a;
  display:grid;
  place-content:center;
  line-height:20px;
}
.b-dayview-day-content .b-time-axis-current-time .b-current-time-text::after{
  color:#fff;
  font-size:0.7em;
  content:var(--current-time, "");
}
.b-dayview-day-content .b-time-axis-current-time::after{
  content:"";
  position:absolute;
  inset-inline-start:100%;
  width:1px;
  border-top:2px solid #ef9a9a;
  top:calc(50% - 1px);
}
.b-dayview-day-content:not(.b-show-current-time) .b-time-axis-current-time{
  visibility:hidden;
}
.b-dayview-day-content.b-hide-current-time .b-calendar-cell.b-today::before, .b-dayview-day-content.b-hide-current-time .b-calendar-cell.b-today::after, .b-dayview-day-content.b-custom-current-time .b-calendar-cell.b-today::before, .b-dayview-day-content.b-custom-current-time .b-calendar-cell.b-today::after{
  display:none;
}

.b-dayview-day-container{
  flex:1 0 0%;
  border-inline-start:1px solid #ddd;
  display:flex;
  flex-flow:row nowrap;
  align-items:stretch;
  min-height:max(100%, var(--day-height));
  overscroll-behavior:contain auto;
  contain:layout style;
  background-image:var(--subtick-background), var(--tick-background);
  background-size:100% var(--hour-height);
  background-position-y:var(--day-start-offset);
  clip-path:polygon(0px 0px, 100% 0px, 100% var(--day-height), 0px var(--day-height));
  background-repeat:repeat-y;
}
.b-dayview-day-container.b-dashed-subticks{
  background-image:var(--tick-background);
}
.b-dayview-day-container.b-dashed-subticks::before{
  content:"";
  position:absolute;
  inset:0;
  background-image:var(--tick-background);
  background:var(--dashed-subtick-background);
  background-size:10px calc(var(--tick-height) + 1px);
  background-position:1px var(--tick-height);
}
.b-dayview-day-container .b-overflow{
  opacity:0;
}
.b-dayview-day-container .b-cal-event-body{
  transition:background-color 0.1s;
}
.b-dayview-day-container .b-cal-event-wrap.b-starts-above .b-event-header{
  visibility:hidden;
}
.b-dayview-day-container .b-calendar-cell{
  flex:1 1 100%;
  height:var(--day-height);
  min-width:var(--min-day-width);
  position:relative;
  overflow:visible;
}
.b-dayview-day-container .b-calendar-cell:not(:last-of-type){
  border-inline-end:1px solid #ddd;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap{
  position:absolute;
  z-index:2;
  left:0;
  width:100%;
  transition:left 0.1s, right 0.1s, width 0.1s, opacity 0.1s;
}
.b-rtl .b-dayview-day-container .b-calendar-cell .b-cal-tentative-event,
.b-rtl .b-dayview-day-container .b-calendar-cell .b-cal-event-wrap{
  left:auto;
  right:0;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-editing,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-editing{
  left:0 !important;
  width:calc(100% + var(--dayview-cell-gutter)) !important;
  z-index:3;
  opacity:1;
}
.b-rtl .b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-editing,
.b-rtl .b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-editing{
  left:auto !important;
  right:0 !important;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-cal-in-cluster,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-cal-in-cluster{
  box-shadow:rgba(170, 170, 170, 0.2509803922) -2px 1px 3px, rgba(170, 170, 170, 0.2509803922) 0 -1px 3px;
}
.b-multidayview .b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-cal-in-cluster.b-cal-event-reveal,
.b-multidayview .b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-cal-in-cluster.b-cal-event-reveal{
  left:0 !important;
  width:calc(100% + var(--dayview-cell-gutter)) !important;
  z-index:3;
  opacity:1;
}
.b-rtl .b-multidayview .b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-cal-in-cluster.b-cal-event-reveal,
.b-rtl .b-multidayview .b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-cal-in-cluster.b-cal-event-reveal{
  left:auto !important;
  right:0 !important;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event .b-event-header,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event .b-event-header{
  margin:0 0 0 auto;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event .b-event-header .b-icon,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event .b-event-header .b-icon{
  margin-inline-start:auto;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event .b-cal-event-body,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event .b-cal-event-body{
  padding-block:0;
  align-items:center;
  flex-flow:row-reverse nowrap;
  justify-content:flex-end;
  gap:0.2em;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event .b-cal-event-body .b-cal-event-desc,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event .b-cal-event-body .b-cal-event-desc{
  font-size:0.8em;
  white-space:nowrap;
  flex:0 1 auto;
  padding-block:0;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event .b-event-time,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event .b-event-time{
  text-transform:lowercase;
  line-height:1.2;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event .b-event-time:not(:last-child),
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event .b-event-time:not(:last-child){
  display:none;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event .b-cal-event-footer, .b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event .b-cal-event-resource-avatars,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event .b-cal-event-footer,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event .b-cal-event-resource-avatars{
  display:none;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event .b-event-action-buttons,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event .b-event-action-buttons{
  inset-block-start:calc(50% - 0.5em);
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event .b-event-action-buttons .b-tool,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event .b-event-action-buttons .b-tool{
  font-size:65%;
  height:1.4em;
  width:3.8em;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event.b-milestone,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event.b-milestone{
  clip-path:polygon(0 0, 100% 0, calc(100% - 0.5em) 100%, 0.5em 100%);
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event.b-short-event.b-milestone .b-cal-event-body,
.b-dayview-day-container .b-calendar-cell .b-cal-event-wrap.b-short-event.b-milestone .b-cal-event-body{
  margin:0.25em 0 0 0;
  padding:0 0.5em 0 0.5em;
}
.b-dayview-day-container .b-calendar-cell .b-cal-tentative-event{
  z-index:20;
}
.b-dayview-day-container .b-calendar-cell .b-cal-event{
  align-items:stretch;
  background-color:var(--cal-event-color);
  color:#606060;
  border-radius:0;
  position:relative;
}
.b-dayview-day-container .b-calendar-cell .b-cal-event .b-cal-event-resource-avatars{
  display:grid;
  column-gap:0;
  grid-template-columns:repeat(auto-fill, minmax(1.4em, 1fr));
  padding-inline-end:0.6em;
  padding-block:0.3em;
}
.b-dayview-day-container .b-calendar-cell .b-cal-event .b-cal-event-resource-avatars .b-resource-avatar{
  width:2em;
  height:2em;
  transition:transform 0.2s;
}
.b-dayview-day-container .b-calendar-cell .b-cal-event .b-cal-event-resource-avatars .b-resource-avatar:hover{
  transform:scale(2);
  z-index:1;
}
.b-dayview-day-container .b-calendar-cell .b-cal-event-body{
  padding:0.3em 0.4em 0;
  flex:1 1 0%;
  margin:0 0 0 0.25em;
  background-color:var(--dayview-body-background-color);
  white-space:normal;
  display:flex;
  flex-direction:column;
}
.b-rtl .b-dayview-day-container .b-calendar-cell .b-cal-event-body{
  margin:0 0.25em 0 0;
}
.b-dayview-day-container .b-calendar-cell.b-nonworking-day{
  background-color:rgba(243, 244, 245, 0.4);
}
.b-dayview-day-container .b-event-header{
  display:flex;
  align-items:center;
  white-space:nowrap;
  gap:0.3em;
  font-size:0.7em;
}
.b-dayview-day-container .b-event-header .b-event-time{
  flex:1;
}
.b-dayview-day-container .b-cal-event-wrap:hover .b-cal-event-body{
  background-color:rgba(255, 255, 255, 0.7);
}
.b-dayview-day-container .b-cal-event-wrap.b-selected:not(.b-dragging-item):hover .b-cal-event-body,
.b-dayview-day-container .b-cal-event-wrap.b-selected:not(.b-dragging-item) .b-cal-event-body{
  background-color:rgba(255, 255, 255, 0.6);
}
.b-dayview-day-container .b-cal-event-wrap.b-custom-body-color:hover .b-cal-event-body{
  background:linear-gradient(var(--dayview-body-hover-mask-color), var(--dayview-body-hover-mask-color)) var(--dayview-body-background-color);
}
.b-dayview-day-container .b-cal-event-wrap.b-custom-body-color.b-selected:not(.b-dragging-item):hover .b-cal-event-body, .b-dayview-day-container .b-cal-event-wrap.b-custom-body-color.b-selected:not(.b-dragging-item) .b-cal-event-body, .b-dayview-day-container .b-cal-event-wrap.b-custom-body-color.b-cal-tentative-event .b-cal-event-body{
  background:linear-gradient(var(--dayview-body-selected-mask-color), var(--dayview-body-selected-mask-color)) var(--dayview-body-background-color);
}

.b-dayview.b-no-transitions .b-cal-event-wrap{
  transition:none !important;
}

.b-timeaxis-container{
  display:flex;
  flex-flow:column nowrap;
  overflow:hidden;
  background-position-x:100%;
  background-repeat:repeat-y;
  flex:0 0 var(--time-axis-width);
  min-width:var(--time-axis-width);
  height:var(--day-height);
  background-image:var(--subtick-background), var(--tick-background);
  background-size:0.5em var(--hour-height);
  background-position-y:var(--day-start-offset);
  position:relative;
}
.b-rtl .b-timeaxis-container{
  background-position-x:0%;
}
.b-timeaxis-container .b-dayview-timeaxis-background{
  pointer-events:none;
  position:absolute;
  inset:0;
}

.b-dayview-timeaxis-tick{
  opacity:0;
  transition:opacity 0.4s;
  max-height:calc(var(--leaf-tick-height) + 1px);
  flex:1 0 0%;
  display:flex;
  flex-direction:column;
  justify-content:flex-end;
  position:relative;
  top:1ex;
}
.b-fit-hours .b-dayview-timeaxis-tick{
  transition:none;
}

.b-dayview-hour-tick{
  color:#7d7d7d;
  opacity:1;
}
.b-dayview-hour-tick.b-dayview-start-hour{
  position:absolute;
  max-height:fit-content;
  top:-3px;
}

.b-dayview-timeaxis-time{
  display:flex;
  flex-flow:column nowrap;
  flex:0 0 var(--hour-height);
  align-items:flex-end;
  font-size:0.7em;
  color:#b0b0b0;
  white-space:nowrap;
  position:relative;
  padding-inline-end:1em;
  pointer-events:none;
}
.b-dayview-timeaxis-time:last-child .b-dayview-hour-tick{
  display:none;
}

.b-dayview-hourheight-level-1 .b-dayview-tick-level-1{
  opacity:1;
}

.b-dayview-hourheight-level-2{
  --tick-height:var(--fifteen-minute-height);
}
.b-dayview-hourheight-level-2 .b-dayview-tick-level-2{
  opacity:1;
}

.b-dayview-hourheight-level-3{
  --tick-height:var(--ten-minute-height);
}
.b-dayview-hourheight-level-3 .b-dayview-tick-level-3{
  opacity:1;
}

.b-dayview-hourheight-level-4{
  --tick-height:var(--five-minute-height);
}
.b-dayview-hourheight-level-4 .b-dayview-tick-level-4{
  opacity:1;
}

.b-six-minute-ticks .b-dayview-hourheight-level-2{
  --tick-height:var(--twelve-minute-height);
}
.b-six-minute-ticks .b-dayview-hourheight-level-3{
  --tick-height:var(--six-minute-height);
}

.b-day-column-header{
  display:none;
}
.b-has-day-header .b-day-column-header{
  display:flex;
  flex-flow:row nowrap;
  border-top:1px solid #ddd;
  margin-inline-start:var(--time-axis-width);
}

.b-day-column-header-cell{
  flex:1 0 0;
  padding-inline-start:0.5em;
  display:flex;
  border-inline-start:1px solid #ddd;
  overflow:hidden;
  text-wrap:nowrap;
}

.b-dayselector .b-calendarrow-header-container{
  border-bottom:1px solid #ddd;
}
.b-dayselector .b-calendarrow-header-container .b-cal-cell-header:not(:hover) div.b-day-name-date{
  background-color:inherit;
  color:inherit;
}
.b-dayselector .b-calendarrow-header-container .b-cal-cell-header.b-selected-date div.b-day-name-date{
  background-color:#5fa2dd;
  color:#fff;
}
.b-dayselector .b-calendarrow-body{
  display:none;
}

.b-dayview-with-dayselector .b-calendarrow-header-container, .b-weekview-with-dayselector .b-calendarrow-header-container{
  border-inline-start:1px solid #ddd;
  margin-inline-start:var(--time-axis-width);
}
.b-dayview-with-dayselector:not(.b-has-allday-events) .b-calendarrow-header-container, .b-weekview-with-dayselector:not(.b-has-allday-events) .b-calendarrow-header-container{
  border-bottom-width:0;
}
.b-visible-scrollbar .b-monthview .b-show-yscroll-padding > .b-yscroll-pad{
  visibility:hidden;
}
.b-monthview.b-hide-othermonth-cells .b-calendar-row[data-row-index="0"] .b-calendar-cell[data-date$="-01"]:not([data-column-index="0"]){
  border-inline-start:1px solid #ddd;
  margin-inline-start:-1px;
}
.b-monthview.b-disable-othermonth-cells .b-calendar-cell.b-other-month .b-cal-event-wrap:not(.b-overflow){
  opacity:1;
  pointer-events:all;
}
.b-monthview .b-calendar-row.b-calendar-weekdays{
  flex:0 0 auto;
}
.b-monthview .b-calendar-row.b-calendar-weekdays .b-week-number-cell{
  flex:0 0 2em;
}
.b-monthview .b-calendar-row.b-calendar-weekdays .b-calendar-day-header{
  flex:1;
  display:flex;
  justify-content:flex-end;
  color:#606060;
  padding-inline-end:0.5em;
  padding-bottom:0.25em;
}
.b-monthview .b-calendar-row.b-calendar-weekdays .b-calendar-day-header.b-weekend{
  color:#ef9a9a;
}
.b-monthview .b-week-number-cell, .b-monthview .b-calendar-cell{
  flex-direction:column;
  contain:size style;
}
.b-monthview .b-week-number-cell.b-other-month .b-day-num, .b-monthview .b-calendar-cell.b-other-month .b-day-num{
  color:#aaa;
}
.b-monthview .b-week-num, .b-monthview .b-day-num{
  font-size:0.9em;
  height:2em;
  width:2em;
  align-self:flex-end;
  display:flex;
  align-items:center;
  justify-content:center;
}
.b-monthview .b-calendar-cell .b-week-num{
  color:#777;
  margin-inline-end:auto;
}
.b-monthview .b-weeks-container{
  --flexed-row-height:max(calc(1 / var(--visible-week-count) * 100%), var(--min-row-height, 0px));
  flex:1 0 0%;
  border:1px solid #ddd;
}
.b-monthview .b-weeks-container .b-calendar-row{
  transition:flex-basis 0.5s;
  flex:0 0 var(--flexed-row-height);
  border-bottom:1px solid #ddd;
}
.b-monthview .b-weeks-container .b-calendar-row:last-child{
  border-bottom:0 none;
}
.b-monthview .b-weeks-container .b-calendar-row.b-shrinkwrapped{
  overflow:hidden;
}
.b-monthview .b-weeks-container .b-calendar-row.b-shrinkwrapped:not(.b-has-overflow) .b-cal-cell-overflow{
  display:none;
}
.b-monthview .b-weeks-container .b-calendar-row.b-shrinkwrapped.b-flexing .b-cal-cell-overflow{
  display:initial;
}
.b-monthview .b-weeks-container .b-calendar-row.b-empty-row{
  flex:1 1 var(--flexed-row-height);
  min-height:max(2em, var(--min-row-height, 0px));
}
.b-monthview .b-weeks-container .b-calendar-row .b-week-number-cell, .b-monthview .b-weeks-container .b-calendar-row .b-calendar-cell{
  flex:1;
  color:#606060;
  border-inline-end:1px solid #ddd;
  overflow:hidden;
}
.b-monthview .b-weeks-container .b-calendar-row .b-week-number-cell:last-child, .b-monthview .b-weeks-container .b-calendar-row .b-calendar-cell:last-child{
  border-inline-end:0 none;
}
.b-monthview .b-weeks-container .b-calendar-row .b-week-number-cell.b-weekend, .b-monthview .b-weeks-container .b-calendar-row .b-calendar-cell.b-weekend{
  color:#606060;
}
.b-monthview .b-weeks-container .b-calendar-row .b-week-number-cell.b-today .b-day-num, .b-monthview .b-weeks-container .b-calendar-row .b-calendar-cell.b-today .b-day-num{
  border-radius:50%;
  background-color:#5fa2dd;
  color:#fff;
  font-weight:bold;
  font-size:85%;
}
.b-monthview .b-weeks-container .b-calendar-row .b-week-number-cell.b-nonworking-day, .b-monthview .b-weeks-container .b-calendar-row .b-calendar-cell.b-nonworking-day{
  background-color:rgba(243, 244, 245, 0.4);
}
.b-monthview .b-weeks-container .b-calendar-row .b-week-number-cell.b-nonworking-day.b-dynamic-nonworking-day, .b-monthview .b-weeks-container .b-calendar-row .b-calendar-cell.b-nonworking-day.b-dynamic-nonworking-day{
  display:flex !important;
}
.b-monthview .b-weeks-container .b-calendar-row .b-week-number-cell{
  padding-top:5px;
  text-align:center;
  cursor:pointer;
  flex:0 0 auto;
  min-width:2em;
  color:#777;
  flex-flow:column nowrap;
  justify-content:space-between;
}
.b-monthview .b-weeks-container .b-calendar-row .b-week-number-cell:hover{
  background-color:#f0f0f0;
  border-radius:3px;
}
.b-monthview .b-weeks-container.b-measuring-container-height .b-calendar-row{
  transition:none !important;
  flex:0 0 var(--flexed-row-height) !important;
}
.b-monthview .b-weeks-container .b-calendar-days{
  position:relative;
}
.b-monthview .b-hide-nonworking-days .b-weeks-container .b-last-working-day{
  border-inline-end:0 none;
}
.b-monthview .b-cal-cell-header{
  display:flex;
  flex:0 0 auto;
  color:#3c4043;
  padding:1px 0.2em 0.1em 0.2em;
  max-height:1.8em;
  align-items:center;
  justify-content:center;
}
.b-monthview .b-cal-cell-header .b-day-name{
  height:1.8em;
  display:flex;
  align-items:center;
  white-space:nowrap;
  text-overflow:ellipsis;
  overflow:hidden;
}
.b-monthview .b-cal-cell-header .b-day-num{
  height:2em;
  z-index:10;
  margin-inline-start:auto;
}
.b-monthview.day-number-center .b-cal-cell-header{
  justify-content:center;
}
.b-monthview.b-show-week-column .b-week-num{
  display:none;
}
.b-monthview.b-show-week-column .b-cal-cell-header{
  cursor:pointer;
}
.b-monthview.b-show-week-column .b-cal-cell-header:hover{
  background-color:#f0f0f0;
  border-radius:3px;
}
.b-monthview:not(.b-show-week-column) .b-day-name{
  cursor:pointer;
}
.b-monthview:not(.b-show-week-column) .b-week-num:hover, .b-monthview:not(.b-show-week-column) .b-day-num:hover{
  cursor:pointer;
  background-color:#f0f0f0;
  border-radius:50%;
}
.b-monthview .b-monthview-content{
  contain:strict;
  background-color:#fff;
  flex-basis:0%;
  justify-content:flex-start;
  padding:0.6em 0 0 0;
}
.b-monthview .b-cal-event-bar-container,
.b-monthview .b-cal-cell-overflow{
  font-size:0.9em;
}

.b-cal-event-wrap{
  display:flex;
  overflow:hidden;
}
.b-cal-event-wrap.b-readonly{
  filter:grayscale(75%);
  transition:filter 0.5s;
}
.b-grid-row .b-cal-event-wrap{
  transition:opacity 0.1s;
}
.b-draggable-started .b-cal-event-wrap:not(.b-dragging-item){
  pointer-events:none;
}
.b-draggable-started .b-cal-event-wrap.b-dragging-item{
  opacity:0.5;
  outline:none;
  pointer-events:none;
}
.b-cal-event-wrap:hover{
  cursor:pointer;
}
.b-cal-event-wrap.b-intraday:not(.b-solid-bar) .b-cal-event .b-cal-event-body{
  --cal-event-color:#606060;
}
.b-cal-event-wrap.b-intraday:not(.b-solid-bar) .b-cal-event:hover{
  background:linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)) currentColor;
}
.b-using-keyboard .b-cal-event-wrap.b-active{
  outline:2px #94ccf9 solid;
  outline-offset:1px;
}
.b-cal-event-wrap.b-active:not(.b-dragging-item, .b-cut-item){
  outline:none;
  opacity:1 !important;
  z-index:10 !important;
}
.b-offset-continues-past .b-cal-event-wrap.b-continues-past{
  margin-inline-start:var(--arrow-margin);
}
.b-cal-event-wrap.b-continues-past:not(.b-rtl) .b-cal-event{
  padding-left:calc(var(--arrow-width) + 0.3em);
  clip-path:polygon(0 50%, var(--arrow-width) 0, 100% 0, 100% 100%, var(--arrow-width) 100%);
}
.b-cal-event-wrap.b-continues-past.b-rtl .b-cal-event{
  padding-right:calc(var(--arrow-width) + 0.3em);
  clip-path:polygon(0 0, calc(100% - var(--arrow-width)) 0, 100% 50%, calc(100% - var(--arrow-width)) 100%, 0 100%);
}
.b-cal-event-wrap.b-continues-future:not(.b-rtl) .b-cal-event{
  padding-right:calc(var(--arrow-width) + 0.3em);
  clip-path:polygon(0 0, calc(100% - var(--arrow-width)) 0, 100% 50%, calc(100% - var(--arrow-width)) 100%, 0 100%);
}
.b-cal-event-wrap.b-continues-future.b-rtl .b-cal-event{
  padding-left:calc(var(--arrow-width) + 0.3em);
  clip-path:polygon(0 50%, var(--arrow-width) 0, 100% 0, 100% 100%, var(--arrow-width) 100%);
}
.b-cal-event-wrap.b-continues-past.b-continues-future .b-cal-event{
  clip-path:polygon(0 50%, var(--arrow-width) 0, calc(100% - var(--arrow-width)) 0, 100% 50%, calc(100% - var(--arrow-width)) 100%, var(--arrow-width) 100%);
}
.b-cal-event-wrap.b-focused .b-cal-event{
  background-color:green !important;
  color:#fff !important;
}

.b-cal-event{
  display:flex;
  border-radius:4px;
  align-items:center;
  flex:1 1 auto;
  overflow:hidden;
  color:#606060;
  line-height:1;
}
.b-cal-event .b-icon{
  flex-shrink:0;
  display:flex;
}

.b-cal-minimal-event-container{
  pointer-events:none;
  position:absolute;
  top:1.5em;
  width:calc((var(--event-count-dot-size) + 2px) * 4);
  display:flex;
  flex-wrap:wrap;
  justify-content:center;
  column-gap:2px;
  row-gap:1px;
  opacity:0.6;
}
.b-cal-minimal-event-container .b-cal-event-wrap{
  position:static !important;
  height:var(--event-count-dot-size);
  width:var(--event-count-dot-size);
  border-radius:50%;
  padding:0 !important;
}
.b-cal-minimal-event-container .b-cal-event{
  padding:0;
  background-color:var(--cal-event-color);
}

img.b-resource-avatar{
  border:1px solid var(--cal-event-color);
}

.b-solid-bar .b-cal-event{
  background-color:var(--cal-event-color);
}
.b-solid-bar .b-resource-avatar{
  border:1px solid #fff;
}

.b-cal-event-desc-complex{
  display:flex;
  align-items:center;
  gap:0.3em;
}

.b-cal-event-name{
  flex:1;
  overflow:hidden;
  text-overflow:ellipsis;
  line-height:1.4;
  min-height:1lh;
}

.b-cal-event-resource-avatars{
  gap:inherit;
  display:flex;
  flex-direction:row;
  flex-shrink:0;
}

:not(.b-calendar-cell)[data-resource-id]{
  cursor:pointer;
}

.b-cal-event-body{
  white-space:nowrap;
  overflow:hidden;
  line-height:1.4;
}
.b-cal-event-body .b-cal-event-desc{
  overflow:hidden;
  text-overflow:ellipsis;
  line-height:1.2;
  padding-block-end:1px;
}

.b-draggable-active .b-calendar-cell{
  -webkit-user-select:none;
  user-select:none;
}

.b-cal-event-list .b-panel-header .b-header-title{
  font-size:1em;
  cursor:pointer;
}

.b-notransiion .b-monthview .b-weeks-container .b-calendar-row.b-shrinkwrapped{
  transition:none;
}

.b-dayresourcecalendarrow .b-dayresourcecalendarrow-column{
  position:relative;
  display:flex;
  flex-flow:row;
  flex:1 0 auto;
}
.b-dayresourcecalendarrow .b-dayresourcecalendarrow-column:not(:last-child){
  border-inline-end:1px solid #c1c1c1;
}
.b-dayresourcecalendarrow .b-dayresourcecalendarrow-column .b-resourcecalendarrow-column-resource-cell{
  flex:1 1 100%;
  min-width:var(--min-resource-width);
  display:flex;
  flex-flow:column nowrap;
}
.b-dayresourcecalendarrow .b-dayresourcecalendarrow-column .b-resourcecalendarrow-column-resource-cell:not(:last-child){
  border-inline-end:1px solid #ddd;
}
.b-dayresourcecalendarrow .b-dayresourcecalendarrow-column .b-cal-event-bar-container{
  position:relative;
}
.b-dayresourcecalendarrow .b-cal-cell-header{
  align-items:stretch;
  padding-bottom:0;
  flex:1 0 var(--min-day-width, auto);
  display:grid;
  grid-template-columns:repeat(var(--visible-resource-count), 1fr);
}
.b-dayresourcecalendarrow .b-cal-cell-header:not(:last-child){
  border-inline-end:1px solid #c1c1c1;
}
.b-dayresourcecalendarrow .b-dayname-date{
  grid-row:1;
  grid-column:1/-1;
  display:flex;
  flex-flow:column nowrap;
  align-items:center;
  padding:0.5em 0;
  max-width:var(--min-resource-width);
  justify-self:center;
}
.b-dayresourcecalendarrow .b-day-name, .b-dayresourcecalendarrow .b-day-date{
  flex:1;
  display:flex;
  justify-content:center;
}
.b-dayresourcecalendarrow .b-dayresourcecalendarrow-resource-header{
  grid-row:2/2;
  border-top:1px solid #ddd;
  min-width:var(--min-resource-width);
  display:flex;
  gap:0.8em;
  justify-content:center;
  align-items:center;
  padding-block:1em;
  padding-inline:0.5em;
  overflow:hidden;
  contain:inline-size layout;
}
.b-dayresourcecalendarrow .b-dayresourcecalendarrow-resource-header .b-resource-avatar{
  border-color:#ddd;
}
.b-dayresourcecalendarrow .b-dayresourcecalendarrow-resource-header.b-avatar-after .b-resource-avatar{
  order:1;
}
.b-dayresourcecalendarrow .b-dayresourcecalendarrow-resource-header:not(:last-child){
  border-inline-end:1px solid #ddd;
}
.b-dayresourcecalendarrow.b-narrow-resource-header .b-dayresourcecalendarrow-resource-header .b-resource-avatar{
  display:none;
}
.b-dayresourcecalendarrow.b-narrow-resource-header.b-hide-resource-name-when-narrow .b-dayresourcecalendarrow-resource-header .b-resource-avatar{
  display:flex;
}
.b-dayresourcecalendarrow.b-narrow-resource-header.b-hide-resource-name-when-narrow .b-dayresourcecalendarrow-resource-header .b-dayresourcecalendarrow-resource-name{
  display:none;
}
.b-dayresourcecalendarrow .b-dayresourcecalendarrow-resource-name{
  white-space:nowrap;
  text-overflow:ellipsis;
  overflow:hidden;
  line-height:2.2em;
}
.b-dayresourcecalendarrow .b-cal-event-wrap{
  max-width:100%;
  opacity:1;
  pointer-events:all;
}

.b-dayresourceview-column{
  display:flex;
  flex:1 0 var(--min-day-width, auto);
  height:var(--day-height);
  position:relative;
}
.b-dayresourceview-column .b-calendar-cell{
  min-width:var(--min-resource-width);
}
.b-dayresourceview-column:not(.b-last-cell){
  border-inline-end:1px solid #c1c1c1;
}

.b-resourceview{
  align-items:stretch;
}
.b-resourceview .b-cal-widget-settings-button{
  display:none;
}

.b-resourceview-content{
  flex-flow:row nowrap;
  align-items:stretch;
  padding:0;
}
.b-resourceview-content > *{
  flex:1 0 0;
  margin-inline-end:var(--view-gap);
}
.b-resourceview-content > *.b-resourceview-resource{
  min-width:var(--resource-width);
}
.b-resourceview-content > *.b-filtered-hiding{
  min-width:0;
  width:0;
  flex:none;
  border:0 none;
  transition:width 0.3s;
}
.b-resourceview-content > *:last-child, .b-resourceview-content > *.b-last-resource-view{
  margin-inline-end:0;
}
.b-resourceview-content .b-panel-header{
  background-color:#fff;
  border:1px solid #ddd;
  border-block:none;
  border-radius:0;
  color:#606060;
  height:4.5em;
}
.b-resourceview-content .b-dayview.b-first-resource-view > .b-panel-header{
  border-inline-start:none;
}
.b-resourceview-content > .b-dayview.b-resource-dayview-timeaxis{
  position:sticky;
  left:0;
  background-color:#fff;
  z-index:1;
  border-inline-end:1px solid #ddd;
}
.b-rtl .b-resourceview-content > .b-dayview.b-resource-dayview-timeaxis{
  left:auto;
  right:0;
}
.b-resourceview-content > .b-dayview.b-resource-dayview-timeaxis .b-dayview-day-container{
  display:none;
}
.b-resourceview-content > .b-dayview.b-first-resource-view .b-dayview-schedule-container, .b-resourceview-content > .b-dayview.b-first-resource-view .b-dayview-day-container{
  border-inline-start:0 none;
}
.b-resourceview-content > .b-dayview.b-last-resource-view .b-panel-header{
  border-inline-end:0 none;
}
.b-resourceview-content > .b-dayview.b-resource-dayview-scroller{
  position:sticky;
  right:0;
  background-color:#fff !important;
  z-index:1;
  border-inline-start:1px solid #ddd;
}
.b-rtl .b-resourceview-content > .b-dayview.b-resource-dayview-scroller{
  right:auto;
  left:0;
}
.b-resourceview-content > .b-dayview.b-resource-dayview-scroller .b-dayview-timeaxis-time, .b-resourceview-content > .b-dayview.b-resource-dayview-scroller .b-dayview-allday-row-start{
  display:none;
}
.b-resourceview-content > .b-dayview.b-resource-dayview-scroller .b-dayview-day-container{
  visibility:hidden;
}
.b-resourceview-content > .b-dayview.b-resource-dayview-scroller .b-timeaxis-container{
  max-width:1px;
  margin-inline-start:-1px;
}
.b-resourceview-content > .b-dayview.b-resource-dayview-scroller .b-yscroll-pad{
  visibility:hidden;
}
.b-resourceview-content > .b-dayview:not(.b-resource-dayview-timeaxis):not(.b-resource-dayview-scroller):not(.b-last-resource-view) .b-dayview-day-container, .b-resourceview-content > .b-dayview:not(.b-resource-dayview-timeaxis):not(.b-resource-dayview-scroller):not(.b-last-resource-view) .b-dayview-schedule-container{
  border-inline-end:1px solid #ddd;
}
.b-resourceview-content > .b-dayview:not(.b-resource-dayview-timeaxis):not(.b-resource-dayview-scroller) .b-dayview-allday-row-start, .b-resourceview-content > .b-dayview:not(.b-resource-dayview-timeaxis):not(.b-resource-dayview-scroller) .b-timeaxis-container{
  display:none;
}
.b-resourceview-content > .b-dayview.b-resource-dayview-timeaxis, .b-resourceview-content > .b-dayview.b-resource-dayview-scroller{
  flex:0 0 auto;
  margin-inline-end:0;
}
.b-resourceview-content > .b-dayview.b-resource-dayview-timeaxis .b-dayview-header, .b-resourceview-content > .b-dayview.b-resource-dayview-scroller .b-dayview-header{
  visibility:hidden;
  padding-inline:0;
}
.b-resourceview-content > .b-dayview.b-resource-dayview-timeaxis .b-dayview-schedule-container, .b-resourceview-content > .b-dayview.b-resource-dayview-scroller .b-dayview-schedule-container{
  display:flex;
  max-width:0;
  border:0 none;
}
.b-resourceview-content .b-dayview-day-container{
  overscroll-behavior:auto;
}
.b-resourceview-content .b-monthview-content{
  padding-top:0;
}
.b-resourceview-content .b-monthview-content .b-calendar-weekdays{
  padding:0.5em 0;
  border-inline:1px solid #ddd;
}
.b-resourceview-content .b-monthview-content .b-calendar-weekdays .b-calendar-day-header{
  padding-bottom:0;
}
.b-resourceview-content > .b-eventlist > .b-panel-body-wrap{
  border-inline:1px solid #ddd;
}

.b-resourceview-title{
  display:grid;
  column-gap:1em;
  align-items:center;
}
.b-resourceview-title .b-resource-avatar-container{
  font-size:1.2em;
}
.b-resourceview-title.b-has-meta .b-resource-avatar-container{
  grid-row:1/span 2;
}
.b-resourceview-title .b-resource-avatar{
  border:1px solid currentColor;
}
.b-resourceview-title .b-resource-name,
.b-resourceview-title .b-resource-meta{
  grid-column:2;
}
.b-resourceview-title .b-resource-name{
  font-size:1.1em;
}
.b-resourceview-title .b-resource-meta{
  font-size:0.7em;
  font-weight:400;
}

.b-resourcedayviewtimeaxis .b-virtual-scrollers{
  display:none;
}
.b-resourcedayviewtimeaxis.b-resource-dayview-scroller .b-timeaxis-container{
  display:none;
}
.b-calendar-fullweek-button{
  margin-inline-end:1em;
}
.b-calendar:not(.b-calendar-include-weekends-button) .b-calendar-fullweek-button{
  display:none;
}

.b-modeselector{
  flex-basis:min-content;
  flex-shrink:0;
}
.b-modeselector.b-minified > :not(.b-calendar-mode-button){
  display:none;
}
.b-modeselector:not(.b-minified) > .b-calendar-mode-button{
  display:none;
}

.b-overflowpopup{
  min-width:13em;
  max-width:30em;
  z-index:20;
  position:fixed !important;
}
.b-overflowpopup .b-cal-event-bar-container{
  flex:0 0 auto;
}
.b-overflowpopup .b-cal-event-wrap{
  flex-shrink:0;
}

.b-overflowpopup-body-wrap{
  background-color:#fafafa;
}

.b-overflowpopup-content{
  padding:0;
  margin:1em;
}
.b-visible-scrollbar .b-overflowpopup-content.b-vertical-overflow{
  margin-inline-end:0;
}

.b-overflowpopup-header{
  border-top-left-radius:3px;
  border-top-right-radius:3px;
}

.b-daycellcollecter .b-overflowpopup-content{
  contain:style !important;
  flex-flow:column nowrap;
  align-items:stretch;
}
.b-daycellcollecter .b-overflowpopup-content .b-cal-event-wrap{
  flex-shrink:0;
  position:relative !important;
}
.b-sidebar{
  flex:1 0 auto;
  transition:min-width 0.3s, width 0.3s, flex-basis 0.3s;
}
.b-sidebar .b-sidebar-content{
  align-items:stretch;
  gap:1em;
}
.b-sidebar .b-sidebar-content.b-panel-content{
  flex-flow:column nowrap;
  justify-content:flex-start;
  padding:1em;
}
.b-sidebar .b-sidebar-body-wrap > .b-toolbar{
  background-color:#fff;
}

.b-calendar .b-sidebar:not(.b-collapsed):not(.b-collapsing).b-has-datepicker{
  flex-basis:17.5em;
}
.b-calendar .b-sidebar:not(.b-collapsed):not(.b-collapsing).b-has-datepicker.b-datepicker-with-events{
  flex-basis:20em;
}
.b-calendar .b-sidebar.b-sidebar-left{
  order:0;
}
.b-calendar .b-sidebar.b-sidebar-right{
  order:10;
}
.b-calendar:has(.b-sidebar + .b-splitter.b-moving) .b-sidebar{
  transition:none;
}
.b-calendar .b-sidebar-content{
  color:#606060;
  overflow:hidden;
}
.b-calendar .b-sidebar-content .b-datepicker{
  flex-shrink:0;
}
.b-calendar .b-sidebar-content .b-datepicker:not(.b-datepicker-with-events) .b-toolbar.b-dock-top .b-toolbar-content{
  padding-inline:0 !important;
  gap:0;
}
.b-calendar .b-sidebar-content .b-datepicker .b-toolbar{
  color:#606060;
  background:transparent;
  font-size:1em;
}
.b-calendar .b-sidebar-content .b-datepicker .b-toolbar .b-datepicker-yearbutton,
.b-calendar .b-sidebar-content .b-datepicker .b-toolbar .b-datepicker-monthfield{
  font-weight:500;
}
.b-calendar .b-sidebar-content .b-datepicker .b-toolbar .b-field, .b-calendar .b-sidebar-content .b-datepicker .b-toolbar .b-button{
  color:#606060;
}
.b-calendar .b-sidebar-content .b-datepicker .b-calendar-cell{
  flex:0 0 2em;
  padding:0;
  margin:0.1em 0.24em;
}
.b-calendar .b-sidebar-content .b-datepicker .b-calendar-cell .b-datepicker-cell-inner{
  height:2em;
  width:2em;
}
.b-calendar .b-sidebar-content .b-calendar-weekdays{
  color:#606060;
  font-size:0.9em;
}
.b-calendar .b-sidebar-content .b-calendar-weekdays{
  border-bottom:none;
}
.b-calendar .b-sidebar-content .b-datepicker-month{
  margin-inline-end:0.5em;
}
.b-calendar .b-sidebar-content .b-datepicker-content{
  font-size:0.85em;
  background:transparent;
}
.b-calendar .b-sidebar-content .b-datepicker-content .b-week-number-cell{
  line-height:2em;
  width:2em;
  margin:0.1em 0.24em;
}
.b-calendar .b-sidebar-content .b-datepicker-content .b-calendar-weekdays{
  background:transparent;
}
.b-calendar .b-sidebar-content .b-list .b-select-all-item:not(.b-active), .b-calendar .b-sidebar-content .b-list .b-list-title:not(.b-active){
  background-color:var(--panel-background-color);
}
.b-calendar .b-sidebar-content .b-resourcefilter{
  flex-shrink:1;
}
.b-calendar .b-sidebar-content .b-resourcefilter .b-selected-icon{
  margin-inline-end:0.8em;
}
.b-calendar .b-sidebar-content .b-resourcefilter .b-selected-icon:before{
  font-size:150%;
}
.b-calendar .b-sidebar-content .b-field{
  flex:0 0 auto;
  width:auto;
  align-self:auto;
}
.b-calendar .b-sidebar-content .b-field input[type=text], .b-calendar .b-sidebar-content .b-field input:not([type]){
  width:0;
}
.b-calendar .b-sidebar-content .b-list{
  background-color:transparent;
}
.b-calendar :where(.b-sidebar-content .b-resourcefilter .b-list-item){
  padding-block:0.5em;
  padding-inline:0 0.5em;
}

.b-yearview-content{
  flex-basis:0;
  display:flex;
  flex-flow:row wrap;
  align-content:flex-start;
  justify-content:space-around;
  font-size:0.9em;
  font-weight:300;
  padding:1em;
  column-gap:1em;
  row-gap:3em;
  padding-inline-start:max(25% - 23em - 2em, 1em);
}
.b-yearview-content.b-show-events-dots .b-calendar-cell{
  height:2.3em;
}
.b-yearview-content.b-show-events-count .b-cell-events-badge{
  position:absolute;
  inset-block-start:1.6EM;
  display:flex;
  align-items:center;
  justify-content:center;
  border-radius:50%;
  flex-shrink:0;
  font-size:80%;
  background-color:#ef9a9a;
  color:#000;
  width:1.3em;
  height:1.3em;
}
.b-yearview-body-wrap .b-yearview-content{
  background-color:#fff;
  color:#606060;
}
.b-yearview.b-responsive-small .b-yearview-content{
  font-size:1.3em;
}
.b-yearview-content .b-yearview-month{
  flex-basis:calc(25% - 1em);
  min-width:16em;
  display:flex;
  flex-flow:column nowrap;
  height:min-content;
}
.b-yearview-content .b-yearview-month-name{
  margin-bottom:0.4em;
  padding:0.1em 0 0.2em 0.3em;
  font-size:1.3em;
  color:#777;
  max-width:15.3846153846em;
  border:0 none;
  text-align:start;
  background-color:transparent;
  font-family:inherit;
  font-weight:inherit;
}
.b-yearview-content .b-yearview-month-name:hover{
  cursor:pointer;
  background-color:#f0f0f0;
}
.b-yearview-content .b-calendar-weekdays{
  height:2.1em;
}
.b-yearview.b-responsive-small .b-yearview-content .b-calendar-week{
  max-width:100%;
}
.b-yearview-content .b-calendar-week{
  display:flex;
  flex-flow:row nowrap;
  max-width:20em;
}
.b-yearview-content .b-calendar-week:last-child{
  margin-bottom:bottom(1em);
}
.b-yearview-content .b-calendar-week > *{
  flex:1 1 auto;
  display:flex;
  justify-content:center;
  align-items:center;
}
.b-yearview-content .b-yearview-weekday-cell{
  color:#333;
}
.b-yearview-content.b-hide-week-numbers .b-week-number-cell{
  display:none;
}
.b-yearview-content .b-cal-cell-overflow.b-active{
  outline:0 none;
  opacity:1 !important;
  z-index:10 !important;
  box-shadow:5px 5px 10px rgba(0, 0, 0, 0.2), -5px 2px 5px rgba(0, 0, 0, 0.2);
}
.b-yearview-content .b-cal-cell-overflow:hover{
  background-color:inherit;
}
.b-yearview-content:not(.b-draggable-active) .b-calendar-cell.b-datepicker-1-to-3-events:hover, .b-yearview-content:not(.b-draggable-active) .b-calendar-cell.b-datepicker-4-to-6-events:hover, .b-yearview-content:not(.b-draggable-active) .b-calendar-cell.b-calendar-7-or-more-events:hover{
  border-radius:0;
}
.b-yearview-content:not(.b-draggable-active) .b-calendar-cell.b-datepicker-1-to-3-events:hover .b-calendar-cell-inner, .b-yearview-content:not(.b-draggable-active) .b-calendar-cell.b-datepicker-4-to-6-events:hover .b-calendar-cell-inner, .b-yearview-content:not(.b-draggable-active) .b-calendar-cell.b-calendar-7-or-more-events:hover .b-calendar-cell-inner{
  mix-blend-mode:multiply;
}
.b-yearview-content:not(.b-draggable-active) .b-calendar-cell.b-datepicker-1-to-3-events:hover .b-calendar-cell-inner{
  background-color:#f9f69e;
}
.b-yearview-content:not(.b-draggable-active) .b-calendar-cell.b-datepicker-4-to-6-events:hover .b-calendar-cell-inner{
  background-color:#f9d49e;
}
.b-yearview-content:not(.b-draggable-active) .b-calendar-cell.b-calendar-7-or-more-events:hover .b-calendar-cell-inner{
  background-color:#f9bdb3;
}
.b-yearview-content:not(.b-draggable-active) .b-calendar-cell .b-calendar-cell-inner:hover{
  cursor:pointer;
  background-color:#f0f0f0;
}
.b-yearview-content.b-show-events-heatmap .b-datepicker-1-to-3-events{
  background-color:#f9f69e;
}
.b-yearview-content.b-show-events-heatmap .b-datepicker-4-to-6-events{
  background-color:#f9d49e;
}
.b-yearview-content.b-show-events-heatmap .b-calendar-7-or-more-events{
  background-color:#f9bdb3;
}
.b-yearview-content .b-calendar-cell{
  position:relative;
}
.b-yearview-content .b-calendar-cell.b-cal-tentative-event{
  background-color:#19fd09;
}
.b-yearview-content .b-calendar-cell.b-cal-tentative-event.b-other-month{
  background-color:#79d59f;
}
.b-yearview-content .b-calendar-cell .b-cal-minimal-event-container{
  top:1.7em;
}
.b-yearview-content .b-calendar-cell .b-calendar-cell-inner{
  height:2em;
  width:2em;
  flex-shrink:0;
  display:flex;
  align-items:center;
  justify-content:center;
  border-radius:50%;
}
.b-yearview-content .b-calendar-cell.b-today .b-calendar-cell-inner{
  border-radius:50%;
  background-color:#5fa2dd;
  color:#fff;
  font-weight:bold;
}
.b-yearview-content .b-calendar-cell.b-other-month .b-calendar-cell-inner{
  color:#aaa;
}
.b-yearview-content .b-week-number-cell{
  flex:0 0 2em;
  background-color:#f1f3f4;
  color:#777;
  border:0 none;
  padding:0;
  font-family:inherit;
  font-weight:inherit;
  font-size:inherit;
}
.b-yearview-content .b-week-number-cell:hover{
  cursor:pointer;
  background-color:#f0f0f0;
}
.b-yearview-content .b-calendar-cell.b-active, .b-yearview-content .b-week-number-cell.b-active{
  outline:0 none;
  background-color:#e8e8e8;
  border-radius:50%;
}
.b-yearview-content.b-hide-nonworking-days .b-nonworking-day{
  display:none;
}
.b-yearview-content .b-calendar-week:not([data-week]) .b-week-number-cell{
  background-color:inherit;
}

.b-hover-top.b-starts-above .b-gripper-horz, .b-hover-bottom.b-ends-below .b-gripper-horz{
  display:none;
}

.b-cal-event-wrap:not(.b-rtl).b-hover-left.b-continues-past .b-gripper-vert, .b-cal-event-wrap:not(.b-rtl).b-hover-right.b-continues-future .b-gripper-vert{
  display:none;
}
.b-cal-event-wrap.b-rtl.b-hover-right.b-continues-past .b-gripper-vert, .b-cal-event-wrap.b-rtl.b-hover-left.b-continues-future .b-gripper-vert{
  display:none;
}

.b-cal-drag-proxy{
  position:absolute;
  pointer-events:none;
  opacity:0.8;
  max-width:17em;
}
.b-cal-drag-proxy .b-cal-event{
  background:linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)) currentColor;
  padding:0 0.3em;
  gap:0.3em;
}

.b-dayview-content .b-cal-tentative-event,
.b-monthview-content .b-cal-tentative-event{
  pointer-events:none;
}

.b-dayview-day-container .b-cal-tentative-event:not(.b-cal-tentative-event-first) .b-event-header,
.b-dayview-day-container .b-cal-tentative-event:not(.b-cal-tentative-event-first) .b-cal-event-desc{
  display:none;
}

.b-draggable-started:not(.b-yearview-content){
  cursor:pointer;
}
.b-draggable-started:not(.b-yearview-content) .b-week-num, .b-draggable-started:not(.b-yearview-content) .b-day-num, .b-draggable-started:not(.b-yearview-content) .b-cal-cell-overflow{
  pointer-events:none;
}

.b-cal-event-wrap.b-resizing{
  display:none;
}

.b-cal-tentative-event.b-cal-event-footer-desc-overlap .b-cal-event-footer, .b-cal-event-wrap:not(.b-cal-tentative-event-last) .b-cal-event-footer{
  display:none;
}

.b-cal-event-footer{
  position:absolute;
  bottom:0.5em;
  right:0.7em;
  white-space:nowrap;
  font-size:0.7em;
}
.b-rtl .b-cal-event-footer{
  right:auto;
  left:0.7em;
}

.b-cal-tooltip-duration,
.b-cal-tooltip-recurrence{
  margin-top:0.5em;
}
.b-cal-tooltip-duration:before,
.b-cal-tooltip-recurrence:before{
  margin-inline-end:0.5em;
  width:1.5em;
  text-align:center;
}

.b-eventtip .b-sch-clock{
  border-color:#fffef6;
}
.b-eventtip .b-tooltip-content{
  display:flex;
  flex-flow:column nowrap;
  gap:0.3em;
}
.b-eventtip .b-tooltip-content > *{
  margin-block:0;
}

.b-grid-to-cal-drag-proxy{
  position:absolute;
  z-index:10;
  pointer-events:none;
  opacity:0.9;
  width:15em;
  height:25px;
  align-items:center;
  display:flex;
  overflow:hidden;
  text-overflow:ellipsis;
  background-color:#59b53b;
  border-radius:4px;
  color:#fdfdfd;
  padding-inline-start:0.3em;
  font-size:0.9em;
}
.b-grid-to-cal-drag-proxy .b-fa{
  font-size:0.7em;
}
.b-grid-to-cal-drag-proxy .b-fa:first-child{
  margin-inline-end:0.3em;
}
.b-grid-to-cal-drag-proxy .b-fa:not(:first-child){
  margin-inline-start:0.3em;
}

.b-grid-row.b-drop-above:before{
  content:"";
  position:absolute;
  height:1px;
  width:100%;
  background-color:red;
}

:is(.b-grid-row, .b-grid-header-container).b-drop-below:before{
  content:"";
  position:absolute;
  top:calc(100% - 1px);
  height:1px;
  width:100%;
  background-color:red;
}
.b-dayview .b-cal-timerange{
  position:absolute;
  display:flex;
  flex-direction:column;
  overflow:hidden;
  left:0;
  right:0;
  --timerange-color:var(--cal-event-color);
  --timerange-footer-color:rgba(205, 220, 57, 0.5);
  --timerange-border-size:0.15em;
  --timerange-line-size:0.2em;
  --timerange-line-zoom-scale:2;
}
.b-dayview .b-cal-timerange.b-cal-timerange-line{
  border-color:var(--timerange-color);
  border-width:calc(var(--timerange-line-size)) 0 0 0;
  border-style:solid;
  min-height:calc(var(--timerange-line-size));
  margin-inline-end:calc(-1 * var(--dayview-cell-gutter));
  transition:transform 0.3s ease-in-out;
  z-index:1;
  margin-top:calc(var(--timerange-line-size) / -2);
}
.b-dayview .b-cal-timerange.b-cal-timerange-line.b-cal-timerange-hover-zoom:hover{
  transform:scale(var(--timerange-line-zoom-scale));
}
.b-dayview .b-cal-timerange.b-cal-timerange-has-header::after{
  content:" ";
  background-color:var(--timerange-color);
  position:absolute;
  top:0;
  width:calc(var(--timerange-border-size));
  bottom:0;
  opacity:0.5;
}
.b-dayview .b-cal-timerange.b-cal-timerange-has-header.b-cal-timerange-align-end.b-cal-timerange-rotate-pos::after, .b-dayview .b-cal-timerange.b-cal-timerange-has-header.b-cal-timerange-align-start.b-cal-timerange-rotate-neg::after{
  right:0;
}
.b-dayview .b-cal-timerange.b-cal-timerange-has-header.b-cal-timerange-align-end.b-cal-timerange-rotate-neg::after, .b-dayview .b-cal-timerange.b-cal-timerange-has-header.b-cal-timerange-align-start.b-cal-timerange-rotate-pos::after{
  left:0;
}
.b-dayview .b-cal-timerange .b-cal-timerange-body{
  width:100%;
  pointer-events:none;
  z-index:-1;
}
.b-dayview .b-cal-timerange .b-cal-timerange-header,
.b-dayview .b-cal-timerange .b-cal-timerange-footer{
  display:flex;
  align-items:center;
  justify-content:center;
}
.b-dayview .b-cal-timerange .b-cal-timerange-header{
  background-color:rgba(255, 255, 255, 0.9);
  transition:background-color 0.1s;
  position:relative;
  min-width:var(--timerange-header-width);
}
.b-dayview .b-cal-timerange .b-cal-timerange-header::after{
  content:" ";
  background-color:var(--timerange-color);
  position:absolute;
  left:0;
  top:0;
  right:0;
  bottom:0;
  z-index:-1;
}
.b-dayview .b-cal-timerange .b-cal-timerange-header.b-icon .b-cal-timerange-header-text{
  margin-inline-start:0.5em;
}
.b-dayview .b-cal-timerange:not(.b-cal-timerange-narrow) .b-cal-timerange-header-text{
  font-size:0.9em;
}
.b-dayview .b-cal-timerange-rotate-neg{
  align-self:center;
  writing-mode:vertical-lr;
  transform:rotate(180deg);
}
.b-dayview .b-cal-timerange-rotate-neg .b-cal-timerange-header::before{
  transform:rotate(180deg);
}
.b-dayview .b-cal-timerange-rotate-pos{
  align-self:center;
  writing-mode:vertical-rl;
}
.b-dayview .b-cal-timerange-align-start{
  flex-direction:column-reverse;
}
.b-dayview .b-dayview-inset-after.b-dayview-inset-before .b-cal-timerange-has-header:not(.b-cal-timerange-stretch) .b-cal-timerange-body{
  width:calc(100% - 2 * var(--dayview-cell-inset-size));
}
.b-dayview .b-dayview-inset-after:not(.b-dayview-inset-before) .b-cal-timerange-has-header:not(.b-cal-timerange-stretch) .b-cal-timerange-body,
.b-dayview .b-dayview-inset-before:not(.b-dayview-inset-after) .b-cal-timerange-has-header:not(.b-cal-timerange-stretch) .b-cal-timerange-body{
  width:calc(100% - var(--dayview-cell-inset-size));
}
.b-dayview-inset-after .b-dayview .b-cal-timerange:not(.b-cal-timerange-line):not(.b-cal-timerange-has-header):not(.b-cal-timerange-stretch){
  margin-block-end:calc(var(--dayview-cell-inset-size));
}
.b-dayview-inset-before .b-dayview .b-cal-timerange:not(.b-cal-timerange-line):not(.b-cal-timerange-has-header):not(.b-cal-timerange-stretch){
  margin-block-start:calc(var(--dayview-cell-inset-size));
}

.b-weekexpander .b-has-overflow .b-week-number-cell, .b-weekexpander .b-shrinkwrapped .b-week-number-cell{
  position:relative;
}
.b-weekexpander .b-has-overflow .b-week-number-cell .b-week-toggle-tool-wrap, .b-weekexpander .b-shrinkwrapped .b-week-number-cell .b-week-toggle-tool-wrap{
  order:9999;
  display:flex;
  flex-flow:column nowrap;
  align-items:center;
  padding-bottom:4px;
}
.b-weekexpander .b-has-overflow .b-week-number-cell .b-week-toggle-tool-wrap .b-week-toggle-tool, .b-weekexpander .b-shrinkwrapped .b-week-number-cell .b-week-toggle-tool-wrap .b-week-toggle-tool{
  height:1.5em;
  width:1.5em;
  display:flex;
  justify-content:center;
  align-items:center;
}
@media (pointer: coarse){
  .b-weekexpander .b-has-overflow .b-week-number-cell .b-week-toggle-tool-wrap .b-week-toggle-tool, .b-weekexpander .b-shrinkwrapped .b-week-number-cell .b-week-toggle-tool-wrap .b-week-toggle-tool{
    font-size:1.5em;
    align-self:stretch;
  }
}
.b-weekexpander .b-has-overflow .b-week-number-cell .b-week-toggle-tool-wrap .b-week-toggle-tool:hover, .b-weekexpander .b-shrinkwrapped .b-week-number-cell .b-week-toggle-tool-wrap .b-week-toggle-tool:hover{
  border-radius:50%;
  background-color:rgba(90, 90, 90, 0.2);
}
.b-weekexpander .b-has-overflow .b-week-toggle-tool:before{
  content:"\f107";
}
.b-weekexpander .b-shrinkwrapped.b-expanded .b-week-toggle-tool:before{
  content:"\f106";
}

.b-calendar-print-iframe{
  position:absolute;
  top:-1000em;
  left:-1000em;
}

.b-calendar.b-shrinkwrap-row-heights .b-print-content .b-weeks-container{
  flex:0 0 auto;
}
.b-calendar .b-print-content{
  font-family:"Helvetica Neue", Arial, Helvetica, sans-serif;
  position:absolute;
  display:flex;
  flex-direction:column;
  height:100%;
  width:100%;
}
.b-calendar .b-print-content .b-print-header{
  font-size:1.4em;
}
.b-calendar .b-print-content .b-print-body{
  flex:1;
  display:flex;
}
.b-calendar .b-print-content .b-print-body .b-print-viewport{
  display:flex;
  flex:1;
}
.b-calendar .b-print-content .b-print-body .b-print-viewport header{
  display:none;
}
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-calendarmixin{
  padding:0;
}
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-dayview, .b-calendar .b-print-content .b-print-body .b-print-viewport .b-weekview, .b-calendar .b-print-content .b-print-body .b-print-viewport .b-monthview{
  flex:1 0 100%;
}
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-dayview .b-monthview-content, .b-calendar .b-print-content .b-print-body .b-print-viewport .b-weekview .b-monthview-content, .b-calendar .b-print-content .b-print-body .b-print-viewport .b-monthview .b-monthview-content{
  padding:0;
}
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-dayview .b-expand-allday-button, .b-calendar .b-print-content .b-print-body .b-print-viewport .b-dayview .b-yscroll-pad, .b-calendar .b-print-content .b-print-body .b-print-viewport .b-weekview .b-expand-allday-button, .b-calendar .b-print-content .b-print-body .b-print-viewport .b-weekview .b-yscroll-pad{
  display:none;
}
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-dayview .b-dayview-day-content, .b-calendar .b-print-content .b-print-body .b-print-viewport .b-weekview .b-dayview-day-content{
  overflow-y:hidden !important;
}
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-agendaview .b-grid-body-container.b-widget-scroller{
  overflow-y:hidden !important;
}
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-yearview .b-yearview-content{
  padding:0;
  overflow-y:hidden !important;
}
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-yearview .b-yearview-month:nth-child(9),
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-yearview .b-yearview-month:nth-child(10),
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-yearview .b-yearview-month:nth-child(11),
.b-calendar .b-print-content .b-print-body .b-print-viewport .b-yearview .b-yearview-month:nth-child(12){
  margin-bottom:0;
}

@keyframes fadeInOpacity{
  0%{
    opacity:0;
  }
  100%{
    opacity:1;
  }
}
.b-calendar.b-outer{
  height:100%;
}

.b-calendar, .b-calendar-content{
  color:#606060;
  overflow:hidden;
}

.b-calendar{
  -webkit-user-select:none;
  user-select:none;
}
.b-calendar.b-overflow-popup-visible{
  z-index:10;
}
.b-calendar .b-calendar-view-desc-text{
  overflow:hidden;
  white-space:nowrap;
  text-overflow:ellipsis;
  text-transform:capitalize;
  font-size:1.4em;
}
.b-calendar .b-calendar-toolbar{
  border-bottom:1px solid #ddd;
}
.b-calendar .b-calendar-toolbar [data-ref=prevButton] + [data-ref=nextButton]{
  margin-inline-start:-0.5em;
}
.b-calendar:not(.b-calendar-nav-toolbar) .b-calendar-toolbar .b-cal-nav-item{
  display:none;
}
.b-calendar:not(.b-calendar-nav-sidebar) .b-sidebar .b-cal-nav-item{
  display:none;
}
.b-calendar.b-responsive-small .b-calendar-view-desc{
  margin-inline-start:0.5em;
}
.b-calendar.b-responsive-small .b-calendar-toolbar > .b-toolbar-content{
  gap:unset;
}
.b-calendar.b-responsive-small .b-calendar-toolbar button:not(.b-pressed){
  background-color:transparent;
  border-color:transparent;
}
.b-calendar.b-responsive-small .b-calendar-toolbar .b-sidebar-toggle{
  margin-inline-end:0.5em;
}
.b-calendar.b-responsive-small .b-calendar-toolbar .b-calendar-mode-button > .b-button-menu-icon,
.b-calendar.b-responsive-small .b-calendar-toolbar .b-calendar-mode-button > label,
.b-calendar.b-responsive-small .b-calendar-toolbar .b-calendar-today-button > .b-button-menu-icon,
.b-calendar.b-responsive-small .b-calendar-toolbar .b-calendar-today-button > label{
  display:none;
}
.b-calendar:not(.b-responsive-small) .b-calendar-toolbar .b-calendar-mode-button > .b-button-icon{
  display:none;
}
.b-calendar .b-calendar-viewcontainer > .b-monthview > .b-panel-body-wrap > .b-panel-content,
.b-calendar .b-calendar-viewcontainer > .b-yearview > .b-panel-body-wrap > .b-panel-content{
  padding-top:1.2em;
}

.b-notransition:is(.b-calendarmixin, .b-calendar) *{
  transition:none !important;
}

.b-panel-ui-calendar-banner.b-panel-header{
  background-color:#f9f9f9;
  color:#616161;
  font-size:1.4em;
  padding:0.5em;
  white-space:nowrap;
  border-bottom:1px solid #ddd;
}
.b-panel-ui-calendar-banner.b-panel-header .b-header-title{
  font-size:unset;
  font-weight:400;
}
.b-panel-ui-calendar-banner.b-panel-header .b-tool{
  color:#616161;
}

/*# sourceMappingURL=calendar.classic-light.thin.css.map */