:root, :host{
  --b-fa-style-family-classic:"Font Awesome 6 Free";
  --b-fa-font-solid:normal 900 1em/1 "Font Awesome 6 Free";
}

@font-face{
  font-family:"Font Awesome 6 Free";
  font-style:normal;
  font-weight:900;
  font-display:block;
  src:url("../../core-thin/resources/fonts/fa-solid-900.woff2") format("woff2"), url("../../core-thin/resources/fonts/fa-solid-900.ttf") format("truetype");
}
.fas,
.b-fa-solid{
  font-weight:900;
}

.b-content-icon{
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
  display:inline-block;
  font-style:normal;
  font-variant:normal;
  font-weight:normal;
  line-height:1;
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
  vertical-align:0;
}
.b-button{
  --button-disabled-text-opacity:0.6;
  --button-disabled-border-opacity:0.5;
}

.b-theme-info:before{
  content:'{"name":"Classic"}';
}
.b-sch-vertical .b-sch-event-wrap.b-milestone-wrap{
  transform:translateY(-50%);
  margin-inline-start:0;
  height:1em;
}
.b-sch-vertical .b-sch-event-wrap.b-milestone-wrap .b-sch-event{
  padding-inline-start:0;
  width:100%;
}
.b-sch-vertical .b-sch-event-wrap.b-milestone-wrap .b-sch-event .b-sch-event-content{
  justify-content:center;
  margin:0;
}
.b-sch-vertical .b-sch-event-wrap.b-milestone-wrap .b-sch-event .b-sch-event-content label{
  left:unset;
  top:105%;
}

.b-sch-canvas{
  position:absolute;
  inset:0;
  pointer-events:none;
  height:100%;
  overflow:hidden;
  overflow:clip;
  contain:strict;
  width:max(var(--total-column-width, 100%), 100%);
}

.b-sch-foreground-canvas{
  z-index:9;
}
.b-row-reordering .b-sch-foreground-canvas *{
  pointer-events:none !important;
}

.b-timelinebase .b-grid-splitter{
  z-index:12;
}

.b-schedulerbase > .b-editor{
  z-index:5;
}

.b-sch-event.b-milestone label,
.b-sch-event .b-sch-event-content,
.b-sch-event-wrap:not(.b-milestone-wrap) .b-sch-event{
  font-size:14px;
}

.b-sch-event-wrap{
  position:absolute;
  z-index:9;
  justify-content:center;
  align-items:center;
  pointer-events:all;
  transition:background-color 0.2s, color 0.2s, opacity 0.2s, font-weight 0.2s, border 0.2s;
  contain:layout style;
}
.b-animating:not(.b-eventbuffer-transition, .b-subgrid-width-transition) .b-sch-event-wrap:not(.b-dragging, .b-sch-event-wrap-resizing, .b-reusing-own){
  transition:background-color 0.2s, color 0.2s, opacity 0.2s, font-weight 0.2s, border 0.2s, inset 0.2s, left 0.2s, top 0.2s, transform 0.2s, width 0.2s, height 0.2s, font-size 0.2s;
}
.b-toggling-node .b-sch-event-wrap{
  transition:none;
}
.b-sch-event-wrap.b-sch-event-hover, .b-sch-event-wrap:focus{
  z-index:109 !important;
}
.b-sch-event-wrap:focus{
  outline:none;
}
.b-scrolling .b-sch-event-wrap:not(.b-aborting){
  transition:none !important;
}
.b-scrolling .b-sch-event-wrap:not(.b-aborting) .b-sch-event{
  transition:none !important;
}
.b-grid-body-container.b-scrolling .b-sch-event-wrap{
  pointer-events:none;
}

.b-sch-event-wrap.b-milestone-wrap{
  z-index:8;
}
.b-sch-event-wrap.b-milestone-wrap .b-sch-event{
  overflow:visible;
  box-shadow:none;
  background-color:transparent;
  width:1em;
}
.b-sch-event-wrap.b-milestone-wrap .b-sch-event:not(.b-sch-event-withicon) .b-sch-event-content{
  transition:background-color 0.2s, border 0.2s;
}
.b-sch-event-wrap.b-milestone-wrap .b-sch-event.b-sch-event-withicon{
  justify-content:center;
}
.b-sch-event-wrap.b-milestone-wrap .b-sch-event.b-sch-event-withicon .b-sch-event-content{
  font-size:inherit;
  justify-content:center;
  background-color:transparent;
}
.b-sch-event-wrap.b-milestone-wrap .b-sch-event.b-sch-event-withicon i{
  margin-inline-end:0;
  color:#777;
  line-height:1em;
}
.b-sch-event-wrap.b-milestone-wrap .b-sch-event label{
  position:absolute;
  left:calc(100% + 0.5em);
}
.b-sch-event-wrap.b-milestone-wrap .b-sch-event-content{
  display:flex;
  align-items:center;
}
.b-sch-event-wrap.b-milestone-wrap .b-fa, .b-sch-event-wrap.b-milestone-wrap .b-icon{
  margin-inline-end:1em;
  color:#777;
  font-size:inherit;
}
.b-labels-topbottom .b-sch-event-wrap.b-milestone-wrap .b-sch-event:not(.b-sch-event-withicon){
  font-size:40%;
}
.b-sch-event-wrap.b-milestone-wrap .b-sch-event:not(.b-sch-event-withicon) .b-sch-event-content{
  font-size:inherit;
}
.b-sch-event-wrap.b-milestone-wrap .b-sch-event:not(.b-sch-event-withicon) .b-sch-event-content::before{
  position:absolute;
  left:0.1464465em;
  content:" ";
  height:0.707107em;
  width:0.707107em;
  transform-origin:50% 50%;
  rotate:45deg;
  background-color:inherit;
}
.b-using-keyboard .b-sch-event-wrap.b-milestone-wrap.b-active .b-sch-event.b-sch-event-withicon{
  outline:2px solid #2ebcfc;
  outline-offset:3px;
}
.b-using-keyboard .b-sch-event-wrap.b-milestone-wrap.b-active .b-sch-event:not(.b-sch-event-withicon) .b-sch-event-content:before{
  outline:2px solid #2ebcfc;
  outline-offset:3px;
}
.b-sch-event-wrap.b-milestone-wrap.b-measure{
  height:1em;
  position:fixed;
  top:-10000px;
  left:-10000px;
  visibility:hidden;
}
.b-sch-event-wrap.b-milestone-wrap.b-measure label{
  font-weight:400;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap{
  margin:0;
  min-width:0;
  flex-direction:row;
}
.b-using-keyboard .b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap.b-active{
  outline:2px solid #2ebcfc;
  outline-offset:3px;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap.b-active .b-sch-event.b-milestone,
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap.b-active .b-sch-event.b-milestone:before{
  outline:none;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap .b-sch-event.b-milestone{
  font-size:inherit;
  left:0.5em;
  width:calc(100% - 1em);
  min-width:0;
  padding:0;
  align-self:stretch;
  display:flex;
  align-items:center;
  justify-content:center;
  z-index:1;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap .b-sch-event.b-milestone .b-sch-event-content{
  background-color:transparent;
  font-size:14px;
  justify-content:center;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap .b-sch-event.b-milestone .b-sch-event-content::before{
  content:none;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap .b-sch-event.b-milestone::before, .b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap .b-sch-event.b-milestone::after{
  background:inherit;
  position:absolute;
  content:" ";
  height:0.707107em;
  width:0.707107em;
  z-index:0;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap .b-sch-event.b-milestone::before{
  top:50%;
  left:0;
  transform-origin:50% 50%;
  transform:translate(-50%, -50%) rotate(45deg);
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap .b-sch-event.b-milestone::after{
  right:0;
  transform:rotate(-45deg);
  transform-origin:100% 0;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-milestone-wrap.b-measure .b-sch-event.b-milestone{
  width:calc(100% + 1em);
}

.b-sch-event{
  -webkit-user-select:none;
  user-select:none;
  display:flex;
  align-items:center;
  justify-content:flex-start;
  cursor:pointer;
  overflow:hidden;
  overflow:clip;
  white-space:nowrap;
  width:calc(100% - 1px);
  text-align:left;
  min-width:5px;
  min-height:5px;
  transition:background-color 0.2s, color 0.2s, opacity 0.2s, font-weight 0.2s, border 0.2s;
  position:relative;
}
.b-toggling-node .b-sch-event{
  transition:none;
}
.b-sch-event label{
  color:#888;
}
.b-sch-event:not(.b-milestone) .b-fa, .b-sch-event:not(.b-milestone) .b-icon{
  margin-inline-end:0.75em;
  line-height:inherit;
}
.b-sch-event:before{
  font-family:"Font Awesome 6 Free", FontAwesome6Free;
  font-weight:900;
}
.b-sch-event.b-sch-event-resizing, .b-sch-event:hover{
  z-index:10;
}
.b-sch-event.b-sch-event-selected{
  z-index:10;
}
.b-sch-event.b-sch-event-selected, .b-sch-event.b-sch-event-selected *{
  font-weight:500;
}
.b-sch-event.b-sch-event-assign-selected{
  animation-name:instance-selected;
  animation-duration:0.2s;
  animation-iteration-count:3;
  animation-timing-function:ease-in-out;
}
.b-sch-event.b-milestone{
  transition:background 0s;
}

.b-sch-event-wrap:not(.b-milestone-wrap){
  min-width:6px;
  min-height:6px;
}
.b-using-keyboard .b-sch-event-wrap:not(.b-milestone-wrap).b-active .b-sch-event{
  outline:2px solid #2ebcfc;
  outline-offset:3px;
}

@keyframes instance-selected{
  0%{
    top:-2px;
  }
  50%{
    top:2px;
  }
  100%{
    top:0;
  }
}
.b-initial-fade-in .b-sch-dependency,
.b-initial-fade-in .b-sch-event-wrap{
  opacity:0;
  animation-name:initial-fade-in;
  animation-duration:0.5s;
  animation-fill-mode:forwards;
}

@keyframes initial-fade-in{
  0%{
    opacity:0;
  }
  100%{
    opacity:1;
  }
}
.b-initial-zoom-in .b-sch-event-wrap .b-sch-event{
  animation-name:initial-zoom-in;
  animation-duration:0.4s;
  animation-timing-function:ease-in;
}

@keyframes initial-zoom-in{
  0%{
    transform:scale(0.01);
  }
  100%{
    transform:scale(1);
  }
}
.b-initial-slide-from-left .b-sch-event-wrap{
  transform:translateX(-100vw);
  animation-name:initial-slide-from-left;
  animation-duration:0.7s;
  animation-fill-mode:forwards;
}

@keyframes initial-slide-from-left{
  100%{
    transform:translateX(0);
  }
}
.b-initial-slide-from-top .b-sch-event-wrap{
  transform:translateY(-100vh);
  animation-name:initial-slide-from-top;
  animation-duration:0.7s;
  animation-fill-mode:forwards;
}

@keyframes initial-slide-from-top{
  100%{
    transform:translateY(0);
  }
}
.b-sch-event-content{
  z-index:1;
}

.b-milestone .b-sch-event-content{
  flex:1;
}

.b-sch-event:not(.b-milestone) .b-sch-event-content{
  margin:0 0.5em 0 0.75em;
  text-overflow:ellipsis;
  overflow:hidden;
  overflow:clip;
  display:flex;
  min-width:0;
}
.b-sch-event:not(.b-milestone) .b-sch-event-content > :not(i){
  text-overflow:ellipsis;
  overflow:hidden;
  overflow:clip;
  min-width:0;
}

.b-verticaltimeaxis-row .b-sch-event:not(.b-milestone) .b-sch-event-content{
  flex-direction:column;
}

.b-prevent-event-transitions .b-sch-event-wrap{
  transition:none !important;
}

.b-sch-event-wrap{
  display:grid;
  grid-template-columns:1fr;
  grid-template-rows:1fr;
}
.b-sch-event-wrap .b-sch-event{
  height:100%;
  grid-area:body;
}
.b-sch-horizontal .b-sch-event-wrap, .b-sch-event-wrap.b-sch-horizontal{
  grid-template-areas:"top    top    top    top    top" "before start  body   end  after" "bottom bottom bottom bottom bottom";
  grid-template-columns:0 auto 1fr auto 0;
  grid-template-rows:min-content auto min-content;
}
.b-sch-event-wrap.b-sch-vertical{
  grid-template-areas:"start" "body" "end";
  grid-template-rows:auto 1fr auto;
}
.b-sch-event-wrap.b-sch-vertical .b-sch-event-narrow{
  justify-content:center;
  padding-inline-start:0;
  padding-inline-end:0;
}
.b-sch-event-wrap.b-sch-vertical .b-sch-event-narrow .b-sch-event-content{
  flex-direction:column;
  align-items:center;
}
.b-sch-event-wrap.b-sch-vertical .b-sch-event-narrow i{
  margin-inline-end:0;
  align-self:center;
}
.b-sch-event-wrap.b-sch-vertical .b-sch-event-narrow .b-event-text-wrap{
  writing-mode:vertical-rl;
}
.b-sch-event-wrap.b-sch-vertical .b-sch-event{
  grid-column:1;
}
.b-sch-horizontal.b-eventbuffer-transition.b-animating .b-sch-event-wrap:not(.b-dragging):not(.b-sch-event-wrap-resizing){
  transition:background-color 0.2s, color 0.2s, opacity 0.2s, font-weight 0.2s, border 0.2s, height 0.2s, font-size 0.2s;
}
.b-sch-vertical.b-eventbuffer-transition.b-animating .b-sch-event-wrap:not(.b-dragging):not(.b-sch-event-wrap-resizing){
  transition:background-color 0.2s, color 0.2s, opacity 0.2s, font-weight 0.2s, border 0.2s, width 0.2s, font-size 0.2s;
}

.b-sch-event-wrap, .b-gantt-task-wrap{
  --event-primary-color-h:130deg;
  --event-primary-color-s:61.2903225806%;
  --event-primary-color-l:63.5294117647%;
  --event-primary-color:hsl(var(--event-primary-color-h), var(--event-primary-color-s), var(--event-primary-color-l));
}

.b-sch-event-wrap > .b-sch-event:hover, .b-sch-event-wrap > .b-sch-event.b-sch-event-selected, .b-sch-event-wrap > .b-sch-event.b-sch-event-resizing,
.b-sch-event-wrap > .b-segmented > .b-sch-event-segments > .b-sch-event:hover,
.b-sch-event-wrap > .b-segmented > .b-sch-event-segments > .b-sch-event.b-sch-event-selected,
.b-sch-event-wrap > .b-segmented > .b-sch-event-segments > .b-sch-event.b-sch-event-resizing{
  --event-s-factor:var(--event-hover-s-factor);
  --event-l-factor:var(--event-hover-l-factor);
  --event-a:var(--event-hover-a);
  --event-color:var(--event-hover-color);
  --event-opacity:var(--event-hover-opacity);
}
.b-sch-event-wrap > .b-sch-event.b-sch-event-selected:hover,
.b-sch-event-wrap > .b-segmented > .b-sch-event-segments > .b-sch-event.b-sch-event-selected:hover{
  --event-s-factor:var(--event-selected-hover-s-factor);
  --event-l-factor:var(--event-selected-hover-l-factor);
  --event-a:var(--event-selected-hover-a);
}
.b-sch-event-wrap > .b-sch-event-selected > .b-sch-event-segments > .b-sch-event.b-sch-event-segment{
  --event-s-factor:var(--event-hover-s-factor);
  --event-l-factor:var(--event-hover-l-factor);
  --event-a:var(--event-hover-a);
  --event-color:var(--event-hover-color);
  --event-opacity:var(--event-hover-opacity);
}

.b-sch-color-none{
  --event-primary-color-h:null;
  --event-primary-color-s:null;
  --event-primary-color-l:null;
  --event-primary-color:null;
}

.b-sch-event,
.b-gantt-task,
.b-task-rollup[class*=b-sch-color-],
.b-sch-event > .b-sch-event-segments > .b-sch-event{
  --event-background-h:var(--event-primary-color-h);
  --event-background-s:var(--event-primary-color-s);
  --event-background-l:var(--event-primary-color-l);
  --event-s-factor:1;
  --event-l-factor:1;
  --event-a:1;
  --event-hover-s-factor:1;
  --event-hover-l-factor:1;
  --event-hover-a:1;
  --event-selected-hover-s-factor:1;
  --event-selected-hover-l-factor:1;
  --event-selected-hover-a:1;
  --event-background-color:hsla(
          var(--event-background-h),
          calc(var(--event-background-s) * var(--event-s-factor)),
          calc(var(--event-background-l) * var(--event-l-factor)),
          var(--event-a)
  );
  --event-border-color:var(--event-primary-color);
  --event-border-width:0px;
  --event-border-style:solid;
  --event-border-radius:0;
  --event-color:#fff;
  --event-hover-color:#fff;
  --event-opacity:1;
  --event-hover-opacity:1;
  --event-font-weight:400;
  color:var(--event-color);
  background-color:var(--event-background-color);
  border-color:var(--event-border-color);
  border-width:var(--event-border-width);
  border-style:var(--event-border-style);
  border-radius:var(--event-border-radius);
  font-weight:var(--event-font-weight);
  opacity:var(--event-opacity);
}
.b-sch-event.b-sch-event-startsoutside,
.b-gantt-task.b-sch-event-startsoutside,
.b-task-rollup[class*=b-sch-color-].b-sch-event-startsoutside,
.b-sch-event > .b-sch-event-segments > .b-sch-event.b-sch-event-startsoutside{
  border-inline-start:none;
}

.b-sch-event-wrap.b-milestone-wrap > .b-sch-event.b-milestone{
  border-width:0;
}

.b-schedulerbase.b-sch-layout-milestones .b-sch-event.b-milestone{
  background-color:var(--event-background-color);
  border-color:var(--event-border-color);
  border-width:var(--event-border-width) 0 var(--event-border-width) 0;
  opacity:var(--event-opacity);
  border-style:var(--event-border-style);
  font-weight:var(--event-font-weight);
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event.b-milestone::before{
  --event-border-width:inherit;
  --event-border-radius:inherit;
  border-style:var(--event-border-style);
  border-color:var(--event-border-color);
  border-top-width:0;
  border-inline-end-width:0;
  border-bottom-width:var(--event-border-width);
  border-inline-start-width:var(--event-border-width);
  border-bottom-left-radius:var(--event-border-radius);
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event.b-milestone::after{
  --event-border-width:inherit;
  --event-border-radius:inherit;
  border-style:var(--event-border-style);
  border-color:var(--event-border-color);
  border-top-width:0;
  border-inline-end-width:var(--event-border-width);
  border-inline-start-width:0;
  border-bottom-width:var(--event-border-width);
  border-bottom-right-radius:var(--event-border-radius);
  top:calc(var(--event-border-width) * -1);
}

.b-sch-event.b-milestone, .b-gantt-task.b-milestone{
  border-radius:0;
}
.b-sch-event.b-milestone .b-gantt-task-content,
.b-sch-event.b-milestone .b-sch-event-content, .b-gantt-task.b-milestone .b-gantt-task-content,
.b-gantt-task.b-milestone .b-sch-event-content{
  background-color:var(--event-background-color);
}
.b-sch-event.b-milestone .b-gantt-task-content::before,
.b-sch-event.b-milestone .b-sch-event-content::before, .b-gantt-task.b-milestone .b-gantt-task-content::before,
.b-gantt-task.b-milestone .b-sch-event-content::before{
  border-width:var(--event-border-width);
  border-radius:var(--event-border-radius);
  border-color:var(--event-border-color);
  opacity:var(--event-opacity);
  border-style:var(--event-border-style);
}

.b-gantt-task,
.b-sch-style-none .b-sch-event{
  --event-background-image:none;
}
.b-gantt-task.b-task-selected, .b-gantt-task.b-sch-event-selected,
.b-sch-style-none .b-sch-event.b-task-selected,
.b-sch-style-none .b-sch-event.b-sch-event-selected{
  --event-background-image:linear-gradient(rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.25));
}
.b-gantt-task.b-task-selected:hover, .b-gantt-task.b-sch-event-selected:hover,
.b-sch-style-none .b-sch-event.b-task-selected:hover,
.b-sch-style-none .b-sch-event.b-sch-event-selected:hover{
  --event-background-image:linear-gradient(rgba(0, 0, 0, 0.35), rgba(0, 0, 0, 0.35));
}
.b-gantt-task.b-task-selected .b-sch-event-segment, .b-gantt-task.b-sch-event-selected .b-sch-event-segment,
.b-sch-style-none .b-sch-event.b-task-selected .b-sch-event-segment,
.b-sch-style-none .b-sch-event.b-sch-event-selected .b-sch-event-segment{
  --event-background-image:linear-gradient(rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.25));
}
.b-gantt-task.b-task-selected .b-sch-event-segment:hover, .b-gantt-task.b-sch-event-selected .b-sch-event-segment:hover,
.b-sch-style-none .b-sch-event.b-task-selected .b-sch-event-segment:hover,
.b-sch-style-none .b-sch-event.b-sch-event-selected .b-sch-event-segment:hover{
  --event-background-image:linear-gradient(rgba(0, 0, 0, 0.35), rgba(0, 0, 0, 0.35));
}
.b-gantt-task:hover,
.b-sch-style-none .b-sch-event:hover{
  --event-background-image:linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15));
}
.b-gantt-task:not(.b-milestone),
.b-sch-style-none .b-sch-event:not(.b-milestone){
  background-image:var(--event-background-image);
}
.b-gantt-task.b-milestone > .b-gantt-task-content::before, .b-gantt-task.b-milestone > .b-sch-event-content::before,
.b-sch-style-none .b-sch-event.b-milestone > .b-gantt-task-content::before,
.b-sch-style-none .b-sch-event.b-milestone > .b-sch-event-content::before{
  background-image:var(--event-background-image);
}

.b-gantt-task-hover .b-gantt-task{
  --event-background-image:linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15));
}

.b-sch-vertical .b-sch-event{
  align-items:flex-start;
  padding-inline:0.75em;
  white-space:normal;
}
.b-sch-vertical .b-sch-event .b-sch-event-content{
  margin:rotateSides(0 0.5em 0 0.75em);
}
.b-sch-vertical:not(.b-masked).b-grid-empty .b-grid-subgrid:first-child::before{
  content:"";
  display:none;
}
.b-sch-vertical:not(.b-masked).b-grid-empty .b-timeline-subgrid::before{
  color:#262626;
  content:attr(data-empty-text);
  padding:1em;
}
.b-sch-vertical .b-grid-row{
  border-bottom:none;
}

.b-sch-vertical.b-eventlayout-mixed .b-sch-event{
  opacity:0.8;
}

.b-timelinebase.b-overlay-scrollbar .b-virtual-scrollers{
  z-index:100;
}

.b-sch-layout-milestone .b-milestone-wrap{
  min-width:1em;
}

.b-timeline-loading-indicator-wrap{
  position:absolute;
  bottom:0;
  display:flex;
  width:100%;
  height:2px;
  z-index:100;
  background:rgba(76, 175, 80, 0.2);
}

.b-timeline-loading-indicator{
  background:#4caf50;
  opacity:0.6;
  animation:line-loop 3s linear infinite;
}

@keyframes line-loop{
  0%{
    width:0;
    margin-inline-start:0;
  }
  50%{
    width:100%;
    margin-inline-start:0;
  }
  100%{
    width:100%;
    margin-inline-start:100%;
  }
}
.b-scale-cell{
  padding-right:0 !important;
  padding-left:0 !important;
}
.b-scale-cell .b-scale{
  background-color:transparent;
  font-weight:300;
}
.b-scale-cell .b-scale-tick-label{
  font-size:70%;
}
.b-schedulerbase.b-animating .b-grid-row{
  transition:height 0.2s, transform 0.2s;
}

.b-grid-footer-container .b-sch-timeaxiscolumn{
  padding:0;
}

.b-sch-timeaxis-cell{
  padding:0;
  flex:none !important;
  align-items:baseline;
  border-inline-end:none;
}

.b-gridbase .b-timeline-subgrid .b-grid-row.b-selected:not(.b-group-row){
  background-color:transparent;
}
.b-gridbase .b-timeline-subgrid .b-grid-row.b-selected .b-grid-cell.b-sch-timeaxis-cell.b-selected{
  background-color:transparent;
}

.b-stripe .b-timeline-subgrid .b-grid-row.b-selected.b-odd,
.b-stripe .b-timeline-subgrid .b-grid-row.b-odd,
.b-gridbase.b-stripe .b-timeline-subgrid .b-grid-row.b-selected.b-odd,
.b-gridbase.b-stripe .b-timeline-subgrid .b-grid-row.b-odd{
  background-color:rgba(247, 247, 249, 0.8);
}
.b-stripe .b-timeline-subgrid .b-grid-row.b-selected.b-even,
.b-stripe .b-timeline-subgrid .b-grid-row.b-even,
.b-gridbase.b-stripe .b-timeline-subgrid .b-grid-row.b-selected.b-even,
.b-gridbase.b-stripe .b-timeline-subgrid .b-grid-row.b-even{
  background-color:rgba(255, 255, 255, 0.8);
}

.b-stripe .b-timeline-subgrid .b-grid-row.b-selected .b-grid-cell.b-selected{
  background-color:transparent;
}

.b-timeline-subgrid{
  -webkit-user-select:none;
  user-select:none;
}
.b-timeline-subgrid .b-grid-row{
  z-index:1;
}
.b-timeline-subgrid .b-grid-row.b-group-row, .b-timeline-subgrid .b-grid-row.b-group-row.b-selected{
  z-index:7;
  background-color:rgba(255, 255, 255, 0.8);
}
.b-resource-info{
  flex:1;
  height:100%;
  display:flex;
  flex-direction:row;
  align-items:center;
  overflow:hidden;
}
.b-resource-info dl{
  overflow:hidden;
  gap:0.2em;
}
.b-resource-info dt{
  font-weight:600;
  overflow:hidden;
  text-overflow:ellipsis;
}
.b-resource-info.b-no-avatar dt{
  margin-inline-start:0.6em;
}
.b-resource-info dd{
  margin:0;
  font-size:0.8em;
  color:#b0b0b7;
  overflow:hidden;
  text-overflow:ellipsis;
}
.b-resource-info .b-resource-avatar{
  margin-inline-end:0.5em;
}
.b-resourcecollapse-cell{
  cursor:pointer;
}
.b-resourcecollapse-cell .b-icon{
  transition:transform 0.2s !important;
}
.b-resourcecollapse-cell .b-icon.b-flip{
  transform:rotate(180deg);
}
.b-column-lines-canvas{
  z-index:2;
}

.b-columnlines .b-grid-cell:not(:last-child){
  border-inline-end:1px solid #e0e0e7;
}

.b-columnlines .b-group-row .b-grid-cell{
  border-inline-end-color:transparent;
}

.b-column-line,
.b-column-line-major{
  height:100%;
  position:absolute;
  border-left-width:1px;
  border-left-style:solid;
}

.b-column-line{
  border-color:#f0f0f3;
}

.b-column-line-major{
  border-color:#e0e0e7;
  z-index:1;
}

.b-sch-vertical .b-column-line:not(.b-resource-column-line),
.b-sch-vertical .b-column-line-major{
  height:auto;
  width:100%;
  position:absolute;
  border-left:none;
  border-top-width:1px;
  border-top-style:solid;
}
.b-sch-vertical .b-resource-group-divider{
  border-color:#b0b0b7;
}

.b-animating .b-resource-column-line{
  transition:left 0.2s linear;
}
.b-sch-foreground-canvas{
  --scheduler-dependency-terminal-offset:0px;
  --scheduler-dependency-terminal-size:12px;
}

svg.b-sch-dependencies-canvas{
  --scheduler-dependency-marker:url("#arrowEnd");
  contain:unset;
  overflow:visible;
  z-index:8;
}

.b-sch-dependency-arrow{
  fill:#bbb;
}
.b-sch-dependency-arrow path{
  fill:inherit;
}

.b-sch-dependency{
  pointer-events:visibleStroke;
  fill:transparent;
  stroke:#bbb;
  stroke-width:1;
  transition:stroke-width 0.2s linear;
}
.b-sch-dependency.b-sch-dependency-over{
  stroke-width:2;
  stroke-dasharray:0;
}
.b-sch-dependency.b-sch-released{
  display:none;
}
.b-sch-dependency.b-click-area{
  stroke:transparent;
  stroke-dasharray:none !important;
  marker-end:none;
}

.b-sch-dependency{
  marker-end:var(--scheduler-dependency-marker);
}
.b-sch-dependency.b-sch-bidirectional-line{
  marker-start:var(--scheduler-dependency-marker);
}
.b-sch-dependency.b-sch-dependency-markerless{
  marker-end:none;
}

.b-dependencies .b-sch-event-hover{
  z-index:110;
}
.b-dependencies .b-sch-event.b-sch-terminals-visible,
.b-dependencies .b-gantt-task.b-sch-terminals-visible{
  overflow:visible;
}
.b-dependencies.b-highlighting .b-sch-event-wrap .b-sch-event,
.b-dependencies.b-highlighting .b-sch-dependency{
  opacity:0.3;
}
.b-dependencies.b-highlighting .b-sch-event-wrap .b-sch-event.b-highlight,
.b-dependencies.b-highlighting .b-sch-dependency.b-highlight{
  opacity:1;
}
.b-dependencies.b-highlighting .b-sch-resourcetimerange{
  opacity:0.3;
}

.b-sch-terminal{
  width:var(--scheduler-dependency-terminal-size);
  height:var(--scheduler-dependency-terminal-size);
  background-color:#fff;
  border-width:1px;
  border-style:solid;
  border-color:inherit;
  border-radius:50%;
  position:absolute;
  z-index:2;
  cursor:pointer;
  --terminal-start-end-offset:80%;
}
@media (pointer: coarse){
  .b-sch-terminal{
    --terminal-start-end-offset:115%;
  }
  .b-sch-terminal::after{
    content:"";
    height:calc(var(--scheduler-dependency-terminal-size) * 2);
    width:calc(var(--scheduler-dependency-terminal-size) * 2);
    border-radius:50%;
    position:absolute;
    transform:translate(calc(var(--scheduler-dependency-terminal-size) * -0.5 - 1), calc(var(--scheduler-dependency-terminal-size) * -0.5 - 1));
  }
}
.b-sch-terminal.b-sch-terminal:hover{
  background-color:#e6e6e6;
}
.b-dragging .b-sch-terminal, .b-sch-event-resizing .b-sch-terminal{
  display:none;
}

.b-creating-dependency .b-sch-terminal:hover,
.b-creating-dependency .b-sch-terminal-active{
  background-color:#e6e6e6;
}
.b-creating-dependency .b-sch-terminal:hover.b-valid,
.b-creating-dependency .b-sch-terminal-active.b-valid{
  border-color:#4caf50;
  background-color:#b5dfb7;
}
.b-creating-dependency .b-sch-terminal:hover.b-invalid,
.b-creating-dependency .b-sch-terminal-active.b-invalid{
  border-color:#f44336;
  background-color:#fccbc7;
}
.b-creating-dependency *{
  touch-action:none;
}

.b-sch-terminal-top{
  top:var(--scheduler-dependency-terminal-offset);
  left:50%;
  transform:translateX(-50%) translateY(-80%);
}

.b-sch-terminal-bottom{
  bottom:var(--scheduler-dependency-terminal-offset);
  left:50%;
  transform:translateX(-50%) translateY(80%);
}

.b-sch-terminal-start{
  left:var(--scheduler-dependency-terminal-offset);
  top:50%;
  transform:translateX(calc(var(--terminal-start-end-offset) * -1 * var(--rtl-negate))) translateY(-50%);
}
.b-rtl .b-sch-terminal-start{
  left:auto;
  right:0;
}

.b-sch-terminal-end{
  right:var(--scheduler-dependency-terminal-offset);
  top:50%;
  transform:translateX(calc(var(--terminal-start-end-offset) * var(--rtl-negate))) translateY(-50%);
}
.b-rtl .b-sch-terminal-end{
  right:auto;
  left:0;
}

.b-sch-terminal-hover-area{
  grid-area:body;
  width:calc(100% + var(--scheduler-dependency-terminal-offset) * -2 + var(--scheduler-dependency-terminal-size) * 0.8);
  height:calc(100% + var(--scheduler-dependency-terminal-offset) * -2 + var(--scheduler-dependency-terminal-size) * 0.8);
  justify-self:center;
}

.b-labels-topbottom .b-schedulerbase:not(.b-sch-layout-milestones) .b-milestone:not(.b-sch-event-withicon) .b-sch-terminal-start,
.b-labels-topbottom .b-gantt:not(.b-sch-layout-milestones) .b-milestone:not(.b-gantt-task-withicon) .b-sch-terminal-start{
  transform:translate(calc(-0.35em * var(--rtl-negate)), -50%);
}
.b-labels-topbottom .b-schedulerbase:not(.b-sch-layout-milestones) .b-milestone:not(.b-sch-event-withicon) .b-sch-terminal-end,
.b-labels-topbottom .b-gantt:not(.b-sch-layout-milestones) .b-milestone:not(.b-gantt-task-withicon) .b-sch-terminal-end{
  transform:translate(calc(0.35em * var(--rtl-negate)), -50%);
}

.b-sch-dependency-connector{
  position:absolute;
  contain:strict;
  border-top:2px dashed #bbb;
  z-index:10;
  transform-origin:0 0;
  pointer-events:none;
}
.b-sch-dependency-connector.b-removing{
  transition:width 0.2s ease-out;
}
.b-sch-dependency-connector.b-valid{
  border-color:#4caf50;
  border-top-style:solid;
}

.b-sch-dependency-creation-tooltip.b-popup{
  max-width:none;
}
.b-sch-dependency-creation-tooltip .b-popup-header{
  padding:0.5em 0.075em;
  background-color:#4caf50;
}
.b-safari .b-sch-dependency-creation-tooltip .b-popup-header, .b-firefox .b-sch-dependency-creation-tooltip .b-popup-header{
  width:1.5em;
}
.b-sch-dependency-creation-tooltip .b-popup-header i{
  margin-bottom:0.5em;
}
.b-sch-dependency-creation-tooltip.b-rtl .b-popup-header i{
  margin-bottom:0;
  margin-top:0.5em;
}
.b-sch-dependency-creation-tooltip.b-invalid .b-popup-header{
  background-color:#f44336;
}
.b-sch-dependency-creation-tooltip.b-checking .b-popup-header{
  background-color:#b0b0b7;
}

.b-sch-dependency-tooltip .b-tooltip-content{
  display:grid;
  grid-template-columns:auto auto auto;
  grid-column-gap:0.5em;
  align-items:center;
}
.b-sch-dependency-tooltip .b-tooltip-content label{
  font-size:0.8em;
}

.b-sch-dependency-creation-tooltip .b-panel-header,
.b-sch-dependency-tooltip .b-tooltip-content .b-panel-header{
  color:#fff;
}
.b-sch-dependency-creation-tooltip .b-sch-box,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box{
  width:12px;
  height:8px;
  border:1px solid #bbb;
  display:none;
  position:relative;
}
.b-sch-dependency-creation-tooltip .b-sch-box.b-start, .b-sch-dependency-creation-tooltip .b-sch-box.b-end, .b-sch-dependency-creation-tooltip .b-sch-box.b-right, .b-sch-dependency-creation-tooltip .b-sch-box.b-left, .b-sch-dependency-creation-tooltip .b-sch-box.b-top, .b-sch-dependency-creation-tooltip .b-sch-box.b-bottom,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-start,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-end,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-right,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-left,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-top,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-bottom{
  display:block;
}
.b-sch-dependency-creation-tooltip .b-sch-box:after,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box:after{
  content:"";
  width:3px;
  height:3px;
  border-radius:50%;
  background-color:#fff;
  position:absolute;
}
.b-sch-dependency-creation-tooltip .b-sch-box.b-end:after, .b-sch-dependency-creation-tooltip .b-sch-box.b-right:after,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-end:after,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-right:after{
  right:-2px;
  top:50%;
  transform:translateY(-50%);
}
.b-rtl .b-sch-dependency-creation-tooltip .b-sch-box.b-end:after,
.b-rtl .b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-end:after{
  right:auto;
  left:-2px;
}
.b-sch-dependency-creation-tooltip .b-sch-box.b-start:after, .b-sch-dependency-creation-tooltip .b-sch-box.b-left:after,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-start:after,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-left:after{
  left:-2px;
  top:50%;
  transform:translateY(-50%);
}
.b-rtl .b-sch-dependency-creation-tooltip .b-sch-box.b-start:after,
.b-rtl .b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-start:after{
  left:auto;
  right:-2px;
}
.b-sch-dependency-creation-tooltip .b-sch-box.b-top:after,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-top:after{
  top:-2px;
  left:50%;
  transform:translateX(-50%);
}
.b-sch-dependency-creation-tooltip .b-sch-box.b-bottom:after,
.b-sch-dependency-tooltip .b-tooltip-content .b-sch-box.b-bottom:after{
  bottom:-2px;
  left:50%;
  transform:translateX(-50%);
}

.b-predecessor-list{
  display:flex;
  flex-direction:column;
}
.b-predecessor-list > *{
  flex:0 0 auto;
  align-self:stretch;
  width:auto;
}
.b-predecessor-list > .b-list-item .b-predecessor-item-text{
  flex:1;
}
.b-predecessor-list > .b-list-item .b-sch-box{
  width:1.2em;
  height:0.9em;
  border:1px solid #bbb;
  margin-inline-start:0.5em;
  position:relative;
}
.b-predecessor-list > .b-list-item .b-sch-box:after{
  content:"";
  width:0.5em;
  height:0.5em;
  border-radius:50%;
  background-color:#fff;
  position:absolute;
  border:1px solid #bbb;
  display:none;
}
.b-predecessor-list > .b-list-item.b-selected .b-sch-box{
  background-color:#4caf50;
}
.b-predecessor-list > .b-list-item.b-selected.b-fs .b-sch-box.b-from::after{
  right:-0.25em;
  top:50%;
  transform:translateY(-50%);
  display:block;
}
.b-predecessor-list > .b-list-item.b-selected.b-fs .b-sch-box.b-to::after{
  left:-0.25em;
  top:50%;
  transform:translateY(-50%);
  display:block;
}
.b-predecessor-list > .b-list-item.b-selected.b-sf .b-sch-box.b-from::after{
  left:-0.25em;
  top:50%;
  transform:translateY(-50%);
  display:block;
}
.b-predecessor-list > .b-list-item.b-selected.b-sf .b-sch-box.b-to::after{
  right:-0.25em;
  top:50%;
  transform:translateY(-50%);
  display:block;
}
.b-predecessor-list > .b-list-item.b-selected.b-ss .b-sch-box.b-from::after{
  left:-0.25em;
  top:50%;
  transform:translateY(-50%);
  display:block;
}
.b-predecessor-list > .b-list-item.b-selected.b-ss .b-sch-box.b-to::after{
  left:-0.25em;
  top:50%;
  transform:translateY(-50%);
  display:block;
}
.b-predecessor-list > .b-list-item.b-selected.b-ff .b-sch-box.b-from::after{
  right:-0.25em;
  top:50%;
  transform:translateY(-50%);
  display:block;
}
.b-predecessor-list > .b-list-item.b-selected.b-ff .b-sch-box.b-to::after{
  right:-0.25em;
  top:50%;
  transform:translateY(-50%);
  display:block;
}
.b-dependencyeditor.b-popup{
  width:29em;
}
.b-dependencyeditor.b-popup .b-popup-content{
  padding:1em 1em 0 1em;
}
.b-dependencyeditor.b-popup .b-popup-content .b-textfield label,
.b-dependencyeditor.b-popup .b-popup-content .b-checkbox label{
  flex:1 0 4em;
}
.b-dependencyeditor.b-popup .b-popup-content .b-textfield .b-checkbox-label,
.b-dependencyeditor.b-popup .b-popup-content .b-checkbox .b-checkbox-label{
  text-align:start;
}
.b-dependencyeditor.b-popup .b-bottom-toolbar{
  padding:1em 1em 0.4em 1em;
}
.b-dependencyeditor.b-popup .b-bottom-toolbar .b-label-filler{
  display:none;
}
.b-dependencyeditor.b-popup .b-bottom-toolbar > button{
  flex:1;
  margin-inline-end:0.5em;
}
.b-dependencyeditor.b-popup .b-bottom-toolbar > button:last-child{
  margin-inline-end:0;
}
.b-dragselect-rect{
  position:absolute;
  left:0;
  top:0;
  transform-origin:0 0;
  pointer-events:none;
  z-index:100;
  border:1px dashed #505057;
}

.b-cut-item{
  opacity:0.4;
}
.b-schedulerbase.b-dragging-event{
  -webkit-user-select:none;
  user-select:none;
}

.b-sch-event-wrap.b-aborting, .b-sch-event-wrap.b-drag-unified-animation{
  transition:background-color 0.2s, color 0.2s, opacity 0.2s, font-weight 0.2s, border 0.2s, transform 0.2s, left 0.2s, top 0.2s;
}
.b-sch-event-wrap.b-dragging{
  z-index:200;
}
.b-sch-event-wrap.b-dragging.b-drag-main{
  z-index:201;
}
.b-sch-event-wrap.b-dragging .b-sch-event{
  outline:none;
}
.b-sch-event-wrap.b-dragging .b-sch-event.b-sch-style-plain .b-sch-event-wrap.b-dragging .b-sch-event, .b-sch-event-wrap.b-dragging .b-sch-event.b-sch-style-border .b-sch-event-wrap.b-dragging .b-sch-event, .b-sch-event-wrap.b-dragging .b-sch-event.b-sch-style-colored .b-sch-event-wrap.b-dragging .b-sch-event{
  box-shadow:3px 3px 6px rgba(0, 0, 0, 0.4);
}
.b-sch-event-wrap.b-hidden{
  display:none !important;
}

.b-float-root > .b-tooltip.b-eventdrag-tooltip{
  pointer-events:none;
}

.b-sch-tip-message{
  margin-top:0.5em;
}
.b-sch-tip-invalid .b-sch-tip-message{
  color:#ff8787;
}
.b-sch-tip-message .b-icon:first-child{
  margin-inline-end:0.4em;
}
.b-sch-tip-message:empty{
  display:none;
}
.b-timelinebase.b-dragcreating{
  -webkit-user-select:none;
  user-select:none;
}
.b-timelinebase.b-dragcreating .b-sch-timeaxis-cell,
.b-timelinebase.b-dragcreating .b-sch-event-wrap,
.b-timelinebase.b-dragcreating .b-gantt-task-wrap{
  pointer-events:none;
}
.b-timelinebase.b-dragcreating *{
  transition:none;
}

.b-sch-dragcreating.b-too-narrow{
  opacity:0.25;
}

.b-sch-dragcreate-tooltip.b-too-narrow .b-sch-tooltip-startdate,
.b-sch-dragcreate-tooltip.b-too-narrow .b-sch-tooltip-enddate{
  color:#f44336;
}
.b-sch-dragcreate-tooltip.b-too-narrow .b-sch-tooltip-startdate .b-sch-clock,
.b-sch-dragcreate-tooltip.b-too-narrow .b-sch-tooltip-enddate .b-sch-clock{
  border-color:#f44336;
}
.b-eventeditor.b-popup{
  width:min(100%, 16em + var(--date-time-length));
  min-width:min(100%, 16em + var(--date-time-length));
  flex:unset;
}
.b-eventeditor.b-popup.b-collapsed .b-panel-collapse-revealer{
  display:none;
}
.b-eventeditor.b-popup .b-eventeditor-content.b-popup-content .b-field > label{
  flex:0 0 5em;
}
.b-eventeditor.b-popup .b-eventeditor-content.b-popup-content [data-ref=startDateField], .b-eventeditor.b-popup .b-eventeditor-content.b-popup-content [data-ref=endDateField]{
  flex:1 0 calc(55.5% + var(--date-width-difference) - 0.6em / 2);
}
.b-eventeditor.b-popup .b-eventeditor-content.b-popup-content [data-ref=startTimeField], .b-eventeditor.b-popup .b-eventeditor-content.b-popup-content [data-ref=endTimeField]{
  flex:1 0 calc(44.5% - var(--date-width-difference) - 0.6em / 2);
}
.b-eventeditor.b-popup .b-bottom-toolbar{
  background:transparent;
}
.b-eventeditor.b-popup .b-bottom-toolbar > .b-toolbar-content > button{
  flex:1;
  margin-inline-end:0.5em;
  min-width:0;
}
.b-eventeditor.b-popup .b-bottom-toolbar > .b-toolbar-content > button:last-child{
  margin-inline-end:0;
}

.b-panel.b-floating > .b-eventeditor-body-wrap{
  padding-top:1em;
}
.b-panel.b-floating > .b-eventeditor-body-wrap > .b-panel-content{
  padding-block:0;
}
.b-panel.b-floating > .b-eventeditor-body-wrap > .b-bottom-toolbar .b-toolbar-content{
  padding-top:1em;
}
.b-mobile .b-panel.b-floating > .b-eventeditor-body-wrap > .b-bottom-toolbar .b-toolbar-content{
  padding-block:0 1em;
}

.b-readonly.b-panel.b-floating > .b-eventeditor-body-wrap{
  padding-bottom:1em;
}
.b-sch-event-wrap .b-sch-nonworkingtime{
  z-index:0;
}

.b-dragging-event .b-sch-event-wrap.b-dragging .b-sch-nonworkingtime{
  display:none;
}

.b-animating .b-sch-event-wrap .b-sch-timerange{
  transition:none;
}

.b-sch-vertical .b-sch-event-wrap .b-sch-nonworkingtime{
  left:0;
}
.b-sch-event-resizable-true, .b-sch-event-resizable-start, .b-sch-event-resizable-end{
  --handle-size:0.5em;
}
@media (pointer: coarse){
  .b-sch-event-resizable-true, .b-sch-event-resizable-start, .b-sch-event-resizable-end{
    --handle-size:1em;
  }
}
.b-sch-event-resizable-true::before, .b-sch-event-resizable-true::after, .b-sch-event-resizable-start::before, .b-sch-event-resizable-start::after, .b-sch-event-resizable-end::before, .b-sch-event-resizable-end::after{
  --handle-width:9px;
  --handle-height:50%;
  --handle-inset:3px;
  --handle-align-inset:25%;
  --gradient-dir:to right;
  --handle-opacity:0.7;
  --clip-path:inset(0 0 0 var(--handle-inset));
  position:absolute;
  clip-path:var(--clip-path);
  left:0;
  top:var(--handle-align-inset);
  width:var(--handle-width);
  height:var(--handle-height);
  opacity:var(--handle-opacity);
  background:linear-gradient(var(--gradient-dir), #fff 1px, transparent 1px);
  background-size:2px;
  background-position:var(--handle-inset);
}
@media (pointer: coarse){
  .b-sch-event-resizable-true::before, .b-sch-event-resizable-true::after, .b-sch-event-resizable-start::before, .b-sch-event-resizable-start::after, .b-sch-event-resizable-end::before, .b-sch-event-resizable-end::after{
    --handle-width:1.7em;
    --handle-inset:0.7em;
  }
}
.b-sch-event-resizable-true::after, .b-sch-event-resizable-start::after, .b-sch-event-resizable-end::after{
  --clip-path:inset(0 var(--handle-inset) 0 0);
  left:auto;
  right:0;
}
.b-sch-vertical .b-sch-event-resizable-true::before, .b-sch-vertical .b-sch-event-resizable-true::after, .b-sch-vertical .b-sch-event-resizable-start::before, .b-sch-vertical .b-sch-event-resizable-start::after, .b-sch-vertical .b-sch-event-resizable-end::before, .b-sch-vertical .b-sch-event-resizable-end::after{
  --handle-width:50%;
  --handle-height:0.7em;
  --gradient-dir:to bottom;
  background-size:100% 2px;
  --clip-path:inset(var(--handle-inset) 0 0 0);
  top:0;
  left:var(--handle-align-inset);
}
@media (pointer: coarse){
  .b-sch-vertical .b-sch-event-resizable-true::before, .b-sch-vertical .b-sch-event-resizable-true::after, .b-sch-vertical .b-sch-event-resizable-start::before, .b-sch-vertical .b-sch-event-resizable-start::after, .b-sch-vertical .b-sch-event-resizable-end::before, .b-sch-vertical .b-sch-event-resizable-end::after{
    --handle-height:1.7em;
  }
}
.b-sch-vertical .b-sch-event-resizable-true::after, .b-sch-vertical .b-sch-event-resizable-start::after, .b-sch-vertical .b-sch-event-resizable-end::after{
  --clip-path:inset(0 0 var(--handle-inset) 0);
  top:auto;
  bottom:0;
}

.b-eventresize:not(.b-readonly):not(.b-creating-dependency){
  --handle-cursor:ew-resize;
}
.b-eventresize:not(.b-readonly):not(.b-creating-dependency).b-sch-vertical{
  --handle-cursor:ns-resize;
}
.b-eventresize:not(.b-readonly):not(.b-creating-dependency) .b-sch-event-wrap.b-sch-event-hover:not(.b-sch-style-line) > .b-sch-event:not(.b-segmented).b-sch-event-resizable-start:before, .b-eventresize:not(.b-readonly):not(.b-creating-dependency) .b-sch-event-wrap.b-sch-event-hover:not(.b-sch-style-line) > .b-sch-event:not(.b-segmented).b-sch-event-resizable-end:after, .b-eventresize:not(.b-readonly):not(.b-creating-dependency) .b-sch-event-wrap.b-sch-event-hover:not(.b-sch-style-line) > .b-sch-event:not(.b-segmented).b-sch-event-resizable-true:before, .b-eventresize:not(.b-readonly):not(.b-creating-dependency) .b-sch-event-wrap.b-sch-event-hover:not(.b-sch-style-line) > .b-sch-event:not(.b-segmented).b-sch-event-resizable-true:after,
.b-eventresize:not(.b-readonly):not(.b-creating-dependency) .b-sch-event-wrap:not(.b-sch-style-line) .b-sch-event-resizing.b-sch-event-resizable-start:before,
.b-eventresize:not(.b-readonly):not(.b-creating-dependency) .b-sch-event-wrap:not(.b-sch-style-line) .b-sch-event-resizing.b-sch-event-resizable-end:after,
.b-eventresize:not(.b-readonly):not(.b-creating-dependency) .b-sch-event-wrap:not(.b-sch-style-line) .b-sch-event-resizing.b-sch-event-resizable-true:before,
.b-eventresize:not(.b-readonly):not(.b-creating-dependency) .b-sch-event-wrap:not(.b-sch-style-line) .b-sch-event-resizing.b-sch-event-resizable-true:after{
  content:"";
}
.b-eventresize:not(.b-readonly):not(.b-creating-dependency) .b-sch-event.b-resize-handle{
  cursor:var(--handle-cursor);
}
.b-eventresize:not(.b-readonly):not(.b-creating-dependency).b-resizing-event, .b-eventresize:not(.b-readonly):not(.b-creating-dependency) .b-sch-event-resizing{
  -webkit-user-select:none;
  user-select:none;
  cursor:var(--handle-cursor) !important;
}

.b-sch-event-wrap.b-sch-event-wrap-resizing{
  z-index:11;
}

.b-schedulerbase.b-resizing-event .b-sch-timeaxis-cell,
.b-schedulerbase.b-resizing-event .b-sch-event-wrap{
  pointer-events:none;
}
.b-popup.b-sch-event-tooltip{
  max-width:30em;
  display:flex;
  flex-direction:column;
}
.b-popup.b-sch-event-tooltip .b-sch-event-title{
  font-weight:600;
}

.b-eventtip-content{
  gap:0.5em;
}
.b-timeaxis-group-summary{
  display:flex;
  flex-direction:row;
  width:100%;
  height:100%;
}
.b-timeaxis-group-summary .b-timeaxis-tick{
  display:flex;
  flex-direction:column;
  align-items:stretch;
  justify-content:center;
}
.b-timeaxis-group-summary .b-timeaxis-summary-value{
  text-align:center;
}

.b-gridbase.b-schedulerbase .b-timeline-subgrid .b-grid-row.b-group-footer.b-hover .b-grid-cell,
.b-gridbase.b-schedulerbase .b-timeline-subgrid .b-grid-row.b-group-footer.b-hover .b-grid-cell:hover{
  background-color:#f7f7f9;
}
.b-header-drag-selection-rect{
  position:absolute;
  left:0;
  top:0;
  height:100%;
  transform-origin:0 0;
  pointer-events:none;
  z-index:100;
  background:rgba(166, 214, 251, 0.3);
}
.b-sch-event-wrap .b-sch-label, .b-gantt-task-wrap .b-sch-label{
  font-size:12px;
  text-align:center;
  white-space:nowrap;
  color:#888;
  font-weight:400;
  z-index:1;
  min-width:1em;
  pointer-events:all;
  -webkit-user-select:none;
  user-select:none;
}
.b-sch-event-wrap .b-sch-label-top, .b-gantt-task-wrap .b-sch-label-top{
  grid-area:top;
  justify-self:center;
  margin-bottom:0.2em;
}
.b-sch-event-wrap .b-sch-label-bottom, .b-gantt-task-wrap .b-sch-label-bottom{
  grid-area:bottom;
  justify-self:center;
  margin-top:0.2em;
}
.b-sch-event-wrap .b-sch-label-before, .b-gantt-task-wrap .b-sch-label-before{
  grid-area:before;
  justify-self:end;
  margin-inline-end:1.5em;
}
.b-sch-event-wrap .b-sch-label-after, .b-gantt-task-wrap .b-sch-label-after{
  grid-area:after;
  justify-self:start;
  margin-inline-start:1.5em;
}

.b-sch-event-wrap.b-measure-label{
  height:1em;
  position:fixed;
  top:-10000px;
  left:-10000px;
  visibility:hidden;
}
.b-sch-event-wrap.b-measure-label label{
  padding-inline-start:1.5em;
  grid-area:none;
}
.b-pan .b-timeline-subgrid{
  cursor:move;
}
.b-resource-time-range-canvas{
  z-index:5;
}
.b-sch-canvas.b-interactive .b-sch-resourcetimerange{
  pointer-events:auto;
}

.b-sch-resourcetimerange{
  display:flex;
  position:absolute;
  left:0;
  background-color:rgba(253, 216, 53, 0.2);
  color:#e3ba02;
  justify-content:center;
  align-items:flex-end;
}
.b-sch-resourcetimerange [data-task-feature]{
  display:flex;
  height:100%;
  align-items:center;
  font-size:12px;
}
.b-animating .b-sch-resourcetimerange{
  transition:background-color 0.2s, color 0.2s, opacity 0.2s, font-weight 0.2s, border 0.2s, transform 0.2s, left 0.2s, right 0.2s, width 0.2s, height 0.2s, font-size 0.2s;
}
.b-sch-resourcetimerange .b-sch-event-withicon .b-fa{
  margin-inline-end:0.4em;
  line-height:inherit;
}
.b-sch-resourcetimerange .b-sch-event-content{
  margin:3px;
}

.b-sch-vertical .b-sch-resourcetimerange{
  justify-content:center;
  align-items:flex-start;
}

.b-sch-color-red.b-sch-resourcetimerange{
  background-color:rgba(255, 135, 135, 0.2);
  color:#ff2626;
}

.b-sch-color-pink.b-sch-resourcetimerange{
  background-color:rgba(247, 131, 172, 0.2);
  color:#f22a70;
}

.b-sch-color-purple.b-sch-resourcetimerange{
  background-color:rgba(234, 128, 220, 0.2);
  color:#df31c8;
}

.b-sch-color-violet.b-sch-resourcetimerange{
  background-color:rgba(151, 117, 250, 0.2);
  color:#541cf7;
}

.b-sch-color-indigo.b-sch-resourcetimerange{
  background-color:rgba(116, 143, 252, 0.2);
  color:#1a46fa;
}

.b-sch-color-blue.b-sch-resourcetimerange{
  background-color:rgba(77, 173, 247, 0.2);
  color:#0a88e9;
}

.b-sch-color-cyan.b-sch-resourcetimerange{
  background-color:rgba(59, 201, 219, 0.2);
  color:#1fa1b2;
}

.b-sch-color-teal.b-sch-resourcetimerange{
  background-color:rgba(56, 217, 169, 0.2);
  color:#1fae83;
}

.b-sch-color-green.b-sch-resourcetimerange{
  background-color:rgba(105, 219, 124, 0.2);
  color:#2dc646;
}

.b-sch-color-lime.b-sch-resourcetimerange{
  background-color:rgba(169, 227, 75, 0.2);
  color:#85c61d;
}

.b-sch-color-yellow.b-sch-resourcetimerange{
  background-color:rgba(253, 216, 53, 0.2);
  color:#e3ba02;
}

.b-sch-color-orange.b-sch-resourcetimerange{
  background-color:rgba(255, 169, 77, 0.2);
  color:#f98100;
}

.b-sch-color-deep-orange.b-sch-resourcetimerange{
  background-color:rgba(255, 112, 67, 0.2);
  color:#f23a00;
}

.b-sch-color-gray.b-sch-resourcetimerange{
  background-color:rgba(160, 160, 160, 0.2);
  color:#7e7272;
}

.b-sch-color-gantt-green.b-sch-resourcetimerange{
  background-color:rgba(165, 216, 167, 0.2);
  color:#5fbf63;
}

.b-schedule-selected-tick{
  position:absolute;
  background-color:rgba(255, 152, 0, 0.1);
  display:flex;
  justify-content:center;
  align-items:center;
  overflow:hidden;
}
.b-schedule-selected-tick.b-widget, .b-schedule-selected-tick .b-contains-widget{
  z-index:7;
}
.b-schedule-selected-tick:not(.b-widget, .b-contains-widget){
  pointer-events:none;
}

.b-sch-scheduletip{
  min-width:10em;
}
.b-sch-scheduletip.b-panel .b-panel-content{
  padding-block:0.3em;
  margin-block:0.5em;
}
.b-sch-scheduletip.b-nonworking-time{
  opacity:0;
  pointer-events:none;
}
.b-scrollbuttons{
  --scrollbutton-color:#c3c3d1;
  --scrollbutton-background-color:#fff;
  --scrollbutton-hover-color:#8a8aa4;
}
.b-scrollbuttons .b-timeaxissubgrid .b-grid-row,
.b-scrollbuttons .b-timeaxissubgrid .b-grid-cell{
  overflow:unset;
  z-index:4;
}
.b-scrollbuttons .b-timeaxissubgrid .b-scroll-buttons-container{
  height:100%;
  display:flex;
  width:100%;
  pointer-events:none;
  align-items:center;
}
.b-scrollbuttons .b-timeaxissubgrid .b-scroll-button-wrap{
  position:sticky;
  display:flex;
  flex-direction:row;
  align-items:baseline;
  justify-content:center;
  border-radius:0.3em;
  gap:0.5em;
  border:1px solid var(--scrollbutton-color);
  padding:0.1em 0.5em;
  pointer-events:all;
  cursor:pointer;
  background:var(--scrollbutton-background-color);
  transition:color 0.2s, background-color 0.2s;
  color:var(--scrollbutton-color);
  min-width:3.1em;
  font-size:0.8em;
}
.b-scrollbuttons .b-timeaxissubgrid .b-scroll-button-wrap:hover{
  --scrollbutton-color:var(--scrollbutton-hover-color);
}
.b-scrollbuttons .b-timeaxissubgrid .b-scroll-button-wrap label{
  font-size:0.9em;
  font-weight:500;
}
.b-scrollbuttons .b-timeaxissubgrid .b-scroll-button-wrap.b-scroll-button-hidden{
  visibility:hidden;
}
.b-scrollbuttons .b-timeaxissubgrid .b-scroll-button-wrap.b-scroll-backward{
  inset-inline-start:1em;
}
.b-scrollbuttons .b-timeaxissubgrid .b-scroll-button-wrap.b-scroll-forward{
  inset-inline-start:100%;
  translate:calc(-100% - 1em);
  margin-inline-end:-100%;
}
.b-scrollbuttons .b-timeaxissubgrid.b-rtl .b-scroll-button-wrap.b-scroll-forward{
  translate:calc(100% + 1em);
}
.b-simpleeventeditor{
  z-index:15;
}
.b-simpleeventeditor .b-field{
  align-items:stretch;
}
.b-sch-horizontal .b-simpleeventeditor .b-field{
  height:100%;
}

.b-stickyevents:where(.b-sch-horizontal) .b-sch-event-wrap:where(:not(.b-disable-sticky, .b-milestone-wrap)),
.b-stickyevents:where(.b-sch-horizontal) .b-sch-resourcetimerange{
  overflow:visible;
}
.b-stickyevents:where(.b-sch-horizontal) .b-sch-event-wrap:where(:not(.b-disable-sticky, .b-milestone-wrap)) .b-sch-event-content,
.b-stickyevents:where(.b-sch-horizontal) .b-sch-resourcetimerange .b-sch-event-content{
  position:sticky;
  left:0.75em;
}
.b-stickyevents:where(.b-sch-horizontal) .b-sch-event-wrap:where(:not(.b-disable-sticky, .b-milestone-wrap)) .b-sch-event,
.b-stickyevents:where(.b-sch-horizontal) .b-sch-resourcetimerange .b-sch-event{
  overflow:visible;
}
.b-stickyevents:where(.b-sch-horizontal) .b-sch-resourcetimerange{
  justify-content:flex-start;
}
.b-stickyevents:where(.b-sch-horizontal) .b-sch-resourcetimerange > div{
  overflow:visible;
  width:100%;
}

.b-rtl.b-stickyevents.b-sch-horizontal .b-sch-event-wrap:not(.b-disable-sticky, .b-milestone-wrap) .b-sch-event-content,
.b-rtl.b-stickyevents.b-sch-horizontal .b-sch-resourcetimerange .b-sch-event-content{
  right:0.75em;
}

.b-firefox.b-stickyevents.b-sch-horizontal .b-sch-foreground-canvas{
  contain:none;
}
.b-sch-summarybar{
  padding:0;
  flex-direction:row;
  border-inline-end:none;
  justify-content:flex-start;
  flex-grow:unset !important;
}
.b-sch-summarybar .b-timeaxis-tick{
  display:flex;
  flex-direction:column;
  text-align:center;
  overflow:hidden;
  justify-content:center;
  align-items:stretch;
}

.b-sch-summarybar:not(.b-sch-vertical) .b-timeaxis-tick{
  padding:0.5em 0;
}
.b-sch-summarybar:not(.b-sch-vertical) .b-timeaxis-tick:not(:last-child){
  border-inline-end:1px solid #b0b0b7;
}

.b-sch-vertical .b-sch-summarybar{
  flex-direction:column;
  align-items:stretch;
  background:#e0e0e7 !important;
  color:#262626;
}
.b-sch-vertical .b-sch-summarybar .b-timeaxis-tick{
  padding:0 0.5em;
}
.b-sch-vertical .b-sch-summarybar .b-timeaxis-tick:not(:last-child){
  border-bottom:1px solid #b0b0b7;
}

.b-timeaxis-summary-tip{
  display:flex;
  flex-direction:row;
  flex-wrap:wrap;
}
.b-timeaxis-summary-tip header{
  width:100%;
  text-align:center;
  font-weight:700;
  margin-bottom:1em;
}
.b-timeaxis-summary-tip label{
  flex:1 1 50%;
}
.b-timeaxis-summary-tip .b-timeaxis-summary-value{
  flex:0 0 auto;
}

.b-sch-timeaxis-menu-daterange-popup{
  width:21em;
  background-color:#75757f;
}
.b-sch-timeaxis-menu-daterange-popup .b-panel-body-wrap{
  background-color:transparent;
}
.b-sch-timeaxis-menu-daterange-popup .b-left-nav-btn,
.b-sch-timeaxis-menu-daterange-popup .b-right-nav-btn{
  max-width:2.5em;
}

.b-eventfilter-menu .b-panel-content{
  padding:0.25em 0;
}
.b-float-root > .b-floating.b-interaction-tooltip{
  pointer-events:none;
}

.b-sch-line{
  margin-inline-start:-1px;
}

.b-timeranges-canvas{
  display:contents;
}

.b-sch-timerange{
  display:flex;
  flex-direction:row;
  align-items:center;
  position:absolute;
  inset-inline-start:0;
  overflow:hidden;
  justify-content:center;
  color:#aaa;
  z-index:6;
  cursor:pointer;
}
.b-sch-timerange.b-over-resize-handle{
  cursor:ew-resize;
}
.b-timelinebase:not(.b-dragging-timerange) .b-sch-timerange:hover{
  z-index:9;
}
.b-sch-timerange label{
  font-size:0.8em;
  color:inherit;
  white-space:nowrap;
  cursor:inherit;
  text-overflow:ellipsis;
  overflow:hidden;
  padding:0.3em;
}
.b-sch-timerange i{
  margin-inline-end:0.5em;
}
.b-timeranges-body-canvas .b-sch-timerange.b-narrow-range label{
  writing-mode:tb;
  padding-inline-start:1em;
}

.b-sch-current-time{
  pointer-events:none;
}

.b-animating .b-sch-timerange{
  transition:inset 0.3s, width 0.3s, height 0.3s;
}

.b-timeline-subgrid .b-sch-timerange{
  height:100%;
  align-items:flex-start;
  min-width:1px;
  color:#aaa;
  font-size:14px;
}
.b-timeline-subgrid .b-sch-timerange:not(.b-sch-line) label{
  overflow:visible;
}
.b-timeline-subgrid .b-sch-timerange:not(.b-sch-line) label.b-vertical{
  -webkit-writing-mode:vertical-lr;
  writing-mode:vertical-lr;
  -ms-writing-mode:tb-lr;
}
.b-timeline-subgrid .b-sch-timerange.b-sch-line{
  width:1px;
  overflow:visible;
}
.b-timeline-subgrid .b-sch-timerange.b-sch-line:before{
  display:none;
}
.b-timeline-subgrid .b-sch-range{
  background-color:rgba(240, 240, 240, 0.5);
  pointer-events:none;
}
.b-timeline-subgrid div.b-sch-line{
  border-inline-start:2px solid #fab005;
  color:#fff;
  padding:0;
  z-index:10;
}
.b-timeline-subgrid div.b-sch-line label{
  background-color:#fab005;
  padding:0.3em;
  white-space:nowrap;
}
.b-timeline-subgrid div.b-sch-current-time{
  border-inline-start-color:#fa5252;
}
.b-timeline-subgrid div.b-sch-current-time label{
  background-color:#fa5252;
}

.b-timelinebase:not(.b-sch-vertical) .b-timeline-subgrid .b-sch-line{
  flex-direction:column;
  justify-content:flex-start;
}
.b-timelinebase:not(.b-sch-vertical) .b-timeline-subgrid .b-sch-line label{
  margin-top:0.5em;
}

.b-sch-vertical .b-sch-timerange{
  inset-inline-start:auto;
  inset-block-start:0;
  justify-content:flex-start;
}
.b-sch-vertical .b-sch-timerange label{
  -webkit-writing-mode:vertical-lr;
  writing-mode:vertical-lr;
  -ms-writing-mode:tb-lr;
  transform:rotate(180deg);
}
.b-sch-vertical .b-sch-timerange.b-over-resize-handle{
  cursor:ns-resize;
}
.b-sch-vertical .b-timeline-subgrid .b-sch-timerange{
  height:auto;
  width:inherit;
}
.b-sch-vertical .b-timeline-subgrid .b-sch-line{
  border-top:2px solid #fab005;
  color:#fff;
  padding:0;
  height:1px;
}
.b-sch-vertical .b-timeline-subgrid .b-sch-line label{
  background-color:#fab005;
  padding:0.3em;
  white-space:nowrap;
}

.b-timeranges-header-canvas .b-sch-timerange{
  pointer-events:all;
}

.b-grid-header .b-sch-timerange{
  flex-direction:row;
  justify-content:center;
  bottom:0;
  background-color:#a6d6fb;
  color:#fff;
  line-height:1;
  padding:0.5em 0;
  -webkit-user-select:none;
  user-select:none;
}
.b-grid-header .b-sch-timerange.b-sch-line{
  padding:0.5em;
  background-color:#fab005;
  z-index:10;
}
.b-grid-header .b-sch-timerange.b-sch-current-time{
  background-color:#fa5252;
}
.b-grid-header .b-sch-timerange:before{
  margin-inline-end:0.5em;
}
.b-grid-header .b-sch-timerange label{
  padding:0;
}
.b-grid-header.b-sch-timeaxiscolumn-levels-1 .b-sch-timerange{
  height:100%;
}
.b-grid-header.b-sch-timeaxiscolumn-levels-2 .b-sch-timerange{
  height:50%;
}
.b-grid-header.b-sch-timeaxiscolumn-levels-3 .b-sch-timerange{
  height:33.3333333333%;
}
.b-grid-header.b-sch-timeaxiscolumn-levels-4 .b-sch-timerange{
  height:25%;
}
.b-grid-header.b-sch-timeaxiscolumn-levels-5 .b-sch-timerange{
  height:20%;
}
.b-grid-header .b-sch-line{
  border-bottom-right-radius:5px;
  border-top-right-radius:5px;
}
.b-grid-header .b-sch-line.b-rtl{
  border-bottom-right-radius:0;
  border-top-right-radius:0;
  border-bottom-left-radius:5px;
  border-top-left-radius:5px;
}
.b-grid-header .b-sch-line.b-sch-timerange-with-headerlabel label{
  inset-inline-start:-2px;
}

.b-sch-vertical .b-grid-subgrid-locked .b-sch-timerange{
  flex-direction:column;
  justify-content:center;
  inset-inline-end:0;
  background-color:#a6d6fb;
  color:#fff;
  width:2em;
  -webkit-user-select:none;
  user-select:none;
}
.b-sch-vertical .b-grid-subgrid-locked .b-sch-timerange:before{
  margin-bottom:0.5em;
}
.b-sch-vertical .b-grid-subgrid-locked .b-sch-line{
  border-bottom-left-radius:5px;
  border-bottom-right-radius:5px;
}
.b-sch-vertical .b-grid-subgrid-locked .b-sch-line.b-sch-timerange-with-headerlabel label{
  inset-block-start:-2px;
}

.b-sch-timeranges-with-headerelements .b-timeline-subgrid .b-sch-range:before{
  display:none;
}

.b-dragging-timerange .b-sch-timerange:not(.b-dragging){
  pointer-events:none;
}

.b-locked-rows-clone .b-sch-timerange label{
  display:none;
}
.b-sch-timeaxiscolumn .b-selected-time-span,
.b-verticaltimeaxiscolumn .b-selected-time-span{
  background:#4dadf7;
  color:#fff;
  justify-content:space-between;
  padding:0.4em;
  opacity:1;
}
.b-sch-timeaxiscolumn .b-selected-time-span span,
.b-verticaltimeaxiscolumn .b-selected-time-span span{
  white-space:nowrap;
  overflow:hidden;
}
.b-sch-timeaxiscolumn .b-selected-time-span .b-selection-start,
.b-sch-timeaxiscolumn .b-selected-time-span .b-selection-end,
.b-verticaltimeaxiscolumn .b-selected-time-span .b-selection-start,
.b-verticaltimeaxiscolumn .b-selected-time-span .b-selection-end{
  align-self:flex-end;
}
.b-sch-timeaxiscolumn .b-selected-time-span .b-icon-close,
.b-verticaltimeaxiscolumn .b-selected-time-span .b-icon-close{
  position:absolute;
  top:0.2em;
  right:0.4em;
  cursor:pointer;
}

.b-timelinebase.b-rtl .b-sch-timeaxiscolumn .b-selected-time-span .b-icon-close,
.b-timelinebase.b-rtl .b-verticaltimeaxiscolumn .b-selected-time-span .b-icon-close{
  left:0.4em;
  right:auto;
}

.b-sch-timeaxiscolumn .b-selected-time-span{
  font-size:0.7em;
}

.b-verticaltimeaxiscolumn .b-selected-time-span{
  width:calc(100% - 2em);
}
.b-verticaltimeaxiscolumn .b-selected-time-span span{
  font-size:0.7em;
}
.b-verticaltimeaxiscolumn .b-selected-time-span .b-icon-close{
  left:0.4em;
}

.b-grid-header .b-selected-time-span,
.b-timeline-subgrid .b-selected-time-span,
.b-verticaltimeaxiscolumn .b-selected-time-span{
  transition:none;
  z-index:10;
}

.b-timeline-subgrid .b-selected-time-span{
  background:rgba(166, 214, 251, 0.2);
}
.b-timeranges-header-canvas .b-sch-nonworkingtime{
  background-color:rgba(240, 213, 213, 0.3);
  z-index:5;
  pointer-events:none;
}

.b-sch-nonworkingtime{
  z-index:3;
}
.b-sch-clockwrap{
  display:flex;
}
.b-sch-clockwrap:not(:first-child){
  margin-top:0.3em;
}

.b-sch-clock{
  position:relative;
  height:21px;
  width:21px;
  min-width:21px;
  white-space:nowrap;
}
.b-sch-clock .b-sch-hour-indicator{
  border-top:2px solid transparent;
  border-bottom:6px solid #777;
}
.b-sch-clock .b-sch-minute-indicator{
  border-top:8px solid #777;
}
.b-sch-clock .b-sch-minute-indicator,
.b-sch-clock .b-sch-hour-indicator{
  position:absolute;
  left:50%;
  top:1px;
  width:2px;
  height:8px;
  overflow:hidden;
  margin-inline-start:-1px;
  transform-origin:50% 100%;
}

.b-sch-clock-hour .b-sch-clock{
  border:2px solid #03a9f4;
  background-color:#fff;
  border-radius:100%;
}
.b-sch-clock-hour .b-sch-clock-dot{
  position:absolute;
  left:50%;
  top:50%;
  width:4px;
  height:4px;
  margin-inline-start:-2px;
  margin-top:-2px;
  background:#777;
  border-radius:3px;
  z-index:2;
}

.b-sch-clock-day .b-sch-clock{
  background-color:#fff;
  border:none;
  border-radius:2px;
}
.b-sch-clock-day .b-sch-clock .b-sch-hour-indicator{
  width:inherit;
  position:static !important;
  background-color:#03a9f4;
  border-top-left-radius:2px;
  border-top-right-radius:2px;
  font-size:7px;
  line-height:8px;
  text-align:center;
  color:#fff;
  height:9px;
  text-indent:0;
  margin-top:-1px;
  margin-inline-start:0;
  border:0 none;
  transform:none !important;
}
.b-sch-clock-day .b-sch-clock .b-sch-minute-indicator{
  width:inherit;
  color:#555;
  position:static !important;
  height:16px;
  background-color:transparent;
  font-size:10px;
  text-align:center;
  text-indent:0;
  line-height:12px;
  border:none;
  margin-inline-start:0;
  transform:none !important;
}
.b-sch-clock-day .b-sch-clock-dot{
  display:none;
}

.b-sch-clock-text{
  margin-inline-start:8px;
  padding-top:1px;
  white-space:nowrap;
  width:100%;
}
.b-sticky-headers .b-grid-header, .b-sticky-headers .b-timeaxis, .b-sticky-headers .b-sticky-header, .b-sticky-headers .b-sch-header-timeaxis-cell{
  overflow:visible;
}
.b-sticky-headers .b-sch-header-text.b-sticky-header{
  position:sticky;
}
.b-sticky-headers .b-horizontaltimeaxis .b-sch-header-text.b-sticky-header{
  left:0;
  padding-inline:left(0 0.5em 0 0.75em);
}
.b-sticky-headers .b-verticaltimeaxis .b-sch-header-text.b-sticky-header{
  top:0.15em;
}
.b-sticky-headers .b-verticaltimeaxis .b-sch-header-row-0 .b-sch-header-text.b-sticky-header{
  padding-top:right(0 0.5em 0 0.75em);
}
.b-sticky-headers .b-verticaltimeaxis .b-sch-header-row.b-lowest .b-sch-header-text.b-sticky-header{
  padding-bottom:right(0 0.5em 0 0.75em);
}

body:not(.b-using-keyboard) .b-sch-timeaxis-cell:focus, body:not(.b-using-keyboard) .b-sch-timeaxis-cell:focus-within{
  outline:none !important;
}

.b-sch-header-timeaxis-cell{
  align-items:center;
}

.b-gridbase:not(.b-column-resizing):not(.b-row-reordering) .b-grid-header-container:not(.b-dragging-header) .b-depth-0:hover, .b-gridbase:not(.b-column-resizing):not(.b-row-reordering) .b-grid-header-container:not(.b-dragging-header) .b-depth-0:focus{
  background:transparent;
}
.b-timelinehistogram .b-grid-row{
  z-index:3;
}
.b-timelinehistogram .b-timelinehistogram-cell{
  padding:0;
  border-inline-end-width:0;
}
.b-timelinehistogram .b-timeline-subgrid .b-grid-cell.b-focused::after{
  display:none;
}

.b-timelinehistogram-histogram{
  padding:0;
  background-color:transparent;
}
.b-sch-column.b-grid-header{
  padding:0;
}

.b-grid-header-container .b-sch-timeaxiscolumn{
  -webkit-user-select:none;
  user-select:none;
  padding:0;
  flex-grow:unset !important;
  background-color:#e0e0e7;
  border-inline-end:0 none;
}
.b-grid-header-container .b-sch-timeaxiscolumn .b-sch-header-timeaxis-cell:hover{
  background-color:#eeeef2;
}

.b-horizontaltimeaxis{
  flex-direction:column;
  flex:1 0 100%;
}
.b-horizontaltimeaxis .b-sch-header-row{
  flex:1 0 2em;
}
.b-horizontaltimeaxis .b-sch-header-row.b-sch-header-row-0.b-lowest{
  flex:1;
}
.b-horizontaltimeaxis .b-sch-header-timeaxis-cell{
  height:100%;
  border-inline-start:1px solid #b0b0b7;
}
.b-horizontaltimeaxis .b-sch-header-timeaxis-cell:hover{
  cursor:pointer;
  background-color:#eeeef2;
}
.b-horizontaltimeaxis .b-sch-header-timeaxis-cell.b-last .b-sch-header-text{
  overflow:hidden;
}

.b-sch-timeaxiscolumn-levels-1{
  min-height:1.5em;
}

.b-sch-timeaxiscolumn-levels-2{
  min-height:3em;
}

.b-sch-timeaxiscolumn-levels-3{
  min-height:4.5em;
}

.b-sch-header-row{
  position:relative;
  contain:strict;
}
.b-sch-header-row:last-child .b-sch-header-timeaxis-cell{
  border-bottom:none;
}

.b-sch-header-timeaxis-cell{
  display:flex;
  justify-content:center;
  position:absolute;
  color:#262626;
  border-bottom:1px solid #b0b0b7;
  overflow:hidden;
  transition:background-color 0.2s;
  font-size:1em;
  font-weight:inherit;
  padding:top(0.5em) 0 bottom(0.5em) 0;
  white-space:nowrap;
  contain:strict;
  -webkit-user-select:none;
  user-select:none;
}
.b-sch-header-timeaxis-cell.b-align-start .b-sch-header-text, .b-sch-header-timeaxis-cell.b-align-end .b-sch-header-text{
  padding-inline-start:left(0 0.5em 0 0.75em);
  padding-inline-end:right(0 0.5em 0 0.75em);
}
.b-sch-header-timeaxis-cell.b-align-start{
  justify-content:flex-start;
}
.b-sch-header-timeaxis-cell.b-align-end{
  justify-content:flex-end;
}

.b-schedulerbase.b-fill-last-column .b-sch-timeaxiscolumn:last-child{
  border-inline-end-color:#b0b0b7;
}

.b-sch-header-timeaxis-cell[data-tick-index="0"]{
  border-inline-start:0;
}

.b-timelinebase:not(.b-sch-vertical) .b-grid-header-container{
  border-bottom:0;
}
.b-timelinebase:not(.b-sch-vertical) .b-header:not(.b-grid-header-scroller-normal) .b-grid-headers,
.b-timelinebase:not(.b-sch-vertical) .b-horizontaltimeaxis{
  border-bottom:1px solid #b0b0b7;
}
.b-verticaltimeaxiscolumn{
  flex-flow:column nowrap;
  align-items:stretch;
  padding:0;
  border-inline-end:none;
  background-color:#e9e9ee !important;
}
.b-verticaltimeaxiscolumn.b-grid-cell.b-focused:after{
  display:none;
}

.b-verticaltimeaxis{
  flex:1 0 100%;
  contain:strict;
}
.b-verticaltimeaxis .b-sch-header-row{
  flex-direction:column;
  flex:1;
}
.b-verticaltimeaxis .b-sch-header-timeaxis-cell{
  width:100%;
  text-transform:none;
  padding:0;
  border-inline-start:0 none;
}
.b-verticaltimeaxis .b-sch-header-row.b-lowest .b-sch-header-timeaxis-cell{
  border-inline-end:none;
  border-bottom:none;
  justify-content:flex-end;
  align-items:flex-start;
  contain:unset;
}
.b-verticaltimeaxis .b-sch-header-row.b-lowest .b-sch-header-timeaxis-cell .b-sch-header-text{
  padding-inline-end:0.5em;
}
body:not(.b-export) .b-verticaltimeaxis .b-sch-header-row.b-lowest .b-sch-header-timeaxis-cell .b-sch-header-text{
  margin-top:-0.75em;
}
.b-verticaltimeaxis .b-sch-header-row.b-lowest .b-sch-header-timeaxis-cell:hover{
  background-color:#e0e0e7;
}
.b-verticaltimeaxis .b-sch-header-row:not(.b-lowest){
  flex:0 0 2em;
}
.b-verticaltimeaxis .b-sch-header-row:not(.b-lowest) .b-sch-header-timeaxis-cell{
  align-items:flex-start;
  border-inline-end:1px solid #b0b0b7;
}
.b-verticaltimeaxis .b-sch-header-row:not(.b-lowest) .b-sch-header-text{
  padding-bottom:left(0 0.5em 0 0.75em);
  writing-mode:vertical-lr;
  -ms-writing-mode:tb-lr;
  transform:rotate(180deg);
}

.b-sch-vertical .b-sticky-headers .b-content-element,
.b-sch-vertical .b-sticky-headers .b-grid-subgrid-locked,
.b-sch-vertical .b-sticky-headers .b-verticaltimeaxis-row,
.b-sch-vertical .b-sticky-headers .b-verticaltimeaxiscolumn,
.b-sch-vertical .b-sticky-headers .b-verticaltimeaxis,
.b-sch-vertical .b-sticky-headers .b-sch-header-row,
.b-sch-vertical .b-sticky-headers .b-sch-header-timeaxis-cell{
  overflow:visible;
}
.b-sch-vertical .b-sch-timeaxiscolumn{
  flex-direction:row;
  min-height:3em;
}

.b-resourceheader{
  flex:1;
  position:relative;
  contain:strict;
}
.b-resourceheader.b-has-images{
  min-height:3em;
}
.b-resourceheader.b-grouped{
  height:6em;
}

.b-resourceheader-group-cell{
  position:absolute;
  display:flex;
  align-items:stretch;
  flex-direction:column;
  height:100%;
}
.b-resourceheader-group-cell > span{
  padding:0.5em 0;
  display:flex;
  width:100%;
  justify-content:center;
  align-items:center;
  border-bottom:1px solid #b0b0b7;
  border-inline-end:1px solid #b0b0b7;
}
.b-resourceheader-group-cell .b-resourceheader-group-children{
  position:relative;
  height:100%;
}

.b-resourceheader-cell{
  position:absolute;
  display:flex;
  justify-content:center;
  align-items:center;
  color:#262626;
  border-inline-end:1px solid #b0b0b7;
  overflow:hidden;
  transition:background-color 0.2s;
  font-size:1em;
  font-weight:inherit;
  padding:top(0.5em);
  white-space:nowrap;
  contain:strict;
  height:100%;
  -webkit-user-select:none;
  user-select:none;
}
.b-animating .b-resourceheader-cell{
  transition:width 0.2s, left 0.2s, right 0.2s;
}
.b-resourceheader-cell:hover{
  cursor:pointer;
  background-color:#eeeef2;
}
.b-resourceheader-cell.b-align-start, .b-resourceheader-cell.b-align-end{
  padding-inline:0.3em;
}
.b-resourceheader-cell.b-align-start{
  justify-content:flex-start;
}
.b-resourceheader-cell.b-align-end{
  justify-content:flex-end;
}
.b-resourceheader-cell i{
  margin-inline-end:0.5em;
}
.b-resourceheader-cell .b-resource-name{
  overflow:hidden;
}
.b-resourceheader-cell .b-resource-avatar{
  margin-inline-end:1em;
}

.b-recurrenceconfirmationpopup.b-popup{
  width:29em;
  max-width:none;
}
.b-recurrenceconfirmationpopup.b-popup .b-recurrenceconfirmationpopup-header,
.b-recurrenceconfirmationpopup.b-popup .b-recurrenceconfirmationpopup-content{
  padding:0.65em;
}
.b-recurrenceconfirmationpopup.b-popup .b-bottom-toolbar > button{
  margin-inline-end:0.5em;
}
.b-recurrenceconfirmationpopup.b-popup .b-bottom-toolbar > button:last-child{
  margin-inline-end:0;
}

button.b-button.b-recurrencelegendbutton{
  white-space:normal;
}
.b-recurrenceeditor .b-panel{
  --panel-background-color:inherit;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content{
  padding:0;
  column-gap:1em;
  display:grid;
  grid-template-columns:auto 1fr 1fr;
  justify-content:normal;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content > .b-field:not(.b-no-span):not(.b-label-with-checkbox):not(.b-buttongroup){
  display:contents;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content > .b-field:not(.b-no-span):not(.b-label-with-checkbox):not(.b-buttongroup):not(.b-no-inner-span) .b-field-inner{
  grid-column:span 2;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content > .b-field[data-ref=emptyMonthsHiddenField] .b-field-inner{
  border-width:0;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-recurrencedayscombo, .b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-recurrencepositionscombo{
  width:auto;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-buttongroup{
  gap:0.5em;
  grid-column:span 2;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-buttongroup.b-recurrencedaysbuttongroup .b-button{
  flex:1 1 100%;
  min-width:auto;
  padding-inline:0;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-buttongroup.b-recurrencemonthsbuttongroup{
  display:grid;
  grid-template-columns:repeat(4, 1fr);
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-buttongroup.b-recurrencemonthdaysbuttongroup{
  display:grid;
  grid-template-columns:repeat(7, 1fr);
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-buttongroup.b-recurrencemonthdaysbuttongroup .b-button{
  min-height:2em;
  min-width:0;
  padding:0;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-space-above{
  margin-block-start:1.5em;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-space-below{
  margin-block-end:1.5em;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-button-group-label{
  margin-inline-end:1em;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-checkbox.b-label-with-checkbox .b-field-inner{
  justify-content:flex-end;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-checkbox.b-label-with-checkbox .b-label{
  flex:1 1 100%;
}
.b-recurrenceeditor .b-recurrenceeditorpanel-content.b-panel-content .b-combo-picker .b-recurrencepositions-split{
  border-top-style:solid;
  border-top-color:#000;
}
.b-recurrenceeditor .b-bottom-toolbar .b-label-filler{
  display:none;
}
.b-recurrenceeditor .b-bottom-toolbar button{
  flex:1;
}

.b-schedulerpro-taskeditor .b-recurrenceeditor .b-buttongroup{
  font-size:0.8em;
}
.b-daybuttons .b-button.b-pressed{
  box-shadow:none;
}
.b-daybuttons .b-button.b-pressed.b-raised{
  background-image:none;
}
.b-daybuttons .b-button.b-raised{
  border:1px solid rgb(var(--widget-primary-color-rgb));
}

.b-datepicker .b-icon-circle{
  font-size:50%;
  color:#03a9f4;
  margin-top:1px;
}
.b-datepicker .b-selected-date .b-icon-circle{
  color:#fff;
}
.b-datepicker .b-cell-events-badge{
  display:flex;
  align-items:center;
  justify-content:center;
  border-radius:50%;
  font-size:80%;
  background-color:#f44336;
  color:#fff;
  width:1.3em;
  height:1.3em;
}

.b-datepicker-with-events .b-calendar-weekdays{
  padding-bottom:4px;
}
.b-datepicker-with-events .b-calendar-week .b-calendar-cell{
  margin:1px 1px;
  justify-content:flex-start;
}
.b-datepicker-with-events .b-calendar-week .b-calendar-cell .b-datepicker-cell-inner{
  margin-bottom:0.45em;
}

.b-resourcecombo .b-resource-icon{
  display:none;
  margin-inline:0.5em 0;
}
.b-resourcecombo.b-show-event-color:not(.b-uses-chipview) .b-resource-icon{
  display:block;
}

.b-resourcecombo-picker .b-icon{
  display:none;
  margin-inline-end:0.5em;
}
.b-resourcecombo-picker.b-multiselect .b-icon, .b-resourcecombo-picker.b-show-event-color .b-icon{
  display:block;
}
.b-resourcecombo-picker.b-multiselect .b-selected .b-icon-square:before, .b-resourcecombo-picker.b-show-event-color .b-selected .b-icon-square:before{
  content:"\f14a";
}

.b-theme-material .b-resourcecombo .b-resource-icon{
  margin-inline:0 0.5em;
}

.b-resourcefilter .b-list-item{
  border:0 none;
  border-radius:0;
}
.b-resourcefilter .b-selected-icon{
  visibility:visible !important;
}
.b-resourcefilter .b-selected-icon:before{
  content:"\f0c8";
  opacity:0.6;
}
.b-resourcefilter .b-selected .b-selected-icon:before{
  content:"\f14a";
  opacity:1;
}

@keyframes fadeInOpacity{
  0%{
    opacity:0;
  }
  100%{
    opacity:1;
  }
}
.b-sch-red{
  background-color:#ff8787;
}

.b-sch-foreground-red{
  color:#ff8787;
}

.b-sch-pink{
  background-color:#f783ac;
}

.b-sch-foreground-pink{
  color:#f783ac;
}

.b-sch-purple{
  background-color:#ea80dc;
}

.b-sch-foreground-purple{
  color:#ea80dc;
}

.b-sch-magenta{
  background-color:#ff4dff;
}

.b-sch-foreground-magenta{
  color:#ff4dff;
}

.b-sch-violet{
  background-color:#9775fa;
}

.b-sch-foreground-violet{
  color:#9775fa;
}

.b-sch-indigo{
  background-color:#748ffc;
}

.b-sch-foreground-indigo{
  color:#748ffc;
}

.b-sch-blue{
  background-color:#4dadf7;
}

.b-sch-foreground-blue{
  color:#4dadf7;
}

.b-sch-cyan{
  background-color:#3bc9db;
}

.b-sch-foreground-cyan{
  color:#3bc9db;
}

.b-sch-teal{
  background-color:#38d9a9;
}

.b-sch-foreground-teal{
  color:#38d9a9;
}

.b-sch-green{
  background-color:#69db7c;
}

.b-sch-foreground-green{
  color:#69db7c;
}

.b-sch-lime{
  background-color:#a9e34b;
}

.b-sch-foreground-lime{
  color:#a9e34b;
}

.b-sch-gantt-green{
  background-color:#a5d8a7;
}

.b-sch-foreground-gantt-green{
  color:#a5d8a7;
}

.b-sch-yellow{
  background-color:#fdd835;
}

.b-sch-foreground-yellow{
  color:#fdd835;
}

.b-sch-orange{
  background-color:#ffa94d;
}

.b-sch-foreground-orange{
  color:#ffa94d;
}

.b-sch-deep-orange{
  background-color:#ff7043;
}

.b-sch-foreground-deep-orange{
  color:#ff7043;
}

.b-sch-gray{
  background-color:#a0a0a0;
}

.b-sch-foreground-gray{
  color:#a0a0a0;
}

.b-sch-light-gray{
  background-color:#e0e0e7;
}

.b-sch-foreground-light-gray{
  color:#e0e0e7;
}

.b-sch-foreground-black{
  color:#000;
}

.b-sch-color-red{
  --event-primary-color-h:0deg;
  --event-primary-color-s:100%;
  --event-primary-color-l:76.4705882353%;
}

.b-sch-color-pink{
  --event-primary-color-h:338.7931034483deg;
  --event-primary-color-s:87.8787878788%;
  --event-primary-color-l:74.1176470588%;
}

.b-sch-color-purple{
  --event-primary-color-h:307.9245283019deg;
  --event-primary-color-s:71.6216216216%;
  --event-primary-color-l:70.9803921569%;
}

.b-sch-color-violet{
  --event-primary-color-h:255.3383458647deg;
  --event-primary-color-s:93.006993007%;
  --event-primary-color-l:71.9607843137%;
}

.b-sch-color-indigo{
  --event-primary-color-h:228.0882352941deg;
  --event-primary-color-s:95.7746478873%;
  --event-primary-color-l:72.1568627451%;
}

.b-sch-color-blue{
  --event-primary-color-h:206.1176470588deg;
  --event-primary-color-s:91.3978494624%;
  --event-primary-color-l:63.5294117647%;
}

.b-sch-color-cyan{
  --event-primary-color-h:186.75deg;
  --event-primary-color-s:68.9655172414%;
  --event-primary-color-l:54.5098039216%;
}

.b-sch-color-magenta{
  --event-primary-color-h:300deg;
  --event-primary-color-s:100%;
  --event-primary-color-l:65.0980392157%;
}

.b-sch-color-teal{
  --event-primary-color-h:162.1118012422deg;
  --event-primary-color-s:67.9324894515%;
  --event-primary-color-l:53.5294117647%;
}

.b-sch-color-green{
  --event-primary-color-h:130deg;
  --event-primary-color-s:61.2903225806%;
  --event-primary-color-l:63.5294117647%;
}

.b-sch-color-lime{
  --event-primary-color-h:82.8947368421deg;
  --event-primary-color-s:73.0769230769%;
  --event-primary-color-l:59.2156862745%;
}

.b-sch-color-yellow{
  --event-primary-color-h:48.9deg;
  --event-primary-color-s:98.0392156863%;
  --event-primary-color-l:60%;
}

.b-sch-color-orange{
  --event-primary-color-h:31.0112359551deg;
  --event-primary-color-s:100%;
  --event-primary-color-l:65.0980392157%;
}

.b-sch-color-deep-orange{
  --event-primary-color-h:14.3617021277deg;
  --event-primary-color-s:100%;
  --event-primary-color-l:63.137254902%;
}

.b-sch-color-gray{
  --event-primary-color-h:0deg;
  --event-primary-color-s:0%;
  --event-primary-color-l:62.7450980392%;
}

.b-sch-color-light-gray{
  --event-primary-color-h:240deg;
  --event-primary-color-s:12.7272727273%;
  --event-primary-color-l:89.2156862745%;
}

.b-sch-color-gantt-green{
  --event-primary-color-h:122.3529411765deg;
  --event-primary-color-s:39.5348837209%;
  --event-primary-color-l:74.7058823529%;
}

.b-sch-color-black{
  --event-primary-color-h:0deg;
  --event-primary-color-s:0%;
  --event-primary-color-l:0%;
}

.b-sch-style-interday > .b-sch-event,
.b-sch-style-interday > .b-sch-event > .b-sch-event-segments > .b-sch-event,
.b-sch-style-plain > .b-sch-event,
.b-sch-style-plain > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  --event-s-factor:1;
  --event-l-factor:1;
  --event-hover-s-factor:1.3;
  --event-hover-l-factor:0.6;
  --event-selected-hover-s-factor:1.4;
  --event-selected-hover-l-factor:0.55;
}

.b-sch-style-interday .b-sch-event{
  opacity:0.8;
  border-radius:3px;
}
.b-sch-style-interday .b-sch-event-content{
  font-weight:400;
}

.b-sch-style-plain.b-sch-custom-color > .b-sch-event:not(.b-milestone){
  background-color:currentColor;
}
.b-sch-style-plain.b-sch-custom-color > .b-sch-event:not(.b-milestone):hover, .b-sch-style-plain.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected, .b-sch-style-plain.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected:hover{
  background-color:currentColor;
  background-image:linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1));
}
.b-sch-style-plain.b-sch-custom-color > .b-sch-event:not(.b-milestone) > .b-sch-event-content{
  color:#fff;
}
.b-sch-style-plain.b-sch-custom-color > .b-sch-event.b-milestone .b-sch-event-content::before{
  background-color:currentColor;
}
.b-sch-style-plain.b-sch-custom-color > .b-sch-event.b-milestone:hover .b-sch-event-content::before, .b-sch-style-plain.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected .b-sch-event-content::before, .b-sch-style-plain.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected:hover .b-sch-event-content::before{
  background-image:linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1));
}

.b-sch-layout-milestone-text-position-inside .b-sch-event-wrap.b-milestone-wrap .b-milestone{
  display:flex;
  justify-content:center;
  align-items:center;
}
.b-sch-layout-milestone-text-position-inside .b-sch-event-wrap.b-milestone-wrap .b-milestone label{
  position:absolute;
  left:50%;
  transform:translateX(-50%);
  color:inherit;
}

.b-sch-style-border > .b-sch-event,
.b-sch-style-border > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  --event-border-color:hsl(
                  var(--event-background-h),
                  var(--event-background-s),
                  calc(var(--event-background-l) * 0.7)
  );
  --event-hover-color:#fff;
  --event-hover-l-factor:0.7;
  --event-selected-hover-l-factor:0.6;
  --event-border-style:solid;
  --event-border-radius:3px;
  --event-border-width:1px;
  --event-opacity:.8;
  --event-hover-opacity:1;
}

.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-sch-style-border > .b-sch-event.b-milestone > .b-sch-event-content{
  font-size:12px;
}

.b-sch-style-border.b-sch-custom-color > .b-sch-event:not(.b-milestone){
  background-color:currentColor;
  background-image:linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2));
  border-color:currentColor;
}
.b-sch-style-border.b-sch-custom-color > .b-sch-event:not(.b-milestone) .b-sch-event-content{
  color:#fff;
}
.b-sch-style-border.b-sch-custom-color > .b-sch-event:not(.b-milestone):hover, .b-sch-style-border.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-resizing, .b-sch-style-border.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected, .b-sch-style-border.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected:hover{
  background-color:currentColor;
  background-image:none;
  border-color:currentColor;
}
.b-sch-style-border.b-sch-custom-color > .b-sch-event.b-milestone .b-sch-event-content::before{
  background-color:currentColor;
  background-image:linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2));
  border-color:currentColor;
}

.b-sch-style-hollow > .b-sch-event,
.b-sch-style-hollow > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  --event-s-factor:0;
  --event-l-factor:2;
  --event-a:0.2;
  --event-hover-s-factor:1;
  --event-hover-l-factor:1;
  --event-hover-a:1;
  --event-selected-hover-l-factor:0.8;
  --event-color:var(--event-primary-color);
  --event-hover-color:#fff;
  --event-border-width:2px;
  --event-border-radius:3px;
  --event-opacity:.8;
}

.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-sch-style-hollow > .b-sch-event.b-milestone > .b-sch-event-content{
  font-size:12px;
  font-weight:500;
}

.b-sch-style-hollow.b-sch-custom-color > .b-sch-event{
  border-color:currentColor;
}
.b-sch-style-hollow.b-sch-custom-color > .b-sch-event:not(.b-milestone):hover, .b-sch-style-hollow.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected, .b-sch-style-hollow.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-resizing, .b-sch-style-hollow.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected:hover{
  background-color:currentColor;
}
.b-sch-style-hollow.b-sch-custom-color > .b-sch-event:not(.b-milestone):hover .b-sch-event-content, .b-sch-style-hollow.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected .b-sch-event-content, .b-sch-style-hollow.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-resizing .b-sch-event-content, .b-sch-style-hollow.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected:hover .b-sch-event-content{
  color:#fff;
}
.b-sch-style-hollow.b-sch-custom-color > .b-sch-event.b-milestone .b-sch-event-content::before{
  background-color:rgba(255, 255, 255, 0.5);
}
.b-sch-style-hollow.b-sch-custom-color > .b-sch-event.b-milestone:hover .b-sch-event-content::before, .b-sch-style-hollow.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected .b-sch-event-content::before, .b-sch-style-hollow.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-resizing .b-sch-event-content::before, .b-sch-style-hollow.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected:hover .b-sch-event-content::before{
  background-color:currentColor;
}

.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event,
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event,
.b-sch-style-colored.b-sch-event-wrap > .b-sch-event,
.b-sch-style-colored.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  --event-background-l:90%;
  --event-border-width:3px;
  --event-color:hsl(var(--event-primary-color-h), var(--event-primary-color-s), calc(var(--event-primary-color-l) * 0.8));
  --event-border-color:hsl(var(--event-primary-color-h), var(--event-primary-color-s), calc(var(--event-primary-color-l) * 0.9));
  --event-opacity:0.8;
  --event-selected-hover-l-factor:0.8;
}
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event:hover,
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event:hover,
.b-sch-style-colored.b-sch-event-wrap > .b-sch-event:hover,
.b-sch-style-colored.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event:hover{
  --event-background-l:85%;
}
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event.b-sch-event-selected,
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-sch-event-selected,
.b-sch-style-colored.b-sch-event-wrap > .b-sch-event.b-sch-event-selected,
.b-sch-style-colored.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-sch-event-selected{
  --event-background-l:var(--event-primary-color-l);
  --event-color:#fff;
}
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event:not(.b-milestone),
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone),
.b-sch-style-colored.b-sch-event-wrap > .b-sch-event:not(.b-milestone),
.b-sch-style-colored.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone){
  border-block-width:0;
  border-inline-end-width:0;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event.b-milestone,
.b-schedulerbase.b-sch-layout-milestones .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-milestone,
.b-schedulerbase.b-sch-layout-milestones .b-sch-style-colored.b-sch-event-wrap > .b-sch-event.b-milestone,
.b-schedulerbase.b-sch-layout-milestones .b-sch-style-colored.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-milestone{
  --event-border-width:0px;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event.b-milestone::before,
.b-schedulerbase.b-sch-layout-milestones .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-milestone::before,
.b-schedulerbase.b-sch-layout-milestones .b-sch-style-colored.b-sch-event-wrap > .b-sch-event.b-milestone::before,
.b-schedulerbase.b-sch-layout-milestones .b-sch-style-colored.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-milestone::before{
  --event-border-width:3px;
}
.b-sch-vertical .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event:not(.b-milestone),
.b-sch-vertical .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone),
.b-sch-vertical .b-sch-style-colored.b-sch-event-wrap > .b-sch-event:not(.b-milestone),
.b-sch-vertical .b-sch-style-colored.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone){
  border-top-width:3px;
  border-inline-width:0;
}

.b-sch-style-calendar.b-sch-custom-color > .b-sch-event,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event{
  border-color:currentColor;
}
.b-sch-style-calendar.b-sch-custom-color > .b-sch-event:not(.b-milestone),
.b-sch-style-colored.b-sch-custom-color > .b-sch-event:not(.b-milestone){
  background-color:currentColor;
  background-image:linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8));
}
.b-sch-style-calendar.b-sch-custom-color > .b-sch-event:not(.b-milestone):hover, .b-sch-style-calendar.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-resizing, .b-sch-style-calendar.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event:not(.b-milestone):hover,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-resizing,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected{
  background-color:currentColor;
  background-image:linear-gradient(rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.4));
}
.b-sch-style-calendar.b-sch-custom-color > .b-sch-event:not(.b-milestone):hover .b-sch-event-content, .b-sch-style-calendar.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-resizing .b-sch-event-content, .b-sch-style-calendar.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected .b-sch-event-content,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event:not(.b-milestone):hover .b-sch-event-content,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-resizing .b-sch-event-content,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected .b-sch-event-content{
  color:#fff;
}
.b-sch-style-calendar.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected, .b-sch-style-calendar.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected:hover,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected:hover{
  background-color:currentColor;
  background-image:none;
}
.b-sch-style-calendar.b-sch-custom-color > .b-sch-event.b-milestone .b-sch-event-content::before,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event.b-milestone .b-sch-event-content::before{
  background-color:currentColor;
  background-image:linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8));
}
.b-sch-style-calendar.b-sch-custom-color > .b-sch-event.b-milestone:hover .b-sch-event-content::before, .b-sch-style-calendar.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-resizing .b-sch-event-content::before, .b-sch-style-calendar.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected .b-sch-event-content::before,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event.b-milestone:hover .b-sch-event-content::before,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-resizing .b-sch-event-content::before,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected .b-sch-event-content::before{
  background-image:linear-gradient(rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.4));
}
.b-sch-style-calendar.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected .b-sch-event-content::before, .b-sch-style-calendar.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected:hover .b-sch-event-content::before,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected .b-sch-event-content::before,
.b-sch-style-colored.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected:hover .b-sch-event-content::before{
  background-image:none;
}

.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event, .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  --event-background-l:97%;
  --event-selected-hover-l-factor:1;
  opacity:1;
}
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event:hover, .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event:hover{
  --event-background-l:89%;
}
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event.b-sch-event-selected, .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-sch-event-selected{
  --event-background-l:84%;
}
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event.b-sch-event-selected .b-sch-event-content *, .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-sch-event-selected .b-sch-event-content *{
  font-weight:400;
}
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event .b-sch-event-content *, .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event .b-sch-event-content *{
  font-weight:400;
  color:#606060;
}
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event.b-sch-event-selected .b-sch-event-content *, .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-sch-event-selected .b-sch-event-content *{
  color:#606060;
}
.b-sch-style-calendar.b-sch-event-wrap > .b-sch-event:not(.b-milestone) .b-sch-event-content, .b-sch-style-calendar.b-sch-event-wrap > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone) .b-sch-event-content{
  margin:0;
  padding:0.3em;
}

.b-sch-event-wrap.b-sch-style-line > .b-sch-event,
.b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  --event-background-color:transparent;
  --event-border-color:hsl(var(--event-primary-color-h), var(--event-primary-color-s), calc(var(--event-primary-color-l) * var(--event-l-factor)));
  --event-color:#777;
  --event-hover-color:#777;
  --event-border-width:5px;
  --event-hover-l-factor:0.8;
  --event-selected-hover-l-factor:0.7;
  justify-content:center;
  overflow:visible;
  height:auto !important;
}
.b-sch-event-wrap.b-sch-style-line > .b-sch-event .b-sch-event-content,
.b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event .b-sch-event-content{
  padding:0;
}
.b-sch-event-wrap.b-sch-style-line > .b-sch-event.b-active,
.b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-active{
  outline-offset:5px;
}
.b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone),
.b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone){
  align-self:center;
}
.b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone)::before, .b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone)::after,
.b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone)::before,
.b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone)::after{
  content:"";
  border-color:inherit;
  border-style:solid;
  border-width:0;
  position:absolute;
  border-radius:2px;
  --handle-width:unset;
  --handle-height:unset;
  --handle-inset:unset;
  --handle-opacity:unset;
  --clip-path:unset;
}
.b-sch-horizontal .b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone)::before, .b-sch-horizontal .b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone)::after,
.b-sch-horizontal .b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone)::before,
.b-sch-horizontal .b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone)::after{
  top:-9px;
  height:13px;
  border-inline-start-width:5px;
}
.b-sch-vertical .b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone)::before, .b-sch-vertical .b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone)::after,
.b-sch-vertical .b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone)::before,
.b-sch-vertical .b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone)::after{
  left:-9px;
  width:13px;
  border-top-width:5px;
}
.b-sch-horizontal .b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone)::before,
.b-sch-horizontal .b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone)::before{
  left:0;
}
.b-sch-vertical .b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone)::before,
.b-sch-vertical .b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone)::before{
  border-inline-start-width:5px;
  top:0;
}
.b-sch-horizontal .b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone)::after,
.b-sch-horizontal .b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone)::after{
  top:-9px;
  right:0;
  height:13px;
}
.b-sch-vertical .b-sch-event-wrap.b-sch-style-line > .b-sch-event:not(.b-milestone)::after,
.b-sch-vertical .b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone)::after{
  bottom:0;
}
.b-sch-event-wrap.b-sch-style-line > .b-sch-event.b-milestone,
.b-sch-event-wrap.b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-milestone{
  top:initial;
}

.b-sch-style-line.b-sch-custom-color > .b-sch-event{
  border-color:currentColor;
  opacity:0.8;
}
.b-sch-style-line.b-sch-custom-color > .b-sch-event:hover, .b-sch-style-line.b-sch-custom-color > .b-sch-event.b-sch-event-selected, .b-sch-style-line.b-sch-custom-color > .b-sch-event.b-sch-event-resizing, .b-sch-style-line.b-sch-custom-color > .b-sch-event.b-sch-event-selected:hover{
  border-color:currentColor;
  opacity:1;
}

.b-sch-event-wrap.b-sch-style-dashed > .b-sch-event,
.b-sch-event-wrap.b-sch-style-dashed > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  --event-background-color:transparent;
  --event-border-color:hsl(var(--event-primary-color-h), var(--event-primary-color-s), calc(var(--event-primary-color-l) * var(--event-l-factor)));
  --event-color:#777;
  --event-hover-color:#777;
  --event-border-width:5px;
  --event-border-style:dashed;
  --event-hover-l-factor:0.8;
  --event-selected-hover-l-factor:0.7;
  justify-content:center;
  overflow:visible;
}
.b-sch-horizontal .b-sch-event-wrap.b-sch-style-dashed > .b-sch-event,
.b-sch-horizontal .b-sch-event-wrap.b-sch-style-dashed > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  padding:5px 0 3px 0;
}
.b-sch-event-wrap.b-sch-style-dashed > .b-sch-event .b-sch-event-content,
.b-sch-event-wrap.b-sch-style-dashed > .b-sch-event > .b-sch-event-segments > .b-sch-event .b-sch-event-content{
  padding:0;
}
.b-sch-event-wrap.b-sch-style-dashed > .b-sch-event:not(.b-milestone),
.b-sch-event-wrap.b-sch-style-dashed > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone){
  align-self:center;
}
.b-sch-event-wrap.b-sch-style-dashed > .b-sch-event.b-milestone,
.b-sch-event-wrap.b-sch-style-dashed > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-milestone{
  top:initial;
}

.b-sch-style-dashed.b-sch-custom-color > .b-sch-event{
  border-color:currentColor;
  opacity:0.8;
}
.b-sch-style-dashed.b-sch-custom-color > .b-sch-event:hover, .b-sch-style-dashed.b-sch-custom-color > .b-sch-event.b-sch-event-selected, .b-sch-style-dashed.b-sch-custom-color > .b-sch-event.b-sch-event-resizing, .b-sch-style-dashed.b-sch-custom-color > .b-sch-event.b-sch-event-selected:hover{
  border-color:currentColor;
  opacity:1;
}

.b-sch-event-wrap.b-sch-style-minimal > .b-sch-event, .b-sch-event-wrap.b-sch-style-minimal > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  --event-background-color:transparent;
  --event-border-color:hsl(var(--event-primary-color-h), var(--event-primary-color-s), calc(var(--event-primary-color-l) * var(--event-l-factor)));
  --event-color:#777;
  --event-hover-color:#777;
  --event-border-width:1px;
  --event-hover-l-factor:0.8;
  --event-selected-hover-l-factor:0.7;
  overflow:visible;
  padding:0;
}
.b-sch-event-wrap.b-sch-style-minimal > .b-sch-event:not(.b-milestone), .b-sch-event-wrap.b-sch-style-minimal > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone){
  font-size:10px;
}
.b-sch-horizontal .b-sch-event-wrap.b-sch-style-minimal > .b-sch-event:not(.b-milestone), .b-sch-horizontal .b-sch-event-wrap.b-sch-style-minimal > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone){
  border-width:0 0 var(--event-border-width) 0;
  align-items:flex-end;
}
.b-sch-vertical .b-sch-event-wrap.b-sch-style-minimal > .b-sch-event:not(.b-milestone), .b-sch-vertical .b-sch-event-wrap.b-sch-style-minimal > .b-sch-event > .b-sch-event-segments > .b-sch-event:not(.b-milestone){
  border-width:0;
  border-inline-start-width:var(--event-border-width);
  align-items:flex-start;
  padding-inline-start:0.75em;
}
.b-sch-event-wrap.b-sch-style-minimal > .b-sch-event.b-milestone .b-sch-event-content, .b-sch-event-wrap.b-sch-style-minimal > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-milestone .b-sch-event-content{
  font-size:20px;
  left:10px;
  position:relative;
}
.b-sch-event-wrap.b-sch-style-minimal > .b-sch-event.b-milestone .b-sch-event-content label, .b-sch-event-wrap.b-sch-style-minimal > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-milestone .b-sch-event-content label{
  font-size:10px;
  left:70%;
}

.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-sch-style-minimal > .b-sch-event.b-milestone{
  height:20px;
  font-size:20px;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-sch-style-minimal > .b-sch-event.b-milestone .b-sch-event-content{
  font-size:10px;
  left:0;
}

.b-sch-style-minimal.b-sch-custom-color > .b-sch-event{
  border-color:currentColor;
  opacity:0.8;
}
.b-sch-style-minimal.b-sch-custom-color > .b-sch-event .b-sch-event-content{
  color:#777;
}
.b-sch-style-minimal.b-sch-custom-color > .b-sch-event:hover, .b-sch-style-minimal.b-sch-custom-color > .b-sch-event.b-sch-event-selected, .b-sch-style-minimal.b-sch-custom-color > .b-sch-event.b-sch-event-resizing, .b-sch-style-minimal.b-sch-custom-color > .b-sch-event.b-sch-event-selected:hover{
  border-color:currentColor;
  opacity:1;
}

.b-sch-horizontal .b-sch-style-line > .b-sch-event,
.b-sch-horizontal .b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event,
.b-sch-horizontal .b-sch-style-dashed > .b-sch-event,
.b-sch-horizontal .b-sch-style-dashed > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  top:5px;
  height:auto !important;
  border-width:5px 0 0 0;
}
.b-sch-vertical .b-sch-style-line > .b-sch-event,
.b-sch-vertical .b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event,
.b-sch-vertical .b-sch-style-dashed > .b-sch-event,
.b-sch-vertical .b-sch-style-dashed > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  left:0;
  border-width:0 0 0 5px;
  flex-direction:column;
}
.b-sch-vertical .b-sch-style-line > .b-sch-event .b-sch-event-content,
.b-sch-vertical .b-sch-style-line > .b-sch-event > .b-sch-event-segments > .b-sch-event .b-sch-event-content,
.b-sch-vertical .b-sch-style-dashed > .b-sch-event .b-sch-event-content,
.b-sch-vertical .b-sch-style-dashed > .b-sch-event > .b-sch-event-segments > .b-sch-event .b-sch-event-content{
  margin:0;
}

.b-sch-event-wrap.b-sch-style-rounded > .b-sch-event, .b-sch-event-wrap.b-sch-style-rounded > .b-sch-event > .b-sch-event-segments > .b-sch-event{
  --event-background-l:80%;
  --event-a:0.6;
  --event-hover-a:1;
  --event-hover-l-factor:0.8;
  --event-selected-hover-l-factor:0.9;
  --event-color:hsl(var(--event-primary-color-h), var(--event-primary-color-s), calc(var(--event-primary-color-l) * 0.6));
  --event-border-color:var(--event-primary-color);
  --event-border-radius:2em;
}
.b-sch-event-wrap.b-sch-style-rounded > .b-sch-event.b-sch-event-selected, .b-sch-event-wrap.b-sch-style-rounded > .b-sch-event > .b-sch-event-segments > .b-sch-event.b-sch-event-selected{
  --event-background-l:var(--event-primary-color-l);
  --event-a:1;
  --event-color:#fff;
}
.b-sch-event-wrap.b-sch-style-rounded > .b-sch-event.b-milestone{
  --event-border-radius:1em;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-sch-style-rounded .b-sch-event.b-milestone{
  --event-border-radius:3px;
  --event-a:1;
  --event-background-l:85%;
}
.b-schedulerbase.b-sch-layout-milestones .b-sch-event-wrap.b-sch-style-rounded .b-sch-event.b-milestone > .b-sch-event-content{
  font-size:11px;
}

.b-sch-horizontal .b-sch-event-wrap.b-sch-style-rounded:not(.b-milestone) .b-sch-event-content{
  margin:0 1em;
}
.b-sch-vertical .b-sch-event-wrap.b-sch-style-rounded:not(.b-milestone) .b-sch-event-content{
  margin:1em 0;
}

.b-sch-event-wrap.b-sch-style-rounded.b-sch-custom-color > .b-sch-event:not(.b-milestone){
  background-color:currentColor;
  background-image:linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8));
}
.b-sch-event-wrap.b-sch-style-rounded.b-sch-custom-color > .b-sch-event:not(.b-milestone):hover{
  background-color:currentColor;
  background-image:linear-gradient(rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6));
}
.b-sch-event-wrap.b-sch-style-rounded.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected{
  background-image:none;
}
.b-sch-event-wrap.b-sch-style-rounded.b-sch-custom-color > .b-sch-event:not(.b-milestone).b-sch-event-selected .b-sch-event-content{
  color:#fff;
}
.b-sch-event-wrap.b-sch-style-rounded.b-sch-custom-color > .b-sch-event.b-milestone .b-sch-event-content{
  background-color:currentColor;
  background-image:linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8));
}
.b-sch-event-wrap.b-sch-style-rounded.b-sch-custom-color > .b-sch-event.b-milestone:hover .b-sch-event-content{
  background-image:linear-gradient(rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6));
}
.b-sch-event-wrap.b-sch-style-rounded.b-sch-custom-color > .b-sch-event.b-milestone.b-sch-event-selected .b-sch-event-content{
  background-image:none;
}

/*# sourceMappingURL=scheduler.classic.thin.css.map */