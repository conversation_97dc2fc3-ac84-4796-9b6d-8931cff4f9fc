var __accessCheck = (obj, member, msg) => {
  if (!member.has(obj))
    throw TypeError("Cannot " + msg);
};
var __privateGet = (obj, member, getter) => {
  __accessCheck(obj, member, "read from private field");
  return getter ? getter.call(obj) : member.get(obj);
};
var __privateAdd = (obj, member, value) => {
  if (member.has(obj))
    throw TypeError("Cannot add the same private member more than once");
  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
};
var __privateSet = (obj, member, value, setter) => {
  __accessCheck(obj, member, "write to private field");
  setter ? setter.call(obj, value) : member.set(obj, value);
  return value;
};
import Base from "@bryntum/core-thin/lib/Base.js";
import ProjectModel from "../../model/ProjectModel.js";
import StringHelper from "@bryntum/core-thin/lib/helper/StringHelper.js";
import TimeZoneHelper from "@bryntum/core-thin/lib/helper/TimeZoneHelper.js";
const engineStoreNames = [
  "assignmentStore",
  "dependencyStore",
  "eventStore",
  "resourceStore"
];
var ProjectConsumer_default = (Target) => {
  var _suspendedByRestore, _a;
  return _a = class extends (Target || Base) {
    constructor() {
      super(...arguments);
      __privateAdd(this, _suspendedByRestore, void 0);
    }
    static get $name() {
      return "ProjectConsumer";
    }
    //region Default config
    static get declarable() {
      return ["projectStores"];
    }
    static get configurable() {
      return {
        projectModelClass: ProjectModel,
        /**
         * A {@link Scheduler.model.ProjectModel} instance or a config object. The project holds all Scheduler data.
         * Can be omitted in favor of individual store configs or {@link Scheduler.view.mixin.SchedulerStores#config-crudManager} config.
         *
         * **Note:** In SchedulerPro the project is instance of SchedulerPro.model.ProjectModel class.
         * @prp {Scheduler.model.ProjectModel|ProjectModelConfig} project
         * @typings {ProjectModel|ProjectModelConfig}
         * @category Data
         */
        project: {},
        /**
         * Configure as `true` to destroy the Project and stores when `this` is destroyed.
         * @config {Boolean}
         * @category Data
         */
        destroyStores: null,
        // Will be populated by AttachToProjectMixin which features mix in
        projectSubscribers: []
      };
    }
    //endregion
    startConfigure(config) {
      this.getConfig("project");
      super.startConfigure(config);
    }
    //region Project
    // This is where all the ingestion happens.
    // At config time, the changers inject incoming values into the project config object
    // that we are building. At the end we instantiate the project with all incoming
    // config values filled in.
    changeProject(project, oldProject) {
      const me = this, {
        projectDataNames
      } = me.constructor;
      me.projectCallbacks = /* @__PURE__ */ new Set();
      if (project) {
        me.buildingProjectConfig = true;
        if (!project.isModel) {
          if (me.isConfiguring) {
            me._project = project;
            const { crudManager } = me;
            if (crudManager) {
              this.updateStoresFromCrudManager();
            }
            me.getConfig("projectStores");
            for (const dataName of projectDataNames) {
              me.getConfig(dataName);
            }
          }
          const { eventStore } = project;
          let { _sharedProject: sharedProject } = me;
          if (eventStore && !eventStore.isEventStoreMixin && eventStore.autoLoad && !eventStore.count) {
            eventStore.autoLoad = false;
            me.delayAutoLoad = true;
          }
          if (sharedProject && engineStoreNames.some((store) => project[store] && project[store] !== sharedProject[store])) {
            for (const store of engineStoreNames) {
              if (project[store] && project[store] === sharedProject[store]) {
                project[store] = project[store].chain();
              }
            }
            sharedProject = null;
          }
          project = sharedProject || new me.projectModelClass(project);
          delete me._project;
        }
        me.buildingProjectConfig = false;
      }
      return project;
    }
    updateStoresFromCrudManager() {
      const { crudManager } = this, { isCrudManager } = crudManager;
      for (const storeName of this.constructor.projectStoreNames) {
        if (crudManager[storeName]) {
          this[storeName] = crudManager[storeName];
          if (!isCrudManager) {
            delete crudManager[storeName];
          }
        }
      }
    }
    /**
     * Implement in subclass to take action when project is replaced.
     *
     * __`super.updateProject(...arguments)` must be called first.__
     *
     * @param {Scheduler.model.ProjectModel} project
     * @category Data
     */
    updateProject(project, oldProject) {
      var _a2;
      const me = this, {
        projectListeners,
        crudManager
      } = me;
      me.detachListeners("projectConsumer");
      me.detachListeners("projectDestroy");
      delete me._crudManager;
      if (project) {
        projectListeners.thisObj = me;
        project.ion(projectListeners);
        if (project.isCrudManager) {
          me.crudManager = project;
        } else if (crudManager) {
          crudManager.project = project;
          me.crudManager = crudManager;
        }
        me.projectSubscribers.forEach((subscriber) => {
          subscriber.detachFromProject(oldProject);
          subscriber.attachToProject(project);
        });
        for (const storeName of me.constructor.projectStoreNames) {
          me[storeName] = project[storeName];
        }
        if (me.delayAutoLoad) {
          project.eventStore.autoLoad = true;
          project.eventStore.load();
        }
        (_a2 = project.stm) == null ? void 0 : _a2.ion({
          name: "projectConsumer",
          restoringStart: "onProjectRestoringStart",
          restoringStop: "onProjectRestoringStop",
          thisObj: me
        });
        project.ion({
          name: "projectDestroy",
          beforeDestroy: "onProjectBeforeDestroy",
          thisObj: me
        });
      }
      me.trigger("projectChange", { project });
    }
    // Implementation here because we need to get first look at it to adopt its stores
    changeCrudManager(crudManager) {
      if (this.buildingProjectConfig) {
        this._crudManager = crudManager.isCrudManager ? crudManager : Object.assign({}, crudManager);
      } else {
        return super.changeCrudManager(crudManager);
      }
    }
    updateCrudManager(crudManager) {
      if (!this.isConfiguring && crudManager) {
        this.updateStoresFromCrudManager();
      }
    }
    onProjectBeforeDestroy(event) {
      const emptyProject = new event.source.constructor();
      this.project = emptyProject;
    }
    // Called when project changes are committed, after data is written back to records
    onProjectDataReady() {
      const me = this;
      me.whenVisible(() => {
        if (me.projectCallbacks.size) {
          me.projectCallbacks.forEach((callback) => callback());
          me.projectCallbacks.clear();
        }
      }, null, null, "onProjectDataReady");
    }
    onProjectRestoringStart({ stm }) {
      const { rawQueue } = stm;
      if (rawQueue.length && rawQueue[rawQueue.length - 1].length > 1) {
        __privateSet(this, _suspendedByRestore, true);
        this.suspendRefresh();
      }
    }
    onProjectRestoringStop() {
      if (__privateGet(this, _suspendedByRestore)) {
        __privateSet(this, _suspendedByRestore, false);
        this.resumeRefresh(true);
      }
    }
    // Overridden in CalendarStores.js
    onBeforeTimeZoneChange() {
    }
    // When project changes time zone, change start and end dates
    onTimeZoneChange({ timeZone, oldTimeZone }) {
      const me = this;
      if (me.startDate && me.timeAxis.timeZone !== timeZone) {
        const startDate = oldTimeZone != null ? TimeZoneHelper.fromTimeZone(me.startDate, oldTimeZone) : me.startDate;
        me.startDate = timeZone != null ? TimeZoneHelper.toTimeZone(startDate, timeZone) : startDate;
        me.timeAxis.timeZone = timeZone;
      }
    }
    onStartApplyChangeset() {
      this.suspendRefresh();
    }
    onEndApplyChangeset() {
      this.resumeRefresh(true);
    }
    /**
     * Accepts a callback that will be called when the underlying project is ready (no commit pending and current commit
     * finalized)
     * @param {Function} callback
     * @category Data
     */
    whenProjectReady(callback) {
      if (this.isEngineReady) {
        callback();
      } else {
        this.projectCallbacks.add(callback);
      }
    }
    /**
     * Returns `true` if engine is in a stable calculated state, `false` otherwise.
     * @property {Boolean}
     * @category Misc
     */
    get isEngineReady() {
      var _a2, _b;
      return Boolean((_b = (_a2 = this.project).isEngineReady) == null ? void 0 : _b.call(_a2));
    }
    //endregion
    //region Destroy
    // Cleanup, destroys stores if this.destroyStores is true.
    doDestroy() {
      super.doDestroy();
      if (this.destroyStores) {
        !this.project.isDestroyed && this.project.destroy();
      }
    }
    //endregion
    get projectStores() {
      const { projectStoreNames } = this.constructor;
      return projectStoreNames.map((storeName) => this[storeName]);
    }
    static get projectStoreNames() {
      return Object.keys(this.projectStores);
    }
    static get projectDataNames() {
      return this.projectStoreNames.reduce((result, storeName) => {
        const { dataName } = this.projectStores[storeName];
        if (dataName) {
          result.push(dataName);
        }
        return result;
      }, []);
    }
    static setupProjectStores(cls, meta) {
      const { projectStores } = cls;
      if (projectStores) {
        const projectListeners = {
          name: "projectConsumer",
          dataReady: "onProjectDataReady",
          change: "relayProjectDataChange",
          beforeTimeZoneChange: "onBeforeTimeZoneChange",
          timeZoneChange: "onTimeZoneChange",
          // Next 4 handles only suspend/resume view refresh
          startApplyChangeset: "onStartApplyChangeset",
          endApplyChangeset: "onEndApplyChangeset",
          startApplyingRevision: "onStartApplyChangeset",
          stopApplyingRevision: "onEndApplyChangeset"
        }, storeConfigs = {
          projectListeners
        };
        let previousDataName;
        for (const storeName in projectStores) {
          const { dataName } = projectStores[storeName];
          storeConfigs[storeName] = storeConfigs[dataName] = null;
          if (dataName) {
            Object.defineProperty(meta.class.prototype, dataName, {
              configurable: true,
              // So that Config can add its setter.
              get() {
                var _a2;
                return (_a2 = this.project[storeName]) == null ? void 0 : _a2.records;
              }
            });
            this.createDataUpdater(storeName, dataName, previousDataName, meta);
          }
          this.createStoreDescriptor(meta, storeName, projectStores[storeName], projectListeners);
          previousDataName = dataName;
        }
        this.setupConfigs(meta, storeConfigs);
      }
    }
    static createDataUpdater(storeName, dataName, previousDataName, meta) {
      meta.class.prototype[`update${StringHelper.capitalize(dataName)}`] = function(data) {
        const { project } = this;
        previousDataName && this.getConfig(previousDataName);
        if (this.buildingProjectConfig) {
          project[`${dataName}Data`] = data;
        } else {
          project[storeName].data = data;
        }
      };
    }
    // eslint-disable-next-line bryntum/no-listeners-in-lib
    static createStoreDescriptor(meta, storeName, { listeners }, projectListeners) {
      const { prototype: clsProto } = meta.class, storeNameCap = StringHelper.capitalize(storeName);
      projectListeners[`${storeName}Change`] = function({ store }) {
        this[storeName] = store;
      };
      clsProto[`change${storeNameCap}`] = function(store, oldStore) {
        const me = this, { project } = me, storeProject = store == null ? void 0 : store.project;
        if (me.buildingProjectConfig) {
          if (storeProject == null ? void 0 : storeProject.isProjectModel) {
            me._sharedProject = storeProject;
          }
          project[storeName] = store;
          return;
        }
        if (!me.initializingProject) {
          if (project[storeName] !== store) {
            project[`set${storeNameCap}`](store);
            store = project[storeName];
          }
        }
        if (store !== oldStore) {
          if (listeners) {
            listeners.thisObj = me;
            listeners.name = `${storeName}Listeners`;
            listeners.startApplyChangeset = "onProjectStoreStartApplyChangeset";
            listeners.endApplyChangeset = "onProjectStoreEndApplyChangeset";
            me.detachListeners(listeners.name);
            store.ion(listeners);
          }
          me[`_${storeName}`] = store;
          me.projectSubscribers.forEach((subscriber) => {
            var _a2;
            (_a2 = subscriber[`attachTo${storeNameCap}`]) == null ? void 0 : _a2.call(subscriber, store);
          });
          me[`_${storeName}`] = null;
        }
        return store;
      };
    }
    onProjectStoreStartApplyChangeset() {
      this.suspendRefresh();
    }
    onProjectStoreEndApplyChangeset() {
      this.resumeRefresh(true);
    }
    relayProjectDataChange(event) {
      if ((event.isExpand || event.isCollapse) && !event.records[0].fieldMap.expanded.persist) {
        return;
      }
      return this.trigger("dataChange", { project: event.source, ...event, source: this });
    }
    //region WidgetClass
    // This does not need a className on Widgets.
    // Each *Class* which doesn't need 'b-' + constructor.name.toLowerCase() automatically adding
    // to the Widget it's mixed in to should implement this.
    get widgetClass() {
    }
    //endregion
  }, _suspendedByRestore = new WeakMap(), _a;
};
export {
  ProjectConsumer_default as default
};
