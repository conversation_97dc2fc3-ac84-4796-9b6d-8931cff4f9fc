var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import DragCreateBase from "./base/DragCreateBase.js";
import GridFeatureManager from "@bryntum/grid-thin/lib/feature/GridFeatureManager.js";
import DateHelper from "@bryntum/core-thin/lib/helper/DateHelper.js";
import ObjectHelper from "@bryntum/core-thin/lib/helper/ObjectHelper.js";
import DomHelper from "@bryntum/core-thin/lib/helper/DomHelper.js";
class EventDragCreate extends DragCreateBase {
  //endregion
  //region Events
  /**
   * Fires on the owning Scheduler after the new event has been created.
   * @event dragCreateEnd
   * @on-owner
   * @param {Scheduler.view.Scheduler} source
   * @param {Scheduler.model.EventModel} eventRecord The new `EventModel` record.
   * @param {Scheduler.model.ResourceModel} resourceRecord The resource for the row in which the event is being
   * created.
   * @param {MouseEvent} event The ending mouseup event.
   * @param {HTMLElement} eventElement The DOM element representing the newly created event un the UI.
   */
  /**
   * Fires on the owning Scheduler at the beginning of the drag gesture. Returning `false` from a listener prevents
   * the drag create operation from starting.
   *
   * ```javascript
   * const scheduler = new Scheduler({
   *     listeners : {
   *         beforeDragCreate({ date }) {
   *             // Prevent drag creating events in the past
   *             return date >= Date.now();
   *         }
   *     }
   * });
   * ```
   *
   * @event beforeDragCreate
   * @on-owner
   * @preventable
   * @param {Scheduler.view.Scheduler} source
   * @param {Scheduler.model.ResourceModel} resourceRecord
   * @param {Date} date The datetime associated with the drag start point.
   */
  /**
   * Fires on the owning Scheduler after the drag start has created a new Event record.
   * @event dragCreateStart
   * @on-owner
   * @param {Scheduler.view.Scheduler} source
   * @param {Scheduler.model.EventModel} eventRecord The event record being created
   * @param {Scheduler.model.ResourceModel} resourceRecord The resource record
   * @param {HTMLElement} eventElement The element representing the new event.
   */
  /**
   * Fires on the owning Scheduler to allow implementer to prevent immediate finalization by setting
   * `data.context.async = true` in the listener, to show a confirmation popup etc.
   *
   * ```javascript
   *  scheduler.on('beforeDragCreateFinalize', ({context}) => {
   *      context.async = true;
   *      setTimeout(() => {
   *          // async code don't forget to call finalize
   *          context.finalize();
   *      }, 1000);
   *  })
   * ```
   *
   * Note that at this point the new `eventRecord` does not yet have the dates set, you can instead find the dates in
   * the context object.
   *
   * @event beforeDragCreateFinalize
   * @on-owner
   * @param {Scheduler.view.Scheduler} source Scheduler instance
   * @param {Scheduler.model.EventModel} eventRecord The event record being created
   * @param {Scheduler.model.ResourceModel} resourceRecord The resource record
   * @param {HTMLElement} eventElement The element representing the new Event record
   * @param {Object} context
   * @param {Date} context.startDate The start date of the event being created
   * @param {Date} context.endDate The end date of the event being created
   * @param {Boolean} context.async Set true to handle drag create asynchronously (e.g. to wait for user
   * confirmation)
   * @param {Function} context.finalize Call this method to finalize drag create. This method accepts one
   * argument: pass true to update records, or false, to ignore changes
   */
  /**
   * Fires on the owning Scheduler at the end of the drag create gesture whether or not
   * a new event was created by the gesture.
   * @event afterDragCreate
   * @on-owner
   * @param {Scheduler.view.Scheduler} source
   * @param {Scheduler.model.EventModel} eventRecord The event record being created
   * @param {Scheduler.model.ResourceModel} resourceRecord The resource record
   * @param {HTMLElement} eventElement The element representing the created event record
   */
  //endregion
  //region Init
  get scheduler() {
    return this.client;
  }
  get store() {
    return this.client.eventStore;
  }
  get project() {
    return this.client.project;
  }
  updateLockLayout(lock) {
    this.dragActiveCls = `b-dragcreating${lock ? " b-dragcreate-lock" : ""}`;
  }
  //endregion
  //region Scheduler specific implementation
  handleBeforeDragCreate(drag, eventRecord, event) {
    var _a;
    const me = this, { scheduler } = me, { resourceRecord } = drag;
    if (me.disabled || resourceRecord.readOnly || !me.scheduler.resourceStore.isAvailable(resourceRecord)) {
      return false;
    }
    if (!scheduler.isDateRangeAvailable(drag.mousedownDate, drag.mousedownDate, null, resourceRecord)) {
      return false;
    }
    const isWorkingTime = !scheduler.isSchedulerPro || eventRecord.ignoreResourceCalendar || resourceRecord.isWorkingTime(drag.mousedownDate), result = isWorkingTime && scheduler.trigger("beforeDragCreate", {
      resourceRecord,
      date: drag.mousedownDate,
      event
    });
    me.dateConstraints = (_a = scheduler.getDateConstraints) == null ? void 0 : _a.call(scheduler, resourceRecord, eventRecord);
    return result;
  }
  dragStart(drag) {
    var _a;
    const me = this, { client } = me, {
      eventStore,
      assignmentStore,
      enableEventAnimations,
      enableTransactionalFeatures
    } = client, { resourceRecord } = drag, eventRecord = me.createEventRecord(drag), resourceRecords = [resourceRecord];
    eventRecord.set("duration", DateHelper.diff(eventRecord.startDate, eventRecord.endDate, eventRecord.durationUnit, true));
    eventRecord.isCreating = true;
    eventRecord.meta.isDragCreating = true;
    client.features.taskEdit && client.features.taskEdit.doCancel();
    if (me.handleBeforeDragCreate(drag, eventRecord, drag.event) === false) {
      return false;
    }
    me.captureStm(true);
    let assignmentRecords = [];
    if (resourceRecord) {
      if (eventStore.usesSingleAssignment || !enableTransactionalFeatures) {
        assignmentRecords = assignmentStore.assignEventToResource(eventRecord, resourceRecord);
      } else {
        assignmentRecords = [assignmentStore.createRecord({
          event: eventRecord,
          resource: resourceRecord
        })];
      }
    }
    if (client.trigger("beforeEventAdd", { eventRecord, resourceRecords, assignmentRecords }) === false) {
      if (eventStore.usesSingleAssignment || !enableTransactionalFeatures) {
        assignmentStore.remove(assignmentRecords);
      }
      return false;
    }
    if (me.lockLayout) {
      eventRecord.meta.excludeFromLayout = true;
    }
    (_a = client.onEventCreated) == null ? void 0 : _a.call(client, eventRecord);
    client.enableEventAnimations = false;
    const result = eventStore.add(eventRecord);
    this.project.commitAsync().then(() => client.enableEventAnimations = enableEventAnimations);
    if (!result) {
      if (eventStore.usesSingleAssignment || !enableTransactionalFeatures) {
        assignmentStore.remove(assignmentRecords);
      }
      return false;
    }
    if (!eventStore.usesSingleAssignment && enableTransactionalFeatures) {
      assignmentStore.add(assignmentRecords[0]);
    }
    client.isCreating = true;
    if (client.isHorizontal) {
      const record = client.store.getById(resourceRecord);
      client.rowManager.renderRows([client.getRowFor(record)]);
    } else {
      client.refreshRows();
    }
    client.isCreating = false;
    drag.itemElement = drag.element = client.getElementFromEventRecord(eventRecord);
    if (!DomHelper.isInView(drag.itemElement)) {
      client.scrollable.scrollIntoView(drag.itemElement, {
        animate: true,
        edgeOffset: client.barMargin
      });
    }
    return super.dragStart(drag);
  }
  checkValidity(context, event) {
    const me = this, { client } = me, resourceRecord = context.resourceRecord = me.dragging.resourceRecord;
    return client.isDateRangeAvailable(context.startDate, context.endDate, context.eventRecord, resourceRecord) && me.createValidatorFn.call(me.validatorFnThisObj || me, context, event);
  }
  // Determine if resource already has events or not
  isRowEmpty(resourceRecord) {
    const events = this.store.getEventsForResource(resourceRecord);
    return !events || !events.length;
  }
  //endregion
  /**
   * Creates an event by the event object coordinates
   * @param {Object} drag The Bryntum event object
   * @private
   */
  createEventRecord(drag) {
    const me = this, { client } = me, dimension = client.isHorizontal ? "X" : "Y", {
      timeAxis,
      eventStore,
      weekStartDay
    } = client, {
      event,
      mousedownDate
    } = drag, draggingEnd = me.draggingEnd = event[`page${dimension}`] > drag.startEvent[`page${dimension}`], eventConfig = {
      name: eventStore.modelClass.fieldMap.name.defaultValue || me.L("L{Object.newEvent}"),
      startDate: draggingEnd ? DateHelper.floor(mousedownDate, timeAxis.resolution, null, weekStartDay) : mousedownDate,
      endDate: draggingEnd ? mousedownDate : DateHelper.ceil(mousedownDate, timeAxis.resolution, null, weekStartDay)
    };
    if (client.project.isGanttProjectMixin) {
      ObjectHelper.assign(eventConfig, {
        constraintDate: eventConfig.startDate,
        constraintType: "startnoearlierthan"
      });
    }
    return eventStore.createRecord(eventConfig);
  }
  async internalUpdateRecord(context, eventRecord) {
    await super.internalUpdateRecord(context, eventRecord);
    if (!this.client.hasEventEditor) {
      context.eventRecord.isCreating = false;
    }
  }
  async finalizeDragCreate(context) {
    const { meta } = context.eventRecord;
    meta.excludeFromLayout = false;
    meta.isDragCreating = false;
    const transferred = await super.finalizeDragCreate(context);
    if (!transferred) {
      await this.freeStm(true);
    } else {
      this.hasStmCapture = false;
    }
    return transferred;
  }
  async cancelDragCreate(context) {
    await super.cancelDragCreate(context);
    await this.freeStm(false);
  }
  getTipHtml(...args) {
    const html = super.getTipHtml(...args), { element } = this.tip;
    element.classList.add("b-sch-dragcreate-tooltip");
    element.classList.toggle("b-too-narrow", this.dragging.context.tooNarrow);
    return html;
  }
  onAborted(context) {
    var _a, _b;
    const { eventRecord, resourceRecord } = context.eventContexts[0];
    (_b = (_a = this.store).unassignEventFromResource) == null ? void 0 : _b.call(_a, eventRecord, resourceRecord);
    this.store.remove(eventRecord);
  }
}
//region Config
__publicField(EventDragCreate, "$name", "EventDragCreate");
__publicField(EventDragCreate, "configurable", {
  /**
   * Locks the layout during drag create, overriding the default behaviour that uses the same rendering
   * pathway for drag creation as for already existing events.
   *
   * This more closely resembles the behaviour of versions prior to 4.2.0.
   *
   * Enabling this config also leads to cheaper drag creation, only the events of the affected resource are
   * refreshed during the operation.
   *
   * For even cheaper drag creation, configure it as `'minimal-updates'`. In this mode, no other events are
   * updated during the operation.
   *
   * @config {Boolean|'minimal-updates'} lockLayout
   * @default true
   */
  /**
   * An empty function by default, but provided so that you can perform custom validation on the event being
   * created. Return `true` if the new event is valid, `false` to prevent an event being created.
   * @param {Object} context A drag create context
   * @param {Date} context.startDate Event start date
   * @param {Date} context.endDate Event end date
   * @param {Scheduler.model.EventModel} context.record Event record
   * @param {Scheduler.model.ResourceModel} context.resourceRecord Resource record
   * @param {Event} event The event object
   * @returns {Boolean} `true` if this validation passes
   * @config {Function}
   */
  validatorFn: () => true
});
EventDragCreate._$name = "EventDragCreate";
GridFeatureManager.registerFeature(EventDragCreate, true, "Scheduler");
GridFeatureManager.registerFeature(EventDragCreate, false, "ResourceHistogram");
export {
  EventDragCreate as default
};
