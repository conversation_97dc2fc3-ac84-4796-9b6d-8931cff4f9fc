import DragBase from "./base/DragBase.js";
import <PERSON><PERSON><PERSON><PERSON> from "@bryntum/core-thin/lib/helper/DateHelper.js";
import <PERSON><PERSON><PERSON>per from "@bryntum/core-thin/lib/helper/DomHelper.js";
import DomSync from "@bryntum/core-thin/lib/helper/DomSync.js";
import Rectangle, { Point } from "@bryntum/core-thin/lib/helper/util/Rectangle.js";
import GridFeatureManager from "@bryntum/grid-thin/lib/feature/GridFeatureManager.js";
import EventHelper from "@bryntum/core-thin/lib/helper/EventHelper.js";
import BrowserHelper from "@bryntum/core-thin/lib/helper/BrowserHelper.js";
class EventDrag extends DragBase {
  //region Config
  static get $name() {
    return "EventDrag";
  }
  static get configurable() {
    return {
      /**
       * Template used to generate drag tooltip contents.
       * ```javascript
       * const scheduler = new Scheduler({
       *     features : {
       *         eventDrag : {
       *             tooltipTemplate({eventRecord, startText}) {
       *                 return `${eventRecord.name}: ${startText}`
       *             }
       *         }
       *     }
       * });
       * ```
       * @config {Function} tooltipTemplate
       * @param {Object} data Tooltip data
       * @param {Scheduler.model.EventModel} data.eventRecord
       * @param {Scheduler.model.ResourceModel} data.newResource The new resource record
       * @param {Boolean} data.valid Currently over a valid drop target or not
       * @param {Date} data.startDate New start date
       * @param {Date} data.endDate New end date
       * @returns {String}
       */
      /**
       * Set to `true` to only allow dragging in one direction (based on initial movement)
       * @prp {Boolean}
       * @default false
       */
      singleDirection: null,
      /**
       * Set to true to only allow dragging events within the same resource.
       * @prp {Boolean}
       * @default
       */
      constrainDragToResource: false,
      /**
       * Set to true to only allow dragging events to different resources, and disallow rescheduling by dragging.
       * @prp {Boolean}
       * @default
       */
      constrainDragToTimeSlot: false,
      /**
       * A CSS selector specifying elements outside the scheduler element which are valid drop targets.
       * @config {String}
       */
      externalDropTargetSelector: null,
      /**
       * An empty function by default, but provided so that you can perform custom validation on the item being
       * dragged. This function is called during the drag and drop process and also after the drop is made.
       * Return `true` if the new position is valid, `false` to prevent the drag.
       *
       * ```javascript
       * features : {
       *     eventDrag : {
       *         validatorFn({ eventRecords, newResource }) {
       *             const
       *                 task  = eventRecords[0],
       *                 valid = newResource.role === task.resource.role;
       *
       *             return {
       *                 valid   : newResource.role === task.resource.role,
       *                 message : valid ? '' : 'Resource role does not match required role for this task'
       *             };
       *         }
       *     }
       * }
       * ```
       * @param {Object} context A drag drop context object
       * @param {Date} context.startDate New start date
       * @param {Date} context.endDate New end date
       * @param {Scheduler.model.AssignmentModel[]} context.assignmentRecords Assignment records which were dragged
       * @param {Scheduler.model.EventModel[]} context.eventRecords Event records which were dragged
       * @param {Scheduler.model.ResourceModel} context.newResource New resource record
       * @param {Scheduler.model.EventModel} context.targetEventRecord Currently hovering this event record
       * @param {Event} event The event object
       * @returns {Boolean|Object} `true` if this validation passes, `false` if it does not.
       *
       * Or an object with 2 properties: `valid` -  Boolean `true`/`false` depending on validity,
       * and `message` - String with a custom error message to display when invalid.
       * @config {Function}
       */
      validatorFn: (context, event) => {
      },
      /**
       * The `this` reference for the validatorFn
       * @config {Object}
       */
      validatorFnThisObj: null,
      /**
       * When the host Scheduler is `{@link Scheduler.view.mixin.EventSelection#config-multiEventSelect}: true`
       * then, there are two modes of dragging *within the same Scheduler*.
       *
       * Non unified means that all selected events are dragged by the same number of resource rows.
       *
       * Unified means that all selected events are collected together and dragged as one, and are all dropped
       * on the same targeted resource row at the same targeted time.
       * @prp {Boolean}
       * @default false
       */
      unifiedDrag: null,
      /**
       * A hook that allows manipulating the position the drag proxy snaps to. Manipulate the `snapTo` property
       * to alter snap position.
       *
       * ```javascript
       * const scheduler = new Scheduler({
       *     features : {
       *         eventDrag : {
       *             snapToPosition({ eventRecord, snapTo }) {
       *                 if (eventRecord.late) {
       *                     snapTo.x = 400;
       *                 }
       *             }
       *         }
       *     }
       * });
       * ```
       *
       * @config {Function}
       * @param {Object} context
       * @param {Scheduler.model.AssignmentModel} context.assignmentRecord Dragged assignment
       * @param {Scheduler.model.EventModel} context.eventRecord Dragged event
       * @param {Scheduler.model.ResourceModel} context.resourceRecord Currently over this resource
       * @param {Date} context.startDate Start date for current position
       * @param {Date} context.endDate End date for current position
       * @param {Object} context.snapTo
       * @param {Number} context.snapTo.x X to snap to
       * @param {Number} context.snapTo.y Y to snap to
       * @returns {void}
       */
      snapToPosition: null,
      /**
       * A modifier key (CTRL, SHIFT, ALT, META) that when pressed will copy an event instead of moving it. Set to
       * empty string to disable copying. Defaults to "CTRL" which is the Ctrl-key for Windows, and Meta-key for
       * MacOS.
       * @prp {'CTRL'|'ALT'|'SHIFT'|'META'|''}
       * @default
       */
      copyKey: "CTRL",
      /**
       * Event can be copied two ways: either by adding new assignment to an existing event ('assignment'), or
       * by copying the event itself ('event'). 'auto' mode will pick 'event' for a single-assignment mode (when
       * event has `resourceId` field) and 'assignment' mode otherwise.
       * @prp {'auto'|'assignment'|'event'}
       * @default
       */
      copyMode: "auto",
      /**
       * Mode of the current drag drop operation.
       * @member {'move'|'copy'}
       * @readonly
       */
      mode: "move",
      capitalizedEventName: null
    };
  }
  afterConstruct() {
    this.capitalizedEventName = this.capitalizedEventName || this.client.capitalizedEventName;
    super.afterConstruct(...arguments);
  }
  //endregion
  changeMode(value) {
    const { dragData, copyMode } = this;
    if ((copyMode === "event" || copyMode === "auto" || copyMode === "assignment" && !this.scheduler.eventStore.usesSingleAssignment) && (!dragData || dragData.eventRecords.every((r) => !r.isRecurring))) {
      return value;
    }
  }
  updateMode(mode) {
    if (this.dragData) {
      if (mode === "copy") {
        this.setCopying();
      } else {
        this.setMoving();
      }
      this.client.trigger("eventDragModeChange", { mode });
    }
  }
  setCopying() {
    const { dragData } = this;
    if (!dragData) {
      return;
    }
    if (!dragData.eventBarCopies.some((el) => el.isConnected)) {
      dragData.eventBarCopies.forEach((el) => {
        el.classList.add("b-drag-proxy-copy");
        el.classList.remove("b-hidden");
        dragData.context.grabbedParent.appendChild(el);
        el.retainElement = true;
      });
    } else {
      dragData.eventBarCopies.forEach((el) => {
        el.classList.remove("b-hidden");
      });
    }
  }
  setMoving() {
    const { dragData } = this;
    if (!dragData) {
      return;
    }
    dragData.eventBarCopies.forEach((el) => {
      el.classList.add("b-hidden");
    });
  }
  //region Events
  /**
   * This event is fired on the owning Scheduler after the event drag operation completes, but before changing any data.
   * It allows implementer to use asynchronous validation/finalization by setting `context.async = true`
   * in the listener, for example, to show a confirmation popup, make async data request etc.
   * In such case, implementer need to call the `context.finalize()` method manually:
   *
   * ```javascript
   *  scheduler.on('beforeeventdropfinalize', ({ context }) => {
   *      context.async = true;
   *      setTimeout(() => {
   *          // `true` to perform the drop, `false` to ignore it
   *          context.finalize(true);
   *      }, 1000);
   *  })
   * ```
   *
   * For synchronous one-time validation, simply set `context.valid` to true or false.
   * ```javascript
   *  scheduler.on('beforeeventdropfinalize', ({ context }) => {
   *      context.valid = false;
   *  })
   * ```
   * @event beforeEventDropFinalize
   * @on-owner
   * @param {Scheduler.view.Scheduler} source Scheduler instance
   * @param {Object} context
   * @param {DropData} context.dropData Information about the drop points for dragged events/assignments.
   * @param {Boolean} context.async Set to `true` to not finalize the drag-drop operation immediately (e.g. to wait for user confirmation)
   * @param {Scheduler.model.EventModel[]} context.eventRecords Event records being dragged
   * @param {Scheduler.model.AssignmentModel[]} context.assignmentRecords Assignment records being dragged
   * @param {Scheduler.model.EventModel} context.targetEventRecord Event record for drop target
   * @param {Scheduler.model.ResourceModel} context.newResource Resource record for drop target
   * @param {Boolean} context.valid Set this to `false` to abort the drop immediately.
   * @param {Function} context.finalize Call this method after an **async** finalization flow, to finalize the drag-drop operation. This method accepts one
   * argument: pass `true` to update records, or `false` to ignore changes
   * @param {MouseEvent} domEvent Browser event
   */
  /**
   * Fired on the owning Scheduler after event drop
   * @event afterEventDrop
   * @on-owner
   * @param {Scheduler.view.Scheduler} source
   * @param {Scheduler.model.AssignmentModel[]} assignmentRecords
   * @param {Scheduler.model.EventModel[]} eventRecords
   * @param {Boolean} valid
   * @param {Object} context
   * @param {MouseEvent} domEvent Browser event
   */
  /**
   * Fired on the owning Scheduler when an event is dropped
   * @event eventDrop
   * @on-owner
   * @param {Scheduler.view.Scheduler} source
   * @param {Scheduler.model.EventModel[]} eventRecords
   * @param {Scheduler.model.AssignmentModel[]} assignmentRecords
   * @param {HTMLElement} externalDropTarget The HTML element dropped upon, if drop happened on a valid external drop target
   * @param {Boolean} isCopy
   * @param {Object} context
   * @param {Scheduler.model.EventModel} context.targetEventRecord Event record for drop target
   * @param {Scheduler.model.ResourceModel} context.newResource Resource record for drop target
   * @param {MouseEvent} domEvent Browser event
   */
  /**
   * Fired on the owning Scheduler before event dragging starts. Return `false` to prevent the action.
   * @event beforeEventDrag
   * @on-owner
   * @preventable
   * @param {Scheduler.view.Scheduler} source Scheduler instance
   * @param {Scheduler.model.EventModel} eventRecord Event record the drag starts from
   * @param {Scheduler.model.ResourceModel} resourceRecord Resource record the drag starts from
   * @param {Scheduler.model.EventModel[]} eventRecords Event records being dragged
   * @param {Scheduler.model.AssignmentModel[]} assignmentRecords Assignment records being dragged
   * @param {MouseEvent} domEvent Browser event
   */
  /**
   * Fired on the owning Scheduler when event dragging starts
   * @event eventDragStart
   * @on-owner
   * @param {Scheduler.view.Scheduler} source Scheduler instance
   * @param {Scheduler.model.ResourceModel} resourceRecord Resource record the drag starts from
   * @param {Scheduler.model.EventModel[]} eventRecords Event records being dragged
   * @param {Scheduler.model.AssignmentModel[]} assignmentRecords Assignment records being dragged
   * @param {MouseEvent} domEvent Browser event
   */
  /**
   * Fired on the owning Scheduler when event is dragged
   * @event eventDrag
   * @on-owner
   * @param {Scheduler.view.Scheduler} source Scheduler instance
   * @param {Scheduler.model.EventModel[]} eventRecords Event records being dragged
   * @param {Scheduler.model.AssignmentModel[]} assignmentRecords Assignment records being dragged
   * @param {Date} startDate Start date for the current location
   * @param {Date} endDate End date for the current location
   * @param {Scheduler.model.ResourceModel} resourceRecord Resource record the drag started from
   * @param {Scheduler.model.ResourceModel} newResource Resource at the current location
   * @param {Object} context
   * @param {Boolean} context.valid Set this to `false` to signal that the current drop position is invalid.
   * @param {MouseEvent} domEvent Browser event
   */
  /**
   * Fired on the owning Scheduler after an event drag operation has been aborted
   * @event eventDragAbort
   * @on-owner
   * @param {Scheduler.view.Scheduler} source Scheduler instance
   * @param {Scheduler.model.EventModel[]} eventRecords Event records being dragged
   * @param {Scheduler.model.AssignmentModel[]} assignmentRecords Assignment records being dragged
   * @param {MouseEvent} domEvent Browser event
   */
  /**
   * Fired on the owning Scheduler after an event drag operation regardless of the operation being cancelled or not
   * @event eventDragReset
   * @on-owner
   * @param {Scheduler.view.Scheduler} source Scheduler instance
   */
  //endregion
  //region Data layer
  // Deprecated. Use this.client instead
  get scheduler() {
    return this.client;
  }
  //endregion
  //#region Drag lifecycle
  onAfterDragStart(event) {
    const me = this, { drag } = me, { context: { element } } = event;
    if (me.singleDirection) {
      drag.lockY = event.initialDirection === "horizontal";
      drag.lockX = !drag.lockY;
    }
    super.onAfterDragStart(event);
    me.handleKeyDownOrMove(event.event);
    me.keyEventDetacher = EventHelper.on({
      // In case we drag event between scheduler focused event gets moved and focus
      // moves to the body. We only need to read the key from this event
      element: DomHelper.getRootElement(element),
      keydown: me.handleKeyDownOrMove,
      keyup: me.handleKeyUp,
      thisObj: me
    });
  }
  onDragReset(event) {
    var _a;
    super.onDragReset(event);
    (_a = this.keyEventDetacher) == null ? void 0 : _a.call(this);
    this.mode = "move";
  }
  onDrop(event) {
    var _a;
    (_a = this.dragData.eventBarCopies) == null ? void 0 : _a.forEach((el) => el.remove());
    return super.onDrop(event);
  }
  //#endregion
  //region Drag events
  getDraggableElement(el) {
    return el == null ? void 0 : el.closest(this.drag.targetSelector);
  }
  resolveEventRecord(eventElement, client = this.client) {
    return client.resolveEventRecord(eventElement);
  }
  isElementDraggable(el, event) {
    var _a;
    const me = this, { client } = me, eventElement = me.getDraggableElement(el);
    if (!eventElement || me.disabled || client.readOnly) {
      return false;
    }
    if (el.matches('[class$="-handle"]')) {
      return false;
    }
    const eventRecord = me.resolveEventRecord(eventElement, client);
    if (!eventRecord || !eventRecord.isDraggable || eventRecord.readOnly) {
      return false;
    }
    const prevented = ((_a = client[`is${me.capitalizedEventName}ElementDraggable`]) == null ? void 0 : _a.call(
      client,
      eventElement,
      eventRecord,
      el,
      event
    )) === false;
    return !prevented;
  }
  getTriggerParams(dragData) {
    const { assignmentRecords, eventRecords, resourceRecord, browserEvent: domEvent } = dragData;
    return {
      // `context` is now private, but used in WebSocketHelper
      context: dragData,
      eventRecords,
      resourceRecord,
      assignmentRecords,
      domEvent
    };
  }
  getGroupedToStoreResources(dragData) {
    if (dragData.resourcesInStore) {
      return dragData.resourcesInStore;
    }
    const fromScheduler = this.client, fromResourceStore = fromScheduler.isVertical ? fromScheduler.resourceStore : fromScheduler.store;
    return dragData.resourcesInStore = [...new Set(fromResourceStore.getAllDataRecords().map((r) => r.$original))].filter((r) => r.isLeaf);
  }
  getIndexDiff(dragData) {
    const me = this, fromScheduler = me.client, toScheduler = me.currentOverClient, isCrossScheduler = fromScheduler !== toScheduler, { isVertical } = toScheduler, fromResourceStore = fromScheduler.isVertical ? fromScheduler.resourceStore : fromScheduler.store, toResourceStore = isVertical ? toScheduler.resourceStore : toScheduler.store, {
      resourceRecord: fromResource,
      newResource: toResource
    } = dragData;
    let indexDiff;
    if (isCrossScheduler) {
      indexDiff = toResourceStore.indexOf(toResource) - fromResourceStore.indexOf(fromResource.$original);
    } else if (me.constainDragToResource) {
      indexDiff = 0;
    } else if (isVertical && toResourceStore.isGrouped) {
      const resourcesInStore = me.getGroupedToStoreResources(dragData);
      indexDiff = resourcesInStore.indexOf(fromResource.$original) - resourcesInStore.indexOf(toResource);
    } else {
      indexDiff = fromResourceStore.indexOf(fromResource.$original) - fromResourceStore.indexOf(toResource);
    }
    return indexDiff;
  }
  getNewResource(dragData, originalResourceRecord, indexDiff) {
    const me = this, fromScheduler = me.client, toScheduler = me.currentOverClient, isCrossScheduler = fromScheduler !== toScheduler, { isVertical } = toScheduler, fromResourceStore = fromScheduler.isVertical ? fromScheduler.resourceStore : fromScheduler.store, toResourceStore = isVertical ? toScheduler.resourceStore : toScheduler.store;
    let { newResource } = dragData;
    if (!isCrossScheduler) {
      if (indexDiff !== 0) {
        let newIndex;
        if (isVertical && toResourceStore.isGrouped) {
          const resourcesInStore = me.getGroupedToStoreResources(dragData);
          newIndex = Math.max(
            Math.min(
              resourcesInStore.indexOf(originalResourceRecord) - indexDiff,
              resourcesInStore.length - 1
            ),
            0
          );
          newResource = resourcesInStore[newIndex];
        } else {
          newIndex = Math.max(
            Math.min(
              fromResourceStore.indexOf(originalResourceRecord) - indexDiff,
              fromResourceStore.count - 1
            ),
            0
          );
          newResource = fromResourceStore.getAt(newIndex);
          if (newResource.isSpecialRow) {
            newResource = fromResourceStore.getNext(newResource, false, true) || fromResourceStore.getPrevious(newResource, false, true);
          }
        }
        newResource = newResource == null ? void 0 : newResource.$original;
      } else {
        newResource = originalResourceRecord;
      }
    } else {
      const draggedEventResourceIndex = fromResourceStore.indexOf(originalResourceRecord);
      newResource = toResourceStore.getAt(draggedEventResourceIndex + indexDiff) || newResource;
    }
    return newResource;
  }
  getDropData(dragData) {
    const indexDiff = this.getIndexDiff(dragData);
    return {
      events: dragData.eventRecords.map((eventRecord) => {
        return {
          eventRecord,
          ...this.getEventNewStartEndDates(eventRecord, dragData.timeDiff)
        };
      }),
      assignments: dragData.assignmentRecords.map((assignmentRecord) => {
        return {
          assignmentRecord,
          resourceRecord: this.getNewResource(dragData, assignmentRecord.resource, indexDiff)
        };
      })
    };
  }
  triggerBeforeEventDropFinalize(eventType, eventData, client) {
    eventData.context.dropData = this.getDropData(eventData.context);
    super.triggerBeforeEventDropFinalize(eventType, eventData, client);
  }
  triggerBeforeEventDrag(eventType, event) {
    return this.client.trigger(eventType, event);
  }
  triggerEventDrag(dragData, start) {
    this.client.trigger("eventDrag", Object.assign(this.getTriggerParams(dragData), {
      startDate: dragData.startDate,
      endDate: dragData.endDate,
      newResource: dragData.newResource
    }));
  }
  triggerDragStart(dragData) {
    this.client.navigator.skipNextClick = true;
    this.client.trigger("eventDragStart", this.getTriggerParams(dragData));
  }
  triggerDragAbort(dragData) {
    this.client.trigger("eventDragAbort", this.getTriggerParams(dragData));
  }
  triggerDragAbortFinalized(dragData) {
    this.client.trigger("eventDragAbortFinalized", this.getTriggerParams(dragData));
  }
  triggerAfterDrop(dragData, valid) {
    const me = this;
    me.currentOverClient.trigger("afterEventDrop", Object.assign(me.getTriggerParams(dragData), {
      valid
    }));
    if (!valid) {
      const { assignmentStore, eventStore } = me.client, needRefresh = me.dragData.initialAssignmentsState.find(({
        resource,
        assignment
      }, i) => {
        var _a;
        return !assignmentStore.includes(assignment) || !eventStore.includes(assignment.event) || resource.id !== ((_a = me.dragData.assignmentRecords[i]) == null ? void 0 : _a.resourceId);
      });
      if (needRefresh) {
        me.client.refresh();
      }
    }
    me.client.setTimeout(() => me.client.navigator.skipNextClick = false, 10);
  }
  handleKeyDownOrMove(event) {
    var _a, _b;
    if (this.mode !== "copy" && (this.isCtrlKeyPress(event) || event.key && EventHelper.specialKeyFromEventKey(event.key) === ((_a = this.copyKey) == null ? void 0 : _a.toLowerCase()) || event[`${(_b = this.copyKey) == null ? void 0 : _b.toLowerCase()}Key`])) {
      this.mode = "copy";
    }
  }
  handleKeyUp(event) {
    if (this.isCtrlKeyPress(event) || EventHelper.specialKeyFromEventKey(event.key) === this.copyKey.toLowerCase()) {
      this.mode = "move";
    }
  }
  isCtrlKeyPress(event) {
    return this.copyKey === "CTRL" && event.key === (BrowserHelper.ctrlKey === "ctrlKey" ? "Ctrl" : "Meta");
  }
  //endregion
  //region Finalization & validation
  /**
   * Checks if an event can be dropped on the specified position.
   * @private
   * @returns {Boolean} Valid (true) or invalid (false)
   */
  isValidDrop(dragData) {
    const {
      newResource,
      resourceRecord,
      browserEvent
    } = dragData, sourceRecord = dragData.draggedEntities[0], { target } = browserEvent;
    if (!newResource) {
      return !this.constrainDragToTimeline && this.externalDropTargetSelector ? Boolean(target.closest(this.externalDropTargetSelector)) : false;
    }
    if (newResource.isSpecialRow || newResource.readOnly) {
      return false;
    }
    if (resourceRecord.$original !== newResource) {
      return !sourceRecord.event.resources.includes(newResource);
    }
    return true;
  }
  checkDragValidity(dragData, event) {
    var _a, _b;
    const me = this, scheduler = me.currentOverClient, { newResource } = dragData;
    let result;
    if (newResource == null ? void 0 : newResource.readOnly) {
      return false;
    }
    if (newResource && me.hasOverlaps()) {
      result = {
        valid: false,
        message: me.L("L{eventOverlapsExisting}")
      };
    } else {
      result = me.validatorFn.call(
        me.validatorFnThisObj || me,
        dragData,
        event
      );
    }
    if (!result || result.valid) {
      result = (_b = (_a = scheduler["checkEventDragValidity"]) == null ? void 0 : _a.call(scheduler, dragData, event)) != null ? _b : result;
    }
    return result;
  }
  hasOverlaps() {
    const me = this, { dragData } = me, { startDate, origStart } = dragData, msDiff = startDate - origStart, indexDiff = me.getIndexDiff(me.dragData);
    return dragData.assignmentRecords.some((assignmentRecord) => {
      const newResource = me.getNewResource(dragData, assignmentRecord.resource, indexDiff), eventRecord = assignmentRecord.event, startDate2 = DateHelper.add(eventRecord.startDate, msDiff, "ms"), endDate = DateHelper.add(eventRecord.endDate, msDiff, "ms");
      return !me.currentOverClient.isDateRangeAvailable(startDate2, endDate, dragData.assignmentRecords, newResource);
    });
  }
  //endregion
  //region Update records
  /**
   * Update events being dragged.
   * @private
   * @param context Drag data.
   */
  async updateRecords(context) {
    const me = this, fromScheduler = me.client, toScheduler = me.currentOverClient, copyKeyPressed = me.mode === "copy", { draggedEntities, timeDiff, initialAssignmentsState } = context, originalStartDate = initialAssignmentsState[0].startDate, droppedStartDate = me.adjustStartDate(originalStartDate, timeDiff);
    let result;
    if (!context.externalDropTarget) {
      if (!toScheduler.timeAxis.timeSpanInAxis(droppedStartDate, DateHelper.add(droppedStartDate, draggedEntities[0].event.durationMS, "ms"))) {
        context.valid = false;
      }
      if (context.valid) {
        fromScheduler.eventStore.suspendAutoCommit();
        toScheduler.eventStore.suspendAutoCommit();
        result = await me.updateAssignments(fromScheduler, toScheduler, context, copyKeyPressed);
        fromScheduler.eventStore.resumeAutoCommit();
        toScheduler.eventStore.resumeAutoCommit();
      }
    }
    if (context.valid) {
      toScheduler.trigger("eventDrop", Object.assign(me.getTriggerParams(context), {
        isCopy: copyKeyPressed,
        copyMode: me.copyMode,
        domEvent: context.browserEvent,
        targetEventRecord: context.targetEventRecord,
        targetResourceRecord: context.newResource,
        externalDropTarget: context.externalDropTarget
      }));
    }
    return result;
  }
  /**
   * Update assignments being dragged
   * @private
   */
  async updateAssignments(fromScheduler, toScheduler, context, copy) {
    var _a, _b, _c;
    const me = this, { copyMode } = me, isCrossScheduler = fromScheduler !== toScheduler, { isVertical } = toScheduler, {
      assignmentStore: fromAssignmentStore,
      eventStore: fromEventStore
    } = fromScheduler, {
      assignmentStore: toAssignmentStore,
      eventStore: toEventStore
    } = toScheduler, fromResourceStore = fromScheduler.isVertical ? fromScheduler.resourceStore : fromScheduler.store, {
      eventRecords,
      assignmentRecords,
      timeDiff,
      initialAssignmentsState,
      newResource: toResource
    } = context, { unifiedDrag } = me, useSingleAssignment = toEventStore.usesSingleAssignment || toEventStore.usesSingleAssignment !== false && fromEventStore.usesSingleAssignment, effectiveCopyMode = copyMode === "event" ? "event" : copyMode === "assignment" ? "assignment" : useSingleAssignment ? "event" : "assignment", event1Date = me.adjustStartDate(assignmentRecords[0].event.startDate, timeDiff), eventsToAdd = [], eventsToRemove = [], assignmentsToAdd = [], assignmentsToRemove = [], eventsToCheck = [], eventsToBatch = /* @__PURE__ */ new Set();
    fromScheduler.suspendRefresh();
    toScheduler.suspendRefresh();
    let updated = false, updatedEvent = false;
    const indexDiff = me.getIndexDiff(context);
    if (isVertical) {
      eventRecords.forEach((draggedEvent, i) => {
        const eventBar = context.eventBarEls[i];
        delete draggedEvent.instanceMeta(fromScheduler).hasTemporaryDragElement;
        if (eventBar.dataset.transient) {
          eventBar.remove();
        }
      });
    }
    const eventBarEls = context.eventBarEls.slice(), addedEvents = [], copiedAssignmentsMap = {};
    for (let i = 0; i < assignmentRecords.length; i++) {
      const originalAssignment = assignmentRecords[i];
      let draggedEvent = originalAssignment.event, draggedAssignment;
      if (copy) {
        draggedAssignment = originalAssignment.copy();
        copiedAssignmentsMap[originalAssignment.id] = draggedAssignment;
      } else {
        draggedAssignment = originalAssignment;
      }
      if (!draggedAssignment.isOccurrenceAssignment && (!fromAssignmentStore.includes(originalAssignment) || !fromEventStore.includes(draggedEvent))) {
        eventBarEls[i].remove();
        eventBarEls.splice(i, 1);
        assignmentRecords.splice(i, 1);
        i--;
        continue;
      }
      const initialState = initialAssignmentsState[i], originalEventRecord = draggedEvent, originalStartDate = initialState.startDate, originalResourceRecord = initialState.resource, newStartDate = this.constrainDragToTimeSlot ? originalStartDate : unifiedDrag ? event1Date : me.adjustStartDate(originalStartDate, timeDiff);
      if (!fromAssignmentStore.equals(toAssignmentStore)) {
        const keepEvent = originalEventRecord.assignments.length > 1 || copy;
        let newAssignment;
        if (copy) {
          newAssignment = draggedAssignment;
        } else {
          newAssignment = draggedAssignment.copy();
          copiedAssignmentsMap[draggedAssignment.id] = newAssignment;
        }
        if (newAssignment.event && !useSingleAssignment) {
          newAssignment.event = newAssignment.event.id;
          newAssignment.resource = newAssignment.resource.id;
        }
        if (!copy) {
          assignmentsToRemove.push(draggedAssignment);
        }
        if (!keepEvent) {
          eventsToRemove.push(originalEventRecord);
        }
        if (copy && (copyMode === "event" || copyMode === "auto" && toEventStore.usesSingleAssignment) || !toEventStore.getById(originalEventRecord.id)) {
          draggedEvent = toEventStore.createRecord({
            ...originalEventRecord.data,
            children: (_a = originalEventRecord.children) == null ? void 0 : _a.map((child) => child.copy()),
            // If we're copying the event (not making new assignment to existing), we need to generate
            // phantom id to link event to the assignment record
            id: copy && (copyMode === "event" || copyMode === "auto") ? void 0 : originalEventRecord.id,
            // Engine gets mad if not nulled
            calendar: null
          });
          newAssignment.set({
            eventId: draggedEvent.id,
            event: draggedEvent
          });
          eventsToAdd.push(draggedEvent);
        }
        if (!useSingleAssignment) {
          assignmentsToAdd.push(newAssignment);
        }
        draggedAssignment = newAssignment;
      }
      let newResource = toResource, reassignedFrom = null;
      if (!unifiedDrag) {
        newResource = me.getNewResource(context, originalResourceRecord, indexDiff) || toResource;
      }
      newResource = newResource.$original;
      const isCrossResource = draggedAssignment.resourceId !== newResource.id;
      if (isCrossResource) {
        reassignedFrom = fromResourceStore.getById(draggedAssignment.resourceId);
        if (copy && fromAssignmentStore.equals(toAssignmentStore)) {
          draggedAssignment.setData({
            resource: null,
            resourceId: null
          });
          draggedAssignment.resource = newResource;
          draggedAssignment.event = toEventStore.getById(draggedAssignment.eventId);
          const shouldCopyEvent = copyMode === "event" || fromEventStore.usesSingleAssignment && copyMode === "auto";
          if (shouldCopyEvent) {
            draggedEvent = draggedEvent.copy();
            draggedEvent.meta.endDateCached = me.adjustStartDate(draggedEvent.endDate, timeDiff);
            draggedEvent.endDate = null;
            draggedAssignment.event = draggedEvent;
            if (toEventStore.usesSingleAssignment) {
              draggedEvent.resource = newResource;
              draggedEvent.resourceId = newResource.id;
            }
          }
          if (!toAssignmentStore.find((a) => a.eventId === draggedAssignment.eventId && a.resourceId === draggedAssignment.resourceId) && !assignmentsToAdd.find((r) => r.eventId === draggedAssignment.eventId && r.resourceId === draggedAssignment.resourceId)) {
            shouldCopyEvent && eventsToAdd.push(draggedEvent);
            assignmentsToAdd.push(draggedAssignment);
          }
        } else {
          draggedAssignment.resource = newResource;
        }
        draggedEvent.isEvent && eventsToBatch.add(draggedEvent);
        updated = true;
        if (draggedEvent.isOccurrence) {
          draggedEvent.set("newResource", newResource);
        }
        if (isCrossScheduler && useSingleAssignment) {
          draggedEvent.resourceId = newResource.id;
        }
      } else {
        if (copy && (copyMode === "event" || copyMode === "auto" && fromEventStore.usesSingleAssignment) && !eventsToAdd.includes(draggedEvent)) {
          draggedEvent = draggedEvent.copy();
          draggedEvent.meta.endDateCached = me.adjustStartDate(draggedEvent.endDate, timeDiff);
          draggedEvent.endDate = null;
          eventsToAdd.push(draggedEvent);
          draggedAssignment.event = draggedEvent;
          if (toEventStore.usesSingleAssignment) {
            draggedEvent.set({
              resource: newResource,
              resourceId: newResource.id
            });
          }
          assignmentsToAdd.push(draggedAssignment);
        }
      }
      if (!eventsToCheck.find((ev) => ev.draggedEvent === draggedEvent) && !DateHelper.isEqual(draggedEvent.startDate, newStartDate)) {
        while (!draggedEvent.isOccurrence && draggedEvent.isBatchUpdating) {
          draggedEvent.endBatch(true);
        }
        const shouldKeepStartDate = copy && !isCrossScheduler && !useSingleAssignment && effectiveCopyMode === "assignment" && isCrossResource;
        if (!shouldKeepStartDate) {
          draggedEvent.startDate = newStartDate;
          eventsToCheck.push({ draggedEvent, originalStartDate });
        }
        draggedEvent.isEvent && eventsToBatch.add(draggedEvent);
        updatedEvent = true;
      }
      toScheduler.processEventDrop({
        eventRecord: draggedEvent,
        resourceRecord: newResource,
        element: i === 0 ? context.context.element : context.context.relatedElements[i - 1],
        context,
        toScheduler,
        reassignedFrom,
        eventsToAdd,
        addedEvents,
        draggedAssignment
      });
      toScheduler.trigger("processEventDrop", {
        originalAssignment,
        draggedAssignment,
        context,
        copyMode,
        isCopy: copy
      });
    }
    fromAssignmentStore.remove(assignmentsToRemove);
    fromEventStore.remove(eventsToRemove);
    toAssignmentStore.add(assignmentsToAdd);
    if (copy && fromAssignmentStore.equals(toAssignmentStore)) {
      const { syncIdMap } = fromScheduler.foregroundCanvas;
      Object.entries(copiedAssignmentsMap).forEach(([originalId, cloneRecord]) => {
        const element = syncIdMap[originalId];
        delete syncIdMap[originalId];
        syncIdMap[cloneRecord.id] = element;
      });
    }
    eventsToAdd.length && addedEvents.push(...toEventStore.add(eventsToAdd));
    addedEvents == null ? void 0 : addedEvents.forEach((added) => eventsToBatch.add(added));
    if (assignmentsToRemove.length || eventsToRemove.length || assignmentsToAdd.length || eventsToAdd.length) {
      updated = true;
    }
    if (updated || updatedEvent) {
      if (!me.constrainDragToTimeline) {
        for (let i = 0; i < assignmentRecords.length; i++) {
          const assignmentRecord = copiedAssignmentsMap[assignmentRecords[i].id] || assignmentRecords[i], originalDraggedEvent = assignmentRecord.event, draggedEvent = (addedEvents == null ? void 0 : addedEvents.find((r) => r.id === originalDraggedEvent.id)) || originalDraggedEvent, eventBar = context.eventBarEls[i], element = i === 0 ? context.context.element : context.context.relatedElements[i - 1], inTimeAxis = toScheduler.isInTimeAxis(draggedEvent);
          delete draggedEvent.meta.endDateCached;
          if (!copy) {
            DomSync.removeChild(eventBar.parentElement, eventBar);
          }
          if (draggedEvent.resource && (isVertical || toScheduler.rowManager.getRowFor(draggedEvent.resource)) && inTimeAxis) {
            if (!draggedEvent.parent || draggedEvent.parent.isRoot) {
              const elRect = Rectangle.from(element, toScheduler.foregroundCanvas, true), x = toScheduler.rtl ? toScheduler.timeAxisViewModel.totalSize - elRect.right : elRect.x;
              DomHelper.setTopInsetInlineStart(element, elRect.y, x);
              DomSync.addChild(toScheduler.foregroundCanvas, element, draggedEvent.assignments[0].id);
              isCrossScheduler && toScheduler.processCrossSchedulerEventDrop({
                eventRecord: draggedEvent,
                toScheduler
              });
            }
            element.classList.remove("b-sch-event-hover", "b-active", "b-drag-proxy", "b-dragging");
            element.retainElement = false;
          }
        }
      }
      useSingleAssignment && eventsToBatch.forEach((eventRecord) => eventRecord.beginBatch());
      const toProject = (_b = toEventStore.$master.project) != null ? _b : toScheduler.project, fromProject = (_c = fromEventStore.$master.project) != null ? _c : fromScheduler.project;
      await Promise.all([
        toProject !== fromProject ? toProject.commitAsync() : null,
        fromProject.commitAsync()
      ]);
      useSingleAssignment && eventsToBatch.forEach((eventRecord) => eventRecord.endBatch(false, true));
    }
    if (!updated) {
      updated = eventsToCheck.some(
        ({ draggedEvent, originalStartDate }) => !DateHelper.isEqual(draggedEvent.startDate, originalStartDate)
      );
    }
    toScheduler.resumeRefresh(false);
    fromScheduler.resumeRefresh(false);
    if (assignmentRecords.length > 0) {
      if (!updated) {
        context.valid = false;
      } else {
        eventBarEls.forEach((el) => delete el.lastDomConfig);
        toScheduler.refreshWithTransition();
        if (isCrossScheduler) {
          fromScheduler.refreshWithTransition();
          toScheduler.selectedEvents = addedEvents;
        }
      }
    }
  }
  //endregion
  //region Drag data
  getProductDragContext(dragData) {
    const me = this, { currentOverClient: scheduler } = me, target = dragData.browserEvent.target, previousResolvedResource = dragData.newResource || dragData.resourceRecord, previousTargetEventRecord = dragData.targetEventRecord;
    let targetEventRecord = scheduler ? me.resolveEventRecord(target, scheduler) : null, newResource, externalDropTarget;
    if (dragData.eventRecords.includes(targetEventRecord)) {
      targetEventRecord = null;
    }
    if (me.constrainDragToResource) {
      newResource = dragData.resourceRecord;
    } else if (!me.constrainDragToTimeline) {
      newResource = me.resolveResource();
    } else if (scheduler) {
      newResource = me.resolveResource() || dragData.newResource || dragData.resourceRecord;
    }
    const { assignmentRecords, eventRecords } = dragData, isOverNewResource = previousResolvedResource !== newResource;
    let valid = Boolean(newResource && !newResource.isSpecialRow);
    if (!newResource && me.externalDropTargetSelector) {
      externalDropTarget = target.closest(me.externalDropTargetSelector);
      valid = Boolean(externalDropTarget);
    }
    return {
      valid,
      externalDropTarget,
      eventRecords,
      assignmentRecords,
      newResource,
      targetEventRecord,
      dirty: isOverNewResource || targetEventRecord !== previousTargetEventRecord,
      proxyElements: [dragData.context.element, ...dragData.context.relatedElements || []]
    };
  }
  getMinimalDragData(info) {
    const me = this, { scheduler } = me, element = me.getElementFromContext(info), eventRecord = me.resolveEventRecord(element, scheduler), resourceRecord = scheduler.resolveResourceRecord(element), assignmentRecord = scheduler.resolveAssignmentRecord(element);
    let assignmentRecords = assignmentRecord ? [assignmentRecord] : [];
    if (assignmentRecord && (scheduler.isAssignmentSelected(assignmentRecords[0]) || me.drag.startEvent.ctrlKey && scheduler.multiEventSelect)) {
      assignmentRecords.push.apply(assignmentRecords, me.getRelatedRecords(assignmentRecord));
    }
    assignmentRecords = assignmentRecords.filter((assignment) => scheduler.resourceStore.isAvailable(assignment.resource));
    const eventRecords = [...new Set(assignmentRecords.map((assignment) => assignment.event))];
    return {
      eventRecord,
      resourceRecord,
      assignmentRecord,
      eventRecords,
      assignmentRecords
    };
  }
  setupProductDragData(info) {
    var _a;
    const me = this, { scheduler } = me, element = me.getElementFromContext(info), {
      eventRecord,
      resourceRecord,
      assignmentRecord,
      assignmentRecords
    } = me.getMinimalDragData(info), eventBarEls = [];
    if (me.constrainDragToResource && !resourceRecord) {
      throw new Error("Resource could not be resolved for event: " + eventRecord.id);
    }
    let dateConstraints;
    if (me.constrainDragToTimeline) {
      dateConstraints = (_a = me.getDateConstraints) == null ? void 0 : _a.call(me, resourceRecord, eventRecord);
      const constrainRectangle = me.constrainRectangle = me.getConstrainingRectangle(dateConstraints, resourceRecord, eventRecord), eventRegion = Rectangle.from(element, scheduler.timeAxisSubGridElement), isDateConstrained = Boolean(dateConstraints.start);
      super.setupConstraints(
        constrainRectangle,
        eventRegion,
        scheduler.timeAxisViewModel.snapPixelAmount,
        isDateConstrained && scheduler.isHorizontal,
        isDateConstrained && scheduler.isVertical
      );
    }
    assignmentRecords.forEach((assignment) => {
      let eventBarEl = scheduler.getElementFromAssignmentRecord(assignment, true);
      if (!eventBarEl) {
        eventBarEl = scheduler.currentOrientation.addTemporaryDragElement(assignment.event, assignment.resource);
      }
      eventBarEls.push(eventBarEl);
    });
    return {
      record: assignmentRecord,
      draggedEntities: assignmentRecords,
      dateConstraints: (dateConstraints == null ? void 0 : dateConstraints.start) ? dateConstraints : null,
      // Create copies of the elements
      eventBarCopies: eventBarEls.map((el) => me.createProxy(el)),
      eventBarEls
    };
  }
  getDateConstraints(resourceRecord, eventRecord) {
    var _a;
    const { scheduler } = this, externalDateConstraints = (_a = scheduler.getDateConstraints) == null ? void 0 : _a.call(scheduler, resourceRecord, eventRecord);
    let minDate, maxDate;
    if (this.constrainDragToTimeSlot) {
      minDate = eventRecord.startDate;
      maxDate = eventRecord.endDate;
    } else if (externalDateConstraints) {
      minDate = externalDateConstraints.start;
      maxDate = externalDateConstraints.end;
    }
    return {
      start: minDate,
      end: maxDate
    };
  }
  getConstrainingRectangle(dateRange, resourceRecord, eventRecord) {
    return this.scheduler.getScheduleRegion(this.constrainDragToResource && resourceRecord, eventRecord, true, dateRange && {
      start: dateRange.start,
      end: dateRange.end
    });
  }
  /**
   * Initializes drag data (dates, constraints, dragged events etc). Called when drag starts.
   * @private
   * @param info
   * @returns {*}
   */
  getDragData(info) {
    const dragData = this.getMinimalDragData(info) || {};
    return {
      ...super.getDragData(info),
      ...dragData,
      initialAssignmentsState: dragData.assignmentRecords.map((assignment) => ({
        startDate: assignment.event.startDate,
        resource: assignment.resource,
        assignment
      }))
    };
  }
  /**
   * Provide your custom implementation of this to allow additional selected records to be dragged together with the original one.
   * @param {Scheduler.model.AssignmentModel} assignmentRecord The assignment about to be dragged
   * @returns {Scheduler.model.AssignmentModel[]} An array of assignment records to drag together with the original
   */
  getRelatedRecords(assignmentRecord) {
    return this.scheduler.selectedAssignments.filter((selectedRecord) => selectedRecord !== assignmentRecord && !selectedRecord.resource.readOnly && selectedRecord.event.isDraggable);
  }
  /**
   * Get correct axis coordinate depending on schedulers mode (horizontal -> x, vertical -> y). Also takes milestone
   * layout into account.
   * @private
   * @param {Scheduler.model.EventModel} eventRecord Record being dragged
   * @param {HTMLElement} element Element being dragged
   * @param {Number[]} coord XY coordinates
   * @returns {Number|Number[]} X,Y or XY
   */
  getCoordinate(eventRecord, element, coord) {
    const scheduler = this.currentOverClient;
    if (scheduler.isHorizontal) {
      let x = coord[0];
      if (scheduler.milestoneLayoutMode !== "default" && eventRecord.isMilestone) {
        switch (scheduler.milestoneAlign) {
          case "center":
            x += element.offsetWidth / 2;
            break;
          case "end":
            x += element.offsetWidth;
            break;
        }
      }
      return x;
    } else {
      let y = coord[1];
      if (scheduler.milestoneLayoutMode !== "default" && eventRecord.isMilestone) {
        switch (scheduler.milestoneAlign) {
          case "center":
            y += element.offsetHeight / 2;
            break;
          case "end":
            y += element.offsetHeight;
            break;
        }
      }
      return y;
    }
  }
  /**
   * Get resource record occluded by the drag proxy.
   * @private
   * @returns {Scheduler.model.ResourceModel}
   */
  resolveResource() {
    const me = this, client = me.currentOverClient, { isHorizontal } = client, {
      context,
      browserEvent,
      dragProxy
    } = me.dragData, element = dragProxy || context.element, pageRect = Rectangle.from(element, null, true), y = client.isVertical || me.unifiedDrag ? context.clientY : pageRect.center.y, localRect = Rectangle.from(element, client.timeAxisSubGridElement, true), { x: lx, y: ly } = localRect.center;
    let resource = null, isInsideClientEl;
    if (browserEvent.touches) {
      isInsideClientEl = Rectangle.from(client.element).contains(Point.from(browserEvent, true));
    } else {
      isInsideClientEl = client.element.contains(me.getMouseMoveEventTarget(browserEvent));
    }
    if (isInsideClientEl) {
      if (isHorizontal) {
        const row = client.rowManager.getRowAt(y);
        resource = row && client.store.storage.getAt(row.dataIndex);
      } else {
        resource = client.resolveResourceRecord(client.timeAxisSubGridElement.querySelector(".b-sch-timeaxis-cell"), [lx, ly]);
      }
    }
    return resource == null ? void 0 : resource.$original;
  }
  //endregion
  //region Other stuff
  getRecordElement(assignmentRecord) {
    return this.client.getElementFromAssignmentRecord(assignmentRecord, true);
  }
  // Used by the Dependencies feature to draw lines to the drag proxy instead of the original event element
  getProxyElement(assignmentRecord) {
    var _a;
    const { dragData } = this;
    if (this.isDragging && ((_a = dragData == null ? void 0 : dragData.proxyElements) == null ? void 0 : _a.length)) {
      const index = dragData.assignmentRecords.indexOf(assignmentRecord);
      if (index >= 0) {
        return dragData.proxyElements[index];
      }
    }
    return null;
  }
  //endregion
  //#region Salesforce hooks
  getMouseMoveEventTarget(event) {
    return event.target;
  }
  //#endregion
}
EventDrag._$name = "EventDrag";
GridFeatureManager.registerFeature(EventDrag, true, "Scheduler");
GridFeatureManager.registerFeature(EventDrag, false, "ResourceHistogram");
export {
  EventDrag as default
};
