import LocaleHelper from "@bryntum/core-thin/lib/localization/LocaleHelper.js";
import "@bryntum/grid-thin/lib/localization/Fi.js";
const locale = {
  localeName: "Fi",
  localeDesc: "Suomi",
  localeCode: "fi",
  Object: {
    newEvent: "Uusi tapahtuma"
  },
  ResourceInfoColumn: {
    eventCountText: (data) => data + " tapahtuma" + (data !== 1 ? "t" : "")
  },
  Dependencies: {
    from: "From",
    to: "To",
    valid: "Voimassa",
    invalid: "Virheellinen"
  },
  DependencyType: {
    SS: "SS",
    SF: "SF",
    FS: "FS",
    FF: "FF",
    StartToStart: "Start-to-Start",
    StartToEnd: "Start-to-Finish",
    EndToStart: "Finish-to-Start",
    EndToEnd: "Finish-to-Finish",
    short: [
      "SS",
      "SF",
      "FS",
      "FF"
    ],
    long: [
      "Start-to-Start",
      "Start-to-Finish",
      "Finish-to-Start",
      "Finish-to-Finish"
    ]
  },
  DependencyEdit: {
    From: "L\xE4hett\xE4j\xE4",
    To: "Vastaanottaja",
    Type: "Tyyppi",
    Lag: "Lag",
    "Edit dependency": "Muokkaa riippuvuutta",
    Save: "Tallenna",
    Delete: "Poista",
    Cancel: "Peruuta",
    StartToStart: "alusta alkuun",
    StartToEnd: "alusta loppuun",
    EndToStart: "maalista alkuun",
    EndToEnd: "maalista maaliin"
  },
  EventEdit: {
    Name: "Nimi",
    Resource: "L\xE4hde",
    Start: "Start",
    End: "Loppuun",
    Save: "Tallenna",
    Delete: "Poista",
    Cancel: "Peruuta",
    "Edit event": "Muokkaa tapahtumaa",
    Repeat: "Toista"
  },
  EventDrag: {
    eventOverlapsExisting: "Tapahtuma on p\xE4\xE4llekk\xE4inen t\xE4m\xE4n resurssin olemassa olevan tapahtuman kanssa",
    noDropOutsideTimeline: "Tapahtumaa ei saa pudottaa kokonaan aikajanan ulkopuolelle"
  },
  SchedulerBase: {
    "Add event": "Lis\xE4\xE4 tapahtuma",
    "Delete event": "Poista tapahtuma",
    "Unassign event": "Poista tapahtuma",
    color: "V\xE4ri"
  },
  TimeAxisHeaderMenu: {
    pickZoomLevel: "Zoom",
    activeDateRange: "P\xE4iv\xE4ysalue",
    startText: "Aloitusp\xE4iv\xE4",
    endText: "Lopetusp\xE4iv\xE4",
    todayText: "T\xE4n\xE4\xE4n"
  },
  EventCopyPaste: {
    copyEvent: "Kopioi tapahtuma",
    cutEvent: "Leikkaa tapahtuma",
    pasteEvent: "Liit\xE4 tapahtuma"
  },
  EventFilter: {
    filterEvents: "Suodata teht\xE4v\xE4t",
    byName: "nimien mukaan"
  },
  TimeRanges: {
    showCurrentTimeLine: "N\xE4yt\xE4 nykyinen aikajana"
  },
  PresetManager: {
    secondAndMinute: {
      displayDateFormat: "ll LTS",
      name: "Sekuntia"
    },
    minuteAndHour: {
      topDateFormat: "ddd DD.MM, H",
      displayDateFormat: "ll LST"
    },
    hourAndDay: {
      topDateFormat: "ddd DD.MM",
      middleDateFormat: "LST",
      displayDateFormat: "ll LST",
      name: "P\xE4iv\xE4"
    },
    day: {
      name: "P\xE4iv\xE4/tunnit"
    },
    week: {
      name: "Viikko/tunnit"
    },
    dayAndWeek: {
      displayDateFormat: "ll LST",
      name: "Viikko/p\xE4iv\xE4t"
    },
    dayAndMonth: {
      name: "Kuukausi"
    },
    weekAndDay: {
      displayDateFormat: "ll LST",
      name: "Viikko"
    },
    weekAndMonth: {
      name: "Viikkoa"
    },
    weekAndDayLetter: {
      name: "Viikot/viikonp\xE4iv\xE4t"
    },
    weekDateAndMonth: {
      name: "Kuukaudet/viikot"
    },
    monthAndYear: {
      name: "Kuukaudet"
    },
    year: {
      name: "Vuodet"
    },
    manyYears: {
      name: "Useita vuosia"
    }
  },
  RecurrenceConfirmationPopup: {
    "delete-title": "Olet poistamassa tapahtumaa",
    "delete-all-message": "Haluatko poistaa t\xE4m\xE4n tapahtuman kaikki merkinn\xE4t?",
    "delete-further-message": "Haluatko poistaa t\xE4m\xE4n tapahtuman ja kaikki tulevat tapahtumat vai vain valitun tapahtuman?",
    "delete-further-btn-text": "Poista kaikki tulevaisuuden tapahtumat",
    "delete-only-this-btn-text": "Poista vain t\xE4m\xE4 tapahtuma",
    "update-title": "Olet muuttamassa toistuvaa tapahtumaa",
    "update-all-message": "Haluatko muuttaa t\xE4m\xE4n tapahtuman kaikki merkinn\xE4t?",
    "update-further-message": "Haluatko muuttaa vain t\xE4m\xE4n tapahtuman vai t\xE4m\xE4n ja kaikki tulevat tapahtumat?",
    "update-further-btn-text": "Kaikki tulevaisuuden tapahtumat",
    "update-only-this-btn-text": "Vain t\xE4m\xE4 tapahtuma",
    Yes: "Kyll\xE4",
    Cancel: "Peruuta",
    width: 600
  },
  RecurrenceLegend: {
    " and ": " ja ",
    Daily: "P\xE4ivitt\xE4in",
    "Weekly on {1}": ({ days }) => `Viikoittain ${days}`,
    "Monthly on {1}": ({ days }) => `Kuukausittain ${days}`,
    "Yearly on {1} of {2}": ({ days, months }) => `Vuosittain ${days} of ${months}`,
    "Every {0} days": ({ interval }) => `Joka ${interval} days`,
    "Every {0} weeks on {1}": ({ interval, days }) => `Joka ${interval} viikko ${days}`,
    "Every {0} months on {1}": ({ interval, days }) => `Joka ${interval} kuukausi on ${days}`,
    "Every {0} years on {1} of {2}": ({ interval, days, months }) => `Joka ${interval} vuosi ${days} of ${months}`,
    position1: "the ensimm\xE4inen",
    position2: "toinen",
    position3: "kolmas",
    position4: "nelj\xE4s",
    position5: "viides",
    "position-1": "the last",
    day: "p\xE4iv\xE4",
    weekday: "viikonp\xE4iv\xE4",
    "weekend day": "viikonlopun p\xE4iv\xE4",
    daysFormat: ({ position, days }) => `${position} ${days}`
  },
  RecurrenceEditor: {
    "Repeat event": "Toista tapahtuma",
    Cancel: "Peruuta",
    Save: "Tallenna",
    Frequency: "Taajuus",
    Every: "Joka",
    DAILYintervalUnit: "day(s)",
    WEEKLYintervalUnit: "viikkoa",
    MONTHLYintervalUnit: "kuukautta",
    YEARLYintervalUnit: "vuotta",
    Each: "kukin",
    on: "Kuukauden p\xE4iv\xE4",
    the: "Ajankohta",
    "On the": "P\xE4iv\xE4n\xE4",
    "End repeat": "Loppu toisto",
    "time(s)": "ajastin(s)",
    Days: "P\xE4iv\xE4",
    Months: "Kuukaudet"
  },
  RecurrenceDaysCombo: {
    day: "p\xE4iv\xE4",
    weekday: "viikonp\xE4iv\xE4",
    "weekend day": "viikonlopun p\xE4iv\xE4"
  },
  RecurrencePositionsCombo: {
    position1: "ensimm\xE4inen",
    position2: "toinen",
    position3: "kolmas",
    position4: "nelj\xE4s",
    position5: "viides",
    "position-1": "viimeinen"
  },
  RecurrenceStopConditionCombo: {
    Never: "Ei koskaan",
    After: "J\xE4lkeen",
    "On date": "P\xE4iv\xE4n\xE4"
  },
  RecurrenceFrequencyCombo: {
    None: "Ei toistoa",
    Daily: "P\xE4ivitt\xE4in",
    Weekly: "Viikoittain",
    Monthly: "Kuukausittain",
    Yearly: "Vuosittain"
  },
  RecurrenceCombo: {
    None: "Ei ole",
    Custom: "Kustomoitu..."
  },
  Summary: {
    "Summary for": (date) => `Yhteenve ${date}`
  },
  ScheduleRangeCombo: {
    completeview: "T\xE4ydellinen aikataulu",
    currentview: "N\xE4kyv\xE4 aikataulu",
    daterange: "P\xE4iv\xE4m\xE4\xE4r\xE4alue",
    completedata: "T\xE4ydellinen aikataulu (kaikkien tapahtumien osalta)"
  },
  SchedulerExportDialog: {
    "Schedule range": "Aikataulualue",
    "Export from": "L\xE4hett\xE4j\xE4",
    "Export to": "Vastaanottaja"
  },
  ExcelExporter: {
    "No resource assigned": "Resurssia ei m\xE4\xE4ritetty"
  },
  CrudManagerView: {
    serverResponseLabel: "Vastaus palvelimelta:"
  },
  DurationColumn: {
    Duration: "Kesto"
  }
};
var Fi_default = LocaleHelper.publishLocale(locale);
export {
  Fi_default as default
};
