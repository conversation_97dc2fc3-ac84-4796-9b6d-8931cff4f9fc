class ViewPresetHeaderRow {
  /**
   * The text alignment for the cell. Valid values are `start` or `end`, omit this to center text content (default).
   * Can also be added programmatically in the {@link #config-renderer}
   * @config {'start'|'center'|'end'} align
   */
  /**
   * The unit of time represented by each cell in this header row. See also increment property.
   * Valid values are "millisecond", "second", "minute", "hour", "day", "week", "month", "quarter", "year".
   * @config {DurationUnit} unit
   */
  /**
   * A CSS class to add to the cells in the time axis header row.
   * Can also be added programmatically in the {@link #config-renderer}
   * @config {String} headerCellCls
   */
  /**
   * The number of units each header cell will represent (e.g. 30 together with unit: "minute" for 30 minute cells)
   * @config {Number} increment
   */
  /**
   * Defines how the cell date will be formatted
   * @config {String} dateFormat
   */
  /**
   * A custom renderer function used to render the cell content. It should return text/HTML to put in the header cell.
   * The render function is called with the following parameters:
   *
   * ```javascript
   * function (startDate, endDate, headerConfig, i) {
   *   headerConfig.align = "start"; // applies special CSS class to align header left
   *   headerConfig.headerCellCls = "myClass"; // will be added as a CSS class of the header cell DOM element
   *
   *   return DateHelper.format(startDate, 'YYYY-MM-DD');
   * }
   * ```
   *
   * @config {Function} renderer
   * @param {Date} startDate The start date of the cell.
   * @param {Date} endDate The end date of the cell.
   * @param {Object} headerConfig An object containing the header config.
   * @param {'start'|'center'|'end'} headerConfig.align The text alignment for the cell. See {@link #config-align} config.
   * @param {String} headerConfig.headerCellCls A CSS class to add to the cells in the time axis header row. See {@link #config-headerCellCls} config.
   * @param {Number} index The index of the cell in the row.
   * @param {Scheduler.view.TimelineBase} timeline The timeline widget (e.g. Scheduler or Gantt)
   * @returns {DomConfig|String|null}
   */
  /**
   * `this` reference for the renderer function
   * @config {Object} thisObj
   */
  /**
   * A function that should return an array of objects containing 'start', 'end' and 'header' properties.
   * Use this if you want full control over how the header rows are generated.
   *
   * **Note:** `cellGenerator` cannot be used for the bottom level of your headers.
   *
   * Example :
   *
   * ```javascript
   * viewPreset : {
   *     displayDateFormat : 'H:mm',
   *     shiftIncrement    : 1,
   *     shiftUnit         : 'WEEK',
   *     timeResolution    : {
   *         unit      : 'MINUTE',
   *         increment : 10
   *     },
   *     headers           : [
   *         {
   *             unit          : 'year',
   *             // Simplified scenario, assuming view will always just show one US fiscal year
   *             cellGenerator : (viewStart, viewEnd) => [{
   *                 start  : viewStart,
   *                 end    : viewEnd,
   *                 header : `Fiscal Year ${viewStart.getFullYear() + 1}`
   *             }]
   *         },
   *         {
   *             unit : 'quarter',
   *             renderer(start, end, cfg) {
   *                 const
   *                     quarter       = Math.floor(start.getMonth() / 3) + 1,
   *                     fiscalQuarter = quarter === 4 ? 1 : (quarter + 1);
   *
   *                 return `FQ${fiscalQuarter} ${start.getFullYear() + (fiscalQuarter === 1 ? 1 : 0)}`;
   *             }
   *         },
   *         {
   *             unit       : 'month',
   *             dateFormat : 'MMM Y'
   *         }
   *     ]
   *  },
   * ```
   *
   * @config {Function} cellGenerator
   * @param {Date} viewStart View start date
   * @param {Date} viewEnd View end date
   * @returns {Array}
   */
}
ViewPresetHeaderRow._$name = "ViewPresetHeaderRow";
export {
  ViewPresetHeaderRow as default
};
