var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Base from "@bryntum/core-thin/lib/Base.js";
import DomHelper from "@bryntum/core-thin/lib/helper/DomHelper.js";
import AsyncHelper from "@bryntum/core-thin/lib/helper/AsyncHelper.js";
import Duration from "@bryntum/core-thin/lib/data/Duration.js";
const defaultScrollOptions = {
  block: "nearest",
  edgeOffset: 20
};
var SchedulerScroll_default = (Target) => {
  var _a;
  return _a = class extends (Target || Base) {
    static get $name() {
      return "SchedulerScroll";
    }
    //region Scroll to event
    /**
     * Scrolls an event record into the viewport.
     * If the resource store is a tree store, this method will also expand all relevant parent nodes to locate the event.
     *
     * This function is not applicable for events with multiple assignments, please use #scrollResourceEventIntoView instead.
     *
     * @param {Scheduler.model.EventModel} eventRecord the event record to scroll into view
     * @param {BryntumScrollOptions} [options] How to scroll.
     * @returns {Promise} A Promise which resolves when the scrolling is complete.
     * @async
     * @category Scrolling
     */
    async scrollEventIntoView(eventRecord, options = defaultScrollOptions) {
      const me = this, resources = eventRecord.resources || [eventRecord];
      if (resources.length > 1) {
        throw new Error("scrollEventIntoView() is not applicable for events with multiple assignments, please use scrollResourceEventIntoView() instead.");
      }
      if (!resources.length) {
        console.warn("You have asked to scroll to an event which is not assigned to a resource");
      }
      await me.scrollResourceEventIntoView(resources[0], eventRecord, options);
    }
    /**
     * Scrolls an assignment record into the viewport.
     *
     * If the resource store is a tree store, this method will also expand all relevant parent nodes
     * to locate the event.
     *
     * @param {Scheduler.model.AssignmentModel} assignmentRecord A resource record an event record is assigned to
     * @param {BryntumScrollOptions} [options] How to scroll.
     * @returns {Promise} A Promise which resolves when the scrolling is complete.
     * @category Scrolling
     */
    scrollAssignmentIntoView(assignmentRecord, ...args) {
      return this.scrollResourceEventIntoView(assignmentRecord.resource, assignmentRecord.event, ...args);
    }
    /**
     * Scrolls a resource event record into the viewport.
     *
     * If the resource store is a tree store, this method will also expand all relevant parent nodes
     * to locate the event.
     *
     * @param {Scheduler.model.ResourceModel} resourceRecord A resource record an event record is assigned to
     * @param {Scheduler.model.EventModel} eventRecord An event record to scroll into view
     * @param {BryntumScrollOptions} [options] How to scroll.
     * @returns {Promise} A Promise which resolves when the scrolling is complete.
     * @category Scrolling
     * @async
     */
    async scrollResourceEventIntoView(resourceRecord, eventRecord, options = defaultScrollOptions) {
      var _a2, _b;
      const me = this, {
        store,
        timeAxis,
        visibleDateRange,
        scrollExtensionThreshold
      } = me, currentTimeSpanRange = timeAxis.endDate - timeAxis.startDate, eventStart = eventRecord.startDate, eventEnd = eventRecord.endDate, eventStartMs = eventRecord.isScheduled && eventStart.getTime(), eventEndMs = eventRecord.isScheduled && eventEnd.getTime(), eventIsOutside = eventRecord.isScheduled && eventStart < me.timeAxis.startDate | (eventEnd > me.timeAxis.endDate) << 1;
      let timeSpanWasExpanded = false;
      let el;
      if (options.edgeOffset == null) {
        options.edgeOffset = 20;
      }
      if (eventIsOutside && options.extendTimeAxis !== false) {
        if (eventIsOutside === 3) {
          await me.setTimeSpan(
            new Date(eventStartMs - currentTimeSpanRange / 2),
            new Date(eventEndMs + currentTimeSpanRange / 2)
          );
        } else if (me.infiniteScroll) {
          const extendedTimeSpanRange = {
            start: timeAxis.startDate.getTime(),
            end: timeAxis.endDate.getTime()
          };
          const extensionMs = scrollExtensionThreshold ? new Duration(scrollExtensionThreshold).milliseconds : currentTimeSpanRange;
          if (eventIsOutside === 1) {
            extendedTimeSpanRange.start -= extensionMs;
          } else {
            extendedTimeSpanRange.end += extensionMs;
          }
          const eventWillBeInsideExtendedTimeSpan = eventStartMs >= extendedTimeSpanRange.start && eventEndMs <= extendedTimeSpanRange.end;
          if (eventWillBeInsideExtendedTimeSpan) {
            await me.setTimeSpan(
              new Date(extendedTimeSpanRange.start),
              new Date(extendedTimeSpanRange.end),
              {
                visibleDate: visibleDateRange.startDate
              }
            );
            timeSpanWasExpanded = true;
          } else {
            const visibleMS = visibleDateRange.endMS - visibleDateRange.startMS, sign = eventIsOutside & 1 ? 1 : -1, visibleDate = sign === 1 ? new Date(eventEndMs + visibleMS) : new Date(eventStartMs - visibleMS);
            await me.setTimeSpan(
              new Date(eventStartMs - currentTimeSpanRange / 2),
              new Date(eventStartMs + currentTimeSpanRange / 2),
              {
                visibleDate
              }
            );
          }
        } else {
          const currentPreset = me.presets.allRecords[me.zoomLevel], edgeOffset = options.edgeOffset * me.getMilliSecondsPerPixelForZoomLevel(currentPreset, false);
          if (eventIsOutside & 1) {
            await me.setTimeSpan(
              // Add time equal to the edgeOffset
              new Date(eventStartMs - edgeOffset),
              new Date(eventStartMs - edgeOffset + currentTimeSpanRange)
            );
          } else {
            await me.setTimeSpan(
              new Date(eventEndMs + edgeOffset - currentTimeSpanRange),
              new Date(eventEndMs + edgeOffset)
            );
          }
        }
      }
      if (me.isDestroyed) {
        return;
      }
      if (store.tree) {
        await ((_a2 = me.expandTo) == null ? void 0 : _a2.call(me, resourceRecord));
      }
      if (store.isGrouped) {
        await store.expand(store.getGroupHeaderForRecord(resourceRecord));
      }
      if (((_b = me.features.nestedEvents) == null ? void 0 : _b.enabled) && eventRecord.parent && !eventRecord.parent.isRoot) {
        await me.scrollEventIntoView(eventRecord.parent);
      }
      el = me.getElementFromEventRecord(eventRecord, resourceRecord);
      if (el) {
        if (!DomHelper.isFocusable(el)) {
          el = el.parentNode;
        }
        const scroller = me.timeAxisSubGrid.scrollable;
        await scroller.scrollIntoView(el, options);
        if (me.isDestroyed) {
          return;
        }
        let element;
        do {
          element = me.getElementFromEventRecord(eventRecord, resourceRecord);
          if (!element) {
            await AsyncHelper.animationFrame();
          }
          if (me.isDestroyed) {
            return;
          }
        } while (!element);
      } else if (eventIsOutside === 3 && options.extendTimeAxis === false) {
        console.warn("You have asked to scroll to an event which is outside the current view and extending timeaxis is disabled");
      } else if (!eventRecord.isOccurrence && me.eventStore.isFilteredOut(eventRecord)) {
        console.warn("You have asked to scroll to an event which is not available");
      } else if (eventRecord.isScheduled) {
        await me.scrollUnrenderedEventIntoView(resourceRecord, eventRecord, options);
      } else {
        await me.scrollResourceIntoView(resourceRecord, options);
      }
      if (timeSpanWasExpanded) {
        if (eventIsOutside === 1) {
          await me.setTimeSpan(
            timeAxis.startDate,
            new Date(timeAxis.endDate.getTime() - currentTimeSpanRange),
            {
              maintainVisibleStart: true
            }
          );
        } else {
          await me.setTimeSpan(
            new Date(timeAxis.startDate.getTime() + currentTimeSpanRange),
            timeAxis.endDate,
            {
              maintainVisibleStart: true
            }
          );
        }
      }
    }
    /**
     * Scrolls an unrendered event into view. Internal function used from #scrollResourceEventIntoView.
     * @private
     * @category Scrolling
     */
    scrollUnrenderedEventIntoView(resourceRec, eventRec, options = defaultScrollOptions) {
      return new Promise(async (resolve) => {
        const me = this, scroller = me.timeAxisSubGrid.scrollable, scrollerViewport = scroller.viewport, { rowManager } = me, initialY = scroller.y;
        if (!scrollerViewport) {
          resolve();
          return;
        }
        const instantScrollOptions = Object.assign({}, defaultScrollOptions);
        let eventElement, delta, counter = 0;
        do {
          if (++counter >= 50) {
            throw new Error(`Too many preparational scrolls during 'scrollIntoView' for event id = ${eventRec.id}`);
          }
          const box = me.getResourceEventBox(eventRec, resourceRec);
          if (!box) {
            resolve();
            return;
          }
          box.x = Math.ceil(box.x);
          box.y = Math.ceil(box.y);
          if (me.rtl) {
            box._x = -(box.x - scrollerViewport.width);
          }
          box.translate(scrollerViewport.x - scroller.scrollLeft, scrollerViewport.y - scroller.y);
          if (delta === void 0) {
            delta = scroller.getDeltaTo(box, instantScrollOptions);
          }
          const scrollPromise = scroller.scrollIntoView(box, options);
          await scrollPromise;
          if (scrollPromise.cancelled || me.isDestroyed) {
            resolve();
            return true;
          }
          await AsyncHelper.animationFrame();
          if (me.isDestroyed) {
            resolve();
            return true;
          }
          eventElement = me.getElementFromEventRecord(eventRec, resourceRec);
        } while (!eventElement);
        scroller.suspendEvents();
        if (delta.yDelta >= 0) {
          scroller.y = Math.max(rowManager.topRow.top - scroller.viewport.height, initialY);
        } else {
          scroller.y = Math.min(rowManager.bottomRow.bottom, initialY);
        }
        me.fixElementHeights();
        scroller.resumeEvents();
        const scrollPromise2 = scroller.scrollIntoView(
          eventElement,
          instantScrollOptions
        );
        await scrollPromise2;
        if (scrollPromise2.canceled || me.isDestroyed) {
          resolve();
          return true;
        }
        await AsyncHelper.animationFrame();
        resolve();
      });
    }
    /**
     * Scrolls the specified resource into view, works for both horizontal and vertical modes.
     * @param {Scheduler.model.ResourceModel} resourceRecord A resource record an event record is assigned to
     * @param {BryntumScrollOptions} [options] How to scroll.
     * @returns {Promise} A promise which is resolved when the scrolling has finished.
     * @category Scrolling
     */
    scrollResourceIntoView(resourceRecord, options = defaultScrollOptions) {
      if (this.isVertical) {
        return this.currentOrientation.scrollResourceIntoView(resourceRecord, options);
      }
      return this.scrollRowIntoView(resourceRecord, options);
    }
    //endregion
    // This does not need a className on Widgets.
    // Each *Class* which doesn't need 'b-' + constructor.name.toLowerCase() automatically adding
    // to the Widget it's mixed in to should implement this.
    get widgetClass() {
    }
  }, __publicField(_a, "configurable", {
    /**
     * Specifies the maximum duration for extending the current {@link Scheduler.model.TimeSpan} to allow for smooth
     * scrolling to an event.
     *
     * This configuration determines how far the scheduler should extend the visible timespan to enable smooth
     * scrolling. If the target event is within this duration from the current view, the scheduler will animate the
     * scroll. If the event lies beyond this threshold, the scheduler will directly jump to it without animation.
     *
     * Setting a shorter duration can enhance performance in views with many events, as it limits the number of
     * events that need to be rendered when extending the timespan.
     *
     * The value can be specified as a string or a {@link DurationConfig} object, allowing for flexible duration
     * definitions, such as '1 week' for one week or `{unit: 'week', magnitude: 1}`.
     *
     * Default value is the duration of the currently rendered timespan at the time of invocation.
     * @config {String|DurationConfig}
     */
    scrollExtensionThreshold: null
  }), _a;
};
export {
  SchedulerScroll_default as default
};
