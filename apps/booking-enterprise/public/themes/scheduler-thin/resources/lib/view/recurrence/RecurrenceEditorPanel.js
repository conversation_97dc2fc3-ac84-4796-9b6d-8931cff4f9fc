var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
import Panel from "@bryntum/core-thin/lib/widget/Panel.js";
import "@bryntum/core-thin/lib/widget/Widget.js";
import "@bryntum/core-thin/lib/widget/Button.js";
import "@bryntum/core-thin/lib/widget/Checkbox.js";
import "@bryntum/core-thin/lib/widget/SlideToggle.js";
import "@bryntum/core-thin/lib/widget/DateField.js";
import "@bryntum/core-thin/lib/widget/NumberField.js";
import "./field/RecurrenceFrequencyCombo.js";
import "./field/RecurrenceDaysCombo.js";
import "./field/RecurrenceDaysButtonGroup.js";
import "./field/RecurrenceMonthDaysButtonGroup.js";
import "./field/RecurrenceMonthsButtonGroup.js";
import "./field/RecurrenceStopConditionCombo.js";
import "./field/RecurrencePositionsCombo.js";
import BrowserHelper from "@bryntum/core-thin/lib/helper/BrowserHelper.js";
import DateHelper from "@bryntum/core-thin/lib/helper/DateHelper.js";
import ArrayHelper from "@bryntum/core-thin/lib/helper/ArrayHelper.js";
class RecurrenceEditorPanel extends Panel {
  changeItems(items, oldItems) {
    const result = super.changeItems(items, oldItems), {
      positionAndDayRadioField,
      monthDaysNumberRadioField,
      monthDaysNumberField
    } = this.widgetMap;
    if (monthDaysNumberRadioField) {
      positionAndDayRadioField.label = this.L("L{RecurrenceEditor.the}");
    } else {
      positionAndDayRadioField.label = this.L("L{RecurrenceEditor.On the}");
      monthDaysNumberField && result.remove(monthDaysNumberField);
    }
    return result;
  }
  setupWidgetConfig(widgetConfig) {
    if (BrowserHelper.isMobile && !("editable" in widgetConfig)) {
      widgetConfig.editable = false;
    }
    return super.setupWidgetConfig(...arguments);
  }
  updateRecord(record) {
    var _a, _b, _c;
    super.updateRecord(record);
    const me = this, {
      frequencyField,
      daysButtonField,
      monthDaysButtonField,
      monthDaysNumberField,
      monthsButtonField,
      monthDaysRadioField,
      monthDaysNumberRadioField,
      positionAndDayRadioField,
      stopRecurrenceField
    } = me.widgetMap;
    if (record) {
      const timeSpanValues = record.getDateValues();
      if (timeSpanValues) {
        if (!((_a = record.days) == null ? void 0 : _a.length)) {
          daysButtonField.value = timeSpanValues.days;
        }
        if (!((_b = record.monthDays) == null ? void 0 : _b.length)) {
          if (monthDaysButtonField) {
            monthDaysButtonField.value = timeSpanValues.monthDays;
          }
          if (monthDaysNumberField) {
            monthDaysNumberField.value = timeSpanValues.monthDays[0];
          }
        }
        if (!((_c = record.months) == null ? void 0 : _c.length) && monthsButtonField) {
          monthsButtonField.value = timeSpanValues.months[0];
        }
      }
      if (daysButtonField.value && record.positions) {
        positionAndDayRadioField.check();
        if (!me.isPainted) {
          monthDaysRadioField.uncheck();
        }
      } else if (monthDaysButtonField == null ? void 0 : monthDaysButtonField.value) {
        if (monthDaysNumberRadioField && !monthDaysNumberRadioField.hidden) {
          monthDaysNumberRadioField.check();
        } else {
          monthDaysRadioField.check();
        }
        if (!me.isPainted || !me.element.isConnected) {
          positionAndDayRadioField.uncheck();
        }
      }
      if (stopRecurrenceField) {
        stopRecurrenceField.recurrence = record;
      }
    } else {
      frequencyField.value = "NONE";
    }
  }
  //#endregion Configs
  // Disabled field does not contribute to values, clear manually
  get valuesCleanState() {
    return {
      frequency: null,
      interval: null,
      days: null,
      months: null,
      monthDays: null,
      positions: null,
      endDate: null,
      count: null
    };
  }
  processValues(result) {
    if ("monthDays" in result) {
      if (typeof result.monthDays === "string") {
        result.monthDays = result.monthDays.split(",");
      } else {
        result.monthDays = ArrayHelper.asArray(result.monthDays);
      }
    }
  }
  get values() {
    const result = { ...this.valuesCleanState, ...super.values };
    this.processValues(result);
    return result;
  }
  set values(values) {
    super.values = values;
  }
  getValues() {
    const result = { ...this.valuesCleanState, ...super.getValues(...arguments) };
    this.processValues(result);
    return result;
  }
  /**
   * Updates the provided recurrence model with the contained form data.
   * If recurrence model is not provided updates the last loaded recurrence model.
   * @internal
   */
  syncEventRecord(recurrence) {
    recurrence = recurrence || this.record;
    if (recurrence) {
      const values = this.getValues((w) => w.name in recurrence && !w.disabled);
      recurrence.set(values);
    }
  }
  toggleStopFields(userAction) {
    var _a;
    const me = this, stopValue = (_a = me.widgetMap.stopRecurrenceField) == null ? void 0 : _a.value, { countField, endDateField } = me.widgetMap, countIsUsed = stopValue === "count", dateIsUsed = !countIsUsed && stopValue === "date";
    countField._userAction = endDateField._userAction = userAction;
    countField.disabled = countField.hidden = !countIsUsed;
    endDateField.disabled = endDateField.hidden = !dateIsUsed;
    countField._userAction = endDateField._userAction = false;
  }
  isWidgetAvailableForFrequency(widget, frequency = this.widgetMap.frequencyField.value) {
    return !widget.forFrequency || widget.forFrequency.includes(frequency);
  }
  //#region Events
  onFieldChange({ source, userAction }) {
    const { record } = this;
    if (userAction && this.autoUpdateRecord && record) {
      record.isSanitizingSuspended = true;
    }
    super.onFieldChange(...arguments);
    if (userAction && this.autoUpdateRecord && record) {
      record.isSanitizingSuspended = false;
      this.setTimeout({
        fn: () => record.sanitize(),
        delay: 50,
        cancelOutstanding: true
      });
    }
  }
  onFieldBeforeUpdateDisabled({ source, disabled }) {
    if (!disabled && source._userAction) {
      source.triggerFieldChange({ userAction: true });
    }
  }
  onMonthDaysRadioFieldChange({ source, checked, userAction, propagatingUserActionFrom }) {
    const { monthDaysButtonField, emptyMonthsHiddenField } = this.widgetMap;
    if (monthDaysButtonField) {
      monthDaysButtonField._userAction = userAction;
      monthDaysButtonField.disabled = !checked || !this.isWidgetAvailableForFrequency(monthDaysButtonField);
      monthDaysButtonField._userAction = false;
    }
    if (emptyMonthsHiddenField) {
      emptyMonthsHiddenField._userAction = userAction || propagatingUserActionFrom;
      emptyMonthsHiddenField.disabled = checked || !this.isWidgetAvailableForFrequency(emptyMonthsHiddenField);
      emptyMonthsHiddenField._userAction = false;
    }
  }
  onMonthsButtonFieldChange({ value }) {
    const monthNames = DateHelper.getMonthNames(), { monthDaysNumberField } = this.widgetMap;
    if (value && monthDaysNumberField) {
      monthDaysNumberField.hint = value.split(",").map((month) => monthNames[month - 1]).join(", ");
    }
  }
  onMonthDaysNumberRadioFieldChange({ checked, userAction }) {
    const { monthDaysNumberField } = this.widgetMap;
    monthDaysNumberField._userAction = userAction;
    monthDaysNumberField.disabled = !checked || !this.isWidgetAvailableForFrequency(monthDaysNumberField);
    monthDaysNumberField._userAction = false;
  }
  onPositionAndDayRadioFieldChange({ source, checked, userAction }) {
    const { daysCombo, positionsCombo } = this.widgetMap;
    daysCombo._userAction = positionsCombo._userAction = userAction;
    daysCombo.disabled = positionsCombo.disabled = !checked || !this.isWidgetAvailableForFrequency(daysCombo);
    daysCombo._userAction = positionsCombo._userAction = false;
  }
  onStopRecurrenceFieldChange({ userAction }) {
    this.toggleStopFields(userAction);
  }
  onFrequencyFieldChange({ value, oldValue, valid, userAction }) {
    const me = this, items = me.queryAll((w) => "forFrequency" in w), {
      intervalField,
      stopRecurrenceField
    } = me.widgetMap, isNone = value === "NONE";
    if (valid && value) {
      for (const item of items) {
        item._userAction = userAction;
        item.disabled = item.hidden = !me.isWidgetAvailableForFrequency(item, value);
        item._userAction = false;
      }
      if (stopRecurrenceField) {
        stopRecurrenceField.hidden = isNone;
      }
      if (intervalField) {
        intervalField.hidden = isNone;
        if (!isNone) {
          intervalField.hint = me.L(`L{RecurrenceEditor.${value}intervalUnit}`);
        }
        if (oldValue === "NONE" && intervalField.value == null) {
          intervalField.value = 1;
        }
      }
      me.toggleFieldsState();
    }
  }
  //#endregion Events
  toggleFieldsState(userAction) {
    const me = this, { widgetMap } = me, { monthDaysRadioField } = widgetMap;
    if (monthDaysRadioField) {
      me.onMonthDaysRadioFieldChange({ checked: monthDaysRadioField.checked, userAction });
    }
    me.onPositionAndDayRadioFieldChange({ checked: widgetMap.positionAndDayRadioField.checked, userAction });
    me.onStopRecurrenceFieldChange({ userAction });
  }
  updateLocalization() {
    const { countField, intervalField, frequencyField } = this.widgetMap;
    countField.hint = this.L("L{RecurrenceEditor.time(s)}");
    if (frequencyField.value && frequencyField.value !== "NONE") {
      intervalField.hint = this.L(`L{RecurrenceEditor.${frequencyField.value}intervalUnit}`);
    }
    super.updateLocalization();
  }
}
__publicField(RecurrenceEditorPanel, "$name", "RecurrenceEditorPanel");
__publicField(RecurrenceEditorPanel, "type", "recurrenceeditorpanel");
//#region Configs
__publicField(RecurrenceEditorPanel, "configurable", {
  focusable: false,
  cls: "b-recurrenceeditor",
  record: false,
  addNone: false,
  strictRecordMapping: true,
  items: {
    frequencyField: {
      type: "recurrencefrequencycombo",
      name: "frequency",
      label: "L{RecurrenceEditor.Frequency}",
      weight: 10,
      onChange: "up.onFrequencyFieldChange",
      addNone: "up.addNone"
    },
    intervalField: {
      type: "numberfield",
      weight: 15,
      name: "interval",
      label: "L{RecurrenceEditor.Every}",
      min: 1,
      required: true
    },
    daysLabel: {
      type: "label",
      text: "L{RecurrenceEditor.Days}",
      weight: 18,
      forFrequency: "WEEKLY",
      cls: "b-button-group-label"
    },
    daysButtonField: {
      type: "daybuttons",
      cls: "b-recurrencedaysbuttongroup",
      weight: 20,
      name: "days",
      forFrequency: "WEEKLY",
      useGap: true,
      required: true
    },
    emptyMonthsHiddenField: {
      type: "field",
      inputType: "hidden",
      name: "monthDays",
      forFrequency: "MONTHLY",
      internalListeners: {
        beforeUpdateDisabled: "up.onFieldBeforeUpdateDisabled",
        // Enforce empty value for this field.
        // It's used specially to reset monthDays when toggling states
        change() {
          this._value = null;
        }
      }
    },
    // the radio button enabling "monthDaysButtonField" in MONTHLY mode
    monthDaysRadioField: {
      type: "checkbox",
      weight: 30,
      toggleGroup: "radio",
      toggleGroupRootElement: ".b-recurrenceeditorpanel",
      forFrequency: "MONTHLY",
      label: "L{RecurrenceEditor.Each}",
      checked: true,
      onChange: "up.onMonthDaysRadioFieldChange",
      cls: "b-label-with-checkbox"
    },
    monthDaysButtonField: {
      type: "recurrencemonthdaysbuttongroup",
      weight: 40,
      name: "monthDays",
      forFrequency: "MONTHLY",
      cls: "b-space-above",
      required: true,
      internalListeners: {
        beforeUpdateDisabled: "up.onFieldBeforeUpdateDisabled"
      }
    },
    // the radio button enabling monthDays number field in YEARLY modes
    monthDaysNumberRadioField: {
      type: "checkbox",
      weight: 53,
      cls: "b-label-with-checkbox",
      toggleGroup: "radio",
      toggleGroupRootElement: ".b-recurrenceeditorpanel",
      forFrequency: "YEARLY",
      label: "L{RecurrenceEditor.on}",
      style: "display:inline-flex;",
      onChange: "up.onMonthDaysNumberRadioFieldChange"
    },
    monthDaysNumberField: {
      type: "numberfield",
      weight: 56,
      flex: 1,
      name: "monthDays",
      forFrequency: "YEARLY",
      min: 1,
      max: 31,
      required: true,
      internalListeners: {
        beforeUpdateDisabled: "up.onFieldBeforeUpdateDisabled"
      }
    },
    monthsLabel: {
      type: "label",
      text: "L{RecurrenceEditor.Months}",
      weight: 57,
      forFrequency: "YEARLY",
      cls: "b-button-group-label"
    },
    monthsButtonField: {
      type: "recurrencemonthsbuttongroup",
      weight: 58,
      cls: "b-space-above b-space-below",
      name: "months",
      forFrequency: "YEARLY",
      onChange: "up.onMonthsButtonFieldChange",
      required: true
    },
    positionAndDayRadioField: {
      type: "checkbox",
      weight: 60,
      cls: "b-label-with-checkbox b-space-below",
      toggleGroup: "radio",
      toggleGroupRootElement: ".b-recurrenceeditorpanel",
      forFrequency: "MONTHLY|YEARLY",
      label: "L{RecurrenceEditor.On the}",
      onChange: "up.onPositionAndDayRadioFieldChange"
    },
    positionsCombo: {
      type: "recurrencepositionscombo",
      weight: 80,
      name: "positions",
      forFrequency: "MONTHLY|YEARLY",
      cls: "b-space-below b-no-span",
      internalListeners: {
        beforeUpdateDisabled: "up.onFieldBeforeUpdateDisabled"
      }
    },
    daysCombo: {
      type: "recurrencedayscombo",
      weight: 90,
      name: "days",
      forFrequency: "MONTHLY|YEARLY",
      cls: "b-space-below b-no-span",
      internalListeners: {
        beforeUpdateDisabled: "up.onFieldBeforeUpdateDisabled"
      }
    },
    stopRecurrenceField: {
      type: "recurrencestopconditioncombo",
      weight: 100,
      label: "L{RecurrenceEditor.End repeat}",
      onChange: "up.onStopRecurrenceFieldChange",
      cls: "b-no-inner-span"
    },
    countField: {
      type: "numberfield",
      weight: 110,
      name: "count",
      min: 2,
      required: true,
      disabled: true,
      label: " ",
      cls: "b-no-span",
      internalListeners: {
        beforeUpdateDisabled: "up.onFieldBeforeUpdateDisabled"
      }
    },
    endDateField: {
      type: "datefield",
      weight: 120,
      name: "endDate",
      hidden: true,
      disabled: true,
      label: " ",
      required: true,
      cls: "b-no-span",
      internalListeners: {
        beforeUpdateDisabled: "up.onFieldBeforeUpdateDisabled"
      }
    }
  }
});
RecurrenceEditorPanel.initClass();
RecurrenceEditorPanel._$name = "RecurrenceEditorPanel";
export {
  RecurrenceEditorPanel as default
};
