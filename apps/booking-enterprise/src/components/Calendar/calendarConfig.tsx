import { type Calendar } from "@bryntum/calendar-thin"
import { type BryntumCalendarProps } from "@bryntum/calendar-react-thin"
import { useCalendarOptions } from "src/stores/calendarStore"
import { DateHelper } from "@bryntum/core-thin"
import { ReservationModel } from "src/utils/models/ReservationModel"
import { SpaceModel } from "src/utils/models/SpaceModel"

const bookingCalendarEventsUrl =
  process.env.NODE_ENV === "development"
    ? `http://localhost:9000/booking/get-bookings`
    : `/app/site/hosting/restlet.nl?script=customscript_ng_cses_rl_get_bookings&deploy=customdeploy_ng_cses_rl_get_bookings&action=getBookings&project=false`

export const useCalendarConfig = (): BryntumCalendarProps => {
  const { options, setEndDate, setStartDate } = useCalendarOptions()
  // Start life looking at this date
  return {
    date: options.startDate,
    minHeight: "calc(100vh - 128px)",
    weekExpanderFeature: true,
    // Show the event list
    modes: {
      list: {
        // If we use field names which the EventList creates for itself, our config
        // gets merged into the default, so we can affect the EventList's own columns.
        // columns: [
        //   {
        //     type: 'tree',
        //     text: 'Venue',
        //     field: 'name',
        //     flex: 1,
        //     minWidth: 250,
        //     autoTree: true,
        //   },
        //   {
        //     type: 'column',
        //     field: 'name',
        //     flex: '0 0 12em',
        //     // renderer({ record }: { record: ProEvent }) {
        //     //   return [
        //     //     {
        //     //       tag: 'i',
        //     //       className: 'b-icon b-icon-circle',
        //     //       style: `color:${record.resource?.color}`,
        //     //     },
        //     //     {
        //     //       text: record.name,
        //     //     },
        //     //   ];
        //     // },
        //   },
        // ],
        features: {
          headerMenu: {
            // We can't group by other fields, so disable all grouping menu options
            items: {
              groupAsc: false,
              groupDesc: false,
              groupRemove: false,
            },
          },
          stripe: true,
          group: {
            field: "startDate",
            // Render the group date for the first (group field) column
            renderer({ rowElement, grid, isFirstColumn, groupRowFor }: any) {
              if (isFirstColumn) {
                // the second argument dateFormat should be precisely as 'YYYY-MM-DD'
                rowElement.dataset.date = DateHelper.format(groupRowFor, "YYYY-MM-DD")
                return DateHelper.format(groupRowFor, grid.dateFormat)
              }
              return ""
            },
          },
        },
      },
    },
    // onDateRangeChange: (event) => {
    //   console.log('onDateRangeChange', event);
    //   const { startDate, endDate } = event.new;
    //
    //   setEndDate(endDate);
    //   setStartDate(startDate);
    // },
    // CrudManager arranges loading and syncing of data in JSON form from/to a web service
    // Crud manager setup
    crudManager: {
      validateResponse: process.env.NODE_ENV === "development",
      autoLoad: true,
      resourceStore: {
        modelClass: SpaceModel,
      },
      eventStore: {
        modelClass: ReservationModel,
      },
      transport: {
        load: {
          credentials: "same-origin",
          url: bookingCalendarEventsUrl,
          params: {
            startDate: options.startDate,
            endDate: options.endDate,
          },
        },
        sync: {
          url: bookingCalendarEventsUrl,
          params: {
            sync: true,
          },
          credentials: "same-origin",
        },
      },
      encoder: {
        requestData: {
          startDate: options.startDate,
          endDate: options.endDate,
        },
      },
    },
    // A block of configs which is applied to all modes.
    modeDefaults: {
      // Allows us to see details in a crowded day
      zoomOnMouseWheel: true,
    },
    // sidebar: {
    //   // items: {
    //   //   // This is the "ref" of the new field
    //   //   resourceFilterFilter: {
    //   //     // Inserts just before the resourceFilter List
    //   //     weight: 190,
    //   //
    //   //     // Shows a clear trigger
    //   //     clearable: true,
    //   //
    //   //     label: 'Filter resources',
    //   //     placeholder: 'Filter resources',
    //   //     labelPosition: 'above',
    //   //     type: 'textfield',
    //   //     keyStrokeChangeDelay: 100,
    //   //   },
    //   // },
    // },
  }
}
