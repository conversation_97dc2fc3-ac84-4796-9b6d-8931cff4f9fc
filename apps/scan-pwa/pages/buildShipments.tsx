import React, { useEffect, useRef, useState } from 'react'
import Page from '@/components/page'
import Section from '@/components/section'
import {
	Alert,
	AppBar,
	Autocomplete,
	Box,
	Button,
	Checkbox,
	Chip, CircularProgress,
	Dialog,
	Divider,
	FormControlLabel,
	FormGroup,
	Grid,
	IconButton,
	InputAdornment,
	List,
	ListItem,
	Snackbar,
	TextField,
	Toolbar,
	Typography
} from '@mui/material'
import { openDB, deleteDB, wrap, unwrap } from 'idb';
import WebcamComponent from './webCamComponent'
import useNetworkStatus from '../hooks/useNetworkStatus'
import { AuthGuard } from '../guards/AuthGuard'
import 'react-html5-camera-photo/build/css/index.css'
import { useCurrentEvent, useEventData } from '../store/zEventStore'
import { useSession } from 'next-auth/react'
import NoAuthPage from '@/components/NoAuthPage'
import * as Yup from 'yup'
import { useShipmentsDataStore } from '../store/zShipmentsStore'
import { Montserrat } from 'next/font/google'
import { Formik } from 'formik'
import imageCompression from 'browser-image-compression'
import { LoadingButton } from '@mui/lab'
import ClearIcon from '@mui/icons-material/Clear'
import CloseIcon from '@mui/icons-material/Close'
import CameraAltIcon from '@mui/icons-material/CameraAlt'
import { Html5QrcodeScanner, Html5Qrcode } from 'html5-qrcode'
import { UserProfile } from '@/types/user'
import { useSnackbar } from 'notistack'

const montserrat = Montserrat({
	weight: ['100', '200', '300', '400', '500', '600', '800', '900'],
	subsets: ['latin'],
	style: ['normal', 'italic'],
})

const videoConstraints = {
	facingMode: 'user',
}

const BuildShipments = () => {
	const { data: session, status } = useSession()
	const [selectedItem, setSelectedItem] = useState('')
	const [selectedCarrier, setSelectedCarrier] = useState('')
	const [notesText, setNotesText] = useState('')
	const [noCratesText, setNoCratesText] = useState('')
	const [noSkidsText, setNoSkidsText] = useState('')
	const [noFibersText, setNoFibersText] = useState('')
	const [noCartonsText, setNoCartonsText] = useState('')
	const [bolWeightText, setBolWeightText] = useState('')
	const [noOtherText, setNoOtherText] = useState('')
	const [shipmentType, setShipmentType] = useState('')
	const [trackingNumber, setTrackingNumber] = useState('')
	const [cameraOpen, setCameraOpen] = useState(false)
	const [openShipmentSavedSnackbar, setShipmentSavedSnackbar] = useState(false)
	const [openMaxImageSnackbar, setOpenMaxImageSnackbar] = useState(false)
	const [isloadingImage, setIsloadingImage] = useState(false)
	const scannerRef = useRef<Html5Qrcode | null>(null);
	const addButtonRef = useRef(null)
	const { enqueueSnackbar, closeSnackbar } = useSnackbar()

	const [showCamera, setShowCamera] = useState(false)
	const [previewImgUrl, setPreviewImageUrl] = useState<null | string>(null)
	const [imageUrl, setImageUrl] = useState<string[]>([])
	const [openDialog, setOpenDialog] = useState(false)

	const { currentShipmentsData, setCurrentShipmentsData } = useShipmentsDataStore()
	const { currentEvent, setCurrentEvent } = useCurrentEvent()
	const { currentEventData, setCurrentEventData } = useEventData()
	const { isOnline } = useNetworkStatus()

	useEffect(() => {
		if (!openDialog && addButtonRef.current) {
			// @ts-ignore
			addButtonRef.current.focus()
		}
	}, [openDialog])

	const handleItemChange = (e: any) => {
		console.log('handleItemChange', e)
		setSelectedItem(e.target.value)
	}

	const handleCarrierChange = (e: any) => {
		setSelectedCarrier(e.target.value)
	}

	const handleShipmentTypeChange = (e: any) => {
		console.log('handleShipmentTypeChange', e)
		setShipmentType(e.target.value)
	}

	const handleNotesTextChange = (e: any) => {
		setNotesText(e.target.value)
	}

	const handleNoCratesTextChange = (e: any) => {
		setNoCratesText(e.target.value)
	}

	const handleNoSkidsTextChange = (e: any) => {
		setNoSkidsText(e.target.value)
	}

	const handleNoFibersTextChange = (e: any) => {
		setNoFibersText(e.target.value)
	}

	const handleNoCartonsTextChange = (e: any) => {
		setNoCartonsText(e.target.value)
	}

	const handleBolWeightTextChange = (e: any) => {
		setBolWeightText(e.target.value)
	}

	const handleNoOtherTextChange = (e: any) => {
		setNoOtherText(e.target.value)
	}

	const handleTrackingInput = (e: any) => {
		setTrackingNumber(e.target.value)
	}

	const handleShipmentSubmitClose = () => {
		// if (reason === 'clickaway') {
		// 	return;
		// }

		setShipmentSavedSnackbar(false)
	}

	const handleSubmitShipment = async (
		_values: any,
		setStatus: any,
		setSubmitting: any,
		resetForm: any,
	) => {
		const shipmentId = () => {
			return Date.now().toString(36) + Math.random().toString(36).substr(2)
		}
		_values = {
			..._values,
			event: currentEvent.event,
			booth: currentEvent.booth,
			id: shipmentId(),
		}

		try {
			setCurrentShipmentsData(_values)

			resetForm()
			setTrackingNumber('')
			setSelectedItem('')
			setSelectedCarrier('')
			setShipmentType('')
			setNoCratesText('')
			setNoSkidsText('')
			setNoFibersText('')
			setNoCartonsText('')
			setBolWeightText('')
			setNoOtherText('')
			setNotesText('')
			setImageUrl([])
			setPreviewImageUrl(null)
			setShipmentSavedSnackbar(true)
		} catch (e: any) {
			console.log('Error saving shipment', e)

			if (e.name === 'QuotaExceededError') {
				enqueueSnackbar('Shipment list full.  Submit or remove existing shipments to create more.', {
					variant: 'error',
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right',
					}
				})
			} else {
				enqueueSnackbar('Error submitting shipment.', {
					variant: 'error',
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right',
					}
				})
			}
		}

	}

	const clearScanner = () => {
		if (scannerRef.current) {
			scannerRef.current.stop().catch((err : object) => {
				console.error("Failed to stop scanner", err);
			});
			scannerRef.current = null;
		}
		setCameraOpen(false);
	}

	const triggerScanner = () => {
		if (scannerRef.current) {
			// If a scanner is already running, stop it first
			scannerRef.current.stop();
		}

		const html5QrCode = new Html5Qrcode("reader");
		scannerRef.current = html5QrCode;

		const config = { fps: 10, qrbox: { width: 250, height: 250 } };
		html5QrCode.start({ facingMode: "environment" }, config, (decodedText, decodedResult) => {
			console.log('decodedText', decodedText)
			console.log('decodedResult', decodedResult)

			if (decodedText) {
				setTrackingNumber(decodedText);
				clearScanner();
			}
		},
			(errorMessage) => {
				console.log('errorMessage Error', errorMessage)
			})
			.catch((err) => {
				console.log('Scanner Error', err)
			})

		setCameraOpen(true)
	}

	const handleClickOpenDialog = () => {
		setOpenDialog(true)
	}

	const handleCloseDialog = () => {
		setOpenDialog(false)
		setShowCamera(false)
		setPreviewImageUrl(null)
	}

	const handleImgDelete = (index: any) => {
		const updatedImages = imageUrl.filter((_, i) => i !== index)
		setImageUrl(updatedImages)
	}

	const handleMaxImageClose = (event: any, reason: any) => {
		console.log('CLOSE SNACKBAR', reason)
		// if (reason === 'clickaway') {
		// 	return;
		// }

		setOpenMaxImageSnackbar(false)
	}

	const fileInputRef = useRef<HTMLInputElement>(null)

	const openFileSelector = () => {
		fileInputRef.current?.click()
	}

	if (!(session?.user as UserProfile).isScanUser) {
		return <NoAuthPage />
	}

	return (
		<Page>
			{/* @ts-ignore */}
			<Section sx={{ position: 'relative' }}>
				{!isOnline ? (
					<Box sx={{ position: 'absolute', top: 60, right: 0, p: 2 }}>
						<Chip label='Offline' color='warning' />
					</Box>
				) : null}
				<Formik
					initialValues={{
						trackingNumber: '',
						item: {},
						carrier: {},
						shipmentType: {},
						noCrates: '',
						noSkids: '',
						noFibers: '',
						noCartons: '',
						bolWeight: '',
						noOther: '',
						earlyLateCheckbox: false,
						overtimeCheckbox: false,
						doubleTimeCheckbox: false,
						notes: '',
						imgData: [],
					}}
					validationSchema={Yup.object().shape({
						// item: Yup.string()
						// 	.required("The Item field is required"),
						// materialarrival: Yup.string().max(255),
						// materialpickup: Yup.string().max(255),
						// moveindate: Yup.string(),
						// moveoutdate: Yup.string(),
						// boothsize: Yup.string().max(255),
						// shippingaddress: Yup.string().max(255)
					})}
					onSubmit={async (
						_values,
						{ resetForm, setErrors, setStatus, setSubmitting },
					) => {
						try {
							// @ts-ignore
							await handleSubmitShipment(
								_values,
								setStatus,
								setSubmitting,
								resetForm,
							)
						} catch (err) {
							console.error(err)
							setStatus({ success: false })
							// @ts-ignore
							setErrors({ submit: err.message })
							setSubmitting(false)
						}
					}}
				>
					{({
						errors,
						handleBlur,
						handleChange,
						handleSubmit,
						isSubmitting,
						touched,
						values,
						setFieldValue,
						validateForm,
						validateField,
					}) => (
						<form onSubmit={handleSubmit}>
							<Grid container spacing={1}>
								<Grid
									sx={{
										mb: 3,
									}}
									item
									xs={12}
								>
									{/* @ts-ignore */}
									<div id='reader' width='600px'></div>

									<TextField
										fullWidth
										name='trackingNumber'
										placeholder={'From Tracking Number here...'}
										value={trackingNumber}
										onChange={(e) => {
											handleTrackingInput(e)
											setFieldValue('trackingNumber', e.target.value)
										}}
										InputProps={{
											endAdornment: (
												<InputAdornment position='end'>
													<IconButton
														onClick={(e) =>
															cameraOpen ? clearScanner() : triggerScanner()
														}
														color='primary'
													>
														{cameraOpen ? <ClearIcon /> : <CameraAltIcon />}
													</IconButton>
												</InputAdornment>
											),
										}}
										onKeyDown={(e) => {
											e.key === 'Enter' && e.preventDefault()
										}}
									/>
								</Grid>
								<Grid
									sx={{
										mb: 3,
									}}
									item
									xs={12}
								>
									<Box>
										<Autocomplete
											fullWidth
											id='item-autocomplete'
											options={currentEventData?.shipmentData?.itemsList || []}
											getOptionLabel={(option) => option.itemid || ''}
											value={
												selectedItem
													? // @ts-ignore
														currentEventData?.shipmentData?.itemsList.find(
															(item: any) => item.id === selectedItem,
														)
													: null
											}
											onChange={(e, newValue) => {
												if (newValue) {
													const selectedValue = newValue.id
													const selectedText = newValue.itemid || ''
													handleItemChange({
														target: { name: 'item', value: selectedValue },
													})
													setFieldValue('item', {
														value: selectedValue,
														name: selectedText,
													})
												} else {
													handleItemChange({
														target: { name: 'item', value: '' },
													})
													setFieldValue('item', { value: '', name: '' })
												}
											}}
											renderInput={(params) => (
												<TextField
													{...params}
													label={
														currentEventData?.shipmentData?.shipmentFields
															?.materialHandling
													}
													variant='outlined'
													error={Boolean(touched.item && errors.item)}
												/>
											)}
											isOptionEqualToValue={(option, value) =>
												option.id === value.value
											}
										/>
									</Box>
								</Grid>
								<Grid
									sx={{
										mb: 3,
									}}
									item
									xs={12}
								>
									<Box>
										<Autocomplete
											fullWidth
											id='carrier-autocomplete'
											options={
												currentEventData?.shipmentData?.carrierList || []
											}
											getOptionLabel={(option) => option.name || ''}
											value={
												selectedCarrier
													? // @ts-ignore
														currentEventData?.shipmentData?.carrierList.find(
															(carrier: { id: string }) =>
																carrier.id === selectedCarrier,
														)
													: null
											}
											onChange={(e, newValue) => {
												if (newValue) {
													const selectedValue = newValue.id
													const selectedText = newValue.name || ''
													handleCarrierChange({
														target: {
															name: 'carrier',
															value: selectedValue,
														},
													})
													setFieldValue('carrier', {
														value: selectedValue,
														name: selectedText,
													})
												} else {
													handleCarrierChange({
														target: { name: 'carrier', value: '' },
													})
													setFieldValue('carrier', { value: '', name: '' })
												}
											}}
											renderInput={(params) => (
												<TextField
													{...params}
													label={
														currentEventData?.shipmentData?.shipmentFields
															?.carrier
													}
													variant='outlined'
													sx={{ fontFamily: montserrat.style.fontFamily }}
												/>
											)}
											isOptionEqualToValue={(option, value) =>
												option.id === value.value
											}
										/>
									</Box>
								</Grid>
								<Grid
									sx={{
										mb: 3,
									}}
									item
									xs={12}
								>
									<Box>
										<Autocomplete
											fullWidth
											id='shipment-type-autocomplete'
											options={
												currentEventData?.shipmentData?.shipmentTypeList || []
											}
											getOptionLabel={(option) => option.name || ''}
											value={
												shipmentType
													? currentEventData?.shipmentData?.shipmentTypeList.find(
															(shipment: any) => shipment.id === shipmentType,
														)
													: null
											}
											onChange={(e, newValue) => {
												if (newValue) {
													const selectedValue = newValue.id
													const selectedText = newValue.name || ''
													handleShipmentTypeChange({
														target: {
															name: 'shipmentType',
															value: selectedValue,
														},
													})
													setFieldValue('shipmentType', {
														value: selectedValue,
														name: selectedText,
													})
												} else {
													handleShipmentTypeChange({
														target: {
															name: 'shipmentType',
															value: '',
														},
													})
													setFieldValue('shipmentType', { value: '', name: '' })
												}
											}}
											renderInput={(params) => (
												<TextField
													{...params}
													label={
														currentEventData?.shipmentData?.shipmentFields
															?.shipmentType
													}
													variant='outlined'
													sx={{ fontFamily: montserrat.style.fontFamily }}
												/>
											)}
											isOptionEqualToValue={(option, value) =>
												option.id === value.value
											}
										/>
									</Box>
								</Grid>
								<Grid
									container
									spacing={5}
									sx={{
										mb: 3,
									}}
									item
									xs={12}
								>
									<Grid item xs={6}>
										<TextField
											fullWidth
											name='noCrates'
											onChange={(e) => {
												const value = e.target.value.replace(/\D/g, '')
												handleNoCratesTextChange(e)
												setFieldValue('noCrates', value)
											}}
											value={noCratesText}
											placeholder={
												currentEventData?.shipmentData?.shipmentFields?.noCrates
											}
											onKeyDown={(e) => {
												if (
													!/[0-9]/.test(e.key) &&
													e.key !== 'Backspace' &&
													e.key !== 'Delete' &&
													e.key !== 'ArrowLeft' &&
													e.key !== 'ArrowRight'
												) {
													e.preventDefault()
												}
												e.key === 'Enter' && e.preventDefault()
											}}
											inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
										/>
									</Grid>
									<Grid item xs={6}>
										<TextField
											fullWidth
											name='noSkids'
											onChange={(e) => {
												const value = e.target.value.replace(/\D/g, '')
												handleNoSkidsTextChange(e)
												setFieldValue('noSkids', value)
											}}
											value={noSkidsText}
											placeholder={
												currentEventData?.shipmentData?.shipmentFields?.noSkids
											}
											onKeyDown={(e) => {
												if (
													!/[0-9]/.test(e.key) &&
													e.key !== 'Backspace' &&
													e.key !== 'Delete' &&
													e.key !== 'ArrowLeft' &&
													e.key !== 'ArrowRight'
												) {
													e.preventDefault()
												}
												e.key === 'Enter' && e.preventDefault()
											}}
											inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
										/>
									</Grid>
								</Grid>
								<Grid
									container
									spacing={5}
									sx={{
										mb: 3,
									}}
									item
									xs={12}
								>
									<Grid item xs={6}>
										<TextField
											fullWidth
											name='noFibers'
											onChange={(e) => {
												const value = e.target.value.replace(/\D/g, '')
												handleNoFibersTextChange(e)
												setFieldValue('noFibers', value)
											}}
											value={noFibersText}
											placeholder={
												currentEventData?.shipmentData?.shipmentFields?.noFibers
											}
											onKeyDown={(e) => {
												if (
													!/[0-9]/.test(e.key) &&
													e.key !== 'Backspace' &&
													e.key !== 'Delete' &&
													e.key !== 'ArrowLeft' &&
													e.key !== 'ArrowRight'
												) {
													e.preventDefault()
												}
												e.key === 'Enter' && e.preventDefault()
											}}
											inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
										/>
									</Grid>
									<Grid item xs={6}>
										<TextField
											fullWidth
											name='noCartons'
											onChange={(e) => {
												const value = e.target.value.replace(/\D/g, '')
												handleNoCartonsTextChange(e)
												setFieldValue('noCartons', value)
											}}
											value={noCartonsText}
											placeholder={
												currentEventData?.shipmentData?.shipmentFields
													?.noCartons
											}
											onKeyDown={(e) => {
												if (
													!/[0-9]/.test(e.key) &&
													e.key !== 'Backspace' &&
													e.key !== 'Delete' &&
													e.key !== 'ArrowLeft' &&
													e.key !== 'ArrowRight'
												) {
													e.preventDefault()
												}
												e.key === 'Enter' && e.preventDefault()
											}}
											inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
										/>
									</Grid>
								</Grid>
								<Grid
									container
									spacing={5}
									sx={{
										mb: 3,
									}}
									item
									xs={12}
								>
									<Grid item xs={6}>
										<TextField
											fullWidth
											name='noOther'
											onChange={(e) => {
												handleNoOtherTextChange(e)
												setFieldValue('noOther', e.target.value)
											}}
											value={noOtherText}
											placeholder={
												currentEventData?.shipmentData?.shipmentFields?.noOther
											}
											onKeyDown={(e) => {
												e.key === 'Enter' && e.preventDefault()
											}}
										/>
									</Grid>
									<Grid item xs={6}>
										<TextField
											fullWidth
											name='bolWeight'
											onChange={(e) => {
												const value = e.target.value.replace(/\D/g, '')
												handleBolWeightTextChange(e)
												setFieldValue('bolWeight', value)
											}}
											value={bolWeightText}
											placeholder={
												currentEventData?.shipmentData?.shipmentFields
													?.bolWeight
											}
											onKeyDown={(e) => {
												if (
													!/[0-9]/.test(e.key) &&
													e.key !== 'Backspace' &&
													e.key !== 'Delete' &&
													e.key !== 'ArrowLeft' &&
													e.key !== 'ArrowRight'
												) {
													e.preventDefault()
												}
												e.key === 'Enter' && e.preventDefault()
											}}
											inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
										/>
									</Grid>
								</Grid>
								<Grid
									container
									spacing={4}
									sx={{
										mb: 3,
									}}
									item
									xs={12}
								>
									<Grid item xs={6}>
										<FormGroup>
											<FormControlLabel
												sx={{
													pb: 1,
												}}
												onChange={() =>
													setFieldValue(
														'earlyLateCheckbox',
														!values.earlyLateCheckbox,
													)
												}
												control={
													<Checkbox checked={values.earlyLateCheckbox} />
												}
												label={
													<Typography variant='body2'>
														{
															currentEventData?.shipmentData?.shipmentFields
																?.earlyLate
														}
													</Typography>
												}
											/>
											<FormControlLabel
												onChange={() =>
													setFieldValue(
														'overtimeCheckbox',
														!values.overtimeCheckbox,
													)
												}
												control={<Checkbox checked={values.overtimeCheckbox} />}
												label={
													<Typography variant='body2'>
														{
															currentEventData?.shipmentData?.shipmentFields
																?.overtime
														}
													</Typography>
												}
											/>
										</FormGroup>
									</Grid>
									<Grid item xs={6}>
										<FormGroup>
											<FormControlLabel
												sx={{
													pb: 1,
												}}
												onChange={() =>
													setFieldValue(
														'doubleTimeCheckbox',
														!values.doubleTimeCheckbox,
													)
												}
												control={
													<Checkbox checked={values.doubleTimeCheckbox} />
												}
												label={
													<Typography variant='body2'>
														{
															currentEventData?.shipmentData?.shipmentFields
																?.doubleTime
														}
													</Typography>
												}
											/>
										</FormGroup>
									</Grid>
								</Grid>
								<Grid
									sx={{
										mb: 3,
									}}
									item
									xs={12}
								>
									<TextField
										multiline
										fullWidth
										minRows={3}
										maxRows={13}
										name='notes'
										onChange={(e) => {
											handleNotesTextChange(e)
											setFieldValue('notes', e.target.value)
										}}
										value={notesText}
										placeholder='Notes...'
										onKeyDown={(e) => {
											e.key === 'Enter' && e.preventDefault()
										}}
									/>
								</Grid>

								<Grid
									sx={{
										mb: 3,
									}}
									item
									xs={12}
								>
									<Grid container spacing={1}>
										<Grid item xs={8}>
											<Button
												className={`${montserrat.className} drop-shadow-sm hover:shadow-md transition ease-in-out duration-300`}
												ref={addButtonRef}
												variant='contained'
												color='primary'
												onClick={handleClickOpenDialog}
											>
												Add Pictures
											</Button>
										</Grid>
										<Grid item xs={4}>
											{imageUrl.length !== 0 ? (
												<Chip
													label={`${imageUrl.length} Saved`}
													onClick={handleClickOpenDialog}
													icon={<CameraAltIcon />}
												/>
											) : (
												<Chip label='0 Saved' icon={<CameraAltIcon />} />
											)}
										</Grid>
									</Grid>
								</Grid>

								<Dialog
									fullScreen
									open={openDialog}
									onClose={handleCloseDialog}
								>
									<AppBar sx={{ position: 'relative' }}>
										<Toolbar>
											<IconButton
												edge='start'
												color='inherit'
												onClick={handleCloseDialog}
												aria-label='close'
											>
												<CloseIcon />
											</IconButton>
											<Typography
												sx={{ ml: 2, flex: 1 }}
												variant='h6'
												component='div'
											>
												Pictures
											</Typography>
											<Button
												autoFocus
												color='inherit'
												onClick={handleCloseDialog}
											>
												save
											</Button>
										</Toolbar>
									</AppBar>

									{isloadingImage ? (
										<Box
											sx={{
												display: 'flex',
												flexDirection: 'column',
												justifyContent: 'center',
												alignItems: 'center',
												flexGrow: 1, // Center vertically and horizontally
											}}
										>
											<CircularProgress />
											<Typography variant="h6" sx={{ mt: 2 }}>
												Saving Image
											</Typography>
										</Box>
									) : (
										<List
											sx={
												showCamera || previewImgUrl
													? {
														maxHeight: imageUrl.length >= 1 ? '350px' : 'auto', // Adjust height as per requirement
														overflowY:
															imageUrl.length >= 1 ? 'auto' : 'visible',
													}
													: {
														maxHeight: imageUrl.length > 6 ? '1200px' : 'auto', // Adjust height as per requirement
														overflowY: imageUrl.length > 6 ? 'auto' : 'visible',
													}
											}
										>
											{imageUrl.length !== 0
												? imageUrl.map((img, i) => (
													<>
														<ListItem
															key={i}
															secondaryAction={
																<IconButton
																	aria-label='comment'
																	onClick={() => handleImgDelete(i)}
																>
																	<CloseIcon />
																</IconButton>
															}
														>
															<img
																src={img}
																alt='caputured Image'
																style={{
																	borderRadius: '100%',
																	height: '100px',
																	width: '100px',
																	objectFit: 'cover',
																}}
															/>
														</ListItem>
														<Divider />
													</>
												))
												: null}
										</List>
									)}


									<Grid
										sx={{
											mb: 3,
											position: 'relative',
										}}
										item
										xs={12}
									>
										<Box sx={{ pt: 2, pb: 8 }}>
											{showCamera && (
												<WebcamComponent
													setShowCamera={setShowCamera}
													setPreviewImageUrl={setPreviewImageUrl}
												/>
											)}
											{previewImgUrl && (
												<div
													style={{
														display: 'flex',
														flexDirection: 'column',
														gap: '10px',
														alignItems: 'center',
													}}
												>
													<img
														src={previewImgUrl}
														alt='caputured Image'
														style={{
															borderRadius: '100%',
															height: '300px',
															width: '300px',
															objectFit: 'cover',
														}}
													/>
													<div
														style={{
															display: 'flex',
															flexDirection: 'row',
															gap: '5px',
														}}
													>
														<Button
															className={`${montserrat.className} drop-shadow-sm hover:shadow-md transition ease-in-out duration-300`}
															variant='contained'
															color='primary'
															onClick={() => {
																setShowCamera(true)
																setPreviewImageUrl(null)
															}}
														>
															Retake
														</Button>
														<Button
															className={`${montserrat.className} drop-shadow-sm hover:shadow-md transition ease-in-out duration-300`}
															variant='contained'
															color='primary'
															onClick={() => {
																setShowCamera(false)
																let imageAdded = [...imageUrl, previewImgUrl]
																// @ts-ignore
																setImageUrl(imageAdded)
																setPreviewImageUrl(null)
																setFieldValue('imgData', imageAdded)
															}}
														>
															Use Image
														</Button>
													</div>
												</div>
											)}
										</Box>


										<Grid container spacing={1}>
											<Grid item xs={12}>
												<Box
													sx={{
														display: 'flex',
														justifyContent: 'flex-start',
														alignItems: 'center',
														gap: 2, // Adds spacing between the buttons
														position: 'absolute',
														bottom: 16,
														left: 16,
													}}
												>
													<Button
														className={`${montserrat.className} drop-shadow-sm hover:shadow-md transition ease-in-out duration-300`}
														variant='contained'
														color='primary'
														onClick={() => {
															if (imageUrl.length === 6) {
																setOpenMaxImageSnackbar(true)
																return
															}
															openFileSelector()
														}}
													>
														Upload Image
													</Button>
													<input
														type='file'
														accept='image/*,text/plain'
														ref={fileInputRef}
														onChange={async (event) => {
															const file = event.target.files?.[0]

															if (file) {
																setIsloadingImage(true)
																const options = {
																	maxSizeMB: 0.5,
																}

																const compressedFile = await imageCompression(file, options)

																const reader = new FileReader()
																reader.onload = () => {
																	if (
																		reader.result &&
																		typeof reader.result === 'string'
																	) {
																		const compressedImageUrl = reader.result
																		const updatedImageUrls = [...imageUrl, compressedImageUrl]

																		setImageUrl(updatedImageUrls)
																		setFieldValue('imgData', updatedImageUrls)
																	}
																	setIsloadingImage(false)
																}
																reader.onerror = (error) => {
																	console.error('Error reading file:', error)
																	setIsloadingImage(false)
																}
																reader.readAsDataURL(compressedFile)
															}
														}}
														style={{ display: 'none' }}
													/>
												</Box>

												<Snackbar
													open={openMaxImageSnackbar}
													anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
													autoHideDuration={3000}
													onClose={handleMaxImageClose}
												>
													<Alert
														// @ts-ignore
														onClose={handleMaxImageClose}
														severity='warning'
														variant='filled'
														sx={{ width: '50%' }}
													>
														Maximum 6 Images
													</Alert>
												</Snackbar>
											</Grid>
											<Grid item xs={4}>
												{imageUrl.length !== 0 ? (
													<Chip
														sx={{
															position: 'absolute',
															bottom: 16,
															right: 16,
														}}
														label={`${imageUrl.length} Saved`}
														icon={<CameraAltIcon />}
													/>
												) : (
													<Chip
														sx={{
															position: 'absolute',
															bottom: 16,
															right: 16,
														}}
														label='0 Saved'
														icon={<CameraAltIcon />}
													/>
												)}
											</Grid>
										</Grid>
									</Grid>


								</Dialog>

								<LoadingButton
									className={`${montserrat.className} drop-shadow-sm hover:shadow-md transition ease-in-out duration-300`}
									fullWidth
									type='submit'
									size='large'
									variant='contained'
									color='primary'
									loadingPosition='end'
								>
									Save Shipment
								</LoadingButton>
							</Grid>
						</form>
					)}
				</Formik>
				<Snackbar
					open={openShipmentSavedSnackbar}
					anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
					autoHideDuration={3000}
					onClose={handleShipmentSubmitClose}
				>
					<Alert
						onClose={handleShipmentSubmitClose}
						severity='success'
						variant='filled'
						sx={{ width: '50%' }}
					>
						Shipment Successfully Saved!
					</Alert>
				</Snackbar>
			</Section>
		</Page>
	)
}

BuildShipments.getLayout = (page: any) => <AuthGuard>{page}</AuthGuard>

export default BuildShipments
