import request from "request-promise-native";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@event-services/auth";

const REALM = process.env.NEXT_PUBLIC_ACCOUNT_ID;
const CONSUMER_KEY = process.env.OAUTH1_CONSUMER_KEY;
const CONSUMER_SECRET = process.env.OAUTH1_CONSUMER_SECRET;
const TOKEN = process.env.OAUTH1_ACCESS_TOKEN;
const TOKEN_SECRET = process.env.OAUTH1_TOKEN_SECRET;

// eslint-disable-next-line consistent-return
export default async (req, res) => {
  const session = await getServerSession(req, res, authOptions);
  const { ceid, uid } = req.query;
  let response = null;
  if (session) {
    try {
      switch (req.method) {
        case "POST":
          response = await request({
            method: "POST",
            uri: `https://${process.env.NEXT_PUBLIC_ACCOUNT_URL_ID}.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=customscript_ng_rl_rcs_handle_tracking&deploy=customdeploy_ng_rl_rcs_handle_tracking`,
            oauth: {
              consumer_key: CONSUMER_KEY,
              consumer_secret: CONSUMER_SECRET,
              token: TOKEN,
              token_secret: TOKEN_SECRET,
              signature_method: "HMAC-SHA256",
              realm: REALM,
              version: "1.0",
            },
            body: req.body,
            resolveWithFullResponse: true,
            headers: { "Content-Type": "application/json" },
          })
            .then((response) => {
              if (response.status !== 200) {
                if (response.body?.error) {
                  res.status(500).json({
                    error: `Error processing tracking`,
                    details: response.body,
                  });
                }
              }

              res.status(200).json(JSON.parse(response.body));
            })
            .catch((error) => {
              let body = error?.error;

              res.status(500).json({
                error: `Error processing tracking: ${error}`,
                data: body,
              });
            });
          break;
        default:
          return "No request method type was met. Please ensure the type of method has a type. Ex. GET, POST, PUT, PATCH, etc.";
      }
    } catch (e) {
      // eslint-disable-next-line no-undef
      res.json(e);
      res.status(405).end();
    }
  } else {
    res
      .status(401)
      .send("You must be sign in to view the protected content on this page.");
    // res.redirect('/auth/login')
  }
};
