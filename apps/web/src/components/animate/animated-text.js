import { motion } from "framer-motion";
import { Box, Typography } from "@mui/material";
import { useEffect, useState } from "react";

export function AnimatedTextLoader({
  children = "Loading...",
  className = "",
  ...other
}) {
  const fadeInOut = {
    opacity: [0, 1, 1, 0],
    transition: {
      duration: 3,
      times: [0, 0.3, 0.7, 1],
      ease: "easeInOut",
      repeat: Infinity,
      repeatDelay: 0,
    },
  };

  return (
    <Box
      component={motion.div}
      className={`overflow-hidden ${className}`}
      animate={fadeInOut}
    >
      <Typography
        fontSize="16px"
        fontWeight="600"
        lineHeight="1.5"
        component={motion.p}
        variant="h5"
        className="text-center"
        {...other}
      >
        {children}
      </Typography>
    </Box>
  );
}

const ONE_SECOND = 1000;
const WAIT_TIME = ONE_SECOND * 8;

export const AnimatedText = ({ phrases, ...other }) => {
  const [active, setActive] = useState(0);

  useEffect(() => {
    const intervalRef = setInterval(() => {
      setActive((pv) => (pv + 1) % phrases.length);
    }, WAIT_TIME);

    return () => clearInterval(intervalRef);
  }, [phrases]);

  return (
    <Box className="relative mb-14 mt-2 w-full">
      {phrases.map((phrase) => {
        const isActive = phrases[active] === phrase;
        return (
          <Typography
            component={motion.div}
            key={phrase}
            initial={false}
            animate={isActive ? "active" : "inactive"}
            {...other}
            variants={{
              active: {
                opacity: 1,
                scale: 1,
              },
              inactive: {
                opacity: 0,
                scale: 0,
              },
            }}
            className="absolute top-0 w-full"
          >
            {phrase}
          </Typography>
        );
      })}
    </Box>
  );
};
