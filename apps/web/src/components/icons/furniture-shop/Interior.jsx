import { SvgIcon } from "@mui/material";
import React from "react";

const Interior = (props) => {
  return (
    <SvgIcon {...props} viewBox="0 0 19 19" fill="none">
      <g clipPath="url(#clip0_6800_50481)">
        <path
          d="M8.09044 5.60442C8.12742 5.60451 8.16405 5.59729 8.19824 5.5832C8.23243 5.5691 8.26351 5.5484 8.28969 5.52228C8.31587 5.49616 8.33664 5.46513 8.35081 5.43097C8.36498 5.39681 8.37228 5.3602 8.37228 5.32321C8.37228 5.28623 8.36498 5.24961 8.35081 5.21545C8.33664 5.18129 8.31587 5.15027 8.28969 5.12414C8.26351 5.09802 8.23243 5.07732 8.19824 5.06323C8.16405 5.04913 8.12742 5.04192 8.09044 5.042H6.73643L5.41698 2.65632C5.39846 2.62281 5.37328 2.59344 5.34301 2.57001C5.31273 2.54657 5.27798 2.52957 5.2409 2.52003C5.20382 2.5105 5.16518 2.50864 5.12736 2.51457C5.08953 2.52049 5.05331 2.53408 5.02092 2.5545L4.14353 3.10738C4.08336 3.14528 4.0398 3.20457 4.0216 3.27331C4.00341 3.34206 4.01194 3.41514 4.04547 3.47784L4.88166 5.042H4.01231V2.5107C4.0123 2.43612 3.98266 2.36459 3.92992 2.31185C3.87718 2.25912 3.80564 2.22949 3.73106 2.22949H2.60606C2.53148 2.22949 2.45995 2.25912 2.4072 2.31185C2.35446 2.36459 2.32482 2.43612 2.32481 2.5107V5.042H0.777935C0.740953 5.04192 0.704317 5.04913 0.670127 5.06323C0.635936 5.07732 0.604861 5.09802 0.578681 5.12414C0.552502 5.15027 0.531732 5.18129 0.51756 5.21545C0.503388 5.24961 0.496094 5.28623 0.496094 5.32321C0.496094 5.3602 0.503388 5.39681 0.51756 5.43097C0.531732 5.46513 0.552502 5.49616 0.578681 5.52228C0.604861 5.5484 0.635936 5.5691 0.670127 5.5832C0.704317 5.59729 0.740953 5.60451 0.777935 5.60442H8.09044ZM3.44981 5.042H2.88731V2.79191H3.44981V5.042ZM5.51946 5.042L4.66497 3.44358L5.06885 3.18908L6.0937 5.042H5.51946ZM14.8404 7.01074C15.3411 7.01074 15.8305 6.86229 16.2467 6.58415C16.663 6.30601 16.9874 5.91069 17.179 5.44816C17.3706 4.98564 17.4207 4.47669 17.3231 3.98567C17.2254 3.49466 16.9843 3.04363 16.6303 2.68963C16.2763 2.33563 15.8253 2.09455 15.3343 1.99688C14.8432 1.89921 14.3343 1.94934 13.8718 2.14092C13.4092 2.33251 13.0139 2.65694 12.7358 3.07321C12.4576 3.48947 12.3092 3.97886 12.3092 4.47949C12.31 5.15059 12.5769 5.79397 13.0514 6.26851C13.526 6.74304 14.1693 7.00998 14.8404 7.01074ZM14.8404 2.51074C15.2298 2.51074 15.6105 2.62621 15.9342 2.84254C16.258 3.05887 16.5103 3.36634 16.6593 3.72609C16.8083 4.08583 16.8473 4.48168 16.7714 4.86358C16.6954 5.24548 16.5079 5.59628 16.2326 5.87161C15.9572 6.14694 15.6064 6.33445 15.2245 6.41041C14.8426 6.48638 14.4468 6.44739 14.087 6.29838C13.7273 6.14937 13.4198 5.89703 13.2035 5.57327C12.9872 5.24951 12.8717 4.86888 12.8717 4.47949C12.8723 3.95753 13.0799 3.45711 13.449 3.08802C13.8181 2.71894 14.3185 2.51133 14.8404 2.51074V2.51074ZM4.43419 13.7607H0.777935C0.73504 13.7607 0.692713 13.7705 0.654192 13.7894C0.615672 13.8083 0.581978 13.8357 0.55569 13.8696C0.529403 13.9035 0.511217 13.943 0.502525 13.985C0.493834 14.027 0.494866 14.0704 0.505544 14.112L1.13225 16.5508C1.17151 16.7147 1.26395 16.861 1.39513 16.9668C1.52631 17.0726 1.68884 17.132 1.85734 17.1357H3.35478C3.52328 17.132 3.68581 17.0726 3.81699 16.9668C3.94817 16.861 4.04061 16.7147 4.07988 16.5508L4.70658 14.112C4.71725 14.0704 4.71829 14.027 4.70959 13.985C4.7009 13.943 4.68272 13.9035 4.65643 13.8696C4.63014 13.8357 4.59645 13.8083 4.55793 13.7894C4.51941 13.7705 4.47708 13.7607 4.43419 13.7607V13.7607ZM3.53022 16.4312C3.51871 16.4698 3.4958 16.504 3.46449 16.5294C3.43318 16.5547 3.39494 16.57 3.35478 16.5732H1.85734C1.81718 16.57 1.77894 16.5547 1.74763 16.5294C1.71632 16.504 1.69341 16.4698 1.6819 16.4312C1.68094 16.4264 1.14062 14.3232 1.14062 14.3232H4.0715C4.0715 14.3232 3.53118 16.4264 3.53022 16.4312ZM0.522366 11.4568C0.516319 11.4877 0.378994 12.222 0.81316 12.8099C1.09427 13.1905 1.53743 13.4152 2.13028 13.4779C2.19958 13.4847 2.26894 13.4657 2.32518 13.4247C2.38142 13.3836 2.42061 13.3234 2.43529 13.2553C2.44188 13.2235 2.59157 12.4696 2.15102 11.8786C1.8686 11.4998 1.42153 11.2817 0.822362 11.2305C0.753249 11.2246 0.68437 11.2444 0.628985 11.2861C0.5736 11.3279 0.535623 11.3887 0.522366 11.4568V11.4568ZM1.6977 12.2117C1.83164 12.4044 1.90306 12.6337 1.90225 12.8684C1.65198 12.8165 1.4277 12.6789 1.2682 12.4792C1.13643 12.2899 1.0642 12.0655 1.06076 11.8349C1.31133 11.8802 1.53733 12.0139 1.6977 12.2117V12.2117ZM2.77683 13.2553C2.79223 13.323 2.83159 13.3828 2.88765 13.4237C2.94371 13.4646 3.01269 13.4839 3.08184 13.4779C3.67469 13.4152 4.11785 13.1905 4.39896 12.8099C4.83313 12.222 4.6958 11.4877 4.68976 11.4568C4.67637 11.3887 4.63836 11.328 4.58301 11.2863C4.52766 11.2446 4.45885 11.2247 4.38976 11.2305C3.79059 11.2817 3.34352 11.4998 3.0611 11.8786C2.62056 12.4696 2.77023 13.2235 2.77683 13.2553ZM3.51209 12.2149C3.67243 12.0153 3.89944 11.8804 4.15136 11.8349C4.14784 12.0641 4.07655 12.2871 3.94647 12.4758C3.78701 12.6773 3.56169 12.8163 3.31001 12.8683C3.3093 12.635 3.3798 12.4071 3.51209 12.2149H3.51209ZM2.40645 8.6407C2.38015 8.66721 1.76231 9.29885 1.76231 10.1045C1.76231 10.9101 2.38015 11.5418 2.40645 11.5683C2.4326 11.5946 2.4637 11.6155 2.49796 11.6298C2.53221 11.644 2.56895 11.6514 2.60606 11.6514C2.64317 11.6514 2.67991 11.644 2.71417 11.6298C2.74842 11.6155 2.77952 11.5946 2.80567 11.5683C2.83197 11.5418 3.44981 10.9101 3.44981 10.1045C3.44981 9.29885 2.83197 8.66721 2.80567 8.6407C2.75181 8.58952 2.68036 8.56098 2.60606 8.56098C2.53176 8.56098 2.46031 8.58952 2.40645 8.6407V8.6407ZM2.60606 10.9169C2.43628 10.6787 2.33864 10.3967 2.32481 10.1045C2.33865 9.8123 2.43629 9.53027 2.60606 9.29205C2.77584 9.53026 2.87348 9.8123 2.88731 10.1045C2.87347 10.3967 2.77583 10.6787 2.60606 10.9169V10.9169Z"
          fill="currentColor"
        />
        <path
          d="M14.6848 4.72957L15.5285 5.29207C15.591 5.33204 15.6666 5.34624 15.7393 5.3317C15.812 5.31715 15.8763 5.27499 15.9186 5.21407C15.9599 5.152 15.9749 5.07605 15.9603 5.00291C15.9457 4.92977 15.9026 4.86544 15.8406 4.82405L15.1211 4.3444V3.35449C15.1211 3.2799 15.0915 3.20836 15.0387 3.15562C14.986 3.10287 14.9144 3.07324 14.8398 3.07324C14.7653 3.07324 14.6937 3.10287 14.641 3.15562C14.5882 3.20836 14.5586 3.2799 14.5586 3.35449V4.47949C14.5586 4.48408 14.5597 4.48834 14.5599 4.49286C14.5594 4.5396 14.5705 4.58572 14.5923 4.62706C14.6141 4.66839 14.6459 4.70363 14.6848 4.72957ZM13.7148 11.2295H12.5898C12.5153 11.2295 12.4437 11.2591 12.391 11.3119C12.3382 11.3646 12.3086 11.4362 12.3086 11.5107C12.3086 11.5853 12.3382 11.6569 12.391 11.7096C12.4437 11.7624 12.5153 11.792 12.5898 11.792H13.7148C13.7894 11.792 13.861 11.7624 13.9137 11.7096C13.9665 11.6569 13.9961 11.5853 13.9961 11.5107C13.9961 11.4362 13.9665 11.3646 13.9137 11.3119C13.861 11.2591 13.7894 11.2295 13.7148 11.2295Z"
          fill="currentColor"
        />
        <path
          d="M18.2148 14.042C18.2894 14.042 18.361 14.0124 18.4137 13.9596C18.4665 13.9069 18.4961 13.8353 18.4961 13.7607C18.4961 13.6862 18.4665 13.6146 18.4137 13.5619C18.361 13.5091 18.2894 13.4795 18.2148 13.4795H16.8086V9.40137H18.2148C18.2894 9.40137 18.361 9.37174 18.4137 9.31899C18.4665 9.26625 18.4961 9.19471 18.4961 9.12012C18.4961 9.04553 18.4665 8.97399 18.4137 8.92124C18.361 8.8685 18.2894 8.83887 18.2148 8.83887H7.3804C7.1211 8.83916 6.8725 8.9423 6.68914 9.12566C6.50578 9.30902 6.40264 9.55762 6.40234 9.81693V10.8318C6.17023 10.8734 5.95959 10.9939 5.8059 11.1727C5.65221 11.3516 5.56487 11.578 5.55859 11.8137V14.864C5.5684 15.1399 5.68706 15.4006 5.8886 15.5891C6.09015 15.7776 6.35816 15.8787 6.63402 15.8701H7.99443C7.98812 15.9167 7.98479 15.9637 7.98445 16.0107C7.98478 16.3089 8.10346 16.5947 8.31441 16.8054C8.52536 17.0162 8.81132 17.1345 9.10948 17.1345C9.40764 17.1345 9.69361 17.0162 9.90456 16.8054C10.1155 16.5947 10.2342 16.3089 10.2345 16.0107C10.2342 15.9637 10.2308 15.9167 10.2245 15.8701H18.2148C18.2894 15.8701 18.361 15.8405 18.4137 15.7877C18.4665 15.735 18.4961 15.6635 18.4961 15.5889C18.4961 15.5143 18.4665 15.4427 18.4137 15.39C18.361 15.3372 18.2894 15.3076 18.2148 15.3076H7.80859V14.042H18.2148ZM8.56695 15.8701H9.65201C9.66479 15.9159 9.67151 15.9632 9.67201 16.0107C9.67201 16.1599 9.61274 16.303 9.50725 16.4085C9.40175 16.514 9.25867 16.5733 9.10948 16.5733C8.96028 16.5733 8.8172 16.514 8.7117 16.4085C8.60621 16.303 8.54694 16.1599 8.54694 16.0107C8.54744 15.9632 8.55417 15.9159 8.56695 15.8701V15.8701ZM10.0586 9.40137H16.2461V13.4795H10.0586V9.40137ZM7.24609 15.3076H6.63402C6.50737 15.316 6.38252 15.2741 6.28652 15.1911C6.19051 15.1081 6.13108 14.9906 6.12109 14.864V13.0513H7.24609V15.3076ZM7.24609 12.4888H6.12109V11.8137C6.13108 11.6872 6.19051 11.5697 6.28652 11.4866C6.38252 11.4036 6.50737 11.3617 6.63402 11.3701H6.73338C6.86 11.3618 6.98481 11.4036 7.08077 11.4867C7.17674 11.5697 7.23613 11.6872 7.24609 11.8137V12.4888ZM6.96484 10.8317V9.81693C6.96497 9.70675 7.00879 9.60113 7.0867 9.52322C7.1646 9.44532 7.27023 9.40149 7.3804 9.40137H9.49609V13.4795H7.80859V11.8137C7.80234 11.5779 7.715 11.3516 7.56131 11.1727C7.40762 10.9938 7.19696 10.8734 6.96484 10.8317Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_6800_50481">
          <rect
            width="18"
            height="18"
            fill="white"
            transform="translate(0.496094 0.541992)"
          />
        </clipPath>
      </defs>
    </SvgIcon>
  );
};
export default Interior;
