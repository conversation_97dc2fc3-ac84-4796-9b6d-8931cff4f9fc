import React from "react";
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  CircularProgress, Collapse,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Grid,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Typography,
  useMediaQuery,
  Zoom
} from '@mui/material'
import * as Yup from "yup";
import { t } from "i18next";
import axios from "axios";
import { Formik } from "formik";
import { useSnackbar } from "notistack";
import { useTheme } from "@mui/material/styles";
import { TextMaskCustom } from "../containers/PhoneMaskedField";
import { useEventData, useEventSelection } from '../../store/eventStore'
import { useSettings } from "../../store/zSettingsStore";
import { useUser, useUserSWR } from "../../store/zUserStore";
import { useRouter } from 'next/router'

const BillOfLadingModal = ({
  open,
  onClose,
  selectedBolRequest,
  setSelectedBol,
}) => {
  const { user } = useUser();
  const { settings } = useSettings();
  const { event, booth } = useEventSelection();
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down("sm"));
  const router = useRouter();
  const { slug } = router.query;
  const { data, error, isLoading } = useEventData(slug);

  const deadlineOptions = settings?.bolConfig?.deadlineOptions;
  const payorOptions = settings?.bolConfig?.payorOptions;
  const carrierOptions = settings?.bolConfig?.carrier.names;
  const carrierServices = settings?.bolConfig?.carrier.serviceTypes;
  const reRouteOptions = settings?.bolConfig?.reRouteOptions;
  const showReRouteOption = data?.details?.reRouteCarrierEnabled;

  const { mutate } = useUserSWR();

  return (
    <Dialog sx={{ mt: mobile ? 'unset' : 10}} open={Boolean(open)} onClose={onClose} fullScreen={mobile}>
      <DialogTitle
        sx={{
          p: 3,
        }}
      >
        <Typography variant="h4" gutterBottom>
          Shipping Request
        </Typography>
        <Typography variant="subtitle2">
          Edit and view details of your shipping request.
        </Typography>
        {selectedBolRequest && (
          <Formik
            enableReinitialize={!!selectedBolRequest}
            initialValues={{
              // Required Info
              event: selectedBolRequest.event.id,
              booth: selectedBolRequest.booth.id,
              bolId: selectedBolRequest.id,
              // Material Pickup
              numberOfLabels: selectedBolRequest.numberOfLabels,
              exhibitCompany: selectedBolRequest.company.id,
              exhibitContactPhone: selectedBolRequest.phone,
              exhibitContact: selectedBolRequest.contact.id,
              boothNumber: selectedBolRequest.booth.id,
              // Freight Delivery
              numberOfSeparateBoothShipments: 1,
              company: selectedBolRequest.destination.company.id,
              contact: selectedBolRequest.destination.contact.id,
              contactPhone: selectedBolRequest.destination.contact.phone,
              address: selectedBolRequest.destination.address,
              city: selectedBolRequest.destination.city,
              state: selectedBolRequest.destination.state.label,
              zipCode: selectedBolRequest.destination.zip,
              country: selectedBolRequest.destination.country.label,
              destinationEvent: selectedBolRequest.destination.event,
              destinationExhibitorBooth: selectedBolRequest.destination.exhibitorAndBooth,
              destinationCo: selectedBolRequest.destination.destinationCo,
              // Carrier Details
              carrierName: selectedBolRequest.carrier.id,
              typeOfService: selectedBolRequest.carrier.service.id,
              typeOfServiceNotes: selectedBolRequest.carrier.notes,
              carrierTrackingNumber: selectedBolRequest.destination.carrierTrackingNumber,
              // Deadline Missed
              deadlineMissed: selectedBolRequest.deadlineMissedOption, // 1 = Re-route to exhibitor booth, 2 = Return to warehouse,
              reRouteCarrierName: selectedBolRequest.carrier.reRoute.name,
              reRouteCarrierPhone: selectedBolRequest.carrier.reRoute.phone,
              reRouteShippingOption: selectedBolRequest.carrier.reRoute.shippingOption,
              // Payment Form
              whoPaid: selectedBolRequest.payor.id, // 1 = Exhibitor, 2 = 3rd party
              payorCompany: selectedBolRequest.payor.company,
              payorName: selectedBolRequest.payor.name,
              payorEmail: selectedBolRequest.payor.email,
              payorPhone: selectedBolRequest.payor.phone,
              payorBillingAddress: selectedBolRequest.payor.billingAddress,
              // Special Instructions
              specialInstructions: selectedBolRequest.specialInstructions,
              // Accept Terms and Conditions
              acceptTermsAndConditions: false,
              // Submit
              submit: null,
            }}
            validationSchema={Yup.object().shape({
              numberOfLabels: Yup.number()
                .min(1, "Number must be greater than or equal to 1")
                .required("The number of labels is required"),
              exhibitCompany: Yup.number().required(
                "The exhibit company is required",
              ),
              exhibitContact: Yup.number().required(
                "The exhibit contact name is required",
              ),
              boothNumber: Yup.number().required(
                t("Insert a booth number for the shipment to be sent."),
              ),
              exhibitContactPhone: Yup.string().matches(
                /^\(\d{3}\) \d{3}-\d{4}$/,
                "Invalid phone number",
              ),
              // Freight Delivery
              numberOfSeparateBoothShipments: Yup.number()
                .min(1, "Number must be greater than or equal to 1")
                .required("The number of shipment separations is required"),
              address: Yup.string().required(
                t("Please input address for request"),
              ),
              company: Yup.number().required(
                t("Please input company for request"),
              ),
              contact: Yup.number().required("Please input a contact name"),
              contactPhone: Yup.string().matches(
                /^\(\d{3}\) \d{3}-\d{4}$/,
                "Invalid phone number",
              ),
              state: Yup.string().required(t("Please select a state")),
              city: Yup.string().required(t("Please enter a city")),
              country: Yup.string().required(t("Please select a country")),
              zipCode: Yup.string().matches(
                /^[0-9]{5}$/,
                "Must be exactly 5 digits",
              ),
              // Carrier Details
              carrierName: Yup.number().required("Carrier name is required"),
              typeOfService: Yup.number().required(
                "Type of service is required",
              ),
              typeOfServiceNotes: Yup.string(),
              // Deadline Missed
              deadlineMissed: Yup.number().required(
                "Required for shipment to be sent",
              ),
              reRouteCarrierPhone: Yup.string().matches(
                /^\(\d{3}\) \d{3}-\d{4}$/,
                "Invalid phone number",
              ),
              // Payment Form
              whoPaid: Yup.string().required("Who paid is required"),
              payorPhone: Yup.string().matches(
                /^\(\d{3}\) \d{3}-\d{4}$/,
                "Invalid phone number",
              ),
              payorEmail: Yup.string().email('Invalid email format'),
              // Special Instructions
              specialInstructions: Yup.string(),
              // Accept Terms and Conditions
              acceptTermsAndConditions: Yup.boolean().required(
                "You must accept the terms and conditions to submit this form",
              ),
            })}
            onSubmit={async (
              _values,
              { resetForm, setErrors, setStatus, setSubmitting },
            ) => {
              try {
                console.log("Submitting form...", _values);

                await axios
                  .put("/api/customer/put/update-bill-of-lading", _values, {
                    headers: {
                      "Content-Type": "application/json",
                    },
                    json: true,
                  })
                  .then((response) => {
                    console.log("Response from BOL submit: ", response);
                    setSubmitting(false);
                    if (response.status === 200) {
                      console.log("Outbound Shipment Request submitted successfully");
                      setSubmitting(false);
                      if (response.data) {
                        console.log("Response body: ", response.data);
                        if (response.data?.error) {
                          enqueueSnackbar("Outbound Shipment Request Submission Failed!", {
                            variant: "error",
                            anchorOrigin: {
                              vertical: "top",
                              horizontal: "center",
                            },
                            TransitionComponent: Zoom,
                          });
                        } else {
                          console.log("Bol update successful");
                          enqueueSnackbar(
                            "Outbound Shipment Request Update Successful - Results will take some time to reflect.",
                            {
                              variant: "success",
                              anchorOrigin: {
                                vertical: "top",
                                horizontal: "center",
                              },
                              TransitionComponent: Zoom,
                            },
                          );
                          if (response.data?.printUrl) {
                            mutate();
                            setSelectedBol(null);
                            resetForm();
                          }
                        }
                      }
                    } else {
                      console.log("BOL update failed");
                      enqueueSnackbar(
                        "Outbound Shipment Request Update Failed! - Internal error occurred",
                        {
                          variant: "error",
                          anchorOrigin: {
                            vertical: "top",
                            horizontal: "center",
                          },
                          TransitionComponent: Zoom,
                        },
                      );
                      setSubmitting(false);
                    }
                  });
              } catch (err) {
                console.error(err);
                setStatus({ success: false });
                setErrors({ submit: err.message });
                setSubmitting(false);
              }
            }}
          >
            {({
              errors,
              handleBlur,
              handleChange,
              handleSubmit,
              isSubmitting,
              touched,
              values,
              setFieldValue,
              validateForm,
              validateField,
            }) => (
              <form onSubmit={handleSubmit}>
                <DialogContent sx={{ p: 3 }} dividers>
                  <Grid container spacing={3}>
                    {/*  Pickup fields */}
                    <Grid item xs={12}>
                      <Card variant="outlined">
                        <CardHeader
                          title="Pickup"
                          subheader="Tell us the location of the materials for pickup"
                        />
                        <CardContent>
                          <Grid container item spacing={3}>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                fullWidth
                                error={Boolean(
                                  touched.exhibitCompany &&
                                    errors.exhibitCompany,
                                )}
                              >
                                <InputLabel id="exhibit-company-select-label">
                                  Exhibit Company
                                </InputLabel>
                                <Select
                                  disabled
                                  labelId="exhibit-company-select-label"
                                  label="Exhibit Company"
                                  name="exhibitCompany"
                                  onBlur={handleBlur}
                                  onChange={handleChange}
                                  value={values.exhibitCompany}
                                >
                                  <MenuItem value={user.id} selected>
                                    {user.name}
                                  </MenuItem>
                                </Select>
                                <FormHelperText>
                                  {touched.exhibitCompany &&
                                    errors.exhibitCompany}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                fullWidth
                                error={Boolean(
                                  touched.exhibitContact &&
                                    errors.exhibitContact,
                                )}
                              >
                                <InputLabel id="ex-contact-select-label">
                                  Exhibitor Contact
                                </InputLabel>
                                <Select
                                  disabled
                                  labelId="ex-contact-select-label"
                                  label="Exhibitor Contact"
                                  name="exhibitContact"
                                  onBlur={handleBlur}
                                  onChange={handleChange}
                                  value={values.exhibitContact}
                                >
                                  <MenuItem value={user.contact} selected>
                                    {user.profile?.contactProfile.name}
                                  </MenuItem>
                                </Select>
                                <FormHelperText>
                                  {touched.exhibitContact &&
                                    errors.exhibitContact}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                fullWidth
                                error={Boolean(
                                  touched.boothNumber && errors.boothNumber,
                                )}
                              >
                                <InputLabel id="booth-select-label">
                                  Booth Number
                                </InputLabel>
                                <Select
                                  disabled
                                  labelId="booth-select-label"
                                  label="Booth Number"
                                  name="boothNumber"
                                  onBlur={handleBlur}
                                  onChange={handleChange}
                                  value={values.boothNumber}
                                >
                                  <MenuItem value={booth.id} selected>
                                    {booth.name}
                                  </MenuItem>
                                </Select>
                                <FormHelperText>
                                  {touched.boothNumber && errors.boothNumber}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                error={Boolean(
                                  touched.exhibitContactPhone &&
                                    errors.exhibitContactPhone,
                                )}
                                fullWidth
                                variant="outlined"
                              >
                                <InputLabel
                                  variant="outlined"
                                  htmlFor="formatted-text-contact-number"
                                >
                                  Contact Number
                                </InputLabel>
                                <OutlinedInput
                                  label="Contact Number"
                                  variant="outlined"
                                  value={values.exhibitContactPhone}
                                  onChange={handleChange}
                                  onBlur={handleBlur}
                                  name="exhibitContactPhone"
                                  id="formatted-text-contact-number"
                                  inputComponent={TextMaskCustom}
                                />
                                <FormHelperText>
                                  {touched.exhibitContactPhone &&
                                    errors.exhibitContactPhone}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                fullWidth
                                label="Number of Lables Needed"
                                name="numberOfLabels"
                                onBlur={handleBlur}
                                onChange={handleChange}
                                error={Boolean(
                                  touched.numberOfLabels &&
                                    errors.numberOfLabels,
                                )}
                                helperText={
                                  touched.numberOfLabels &&
                                  errors.numberOfLabels
                                }
                                value={values.numberOfLabels}
                                InputProps={{
                                  type: "number",
                                  inputMode: "numeric",
                                  pattern: "[0-9]*",
                                  min: 0,
                                  sx: { height: 56 }
                                }}
                              />
                            </Grid>
                          </Grid>
                        </CardContent>
                      </Card>
                    </Grid>
                    <Divider sx={{ mt: 1 }} />
                    {/*  Freight Delivery fields */}
                    <Grid item xs={12}>
                      <Card variant="outlined">
                        <CardHeader
                          title="Freight Delivery"
                          subheader="Tell us where you want your materials delivered"
                        />
                        <CardContent>
                          <Grid container item spacing={3}>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                fullWidth
                                error={Boolean(
                                  touched.company && errors.company,
                                )}
                              >
                                <InputLabel id="delivery-company-select-label">
                                  Company
                                </InputLabel>
                                <Select
                                  disabled
                                  labelId="delivery-company-select-label"
                                  label="Company"
                                  name="company"
                                  onBlur={handleBlur}
                                  onChange={handleChange}
                                  value={values.company}
                                >
                                  <MenuItem value={user.id} selected>
                                    {user.name}
                                  </MenuItem>
                                </Select>
                                <FormHelperText>
                                  {touched.company && errors.company}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                fullWidth
                                error={Boolean(
                                  touched.contact && errors.contact,
                                )}
                              >
                                <InputLabel id="contact-select-label">
                                  Contact
                                </InputLabel>
                                <Select
                                  disabled
                                  labelId="contact-select-label"
                                  label="Contact"
                                  name="contact"
                                  onBlur={handleBlur}
                                  onChange={handleChange}
                                  value={values.contact}
                                >
                                  <MenuItem value={user.contact} selected>
                                    {user.profile?.contactProfile.name}
                                  </MenuItem>
                                </Select>
                                <FormHelperText>
                                  {touched.contact && errors.contact}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                error={Boolean(
                                  touched.contactPhone && errors.contactPhone,
                                )}
                                fullWidth
                                variant="outlined"
                              >
                                <InputLabel
                                  variant="outlined"
                                  htmlFor="formatted-text-exhibit-contact-number"
                                >
                                  Contact Number
                                </InputLabel>
                                <OutlinedInput
                                  label="Contact Number"
                                  variant="outlined"
                                  value={values.contactPhone}
                                  onChange={handleChange}
                                  onBlur={handleBlur}
                                  name="contactPhone"
                                  id="formatted-text-exhibit-contact-number"
                                  inputComponent={TextMaskCustom}
                                />
                                <FormHelperText>
                                  {touched.contactPhone && errors.contactPhone}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                fullWidth
                                label="Address"
                                name="address"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={Boolean(
                                  touched.address && errors.address,
                                )}
                                helperText={touched.address && errors.address}
                                value={values.address}
                                InputProps={{
                                  sx: { height: 56 },
                                }}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                fullWidth
                                label="City/Region"
                                name="city"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={Boolean(touched.city && errors.city)}
                                helperText={touched.city && errors.city}
                                value={values.city}
                                InputProps={{
                                  sx: { height: 56 },
                                }}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                fullWidth
                                label="State/Province"
                                name="state"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={Boolean(touched.state && errors.state)}
                                helperText={touched.state && errors.state}
                                value={values.state}
                                InputProps={{
                                  sx: { height: 56 },
                                }}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                fullWidth
                                label="Postal Code"
                                name="zipCode"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={Boolean(
                                  touched.zipCode && errors.zipCode,
                                )}
                                helperText={touched.zipCode && errors.zipCode}
                                value={values.zipCode}
                                InputProps={{
                                  sx: { height: 56 },
                                }}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                fullWidth
                                label="Country"
                                name="country"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={Boolean(
                                  touched.country && errors.country,
                                )}
                                helperText={touched.country && errors.country}
                                value={values.country}
                                InputProps={{
                                  sx: { height: 56 },
                                }}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                fullWidth
                                label="Destination Event"
                                name="destinationEvent"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={Boolean(
                                  touched.destinationEvent && errors.destinationEvent,
                                )}
                                helperText={touched.destinationEvent && errors.destinationEvent}
                                value={values.destinationEvent}
                                InputProps={{
                                  sx: { height: 56 },
                                }}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                fullWidth
                                label="Destination Exhibitor & Booth"
                                name="destinationExhibitorBooth"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={Boolean(
                                  touched.destinationExhibitorBooth && errors.destinationExhibitorBooth,
                                )}
                                helperText={touched.destinationExhibitorBooth && errors.destinationExhibitorBooth}
                                value={values.destinationExhibitorBooth}
                                InputProps={{
                                  sx: { height: 56 },
                                }}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                fullWidth
                                label="Destination C/O"
                                name="destinationCo"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={Boolean(
                                  touched.destinationCo && errors.destinationCo,
                                )}
                                helperText={touched.destinationCo && errors.destinationCo}
                                value={values.destinationCo}
                                InputProps={{
                                  sx: { height: 56 },
                                }}
                              />
                            </Grid>
                          </Grid>
                        </CardContent>
                      </Card>
                    </Grid>

                    {/* Carrier Details */}
                    <Grid item xs={12}>
                      <Card variant="outlined">
                        <CardHeader
                          title="Carrier Details"
                          subheader="Tell us about the carrier you want to use"
                        />
                        <CardContent>
                          <Grid container spacing={3}>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                fullWidth
                                error={Boolean(
                                  touched.carrierName && errors.carrierName,
                                )}
                              >
                                <InputLabel id="carrier-type-select-label">
                                  Carrier Name
                                </InputLabel>
                                <Select
                                  labelId="carrier-select-label"
                                  label="Carrier Name"
                                  name="carrierName"
                                  onBlur={handleBlur}
                                  onChange={handleChange}
                                  value={values.carrierName}
                                >
                                  {carrierOptions.map((carrier) => (
                                    <MenuItem
                                      key={carrier.id}
                                      value={carrier.id}
                                    >
                                      {carrier.name}
                                    </MenuItem>
                                  ))}
                                </Select>
                                <FormHelperText>
                                  {touched.carrierName && errors.carrierName}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                fullWidth
                                error={Boolean(
                                  touched.typeOfService && errors.typeOfService,
                                )}
                              >
                                <InputLabel id="service-type-select-label">
                                  Type of Service
                                </InputLabel>
                                <Select
                                  labelId="service-type-select-label"
                                  label="Type of Service"
                                  name="typeOfService"
                                  onBlur={handleBlur}
                                  onChange={handleChange}
                                  value={values.typeOfService}
                                >
                                  {carrierServices.map((carrier) => (
                                    <MenuItem
                                      key={carrier.id}
                                      value={carrier.id}
                                    >
                                      {carrier.name}
                                    </MenuItem>
                                  ))}
                                </Select>
                                <FormHelperText>
                                  {touched.typeOfService &&
                                    errors.typeOfService}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                multiline
                                label="Carrier Tracking Number"
                                name="carrierTrackingNumber"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                error={Boolean(
                                  touched.carrierTrackingNumber &&
                                  errors.carrierTrackingNumber,
                                )}
                                helperText={
                                  touched.carrierTrackingNumber &&
                                  errors.carrierTrackingNumber
                                }
                                value={values.carrierTrackingNumber}
                                sx={{ height: '56px' }}
                                InputProps={{
                                  style: { height: '100%' },
                                }}
                              />
                            </Grid>
                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                multiline
                                label="Type of service notes"
                                name="typeOfServiceNotes"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                placeholder="Add any notes for the carrier..."
                                error={Boolean(
                                  touched.typeOfServiceNotes &&
                                    errors.typeOfServiceNotes,
                                )}
                                helperText={
                                  touched.typeOfServiceNotes &&
                                  errors.typeOfServiceNotes
                                }
                                value={values.typeOfServiceNotes}
                                sx={{ height: '56px' }} // Consistent height
                                InputProps={{
                                  style: { height: '100%' }, // Ensure proper height for TextField
                                }}
                              />
                            </Grid>
                          </Grid>
                        </CardContent>
                      </Card>
                    </Grid>
                    <Divider sx={{ mt: 1 }} />
                    <Grid item xs={12}>
                      <FormControl
                        fullWidth
                        variant="outlined"
                        error={Boolean(
                          touched.deadlineMissed && errors.deadlineMissed,
                        )}
                      >
                        <FormLabel id="deadline-missed-radio-buttons">
                          Deadline Missed Operation
                        </FormLabel>
                        <RadioGroup
                          sx={{ width: "100%" }}
                          aria-labelledby="deadline-missed-radio-buttons"
                          name="deadlineMissed"
                          onChange={handleChange}
                          value={values.deadlineMissed}
                        >
                          {deadlineOptions
                            .filter((option) => option.id === 2 || (showReRouteOption && option.id === 1))
                            .map((option) => (
                              <FormControlLabel
                                key={option.id}
                                value={option.id}
                                name="deadlineMissed"
                                control={<Radio />}
                                label={option.name}
                              />
                            ))}
                        </RadioGroup>
                        <FormHelperText>
                          {touched.deadlineMissed && errors.deadlineMissed}
                        </FormHelperText>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12}>
                      <Collapse in={values.deadlineMissed === "1"}>
                        <Box display="flex" flexDirection="column" gap={2}>
                          <Grid container spacing={3}>
                            <Grid item md={6} xs={12}>
                              <TextField
                                label="Re-Route Carrier Name"
                                variant="outlined"
                                name="reRouteCarrierName"
                                InputProps={{
                                  sx: { height: 55 },
                                }}
                                value={values.reRouteCarrierName}
                                onChange={handleChange}
                                fullWidth
                                error={Boolean(touched.reRouteCarrierName && errors.reRouteCarrierName)}
                                helperText={touched.reRouteCarrierName && errors.reRouteCarrierName}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                error={Boolean(touched.reRouteCarrierPhone && errors.reRouteCarrierPhone)}
                                fullWidth
                                variant="outlined"
                              >
                                <InputLabel
                                  variant="outlined"
                                  htmlFor="formatted-text-reRoute-carrier-number"
                                >
                                  Re-Route Carrier Phone #
                                </InputLabel>
                                <OutlinedInput
                                  label="Re-Route Carrier Phone #"
                                  variant="outlined"
                                  name="reRouteCarrierPhone"
                                  value={values.reRouteCarrierPhone}
                                  onChange={handleChange}
                                  onBlur={handleBlur}
                                  inputComponent={TextMaskCustom}
                                />
                                <FormHelperText>
                                  {touched.reRouteCarrierPhone && errors.reRouteCarrierPhone}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                fullWidth
                                error={Boolean(touched.reRouteShippingOption && errors.reRouteShippingOption)}
                              >
                                <InputLabel id="reRouteCarrierOptionsLabel">Re-Route Carrier Options</InputLabel>
                                <Select
                                  labelId="reRouteOptions"
                                  label="Re-Route Carrier Options"
                                  name="reRouteShippingOption"
                                  onBlur={handleBlur}
                                  onChange={handleChange}
                                  value={values.reRouteShippingOption}
                                >
                                  {reRouteOptions.map((option) => (
                                    <MenuItem key={option.id} value={option.id}>
                                      {option.name}
                                    </MenuItem>
                                  ))}
                                </Select>
                                <FormHelperText>
                                  {touched.reRouteShippingOption && errors.reRouteShippingOption}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                          </Grid>
                        </Box>
                      </Collapse>
                    </Grid>

                    <Grid item xs={12}>
                      <FormControl
                        fullWidth
                        variant="outlined"
                        error={Boolean(touched.whoPaid && errors.whoPaid)}
                      >
                        <FormLabel id="payment-type-radio-buttons">
                          Select type of payment
                        </FormLabel>
                        <RadioGroup
                          sx={{ width: "100%" }}
                          aria-labelledby="payment-type-radio-buttons"
                          value={values.whoPaid}
                          name="whoPaid"
                          onChange={handleChange}
                        >
                          {payorOptions.map((option) => (
                            <FormControlLabel
                              value={option.id}
                              control={<Radio />}
                              label={option.name}
                              key={option.id}
                            />
                          ))}
                        </RadioGroup>
                        <FormHelperText>
                          {touched.whoPaid && errors.whoPaid}
                        </FormHelperText>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12}>
                      <Collapse in={values.whoPaid === "2"}>
                        <Box display="flex" flexDirection="column" gap={2}>
                          <Grid container spacing={3}>
                            <Grid item md={6} xs={12}>
                              <TextField
                                label="Payor Company"
                                variant="outlined"
                                name="payorCompany"
                                InputProps={{
                                  sx: { height: 55 },
                                }}
                                value={values.payorCompany}
                                onChange={handleChange}
                                fullWidth
                                error={Boolean(touched.payorCompany && errors.payorCompany)}
                                helperText={touched.payorCompany && errors.payorCompany}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                label="Payor Name"
                                variant="outlined"
                                name="payorName"
                                InputProps={{
                                  sx: { height: 55 },
                                }}
                                value={values.payorName}
                                onChange={handleChange}
                                fullWidth
                                error={Boolean(touched.payorName && errors.payorName)}
                                helperText={touched.payorName && errors.payorName}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <TextField
                                label="Payor Email"
                                variant="outlined"
                                name="payorEmail"
                                InputProps={{
                                  sx: { height: 55 },
                                }}
                                value={values.payorEmail}
                                onChange={handleChange}
                                fullWidth
                                error={Boolean(touched.payorEmail && errors.payorEmail)}
                                helperText={touched.payorEmail && errors.payorEmail}
                              />
                            </Grid>
                            <Grid item md={6} xs={12}>
                              <FormControl
                                error={Boolean(touched.reRouteCarrierPhone && errors.reRouteCarrierPhone)}
                                fullWidth
                                variant="outlined"
                              >
                                <InputLabel
                                  variant="outlined"
                                  htmlFor="formatted-text-payor-number"
                                >
                                  Payor Phone #
                                </InputLabel>
                                <OutlinedInput
                                  label="Payor Phone #"
                                  variant="outlined"
                                  name="payorPhone"
                                  value={values.payorPhone}
                                  onChange={handleChange}
                                  onBlur={handleBlur}
                                  inputComponent={TextMaskCustom}
                                />
                                <FormHelperText>
                                  {touched.payorPhone && errors.payorPhone}
                                </FormHelperText>
                              </FormControl>
                            </Grid>
                            <Grid item xs={12}>
                              <TextField
                                label="Payor Billing Address"
                                variant="outlined"
                                name="payorBillingAddress"
                                multiline
                                rows={4}
                                value={values.payorBillingAddress}
                                onChange={handleChange}
                                fullWidth
                                error={Boolean(touched.payorBillingAddress && errors.payorBillingAddress)}
                                helperText={touched.payorBillingAddress && errors.payorBillingAddress}
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </Collapse>
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        minRows={3}
                        placeholder="Add any special instructions for the delivery..."
                        label="Special Instructions"
                        name="specialInstructions"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={Boolean(
                          touched.specialInstructions &&
                            errors.specialInstructions,
                        )}
                        helperText={
                          touched.specialInstructions &&
                          errors.specialInstructions
                        }
                        value={values.specialInstructions}
                      />
                    </Grid>
                  </Grid>
                </DialogContent>
                <Box
                  sx={{
                    display: { xs: "block", sm: "flex" },
                    alignItems: "center",
                    justifyContent: "flex-end",
                    p: 3,
                  }}
                >
                  <Box>
                    <Button
                      fullWidth={mobile}
                      sx={{
                        mr: { xs: 0, sm: 2 },
                        my: { xs: 2, sm: 0 },
                      }}
                      color="secondary"
                      variant="outlined"
                      onClick={onClose}
                    >
                      Close
                    </Button>
                    <Button
                      fullWidth={mobile}
                      type="submit"
                      startIcon={
                        isSubmitting ? <CircularProgress size="1rem" /> : null
                      }
                      disabled={Boolean(errors.submit) || isSubmitting}
                      variant="contained"
                      size="large"
                      color="primary"
                    >
                      Update Request
                    </Button>
                  </Box>
                </Box>
              </form>
            )}
          </Formik>
        )}
      </DialogTitle>
    </Dialog>
  );
};

BillOfLadingModal.propTypes = {};

export default BillOfLadingModal;
