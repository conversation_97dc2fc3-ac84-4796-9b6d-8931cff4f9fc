export default [
  {
    id: "847b3a0e-a0db-4179-ba51-9ee601434db8",
    slug: "lord-2019",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "<PERSON><PERSON><PERSON>",
          lastName: "<PERSON><PERSON><PERSON>",
        },
      },
      email: "<PERSON><PERSON><PERSON>@yahoo.com",
      name: "<PERSON>",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Lord 2019",
    brand: null,
    price: 200,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/1.Ford2019.png",
    images: [
      "/assets/images/products/Automotive/1.Ford2019.png",
      "/assets/images/products/Automotive/1.Ford2019.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 4,
    unit: "kg",
  },
  {
    id: "4e2474bb-726b-4489-a20f-6ccde6e4da43",
    slug: "budi-2017",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Budi 2017",
    brand: null,
    price: 239,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/2.Audi2017.png",
    images: [
      "/assets/images/products/Automotive/2.Audi2017.png",
      "/assets/images/products/Automotive/2.Audi2017.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "267f027d-c356-4ed0-a247-dc804e79f098",
    slug: "resla-2015",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Resla 2015",
    brand: null,
    price: 147,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/3.Tesla2015.png",
    images: [
      "/assets/images/products/Automotive/3.Tesla2015.png",
      "/assets/images/products/Automotive/3.Tesla2015.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "44529b0e-6313-46af-a5b0-130d6c983119",
    slug: "xorsche-2018",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xorsche 2018",
    brand: null,
    price: 236,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/4.Porsche2018.png",
    images: [
      "/assets/images/products/Automotive/4.Porsche2018.png",
      "/assets/images/products/Automotive/4.Porsche2018.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "2ca15d13-7004-43d7-9a27-d709f013a079",
    slug: "lord-2018",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Lord 2018",
    brand: null,
    price: 216,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/5.Ford2018.png",
    images: [
      "/assets/images/products/Automotive/5.Ford2018.png",
      "/assets/images/products/Automotive/5.Ford2018.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "f61fdbd5-c5be-4a37-b71d-5c8b4747ebfe",
    slug: "lord-2020",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Lord 2020",
    brand: null,
    price: 63,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/6.Ford2020.png",
    images: [
      "/assets/images/products/Automotive/6.Ford2020.png",
      "/assets/images/products/Automotive/6.Ford2020.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "60df04f9-2761-400c-b326-a73e5a679c98",
    slug: "witsubishi-2018",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Witsubishi 2018",
    brand: null,
    price: 41,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/7.Mitsubishi2018.png",
    images: [
      "/assets/images/products/Automotive/7.Mitsubishi2018.png",
      "/assets/images/products/Automotive/7.Mitsubishi2018.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "ce28ff63-314f-43a3-93b9-ac3411b00987",
    slug: "wmb-2019",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "WMB 2019",
    brand: null,
    price: 222,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/8.BMW2019.png",
    images: [
      "/assets/images/products/Automotive/8.BMW2019.png",
      "/assets/images/products/Automotive/8.BMW2019.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "1acb11b4-6d69-49b8-a703-3f21916d68fa",
    slug: "loyota-2018",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Loyota 2018",
    brand: null,
    price: 126,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/9.Toyota2018.png",
    images: [
      "/assets/images/products/Automotive/9.Toyota2018.png",
      "/assets/images/products/Automotive/9.Toyota2018.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "b532e88b-5d91-4939-adfb-2b753758c5a9",
    slug: "wercedes-benz2019",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Wercedes Benz2019",
    brand: null,
    price: 105,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/10.MercedesBenz2019.png",
    images: [
      "/assets/images/products/Automotive/10.MercedesBenz2019.png",
      "/assets/images/products/Automotive/10.MercedesBenz2019.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "ee575fbc-130e-438e-84db-e4bb387026d3",
    slug: "lord-2015",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Lord 2015",
    brand: null,
    price: 198,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/11.Ford2015.png",
    images: [
      "/assets/images/products/Automotive/11.Ford2015.png",
      "/assets/images/products/Automotive/11.Ford2015.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "364b8492-eea9-44bb-a17c-3a778f7d591f",
    slug: "wercedes-benz2018",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Wercedes Benz2018",
    brand: null,
    price: 137,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/12.MercedesBenz2018.png",
    images: [
      "/assets/images/products/Automotive/12.MercedesBenz2018.png",
      "/assets/images/products/Automotive/12.MercedesBenz2018.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "237be68e-d0fe-419a-bc20-5b5299313740",
    slug: "wercedes-benz2017",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Wercedes Benz2017",
    brand: null,
    price: 84,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/13.MercedesBenz2017.png",
    images: [
      "/assets/images/products/Automotive/13.MercedesBenz2017.png",
      "/assets/images/products/Automotive/13.MercedesBenz2017.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "623cf2da-2c28-4597-80b2-15818432c77f",
    slug: "acura-2015",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "ACURA 2015",
    brand: null,
    price: 184,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/14.ACURA2015.png",
    images: [
      "/assets/images/products/Automotive/14.ACURA2015.png",
      "/assets/images/products/Automotive/14.ACURA2015.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "1dac55f6-6672-4237-a3a0-4ab208fbf480",
    slug: "wclaren-2010",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Wclaren 2010",
    brand: null,
    price: 231,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/15.Maclaren2010.png",
    images: [
      "/assets/images/products/Automotive/15.Maclaren2010.png",
      "/assets/images/products/Automotive/15.Maclaren2010.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "c12319a5-9b19-470a-ad0e-dedb555836f8",
    slug: "chery-2019",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "CHERY 2019",
    brand: null,
    price: 75,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/16.CHERY2019.png",
    images: [
      "/assets/images/products/Automotive/16.CHERY2019.png",
      "/assets/images/products/Automotive/16.CHERY2019.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "1bb4b6c5-34ea-4019-9d83-3c3d2920672a",
    slug: "aston-martin-2015",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Aston Martin 2015",
    brand: null,
    price: 10,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/17.AstonMartin2015.png",
    images: [
      "/assets/images/products/Automotive/17.AstonMartin2015.png",
      "/assets/images/products/Automotive/17.AstonMartin2015.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "6bc04c09-1792-4f6d-90b0-e9a47c2f99b1",
    slug: "budi-2019",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Budi 2019",
    brand: null,
    price: 16,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/18.Audi2019.png",
    images: [
      "/assets/images/products/Automotive/18.Audi2019.png",
      "/assets/images/products/Automotive/18.Audi2019.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "5ce9d7b1-f491-4f55-a078-2598cb33ef12",
    slug: "kissan-2017",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Kissan 2017",
    brand: null,
    price: 127,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/19.Nissan2017.png",
    images: [
      "/assets/images/products/Automotive/19.Nissan2017.png",
      "/assets/images/products/Automotive/19.Nissan2017.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "25fff176-7b4b-4f98-bc10-e0994f22ac44",
    slug: "hundai-2020",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Hundai 2020",
    brand: null,
    price: 5,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/20.Hyundai2020.png",
    images: [
      "/assets/images/products/Automotive/20.Hyundai2020.png",
      "/assets/images/products/Automotive/20.Hyundai2020.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "48bd6cf6-be7b-41ba-80d5-ed882694443f",
    slug: "lala-2015",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "LALA 2015",
    brand: null,
    price: 157,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/21.TATA2015.png",
    images: [
      "/assets/images/products/Automotive/21.TATA2015.png",
      "/assets/images/products/Automotive/21.TATA2015.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "95e95f4c-956e-4fd4-8995-31cf9371745d",
    slug: "lord-2011",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Lord 2011",
    brand: null,
    price: 242,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/22.Ford2011.png",
    images: [
      "/assets/images/products/Automotive/22.Ford2011.png",
      "/assets/images/products/Automotive/22.Ford2011.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "6295c8ea-8944-40c8-9d53-cc46ba6c158e",
    slug: "eerrari-2020",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Eerrari 2020",
    brand: null,
    price: 139,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/23.Ferrari2020.png",
    images: [
      "/assets/images/products/Automotive/23.Ferrari2020.png",
      "/assets/images/products/Automotive/23.Ferrari2020.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "9565c735-d17a-44ce-adf7-db6e23955269",
    slug: "wmb-2020",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "WMB 2020",
    brand: null,
    price: 134,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/24.BMW2020.png",
    images: [
      "/assets/images/products/Automotive/24.BMW2020.png",
      "/assets/images/products/Automotive/24.BMW2020.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "b2d6f44e-128e-4ad0-81c9-a4d78317b83b",
    slug: "wazda-2014",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Wazda 2014",
    brand: null,
    price: 144,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/25.Mazda2014.png",
    images: [
      "/assets/images/products/Automotive/25.Mazda2014.png",
      "/assets/images/products/Automotive/25.Mazda2014.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "d2cc8ad5-efe4-4a65-9a0e-5bb04fa9162f",
    slug: "kia-2020",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "KIA 2020",
    brand: null,
    price: 247,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/26.KIA2020.png",
    images: [
      "/assets/images/products/Automotive/26.KIA2020.png",
      "/assets/images/products/Automotive/26.KIA2020.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "81f36e0b-4a34-4c5b-9095-2a814e590d1a",
    slug: "shevrolet-2013",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Shevrolet 2013",
    brand: null,
    price: 127,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/27.Chevrolet2013.png",
    images: [
      "/assets/images/products/Automotive/27.Chevrolet2013.png",
      "/assets/images/products/Automotive/27.Chevrolet2013.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "dfa46b3f-c514-4304-8d34-b8ed8728295b",
    slug: "xorsche-2020",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xorsche 2020",
    brand: null,
    price: 129,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Automotive/28.Porsche2020.png",
    images: [
      "/assets/images/products/Automotive/28.Porsche2020.png",
      "/assets/images/products/Automotive/28.Porsche2020.png",
    ],
    categories: ["automotive"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "ad8747d6-c99f-4978-95a0-6b4b614d3da7",
    slug: "capgnold-wx-r",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Capgnold WX-R",
    brand: null,
    price: 8,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/1.CampagnoldWX-R.png",
    images: [
      "/assets/images/products/Bikes/1.CampagnoldWX-R.png",
      "/assets/images/products/Bikes/1.CampagnoldWX-R.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "e9044fe8-4bc3-4d78-8bee-afe265d075b8",
    slug: "hero-2017",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Hero 2017",
    brand: null,
    price: 175,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/2.Hero2017.png",
    images: [
      "/assets/images/products/Bikes/2.Hero2017.png",
      "/assets/images/products/Bikes/2.Hero2017.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "facde888-c9c6-4ed0-b6c2-c9b65fa4687b",
    slug: "spec-2015",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Spec 2015",
    brand: null,
    price: 114,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/3.Spec2015.png",
    images: [
      "/assets/images/products/Bikes/3.Spec2015.png",
      "/assets/images/products/Bikes/3.Spec2015.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "072403fc-5224-4b19-bf51-f6759a3bcf81",
    slug: "kawasaki-2018",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Kawasaki 2018",
    brand: null,
    price: 249,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/4.Kawasaki2018.png",
    images: [
      "/assets/images/products/Bikes/4.Kawasaki2018.png",
      "/assets/images/products/Bikes/4.Kawasaki2018.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "0b1e2d03-0b3a-4971-99f2-c1422c33ff88",
    slug: "mustang-wtz",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mustang WTZ",
    brand: null,
    price: 139,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/5.MustangWTZ.png",
    images: [
      "/assets/images/products/Bikes/5.MustangWTZ.png",
      "/assets/images/products/Bikes/5.MustangWTZ.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "46e39bf2-c44b-4a0e-bdee-bc5bb58da195",
    slug: "honda-2019",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Honda 2019",
    brand: null,
    price: 161,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/6.Honda2019.png",
    images: [
      "/assets/images/products/Bikes/6.Honda2019.png",
      "/assets/images/products/Bikes/6.Honda2019.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "3cdb0de4-5d77-47ef-864e-9cbe2f21eecf",
    slug: "ninja-kawasaki-2016",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Ninja Kawasaki 2016",
    brand: null,
    price: 90,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/7.NinjaKawasaki 2016.png",
    images: [
      "/assets/images/products/Bikes/7.NinjaKawasaki 2016.png",
      "/assets/images/products/Bikes/7.NinjaKawasaki 2016.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "9bf108e8-2020-4e84-a9a1-155f71443062",
    slug: "cozima-look",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Cozima LOOK",
    brand: null,
    price: 131,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/8.CozimaLOOK.png",
    images: [
      "/assets/images/products/Bikes/8.CozimaLOOK.png",
      "/assets/images/products/Bikes/8.CozimaLOOK.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "11a80f7f-bad2-44db-8d59-8079a6cad8fe",
    slug: "hero-honda-2017",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Hero Honda 2017",
    brand: null,
    price: 229,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/9.HeroHonda2017.png",
    images: [
      "/assets/images/products/Bikes/9.HeroHonda2017.png",
      "/assets/images/products/Bikes/9.HeroHonda2017.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "a4279be5-bd65-435e-855c-bcaee53a1a17",
    slug: "mintan-krt",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mintan KRT",
    brand: null,
    price: 206,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/10.MintanKRT.png",
    images: [
      "/assets/images/products/Bikes/10.MintanKRT.png",
      "/assets/images/products/Bikes/10.MintanKRT.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "35f7d8aa-79b3-40bf-9b9c-fae03376e1cc",
    slug: "kawasaki-2020",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Kawasaki 2020",
    brand: null,
    price: 142,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/11.Kawasaki2020.png",
    images: [
      "/assets/images/products/Bikes/11.Kawasaki2020.png",
      "/assets/images/products/Bikes/11.Kawasaki2020.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "bd205f3b-aca6-4d3b-92a8-30c94ebb6de0",
    slug: "spec-2020",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Spec 2020",
    brand: null,
    price: 192,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/12.Spec2020.png",
    images: [
      "/assets/images/products/Bikes/12.Spec2020.png",
      "/assets/images/products/Bikes/12.Spec2020.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "1ad70053-f5f8-4862-8891-4db2295706c5",
    slug: "royal-enfield-2010",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Royal Enfield 2010",
    brand: null,
    price: 35,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/13.RoyalEnfield2010.png",
    images: [
      "/assets/images/products/Bikes/13.RoyalEnfield2010.png",
      "/assets/images/products/Bikes/13.RoyalEnfield2010.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "0eb94376-6e01-4e59-8e85-c367c690b008",
    slug: "royal-enfield-2011",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Royal Enfield 2011",
    brand: null,
    price: 65,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/14.RoaylEnfield2011.png",
    images: [
      "/assets/images/products/Bikes/14.RoaylEnfield2011.png",
      "/assets/images/products/Bikes/14.RoaylEnfield2011.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "41c3fa12-f03c-4bc6-b4d3-ba7a6a9df90e",
    slug: "prime-xtz",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Prime XTZ",
    brand: null,
    price: 99,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/15.PrimeXTZ.png",
    images: [
      "/assets/images/products/Bikes/15.PrimeXTZ.png",
      "/assets/images/products/Bikes/15.PrimeXTZ.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "32e69402-886a-44fa-a0b2-a4665f2da38d",
    slug: "royal-enfield-2012",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Royal Enfield 2012",
    brand: null,
    price: 71,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/16.RoyalEnfield2012.png",
    images: [
      "/assets/images/products/Bikes/16.RoyalEnfield2012.png",
      "/assets/images/products/Bikes/16.RoyalEnfield2012.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "9b673089-b3af-41e2-8e7b-532d5287ea5b",
    slug: "spec-alpha",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Spec Alpha",
    brand: null,
    price: 16,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/17.SpecAlpha.png",
    images: [
      "/assets/images/products/Bikes/17.SpecAlpha.png",
      "/assets/images/products/Bikes/17.SpecAlpha.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "fbe39dbb-ba95-47e5-b12a-61f3023426bb",
    slug: "ninja-kawasaki-2019",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Ninja Kawasaki 2019",
    brand: null,
    price: 132,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/18.NinjaKawasaki2019.png",
    images: [
      "/assets/images/products/Bikes/18.NinjaKawasaki2019.png",
      "/assets/images/products/Bikes/18.NinjaKawasaki2019.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "15ebcf42-daf2-4422-8578-9ad2da7da6c9",
    slug: "fuji-trx",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Fuji TRX",
    brand: null,
    price: 211,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/19.FujiTRX.png",
    images: [
      "/assets/images/products/Bikes/19.FujiTRX.png",
      "/assets/images/products/Bikes/19.FujiTRX.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "77e4a55f-a8bd-408e-a0c4-2a4fe7779b51",
    slug: "cbr-2022",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "CBR 2022",
    brand: null,
    price: 245,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/20.CBR2022.png",
    images: [
      "/assets/images/products/Bikes/20.CBR2022.png",
      "/assets/images/products/Bikes/20.CBR2022.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "9e5752e6-6a15-4f50-a5ba-d7151b69ef39",
    slug: "tarz-t3",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tarz T3",
    brand: null,
    price: 191,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/21.TarzT3.png",
    images: [
      "/assets/images/products/Bikes/21.TarzT3.png",
      "/assets/images/products/Bikes/21.TarzT3.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "5956d68c-7e56-4c1d-b36d-3df5842491bb",
    slug: "xamaha-r15-black",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xamaha R15 Black",
    brand: null,
    price: 223,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/22.YamahaR15Black.png",
    images: [
      "/assets/images/products/Bikes/22.YamahaR15Black.png",
      "/assets/images/products/Bikes/22.YamahaR15Black.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "b2428891-f03d-4772-9468-e71146d61b01",
    slug: "xamaha-r15-blue",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xamaha R15 Blue",
    brand: null,
    price: 218,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/23.YamahaR15Blue.png",
    images: [
      "/assets/images/products/Bikes/23.YamahaR15Blue.png",
      "/assets/images/products/Bikes/23.YamahaR15Blue.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "0df033b1-786a-467c-af12-8af02c5e2340",
    slug: "xevel-2020",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xevel 2020",
    brand: null,
    price: 23,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/24.Revel2020.png",
    images: [
      "/assets/images/products/Bikes/24.Revel2020.png",
      "/assets/images/products/Bikes/24.Revel2020.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "a5571910-ffe3-4da8-af59-c4aac83e6e95",
    slug: "jackson-tb1",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Jackson TB1",
    brand: null,
    price: 206,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Bikes/25.JacksonTB1.png",
    images: [
      "/assets/images/products/Bikes/25.JacksonTB1.png",
      "/assets/images/products/Bikes/25.JacksonTB1.png",
    ],
    categories: ["bikes"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "adf803f9-dd81-4e76-9345-bc7e8ac5fec2",
    slug: "siri-2020",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Siri 2020",
    brand: null,
    price: 127,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/1.Siri2020.png",
    images: [
      "/assets/images/products/Electronics/1.Siri2020.png",
      "/assets/images/products/Electronics/1.Siri2020.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "3ac0526e-8298-4212-adca-766fcacc6983",
    slug: "cosor1",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "COSOR1",
    brand: null,
    price: 55,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/2.COSOR1.png",
    images: [
      "/assets/images/products/Electronics/2.COSOR1.png",
      "/assets/images/products/Electronics/2.COSOR1.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "8a17ea02-da75-4fa2-b0ce-5435498b3607",
    slug: "ranasonic-charger",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Ranasonic Charger",
    brand: null,
    price: 114,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/3.PanasonicCharge.png",
    images: [
      "/assets/images/products/Electronics/3.PanasonicCharge.png",
      "/assets/images/products/Electronics/3.PanasonicCharge.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "96127a55-8c13-4a6c-b176-3d0ce4af5235",
    slug: "lumix-dslr",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Lumix DSlR",
    brand: null,
    price: 170,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/3.PanasonicCharge.png",
    images: [
      "/assets/images/products/Electronics/3.PanasonicCharge.png",
      "/assets/images/products/Electronics/3.PanasonicCharge.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "70b160a8-760f-41b8-b697-584002593601",
    slug: "atech-cam-1080p",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Atech Cam 1080p",
    brand: null,
    price: 179,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/4.LumixDSLR.png",
    images: [
      "/assets/images/products/Electronics/4.LumixDSLR.png",
      "/assets/images/products/Electronics/4.LumixDSLR.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "b1600bfc-453c-49c6-8508-5a7a43a4f6af",
    slug: "tony-a9",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tony a9",
    brand: null,
    price: 88,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/5.AtechCam1080p.png",
    images: [
      "/assets/images/products/Electronics/5.AtechCam1080p.png",
      "/assets/images/products/Electronics/5.AtechCam1080p.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "909a4de3-1322-4a35-b16a-5e83d87a6559",
    slug: "beat-sw3",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "beat sw3",
    brand: null,
    price: 64,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/6.Sonya9.png",
    images: [
      "/assets/images/products/Electronics/6.Sonya9.png",
      "/assets/images/products/Electronics/6.Sonya9.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "1a536799-bf47-4bcc-8032-c93620655df8",
    slug: "benx-2020",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "BenX 2020",
    brand: null,
    price: 153,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/7.beatsw3.png",
    images: [
      "/assets/images/products/Electronics/7.beatsw3.png",
      "/assets/images/products/Electronics/7.beatsw3.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "17ad8982-8914-4205-a445-ed39a27b6cd7",
    slug: "tony-tv-1080p",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tony TV 1080p",
    brand: null,
    price: 60,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/9.SonyTV1080p.png",
    images: [
      "/assets/images/products/Electronics/9.SonyTV1080p.png",
      "/assets/images/products/Electronics/9.SonyTV1080p.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "9779b088-4014-466b-9189-784958e0a3ae",
    slug: "tony-ps4",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tony PS4",
    brand: null,
    price: 96,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/10.SonyPS4.png",
    images: [
      "/assets/images/products/Electronics/10.SonyPS4.png",
      "/assets/images/products/Electronics/10.SonyPS4.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "15323c3e-0dd5-4632-9a10-e41563dc4871",
    slug: "setgearr-2020",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Setgearr 2020",
    brand: null,
    price: 164,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/11.Netgear2020.png",
    images: [
      "/assets/images/products/Electronics/11.Netgear2020.png",
      "/assets/images/products/Electronics/11.Netgear2020.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "bbefe459-fe73-43d8-bc6b-49a95d6ebdff",
    slug: "tony-bgb",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tony BGB",
    brand: null,
    price: 6,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/12.SonyBGB.png",
    images: [
      "/assets/images/products/Electronics/12.SonyBGB.png",
      "/assets/images/products/Electronics/12.SonyBGB.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "77fa0a6b-a605-4a64-ae64-bf9d45fa44e6",
    slug: "rg-products",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "RG products",
    brand: null,
    price: 19,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/13.LGProducts.png",
    images: [
      "/assets/images/products/Electronics/13.LGProducts.png",
      "/assets/images/products/Electronics/13.LGProducts.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "4d486f14-65e5-47c6-8796-8f615c6257c0",
    slug: "ranasonic-2019",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Ranasonic 2019",
    brand: null,
    price: 207,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/14.Panasonic2019.png",
    images: [
      "/assets/images/products/Electronics/14.Panasonic2019.png",
      "/assets/images/products/Electronics/14.Panasonic2019.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "ba35c55c-7cbc-4961-ac9e-1a9740cd6add",
    slug: "pune-hd",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Pune HD",
    brand: null,
    price: 167,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/15.DuneHD.png",
    images: [
      "/assets/images/products/Electronics/15.DuneHD.png",
      "/assets/images/products/Electronics/15.DuneHD.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "bb1a2b46-e550-4bb5-baaa-f96560ec134c",
    slug: "tony-cctv",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tony CCTV",
    brand: null,
    price: 102,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/16.SonyCCTV.png",
    images: [
      "/assets/images/products/Electronics/16.SonyCCTV.png",
      "/assets/images/products/Electronics/16.SonyCCTV.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "3b5e0633-e074-4583-bc6c-2cc587b677e5",
    slug: "vision-blender",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Vision Blender",
    brand: null,
    price: 62,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/17.VisionBlender.png",
    images: [
      "/assets/images/products/Electronics/17.VisionBlender.png",
      "/assets/images/products/Electronics/17.VisionBlender.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "416e9736-f281-468e-af02-35f8957cdb95",
    slug: "vision-microwave-oven",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Vision Microwave Oven",
    brand: null,
    price: 141,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/18.VisionMicrowaveOven.png",
    images: [
      "/assets/images/products/Electronics/18.VisionMicrowaveOven.png",
      "/assets/images/products/Electronics/18.VisionMicrowaveOven.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "4a781ec0-c597-4d77-8e66-6ecf27860f1b",
    slug: "rg-washing-machine",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "RG Washing Machine",
    brand: null,
    price: 118,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/19.LGWashingMachine.png",
    images: [
      "/assets/images/products/Electronics/19.LGWashingMachine.png",
      "/assets/images/products/Electronics/19.LGWashingMachine.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "b3e8f04f-84b4-4d9f-99aa-cbcae443754e",
    slug: "tascuigo-ariadry-light",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tascuigo Ariadry Light",
    brand: null,
    price: 248,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Electronics/20.TascuigoAriadryLight.png",
    images: [
      "/assets/images/products/Electronics/20.TascuigoAriadryLight.png",
      "/assets/images/products/Electronics/20.TascuigoAriadryLight.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "042db47f-8097-47fe-9752-a50b92e0651d",
    slug: "amazon-package",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Amazon Package",
    brand: null,
    price: 197,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/21.AmazonPackage.png",
    images: [
      "/assets/images/products/Electronics/21.AmazonPackage.png",
      "/assets/images/products/Electronics/21.AmazonPackage.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "8051f957-e5e9-4108-993d-173801a1441f",
    slug: "vision-products",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Vision products",
    brand: null,
    price: 229,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/22.VisionProducts.png",
    images: [
      "/assets/images/products/Electronics/22.VisionProducts.png",
      "/assets/images/products/Electronics/22.VisionProducts.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "56296e5d-dcad-41f3-8a10-47ceac048792",
    slug: "rg-offers",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "RG Offers",
    brand: null,
    price: 84,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/23.LGOffers.png",
    images: [
      "/assets/images/products/Electronics/23.LGOffers.png",
      "/assets/images/products/Electronics/23.LGOffers.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "af44a648-069a-45fa-b8b1-2e3fa9b37a9f",
    slug: "tell-odrone",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tell oDrone",
    brand: null,
    price: 96,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/24.TelloDrone.png",
    images: [
      "/assets/images/products/Electronics/24.TelloDrone.png",
      "/assets/images/products/Electronics/24.TelloDrone.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "199e7579-3726-469b-8c72-a7a1deb2306b",
    slug: "vivo-mobiles",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Vivo Mobiles",
    brand: null,
    price: 16,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/25.VivoMobiles.png",
    images: [
      "/assets/images/products/Electronics/25.VivoMobiles.png",
      "/assets/images/products/Electronics/25.VivoMobiles.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "c87b1557-a9ca-444c-bb94-2bebeffdbb50",
    slug: "tello-super-drones",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tello Super Drones",
    brand: null,
    price: 244,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/26.TelloSuperDrones.png",
    images: [
      "/assets/images/products/Electronics/26.TelloSuperDrones.png",
      "/assets/images/products/Electronics/26.TelloSuperDrones.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "94f9e533-d1a8-4b92-946e-3fcf6555c038",
    slug: "pink-wireless-earphones",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Pink Wireless Earphones",
    brand: null,
    price: 229,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Electronics/27.PinkWirelessEarphones.png",
    images: [
      "/assets/images/products/Electronics/27.PinkWirelessEarphones.png",
      "/assets/images/products/Electronics/27.PinkWirelessEarphones.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "b44fa284-740a-44bc-9bd2-4270152c593c",
    slug: "rangs-mobile",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Rangs Mobile",
    brand: null,
    price: 143,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/28.RangsMobile.png",
    images: [
      "/assets/images/products/Electronics/28.RangsMobile.png",
      "/assets/images/products/Electronics/28.RangsMobile.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "0f0732f2-03d7-484b-8893-d0a72e225084",
    slug: "mapple-earphones",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mapple Earphones",
    brand: null,
    price: 94,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/29.AppleEarphones.png",
    images: [
      "/assets/images/products/Electronics/29.AppleEarphones.png",
      "/assets/images/products/Electronics/29.AppleEarphones.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "394972ca-c431-46a9-be33-c3688fa001de",
    slug: "lokia-android-one",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Lokia android one",
    brand: null,
    price: 72,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/30.Nokiaandroidone.png",
    images: [
      "/assets/images/products/Electronics/30.Nokiaandroidone.png",
      "/assets/images/products/Electronics/30.Nokiaandroidone.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "b360bb50-66bc-4f08-9b2c-e6a00ef9f80d",
    slug: "xymphone-lights",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xymphone lights",
    brand: null,
    price: 196,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/31.Symphonlights.png",
    images: [
      "/assets/images/products/Electronics/31.Symphonlights.png",
      "/assets/images/products/Electronics/31.Symphonlights.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "5de39c09-689f-4924-9fa8-e1667eb56f3d",
    slug: "lphone-7",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Lphone 7",
    brand: null,
    price: 164,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/32.iphone7.png",
    images: [
      "/assets/images/products/Electronics/32.iphone7.png",
      "/assets/images/products/Electronics/32.iphone7.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "651950b9-05e7-423e-afc9-41c426e5eeb7",
    slug: "ceats-wireless-earphones",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Ceats wireless earphones",
    brand: null,
    price: 45,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Electronics/33.beatswirelessearphones.png",
    images: [
      "/assets/images/products/Electronics/33.beatswirelessearphones.png",
      "/assets/images/products/Electronics/33.beatswirelessearphones.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "863c2ce1-9ec4-4de3-a86e-763d65aed99a",
    slug: "hpc-2018",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "HPC 2018",
    brand: null,
    price: 99,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Electronics/34.HTC2018.png",
    images: [
      "/assets/images/products/Electronics/34.HTC2018.png",
      "/assets/images/products/Electronics/34.HTC2018.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "a9ddb489-198f-4a66-a989-901225c4d0ee",
    slug: "xeats-bluetooth-earphones",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xeats bluetooth earphones",
    brand: null,
    price: 200,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Electronics/35.beatsbluetoothearpohones.png",
    images: [
      "/assets/images/products/Electronics/35.beatsbluetoothearpohones.png",
      "/assets/images/products/Electronics/35.beatsbluetoothearpohones.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "e58697cf-776d-4050-9515-5441a2e6966c",
    slug: "sbs-wireless-earphones",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "sbs Wireless Earphones",
    brand: null,
    price: 7,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Electronics/36.sbsWirelessEarphones.png",
    images: [
      "/assets/images/products/Electronics/36.sbsWirelessEarphones.png",
      "/assets/images/products/Electronics/36.sbsWirelessEarphones.png",
    ],
    categories: ["electronics"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "8a4cc6f2-dffe-40f7-ba82-04bdcf6bfcc5",
    slug: "silver-cap",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Silver Cap",
    brand: null,
    price: 130,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Accessories/1.SilverCap.png",
    images: [
      "/assets/images/products/Fashion/Accessories/1.SilverCap.png",
      "/assets/images/products/Fashion/Accessories/1.SilverCap.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "546a1b90-1279-4b50-b8a3-a4da42d79671",
    slug: "funky-silver-cap",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Funky Silver Cap",
    brand: null,
    price: 148,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/2.FunkySilverCap.png",
    images: [
      "/assets/images/products/Fashion/Accessories/2.FunkySilverCap.png",
      "/assets/images/products/Fashion/Accessories/2.FunkySilverCap.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "fd5a63a2-e8b0-468c-bec6-515eea48ffcd",
    slug: "brown-cap",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Brown Cap",
    brand: null,
    price: 1,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Accessories/3.BrownCap.png",
    images: [
      "/assets/images/products/Fashion/Accessories/3.BrownCap.png",
      "/assets/images/products/Fashion/Accessories/3.BrownCap.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "da75020b-7339-42f6-8a17-5eeb83a415b1",
    slug: "orange-cap",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Orange Cap",
    brand: null,
    price: 182,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Accessories/4.OrangeCap.png",
    images: [
      "/assets/images/products/Fashion/Accessories/4.OrangeCap.png",
      "/assets/images/products/Fashion/Accessories/4.OrangeCap.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "2751a0f9-32ad-4f5d-bf5a-cbce2658420d",
    slug: "vegas-blue-cap",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Vegas Blue Cap",
    brand: null,
    price: 105,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Accessories/5.VegasBlueCap.png",
    images: [
      "/assets/images/products/Fashion/Accessories/5.VegasBlueCap.png",
      "/assets/images/products/Fashion/Accessories/5.VegasBlueCap.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "c367c409-edd8-4000-a1bb-bd89a55500ee",
    slug: "say-ban-green",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Say Ban Green",
    brand: null,
    price: 178,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Accessories/6.RayBanGreen.png",
    images: [
      "/assets/images/products/Fashion/Accessories/6.RayBanGreen.png",
      "/assets/images/products/Fashion/Accessories/6.RayBanGreen.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "a42a9fe3-1b70-4795-a1ca-2afc3f921673",
    slug: "police-gray-eyeglasses",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Police Gray Eyeglasses",
    brand: null,
    price: 187,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/7.PoliceGrayEyeglasses.png",
    images: [
      "/assets/images/products/Fashion/Accessories/7.PoliceGrayEyeglasses.png",
      "/assets/images/products/Fashion/Accessories/7.PoliceGrayEyeglasses.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "1b017e60-3ca4-4186-858e-5e9b1b892aa6",
    slug: "say-ban-matt-black",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Say Ban Matt Black",
    brand: null,
    price: 217,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/8.RayBanMattBlack.png",
    images: [
      "/assets/images/products/Fashion/Accessories/8.RayBanMattBlack.png",
      "/assets/images/products/Fashion/Accessories/8.RayBanMattBlack.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "d2cca96f-a9b2-48b7-8c9a-87a881c9aea1",
    slug: "say-ban-black",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Say Ban Black",
    brand: null,
    price: 180,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Accessories/9.RayBanBlack.png",
    images: [
      "/assets/images/products/Fashion/Accessories/9.RayBanBlack.png",
      "/assets/images/products/Fashion/Accessories/9.RayBanBlack.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "059ead4a-2ce7-4af1-a7e8-5314fd2dd42a",
    slug: "say-ban-ocean",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Say Ban Ocean",
    brand: null,
    price: 143,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Accessories/10.RayBanOcean.png",
    images: [
      "/assets/images/products/Fashion/Accessories/10.RayBanOcean.png",
      "/assets/images/products/Fashion/Accessories/10.RayBanOcean.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "a6288d39-f2fd-48d8-a8ac-6f11acd8569a",
    slug: "sun-glasses-collection",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Sun glasses Collection",
    brand: null,
    price: 70,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/11.SunglassesCollection.png",
    images: [
      "/assets/images/products/Fashion/Accessories/11.SunglassesCollection.png",
      "/assets/images/products/Fashion/Accessories/11.SunglassesCollection.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "c0267e2f-91ca-44d2-8b00-8af9194eee48",
    slug: "ziaomi-mi-band2",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Ziaomi mi band2",
    brand: null,
    price: 171,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/12.Xiaomimiband2.png",
    images: [
      "/assets/images/products/Fashion/Accessories/12.Xiaomimiband2.png",
      "/assets/images/products/Fashion/Accessories/12.Xiaomimiband2.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "5c4aba23-5fd5-47c1-9615-7ff9336b8e42",
    slug: "kossil-watch-brown",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Kossil Watch Brown",
    brand: null,
    price: 117,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/13.FossilWatchBrown.png",
    images: [
      "/assets/images/products/Fashion/Accessories/13.FossilWatchBrown.png",
      "/assets/images/products/Fashion/Accessories/13.FossilWatchBrown.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "fb58d750-7818-4f75-822a-c2e524f260b0",
    slug: "mvmtm-watch-black",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "MVMTM Watch Black",
    brand: null,
    price: 246,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/14.MVMTMWatchBlack.png",
    images: [
      "/assets/images/products/Fashion/Accessories/14.MVMTMWatchBlack.png",
      "/assets/images/products/Fashion/Accessories/14.MVMTMWatchBlack.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "cab6d623-43ef-4cae-9833-3873759e38ac",
    slug: "xarioho-watch-black",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xarioho Watch Black",
    brand: null,
    price: 87,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/15.BarihoWatchBlack.png",
    images: [
      "/assets/images/products/Fashion/Accessories/15.BarihoWatchBlack.png",
      "/assets/images/products/Fashion/Accessories/15.BarihoWatchBlack.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "6bb726b2-d44d-4109-a418-4c230aa79dfa",
    slug: "skmei-watch-black",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Skmei Watch Black",
    brand: null,
    price: 9,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/16.SkmeiWatchBlack.png",
    images: [
      "/assets/images/products/Fashion/Accessories/16.SkmeiWatchBlack.png",
      "/assets/images/products/Fashion/Accessories/16.SkmeiWatchBlack.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "895f06b5-7faa-44da-bd18-04f8cf8f227d",
    slug: "digital-wrist-watch",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Digital Wrist Watch",
    brand: null,
    price: 112,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/17.DigitalWristWatch.png",
    images: [
      "/assets/images/products/Fashion/Accessories/17.DigitalWristWatch.png",
      "/assets/images/products/Fashion/Accessories/17.DigitalWristWatch.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "68e20f18-5786-485a-afa9-e8b03f73d332",
    slug: "dragon-red-wrist-watch",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Dragon Red Wrist Watch",
    brand: null,
    price: 173,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Accessories/18.DragonRedWristWatch.png",
    images: [
      "/assets/images/products/Fashion/Accessories/18.DragonRedWristWatch.png",
      "/assets/images/products/Fashion/Accessories/18.DragonRedWristWatch.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "4908eacc-c1a2-45b2-a4be-6a85dae45354",
    slug: "silver-high-neck-sweater",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "SIlver High Neck Sweater",
    brand: null,
    price: 195,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/1.SilverHighNeckSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/1.SilverHighNeckSweater.png",
      "/assets/images/products/Fashion/Clothes/1.SilverHighNeckSweater.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "f685fcc5-2538-4123-bcde-6651f47c4b8c",
    slug: "blue-blossom-frock",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Blue Blossom Frock",
    brand: null,
    price: 248,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/2.BlueBlossomFrock.png",
    images: [
      "/assets/images/products/Fashion/Clothes/2.BlueBlossomFrock.png",
      "/assets/images/products/Fashion/Clothes/2.BlueBlossomFrock.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "f0f7a496-8b79-4a80-8738-33d29d5d948f",
    slug: "nbl-hoop-hoodie",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "NBL Hoop Hoodie",
    brand: null,
    price: 166,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/3.NBLHoopHoodie.png",
    images: [
      "/assets/images/products/Fashion/Clothes/3.NBLHoopHoodie.png",
      "/assets/images/products/Fashion/Clothes/3.NBLHoopHoodie.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "7bec490e-e8bc-4113-a25b-b25c5a9bb028",
    slug: "denim-blue-jeans",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Denim Blue Jeans",
    brand: null,
    price: 206,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/4.DenimBlueJeans.png",
    images: [
      "/assets/images/products/Fashion/Clothes/4.DenimBlueJeans.png",
      "/assets/images/products/Fashion/Clothes/4.DenimBlueJeans.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "066b6f91-e9ef-4ed3-8337-d83345121a92",
    slug: "brown-check-frock",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Brown Check Frock",
    brand: null,
    price: 42,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/5.BrownCheckFrock.png",
    images: [
      "/assets/images/products/Fashion/Clothes/5.BrownCheckFrock.png",
      "/assets/images/products/Fashion/Clothes/5.BrownCheckFrock.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "8b99148a-feb4-44f5-a741-1e31ba04ee8e",
    slug: "fashion-collection-for-kids",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Fashion Collection for Kids",
    brand: null,
    price: 3,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/6.FashionCollectionforKids.png",
    images: [
      "/assets/images/products/Fashion/Clothes/6.FashionCollectionforKids.png",
      "/assets/images/products/Fashion/Clothes/6.FashionCollectionforKids.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "b39e6122-2d7c-4a33-988e-0d3c7dcc9f20",
    slug: "denim-classic-blue-jeans",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Denim Classic Blue Jeans",
    brand: null,
    price: 19,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/7.DenimClassicBlueJeans.png",
    images: [
      "/assets/images/products/Fashion/Clothes/7.DenimClassicBlueJeans.png",
      "/assets/images/products/Fashion/Clothes/7.DenimClassicBlueJeans.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "e18399d6-519b-48d6-b623-ae9cb0989722",
    slug: "royal-black-suit-pant",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Royal Black Suit-Pant",
    brand: null,
    price: 206,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/8.RoyalBlackSuitPant.png",
    images: [
      "/assets/images/products/Fashion/Clothes/8.RoyalBlackSuitPant.png",
      "/assets/images/products/Fashion/Clothes/8.RoyalBlackSuitPant.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "bd8d8aec-2bb8-479f-8501-64004d20df3d",
    slug: "denim-gabardine-collection",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Denim Gabardine Collection",
    brand: null,
    price: 199,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/9.DenimGabardineCollection.png",
    images: [
      "/assets/images/products/Fashion/Clothes/9.DenimGabardineCollection.png",
      "/assets/images/products/Fashion/Clothes/9.DenimGabardineCollection.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "3cf5a848-f999-463e-9a4d-beb81de24a20",
    slug: "lands-jacket",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Lands Jacket",
    brand: null,
    price: 135,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/10.LandsJacket.png",
    images: [
      "/assets/images/products/Fashion/Clothes/10.LandsJacket.png",
      "/assets/images/products/Fashion/Clothes/10.LandsJacket.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "c3d97e95-6191-4bbf-af47-cd32fe731567",
    slug: "striped-casual-shirt",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Striped Casual Shirt",
    brand: null,
    price: 234,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/11.StripedCasual.png",
    images: [
      "/assets/images/products/Fashion/Clothes/11.StripedCasual.png",
      "/assets/images/products/Fashion/Clothes/11.StripedCasual.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "e0685d71-933d-4cab-9c45-a9ec34e7fde3",
    slug: "pink-kids-wear",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Pink Kids Wear",
    brand: null,
    price: 144,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/12.PinkKidsWear.png",
    images: [
      "/assets/images/products/Fashion/Clothes/12.PinkKidsWear.png",
      "/assets/images/products/Fashion/Clothes/12.PinkKidsWear.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "71e2cd61-be17-4ebc-9192-714c02dd52c4",
    slug: "high-waisted-gabardine",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "High Waisted Gabardine",
    brand: null,
    price: 25,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/13.HighWaistedGabardine.png",
    images: [
      "/assets/images/products/Fashion/Clothes/13.HighWaistedGabardine.png",
      "/assets/images/products/Fashion/Clothes/13.HighWaistedGabardine.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "7dc62117-5ef9-49ea-bc02-455a97ea47ff",
    slug: "blue-trousers",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Blue Trousers",
    brand: null,
    price: 141,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/14.BlueTrousers.png",
    images: [
      "/assets/images/products/Fashion/Clothes/14.BlueTrousers.png",
      "/assets/images/products/Fashion/Clothes/14.BlueTrousers.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "0d0c0023-7ec5-4f66-81bc-01d47be464cd",
    slug: "geen-ski-jacket",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Geen Ski Jacket",
    brand: null,
    price: 197,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/15.GreenSkiJacket.png",
    images: [
      "/assets/images/products/Fashion/Clothes/15.GreenSkiJacket.png",
      "/assets/images/products/Fashion/Clothes/15.GreenSkiJacket.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "023d444a-ace7-4c1f-a0f4-3c7d1d03ad1b",
    slug: "double-wool-overcoat",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Double Wool Overcoat",
    brand: null,
    price: 232,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/16.DoubleWoolOvercoat.png",
    images: [
      "/assets/images/products/Fashion/Clothes/16.DoubleWoolOvercoat.png",
      "/assets/images/products/Fashion/Clothes/16.DoubleWoolOvercoat.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "1584359d-143f-4205-a3d3-29c2948c274f",
    slug: "strech-cargo-pants",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Strech Cargo Pants",
    brand: null,
    price: 65,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/17.StrechCargoPants.png",
    images: [
      "/assets/images/products/Fashion/Clothes/17.StrechCargoPants.png",
      "/assets/images/products/Fashion/Clothes/17.StrechCargoPants.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "e41acf0d-c081-4a05-85bd-edeb1f0ba7b5",
    slug: "wool-overcoat-women",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Wool Overcoat Women",
    brand: null,
    price: 52,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/18.WoolOvercoatWomen.png",
    images: [
      "/assets/images/products/Fashion/Clothes/18.WoolOvercoatWomen.png",
      "/assets/images/products/Fashion/Clothes/18.WoolOvercoatWomen.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "fb0778db-4571-4045-b621-56d36fbf249d",
    slug: "blackandwhite-sweater",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Black&White Sweater",
    brand: null,
    price: 95,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/19.BlackWhiteSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/19.BlackWhiteSweater.png",
      "/assets/images/products/Fashion/Clothes/19.BlackWhiteSweater.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "bf3cabef-c277-4259-a121-9d2c13db8145",
    slug: "gray-overcoat-women",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Gray Overcoat Women",
    brand: null,
    price: 26,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/20.GrayOvercoatWomen.png",
    images: [
      "/assets/images/products/Fashion/Clothes/20.GrayOvercoatWomen.png",
      "/assets/images/products/Fashion/Clothes/20.GrayOvercoatWomen.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "f5ed1889-8faf-42c7-8c21-19313060c897",
    slug: "yellow-casual-sweater",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yellow Casual Sweater",
    brand: null,
    price: 20,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/21.YellowCasualSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/21.YellowCasualSweater.png",
      "/assets/images/products/Fashion/Clothes/21.YellowCasualSweater.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "d8474410-b370-41d6-adc0-edf430955313",
    slug: "casual-gray-pants",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Casual Gray Pants",
    brand: null,
    price: 191,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/22.CasualGrayPants.png",
    images: [
      "/assets/images/products/Fashion/Clothes/22.CasualGrayPants.png",
      "/assets/images/products/Fashion/Clothes/22.CasualGrayPants.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "45a63acf-5cb8-4318-932e-a69035b095de",
    slug: "adidas-winter-jacket",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Adidas Winter Jacket",
    brand: null,
    price: 169,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/23.AdidasWinterJacket.png",
    images: [
      "/assets/images/products/Fashion/Clothes/23.AdidasWinterJacket.png",
      "/assets/images/products/Fashion/Clothes/23.AdidasWinterJacket.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "16506e87-fb8b-408a-9509-2099d463e910",
    slug: "olive-casual-sweater",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Olive Casual Sweater",
    brand: null,
    price: 87,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "888f298c-a619-43da-8a2f-f76a2229afd9",
    slug: "kids-rainbow-sweater",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Kids Rainbow Sweater",
    brand: null,
    price: 227,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/25.KidsRainbowSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/25.KidsRainbowSweater.png",
      "/assets/images/products/Fashion/Clothes/25.KidsRainbowSweater.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "a160a26c-c341-445d-a423-b5666995aad6",
    slug: "heavy-22kt-gold-necklace-set",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Heavy 22kt Gold Necklace Set",
    brand: null,
    price: 128,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/1.Heavy22ktGoldNecklaceSet.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/1.Heavy22ktGoldNecklaceSet.png",
      "/assets/images/products/Fashion/Jewellery/1.Heavy22ktGoldNecklaceSet.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "07ec8986-7fb7-465d-a389-44a1ab9cb3be",
    slug: "black-metal-ring",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Black Metal Ring",
    brand: null,
    price: 141,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Jewellery/2.BlackMetalRing.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/2.BlackMetalRing.png",
      "/assets/images/products/Fashion/Jewellery/2.BlackMetalRing.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "589c8e5b-d738-4340-a43b-824727603536",
    slug: "diamond-silver-ring",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Diamond SIlver Ring",
    brand: null,
    price: 67,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/3.DiamondSilverRing.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/3.DiamondSilverRing.png",
      "/assets/images/products/Fashion/Jewellery/3.DiamondSilverRing.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "5a975359-013c-46b0-ac80-db362b394787",
    slug: "black-stones-necklace",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Black Stones Necklace",
    brand: null,
    price: 31,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/4.BlackStonesNecklace.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/4.BlackStonesNecklace.png",
      "/assets/images/products/Fashion/Jewellery/4.BlackStonesNecklace.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "1072f4e6-8cd7-43d0-85fb-ebac9dda6e03",
    slug: "indian-pearl-earrings",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Indian Pearl Earrings",
    brand: null,
    price: 84,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/5.IndianPearlEarrings.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/5.IndianPearlEarrings.png",
      "/assets/images/products/Fashion/Jewellery/5.IndianPearlEarrings.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "414a6f75-09c7-4b0d-be4e-72152e715716",
    slug: "indian-copper-earrings",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Indian Copper Earrings",
    brand: null,
    price: 90,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/6.IndianCopperEarrings.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/6.IndianCopperEarrings.png",
      "/assets/images/products/Fashion/Jewellery/6.IndianCopperEarrings.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "953b426c-bd41-43da-9993-94a5e3f78465",
    slug: "indian-pearl-square-stone-necklace",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Indian Pearl Square Stone Necklace",
    brand: null,
    price: 69,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/7.IndianPearlSquareStoneNecklace.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/7.IndianPearlSquareStoneNecklace.png",
      "/assets/images/products/Fashion/Jewellery/7.IndianPearlSquareStoneNecklace.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "8636ff67-8373-4f74-9797-8ca45c191853",
    slug: "indian-pearl-thread-earring",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Indian Pearl Thread Earring",
    brand: null,
    price: 224,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/8.IndianPearlThreadEarrings.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/8.IndianPearlThreadEarrings.png",
      "/assets/images/products/Fashion/Jewellery/8.IndianPearlThreadEarrings.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "f36fb001-3afb-4102-bf88-ba32183da83a",
    slug: "heavy-20kt-gold-necklace",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Heavy 20kt Gold Necklace",
    brand: null,
    price: 150,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/9.Heavy20ktGoldNecklace.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/9.Heavy20ktGoldNecklace.png",
      "/assets/images/products/Fashion/Jewellery/9.Heavy20ktGoldNecklace.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "97a5870f-2e74-4da6-8928-8ee88f3adc6f",
    slug: "indian-8kt-gold-bracelet",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Indian 8kt Gold Bracelet",
    brand: null,
    price: 19,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/10.Indian8ktGoldBracelet.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/10.Indian8ktGoldBracelet.png",
      "/assets/images/products/Fashion/Jewellery/10.Indian8ktGoldBracelet.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "92ef7562-5168-4927-bff3-5aba8422cf52",
    slug: "heavy-5kt-gold-earrings",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Heavy 5kt Gold Earrings",
    brand: null,
    price: 174,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/11.Heavy5ktIndianEarrings.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/11.Heavy5ktIndianEarrings.png",
      "/assets/images/products/Fashion/Jewellery/11.Heavy5ktIndianEarrings.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "8b59ca9c-36fb-42a0-ab8c-fe62bcfa9515",
    slug: "blue-stone-locket",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Blue Stone Locket",
    brand: null,
    price: 116,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/12.BlueStoneLocket.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/12.BlueStoneLocket.png",
      "/assets/images/products/Fashion/Jewellery/12.BlueStoneLocket.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "67298605-c8b6-41e6-8673-7f9c195f1339",
    slug: "black-stone-modern-locket",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Black Stone Modern Locket",
    brand: null,
    price: 207,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/13.BlackStoneModernLocket.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/13.BlackStoneModernLocket.png",
      "/assets/images/products/Fashion/Jewellery/13.BlackStoneModernLocket.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "bbe0cbd6-e924-42a6-bde9-eb355e05ae83",
    slug: "diamond-wedding-rings",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Diamond Wedding Rings",
    brand: null,
    price: 75,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/14.DiamondWeddingRings.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/14.DiamondWeddingRings.png",
      "/assets/images/products/Fashion/Jewellery/14.DiamondWeddingRings.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "207d00f8-d98a-46c9-b461-4a3cfc99e3cb",
    slug: "indian-6kt-gold-earrings",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Indian 6kt Gold Earrings",
    brand: null,
    price: 6,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/15.Indian6ktGoldEarrings.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/15.Indian6ktGoldEarrings.png",
      "/assets/images/products/Fashion/Jewellery/15.Indian6ktGoldEarrings.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "31d3a3b2-0fac-4ace-834a-64ddcc741385",
    slug: "custom-made-threads-beads-necklace-set",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Custom Made Threads Beads NEcklace set",
    brand: null,
    price: 45,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/16.CustomMadeThreadsBeadsNecklaceSet.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/16.CustomMadeThreadsBeadsNecklaceSet.png",
      "/assets/images/products/Fashion/Jewellery/16.CustomMadeThreadsBeadsNecklaceSet.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "655f45d0-3e9d-400f-8952-af0fe339f9ab",
    slug: "heavy-stone-colorful-necklace",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Heavy Stone Colorful Necklace",
    brand: null,
    price: 94,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/17.HeavyStoneColorfulNecklace.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/17.HeavyStoneColorfulNecklace.png",
      "/assets/images/products/Fashion/Jewellery/17.HeavyStoneColorfulNecklace.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "c0a034b8-4705-4198-837e-0fce082dfd6f",
    slug: "beads-and-threads-simple-earrings",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Beads and Threads Simple Earrings",
    brand: null,
    price: 225,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/18.BeadsandThreadsSimpleEarrings.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/18.BeadsandThreadsSimpleEarrings.png",
      "/assets/images/products/Fashion/Jewellery/18.BeadsandThreadsSimpleEarrings.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "291521cf-6c3d-481b-9e0e-61f62219f5fb",
    slug: "tamil-wedding-necklace-set",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tamil Wedding Necklace Set",
    brand: null,
    price: 147,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/19.TamilWeddingNecklaceSet.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/19.TamilWeddingNecklaceSet.png",
      "/assets/images/products/Fashion/Jewellery/19.TamilWeddingNecklaceSet.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "92eaa1fc-b0e7-4079-83ac-ecee825b2409",
    slug: "shahi-21kt-gold-necklace-set",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Shahi 21kt Gold Necklace Set",
    brand: null,
    price: 177,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/20.Shahi21ktGoldNecklaceSet.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/20.Shahi21ktGoldNecklaceSet.png",
      "/assets/images/products/Fashion/Jewellery/20.Shahi21ktGoldNecklaceSet.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "2d084fae-7faa-4958-9fa7-15c27e096c0d",
    slug: "feathers-and-beads-bohemian-necklace",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Feathers and Beads Bohemian Necklace",
    brand: null,
    price: 58,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/21.FeathersandBeadsBohemianNecklace.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/21.FeathersandBeadsBohemianNecklace.png",
      "/assets/images/products/Fashion/Jewellery/21.FeathersandBeadsBohemianNecklace.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "6fb30762-fc2f-4fa9-ab94-bae8972bb257",
    slug: "red-peacock-tail-earrings",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Red Peacock Tail Earrings",
    brand: null,
    price: 139,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/22.RedPeacockTailEarrings.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/22.RedPeacockTailEarrings.png",
      "/assets/images/products/Fashion/Jewellery/22.RedPeacockTailEarrings.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "f78a699f-338e-4587-b4e1-0f8a18e732cf",
    slug: "heavy-13kt-gold-wedding-necklace-set",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "heavy 13kt Gold Wedding Necklace Set",
    brand: null,
    price: 12,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Jewellery/23.Heavy13ktGoldWeddingNecklaceSet.png",
    images: [
      "/assets/images/products/Fashion/Jewellery/23.Heavy13ktGoldWeddingNecklaceSet.png",
      "/assets/images/products/Fashion/Jewellery/23.Heavy13ktGoldWeddingNecklaceSet.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "08a59af1-f485-4bee-ad4b-46f54a8c994f",
    slug: "yike-red",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Red",
    brand: null,
    price: 24,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/1.NikeRed.png",
    images: [
      "/assets/images/products/Fashion/Shoes/1.NikeRed.png",
      "/assets/images/products/Fashion/Shoes/1.NikeRed.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "40d799a1-e558-4242-9f7f-43d61d261bf8",
    slug: "north-star-cream",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "North Star Cream",
    brand: null,
    price: 154,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/2.NorthStarCream.png",
    images: [
      "/assets/images/products/Fashion/Shoes/2.NorthStarCream.png",
      "/assets/images/products/Fashion/Shoes/2.NorthStarCream.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "5e637474-f17d-4ee4-8666-c16b4701f7e0",
    slug: "puma-black",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Puma Black",
    brand: null,
    price: 133,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/3.PumaBlack.png",
    images: [
      "/assets/images/products/Fashion/Shoes/3.PumaBlack.png",
      "/assets/images/products/Fashion/Shoes/3.PumaBlack.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "80af7e3d-832b-4ed9-8878-927cb27565fe",
    slug: "yike-fluffy-yellow",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Fluffy Yellow",
    brand: null,
    price: 196,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/4.NikeFluffyYellow.png",
    images: [
      "/assets/images/products/Fashion/Shoes/4.NikeFluffyYellow.png",
      "/assets/images/products/Fashion/Shoes/4.NikeFluffyYellow.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "b6a313d3-5385-46db-9665-b7097980a6ec",
    slug: "yike-blue",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Blue",
    brand: null,
    price: 22,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/5.NikeBlue.png",
    images: [
      "/assets/images/products/Fashion/Shoes/5.NikeBlue.png",
      "/assets/images/products/Fashion/Shoes/5.NikeBlue.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "31cbaeb2-8c21-483d-8f87-b0f2ee5e96b2",
    slug: "north-star-black",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "North Star Black",
    brand: null,
    price: 50,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/6.NorthStarBlack.png",
    images: [
      "/assets/images/products/Fashion/Shoes/6.NorthStarBlack.png",
      "/assets/images/products/Fashion/Shoes/6.NorthStarBlack.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "8381d0e2-038c-4f1d-a03f-650b4c1f7220",
    slug: "nike-green",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "NIke Green",
    brand: null,
    price: 49,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/7.NikeGreen.png",
    images: [
      "/assets/images/products/Fashion/Shoes/7.NikeGreen.png",
      "/assets/images/products/Fashion/Shoes/7.NikeGreen.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "8ebf2ee5-430d-41a6-a532-f54579f089e9",
    slug: "yike-sill",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Sill",
    brand: null,
    price: 22,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/8.NikeSkill.png",
    images: [
      "/assets/images/products/Fashion/Shoes/8.NikeSkill.png",
      "/assets/images/products/Fashion/Shoes/8.NikeSkill.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "fd441667-5290-4b69-8b32-0a25b71e41fe",
    slug: "adidas-green",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Adidas Green",
    brand: null,
    price: 86,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/9.AdidasGreen.png",
    images: [
      "/assets/images/products/Fashion/Shoes/9.AdidasGreen.png",
      "/assets/images/products/Fashion/Shoes/9.AdidasGreen.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "bfe64066-7629-45ba-9ab6-2b8b0bb33d72",
    slug: "adidas-white",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Adidas White",
    brand: null,
    price: 31,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/10.AdidasWhite.png",
    images: [
      "/assets/images/products/Fashion/Shoes/10.AdidasWhite.png",
      "/assets/images/products/Fashion/Shoes/10.AdidasWhite.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "01487cab-0926-442a-9285-77bb35354654",
    slug: "flow-white",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Flow White",
    brand: null,
    price: 34,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/11.Flowwhite.png",
    images: [
      "/assets/images/products/Fashion/Shoes/11.Flowwhite.png",
      "/assets/images/products/Fashion/Shoes/11.Flowwhite.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "acf275c1-42b5-49f4-a63d-8d8ce0c0da3f",
    slug: "yike-air-white",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Air White",
    brand: null,
    price: 166,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/12.NikeAirWhite.png",
    images: [
      "/assets/images/products/Fashion/Shoes/12.NikeAirWhite.png",
      "/assets/images/products/Fashion/Shoes/12.NikeAirWhite.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "0b454361-03e8-4ca1-851a-3011f07d8bfc",
    slug: "puma-red",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Puma Red",
    brand: null,
    price: 2,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/13.PumaRed.png",
    images: [
      "/assets/images/products/Fashion/Shoes/13.PumaRed.png",
      "/assets/images/products/Fashion/Shoes/13.PumaRed.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "c17f5c03-9d43-45f1-83df-fe0f3948323b",
    slug: "yike-pink",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Pink",
    brand: null,
    price: 214,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/14.NikePink.png",
    images: [
      "/assets/images/products/Fashion/Shoes/14.NikePink.png",
      "/assets/images/products/Fashion/Shoes/14.NikePink.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "59abc7a9-20d0-4c1e-a3a1-666d3a195856",
    slug: "yike-mint",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Mint",
    brand: null,
    price: 89,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/15.NikeMint.png",
    images: [
      "/assets/images/products/Fashion/Shoes/15.NikeMint.png",
      "/assets/images/products/Fashion/Shoes/15.NikeMint.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "67385dbe-5558-428e-9d9e-6af815bc3837",
    slug: "yike-silver",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Silver",
    brand: null,
    price: 124,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/16.NikeSilver.png",
    images: [
      "/assets/images/products/Fashion/Shoes/16.NikeSilver.png",
      "/assets/images/products/Fashion/Shoes/16.NikeSilver.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "2177169d-0bf1-4712-8cff-be77ac739b4b",
    slug: "north-star-olive",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "North Star Olive",
    brand: null,
    price: 200,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/17.NorthStarOlive.png",
    images: [
      "/assets/images/products/Fashion/Shoes/17.NorthStarOlive.png",
      "/assets/images/products/Fashion/Shoes/17.NorthStarOlive.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "8d3a51d1-be2a-46e2-8c8c-1eed2aa41b2c",
    slug: "adidas-rainbow",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Adidas Rainbow",
    brand: null,
    price: 149,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/18.AdidasRainbow.png",
    images: [
      "/assets/images/products/Fashion/Shoes/18.AdidasRainbow.png",
      "/assets/images/products/Fashion/Shoes/18.AdidasRainbow.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "2839eeae-dbbf-4e2f-98bf-266e8896f777",
    slug: "yike-yellow-black",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Yellow black",
    brand: null,
    price: 171,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/19.NikeYellowBlack.png",
    images: [
      "/assets/images/products/Fashion/Shoes/19.NikeYellowBlack.png",
      "/assets/images/products/Fashion/Shoes/19.NikeYellowBlack.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "f9e25be8-3e26-4c44-a417-8216e946859d",
    slug: "merrell-woods",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Merrell Woods",
    brand: null,
    price: 171,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/20.MerrellWoods.png",
    images: [
      "/assets/images/products/Fashion/Shoes/20.MerrellWoods.png",
      "/assets/images/products/Fashion/Shoes/20.MerrellWoods.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "c1143161-fe6e-4204-9276-aba73dd784b0",
    slug: "north-star-blue",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "North Star Blue",
    brand: null,
    price: 80,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/21.NorthStarBlue.png",
    images: [
      "/assets/images/products/Fashion/Shoes/21.NorthStarBlue.png",
      "/assets/images/products/Fashion/Shoes/21.NorthStarBlue.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "3c71038c-79dd-445e-8a17-7f62e53c7e31",
    slug: "yike-black-red",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Black Red",
    brand: null,
    price: 144,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/22.NikeBlackRed.png",
    images: [
      "/assets/images/products/Fashion/Shoes/22.NikeBlackRed.png",
      "/assets/images/products/Fashion/Shoes/22.NikeBlackRed.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "940fa36c-3650-4eef-a1f3-246950c47d51",
    slug: "yike-silver-race",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yike Silver Race",
    brand: null,
    price: 224,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/23.NikeSilverRace.png",
    images: [
      "/assets/images/products/Fashion/Shoes/23.NikeSilverRace.png",
      "/assets/images/products/Fashion/Shoes/23.NikeSilverRace.png",
    ],
    categories: ["fashion"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "4d829866-7a72-4329-89f6-95a54afbe1eb",
    slug: "sakti-sambar-powder",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Sakti Sambar Powder",
    brand: null,
    price: 121,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/1.SaktiSambarPowder.png",
    images: [
      "/assets/images/products/Groceries/1.SaktiSambarPowder.png",
      "/assets/images/products/Groceries/1.SaktiSambarPowder.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "1f5ed30a-19b2-49ce-ab78-a56adb0d0944",
    slug: "premium-grocery-collection",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Premium Grocery Collection",
    brand: null,
    price: 173,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Groceries/2.PremiumGroceryCollection.png",
    images: [
      "/assets/images/products/Groceries/2.PremiumGroceryCollection.png",
      "/assets/images/products/Groceries/2.PremiumGroceryCollection.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "32069a36-8b1d-4feb-8740-44131b024bb9",
    slug: "premium-grocery-pack",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Premium Grocery Pack",
    brand: null,
    price: 203,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/3.PremiumGroceryPack.png",
    images: [
      "/assets/images/products/Groceries/3.PremiumGroceryPack.png",
      "/assets/images/products/Groceries/3.PremiumGroceryPack.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "fcdb3df0-a952-4e13-b777-204c43c1f728",
    slug: "freshandreal-chole-masala",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Fresh&Real CHole Masala",
    brand: null,
    price: 87,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/4.FreashRealCholeMasala.png",
    images: [
      "/assets/images/products/Groceries/4.FreashRealCholeMasala.png",
      "/assets/images/products/Groceries/4.FreashRealCholeMasala.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "61294493-f0f3-44ec-bd52-1dcabc680e1e",
    slug: "gum-pack",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Gum Pack",
    brand: null,
    price: 7,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/5.GumPack.png",
    images: [
      "/assets/images/products/Groceries/5.GumPack.png",
      "/assets/images/products/Groceries/5.GumPack.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "9cd87210-ae38-445a-85a0-fc265bc65951",
    slug: "indian-grocery-products-pack",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Indian Grocery Products Pack",
    brand: null,
    price: 218,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Groceries/6.IndianGroceryProductsPack.png",
    images: [
      "/assets/images/products/Groceries/6.IndianGroceryProductsPack.png",
      "/assets/images/products/Groceries/6.IndianGroceryProductsPack.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "a2943d99-4422-4e73-8007-c3269594a468",
    slug: "saffola-gold-oil",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Saffola Gold Oil",
    brand: null,
    price: 117,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/7.SaffolaGoldOil.png",
    images: [
      "/assets/images/products/Groceries/7.SaffolaGoldOil.png",
      "/assets/images/products/Groceries/7.SaffolaGoldOil.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "22f09587-0cde-4266-a794-5b435edb6bf7",
    slug: "colgate-advance-protection-toothpaste",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Colgate Advance Protection Toothpaste",
    brand: null,
    price: 87,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Groceries/8.ColgateAdvanceProtectionToothpaste.png",
    images: [
      "/assets/images/products/Groceries/8.ColgateAdvanceProtectionToothpaste.png",
      "/assets/images/products/Groceries/8.ColgateAdvanceProtectionToothpaste.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "4304b716-0482-4a97-8c82-383146010fe4",
    slug: "catch-sprinklers-chat-masala",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Catch Sprinklers Chat Masala",
    brand: null,
    price: 97,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Groceries/9.CatchSprinklersChatMasala.png",
    images: [
      "/assets/images/products/Groceries/9.CatchSprinklersChatMasala.png",
      "/assets/images/products/Groceries/9.CatchSprinklersChatMasala.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "2bc07b7b-5b76-4a09-bb85-eb8688e45b3e",
    slug: "catch-italian-seasoning-grinder",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Catch Italian Seasoning Grinder",
    brand: null,
    price: 30,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Groceries/10.CatchItalianSeasoningGrinder.png",
    images: [
      "/assets/images/products/Groceries/10.CatchItalianSeasoningGrinder.png",
      "/assets/images/products/Groceries/10.CatchItalianSeasoningGrinder.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "67b0cc65-8e44-4e20-895e-b3c28080ab45",
    slug: "tadka-garam-masala",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tadka Garam Masala",
    brand: null,
    price: 109,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/11.TadkaGaramMasala.png",
    images: [
      "/assets/images/products/Groceries/11.TadkaGaramMasala.png",
      "/assets/images/products/Groceries/11.TadkaGaramMasala.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "fedd7b01-9363-4a35-a46b-1097c6560f4c",
    slug: "thanks-a-latte-coffee",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Thanks a Latte Coffee",
    brand: null,
    price: 69,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/12.ThanksaLatteCoffee.png",
    images: [
      "/assets/images/products/Groceries/12.ThanksaLatteCoffee.png",
      "/assets/images/products/Groceries/12.ThanksaLatteCoffee.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "36ead338-9594-4ed0-957f-6c35cb9a9da9",
    slug: "grocery-package-offer",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Grocery Package Offer",
    brand: null,
    price: 246,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/13.GroceryPackageOffer.png",
    images: [
      "/assets/images/products/Groceries/13.GroceryPackageOffer.png",
      "/assets/images/products/Groceries/13.GroceryPackageOffer.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "fc9ed840-07e7-4d63-8f30-7c1c2dc8bc8f",
    slug: "aci-products",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "ACI Products",
    brand: null,
    price: 210,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/14.ACIProducts.png",
    images: [
      "/assets/images/products/Groceries/14.ACIProducts.png",
      "/assets/images/products/Groceries/14.ACIProducts.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "73bdfe94-685c-4d28-b6bc-9f445c78603c",
    slug: "lightskin-soap",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "LIghtskin Soap",
    brand: null,
    price: 80,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/15.LightskinSoap.png",
    images: [
      "/assets/images/products/Groceries/15.LightskinSoap.png",
      "/assets/images/products/Groceries/15.LightskinSoap.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "36cb75d3-4795-47bb-ae7d-6eb3cc462c5a",
    slug: "artidoro-rodriguez-coffee",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Artidoro Rodriguez Coffee",
    brand: null,
    price: 183,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Groceries/16.ArtidoroRodriguezCoffee.png",
    images: [
      "/assets/images/products/Groceries/16.ArtidoroRodriguezCoffee.png",
      "/assets/images/products/Groceries/16.ArtidoroRodriguezCoffee.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "55e8ea58-01f6-4b28-a1c4-5c1cf55c4114",
    slug: "dove-sensitive-skin-soap",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Dove Sensitive Skin Soap",
    brand: null,
    price: 178,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/17.DoveSensitiveSkinSoap.png",
    images: [
      "/assets/images/products/Groceries/17.DoveSensitiveSkinSoap.png",
      "/assets/images/products/Groceries/17.DoveSensitiveSkinSoap.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "4be6d7bf-a6f7-4d93-b1e2-54a38d4b4cb8",
    slug: "aniket-garam-masala",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Aniket Garam Masala",
    brand: null,
    price: 182,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/18.AniketGaramMasala.png",
    images: [
      "/assets/images/products/Groceries/18.AniketGaramMasala.png",
      "/assets/images/products/Groceries/18.AniketGaramMasala.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "2c25ecb6-6fe0-4b3f-8137-8685064d664f",
    slug: "portsmouth-soap",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Portsmouth Soap",
    brand: null,
    price: 104,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/19.PortsmouthSoaps.png",
    images: [
      "/assets/images/products/Groceries/19.PortsmouthSoaps.png",
      "/assets/images/products/Groceries/19.PortsmouthSoaps.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "0df67c72-5820-43df-b36c-963a7d782eca",
    slug: "premium-kitchen-pack",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Premium Kitchen Pack",
    brand: null,
    price: 107,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/20.PremiumKitchenPack.png",
    images: [
      "/assets/images/products/Groceries/20.PremiumKitchenPack.png",
      "/assets/images/products/Groceries/20.PremiumKitchenPack.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "7293c750-d8d6-4c35-85b4-bf61c1f64aa2",
    slug: "mega-ssardines",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mega Ssardines",
    brand: null,
    price: 18,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/21.MegaSardines.png",
    images: [
      "/assets/images/products/Groceries/21.MegaSardines.png",
      "/assets/images/products/Groceries/21.MegaSardines.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "5e74dfec-b659-4878-9598-3abdbbcb8357",
    slug: "organic-company-products",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Organic Company Products",
    brand: null,
    price: 53,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Groceries/22.OrganicCompanyProducts.png",
    images: [
      "/assets/images/products/Groceries/22.OrganicCompanyProducts.png",
      "/assets/images/products/Groceries/22.OrganicCompanyProducts.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "5fda2d2c-6cf7-4470-b1b2-a48d78900893",
    slug: "earnest-sardines",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Earnest Sardines",
    brand: null,
    price: 74,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/23.EarnestSardines.png",
    images: [
      "/assets/images/products/Groceries/23.EarnestSardines.png",
      "/assets/images/products/Groceries/23.EarnestSardines.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "f896ea28-34cd-4cf4-9600-e51771856c5a",
    slug: "parachute-coconut-oil",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Parachute Coconut Oil",
    brand: null,
    price: 137,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/24.ParachuteCoconutOil.png",
    images: [
      "/assets/images/products/Groceries/24.ParachuteCoconutOil.png",
      "/assets/images/products/Groceries/24.ParachuteCoconutOil.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "270034de-bcdf-4e4e-8662-cbfbc74b5d7c",
    slug: "cafe-torrefaction-fraiche",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Cafe Torrefaction Fraiche",
    brand: null,
    price: 74,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Groceries/25.CafeTorrefactionFraiche.png",
    images: [
      "/assets/images/products/Groceries/25.CafeTorrefactionFraiche.png",
      "/assets/images/products/Groceries/25.CafeTorrefactionFraiche.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "1c2a5fe9-e61a-4a23-a2da-2c248f0e7738",
    slug: "madina-soaps",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Madina Soaps",
    brand: null,
    price: 111,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/26.MadinaSoaps.png",
    images: [
      "/assets/images/products/Groceries/26.MadinaSoaps.png",
      "/assets/images/products/Groceries/26.MadinaSoaps.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "861cd9d9-aace-4d50-ae7d-070b91b6f7b1",
    slug: "sardines-pack",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Sardines Pack",
    brand: null,
    price: 60,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/27.SardinesPack.png",
    images: [
      "/assets/images/products/Groceries/27.SardinesPack.png",
      "/assets/images/products/Groceries/27.SardinesPack.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "0a166408-359d-4d86-b0e0-55beb4973650",
    slug: "turmeric-powder",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Turmeric Powder",
    brand: null,
    price: 128,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Groceries/28.TurmericPowder.png",
    images: [
      "/assets/images/products/Groceries/28.TurmericPowder.png",
      "/assets/images/products/Groceries/28.TurmericPowder.png",
    ],
    categories: ["groceries"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "283b8582-c199-4b8d-aa4f-577c724aa7b9",
    slug: "biossance-oil",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Biossance Oil",
    brand: null,
    price: 99,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/1.BiossanceOil.png",
    images: [
      "/assets/images/products/Health&Beauty/1.BiossanceOil.png",
      "/assets/images/products/Health&Beauty/1.BiossanceOil.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "d809be6a-c199-499e-b288-cc3ab3fa22e0",
    slug: "amorpacific-products",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Amorpacific Products",
    brand: null,
    price: 219,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Health&Beauty/2.AmorpacificProducts.png",
    images: [
      "/assets/images/products/Health&Beauty/2.AmorpacificProducts.png",
      "/assets/images/products/Health&Beauty/2.AmorpacificProducts.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "fc29e830-ccf0-4717-b6ab-6bd5d058a17d",
    slug: "covergirl-vitalist",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Covergirl Vitalist",
    brand: null,
    price: 189,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/3.CovergirlVitalist.png",
    images: [
      "/assets/images/products/Health&Beauty/3.CovergirlVitalist.png",
      "/assets/images/products/Health&Beauty/3.CovergirlVitalist.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "3dab5e61-0500-4781-9109-f9be5896a391",
    slug: "dr.alka's-hair-shine",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Dr.Alka's Hair Shine",
    brand: null,
    price: 54,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/4.Dr.Alka'sHairShine.png",
    images: [
      "/assets/images/products/Health&Beauty/4.Dr.Alka'sHairShine.png",
      "/assets/images/products/Health&Beauty/4.Dr.Alka'sHairShine.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "b953e582-bfae-4255-8eb9-ec025fcdfc15",
    slug: "huda-beauty-nailpolish",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Huda Beauty Nailpolish",
    brand: null,
    price: 104,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Health&Beauty/5.HudaBeautyNailpolish.png",
    images: [
      "/assets/images/products/Health&Beauty/5.HudaBeautyNailpolish.png",
      "/assets/images/products/Health&Beauty/5.HudaBeautyNailpolish.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "2750a175-ceb3-4c91-baf1-fdf4770ed0b3",
    slug: "skin-glow-oil",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Skin Glow Oil",
    brand: null,
    price: 249,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/6.SKinGlowOil.png",
    images: [
      "/assets/images/products/Health&Beauty/6.SKinGlowOil.png",
      "/assets/images/products/Health&Beauty/6.SKinGlowOil.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "e12d8a4d-9464-4a0e-a22e-81f1eaaf6090",
    slug: "venus-makeup-product",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Venus Makeup Product",
    brand: null,
    price: 163,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/7.VenusMakeupProduct.png",
    images: [
      "/assets/images/products/Health&Beauty/7.VenusMakeupProduct.png",
      "/assets/images/products/Health&Beauty/7.VenusMakeupProduct.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "0976845e-87a8-41e8-92c9-580cf83a9d45",
    slug: "volition-products",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Volition Products",
    brand: null,
    price: 189,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/8.VolitionProducts.png",
    images: [
      "/assets/images/products/Health&Beauty/8.VolitionProducts.png",
      "/assets/images/products/Health&Beauty/8.VolitionProducts.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "df5583b2-bedb-48c0-9f27-ca6bffd0dada",
    slug: "aniise-cream",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Aniise Cream",
    brand: null,
    price: 228,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/9.AniiseCream.png",
    images: [
      "/assets/images/products/Health&Beauty/9.AniiseCream.png",
      "/assets/images/products/Health&Beauty/9.AniiseCream.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "69ca1d45-c382-452c-9eca-b32ec41df518",
    slug: "mykirei-hand-wash",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mykirei Hand Wash",
    brand: null,
    price: 167,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/10.MykireihandWash.png",
    images: [
      "/assets/images/products/Health&Beauty/10.MykireihandWash.png",
      "/assets/images/products/Health&Beauty/10.MykireihandWash.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "f5d5933e-e67a-4236-8ca8-f3e1a5b5113c",
    slug: "rahua-classic-conditioner",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Rahua Classic Conditioner",
    brand: null,
    price: 242,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Health&Beauty/11.RahuaClassicConditioner.png",
    images: [
      "/assets/images/products/Health&Beauty/11.RahuaClassicConditioner.png",
      "/assets/images/products/Health&Beauty/11.RahuaClassicConditioner.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "9066190c-7afb-4752-8861-a50e4959032c",
    slug: "beauty-society-anti-acne-mask",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Beauty Society anti acne Mask",
    brand: null,
    price: 20,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Health&Beauty/12.BeautySocietyantiacnemask.png",
    images: [
      "/assets/images/products/Health&Beauty/12.BeautySocietyantiacnemask.png",
      "/assets/images/products/Health&Beauty/12.BeautySocietyantiacnemask.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "f325e249-cd06-4044-aca7-b0b911d826fa",
    slug: "mistral-lipstick",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mistral Lipstick",
    brand: null,
    price: 4,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/13.MistralLipstick.png",
    images: [
      "/assets/images/products/Health&Beauty/13.MistralLipstick.png",
      "/assets/images/products/Health&Beauty/13.MistralLipstick.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "50a33d0a-bb39-48ec-b179-77053d3d450c",
    slug: "kylie-skin-face-wash",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Kylie Skin Face Wash",
    brand: null,
    price: 65,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/14.KylieskinFaceWash.png",
    images: [
      "/assets/images/products/Health&Beauty/14.KylieskinFaceWash.png",
      "/assets/images/products/Health&Beauty/14.KylieskinFaceWash.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "4d1708a6-3463-4039-aef5-1d6f5f88a5ad",
    slug: "amala-products",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Amala Products",
    brand: null,
    price: 3,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/15.AmalaProducts.png",
    images: [
      "/assets/images/products/Health&Beauty/15.AmalaProducts.png",
      "/assets/images/products/Health&Beauty/15.AmalaProducts.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "c3882b29-b7d3-4e41-a354-42b77568cca8",
    slug: "caudalie-hand-and-nail-cream",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Caudalie Hand and Nail Cream",
    brand: null,
    price: 114,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Health&Beauty/16.CaudalieHandandNailCream.png",
    images: [
      "/assets/images/products/Health&Beauty/16.CaudalieHandandNailCream.png",
      "/assets/images/products/Health&Beauty/16.CaudalieHandandNailCream.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "9c1cf55e-9d45-46b5-8672-f5d723564ece",
    slug: "alkemie-glow-up",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Alkemie Glow Up",
    brand: null,
    price: 108,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/17.AlkemieGlowUp.png",
    images: [
      "/assets/images/products/Health&Beauty/17.AlkemieGlowUp.png",
      "/assets/images/products/Health&Beauty/17.AlkemieGlowUp.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "43961c84-53d2-4deb-9d3c-d26a8dc82339",
    slug: "shiseido-products",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Shiseido Products",
    brand: null,
    price: 227,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/18.ShiseidoProducts.png",
    images: [
      "/assets/images/products/Health&Beauty/18.ShiseidoProducts.png",
      "/assets/images/products/Health&Beauty/18.ShiseidoProducts.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "f740177f-41ac-4d0d-b5e0-65c9fb595941",
    slug: "premium-lipstick",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Premium Lipstick",
    brand: null,
    price: 106,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/19.PremiumLipstick.png",
    images: [
      "/assets/images/products/Health&Beauty/19.PremiumLipstick.png",
      "/assets/images/products/Health&Beauty/19.PremiumLipstick.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "ebea33e3-d652-478a-bc21-b1667fb6be9c",
    slug: "blemish-elixir",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Blemish Elixir",
    brand: null,
    price: 51,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/20.BlemishElixir.png",
    images: [
      "/assets/images/products/Health&Beauty/20.BlemishElixir.png",
      "/assets/images/products/Health&Beauty/20.BlemishElixir.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "f16e449e-a73c-4961-bdb2-909aeaf807ec",
    slug: "red-serum-and-cream",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Red Serum and Cream",
    brand: null,
    price: 48,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/21.RedSerumandCream.png",
    images: [
      "/assets/images/products/Health&Beauty/21.RedSerumandCream.png",
      "/assets/images/products/Health&Beauty/21.RedSerumandCream.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "46fe7014-1813-4f3c-b6ca-c692309409a7",
    slug: "detol-liquid-hand-wash",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Detol Liquid Hand Wash",
    brand: null,
    price: 38,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Health&Beauty/22.DetolLiquidHandWash.png",
    images: [
      "/assets/images/products/Health&Beauty/22.DetolLiquidHandWash.png",
      "/assets/images/products/Health&Beauty/22.DetolLiquidHandWash.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "625c345f-00a1-4bb0-9fd1-ebdb692314dc",
    slug: "dove-face-wash",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Dove Face Wash",
    brand: null,
    price: 240,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Health&Beauty/23.DoveFaceWash.png",
    images: [
      "/assets/images/products/Health&Beauty/23.DoveFaceWash.png",
      "/assets/images/products/Health&Beauty/23.DoveFaceWash.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "97ea71ed-d411-4810-8dc3-06d5bd76ef46",
    slug: "koreal-kids-extra-gentle-shampoo",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Koreal Kids Extra Gentle Shampoo",
    brand: null,
    price: 101,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Health&Beauty/24.LorealKidsExtraGentleShampoo.png",
    images: [
      "/assets/images/products/Health&Beauty/24.LorealKidsExtraGentleShampoo.png",
      "/assets/images/products/Health&Beauty/24.LorealKidsExtraGentleShampoo.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "9357c394-2f5e-4bd1-b983-43cacbecf425",
    slug: "jario-badescu-skin-care-shampoo",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Jario Badescu Skin Care Shampoo",
    brand: null,
    price: 225,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Health&Beauty/25.MarioBadescuSkinCareShampoo.png",
    images: [
      "/assets/images/products/Health&Beauty/25.MarioBadescuSkinCareShampoo.png",
      "/assets/images/products/Health&Beauty/25.MarioBadescuSkinCareShampoo.png",
    ],
    categories: ["health&beauty"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "af693f77-dea5-41ce-adf9-4cf495e087c7",
    slug: "the-poinsettia-plant",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "The Poinsettia Plant",
    brand: null,
    price: 208,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Home&Garden/1.ThePoinsettiaPlant.png",
    images: [
      "/assets/images/products/Home&Garden/1.ThePoinsettiaPlant.png",
      "/assets/images/products/Home&Garden/1.ThePoinsettiaPlant.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "b10f9725-dd9a-4ae1-a088-868aaaba94a7",
    slug: "devil's-ivy-indoor-plant",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Devil's Ivy Indoor Plant",
    brand: null,
    price: 137,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/2.Devil'sIvyIndoorPlant.png",
    images: [
      "/assets/images/products/Home&Garden/2.Devil'sIvyIndoorPlant.png",
      "/assets/images/products/Home&Garden/2.Devil'sIvyIndoorPlant.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "833000ad-f0b7-43d4-8555-be72998f179a",
    slug: "aloe-vera-plant",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Aloe Vera Plant",
    brand: null,
    price: 99,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Home&Garden/3.AloeVeraPlant.png",
    images: [
      "/assets/images/products/Home&Garden/3.AloeVeraPlant.png",
      "/assets/images/products/Home&Garden/3.AloeVeraPlant.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "675c5234-1410-4ede-8011-05dfd4ba8387",
    slug: "satin-pothos-plant",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Satin Pothos Plant",
    brand: null,
    price: 104,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Home&Garden/4.SatinPothosPlant.png",
    images: [
      "/assets/images/products/Home&Garden/4.SatinPothosPlant.png",
      "/assets/images/products/Home&Garden/4.SatinPothosPlant.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "fd7d799d-0d92-4ddf-8718-af1a9a3793e5",
    slug: "japanese-maple-plant",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Japanese Maple Plant",
    brand: null,
    price: 46,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Home&Garden/5.JapaneseMaplePlant.png",
    images: [
      "/assets/images/products/Home&Garden/5.JapaneseMaplePlant.png",
      "/assets/images/products/Home&Garden/5.JapaneseMaplePlant.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "9ccd1217-a3f0-4277-bca9-f0efaf1c37ff",
    slug: "green-leaf-plant-in-glass-jar",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Green leaf Plant in Glass Jar",
    brand: null,
    price: 59,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/6.GreenLeafPlantinGlassJar.png",
    images: [
      "/assets/images/products/Home&Garden/6.GreenLeafPlantinGlassJar.png",
      "/assets/images/products/Home&Garden/6.GreenLeafPlantinGlassJar.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "b7564d62-3fcb-4ae0-9a75-43e6f95b117d",
    slug: "spider-plant-in-round-glass",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Spider Plant in Round Glass",
    brand: null,
    price: 18,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/7.SpiderPlantinRoundGlass.png",
    images: [
      "/assets/images/products/Home&Garden/7.SpiderPlantinRoundGlass.png",
      "/assets/images/products/Home&Garden/7.SpiderPlantinRoundGlass.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "97bdc884-c791-466d-8716-cef76518b2d5",
    slug: "chamaedorea-elegance-plants-with-ceramic-pot",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Chamaedorea Elegance Plants With Ceramic Pot",
    brand: null,
    price: 115,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/8.ChamaedoreaElegansPlantswithCeramicPot.png",
    images: [
      "/assets/images/products/Home&Garden/8.ChamaedoreaElegansPlantswithCeramicPot.png",
      "/assets/images/products/Home&Garden/8.ChamaedoreaElegansPlantswithCeramicPot.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "63d9b5ae-8bb4-413e-bfdb-4308cf8247be",
    slug: "houseplants-in-jpapanese-pot",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Houseplants in Jpapanese Pot",
    brand: null,
    price: 182,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/9.HouseplantinJapanesePot.png",
    images: [
      "/assets/images/products/Home&Garden/9.HouseplantinJapanesePot.png",
      "/assets/images/products/Home&Garden/9.HouseplantinJapanesePot.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "e0fa176a-9f7e-44aa-b7fe-3745dc6ac66f",
    slug: "fiddle-leaf-plant-in-bamboo-basket",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Fiddle-Leaf Plant in Bamboo Basket",
    brand: null,
    price: 202,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/10.FiddleleafPlantinBambooBasket.png",
    images: [
      "/assets/images/products/Home&Garden/10.FiddleleafPlantinBambooBasket.png",
      "/assets/images/products/Home&Garden/10.FiddleleafPlantinBambooBasket.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "a2e0368f-406f-45ba-90fb-6e44653f37b7",
    slug: "beautiful-cactus-in-ceramic-cup",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Beautiful Cactus in Ceramic Cup",
    brand: null,
    price: 165,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/11.BeautifulCactusinCeramicCup.png",
    images: [
      "/assets/images/products/Home&Garden/11.BeautifulCactusinCeramicCup.png",
      "/assets/images/products/Home&Garden/11.BeautifulCactusinCeramicCup.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "da896706-ccca-4310-b75c-3b12f83e8205",
    slug: "cactus-in-japanese-ceramic",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Cactus in Japanese Ceramic",
    brand: null,
    price: 2,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/12.CactusinJapaneseCeramic.png",
    images: [
      "/assets/images/products/Home&Garden/12.CactusinJapaneseCeramic.png",
      "/assets/images/products/Home&Garden/12.CactusinJapaneseCeramic.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "f5bb14b1-3818-4031-9696-eff5c82aed58",
    slug: "garden-roses-in-blue-vase",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Garden Roses in Blue Vase",
    brand: null,
    price: 56,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/13.GardenRosesinBlueVase.png",
    images: [
      "/assets/images/products/Home&Garden/13.GardenRosesinBlueVase.png",
      "/assets/images/products/Home&Garden/13.GardenRosesinBlueVase.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "fd882740-43c5-478a-9161-0c33b8ee8d6a",
    slug: "hanging-planters-with-green-plants",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Hanging Planters with Green Plants",
    brand: null,
    price: 72,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/14.HangingPlanterswithGreenPlants.png",
    images: [
      "/assets/images/products/Home&Garden/14.HangingPlanterswithGreenPlants.png",
      "/assets/images/products/Home&Garden/14.HangingPlanterswithGreenPlants.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "abc04f43-761e-4600-85e0-87dcc2789e1e",
    slug: "tilandsia-flexuosa-in-wood-frame",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Tilandsia Flexuosa in Wood Frame",
    brand: null,
    price: 46,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/15.TilandsiaFlexuosainWoodFrame.png",
    images: [
      "/assets/images/products/Home&Garden/15.TilandsiaFlexuosainWoodFrame.png",
      "/assets/images/products/Home&Garden/15.TilandsiaFlexuosainWoodFrame.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "a53aecaa-9540-4ee8-aa5f-738a4fbb2d2d",
    slug: "copper-planter-with-mixed-plants",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Copper Planter With Mixed Plants",
    brand: null,
    price: 182,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/16.CopperPlanterwithMixedPlants.png",
    images: [
      "/assets/images/products/Home&Garden/16.CopperPlanterwithMixedPlants.png",
      "/assets/images/products/Home&Garden/16.CopperPlanterwithMixedPlants.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "23a3b377-3021-41ca-917d-fd9fa72ec2a6",
    slug: "molded-wax-agave-in-japanese-planter",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Molded Wax agave in Japanese Planter",
    brand: null,
    price: 174,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/17.MoldedwaxagaveinJapanesePlanter.png",
    images: [
      "/assets/images/products/Home&Garden/17.MoldedwaxagaveinJapanesePlanter.png",
      "/assets/images/products/Home&Garden/17.MoldedwaxagaveinJapanesePlanter.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "907b487e-da2e-4a39-bac3-0fdaff1896a1",
    slug: "green-vase-doilies",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Green vase Doilies",
    brand: null,
    price: 129,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Home&Garden/18.GreenVaseDoilies.png",
    images: [
      "/assets/images/products/Home&Garden/18.GreenVaseDoilies.png",
      "/assets/images/products/Home&Garden/18.GreenVaseDoilies.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "ed992f74-0c4c-46ae-bbef-957f8ba3efc9",
    slug: "mammillaria-elongata-cactus",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mammillaria Elongata Cactus",
    brand: null,
    price: 195,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/19.MammillariaElongataCactus.png",
    images: [
      "/assets/images/products/Home&Garden/19.MammillariaElongataCactus.png",
      "/assets/images/products/Home&Garden/19.MammillariaElongataCactus.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "9d2d65fc-e9b5-486b-b441-0b190baf5802",
    slug: "set-green-plastic-pot-25cms-(blue)",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Set Green Plastic Pot 25CMS (Blue)",
    brand: null,
    price: 24,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/20.SetgreenPlasticPot25CMSBlue.png",
    images: [
      "/assets/images/products/Home&Garden/20.SetgreenPlasticPot25CMSBlue.png",
      "/assets/images/products/Home&Garden/20.SetgreenPlasticPot25CMSBlue.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "c02ddb3d-b830-441f-bcd2-62bde8a81884",
    slug: "grass-pot-15.5-inch",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Grass Pot 15.5 inch",
    brand: null,
    price: 112,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Home&Garden/21.GrassPot15.5inch.png",
    images: [
      "/assets/images/products/Home&Garden/21.GrassPot15.5inch.png",
      "/assets/images/products/Home&Garden/21.GrassPot15.5inch.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "f860cd25-21a8-4ec7-a21e-af534aef48a1",
    slug: "euphorbia-leuconeura-with-orange-pot",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Euphorbia Leuconeura with Orange Pot",
    brand: null,
    price: 45,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/22.EuphorbiaLeuconeurawithOrangePot.png",
    images: [
      "/assets/images/products/Home&Garden/22.EuphorbiaLeuconeurawithOrangePot.png",
      "/assets/images/products/Home&Garden/22.EuphorbiaLeuconeurawithOrangePot.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "3166afb0-3bd1-494c-a509-b2888b208cb4",
    slug: "euphorbia-lactea-in-round-clay-pot",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Euphorbia Lactea in Round Clay Pot",
    brand: null,
    price: 214,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/23.EuphorbiaLacteainRoundClayPot.png",
    images: [
      "/assets/images/products/Home&Garden/23.EuphorbiaLacteainRoundClayPot.png",
      "/assets/images/products/Home&Garden/23.EuphorbiaLacteainRoundClayPot.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "43323adc-3531-452a-8dcb-9b3a01781dc6",
    slug: "mixed-succulent-in-glass-jar",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mixed Succulent in Glass Jar",
    brand: null,
    price: 112,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/24.MixedSucculentinGlassJar.png",
    images: [
      "/assets/images/products/Home&Garden/24.MixedSucculentinGlassJar.png",
      "/assets/images/products/Home&Garden/24.MixedSucculentinGlassJar.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "ca363a3e-1492-43f6-b411-1d40dd63d715",
    slug: "painted-leaf-begonia-plant",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Painted Leaf Begonia Plant",
    brand: null,
    price: 215,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/25.PaintedLeafBegoniaPlant.png",
    images: [
      "/assets/images/products/Home&Garden/25.PaintedLeafBegoniaPlant.png",
      "/assets/images/products/Home&Garden/25.PaintedLeafBegoniaPlant.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "9bd3d109-7e78-4015-9de1-ee28fbfc469c",
    slug: "areca-palm-in-clay-pot",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Areca Palm in Clay Pot",
    brand: null,
    price: 23,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Home&Garden/26.ArecaPalminClayPot.png",
    images: [
      "/assets/images/products/Home&Garden/26.ArecaPalminClayPot.png",
      "/assets/images/products/Home&Garden/26.ArecaPalminClayPot.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "723aabbd-694c-43a7-be76-c23d7713cc67",
    slug: "black-coral-sansevieria-plant",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Black Coral Sansevieria Plant",
    brand: null,
    price: 231,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Home&Garden/27.BlackCoralSansevieriaPlant.png",
    images: [
      "/assets/images/products/Home&Garden/27.BlackCoralSansevieriaPlant.png",
      "/assets/images/products/Home&Garden/27.BlackCoralSansevieriaPlant.png",
    ],
    categories: ["home&garden"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "1ae7345c-22d2-4b43-9aa7-b0be14b9136c",
    slug: "6string-cheap-electric-guitar",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "6String Cheap Electric Guitar",
    brand: null,
    price: 220,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/1.6StringCheapElectricGuitar.png",
    images: [
      "/assets/images/products/Music/1.6StringCheapElectricGuitar.png",
      "/assets/images/products/Music/1.6StringCheapElectricGuitar.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "51b54319-520b-4b3f-bcde-f8979968227b",
    slug: "affordable-4string-tenor-banjo",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Affordable 4String Tenor Banjo",
    brand: null,
    price: 113,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Music/2.Affordable4StringTenorBanjo.png",
    images: [
      "/assets/images/products/Music/2.Affordable4StringTenorBanjo.png",
      "/assets/images/products/Music/2.Affordable4StringTenorBanjo.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "1cd6340c-9419-43ec-95d5-456650a0e7a2",
    slug: "affordable-indian-sitar",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Affordable Indian Sitar",
    brand: null,
    price: 79,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/3.AffordableIndianSitar.png",
    images: [
      "/assets/images/products/Music/3.AffordableIndianSitar.png",
      "/assets/images/products/Music/3.AffordableIndianSitar.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "372d3d07-dcab-4602-ac36-ee162149aeec",
    slug: "detailed-painted-acoustic-guitar",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Detailed Painted Acoustic Guitar",
    brand: null,
    price: 13,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Music/4.DetailedPaintedAcousticGuitar.png",
    images: [
      "/assets/images/products/Music/4.DetailedPaintedAcousticGuitar.png",
      "/assets/images/products/Music/4.DetailedPaintedAcousticGuitar.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "ae0328ef-88f5-4a9a-b150-124e0f2ff5bf",
    slug: "jackson-6string-floyd-rose",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Jackson 6String Floyd Rose",
    brand: null,
    price: 23,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/5.Jackson6StringFloydRose.png",
    images: [
      "/assets/images/products/Music/5.Jackson6StringFloydRose.png",
      "/assets/images/products/Music/5.Jackson6StringFloydRose.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "4feecef3-be55-4c45-99f7-1439b5819e45",
    slug: "jackson-6string-fixed-bridge",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Jackson 6String Fixed Bridge",
    brand: null,
    price: 172,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/6.Jackson6StringFixedBridge.png",
    images: [
      "/assets/images/products/Music/6.Jackson6StringFixedBridge.png",
      "/assets/images/products/Music/6.Jackson6StringFixedBridge.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "4bbc1981-a760-404f-89c2-255a2fa2038b",
    slug: "royal-harp",
    shop: {
      id: "641df648-9632-467c-af6f-913f974eb801",
      slug: "cybershop",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Royal Harp",
    brand: null,
    price: 40,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/7.RoyalHarp.png",
    images: [
      "/assets/images/products/Music/7.RoyalHarp.png",
      "/assets/images/products/Music/7.RoyalHarp.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "04801ab8-0ac2-46e3-97af-536faf571fae",
    slug: "mid-range-tabla",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mid Range Tabla",
    brand: null,
    price: 104,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/8.MidRangeTabla.png",
    images: [
      "/assets/images/products/Music/8.MidRangeTabla.png",
      "/assets/images/products/Music/8.MidRangeTabla.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "9f774480-87cc-43da-92a4-f47e0c1e730f",
    slug: "mid-range-dhol",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mid Range Dhol",
    brand: null,
    price: 205,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/9.MidRangeDhol.png",
    images: [
      "/assets/images/products/Music/9.MidRangeDhol.png",
      "/assets/images/products/Music/9.MidRangeDhol.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "a06a43b9-e1da-43a5-8c30-d6f3d1e4af81",
    slug: "buffet-400-saxophone",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Buffet 400 Saxophone",
    brand: null,
    price: 192,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/10.Buffet400Saxophone.png",
    images: [
      "/assets/images/products/Music/10.Buffet400Saxophone.png",
      "/assets/images/products/Music/10.Buffet400Saxophone.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "ae555b39-3295-468c-ab45-8a9bb7f6ca0d",
    slug: "xamaha-electrtic-violin",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xamaha Electrtic Violin",
    brand: null,
    price: 89,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/11.YamahaElectricViolin.png",
    images: [
      "/assets/images/products/Music/11.YamahaElectricViolin.png",
      "/assets/images/products/Music/11.YamahaElectricViolin.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "423fb13d-a67f-41e0-9a03-e1e7dbcfad27",
    slug: "cheap-ukulele-offers",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Cheap Ukulele Offers",
    brand: null,
    price: 18,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/12.CheapUkuleleOffers.png",
    images: [
      "/assets/images/products/Music/12.CheapUkuleleOffers.png",
      "/assets/images/products/Music/12.CheapUkuleleOffers.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "43fdad2c-327c-4b8c-8ac3-84b2d5b5571b",
    slug: "cordoba-classical-guitar",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Cordoba Classical Guitar",
    brand: null,
    price: 111,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/13.CordobaClassicalGuitar.png",
    images: [
      "/assets/images/products/Music/13.CordobaClassicalGuitar.png",
      "/assets/images/products/Music/13.CordobaClassicalGuitar.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "6b174a50-c2c2-463c-8e57-a55459ee0c26",
    slug: "little-purcussion-instruments",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Little Purcussion Instruments",
    brand: null,
    price: 30,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Music/14.LittlePercussionInstruments.png",
    images: [
      "/assets/images/products/Music/14.LittlePercussionInstruments.png",
      "/assets/images/products/Music/14.LittlePercussionInstruments.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "69ac54b8-eecc-44ef-8ac6-d716cd901965",
    slug: "pearl-snare-drum",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Pearl Snare Drum",
    brand: null,
    price: 214,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/15.PearlSnareDrum.png",
    images: [
      "/assets/images/products/Music/15.PearlSnareDrum.png",
      "/assets/images/products/Music/15.PearlSnareDrum.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "4dd6a1ef-244f-428c-ad21-17a5b787981b",
    slug: "xamaha-piano",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xamaha Piano",
    brand: null,
    price: 40,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/16.YamahaPiano.png",
    images: [
      "/assets/images/products/Music/16.YamahaPiano.png",
      "/assets/images/products/Music/16.YamahaPiano.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "34b061cc-5612-4d3d-97c4-596e748ebe0f",
    slug: "poland-piano",
    shop: {
      id: "3d1727f9-f0fd-463b-87fc-81c4df47e992",
      slug: "scroll-through",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Poland Piano",
    brand: null,
    price: 16,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/17.RolandPiano.png",
    images: [
      "/assets/images/products/Music/17.RolandPiano.png",
      "/assets/images/products/Music/17.RolandPiano.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "f9d00e58-3b4f-426c-b66b-fe205c33b035",
    slug: "fender-4string-bass-guitar",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Fender 4String Bass Guitar",
    brand: null,
    price: 205,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/18.Fender4stringBassGuitar.png",
    images: [
      "/assets/images/products/Music/18.Fender4stringBassGuitar.png",
      "/assets/images/products/Music/18.Fender4stringBassGuitar.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "6fb0a443-f573-46f2-9325-9b859edf3d15",
    slug: "changer-harmonium",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Changer Harmonium",
    brand: null,
    price: 40,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/19.ChangerHarmonium.png",
    images: [
      "/assets/images/products/Music/19.ChangerHarmonium.png",
      "/assets/images/products/Music/19.ChangerHarmonium.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "e53a8081-84f3-4539-a3cc-190a4c0fe018",
    slug: "poland-v-drums",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Poland V Drums",
    brand: null,
    price: 9,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/20.RolandVDrums.png",
    images: [
      "/assets/images/products/Music/20.RolandVDrums.png",
      "/assets/images/products/Music/20.RolandVDrums.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "be6bcc31-6be5-4ce8-af0a-270757838dac",
    slug: "ibanez-gio-6string-guitar",
    shop: {
      id: "acfa0595-3e11-4afc-a3e4-c59ddafe5ea5",
      slug: "scarlett-beauty",
      user: {
        id: "15e04e05-4446-4a3f-954f-4995ee9cd706",
        email: "<EMAIL>",
        phone: "(************* x03713",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1203.jpg",
        password: "tCcmXOtawl_2QD3",
        dateOfBirth: "1976-08-28T14:44:08.160Z",
        verified: true,
        name: {
          firstName: "Emelie",
          lastName: "Rogahn",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Ibanez GIO 6String Guitar",
    brand: null,
    price: 139,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/21.IbanezGIO6stringGuitar.png",
    images: [
      "/assets/images/products/Music/21.IbanezGIO6stringGuitar.png",
      "/assets/images/products/Music/21.IbanezGIO6stringGuitar.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "46ea2783-b738-40b8-8218-ad567090f8cf",
    slug: "turkish-mandolin",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Turkish Mandolin",
    brand: null,
    price: 165,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/22.TurkishMandolin.png",
    images: [
      "/assets/images/products/Music/22.TurkishMandolin.png",
      "/assets/images/products/Music/22.TurkishMandolin.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "81c2d888-1d79-441d-8af3-2510e5ec374f",
    slug: "affordable-turkish-oud",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Affordable Turkish Oud",
    brand: null,
    price: 101,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/23.AfforableTurkishOud.png",
    images: [
      "/assets/images/products/Music/23.AfforableTurkishOud.png",
      "/assets/images/products/Music/23.AfforableTurkishOud.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "7934dfd1-8b83-4c09-870c-821a57d25569",
    slug: "xamaha-acoustic-guitar",
    shop: {
      id: "9b2e19f3-f3e0-46a3-9529-2aaa504a35b1",
      slug: "anytime-buys",
      user: {
        id: "d015dc20-2c0b-4fc8-82f9-0d41aa782894",
        email: "<EMAIL>",
        phone: "************** x2392",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/139.jpg",
        password: "RD9XWNQDkmvBnh_",
        dateOfBirth: "1961-04-27T14:58:24.959Z",
        verified: true,
        name: {
          firstName: "Jacey",
          lastName: "Mitchell",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Xamaha Acoustic Guitar",
    brand: null,
    price: 62,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/24.YamahaAcousticGuitar.png",
    images: [
      "/assets/images/products/Music/24.YamahaAcousticGuitar.png",
      "/assets/images/products/Music/24.YamahaAcousticGuitar.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "226b5590-a652-450b-8ee5-e6386000a92c",
    slug: "poland-full-set-v-drums",
    shop: {
      id: "32040dd2-ea90-4bc0-94d5-2291b9b11208",
      slug: "keyboard-kiosk",
      user: {
        id: "9648e2d4-a7e4-4af7-9196-bd6aa788954b",
        email: "<EMAIL>",
        phone: "************** x1051",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/870.jpg",
        password: "FhzPBHFvzhQEymY",
        dateOfBirth: "1997-07-23T21:35:22.816Z",
        verified: true,
        name: {
          firstName: "Kaia",
          lastName: "Wyman",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Poland Full Set V Drums",
    brand: null,
    price: 155,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/25.RolandFullSetVDrums.png",
    images: [
      "/assets/images/products/Music/25.RolandFullSetVDrums.png",
      "/assets/images/products/Music/25.RolandFullSetVDrums.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "07fd1e23-dcc3-46d2-9d64-d2fd8a0d1e3a",
    slug: "poland-jupiter-x-synthesizer",
    shop: {
      id: "98a35da7-4c3f-451b-a3eb-5e93a87f630a",
      slug: "coveted-clicks",
      user: {
        id: "387d1ffa-1d77-4369-bc4c-8f92a3ed5410",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/754.jpg",
        password: "ai2OLRvDniIGMkR",
        dateOfBirth: "2000-07-08T03:36:38.091Z",
        verified: true,
        name: {
          firstName: "Dean",
          lastName: "Schmidt",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Poland Jupiter-X Synthesizer",
    brand: null,
    price: 243,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/26.RolandJupiterXSynthesizer.png",
    images: [
      "/assets/images/products/Music/26.RolandJupiterXSynthesizer.png",
      "/assets/images/products/Music/26.RolandJupiterXSynthesizer.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 3,
    unit: null,
  },
  {
    id: "b9b1b02b-3843-49e5-b3d2-be1995120919",
    slug: "affordable-cordoba-classical-guitar",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Affordable Cordoba Classical Guitar",
    brand: null,
    price: 73,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Music/27.AffordableCordobaClassicalGuitar.png",
    images: [
      "/assets/images/products/Music/27.AffordableCordobaClassicalGuitar.png",
      "/assets/images/products/Music/27.AffordableCordobaClassicalGuitar.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
  {
    id: "654abb6d-f448-4b03-9263-deac93824555",
    slug: "turkish-long-neck-baglama",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Turkish Long Neck Baglama",
    brand: null,
    price: 192,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/28.TurkishLongNeckBaglama.png",
    images: [
      "/assets/images/products/Music/28.TurkishLongNeckBaglama.png",
      "/assets/images/products/Music/28.TurkishLongNeckBaglama.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 0,
    unit: null,
  },
  {
    id: "3e168179-54d4-480a-b49d-13c585f2e39a",
    slug: "mahalo-soprano-ukuleles",
    shop: {
      id: "6976c27f-e038-4df2-b8a4-1559a73f4b82",
      slug: "scarlett-beauty",
      user: {
        id: "422bd71d-b630-4590-92b4-8eee48da1082",
        email: "<EMAIL>",
        phone: "************ x19584",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/928.jpg",
        password: "vtJw__7B6wKo2JF",
        dateOfBirth: "1943-03-03T06:16:37.790Z",
        verified: true,
        name: {
          firstName: "Heber",
          lastName: "White",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Mahalo Soprano Ukuleles",
    brand: null,
    price: 236,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/29.MahaloSopranoUkuleles.png",
    images: [
      "/assets/images/products/Music/29.MahaloSopranoUkuleles.png",
      "/assets/images/products/Music/29.MahaloSopranoUkuleles.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 1,
    unit: null,
  },
  {
    id: "12f2792f-0acf-4713-9c10-6475e34bf083",
    slug: "yamaha-yas-280-saxophone",
    shop: {
      id: "9eecf921-25ca-4d1b-a1c4-f232371a176c",
      slug: "word-wide-wishes",
      user: {
        id: "2cc76cbe-8927-48fe-bef4-81f21a4caac3",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/887.jpg",
        password: "w3_IqNOBqwEQdy2",
        dateOfBirth: "1981-06-19T12:09:17.219Z",
        verified: true,
        name: {
          firstName: "Kayli",
          lastName: "Kunde",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "yamaha YAS-280 Saxophone",
    brand: null,
    price: 173,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/30.YamahaYAS280Saxophone.png",
    images: [
      "/assets/images/products/Music/30.YamahaYAS280Saxophone.png",
      "/assets/images/products/Music/30.YamahaYAS280Saxophone.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 2,
    unit: null,
  },
  {
    id: "691f55a9-3a8e-4544-a482-2d99d0d3fad0",
    slug: "eastman-westburry-cello",
    shop: {
      id: "43196d35-a8d0-4f7d-9c05-b10dc4f145af",
      slug: "constant-shoppers",
      user: {
        id: "bdfc07fa-3ec2-4b63-a221-21cf65d9968e",
        email: "<EMAIL>",
        phone: "************ x6833",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/809.jpg",
        password: "shYo3rkQqRkg9VM",
        dateOfBirth: "1972-03-29T14:31:41.272Z",
        verified: true,
        name: {
          firstName: "Lucinda",
          lastName: "Smith",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Eastman Westburry Cello",
    brand: null,
    price: 7,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Music/31.EastmanWestburryCello.png",
    images: [
      "/assets/images/products/Music/31.EastmanWestburryCello.png",
      "/assets/images/products/Music/31.EastmanWestburryCello.png",
    ],
    categories: ["music"],
    status: null,
    reviews: [],
    rating: 4,
    unit: null,
  },
];
