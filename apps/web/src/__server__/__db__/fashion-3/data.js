// carousel data
export const mainCarouselData = [
  {
    id: 1,
    discount: 30,
    category: "Men",
    buttonLink: "#",
    buttonText: "Shop Now",
    title: "Lifestyle collection",
    imgUrl: "/assets/images/banners/banner-24.jpg",
    description: "Get Free Shipping on orders over $99.00",
  },
  {
    id: 2,
    discount: 35,
    buttonLink: "#",
    category: "Women",
    buttonText: "Shop Now",
    title: "Lifestyle collection",
    imgUrl: "/assets/images/banners/banner-23.jpg",
    description: "Get Free Shipping on orders over $99.00",
  },
];

// products
export const products = [
  // featured-products
  {
    id: "52b31bcd-84c1-4c1c-a8fc-2ccf8cbf93c3",
    slug: "denim-classic-blue-jeans",
    shop: {
      id: "159b6c6a-82a5-4738-8887-dd161ae44044",
      slug: "scarlett-beauty",
      user: {
        id: "0c20166d-4352-4150-995a-bd60c4fe2ce8",
        email: "<PERSON><PERSON><PERSON>uce<PERSON>@yahoo.com",
        phone: "************ x888",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/349.jpg",
        password: "HfexywaO7hS6fOB",
        dateOfBirth: "1946-05-10T01:14:03.001Z",
        verified: true,
        name: {
          firstName: "Arne",
          lastName: "Wolf",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-9.png",
      profilePicture: "/assets/images/faces/propic(8).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Denim Classic Blue Jeans",
    brand: null,
    price: 310,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/7.DenimClassicBlueJeans.png",
    images: [
      "/assets/images/products/Fashion/Clothes/7.DenimClassicBlueJeans.png",
      "/assets/images/products/Fashion/Clothes/7.DenimClassicBlueJeans.png",
    ],
    categories: ["Men's Fashion"],
    status: null,
    reviews: [],
    rating: 1,
    for: {
      demo: "fashion-3",
      type: "featured-products",
    },
  },
  {
    id: "69f3cc5a-edf1-4b71-836d-cf1792965b59",
    slug: "double-wool-overcoat",
    shop: {
      id: "05073267-1f67-440d-84e8-d515045c57ca",
      slug: "word-wide-wishes",
      user: {
        id: "40233a44-6a28-4c41-a4e9-5c36a18aff60",
        email: "<EMAIL>",
        phone: "************ x23575",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/518.jpg",
        password: "8oRfYKuQsAKnGeD",
        dateOfBirth: "1957-03-01T18:30:42.514Z",
        verified: true,
        name: {
          firstName: "Bernhard",
          lastName: "Friesen",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Double Wool Overcoat",
    brand: null,
    price: 310,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/16.DoubleWoolOvercoat.png",
    images: [
      "/assets/images/products/Fashion/Clothes/16.DoubleWoolOvercoat.png",
      "/assets/images/products/Fashion/Clothes/16.DoubleWoolOvercoat.png",
    ],
    categories: ["Women's Fashion"],
    status: null,
    reviews: [],
    rating: 2,
    for: {
      demo: "fashion-3",
      type: "featured-products",
    },
  },
  {
    id: "7692beed-2624-4098-a10e-de4cf6459b51",
    slug: "royal-black-suit-pant",
    shop: {
      id: "bc577201-2996-4ab3-a172-cfa60bed340d",
      slug: "coveted-clicks",
      user: {
        id: "40233a44-6a28-4c41-a4e9-5c36a18aff60",
        email: "<EMAIL>",
        phone: "************ x23575",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/518.jpg",
        password: "8oRfYKuQsAKnGeD",
        dateOfBirth: "1957-03-01T18:30:42.514Z",
        verified: true,
        name: {
          firstName: "Bernhard",
          lastName: "Friesen",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Royal Black Suit Pant",
    brand: null,
    price: 1140,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/8.RoyalBlackSuitPant.png",
    images: [
      "/assets/images/products/Fashion/Clothes/8.RoyalBlackSuitPant.png",
      "/assets/images/products/Fashion/Clothes/8.RoyalBlackSuitPant.png",
    ],
    categories: ["Men's Fashion"],
    status: null,
    reviews: [],
    rating: 1,
    for: {
      demo: "fashion-3",
      type: "featured-products",
    },
  },
  {
    id: "7aec8d52-46e5-432c-acd8-43c08746bbe1",
    slug: "blue-trousers",
    shop: {
      id: "4c4cd765-e0a6-4dde-87ef-56a2d03473c3",
      slug: "anytime-buys",
      user: {
        id: "7d1dbf78-4dd9-4431-a6ef-4aad48a9571f",
        email: "<EMAIL>",
        phone: "(*************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/561.jpg",
        password: "RJ9LApEkYeOL84b",
        dateOfBirth: "1996-12-19T09:29:51.709Z",
        verified: true,
        name: {
          firstName: "Sharon",
          lastName: "Donnelly",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Blue Trousers",
    brand: null,
    price: 180,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/14.BlueTrousers.png",
    images: [
      "/assets/images/products/Fashion/Clothes/14.BlueTrousers.png",
      "/assets/images/products/Fashion/Clothes/14.BlueTrousers.png",
    ],
    categories: ["Men's Fashion"],
    status: null,
    reviews: [],
    rating: 3,
    for: {
      demo: "fashion-3",
      type: "featured-products",
    },
  },
  {
    id: "cf5356cb-de5f-4e28-bd4a-09860f78b0e6",
    slug: "women's-fashion",
    shop: {
      id: "9602ec8a-276a-428f-9821-8f638d473d63",
      slug: "scroll-through",
      user: {
        id: "3f49cb21-762c-4140-b379-63608ab6162c",
        email: "<EMAIL>",
        phone: "(************* x811",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/801.jpg",
        password: "bBQJo0Wopy5j6Zb",
        dateOfBirth: "1977-01-31T05:19:46.871Z",
        verified: true,
        name: {
          firstName: "Jalyn",
          lastName: "Brown",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Women's Fashion",
    brand: null,
    price: 140,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
    ],
    categories: ["Fashion"],
    status: null,
    reviews: [],
    rating: 13,
    for: {
      demo: "fashion-3",
      type: "featured-products",
    },
  },
  {
    id: "62037750-df8b-401d-b949-1f5e61312726",
    slug: "gray-overcoat-women",
    shop: {
      id: "9602ec8a-276a-428f-9821-8f638d473d63",
      slug: "scroll-through",
      user: {
        id: "3f49cb21-762c-4140-b379-63608ab6162c",
        email: "<EMAIL>",
        phone: "(************* x811",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/801.jpg",
        password: "bBQJo0Wopy5j6Zb",
        dateOfBirth: "1977-01-31T05:19:46.871Z",
        verified: true,
        name: {
          firstName: "Jalyn",
          lastName: "Brown",
        },
      },
      email: "<EMAIL>",
      name: "Scroll Through",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner.png",
      profilePicture: "/assets/images/faces/propic(1).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Gray Overcoat Women",
    brand: null,
    price: 110,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/20.GrayOvercoatWomen.png",
    images: [
      "/assets/images/products/Fashion/Clothes/20.GrayOvercoatWomen.png",
      "/assets/images/products/Fashion/Clothes/20.GrayOvercoatWomen.png",
    ],
    categories: ["Women's Fashion"],
    status: null,
    reviews: [],
    rating: 10,
    for: {
      demo: "fashion-3",
      type: "featured-products",
    },
  },
  {
    id: "11ce2c04-d52f-4f5b-afda-7ed79188c780",
    slug: "women's-fashion",
    shop: {
      id: "84a14726-a7e4-4b64-a3be-d9f4904dac7b",
      slug: "constant-shoppers",
      user: {
        id: "e52aa490-3f34-4e8e-941e-bbe16c123ced",
        email: "<EMAIL>",
        phone: "(*************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/394.jpg",
        password: "DvvQoSyL5E9XZ5q",
        dateOfBirth: "1989-07-16T03:35:44.506Z",
        verified: true,
        name: {
          firstName: "Isabella",
          lastName: "Hackett",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Women's Fashion",
    brand: null,
    price: 140,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
    ],
    categories: ["Fashion"],
    status: null,
    reviews: [],
    rating: 13,
    for: {
      demo: "fashion-3",
      type: "featured-products",
    },
  },
  //  best-selling-product
  {
    id: "a13fb1ec-5fc7-4b22-83d8-2d454d91aa0a",
    slug: "silver-high-neck-sweater",
    shop: {
      id: "91cf19cf-ea8c-4ae2-b350-6d576dceaa2a",
      slug: "cybershop",
      user: {
        id: "64eaab71-1989-4401-b10b-9c383d2391a3",
        email: "<EMAIL>",
        phone: "************ x351",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/955.jpg",
        password: "4mMv9nw4Tf9Kvcf",
        dateOfBirth: "1984-12-15T19:14:37.380Z",
        verified: true,
        name: {
          firstName: "Erna",
          lastName: "Hartmann",
        },
      },
      email: "<EMAIL>",
      name: "Cybershop",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-8.png",
      profilePicture: "/assets/images/faces/propic(7).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Silver High Neck Sweater",
    brand: null,
    price: 210,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/1.SilverHighNeckSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/1.SilverHighNeckSweater.png",
      "/assets/images/products/Fashion/Clothes/1.SilverHighNeckSweater.png",
    ],
    categories: ["Men's Fashion"],
    status: null,
    reviews: [],
    rating: 5,
    for: {
      demo: "fashion-3",
      type: "best-selling-product",
    },
  },
  {
    id: "da47252d-5cb7-426e-a30f-6434ead76eba",
    slug: "yellow-casual-sweater",
    shop: {
      id: "5d2a040d-4fd5-4e04-8f35-7f7a857f6b21",
      slug: "keyboard-kiosk",
      user: {
        id: "64eaab71-1989-4401-b10b-9c383d2391a3",
        email: "<EMAIL>",
        phone: "************ x351",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/955.jpg",
        password: "4mMv9nw4Tf9Kvcf",
        dateOfBirth: "1984-12-15T19:14:37.380Z",
        verified: true,
        name: {
          firstName: "Erna",
          lastName: "Hartmann",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Yellow Casual Sweater",
    brand: null,
    price: 110,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/21.YellowCasualSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/21.YellowCasualSweater.png",
      "/assets/images/products/Fashion/Clothes/21.YellowCasualSweater.png",
    ],
    categories: ["Women's Fashion"],
    status: null,
    reviews: [],
    rating: 5,
    for: {
      demo: "fashion-3",
      type: "best-selling-product",
    },
  },
  {
    id: "4c60f4a9-fbf4-4d29-bf3e-c77409587996",
    slug: "denim-blue-jeans",
    shop: {
      id: "6805612d-dd75-4b70-aa72-e66068150e00",
      slug: "word-wide-wishes",
      user: {
        id: "30cdeb15-4842-4587-9afa-489162641aad",
        email: "<EMAIL>",
        phone: "**************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/68.jpg",
        password: "GZ3EDWViomelSur",
        dateOfBirth: "2000-11-10T20:44:58.003Z",
        verified: true,
        name: {
          firstName: "Candida",
          lastName: "Brown",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Denim Blue Jeans",
    brand: null,
    price: 140,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Clothes/4.DenimBlueJeans.png",
    images: [
      "/assets/images/products/Fashion/Clothes/4.DenimBlueJeans.png",
      "/assets/images/products/Fashion/Clothes/4.DenimBlueJeans.png",
    ],
    categories: ["Men's Fashion"],
    status: null,
    reviews: [],
    rating: 5,
    for: {
      demo: "fashion-3",
      type: "best-selling-product",
    },
  },
  {
    id: "26a6fb40-cd67-495b-9812-ddd1582b2d02",
    slug: "black-white-sweater",
    shop: {
      id: "5d2a040d-4fd5-4e04-8f35-7f7a857f6b21",
      slug: "keyboard-kiosk",
      user: {
        id: "64eaab71-1989-4401-b10b-9c383d2391a3",
        email: "<EMAIL>",
        phone: "************ x351",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/955.jpg",
        password: "4mMv9nw4Tf9Kvcf",
        dateOfBirth: "1984-12-15T19:14:37.380Z",
        verified: true,
        name: {
          firstName: "Erna",
          lastName: "Hartmann",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Black White Sweater",
    brand: null,
    price: 180,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/19.BlackWhiteSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/19.BlackWhiteSweater.png",
      "/assets/images/products/Fashion/Clothes/19.BlackWhiteSweater.png",
    ],
    categories: ["Men's Fashion"],
    status: null,
    reviews: [],
    rating: 5,
    for: {
      demo: "fashion-3",
      type: "best-selling-product",
    },
  },
  {
    id: "f00f52e6-5dcc-47da-bf0a-6120f1980183",
    slug: "gray-overcoat-women",
    shop: {
      id: "c2d97fd7-b206-4636-b0e0-e732aa0cb37d",
      slug: "anytime-buys",
      user: {
        id: "774caf6c-551a-46ff-adca-d1fd796a8f3b",
        email: "<EMAIL>",
        phone: "************ x63489",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/789.jpg",
        password: "GCogCQ8nhv3n5sK",
        dateOfBirth: "1951-05-18T22:18:58.244Z",
        verified: true,
        name: {
          firstName: "Mckenna",
          lastName: "Stehr",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Gray Overcoat Women",
    brand: null,
    price: 110,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/20.GrayOvercoatWomen.png",
    images: [
      "/assets/images/products/Fashion/Clothes/20.GrayOvercoatWomen.png",
      "/assets/images/products/Fashion/Clothes/20.GrayOvercoatWomen.png",
    ],
    categories: ["Women's Fashion"],
    status: null,
    reviews: [],
    rating: 5,
    for: {
      demo: "fashion-3",
      type: "best-selling-product",
    },
  },
  {
    id: "480125ae-abaa-4902-a4cf-1317798fd2f8",
    slug: "women's-fashion",
    shop: {
      id: "5d2a040d-4fd5-4e04-8f35-7f7a857f6b21",
      slug: "keyboard-kiosk",
      user: {
        id: "64eaab71-1989-4401-b10b-9c383d2391a3",
        email: "<EMAIL>",
        phone: "************ x351",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/955.jpg",
        password: "4mMv9nw4Tf9Kvcf",
        dateOfBirth: "1984-12-15T19:14:37.380Z",
        verified: true,
        name: {
          firstName: "Erna",
          lastName: "Hartmann",
        },
      },
      email: "<EMAIL>",
      name: "Keyboard Kiosk",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-5.png",
      profilePicture: "/assets/images/faces/propic(4).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Women's Fashion",
    brand: null,
    price: 140,
    size: null,
    colors: [],
    discount: 0,
    thumbnail:
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
    images: [
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
      "/assets/images/products/Fashion/Clothes/24.OliveCasualSweater.png",
    ],
    categories: ["Fashion"],
    status: null,
    reviews: [],
    rating: 5,
    for: {
      demo: "fashion-3",
      type: "best-selling-product",
    },
  },
  {
    id: "e65a9646-5c40-4e18-b66f-63ce312c8113",
    slug: "nike-red",
    shop: {
      id: "6805612d-dd75-4b70-aa72-e66068150e00",
      slug: "word-wide-wishes",
      user: {
        id: "30cdeb15-4842-4587-9afa-489162641aad",
        email: "<EMAIL>",
        phone: "**************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/68.jpg",
        password: "GZ3EDWViomelSur",
        dateOfBirth: "2000-11-10T20:44:58.003Z",
        verified: true,
        name: {
          firstName: "Candida",
          lastName: "Brown",
        },
      },
      email: "<EMAIL>",
      name: "Word Wide Wishes",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-7.png",
      profilePicture: "/assets/images/faces/propic(6).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "Nike Red",
    brand: null,
    price: 210,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/1.Nike Red.png",
    images: [
      "/assets/images/products/Fashion/Shoes/1.Nike Red.png",
      "/assets/images/products/Fashion/Shoes/1.Nike Red.png",
    ],
    categories: ["Men's Fashion"],
    status: null,
    reviews: [],
    rating: 5,
    for: {
      demo: "fashion-3",
      type: "best-selling-product",
    },
  },
  {
    id: "cd56364c-9eac-4666-9642-d3a0772a2765",
    slug: "north-star-blue",
    shop: {
      id: "a56aac22-0c15-4a67-9607-822f638fe575",
      slug: "coveted-clicks",
      user: {
        id: "37d31a48-f776-49e7-9404-40b4effa7636",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/690.jpg",
        password: "2WEkWsmNChUOwWw",
        dateOfBirth: "1976-07-06T20:05:27.047Z",
        verified: true,
        name: {
          firstName: "Edyth",
          lastName: "Cole",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    title: "North Star Blue",
    brand: null,
    price: 110,
    size: null,
    colors: [],
    discount: 0,
    thumbnail: "/assets/images/products/Fashion/Shoes/21.NorthStarBlue.png",
    images: [
      "/assets/images/products/Fashion/Shoes/21.NorthStarBlue.png",
      "/assets/images/products/Fashion/Shoes/21.NorthStarBlue.png",
    ],
    categories: ["Women's Fashion"],
    status: null,
    reviews: [],
    rating: 5,
    for: {
      demo: "fashion-3",
      type: "best-selling-product",
    },
  },
];

// services
export const serviceList = [
  {
    id: "68164965-8d4d-4989-b3f8-4ee3fbcf2ce5",
    icon: "Truck",
    title: "Fast Delivery",
    description: "Start from $10",
  },
  {
    id: "23bc91e8-3cee-4571-a7ba-a7aee6dc99bd",
    icon: "MoneyGuarantee",
    title: "Money Guarantee",
    description: "7 Days Back",
  },
  {
    id: "00b698a6-7524-4dff-b98a-04bde8ac57f3",
    icon: "AlarmClock",
    title: "365 Days",
    description: "For free return",
  },
  {
    id: "548a43e2-661d-445c-9f11-f171771f6fc1",
    icon: "Payment",
    title: "Payment",
    description: "Secure system",
  },
];

// instagram blogs
export const blogs = [
  {
    id: "f54ee5db-ff89-4d86-ade8-86d949db7b03",
    title: "Repellendus accusamus occaecati.",
    description:
      "Saepe praesentium saepe iste minus totam excepturi expedita. Illum deserunt suscipit rerum officiis consequatur dicta odio. Necessitatibus perferendis aspernatur eum. Saepe voluptatum beatae. Hic veritatis libero nisi excepturi omnis explicabo et.",
    thumbnail: "/assets/images/blogs/post-1.jpg",
    shop: {
      id: "c2a4ed2d-6708-4c1e-aa36-eddfd0496ce7",
      slug: "constant-shoppers",
      user: {
        id: "5c47a8cb-c032-4e5d-81ff-fd082f8f7740",
        email: "<EMAIL>",
        phone: "************ x73867",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/561.jpg",
        password: "Yj2bbCH9JqNNv_K",
        dateOfBirth: "1993-08-19T08:41:32.306Z",
        verified: true,
        name: {
          firstName: "Alphonso",
          lastName: "Goodwin",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    user: {
      id: "468e1914-4e05-4da7-b84b-b099a4832ce2",
      email: "<EMAIL>",
      phone: "************",
      avatar:
        "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/558.jpg",
      password: "K1v19JSlmawcx2Y",
      dateOfBirth: "1945-03-08T22:29:18.813Z",
      verified: true,
      name: {
        firstName: "Eloy",
        lastName: "Breitenberg",
      },
    },
    createdAt: "2022-11-08T07:15:55.897Z",
    slug: "repellendus-accusamus-occaecati.",
  },
  {
    id: "d27ab586-d42b-4389-8d30-252abcc66d71",
    title: "Libero facere quaerat molestiae.",
    description:
      "Vel laboriosam maiores molestias voluptatem. Facere dolores praesentium natus exercitationem dolore. Eos esse velit expedita ratione corporis sed quae. Odit minima libero sint. Placeat illo veritatis vitae sint laborum officia fugit. Dignissimos illo neque animi vero soluta earum velit beatae.",
    thumbnail: "/assets/images/blogs/post-2.jpg",
    shop: {
      id: "c2a4ed2d-6708-4c1e-aa36-eddfd0496ce7",
      slug: "constant-shoppers",
      user: {
        id: "5c47a8cb-c032-4e5d-81ff-fd082f8f7740",
        email: "<EMAIL>",
        phone: "************ x73867",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/561.jpg",
        password: "Yj2bbCH9JqNNv_K",
        dateOfBirth: "1993-08-19T08:41:32.306Z",
        verified: true,
        name: {
          firstName: "Alphonso",
          lastName: "Goodwin",
        },
      },
      email: "<EMAIL>",
      name: "Constant Shoppers",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-4.png",
      profilePicture: "/assets/images/faces/propic(3).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    user: {
      id: "2a2afbff-c146-439b-8f51-9c54c5b80a38",
      email: "<EMAIL>",
      phone: "(*************",
      avatar:
        "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/244.jpg",
      password: "6ipNKe4R838NjzI",
      dateOfBirth: "1943-07-27T02:23:47.963Z",
      verified: true,
      name: {
        firstName: "Norma",
        lastName: "Kuphal",
      },
    },
    createdAt: "2022-11-08T02:43:37.445Z",
    slug: "libero-facere-quaerat-molestiae.",
  },
  {
    id: "3bc71b63-c646-4add-a199-46b617a827b6",
    title:
      "Provident voluptate beatae nesciunt velit ducimus corrupti magnam ratione cupiditate.",
    description:
      "Perspiciatis quos excepturi. Harum neque id voluptates corrupti natus enim dolore nesciunt. Placeat occaecati dolores quae aliquam quaerat rem accusantium. Qui iusto delectus consectetur deserunt debitis.",
    thumbnail: "/assets/images/blogs/post-3.jpg",
    shop: {
      id: "3864a9a4-529b-4464-9271-6187450e7b95",
      slug: "scarlett-beauty",
      user: {
        id: "468e1914-4e05-4da7-b84b-b099a4832ce2",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/558.jpg",
        password: "K1v19JSlmawcx2Y",
        dateOfBirth: "1945-03-08T22:29:18.813Z",
        verified: true,
        name: {
          firstName: "Eloy",
          lastName: "Breitenberg",
        },
      },
      email: "<EMAIL>",
      name: "Scarlett Beauty",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/cycle.png",
      profilePicture: "/assets/images/faces/propic.png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    user: {
      id: "774fa44e-14aa-43a6-9828-dcd1487cee95",
      email: "<EMAIL>",
      phone: "************",
      avatar:
        "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1142.jpg",
      password: "m31Cbw7j_q2sac4",
      dateOfBirth: "1942-10-27T06:40:00.162Z",
      verified: true,
      name: {
        firstName: "Madelynn",
        lastName: "Borer",
      },
    },
    createdAt: "2022-11-07T21:42:34.912Z",
    slug: "provident-voluptate-beatae-nesciunt-velit-ducimus-corrupti-magnam-ratione-cupiditate.",
  },
  {
    id: "55a6dd5c-61cc-42d6-825f-bb43d8a7f341",
    title: "Sed nulla ea cumque minima rem corrupti eligendi illo.",
    description:
      "Libero repudiandae hic in enim cupiditate adipisci quasi. Dolore perferendis amet voluptate ut. Deserunt quod nobis est fugiat deleniti. Eaque dolorum maiores iste non consectetur deserunt. Voluptatem distinctio impedit.",
    thumbnail: "/assets/images/blogs/post-4.jpg",
    shop: {
      id: "778ce3a0-096b-402e-823f-cbc56e138238",
      slug: "coveted-clicks",
      user: {
        id: "2600d37b-1c1a-4fdd-b777-d952b2bcd4b3",
        email: "<EMAIL>",
        phone: "************ x400",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/114.jpg",
        password: "5lpzzrdtgKvvYPW",
        dateOfBirth: "1990-12-22T13:03:25.917Z",
        verified: true,
        name: {
          firstName: "Will",
          lastName: "Bauch",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    user: {
      id: "774fa44e-14aa-43a6-9828-dcd1487cee95",
      email: "<EMAIL>",
      phone: "************",
      avatar:
        "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1142.jpg",
      password: "m31Cbw7j_q2sac4",
      dateOfBirth: "1942-10-27T06:40:00.162Z",
      verified: true,
      name: {
        firstName: "Madelynn",
        lastName: "Borer",
      },
    },
    createdAt: "2022-11-07T13:41:06.970Z",
    slug: "sed-nulla-ea-cumque-minima-rem-corrupti-eligendi-illo.",
  },
  {
    id: "4ec28f31-4d68-4f10-ae2e-bd36993d6307",
    title: "Quidem quos aspernatur quos recusandae unde iure.",
    description:
      "Id omnis fugit dolorem atque suscipit quasi dolores perspiciatis. Illo aut quisquam magni fuga earum atque a ducimus veritatis. Ducimus architecto laborum soluta facilis ea optio consectetur. Harum aliquid atque a incidunt ea sed a suscipit. Iusto expedita nulla quisquam vitae pariatur.",
    thumbnail: "/assets/images/blogs/post-5.jpg",
    shop: {
      id: "0e5c950e-0158-4368-bc77-2a589b85264e",
      slug: "anytime-buys",
      user: {
        id: "774fa44e-14aa-43a6-9828-dcd1487cee95",
        email: "<EMAIL>",
        phone: "************",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/1142.jpg",
        password: "m31Cbw7j_q2sac4",
        dateOfBirth: "1942-10-27T06:40:00.162Z",
        verified: true,
        name: {
          firstName: "Madelynn",
          lastName: "Borer",
        },
      },
      email: "<EMAIL>",
      name: "Anytime Buys",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-6.png",
      profilePicture: "/assets/images/faces/propic(5).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    user: {
      id: "2a2afbff-c146-439b-8f51-9c54c5b80a38",
      email: "<EMAIL>",
      phone: "(*************",
      avatar:
        "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/244.jpg",
      password: "6ipNKe4R838NjzI",
      dateOfBirth: "1943-07-27T02:23:47.963Z",
      verified: true,
      name: {
        firstName: "Norma",
        lastName: "Kuphal",
      },
    },
    createdAt: "2022-11-07T15:36:52.848Z",
    slug: "quidem-quos-aspernatur-quos-recusandae-unde-iure.",
  },
  {
    id: "6d8688a5-165c-4313-8272-2bfd75cd3065",
    title: "Pariatur recusandae sapiente adipisci velit.",
    description:
      "Dignissimos tempore et. Ex perferendis hic ratione. Occaecati voluptatibus expedita harum voluptatibus occaecati qui unde quae harum. Molestias quisquam quaerat. Molestiae odit libero debitis quisquam iste expedita recusandae odit. Expedita soluta totam harum pariatur.",
    thumbnail: "/assets/images/blogs/post-6.jpg",
    shop: {
      id: "778ce3a0-096b-402e-823f-cbc56e138238",
      slug: "coveted-clicks",
      user: {
        id: "2600d37b-1c1a-4fdd-b777-d952b2bcd4b3",
        email: "<EMAIL>",
        phone: "************ x400",
        avatar:
          "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/114.jpg",
        password: "5lpzzrdtgKvvYPW",
        dateOfBirth: "1990-12-22T13:03:25.917Z",
        verified: true,
        name: {
          firstName: "Will",
          lastName: "Bauch",
        },
      },
      email: "<EMAIL>",
      name: "Coveted Clicks",
      phone: "(*************",
      address: "845 N. Stonybrook Ave. Tonawanda, NY 14210, Denmark",
      verified: false,
      coverPicture: "/assets/images/banners/banner-3.png",
      profilePicture: "/assets/images/faces/propic(2).png",
      socialLinks: {
        facebook: null,
        youtube: null,
        twitter: null,
        instagram: null,
      },
    },
    user: {
      id: "78b60949-8984-4fd6-b4ff-9b6bf9dab0b2",
      email: "<EMAIL>",
      phone: "(*************",
      avatar:
        "https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/933.jpg",
      password: "NLEL3u8mqTB4ciA",
      dateOfBirth: "1993-11-21T08:19:37.273Z",
      verified: true,
      name: {
        firstName: "Gisselle",
        lastName: "Lebsack",
      },
    },
    createdAt: "2022-11-07T14:54:24.645Z",
    slug: "pariatur-recusandae-sapiente-adipisci-velit.",
  },
];
