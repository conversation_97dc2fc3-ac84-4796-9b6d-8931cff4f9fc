import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { mutate } from "swr";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";

export const useSettingsStore = create(
  persist(
    // eslint-disable-next-line no-unused-vars
    (set, get) => ({
      settings: {},
      setSettings: (data) => set({ settings: data }),
    }),
    {
      name: "cs.settings", // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
      partialize: (state) => ({ settings: state.settings }),
    },
  ),
);

export const useInitializeSettings = () => {
  let { settings, setSettings } = useSettingsStore((state) => state);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    let active = true;

    function runFetchRetry(numOfTries, delay) {
      if (active) {
        while (
          retryCount !== numOfTries &&
          Object.keys(settings).length !== 0
        ) {
          let tries = retryCount;
          setRetryCount((tries += 1));
          setTimeout(() => {
            mutate(
              "/api/customer/settings",
              fetch("/api/customer/settings")
                .then((res) => res.json())
                .then((resJson) => {
                  Cookies.set(
                    "secondaryColor",
                    resJson.custrecord_ng_cs_accent_color,
                    { path: "/", expires: 7 },
                  );
                  Cookies.set(
                    "primaryColor",
                    resJson.custrecord_ng_cses_web_primary_color,
                    { path: "/", expires: 7 },
                  );

                  setSettings(resJson);
                })
                .catch((err) => console.error(err))
                .then((r) => r),
            );
          }, delay);
        }
      }
    }

    function prefetch() {
      if (active) {
        mutate(
          "/api/customer/settings",
          fetch("/api/customer/settings")
            .then((res) => res.json())
            .then((resJson) => {
              Cookies.set(
                "secondaryColor",
                resJson.custrecord_ng_cs_accent_color,
                { path: "/", expires: 7 },
              );
              Cookies.set(
                "primaryColor",
                resJson.custrecord_ng_cses_web_primary_color,
                { path: "/", expires: 7 },
              );
              // if (JSON.stringify(resJson) !== JSON.stringify(settings)) {
              //   window.location.reload();
              // }
              setSettings(resJson);
            })
            .catch((err) => {
              console.error("Error getting settings: ", err);
              if (err) {
                runFetchRetry(5, 5000);
              }
            }),
          active,
        );
        // the second parameter is a Promise
        // SWR will use the result when it resolves
      }
    }

    prefetch();

    return () => (active = false);
  }, []);

  useEffect(() => {
    let active = true;
    const storeColor = () => {
      if (active === true) {
        document.documentElement.dataset.secondary = settings
          ? settings.custrecord_ng_cs_accent_color
          : "#343434";
      }
    };
    storeColor();

    return () => {
      active = false;
    };
  }, [settings]);

  return { settings };
};

export const useSettings = () => {
  const { settings } = useSettingsStore((state) => state);

  return { settings };
};

export const useSettingsUpdate = () => {
  const { setSettings } = useSettingsStore((state) => state);

  return { setSettings: (e) => setSettings(e) };
};
