name: "Next.js Bundle Analysis"

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop
  workflow_dispatch:

defaults:
  run:
    # change this if your nextjs app does not live at the root of the repo
    working-directory: ./

jobs:
  analyze:
    env:
      SKIP_ENV_VALIDATION: true
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up node
        uses: actions/setup-node@v3
        with:
          node-version: "20.x"

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Restore next build
        uses: actions/cache@v3
        id: restore-build-cache
        env:
          cache-name: cache-next-build
        with:
          path: .next/cache
          # change this if you prefer a more strict cache
          key: ${{ runner.os }}-build-${{ env.cache-name }}

      - name: Build next.js app
        env:
          SKIP_BUILD_PRODUCT_REDIRECTS: 1
        # change this if your site requires a custom build command
        run: yarn build

      # Here's the first place where next-bundle-analysis' own script is used
      # This step pulls the raw bundle stats for the current bundle
      - name: Analyze bundle
        run: node report-bundle-size.js

      - name: Upload bundle
        uses: actions/upload-artifact@v3
        with:
          name: bundle
          path: .next/analyze/__bundle_analysis.json

      - name: Download base branch bundle stats
        uses: dawidd6/action-download-artifact@v2
        if: success() && github.event.number
        with:
          workflow: nextjs_bundle_analysis.yml
          branch: ${{ github.event.pull_request.base.ref }}
          path: .next/analyze/base

      # And here's the second place - this runs after we have both the current and
      # base branch bundle stats, and will compare them to determine what changed.
      # There are two configurable arguments that come from package.json:
      #
      # - budget: optional, set a budget (bytes) against which size changes are measured
      #           it's set to 350kb here by default, as informed by the following piece:
      #           https://infrequently.org/2021/03/the-performance-inequality-gap/
      #
      # - red-status-percentage: sets the percent size increase where you get a red
      #                          status indicator, defaults to 20%
      #
      # Either of these arguments can be changed or removed by editing the `nextBundleAnalysis`
      # entry in your package.json file.
      - name: Compare with base branch bundle
        if: success() && github.event.number
        run: ls -laR .next/analyze/base && npx -p nextjs-bundle-analysis compare

      - name: Get comment body
        id: get-comment-body
        if: success() && github.event.number
        uses: actions/github-script@v6
        with:
          result-encoding: string
          script: |
            const fs = require('fs')
            const comment = fs.readFileSync('.next/analyze/__bundle_analysis_comment.txt', 'utf8')
            core.setOutput('body', comment)

      - name: Find Comment
        uses: peter-evans/find-comment@v2
        if: success() && github.event.number
        id: fc
        with:
          issue-number: ${{ github.event.number }}
          body-includes: "<!-- __NEXTJS_BUNDLE -->"

      - name: Create Comment
        uses: peter-evans/create-or-update-comment@v3
        if: success() && github.event.number && steps.fc.outputs.comment-id == 0
        with:
          issue-number: ${{ github.event.number }}
          body: ${{ steps.get-comment-body.outputs.body }}

      - name: Update Comment
        uses: peter-evans/create-or-update-comment@v3
        if: success() && github.event.number && steps.fc.outputs.comment-id != 0
        with:
          issue-number: ${{ github.event.number }}
          body: ${{ steps.get-comment-body.outputs.body }}
          comment-id: ${{ steps.fc.outputs.comment-id }}
          edit-mode: replace
