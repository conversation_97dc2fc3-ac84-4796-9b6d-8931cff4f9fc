"use client"

import { useEffect } from "react"
import { BookingCart } from "@/components/booking-cart"
import FullCalendarWrapper from "@/components/calendar/FullCalendar"
import { CalendarHeader } from "@/components/calendar-header"
import { CalendarSidebar } from "@/components/calendar-sidebar"
import { CalendarProvider } from "@/context/calendar-context"

export default function CalendarPage() {
  return (
    <CalendarProvider>
      <div className="flex h-screen flex-col overflow-hidden bg-background">
        <CalendarHeader />
        <div className="flex flex-1 overflow-hidden">
          <CalendarSidebar />
          <main className="flex-1 overflow-auto">
            <FullCalendarWrapper />
          </main>
        </div>
        <BookingCart />
      </div>
    </CalendarProvider>
  )
}
