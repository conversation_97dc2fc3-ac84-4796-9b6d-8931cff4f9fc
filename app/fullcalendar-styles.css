.calendar-wrapper .fc {
  /* Base colors */
  --fc-border-color: hsl(var(--border)); /* Map FC border to our app border */
  --fc-page-bg-color: hsl(var(--background));
  --fc-neutral-bg-color: hsl(var(--muted));

  /* Text colors */
  --fc-text-color: hsl(var(--foreground));
  --fc-list-event-hover-bg-color: hsl(var(--muted));

  /* Today highlight */
  --fc-today-bg-color: hsl(var(--primary), 0.05);

  /* Event colors */
  --fc-event-bg-color: hsl(var(--primary));
  --fc-event-border-color: hsl(var(--primary));
  --fc-event-text-color: hsl(var(--primary-foreground));
  --fc-event-selected-overlay-color: rgba(0, 0, 0, 0.25);

  /* Now indicator */
  --fc-now-indicator-color: hsl(var(--destructive));

  /* Non-business hours */
  --fc-non-business-color: hsl(var(--muted), 0.3);

  /* Ensure the calendar takes full height */
  height: 100%;
  font-family: inherit;
}

/* Dark mode specific adjustments for better contrast */
.dark .calendar-wrapper .fc {
  --fc-border-color: hsl(var(--border));
  /* Increase contrast for dark mode borders */
}

/* ===== TECHNIQUE 2: OVERRIDING PROPERTIES ===== */

/* Hide license message */
.calendar-wrapper .fc-license-message {
  display: none !important;
}

/* ===== HEADER STYLING ===== */
.calendar-wrapper .fc .fc-toolbar {
  padding: 0.75rem 1rem;
  margin-bottom: 0;
  border-bottom: 1px solid hsl(var(--border));
}

.calendar-wrapper .fc .fc-toolbar-title {
  font-size: 1.25rem;
  font-weight: 500;
}

/* ===== BUTTON STYLING ===== */
.calendar-wrapper .fc .fc-button {
  background-color: hsl(var(--primary));
  border-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  font-weight: 500;
  text-transform: capitalize;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.calendar-wrapper .fc .fc-button:hover {
  background-color: hsl(var(--primary), 0.9);
  border-color: hsl(var(--primary), 0.9);
}

.calendar-wrapper .fc .fc-button-primary:not(:disabled).fc-button-active,
.calendar-wrapper .fc .fc-button-primary:not(:disabled):active {
  background-color: hsl(var(--primary), 0.8);
  border-color: hsl(var(--primary), 0.8);
}

/* ===== GRID LINES - ALL VIEWS ===== */
/* Ensure consistent border application across all elements */
.calendar-wrapper .fc-theme-standard td,
.calendar-wrapper .fc-theme-standard th {
  border: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .fc-theme-standard .fc-list,
.calendar-wrapper .fc-theme-standard .fc-scrollgrid,
.calendar-wrapper .fc-theme-standard .fc-scrollgrid tbody,
.calendar-wrapper .fc-theme-standard .fc-scrollgrid td,
.calendar-wrapper .fc-theme-standard .fc-scrollgrid th {
  border-color: hsl(var(--border)) !important;
}

/* Fix inconsistent border widths */
.calendar-wrapper .fc-theme-standard .fc-scrollgrid {
  border-bottom-width: 1px !important;
  border-right-width: 1px !important;
}

/* ===== DAY/WEEK VIEW STYLING ===== */
/* Day header styles - Google Calendar style */
.calendar-wrapper .fc .fc-col-header {
  background-color: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .fc .fc-col-header-cell {
  padding: 0.75rem 0;
  background-color: transparent;
  border: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .fc .fc-col-header-cell-cushion {
  padding: 0.5rem;
  font-weight: 600;
  color: hsl(var(--foreground), 0.8);
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

/* Time grid styles */
.calendar-wrapper .fc .fc-timegrid-slot {
  height: 3rem;
  border-bottom: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .fc .fc-timegrid-slot-minor {
  border-bottom: 1px dashed hsl(var(--border)) !important;
}

.calendar-wrapper .fc .fc-timegrid-slot-label {
  color: hsl(var(--foreground), 0.6);
  font-size: 0.75rem;
  font-weight: 500;
}

.calendar-wrapper .fc .fc-timegrid-axis {
  border-right: 1px solid hsl(var(--border)) !important;
  background-color: hsl(var(--background));
}

.calendar-wrapper .fc .fc-timegrid-axis-cushion {
  font-size: 0.75rem;
  color: hsl(var(--foreground), 0.6);
  font-weight: 500;
  padding: 0 8px;
}

.calendar-wrapper .fc .fc-timegrid-col {
  border-left: 1px solid hsl(var(--border)) !important;
  border-right: 1px solid hsl(var(--border)) !important;
}

/* Ensure time labels are properly formatted and visible */
.calendar-wrapper .fc .fc-timegrid-axis-frame {
  border-right: 1px solid hsl(var(--border)) !important;
}

/* Now indicator - Google Calendar style */
.calendar-wrapper .fc .fc-timegrid-now-indicator-container {
  overflow: visible !important;
}

.calendar-wrapper .fc .fc-timegrid-now-indicator-line {
  border-color: hsl(var(--destructive)) !important;
  border-width: 2px !important;
  z-index: 4;
}

.calendar-wrapper .fc .fc-timegrid-now-indicator-arrow {
  border-color: hsl(var(--destructive)) !important;
  background-color: hsl(var(--destructive)) !important;
  width: 8px;
  height: 8px;
  margin-top: -4px;
  border-radius: 50%;
}

/* ===== MONTH VIEW STYLING ===== */
.calendar-wrapper .fc .fc-daygrid-day {
  border: 1px solid hsl(var(--border)) !important;
  min-height: 100px; /* Ensure consistent day cell height */
}

.calendar-wrapper .fc .fc-daygrid-day-top {
  justify-content: center;
  padding-top: 4px;
}

.calendar-wrapper .fc .fc-daygrid-day-number {
  padding: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
}

/* Today styling in month view */
.calendar-wrapper .fc .fc-day-today {
  background-color: var(--fc-today-bg-color) !important;
}

.calendar-wrapper .fc .fc-day-today .fc-daygrid-day-number {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2px;
}

/* Add subtle hover effect to day cells */
.calendar-wrapper .fc .fc-daygrid-day:hover {
  background-color: hsl(var(--muted), 0.5);
}

/* ===== TIMELINE VIEW STYLING ===== */
/* Resource timeline specific styles */
.calendar-wrapper .fc-resource-timeline .fc-resource-group {
  background-color: hsl(var(--muted), 0.5);
  border: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .fc-resource-timeline .fc-resource-cell {
  padding: 0.5rem;
  border: 1px solid hsl(var(--border)) !important;
}

/* Timeline grid lines */
.calendar-wrapper .fc-timeline-slot {
  border-right: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .fc-timeline-slot-minor {
  border-right: 1px dashed hsl(var(--border)) !important;
}

/* Time slot interval styling */
.calendar-wrapper .fc-timeline-slot {
  transition: border-color 0.2s ease;
}

/* Style for different time slot intervals - ensure consistent border visibility */
.calendar-wrapper .time-slot-15min .fc-timeline-slot {
  border-right: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .time-slot-30min .fc-timeline-slot {
  border-right: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .time-slot-1hour .fc-timeline-slot {
  border-right: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .time-slot-2hours .fc-timeline-slot {
  border-right: 2px solid hsl(var(--border)) !important;
}

.calendar-wrapper .time-slot-4hours .fc-timeline-slot {
  border-right: 2px solid hsl(var(--border)) !important;
}

.calendar-wrapper .time-slot-8hours .fc-timeline-slot {
  border-right: 3px solid hsl(var(--border)) !important;
}

.calendar-wrapper .time-slot-1day .fc-timeline-slot {
  border-right: 3px solid hsl(var(--border)) !important;
}

/* Highlight the time slot labels based on interval */
.calendar-wrapper .time-slot-2hours .fc-timeline-slot-label,
.calendar-wrapper .time-slot-4hours .fc-timeline-slot-label,
.calendar-wrapper .time-slot-8hours .fc-timeline-slot-label,
.calendar-wrapper .time-slot-1day .fc-timeline-slot-label {
  font-weight: 600;
  color: hsl(var(--foreground));
}

/* Add a subtle background to larger time slots for better visibility */
.calendar-wrapper .time-slot-4hours .fc-timeline-slot,
.calendar-wrapper .time-slot-8hours .fc-timeline-slot,
.calendar-wrapper .time-slot-1day .fc-timeline-slot {
  background-color: hsla(var(--muted), 0.1);
}

/* Timeline lanes */
.calendar-wrapper .fc-timeline-lane {
  border-bottom: 1px solid hsl(var(--border)) !important;
  height: 40px !important;
}

.calendar-wrapper .fc-timeline-lane.fc-resource-group {
  height: 40px !important;
}

/* Timeline header */
.calendar-wrapper .fc-timeline-header {
  border-bottom: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .fc-timeline-header .fc-timeline-slot-frame {
  border-right: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .fc-timeline-header-row {
  border-right: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .fc-timeline-header-row-chrono th {
  border-bottom: 1px solid hsl(var(--border)) !important;
}

/* Resource area */
.calendar-wrapper .fc-resource-area th,
.calendar-wrapper .fc-resource-area td {
  border: 1px solid hsl(var(--border)) !important;
}

.calendar-wrapper .fc-resource-area-divider {
  border-right: 1px solid hsl(var(--border)) !important;
}

/* Fix alignment of resource labels */
.calendar-wrapper .fc-datagrid-cell-cushion {
  padding-left: 8px !important;
  height: 100%;
  display: flex;
  align-items: center;
}

/* Timeline header styling - to match the screenshot */
.calendar-wrapper .fc-timeline-header {
  border-bottom: 1px solid hsl(var(--border)) !important;
  background-color: hsl(var(--background));
}

.calendar-wrapper .fc-timeline-header .fc-timeline-slot-frame {
  border-right: 1px solid hsl(var(--border)) !important;
}

/* Style the top-level header (month/year) */
.calendar-wrapper .fc-timeline-header-row:first-child th {
  height: 36px;
  vertical-align: middle;
  font-weight: 600;
  font-size: 1rem;
  text-align: center;
  border-bottom: 1px solid hsl(var(--border)) !important;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Style the day headers (weekday/date) */
.calendar-wrapper .fc-timeline-header-row:not(:first-child) th {
  height: 24px;
  vertical-align: middle;
  font-weight: 500;
  font-size: 0.8rem;
  text-align: center;
  border-bottom: 1px solid hsl(var(--border)) !important;
  border-right: 1px solid hsl(var(--border)) !important;
  background-color: hsl(var(--muted), 0.3);
  color: hsl(var(--foreground), 0.8);
}

/* Style the current day header */
.calendar-wrapper .fc-timeline-header-row:not(:first-child) th.fc-timeline-slot-current {
  background-color: hsl(var(--primary), 0.1);
  font-weight: 600;
}

/* Style the time slots in the header */
.calendar-wrapper .fc-timeline-slot-cushion {
  padding: 4px 2px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

/* Style the main timeline header (date range) */
.calendar-wrapper .fc-toolbar-title {
  font-size: 1.25rem;
  font-weight: 600;
  text-align: center;
}

/* Responsive adjustments for timeline headers */
@media (max-width: 768px) {
  .calendar-wrapper .fc-timeline-header-row:first-child th {
    font-size: 0.9rem;
    height: 26px;
  }

  .calendar-wrapper .fc-timeline-header-row:not(:first-child) th {
    font-size: 0.7rem;
    height: 22px;
  }

  .calendar-wrapper .fc-timeline-slot-cushion {
    padding: 2px 1px;
  }
}

/* Add styling for the current day indicator (red circle) */
.calendar-wrapper .fc-timeline-now-indicator-container {
  z-index: 4;
  position: relative;
}

.calendar-wrapper .fc-timeline-now-indicator-arrow {
  position: absolute;
  z-index: 4;
  margin: 0;
  border: 0;
}

/* Create the red dashed circle for current day */
.calendar-wrapper .fc-timeline-header-row:not(:first-child) th.fc-timeline-slot-current::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  border: 2px dashed hsl(var(--destructive));
  border-radius: 50%;
  z-index: 1;
}

/* ===== EVENT STYLING ===== */
/* Google Calendar-inspired event styling */
.calendar-wrapper .fc-event {
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 0.875rem;
  border: none !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease, transform 0.1s ease;
  margin: 1px 2px;
  overflow: hidden;
}

.calendar-wrapper .fc-event:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
  z-index: 10;
}

.calendar-wrapper .fc-event-main {
  padding: 2px 4px;
}

.calendar-wrapper .fc-event-time {
  font-size: 0.75rem;
  font-weight: 500;
  opacity: 0.9;
}

.calendar-wrapper .fc-event-title {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Timeline event styling */
.calendar-wrapper .fc-timeline-event {
  border-radius: 4px;
  border: none !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  height: 94px !important;
  margin-top: 5px;
}

.calendar-wrapper .fc-timeline-event:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Ghost event styling */
.ghost-event {
  opacity: 0.7 !important;
  border-style: dashed !important;
  transition: all 0.3s ease;
}

.ghost-event:hover {
  opacity: 0.9 !important;
}

.ghost-event-draft {
  border-color: #6366f1 !important;
}

.ghost-event-pending {
  border-color: #f59e0b !important;
}

.ghost-event-confirmed {
  border-color: #10b981 !important;
}

.ghost-event-rejected {
  border-color: #ef4444 !important;
}

.cart-ghost-event {
  border-style: dotted !important;
  border-width: 2px !important;
}

.modal-ghost-event {
  border-style: dashed !important;
  border-width: 2px !important;
}

/* Animation for cart update */
@keyframes cart-update-flash {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

.cart-update-flash {
  animation: cart-update-flash 1s ease-in-out;
}

.ghost-event-pulse {
  animation: ghostPulse 2s infinite ease-in-out;
}

.ghost-event-draft {
  border-style: dashed !important;
}

.ghost-event-pending {
  border-style: dotted !important;
}

/* Cart ghost event specific styling */
.cart-ghost-event {
  border-style: dotted !important;
  border-width: 2px !important;
  background-color: rgba(66, 133, 244, 0.3) !important;
  position: relative;
}



/* Modal ghost event specific styling */
.modal-ghost-event {
  border-style: dashed !important;
  border-width: 2px !important;
  background-color: rgba(251, 188, 5, 0.3) !important;
}

/* Enhance the pulse animation for ghost events */
@keyframes ghostPulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.01);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}

.ghost-event-pulse {
  animation: ghostPulse 2s infinite ease-in-out;
}

/* Cart ghost event specific styling */
/* Modal ghost event specific styling */
.modal-ghost-event {
  border-style: dashed !important;
  border-width: 2px !important;
  background-color: rgba(251, 188, 5, 0.3) !important;
}

/* Pulse animation for ghost events */
@keyframes ghostPulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.5;
  }
}

/* Add a blue highlight to cart items in the modal */
.cart-item-highlight {
  border-left: 4px solid #4285f4;
  background-color: rgba(66, 133, 244, 0.05);
}

/* Ensure ghost events are visible in all views */
.fc-timeline-event.ghost-event,
.fc-daygrid-event.ghost-event,
.fc-timegrid-event.ghost-event {
  display: block !important;
  visibility: visible !important;
}

/* Make ghost events stand out more */
.fc-event.ghost-event {
  background-color: rgba(66, 133, 244, 0.3) !important;
}

/* ===== DARK MODE ADJUSTMENTS ===== */
.dark .calendar-wrapper .fc {
  /* Ensure dark mode has visible grid lines with increased contrast */
  --fc-border-color: hsl(var(--border));
  --fc-page-bg-color: hsl(var(--background));
  --fc-neutral-bg-color: hsl(var(--muted));
  --fc-text-color: hsl(var(--foreground));
}

/* Improve contrast for time labels in dark mode */
.dark .calendar-wrapper .fc .fc-timegrid-axis-cushion,
.dark .calendar-wrapper .fc .fc-timegrid-slot-label-cushion {
  color: hsl(var(--foreground), 0.8);
}

/* Fix for time slot labels in day/week view */
.calendar-wrapper .fc-timegrid-axis-cushion,
.calendar-wrapper .fc-timegrid-slot-label-cushion {
  font-weight: 500;
  color: hsl(var(--foreground), 0.8);
}

/* Specific fix for the January 1970 issue in time labels */
.calendar-wrapper .fc-timegrid-axis-frame {
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendar-wrapper .fc-timegrid-axis-cushion {
  width: auto;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
  .calendar-wrapper .fc .fc-toolbar {
    flex-direction: column;
    gap: 0.5rem;
  }

  .calendar-wrapper .fc .fc-toolbar-chunk {
    display: flex;
    justify-content: center;
  }

  .calendar-wrapper .fc-event {
    font-size: 0.75rem;
  }
}

/* ===== ANIMATIONS ===== */
.calendar-wrapper .fc-view-harness {
  transition: height 0.2s ease;
}

.calendar-wrapper .fc-event {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
/* Ensure sufficient contrast for all text elements */
.calendar-wrapper .fc-col-header-cell-cushion,
.calendar-wrapper .fc-daygrid-day-number,
.calendar-wrapper .fc-event-title,
.calendar-wrapper .fc-event-time {
  color: inherit;
  text-decoration: none;
}

.fc .fc-scrollgrid-section-sticky > * {
  background-color: hsl(var(--primary-foreground), 0.1) !important;
}

/* Improve focus indicators for interactive elements */
.calendar-wrapper .fc-event:focus,
.calendar-wrapper .fc button:focus,
.calendar-wrapper .fc a:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Ensure proper contrast for disabled elements */
.calendar-wrapper .fc button:disabled {
  opacity: 0.6;
}

/* ===== TOOLTIP STYLING ===== */
.fc-tooltip {
  background-color: #1f2937;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  max-width: 300px;
  z-index: 1000;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.fc-tooltip::after {
  content: "";
  position: absolute;
  left: -6px;
  top: 10px;
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid #1f2937;
}

.fc-tooltip h4 {
  margin: 0 0 4px 0;
  font-weight: 600;
}

.fc-tooltip p {
  margin: 2px 0;
  opacity: 0.8;
}

.fc-tooltip .ghost-indicator {
  font-style: italic;
  color: #9ca3af;
  margin-top: 4px;
}

.fc-tooltip .cart-indicator {
  color: #60a5fa;
  font-weight: 500;
  margin-top: 4px;
}

/* Add a specific class for tooltips that should be hidden during drag */
.fc-event-dragging .fc-tooltip,
.fc-event-resizing .fc-tooltip,
.fc-tooltip.hidden {
  display: none !important;
}

/* Ensure consistent border colors */
.calendar-with-app-borders.fc .fc-theme-standard td,
.calendar-with-app-borders.fc .fc-theme-standard th,
.calendar-with-app-borders.fc .fc-scrollgrid,
.calendar-with-app-borders.fc .fc-scrollgrid-section,
.calendar-with-app-borders.fc .fc-scrollgrid-section > td,
.calendar-with-app-borders.fc .fc-scrollgrid-section > th,
.calendar-with-app-borders.fc .fc-scrollgrid-section-header,
.calendar-with-app-borders.fc .fc-scrollgrid-section-body,
.calendar-with-app-borders.fc .fc-scrollgrid-section-footer,
.calendar-with-app-borders.fc .fc-timegrid-divider,
.calendar-with-app-borders.fc .fc-timegrid-axis,
.calendar-with-app-borders.fc .fc-timegrid-slot,
.calendar-with-app-borders.fc .fc-timegrid-col,
.calendar-with-app-borders.fc .fc-col-header-cell,
.calendar-with-app-borders.fc .fc-daygrid-day,
.calendar-with-app-borders.fc .fc-timeline-slot,
.calendar-with-app-borders.fc .fc-timeline-lane,
.calendar-with-app-borders.fc .fc-resource-cell,
.calendar-with-app-borders.fc .fc-resource-area-divider,
.calendar-with-app-borders.fc .fc-timeline-header,
.calendar-with-app-borders.fc .fc-timeline-header-row th {
  border-color: hsl(var(--border)) !important;
}

/* Ensure dashed borders are also consistent */
.calendar-with-app-borders.fc .fc-timegrid-slot-minor,
.calendar-with-app-borders.fc .fc-timeline-slot-minor {
  border-color: hsl(var(--border)) !important;
  border-style: dashed !important;
}

/* Google Calendar inspired styles */
.calendar-wrapper {
  --google-blue: #4285f4;
  --google-red: #ea4335;
  --google-yellow: #fbbc05;
  --google-green: #34a853;
  --google-purple: #a142f4;
  --google-teal: #26c6da;
  --google-orange: #fb8c00;
  --google-pink: #ec407a;
}

/* Custom event colors */
.calendar-wrapper .event-blue {
  background-color: var(--google-blue) !important;
  border-color: var(--google-blue) !important;
  color: white !important;
}

.calendar-wrapper .event-red {
  background-color: var(--google-red) !important;
  border-color: var(--google-red) !important;
  color: white !important;
}

.calendar-wrapper .event-yellow {
  background-color: var(--google-yellow) !important;
  border-color: var(--google-yellow) !important;
  color: black !important;
}

.calendar-wrapper .event-green {
  background-color: var(--google-green) !important;
  border-color: var(--google-green) !important;
  color: white !important;
}

.calendar-wrapper .event-purple {
  background-color: var(--google-purple) !important;
  border-color: var(--google-purple) !important;
  color: white !important;
}

.calendar-wrapper .event-teal {
  background-color: var(--google-teal) !important;
  border-color: var(--google-teal) !important;
  color: white !important;
}

.calendar-wrapper .event-orange {
  background-color: var(--google-orange) !important;
  border-color: var(--google-orange) !important;
  color: white !important;
}

.calendar-wrapper .event-pink {
  background-color: var(--google-pink) !important;
  border-color: var(--google-pink) !important;
  color: white !important;
}

/* Update tooltip to show ghost status */
.fc-tooltip .ghost-status {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 4px;
  background-color: rgba(0, 0, 0, 0.1);
}

/* Ensure ghost events appear behind real events */
.fc-timeline-event.ghost-event {
  z-index: 1 !important;
}

.fc-timeline-event:not(.ghost-event) {
  z-index: 2 !important;
}

/* Cart update flash animation */
@keyframes cartUpdateFlash {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

.cart-update-flash {
  animation: cartUpdateFlash 1s ease-out;
  border: 2px solid #22c55e !important;
  z-index: 10 !important;
}

/* Grid lines enhancement */
.enhanced-grid-line {
  border-top: 1px solid rgba(229, 231, 235, 0.5) !important;
}

.enhanced-grid-line-hour {
  border-top: 1px solid rgba(209, 213, 219, 0.8) !important;
}

/* Resource area styling */
.venue-group-label {
  font-weight: 600 !important;
  background-color: rgba(243, 244, 246, 0.5) !important;
}

.resource-lane {
  border-bottom: 1px solid rgba(229, 231, 235, 0.5) !important;
}

.venue-group-lane {
  border-bottom: 1px solid rgba(209, 213, 219, 0.8) !important;
}

.resource-time-grid {
  border-right: 1px solid rgba(229, 231, 235, 0.5) !important;
}

.resource-area-header {
  font-weight: 600 !important;
}

/* Calendar with app borders */
.calendar-with-app-borders {
  border: 1px solid rgba(229, 231, 235, 0.8);
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Ensure event titles are visible */
.fc-event-title {
  font-weight: 500 !important;
  padding: 2px 4px !important;
}

/* Ensure time is visible */
.fc-event-time {
  padding: 2px 4px 0 4px !important;
  opacity: 0.8 !important;
}

/* Improve event styling */
.fc-event {
  border-radius: 4px !important;
  overflow: hidden !important;
}

/* Google Calendar-like event styling */
.google-calendar-event {
  border-left-width: 5px !important;
}

/* Improve today highlighting */
.fc-day-today {
  background-color: rgba(59, 130, 246, 0.05) !important;
}

/* Improve header styling */
.fc-col-header-cell {
  background-color: rgba(243, 244, 246, 0.5) !important;
  font-weight: 600 !important;
}

/* Improve slot label styling */
.fc-timegrid-slot-label-cushion,
.fc-timegrid-axis-cushion {
  font-weight: 500 !important;
  color: #6b7280 !important;
}

/* Improve resource header styling */
.fc-resource-timeline .fc-resource-group {
  background-color: rgba(243, 244, 246, 0.5) !important;
}

/* Add this style to the end of the file */

/* Highlight pulse animation for locating events */
@keyframes highlightPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 15px rgba(59, 130, 246, 0.4);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    transform: scale(1);
  }
}

.event-highlight-pulse {
  animation: highlightPulse 1.5s ease-out infinite;
  z-index: 100 !important;
  position: relative;
  filter: brightness(1.2) !important;
}

/* Add a prominent border for highlighted events */
.event-highlight-border {
  border: 3px solid #3b82f6 !important;
  outline: 2px dashed rgba(59, 130, 246, 0.7) !important;
  outline-offset: 2px !important;
}

/* Add a location marker animation */
.event-location-marker {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background-color: #3b82f6;
  border-radius: 50%;
  z-index: 101;
  animation: markerPulse 1.5s ease-out infinite;
}

@keyframes markerPulse {
  0% {
    transform: translateX(-50%) scale(0.8);
    opacity: 1;
  }
  50% {
    transform: translateX(-50%) scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: translateX(-50%) scale(0.8);
    opacity: 1;
  }
}

/* Add status-based styling for events */
.fc-event[data-status="Inquiry"] {
  border-right: 3px solid rgb(168, 85, 247) !important;
}

.fc-event[data-status="Prospect"] {
  border-right: 3px solid rgb(236, 72, 153) !important; 
}

.fc-event[data-status="Tentative"] {
  border-right: 3px solid rgb(245, 158, 11) !important;
}

.fc-event[data-status="Definite"] {
  border-right: 3px solid rgb(34, 197, 94) !important;
}

.fc-event[data-status="Lost"] {
  border-right: 3px solid rgb(239, 68, 68) !important;
  opacity: 0.7;
}

.fc-event[data-status="Canceled"] {
  border-right: 3px solid rgb(107, 114, 128) !important;
  opacity: 0.7;
  text-decoration: line-through;
}

.fc-event[data-status="Hold"] {
  border-right: 3px solid rgb(6, 182, 212) !important;
  border-style: dashed !important;
}

/* Status badge styles in tooltip */
.status-badge {
  display: inline-block;
  padding: 2px 8px;
  margin-top: 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Status indicator icons */
.fc-event[data-status="Inquiry"]::before {
  content: "?";
  background-color: rgb(168, 85, 247);
  color: white;
  position: absolute;
  top: 0;
  right: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.fc-event[data-status="Hold"]::before {
  content: "⌛";
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 10px;
}

