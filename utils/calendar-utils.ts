/**
 * Utility functions for calendar operations and styling
 */
import type { ViewType, TimeSlotInterval } from "@/types/calendar"
import type { TimelineDuration } from "@/components/timeline-toolbar"
import { format, addDays } from "date-fns"

/**
 * Converts a time slot interval to a FullCalendar duration string
 * @param interval - The time slot interval
 * @returns A duration string in the format "HH:MM:SS"
 */
export function getSlotDurationFromInterval(interval: TimeSlotInterval): string {
  switch (interval) {
    case "15min":
      return "00:15:00"
    case "30min":
      return "00:30:00"
    case "1hour":
      return "01:00:00"
    case "2hours":
      return "02:00:00"
    case "4hours":
      return "04:00:00"
    case "8hours":
      return "08:00:00"
    case "1day":
      return "24:00:00"
    default:
      return "01:00:00"
  }
}

/**
 * Gets the snap duration based on time slot interval
 * For larger intervals, we want a smaller snap duration for more precise event positioning
 * @param interval - The time slot interval
 * @returns A snap duration string in the format "HH:MM:SS"
 */
export function getSnapDurationFromInterval(interval: TimeSlotInterval): string {
  switch (interval) {
    case "15min":
      return "00:15:00"
    case "30min":
      return "00:15:00"
    case "1hour":
      return "00:15:00"
    case "2hours":
      return "00:30:00"
    case "4hours":
      return "01:00:00"
    case "8hours":
      return "01:00:00"
    case "1day":
      return "01:00:00"
    default:
      return "00:15:00"
  }
}

/**
 * Formats a date range for the timeline toolbar based on the current duration
 * @param currentDate - The current date
 * @param timelineDuration - The timeline duration
 * @returns A formatted date range string
 */
export function formatDateRangeForTimeline(currentDate: Date, timelineDuration: TimelineDuration): string {
  if (!(currentDate instanceof Date) || isNaN(currentDate.getTime())) {
    return "Invalid date range"
  }

  // For timeline view, show a range based on the current date and duration
  const startDate = new Date(currentDate)
  let endDate = new Date(currentDate)

  // Set the end date based on the selected duration
  switch (timelineDuration) {
    case "day":
      // For a single day, just show that day
      return format(startDate, "MMM d, yyyy")
    case "threeDays":
      endDate = addDays(startDate, 2)
      break
    case "week":
      endDate = addDays(startDate, 6)
      break
    case "twoWeeks":
      endDate = addDays(startDate, 13)
      break
    case "month":
      // For month view, show the month name and year
      return format(startDate, "MMMM yyyy")
  }

  // Format as "Mar 27 – Apr 5, 2025"
  return `${format(startDate, "MMM d")} – ${format(endDate, "MMM d, yyyy")}`
}

/**
 * Formats a date for the header based on the current view
 * @param currentDate - The current date
 * @param view - The current view type
 * @returns A formatted date string
 */
export function formatHeaderDate(currentDate: Date, view: ViewType): string {
  if (!(currentDate instanceof Date) || isNaN(currentDate.getTime())) {
    return "Invalid date"
  }

  switch (view) {
    case "day":
      return format(currentDate, "EEEE, MMMM d, yyyy") // e.g., Wednesday, July 12, 2023
    case "week": {
      const startOfWeek = new Date(currentDate)
      const endOfWeek = addDays(startOfWeek, 6)
      return `${format(startOfWeek, "MMM d")} - ${format(endOfWeek, "MMM d, yyyy")}` // e.g., Jul 9 - Jul 15, 2023
    }
    case "month":
      return format(currentDate, "MMMM yyyy") // e.g., July 2023
    default:
      return format(currentDate, "MMMM d, yyyy")
  }
}

/**
 * Determines text color based on background color for optimal contrast
 * @param hexColor - The background color in hex format
 * @returns Black or white color for optimal contrast
 */
export function getContrastColor(hexColor: string): string {
  // Convert hex to RGB
  const r = Number.parseInt(hexColor.slice(1, 3), 16)
  const g = Number.parseInt(hexColor.slice(3, 5), 16)
  const b = Number.parseInt(hexColor.slice(5, 7), 16)

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

  // Return black or white based on luminance
  return luminance > 0.5 ? "#000000" : "#FFFFFF"
}

/**
 * Converts our view types to FullCalendar view types
 * @param viewType - Our application view type
 * @param timelineDuration - The timeline duration (for timeline view)
 * @returns The corresponding FullCalendar view type
 */
export function getFullCalendarViewType(viewType: ViewType, timelineDuration: TimelineDuration): string {
  switch (viewType) {
    case "day":
      return "timeGridDay"
    case "week":
      return "timeGridWeek"
    case "month":
      return "dayGridMonth"
    case "timeline":
      // For timeline, use the current timeline duration
      switch (timelineDuration) {
        case "day":
          return "resourceTimelineDay"
        case "threeDays":
          return "resourceTimelineThreeDays"
        case "week":
          return "resourceTimelineWeek"
        case "twoWeeks":
          return "resourceTimelineTwoWeeks"
        case "month":
          return "resourceTimelineMonth"
      }
    default:
      return "timeGridWeek"
  }
}

/**
 * Converts FullCalendar view types to our application view types
 * @param fcViewType - The FullCalendar view type
 * @returns Our application view type
 */
export function getAppViewType(fcViewType: string): ViewType {
  if (fcViewType.startsWith("resourceTimeline")) {
    return "timeline"
  }

  switch (fcViewType) {
    case "timeGridDay":
      return "day"
    case "timeGridWeek":
      return "week"
    case "dayGridMonth":
      return "month"
    default:
      return "week"
  }
}

/**
 * Gets view-specific slot configuration for FullCalendar
 * @param view - The current view type
 * @param timeSlotInterval - The time slot interval
 * @returns Configuration for slots in the current view
 */
export function getViewSpecificSlotConfig(view: ViewType, timeSlotInterval: TimeSlotInterval) {
  // For day and week views, always use 15-minute slots regardless of timeline preferences
  if (view === "day" || view === "week") {
    return {
      slotDuration: "00:15:00",
      slotLabelInterval: "01:00:00", // Show hour labels
      snapDuration: "00:15:00",
      slotLabelFormat: {
        hour: "numeric",
        minute: "2-digit",
        omitZeroMinute: false,
        meridiem: "short",
      },
    }
  }

  // For timeline view, use the user-selected interval
  return {
    slotDuration: getSlotDurationFromInterval(timeSlotInterval),
    slotLabelInterval: getSlotDurationFromInterval(timeSlotInterval),
    snapDuration: getSnapDurationFromInterval(timeSlotInterval),
    slotLabelFormat: [
      { month: "long", year: "numeric" }, // top level of time axis
      { weekday: "short", day: "numeric", month: "numeric" }, // lower level of time axis
    ],
  }
}

/**
 * Enhances grid lines and styling for the calendar
 * @param calendarElement - The FullCalendar element
 * @param view - The current view type
 * @param timeSlotInterval - The time slot interval
 */
export function enhanceGridLines(
  calendarElement: HTMLElement | null,
  view: ViewType,
  timeSlotInterval: TimeSlotInterval,
) {
  if (!calendarElement) return

  // Apply consistent border styling to all calendar elements
  applyConsistentBorders(calendarElement)

  // Apply grid lines based on the current view
  if (view === "timeline") {
    // Timeline view specific enhancements
    enhanceTimelineView(calendarElement, timeSlotInterval)
  } else if (view === "day" || view === "week") {
    // Time grid view specific enhancements
    enhanceTimeGridView(calendarElement)
  } else if (view === "month") {
    // Month view specific enhancements
    enhanceMonthView(calendarElement)
  }

  // Common enhancements for all views
  enhanceEvents(calendarElement)
}

/**
 * Applies consistent border styling to all calendar elements
 * @param calendarElement - The FullCalendar element
 */
function applyConsistentBorders(calendarElement: HTMLElement) {
  // Get all elements with borders in the calendar
  const borderElements = calendarElement.querySelectorAll(
    ".fc-theme-standard td, .fc-theme-standard th, .fc-scrollgrid-sync-inner, .fc-scrollgrid-section, .fc-scrollgrid-section-header, .fc-scrollgrid-section-body, .fc-scrollgrid-section-footer",
  )

  // Apply consistent border color
  borderElements.forEach((element) => {
    ;(element as HTMLElement).style.borderColor = "hsl(var(--border))"
  })

  // Ensure all dividers use the same border color
  const dividers = calendarElement.querySelectorAll(".fc-divider, .fc-cell-shaded, .fc-list-day-cushion")
  dividers.forEach((divider) => {
    ;(divider as HTMLElement).style.borderColor = "hsl(var(--border))"
  })
}

/**
 * Enhances timeline view with specific styling
 * @param calendarElement - The FullCalendar element
 * @param timeSlotInterval - The time slot interval
 */
function enhanceTimelineView(calendarElement: HTMLElement, timeSlotInterval: TimeSlotInterval) {
  // Ensure timeline slots have visible borders
  const timelineSlots = calendarElement.querySelectorAll(".fc-timeline-slot")
  timelineSlots.forEach((slot) => {
    ;(slot as HTMLElement).style.borderRight = "1px solid hsl(var(--border))"
  })

  // Ensure timeline lanes have visible borders
  const timelineLanes = calendarElement.querySelectorAll(".fc-timeline-lane")
  timelineLanes.forEach((lane) => {
    ;(lane as HTMLElement).style.borderBottom = "1px solid hsl(var(--border))"
    ;(lane as HTMLElement).style.height = "40px"
  })

  // Ensure resource cells have visible borders
  const resourceCells = calendarElement.querySelectorAll(".fc-resource-cell")
  resourceCells.forEach((cell) => {
    ;(cell as HTMLElement).style.border = "1px solid hsl(var(--border))"
  })

  // Ensure timeline header cells have visible borders
  const timelineHeaderCells = calendarElement.querySelectorAll(".fc-timeline-header-row th")
  timelineHeaderCells.forEach((cell) => {
    ;(cell as HTMLElement).style.borderBottom = "1px solid hsl(var(--border))"
    ;(cell as HTMLElement).style.borderRight = "1px solid hsl(var(--border))"
  })

  // Add a class to the timeline based on the time slot interval
  const timelineEl = calendarElement.querySelector(".fc-timeline-body")
  if (timelineEl) {
    // Remove any existing time slot interval classes
    timelineEl.classList.remove(
      "time-slot-15min",
      "time-slot-30min",
      "time-slot-1hour",
      "time-slot-2hours",
      "time-slot-4hours",
      "time-slot-8hours",
      "time-slot-1day",
    )

    // Add the current time slot interval class
    timelineEl.classList.add(`time-slot-${timeSlotInterval}`)
  }

  // Add this new code to highlight the current day in the timeline header
  // Find the current day slot in the timeline header
  const today = new Date()
  const currentDaySlots = calendarElement.querySelectorAll(".fc-timeline-slot.fc-timeline-slot-current")
  currentDaySlots.forEach((slot) => {
    // Add a class to style with CSS
    slot.classList.add("fc-timeline-slot-current")

    // For the day header row (second row), add the dashed circle effect
    const headerRow = slot.closest(".fc-timeline-header-row:not(:first-child)")
    if (headerRow) {
      ;(slot as HTMLElement).style.position = "relative"
      ;(slot as HTMLElement).style.backgroundColor = "hsl(var(--primary-foreground), 0.1)"
      ;(slot as HTMLElement).style.fontWeight = "600"
    }
  })
}

/**
 * Enhances time grid view with specific styling
 * @param calendarElement - The FullCalendar element
 */
function enhanceTimeGridView(calendarElement: HTMLElement) {
  // Ensure time grid columns have visible borders
  const timeGridCols = calendarElement.querySelectorAll(".fc-timegrid-col")
  timeGridCols.forEach((col) => {
    ;(col as HTMLElement).style.borderLeft = "1px solid hsl(var(--border))"
    ;(col as HTMLElement).style.borderRight = "1px solid hsl(var(--border))"
  })

  // Ensure time grid slots have visible borders
  const timeGridSlots = calendarElement.querySelectorAll(".fc-timegrid-slot")
  timeGridSlots.forEach((slot) => {
    ;(slot as HTMLElement).style.borderBottom = "1px solid hsl(var(--border))"
  })

  // Ensure time grid minor slots have dashed borders
  const timeGridMinorSlots = calendarElement.querySelectorAll(".fc-timegrid-slot-minor")
  timeGridMinorSlots.forEach((slot) => {
    ;(slot as HTMLElement).style.borderBottom = "1px dashed hsl(var(--border))"
  })

  // Ensure header cells have visible borders
  const headerCells = calendarElement.querySelectorAll(".fc-col-header-cell")
  headerCells.forEach((cell) => {
    ;(cell as HTMLElement).style.border = "1px solid hsl(var(--border))"
  })

  // Fix the time slot labels in day/week view
  const timeLabels = calendarElement.querySelectorAll(".fc-timegrid-axis-cushion")
  timeLabels.forEach((label) => {
    const text = label.textContent || ""
    // Check if the label contains "January 1970" which indicates the issue
    if (text.includes("January 1970")) {
      // Extract just the time part if possible
      const timePart = text.match(/\d{1,2}:\d{2}(?: [AP]M)?/)
      if (timePart) {
        label.textContent = timePart[0]
      } else {
        // If we can't extract the time, use a more aggressive approach
        // Parse the time from the element's data attributes or position
        const slotEl = label.closest(".fc-timegrid-slot-label")
        if (slotEl) {
          const slotIndex = Array.from(slotEl.parentElement?.children || []).indexOf(slotEl as Element)
          if (slotIndex >= 0) {
            // Calculate the time based on slot index (assuming 24-hour day starting at 00:00)
            const hour = Math.floor(slotIndex / 4) // Assuming 15-minute intervals (4 slots per hour)
            const minute = (slotIndex % 4) * 15

            // Format the time in 12-hour format with AM/PM
            const period = hour >= 12 ? "PM" : "AM"
            const hour12 = hour % 12 || 12 // Convert 0 to 12 for 12 AM
            label.textContent = `${hour12}:${minute.toString().padStart(2, "0")} ${period}`
          }
        }
      }
    }
  })

  // Ensure axis border is visible
  const timeGridAxis = calendarElement.querySelector(".fc-timegrid-axis")
  if (timeGridAxis) {
    ;(timeGridAxis as HTMLElement).style.borderRight = "1px solid hsl(var(--border))"
    ;(timeGridAxis as HTMLElement).style.borderBottom = "1px solid hsl(var(--border))"
  }

  // Ensure time grid divider is visible
  const timeGridDivider = calendarElement.querySelector(".fc-timegrid-divider")
  if (timeGridDivider) {
    ;(timeGridDivider as HTMLElement).style.borderBottom = "1px solid hsl(var(--border))"
  }
}

/**
 * Enhances month view with specific styling
 * @param calendarElement - The FullCalendar element
 */
function enhanceMonthView(calendarElement: HTMLElement) {
  // Ensure day grid cells have visible borders
  const dayGridCells = calendarElement.querySelectorAll(".fc-daygrid-day")
  dayGridCells.forEach((cell) => {
    ;(cell as HTMLElement).style.border = "1px solid hsl(var(--border))"
    ;(cell as HTMLElement).style.minHeight = "100px"
  })

  // Style today's cell
  const todayCell = calendarElement.querySelector(".fc-day-today")
  if (todayCell) {
    ;(todayCell as HTMLElement).style.backgroundColor = "var(--fc-today-bg-color)"
  }

  // Style day numbers
  const dayNumbers = calendarElement.querySelectorAll(".fc-daygrid-day-number")
  dayNumbers.forEach((number) => {
    ;(number as HTMLElement).style.padding = "0.5rem"
    ;(number as HTMLElement).style.fontWeight = "500"
    ;(number as HTMLElement).style.fontSize = "0.875rem"
  })

  // Style today's day number
  const todayNumber = calendarElement.querySelector(".fc-day-today .fc-daygrid-day-number")
  if (todayNumber) {
    ;(todayNumber as HTMLElement).style.backgroundColor = "hsl(var(--primary))"
    ;(todayNumber as HTMLElement).style.color = "hsl(var(--primary-foreground))"
    ;(todayNumber as HTMLElement).style.borderRadius = "50%"
    ;(todayNumber as HTMLElement).style.width = "24px"
    ;(todayNumber as HTMLElement).style.height = "24px"
    ;(todayNumber as HTMLElement).style.display = "flex"
    ;(todayNumber as HTMLElement).style.alignItems = "center"
    ;(todayNumber as HTMLElement).style.justifyContent = "center"
    ;(todayNumber as HTMLElement).style.margin = "2px"
  }
}

/**
 * Enhances events with Google Calendar-like styling
 * @param calendarElement - The FullCalendar element
 */
function enhanceEvents(calendarElement: HTMLElement) {
  // Style all events
  const events = calendarElement.querySelectorAll(".fc-event")
  events.forEach((event) => {
    ;(event as HTMLElement).style.borderRadius = "4px"
    ;(event as HTMLElement).style.padding = "2px 4px"
    ;(event as HTMLElement).style.boxShadow = "0 1px 2px rgba(0, 0, 0, 0.1)"
    ;(event as HTMLElement).style.border = "none"
    ;(event as HTMLElement).style.margin = "1px 2px"
  })

  // Style timeline events
  const timelineEvents = calendarElement.querySelectorAll(".fc-timeline-event")
  timelineEvents.forEach((event) => {
    ;(event as HTMLElement).style.borderRadius = "4px"
    ;(event as HTMLElement).style.border = "none"
    ;(event as HTMLElement).style.boxShadow = "0 1px 2px rgba(0, 0, 0, 0.1)"
    ;(event as HTMLElement).style.height = "96px"
    ;(event as HTMLElement).style.marginTop = "5px"
  })
}

/**
 * Creates a tooltip element for an event
 * @param title - The event title
 * @param start - The event start time
 * @param end - The event end time
 * @param roomName - The room name
 * @param venueName - The venue name
 * @param description - The event description
 * @param isGhost - Whether this is a ghost event
 * @param isCartGhost - Whether this is a cart-related ghost event
 * @param status - The booking status
 * @param holdRank - The hold rank for Hold status
 * @returns HTML string for the tooltip
 */
export function createEventTooltip(
  title: string,
  start: Date | null,
  end: Date | null,
  roomName: string,
  venueName: string,
  description?: string,
  isGhost?: boolean,
  isCartGhost?: boolean,
  status?: string,
  holdRank?: number,
): string {
  // Get appropriate status styling
  let statusElement = ''
  
  if (isGhost) {
    statusElement = isCartGhost
      ? `<div class="ghost-status bg-blue-100 text-blue-800 rounded px-2 py-1 mt-2 text-xs font-medium">Cart Item - Not Confirmed</div>`
      : `<div class="ghost-status bg-yellow-100 text-yellow-800 rounded px-2 py-1 mt-2 text-xs font-medium">Preview - Not Confirmed</div>`
  } else if (status) {
    // Define color mappings for each status
    const statusColors: Record<string, { bg: string, text: string }> = {
      "Inquiry": { bg: "bg-purple-100", text: "text-purple-800" },
      "Prospect": { bg: "bg-pink-100", text: "text-pink-800" },
      "Tentative": { bg: "bg-amber-100", text: "text-amber-800" },
      "Definite": { bg: "bg-green-100", text: "text-green-800" },
      "Lost": { bg: "bg-red-100", text: "text-red-800" },
      "Canceled": { bg: "bg-gray-100", text: "text-gray-800" },
      "Hold": { bg: "bg-cyan-100", text: "text-cyan-800" },
    }
    
    // Get styles for this status
    const style = statusColors[status] || { bg: "bg-blue-100", text: "text-blue-800" }
    
    // For Hold status, include rank
    const statusText = status === "Hold" && holdRank 
      ? `${status} - ${holdRank}` 
      : status
      
    statusElement = `<div class="status-badge ${style.bg} ${style.text} rounded px-2 py-1 mt-2 text-xs font-medium">${statusText}</div>`
  }

  return `
    <div class="p-2 bg-base-100 shadow-lg rounded-md border border-base-300 text-sm text-foreground">
      <div class="font-bold text-foreground">${title}</div>
      <div>${start?.toLocaleTimeString() || ""} - ${end?.toLocaleTimeString() || ""}</div>
      <div>${roomName} ${venueName ? `(${venueName})` : ""}</div>
      ${description ? `<div class="mt-1">${description}</div>` : ""}
      ${statusElement}
    </div>
  `
}

/**
 * Determines the new timeline duration based on the FullCalendar view type
 * @param fcViewType - The FullCalendar view type
 * @returns The corresponding timeline duration
 */
export function getTimelineDurationFromViewType(fcViewType: string): TimelineDuration {
  switch (fcViewType) {
    case "resourceTimelineDay":
      return "day"
    case "resourceTimelineThreeDays":
      return "threeDays"
    case "resourceTimelineWeek":
      return "week"
    case "resourceTimelineTwoWeeks":
      return "twoWeeks"
    case "resourceTimelineMonth":
      return "month"
    default:
      return "week"
  }
}

