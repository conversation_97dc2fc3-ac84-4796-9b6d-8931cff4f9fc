{"name": "cs-event-services", "version": "12.5.81", "private": true, "workspaces": ["apps/*", "packages/*", "services/*"], "scripts": {"build": "turbo run build", "build-web": "turbo run build-web", "dev:exhibitor": "cd apps/web && next dev", "dev:scan": "cd apps/scan-pwa && next dev", "dev-spa": "turbo run dev-spa --parallel", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "test": "turbo run test", "ngrok": "cd packages/eslint-config/runConfigs && bash startNgrok.sh"}, "devDependencies": {"@oracle/suitecloud-unit-testing": "^1.6.0", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "next-auth": "4.24.7", "prettier": "^3.2.5", "turbo": "^2.1.2"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4", "dependencies": {"date-fns": "^3.6.0"}}