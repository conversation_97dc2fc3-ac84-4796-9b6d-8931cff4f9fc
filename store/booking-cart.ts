import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { v4 as uuidv4 } from "uuid"
import type { Booking, Room } from "@/types/calendar"

export interface CartItem {
  id: string
  booking: Booking
  room: Room
  addedAt: Date
}

interface BookingCartState {
  items: CartItem[]
  addItem: (bookingOrCartItem: Booking | CartItem, room?: Room) => void
  updateItem: (id: string, updatedItem: CartItem) => void
  removeItem: (id: string) => void
  clearCart: () => void
  isOpen: boolean
  toggleCart: () => void
  setCartOpen: (open: boolean) => void
}

// Helper function to ensure a date is valid
function ensureValidDate(date: any): Date {
  if (date instanceof Date && !isNaN(date.getTime())) {
    return date
  }

  // Try to parse string dates
  if (typeof date === "string") {
    const parsed = new Date(date)
    if (!isNaN(parsed.getTime())) {
      return parsed
    }
  }

  // Default to current time if invalid
  return new Date()
}

export const useBookingCart = create<BookingCartState>()(
  persist(
    (set, get) => ({
      items: [],
      isOpen: false,
      addItem: (bookingOrCartItem, room) => {
        // Check if the first argument is a CartItem or a Booking
        if ("booking" in bookingOrCartItem && "room" in bookingOrCartItem) {
          // It's a CartItem
          const cartItem = bookingOrCartItem as CartItem

          // Ensure dates are valid Date objects
          const validatedCartItem = {
            ...cartItem,
            booking: {
              ...cartItem.booking,
              start: ensureValidDate(cartItem.booking.start),
              end: ensureValidDate(cartItem.booking.end),
            },
            addedAt: ensureValidDate(cartItem.addedAt),
          }

          set((state) => ({
            items: [...state.items, validatedCartItem],
            isOpen: true, // Open cart when item is added
          }))
        } else if (room) {
          // It's a Booking with a Room
          const booking = bookingOrCartItem as Booking

          // Ensure dates are valid Date objects
          const validatedBooking = {
            ...booking,
            start: ensureValidDate(booking.start),
            end: ensureValidDate(booking.end),
          }

          const cartItem: CartItem = {
            id: uuidv4(),
            booking: validatedBooking,
            room,
            addedAt: new Date(),
          }

          set((state) => ({
            items: [...state.items, cartItem],
            isOpen: true, // Open cart when item is added
          }))
        } else {
          console.error("Invalid arguments to addItem: expected a CartItem or a Booking with a Room")
        }
      },
      updateItem: (id: string, updatedItem: CartItem) => {
        // Ensure dates are valid Date objects
        const validatedItem = {
          ...updatedItem,
          booking: {
            ...updatedItem.booking,
            start: ensureValidDate(updatedItem.booking.start),
            end: ensureValidDate(updatedItem.booking.end),
          },
          addedAt: ensureValidDate(updatedItem.addedAt),
        }

        set((state) => ({
          items: state.items.map((item) => (item.id === id ? validatedItem : item)),
        }))
      },
      removeItem: (id: string) => {
        set((state) => ({
          items: state.items.filter((item) => item.id !== id),
        }))
      },
      clearCart: () => set({ items: [] }),
      toggleCart: () => set((state) => ({ isOpen: !state.isOpen })),
      setCartOpen: (open: boolean) => set({ isOpen: open }),
    }),
    {
      name: "booking-cart-storage",
      storage: createJSONStorage(() => sessionStorage),
    },
  ),
)

// Function to initialize the store from localStorage on client side
export function hydrateBookingCart() {
  if (typeof window !== "undefined") {
    try {
      // First rehydrate the store
      useBookingCart.persist.rehydrate();
      
      // Then ensure all date strings are converted to Date objects
      const currentState = useBookingCart.getState();
      
      if (currentState.items.length > 0) {
        // Convert date strings to Date objects
        const itemsWithDates = currentState.items.map(item => ({
          ...item,
          booking: {
            ...item.booking,
            start: typeof item.booking.start === 'string' ? new Date(item.booking.start) : 
                  item.booking.start instanceof Date ? item.booking.start : new Date(),
            end: typeof item.booking.end === 'string' ? new Date(item.booking.end) : 
                item.booking.end instanceof Date ? item.booking.end : 
                new Date(typeof item.booking.start === 'string' ? new Date(item.booking.start).getTime() + 3600000 : 
                        item.booking.start instanceof Date ? item.booking.start.getTime() + 3600000 : 
                        new Date().getTime() + 3600000),
          },
          addedAt: typeof item.addedAt === 'string' ? new Date(item.addedAt) : 
                  item.addedAt instanceof Date ? item.addedAt : new Date()
        }));
        
        // Update the state with properly formatted dates
        useBookingCart.setState({ items: itemsWithDates });
      }
      
      console.log("Cart hydrated with", useBookingCart.getState().items.length, "items");
    } catch (error) {
      console.error("Error hydrating cart:", error);
    }
  }
}

