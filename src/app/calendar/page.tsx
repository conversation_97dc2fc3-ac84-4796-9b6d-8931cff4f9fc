"use client"

import { useEffect } from "react"
import { BookingCart } from "src/components/booking-cart"
import FullCalendarWrapper from "src/components/calendar/FullCalendar"
import { CalendarHeader } from "src/components/calendar-header"
import { CalendarSidebar } from "src/components/calendar-sidebar"
import { CalendarProvider } from "src/context/calendar-context"

export default function CalendarPage() {
  return (
    <CalendarProvider>
      <div className="flex h-screen flex-col overflow-hidden bg-background">
        <CalendarHeader />
        <div className="flex flex-1 overflow-hidden">
          <CalendarSidebar />
          <main className="flex-1 overflow-auto">
            <FullCalendarWrapper />
          </main>
        </div>
        <BookingCart />
      </div>
    </CalendarProvider>
  )
}
