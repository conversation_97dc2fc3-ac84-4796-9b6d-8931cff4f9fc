@import "./fullcalendar-styles.css";
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 210 6% 12%;

    --card: 0 0% 100%;
    --card-foreground: 210 6% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 6% 12%;

    --primary: 217 89% 51%;
    --primary-foreground: 0 0% 100%;

    --secondary: 137 54% 43%;
    --secondary-foreground: 0 0% 100%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    --accent: 45 100% 51%;
    --accent-foreground: 0 0% 0%;

    --destructive: 4 90% 58%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 89% 51%;

    --radius: 0.5rem;

    /* Calendar specific colors */
    --calendar-today: 217 89% 51%;
    --calendar-hover: 217 89% 95%;
    --calendar-selected: 217 89% 90%;
    --calendar-border: 220 13% 91%;
    --calendar-available: 220 14% 96%;
    --calendar-booked: 137 54% 91%;
  }

  .dark {
    --background: 210 6% 12%;
    --foreground: 0 0% 91%;

    --card: 210 6% 12%;
    --card-foreground: 0 0% 91%;

    --popover: 210 6% 12%;
    --popover-foreground: 0 0% 91%;

    --primary: 213 94% 76%;
    --primary-foreground: 210 6% 12%;

    --secondary: 142 43% 55%;
    --secondary-foreground: 210 6% 12%;

    --muted: 215 14% 20%;
    --muted-foreground: 217 9% 64%;

    --accent: 45 100% 69%;
    --accent-foreground: 210 6% 12%;

    --destructive: 0 73% 74%;
    --destructive-foreground: 210 6% 12%;

    --border: 215 14% 25%;
    --input: 215 14% 25%;
    --ring: 213 94% 76%;

    /* Calendar specific colors */
    --calendar-today: 213 94% 76%;
    --calendar-hover: 217 89% 20%;
    --calendar-selected: 217 89% 25%;
    --calendar-border: 220 13% 23%;
    --calendar-available: 220 14% 16%;
    --calendar-booked: 142 43% 20%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Google Calendar inspired styles */
.calendar-wrapper {
  --google-blue: #4285f4;
  --google-red: #ea4335;
  --google-yellow: #fbbc05;
  --google-green: #34a853;
  --google-purple: #a142f4;
  --google-teal: #26c6da;
  --google-orange: #fb8c00;
  --google-pink: #ec407a;
}

/* Custom event colors */
.calendar-wrapper .event-blue {
  background-color: var(--google-blue) !important;
  border-color: var(--google-blue) !important;
  color: white !important;
}

.calendar-wrapper .event-red {
  background-color: var(--google-red) !important;
  border-color: var(--google-red) !important;
  color: white !important;
}

.calendar-wrapper .event-yellow {
  background-color: var(--google-yellow) !important;
  border-color: var(--google-yellow) !important;
  color: black !important;
}

.calendar-wrapper .event-green {
  background-color: var(--google-green) !important;
  border-color: var(--google-green) !important;
  color: white !important;
}

.calendar-wrapper .event-purple {
  background-color: var(--google-purple) !important;
  border-color: var(--google-purple) !important;
  color: white !important;
}

.calendar-wrapper .event-teal {
  background-color: var(--google-teal) !important;
  border-color: var(--google-teal) !important;
  color: white !important;
}

.calendar-wrapper .event-orange {
  background-color: var(--google-orange) !important;
  border-color: var(--google-orange) !important;
  color: white !important;
}

.calendar-wrapper .event-pink {
  background-color: var(--google-pink) !important;
  border-color: var(--google-pink) !important;
  color: white !important;
}

/* Google Calendar-inspired tooltip */
.fc-tooltip {
  position: absolute;
  z-index: 9999;
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 8px;
  max-width: 300px;
  pointer-events: none;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom indeterminate checkbox styling */
.custom-checkbox-wrapper {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.custom-indeterminate {
  position: relative;
}

.custom-indeterminate::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 2px;
  background-color: currentColor;
}

