import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Alternative implementation if the above doesn't work
export function classNames(...classes: (string | boolean | undefined | null | Record<string, boolean>)[]) {
  return classes
    .filter(Boolean)
    .map((cls) => {
      if (typeof cls === "object") {
        return Object.entries(cls)
          .filter(([_, value]) => Boolean(value))
          .map(([key]) => key)
          .join(" ")
      }
      return cls
    })
    .join(" ")
}

