"use client"

import { useCalendar } from "src/context/calendar-context"
import { Checkbox } from "src/components/ui/checkbox"
import { Input } from "src/components/ui/input"
import { Search, ChevronDown, ChevronRight, Building2, DoorClosed, ChevronLeft, Minus } from "lucide-react"
import { useState, useMemo, useEffect, useRef } from "react"
import { addDays } from "src/utils/date-utils"
import { MiniCalendar } from "./mini-calendar"
import { Card, CardContent, CardHeader } from "src/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "src/components/ui/avatar"
import { cn } from "src/lib/utils"

export function CalendarSidebar() {
  const {
    venues,
    rooms,
    toggleVenueSelection,
    toggleRoomSelection,
    toggleVenueExpansion,
    currentDate,
    setCurrentDate,
  } = useCalendar()

  const [searchTerm, setSearchTerm] = useState("")
  // Track checkbox refs to update indeterminate state
  const checkboxRefs = useRef<Record<string, HTMLInputElement | null>>({})

  // Filter venues and rooms based on search term
  const filteredData = useMemo(() => {
    if (!searchTerm.trim()) {
      return { venues, rooms }
    }

    const term = searchTerm.toLowerCase()

    // Filter rooms first
    const matchedRooms = rooms.filter((room) => room.name.toLowerCase().includes(term))

    // Get venue IDs that have matching rooms
    const venueIdsWithMatchingRooms = [...new Set(matchedRooms.map((room) => room.venueId))]

    // Filter venues that match directly or have matching rooms
    const matchedVenues = venues.filter(
      (venue) => venue.name.toLowerCase().includes(term) || venueIdsWithMatchingRooms.includes(venue.id)
    )

    return {
      venues: matchedVenues,
      rooms: matchedRooms,
    }
  }, [venues, rooms, searchTerm])

  // Get rooms for a specific venue
  const getRoomsForVenue = (venueId: string) => {
    return filteredData.rooms.filter((room) => room.venueId === venueId)
  }

  // Check if all rooms in a venue are selected
  const areAllRoomsSelected = (venueId: string) => {
    const venueRooms = rooms.filter((room) => room.venueId === venueId)
    return venueRooms.length > 0 && venueRooms.every((room) => room.isSelected)
  }

  // Check if some (but not all) rooms in a venue are selected
  const areSomeRoomsSelected = (venueId: string) => {
    const venueRooms = rooms.filter((room) => room.venueId === venueId)
    const selectedCount = venueRooms.filter((room) => room.isSelected).length
    return selectedCount > 0 && selectedCount < venueRooms.length
  }

  // Update indeterminate state for all checkboxes
  useEffect(() => {
    venues.forEach((venue) => {
      const checkbox = checkboxRefs.current[venue.id]
      if (checkbox) {
        const someSelected = areSomeRoomsSelected(venue.id)
        const allSelected = areAllRoomsSelected(venue.id)
        checkbox.indeterminate = someSelected && !allSelected
      }
    })
  }, [venues, rooms])

  // Function to handle date selection in the mini calendar
  const handleSelect = (date: Date | undefined) => {
    if (date) {
      setCurrentDate(date)
    }
  }

  // Add state for sidebar collapse
  const [isCollapsed, setIsCollapsed] = useState(false)

  // Auto-collapse on smaller screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsCollapsed(true)
      }
    }

    // Set initial state
    handleResize()

    // Add event listener
    window.addEventListener("resize", handleResize)

    // Cleanup
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  return (
    <div className="relative h-full">
      {/* Toggle button - visible when collapsed */}
      {isCollapsed && (
        <button
          onClick={() => setIsCollapsed(false)}
          className="fixed left-0 top-20 z-20 rounded-r-md border border-l-0 border-border bg-background p-2 text-foreground shadow-sm transition-all hover:bg-muted/80"
          aria-label="Expand sidebar"
        >
          <ChevronRight className="h-4 w-4" />
        </button>
      )}

      <aside
        className={`
          overflow-y-auto border-r bg-background transition-all duration-300 ease-in-out
          ${isCollapsed ? "w-0 p-0 opacity-0" : "w-75 p-4 opacity-100"}
          fixed left-0 top-0 z-10 h-[calc(100vh-4rem)] md:relative
        `}
      >
        <div className="mb-4 flex items-center justify-between px-1">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8 border border-border">
              <AvatarImage src="/placeholder.svg?height=32&width=32" alt="User avatar" />
              <AvatarFallback>JD</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="text-sm font-medium leading-none">John Doe</span>
              <span className="text-xs text-muted-foreground">Acme Corp</span>
            </div>
          </div>
          <button
            onClick={() => setIsCollapsed(true)}
            className="flex h-6 w-6 items-center justify-center rounded-md text-muted-foreground transition-colors hover:bg-muted/80 hover:text-foreground"
            aria-label="Collapse sidebar"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>
        </div>

        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search venues & rooms"
              className="rounded-full border-0 bg-muted pl-8 focus-visible:ring-1 focus-visible:ring-primary"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Mini Calendar */}
        <div className="mb-6">
          <Card className="w-full overflow-hidden">
            <CardHeader className="bg-muted/50 px-3 py-2">
              <h3 className="text-sm font-medium text-foreground/70">Mini Calendar</h3>
            </CardHeader>
            <CardContent className="p-0">
              <MiniCalendar
                mode="single"
                selected={currentDate}
                onSelect={handleSelect}
                disabled={(date) => date < addDays(new Date(), -365) || date > addDays(new Date(), 365)}
              />
            </CardContent>
          </Card>
        </div>

        <div>
          <h3 className="mb-2 pl-4 text-sm font-medium text-foreground">My Venues</h3>

          <div className="space-y-1">
            {filteredData.venues.map((venue) => {
              const venueRooms = getRoomsForVenue(venue.id)
              const hasRooms = venueRooms.length > 0
              const allSelected = areAllRoomsSelected(venue.id)
              const someSelected = areSomeRoomsSelected(venue.id)
              const isIndeterminate = someSelected && !allSelected

              return (
                <div key={venue.id} className="space-y-1">
                  {/* Venue row */}
                  <div className="flex items-center space-x-2 rounded-lg px-2 py-1 hover:bg-muted">
                    {/* Expand/collapse button */}
                    <button
                      onClick={() => toggleVenueExpansion(venue.id)}
                      className="flex h-5 w-5 items-center justify-center text-muted-foreground hover:text-foreground"
                    >
                      {hasRooms &&
                        (venue.isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />)}
                    </button>

                    {/* Venue checkbox */}
                    <div className="relative">
                      <div className="custom-checkbox-wrapper">
                        <Checkbox
                          id={venue.id}
                          checked={allSelected}
                          onCheckedChange={() => toggleVenueSelection(venue.id)}
                          aria-label={`Select all rooms in ${venue.name}`}
                          ref={(el) => {
                            checkboxRefs.current[venue.id] = el
                            if (el) {
                              el.indeterminate = isIndeterminate
                            }
                          }}
                          style={{
                            borderColor: venue.color,
                            backgroundColor: allSelected ? venue.color : "transparent",
                          }}
                          className={cn(
                            "rounded-sm transition-all duration-150",
                            isIndeterminate && "custom-indeterminate"
                          )}
                        />
                        {isIndeterminate && (
                          <div
                            className="pointer-events-none absolute inset-0 flex items-center justify-center"
                            style={{ color: venue.color }}
                          >
                            <Minus className="h-3 w-3 text-white" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Venue label */}
                    <div className="flex items-center gap-1.5">
                      <Building2 className="h-4 w-4" style={{ color: venue.color }} />
                      <label
                        htmlFor={venue.id}
                        className="cursor-pointer text-sm font-medium leading-none"
                        onClick={() => toggleVenueSelection(venue.id)}
                      >
                        {venue.name}
                      </label>
                    </div>
                  </div>

                  {/* Rooms list (collapsible) */}
                  {venue.isExpanded && venueRooms.length > 0 && (
                    <div className="ml-7 space-y-1 border-l border-border pl-4">
                      {venueRooms.map((room) => (
                        <div key={room.id} className="flex items-center space-x-2 rounded-lg px-2 py-1 hover:bg-muted">
                          <div className="relative">
                            <Checkbox
                              id={room.id}
                              checked={room.isSelected}
                              onCheckedChange={() => toggleRoomSelection(room.id)}
                              style={{
                                backgroundColor: room.isSelected ? room.color : "transparent",
                                borderColor: room.color,
                              }}
                              className="rounded-sm transition-all duration-150"
                            />
                          </div>
                          <div className="flex items-center gap-1.5">
                            <DoorClosed className="h-3.5 w-3.5 text-muted-foreground" />
                            <label htmlFor={room.id} className="cursor-pointer text-sm leading-none">
                              {room.name}
                            </label>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )
            })}

            {/* No results message */}
            {filteredData.venues.length === 0 && (
              <div className="py-4 text-center text-sm text-muted-foreground">No venues or rooms match your search</div>
            )}
          </div>
        </div>
      </aside>

      {/* Overlay for mobile - closes sidebar when clicking outside */}
      {!isCollapsed && (
        <div
          className="fixed inset-0 z-0 bg-black/20 md:hidden"
          onClick={() => setIsCollapsed(true)}
          aria-hidden="true"
        />
      )}
    </div>
  )
}
