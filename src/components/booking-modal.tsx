"use client"
import { CreateBookingModal } from "./create-booking-modal"
import { UpdateBookingModal } from "./update-booking-modal"
import { CartBookingModal } from "./cart-booking-modal"
import type { Booking, GhostBooking } from "src/context/calendar-context"

interface BookingModalProps {
  isOpen: boolean
  onClose: () => void
  booking?: Booking | GhostBooking | null
  startDate?: Date
  endDate?: Date
  initialRoomId?: string | null
  initialVenueId?: string | null
}

export function BookingModal({
  isOpen,
  onClose,
  booking,
  startDate,
  endDate,
  initialRoomId,
  initialVenueId,
}: BookingModalProps) {
  // Determine which modal to show based on the booking type
  if (!booking) {
    // Creating a new booking
    return (
      <CreateBookingModal
        isOpen={isOpen}
        onClose={onClose}
        startDate={startDate}
        endDate={endDate}
        initialRoomId={initialRoomId}
      />
    )
  }

  // Check if this is a ghost booking
  const ghostBooking = booking as GhostBooking
  const isGhost = !!ghostBooking.status

  // Check if this is a cart-related ghost booking
  const isCartGhost = isGhost && ghostBooking.id.startsWith("ghost-cart-")

  if (isCartGhost) {
    // Editing a cart item
    return <CartBookingModal isOpen={isOpen} onClose={onClose} booking={ghostBooking} />
  }

  // Editing a regular booking or a non-cart ghost booking
  return <UpdateBookingModal isOpen={isOpen} onClose={onClose} booking={booking} />
}
