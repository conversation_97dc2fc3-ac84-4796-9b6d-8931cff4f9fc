"use client"

import { useState, useEffect } from "react"
import { useCalendar } from "src/context/calendar-context"
import { Button } from "src/components/ui/button"
import { X } from "lucide-react"

export function Diagnostics() {
  const [isOpen, setIsOpen] = useState(false)
  const [stats, setStats] = useState({
    bookings: 0,
    venues: 0,
    buildings: 0,
    rooms: 0,
    selectedVenues: 0,
    view: "",
    currentDate: new Date(),
    windowSize: { width: 0, height: 0 },
    renderCount: 0,
  })

  const calendar = useCalendar()

  useEffect(() => {
    const updateStats = () => {
      setStats((prev) => ({
        bookings: calendar.bookings.length,
        venues: calendar.venues.length,
        buildings: calendar.buildings.length,
        rooms: calendar.rooms.length,
        selectedVenues: calendar.venues.filter((v) => v.isSelected).length,
        view: calendar.view,
        currentDate: calendar.currentDate,
        windowSize: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        renderCount: prev.renderCount + 1,
      }))
    }

    updateStats()

    window.addEventListener("resize", updateStats)
    return () => window.removeEventListener("resize", updateStats)
  }, [calendar])

  if (!isOpen) {
    return (
      <Button
        className="fixed bottom-4 left-4 z-50 rounded-full bg-primary text-primary-foreground"
        onClick={() => setIsOpen(true)}
      >
        Diagnostics
      </Button>
    )
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 max-w-sm rounded-lg bg-muted p-4 shadow-lg">
      <div className="mb-2 flex items-center justify-between">
        <h3 className="font-bold">Calendar Diagnostics</h3>
        <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-1 text-sm">
        <p>
          View: <span className="font-mono">{stats.view}</span>
        </p>
        <p>
          Current Date: <span className="font-mono">{stats.currentDate.toDateString()}</span>
        </p>
        <p>
          Bookings: <span className="font-mono">{stats.bookings}</span>
        </p>
        <p>
          Venues: <span className="font-mono">{stats.venues}</span> (Selected: {stats.selectedVenues})
        </p>
        <p>
          Buildings: <span className="font-mono">{stats.buildings}</span>
        </p>
        <p>
          Rooms: <span className="font-mono">{stats.rooms}</span>
        </p>
        <p>
          Window Size:{" "}
          <span className="font-mono">
            {stats.windowSize.width}x{stats.windowSize.height}
          </span>
        </p>
        <p>
          Render Count: <span className="font-mono">{stats.renderCount}</span>
        </p>
      </div>

      <div className="mt-4 space-y-2">
        <Button
          size="sm"
          className="w-full"
          onClick={() => {
            console.log("Calendar Context:", calendar)
            alert("Context data logged to console")
          }}
        >
          Log Context Data
        </Button>

        <Button
          size="sm"
          variant="outline"
          className="w-full"
          onClick={() => {
            localStorage.clear()
            sessionStorage.clear()
            window.location.reload()
          }}
        >
          Clear Storage & Reload
        </Button>
      </div>
    </div>
  )
}
