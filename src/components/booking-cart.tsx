"use client"

import { useEffect, useState, useRef } from "react"
import { useBookingCart, type CartItem } from "src/store/booking-cart"
import { <PERSON><PERSON> } from "src/components/ui/button"
import { ScrollArea } from "src/components/ui/scroll-area"
import { ShoppingCart, X, Calendar, Clock, MapPin, Edit, Trash2, Plus, Check, RefreshCw } from "lucide-react"
import { cn } from "src/lib/utils"
import { useCalendar } from "src/context/calendar-context"
import { useToast } from "src/components/ui/use-toast"
import { format, isSameDay } from "date-fns"
import { useRouter } from "next/navigation"
import { BookingCheckoutModal } from "src/components/booking-checkout-modal"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "src/components/ui/select"

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "src/components/ui/card"

import { Badge } from "src/components/ui/badge"

// Define CSS keyframes in a style tag that we'll add to the component
const cartAnimationStyles = `
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
  
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.7);
      transform: scale(1);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(79, 70, 229, 0);
      transform: scale(1.05);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
      transform: scale(1);
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .cart-animation-bounce {
    animation: bounce 1s ease;
  }
  
  .cart-icon-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #ef4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    animation: pulse 1.5s infinite;
    z-index: 50;
  }

  .card-update-pulse {
    animation: pulse 1.5s ease-out;
  }

  .card-new-item {
    animation: fadeIn 0.5s ease-out;
  }
`

// Sample CS Event data - would come from API in a real implementation
const sampleEvents = [
  { id: "evt1", name: "Annual Conference 2025" },
  { id: "evt2", name: "Product Launch Q2" },
  { id: "evt3", name: "Executive Retreat" },
]

export function BookingCart() {
  const { items, removeItem, clearCart, isOpen, toggleCart, setCartOpen, updateItem } = useBookingCart()
  const {
    addBooking,
    addGhostBooking,
    deleteGhostBooking,
    clearGhostBookings,
    ghostBookings,
    convertGhostBookingToReal,
  } = useCalendar()
  const { toast } = useToast()
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const [recentlyUpdated, setRecentlyUpdated] = useState<string[]>([])
  const [updatedCount, setUpdatedCount] = useState(0)
  const [showCheckoutModal, setShowCheckoutModal] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState<string>("") // For CS Event selection

  const prevItemsRef = useRef<CartItem[]>([])
  const isInitialRender = useRef(true)
  const isSyncingRef = useRef(false)
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Hydrate the cart from localStorage on client side
  useEffect(() => {
    setMounted(true)

    // Wait for next tick to ensure the cart is hydrated
    if (typeof window !== "undefined") {
      // Force rehydration of the cart store
      const { hydrateBookingCart } = require("src/store/booking-cart")
      hydrateBookingCart()

      setTimeout(() => {
        console.log("Cart items after hydration:", useBookingCart.getState().items.length)

        // Try to ensure any hydrated items have proper Date objects
        const hydratedItems = useBookingCart.getState().items.map((item) => {
          // Ensure we create and pass new objects to avoid reference issues
          const validStart =
            typeof item.booking.start === "string"
              ? new Date(item.booking.start)
              : item.booking.start instanceof Date
                ? new Date(item.booking.start.getTime()) // Clone the date
                : new Date()

          const validEnd =
            typeof item.booking.end === "string"
              ? new Date(item.booking.end)
              : item.booking.end instanceof Date
                ? new Date(item.booking.end.getTime()) // Clone the date
                : new Date(validStart.getTime() + 3600000)

          const validAddedAt =
            typeof item.addedAt === "string"
              ? new Date(item.addedAt)
              : item.addedAt instanceof Date
                ? new Date(item.addedAt.getTime()) // Clone the date
                : new Date()

          // Return a new item with validated dates
          return {
            ...item,
            booking: {
              ...item.booking,
              start: validStart,
              end: validEnd,
            },
            addedAt: validAddedAt,
          }
        })

        // Set the initial items ref with properly formatted dates
        prevItemsRef.current = hydratedItems

        // Force a render of ghost events for cart items
        if (hydratedItems.length > 0) {
          // Trigger visual feedback for loaded cart items
          setRecentlyUpdated(hydratedItems.map((item) => item.id))
          setUpdatedCount(hydratedItems.length)
          setIsAnimating(true)

          // Reset animation after a delay
          setTimeout(() => {
            setIsAnimating(false)
            setRecentlyUpdated([])
          }, 2000)
        }
      }, 10) // Use a smaller delay to ensure this runs quickly
    }
  }, [])

  // Handle booking confirmation
  const handleBookNow = async () => {
    if (items.length === 0) {
      toast({
        title: "Your cart is empty",
        description: "Add bookings to your cart before proceeding to checkout.",
        variant: "destructive",
      })
      return
    }

    // If a valid event is selected (not empty and not 'none'), process bookings directly
    if (selectedEvent && selectedEvent !== "none") {
      setIsSubmitting(true)
      try {
        // Simulate API call to process booking
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // Prepare bookings with event assignment
        const bookingsToProcess = items.map((item) => {
          return {
            ...item,
            booking: {
              ...item.booking,
              csEventId: selectedEvent,
              needsEventAssignment: false,
            },
          }
        })

        // In a real implementation, this would call NetSuite APIs
        console.log("Processing bookings with event assignment:", bookingsToProcess)

        // Convert all cart items to real bookings on the calendar
        items.forEach((item) => {
          // If this booking has a source ghost booking ID (from when it was added to cart)
          if ("ghostBookingId" in item.booking && item.booking.ghostBookingId) {
            convertGhostBookingToReal(item.booking.ghostBookingId)
          }
        })

        // Clear cart after converting bookings to real ones
        clearCart()

        // Show success toast
        const eventName = sampleEvents.find((e) => e.id === selectedEvent)?.name || selectedEvent
        toast({
          title: "Bookings Confirmed",
          description: `Successfully processed ${items.length} bookings and assigned to "${eventName}"`,
        })
      } catch (error) {
        console.error("Error processing checkout:", error)
        toast({
          title: "Checkout Failed",
          description: "There was a problem processing your bookings. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsSubmitting(false)
      }
    } else {
      // Open the checkout modal if no event is selected or if "none" is selected
      setShowCheckoutModal(true)
    }
  }

  const handleCheckoutComplete = () => {
    // Close cart drawer after successful checkout
    setCartOpen(false)
  }

  // Handle removing an item from the cart
  const handleRemoveItem = (itemId: string) => {
    // Remove the item from the cart
    removeItem(itemId)

    // Remove the ghost event for this cart item
    // This is now safe because we're not in the useEffect
    deleteGhostBooking(`ghost-cart-${itemId}`)
  }

  // Handle clearing the cart
  const handleClearCart = () => {
    // Get all ghost events associated with cart items
    const cartGhostIds = ghostBookings.filter((ghost) => ghost.id.startsWith("ghost-cart-")).map((ghost) => ghost.id)

    // Remove all ghost events associated with cart items
    cartGhostIds.forEach((ghostId) => {
      deleteGhostBooking(ghostId)
    })

    // Clear the cart
    clearCart()
  }

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current)
      }
    }
  }, [])

  // Don't render anything during SSR to prevent hydration errors
  if (!mounted) return null

  return (
    <>
      {/* Add the CSS styles */}
      <style>{cartAnimationStyles}</style>

      <div
        className={cn(
          "fixed right-0 top-0 z-40 h-screen w-80 transform bg-background shadow-xl transition-transform duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "translate-x-full"
        )}
      >
        {/* Cart content */}
        <Card className="h-full rounded-none border-l">
          <CardHeader className="border-b px-4 py-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-lg">
                <div className="relative">
                  <ShoppingCart className={cn("h-5 w-5", isAnimating && "cart-animation-bounce")} />
                  {updatedCount > 0 && !isOpen && <div className="cart-icon-badge">{updatedCount}</div>}
                </div>
                Booking Cart
                {items.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {items.length} {items.length === 1 ? "item" : "items"}
                  </Badge>
                )}
              </CardTitle>
              <Button variant="ghost" size="icon" onClick={() => setCartOpen(false)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <CardDescription>
              {items.length > 0 ? "Review and confirm your bookings" : "Your booking cart is empty"}
            </CardDescription>
          </CardHeader>

          <ScrollArea className="h-[calc(100vh-24rem)]">
            <CardContent className="px-4 py-4">
              {items.length === 0 ? (
                <div className="flex h-40 flex-col items-center justify-center text-muted-foreground">
                  <ShoppingCart className="mb-2 h-12 w-12 opacity-20" />
                  <p>Drag on the calendar to add bookings</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {items.map((item) => (
                    <CartItemCard
                      key={item.id}
                      item={item}
                      onRemove={() => handleRemoveItem(item.id)}
                      isNew={recentlyUpdated.includes(item.id)}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </ScrollArea>

          <CardFooter className="flex min-h-[19rem] flex-col gap-3 border-t p-6">
            {items.length > 0 && (
              <>
                <div className="mb-2 flex w-full justify-between text-sm">
                  <span>Total bookings:</span>
                  <span className="font-medium">{items.length}</span>
                </div>

                {/* CS Event assignment select field */}
                <div className="w-full space-y-2">
                  <label className="text-sm font-medium">Assign to CS Event (optional)</label>
                  <Select value={selectedEvent} onValueChange={setSelectedEvent}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select an event or continue" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None - No Event Assignment</SelectItem>
                      <SelectItem disabled value="undefined" className="mt-1 border-t pt-2">
                        ────────────────
                      </SelectItem>
                      {sampleEvents.map((event) => (
                        <SelectItem key={event.id} value={event.id}>
                          {event.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedEvent && (
                    <div className="mt-1 flex items-center text-xs text-emerald-600">
                      <Check className="mr-1 h-3 w-3" />
                      Bookings will be assigned to a CS Event
                    </div>
                  )}
                </div>

                <Button className="h-11 w-full" onClick={handleBookNow} disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>Book Now</>
                  )}
                </Button>
                <Button variant="outline" className="h-11 w-full" onClick={handleClearCart} disabled={isSubmitting}>
                  Clear Cart
                </Button>
              </>
            )}
          </CardFooter>
        </Card>
      </div>
      {/* Checkout Modal */}
      <BookingCheckoutModal
        open={showCheckoutModal}
        onOpenChange={setShowCheckoutModal}
        onProcessed={handleCheckoutComplete}
      />
    </>
  )
}

function CartItemCard({ item, onRemove, isNew = false }: { item: CartItem; onRemove: () => void; isNew?: boolean }) {
  const [isHovered, setIsHovered] = useState(false)
  const [isRecentlyUpdated, setIsRecentlyUpdated] = useState(isNew)
  const { setCartOpen } = useBookingCart()
  const prevItemRef = useRef(item)

  // Add this useEffect to detect changes to the item
  useEffect(() => {
    // Check if this is an update (not the initial render)
    if (prevItemRef.current) {
      // Safely check dates
      const prevStart = prevItemRef.current.booking?.start
      const prevEnd = prevItemRef.current.booking?.end
      const currStart = item.booking?.start
      const currEnd = item.booking?.end

      const prevStartTime = prevStart instanceof Date ? prevStart.getTime() : null
      const prevEndTime = prevEnd instanceof Date ? prevEnd.getTime() : null
      const currStartTime = currStart instanceof Date ? currStart.getTime() : null
      const currEndTime = currEnd instanceof Date ? currEnd.getTime() : null

      const prevRoomId = prevItemRef.current.booking?.roomId
      const currentRoomId = item.booking?.roomId

      // If any of these properties changed, mark as recently updated
      if (prevStartTime !== currStartTime || prevEndTime !== currEndTime || prevRoomId !== currentRoomId) {
        setIsRecentlyUpdated(true)
      }
    }

    // Update the ref
    prevItemRef.current = item
  }, [item])

  // Effect to handle the "isNew" prop
  useEffect(() => {
    if (isNew) {
      setIsRecentlyUpdated(true)
    }
  }, [isNew])

  // Clear the updated state after a delay
  useEffect(() => {
    if (isRecentlyUpdated) {
      const timer = setTimeout(() => {
        setIsRecentlyUpdated(false)
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [isRecentlyUpdated])

  // Ensure we have valid date objects for formatting
  const start =
    item.booking.start instanceof Date && !isNaN(item.booking.start.getTime())
      ? item.booking.start
      : typeof item.booking.start === "string" && item.booking.start
        ? new Date(item.booking.start) // Try to parse string date
        : new Date() // Only fallback to current time if no valid date exists

  const end =
    item.booking.end instanceof Date && !isNaN(item.booking.end.getTime())
      ? item.booking.end
      : typeof item.booking.end === "string" && item.booking.end
        ? new Date(item.booking.end) // Try to parse string date
        : new Date(start.getTime() + 3600000) // Default to 1 hour after start

  const addedAt =
    item.addedAt instanceof Date && !isNaN(item.addedAt.getTime())
      ? item.addedAt
      : typeof item.addedAt === "string" && item.addedAt
        ? new Date(item.addedAt) // Try to parse string date
        : new Date()

  return (
    <div
      className={cn("relative rounded-md border p-3 transition-all duration-200 hover:shadow-md", {
        "border-green-500 bg-green-50 dark:bg-green-900/20": isRecentlyUpdated,
      })}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={cn(
          "pointer-events-none absolute inset-0 h-full w-full",
          isRecentlyUpdated ? "card-update-pulse" : "",
          isNew ? "card-new-item" : ""
        )}
      />

      {isRecentlyUpdated && (
        <div className="absolute -top-2 right-2 rounded-full bg-green-500 px-2 py-0.5 text-xs text-white">
          {isNew ? "New" : "Updated"}
        </div>
      )}

      <div className="absolute right-2 top-2 flex gap-1">
        <Button
          variant="ghost"
          size="icon"
          className={`h-6 w-6 ${isHovered ? "opacity-100" : "opacity-0"} transition-opacity duration-200`}
          onClick={() => {
            // This would open the booking modal with the cart item
            // We'll use the ghost event click handler in the calendar
            const ghostId = `ghost-cart-${item.id}`

            // Find the ghost event in the calendar and simulate a click
            const ghostEvent = document.querySelector(`[data-event-id="${ghostId}"]`)
            if (ghostEvent) {
              ghostEvent.dispatchEvent(new MouseEvent("click", { bubbles: true }))
              // Close the cart
              setCartOpen(false)
            }
          }}
        >
          <Edit className="h-4 w-4 text-muted-foreground" />
        </Button>
        <Button variant="ghost" size="icon" className="h-6 w-6" onClick={onRemove}>
          <Trash2 className="h-4 w-4 text-muted-foreground" />
        </Button>
      </div>

      <h4 className="mb-2 pr-12 font-medium">{item.booking.title || "Untitled Booking"}</h4>

      <div className="space-y-1 text-sm text-muted-foreground">
        <div className="flex items-start gap-2">
          <Calendar className="mt-0.5 h-4 w-4 flex-shrink-0" />
          <span>
            {format(start, "MMM d, yyyy")}
            {!isSameDay(start, end) ? ` - ${format(end, "MMM d, yyyy")}` : ""}
          </span>
        </div>

        <div className="flex items-start gap-2">
          <Clock className="mt-0.5 h-4 w-4 flex-shrink-0" />
          <span>
            {format(start, "h:mm a")} - {format(end, "h:mm a")}
          </span>
        </div>

        <div className="flex items-start gap-2">
          <MapPin className="mt-0.5 h-4 w-4 flex-shrink-0" />
          <span>{item.room.name}</span>
        </div>
      </div>

      <div className="mt-2 text-xs text-muted-foreground">Added {format(addedAt, "MMM d, h:mm a")}</div>
    </div>
  )
}
