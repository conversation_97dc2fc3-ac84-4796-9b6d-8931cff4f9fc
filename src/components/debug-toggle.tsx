"use client"

import { useState, useEffect } from "react"
import { Bug } from "lucide-react"
import { Button } from "src/components/ui/button"

export function DebugToggle() {
  const [isDebugMode, setIsDebugMode] = useState(false)

  useEffect(() => {
    if (isDebugMode) {
      document.body.classList.add("debug-layout")
    } else {
      document.body.classList.remove("debug-layout")
    }

    return () => {
      document.body.classList.remove("debug-layout")
    }
  }, [isDebugMode])

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={() => setIsDebugMode(!isDebugMode)}
      className="fixed bottom-4 right-4 z-50 rounded-full bg-muted"
      title={isDebugMode ? "Disable debug mode" : "Enable debug mode"}
    >
      <Bug className={`h-5 w-5 ${isDebugMode ? "text-destructive" : "text-muted-foreground"}`} />
    </Button>
  )
}
