"use client"

import { Card, CardContent } from "src/components/ui/card"
import { FullCalendarWrapper } from "./full-calendar"
import { useCalendar } from "src/context/calendar-context"
import { cn } from "../lib/utils"

interface CalendarCardProps {
  className?: string
}

export function CalendarCard({ className }: CalendarCardProps) {
  const { view } = useCalendar()

  return (
    <Card className={cn("flex h-full flex-col overflow-hidden rounded-lg border bg-card shadow-sm", className)}>
      <CardContent className="flex-1 overflow-hidden p-0">
        <div
          className={cn(
            "calendar-wrapper h-full w-full transition-all duration-200",
            view === "timeline" ? "pt-2" : "pt-3"
          )}
        >
          <FullCalendarWrapper />
        </div>
      </CardContent>
    </Card>
  )
}
