"use client"

import { useEffect, useState } from "react"
import { X } from "lucide-react"
import { cn } from "src/lib/utils"

interface CartUpdateToastProps {
  message: string
  duration?: number
  onClose: () => void
}

export function CartUpdateToast({ message, duration = 3000, onClose }: CartUpdateToastProps) {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
      setTimeout(onClose, 300) // Allow time for exit animation
    }, duration)

    return () => clearTimeout(timer)
  }, [duration, onClose])

  return (
    <div
      className={cn(
        "fixed bottom-4 right-4 z-50 flex items-center rounded border-l-4 border-green-500 bg-green-100 p-4 text-green-700 shadow-lg transition-all duration-300",
        isVisible ? "translate-y-0 opacity-100" : "translate-y-2 opacity-0"
      )}
      role="alert"
    >
      <div className="flex-1">{message}</div>
      <button
        onClick={() => {
          setIsVisible(false)
          setTimeout(onClose, 300)
        }}
        className="ml-4 text-green-700 hover:text-green-900"
      >
        <X size={16} />
      </button>
    </div>
  )
}
