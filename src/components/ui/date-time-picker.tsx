"use client"

import * as React from "react"
import { Calendar as Calendar<PERSON><PERSON>, Clock } from "lucide-react"
import { format } from "date-fns"

import { cn } from "src/lib/utils"
import { Button } from "src/components/ui/button"
import { Calendar } from "src/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "src/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "src/components/ui/select"

interface DateTimePickerProps {
  value: Date
  onChange: (date: Date) => void
  disabled?: boolean
}

export function DateTimePicker({ value, onChange, disabled }: DateTimePickerProps) {
  // Create a memoized date instance that doesn't change unless the value changes
  const date = React.useMemo(() => new Date(value), [value])

  // Function to set only the time portion of the date
  const setTime = React.useCallback(
    (hours: number, minutes: number) => {
      const newDate = new Date(date)
      newDate.setHours(hours)
      newDate.setMinutes(minutes)
      onChange(newDate)
    },
    [date, onChange]
  )

  // Create hours and minutes arrays for the selects
  const hours = Array.from({ length: 24 }, (_, i) => i)
  const minutes = Array.from({ length: 60 }, (_, i) => i)

  // Get current hours and minutes for the select values
  const currentHours = date.getHours()
  const currentMinutes = date.getMinutes()

  return (
    <div className="flex gap-2">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, "PPP") : <span>Pick a date</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={date}
            onSelect={(newDate) => {
              // Preserve time when changing date
              if (newDate) {
                const updatedDate = new Date(newDate)
                updatedDate.setHours(date.getHours())
                updatedDate.setMinutes(date.getMinutes())
                updatedDate.setSeconds(0)
                onChange(updatedDate)
              }
            }}
            initialFocus
            disabled={disabled}
          />
        </PopoverContent>
      </Popover>
      <div className="flex items-center gap-1">
        <Clock className="h-4 w-4 text-muted-foreground" />
        <Select
          value={currentHours.toString()}
          onValueChange={(value) => setTime(parseInt(value, 10), currentMinutes)}
          disabled={disabled}
        >
          <SelectTrigger className="w-[70px]">
            <SelectValue placeholder="Hour" />
          </SelectTrigger>
          <SelectContent position="popper" className="h-[200px]">
            {hours.map((hour) => (
              <SelectItem key={hour} value={hour.toString()}>
                {hour.toString().padStart(2, "0")}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <span className="text-muted-foreground">:</span>
        <Select
          value={currentMinutes.toString()}
          onValueChange={(value) => setTime(currentHours, parseInt(value, 10))}
          disabled={disabled}
        >
          <SelectTrigger className="w-[75px]">
            <SelectValue placeholder="Minute" />
          </SelectTrigger>
          <SelectContent position="popper" className="h-[200px]">
            {minutes.map((minute) => (
              <SelectItem key={minute} value={minute.toString()}>
                {minute.toString().padStart(2, "0")}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
