"use client"

import { useState, useEffect, useRef } from "react"
import { createPortal } from "react-dom"
import { CartUpdateToast } from "./cart-update-toast"

export type ToastType = {
  id: string
  message: string
}

// Create a custom event for toast notifications
export const showToast = (message: string, options?: { immediate?: boolean }) => {
  const event = new CustomEvent("showToast", { 
    detail: { 
      message, 
      immediate: options?.immediate ?? true 
    } 
  })
  window.dispatchEvent(event)
}

// Debounced version of showToast for rapid updates
let debounceTimer: NodeJS.Timeout | null = null
export const showToastDebounced = (message: string, delay: number = 300) => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  debounceTimer = setTimeout(() => {
    showToast(message, { immediate: false })
    debounceTimer = null
  }, delay)
}

export function ToastManager() {
  const [toasts, setToasts] = useState<ToastType[]>([])
  const [isMounted, setIsMounted] = useState(false)
  const toastCounterRef = useRef(0)
  const recentMessagesRef = useRef<Map<string, { lastShown: number, count: number }>>(new Map())

  useEffect(() => {
    setIsMounted(true)

    const handleShowToast = (event: CustomEvent<{ message: string; immediate?: boolean }>) => {
      const message = event.detail.message
      const immediate = event.detail.immediate ?? true
      const now = Date.now()
      
      // Get or create message tracking
      const messageInfo = recentMessagesRef.current.get(message) || { lastShown: 0, count: 0 }
      
      // For immediate toasts, only prevent if shown within 100ms
      // For debounced toasts, prevent if shown within 1000ms
      const preventDuplicateThreshold = immediate ? 100 : 1000
      
      if (messageInfo.lastShown && now - messageInfo.lastShown < preventDuplicateThreshold) {
        // Update count but don't show duplicate
        messageInfo.count++
        recentMessagesRef.current.set(message, messageInfo)
        return
      }
      
      // Update message info
      messageInfo.lastShown = now
      messageInfo.count = 1
      recentMessagesRef.current.set(message, messageInfo)
      
      // Clean up old entries (older than 5 seconds)
      for (const [msg, info] of recentMessagesRef.current.entries()) {
        if (now - info.lastShown > 5000) {
          recentMessagesRef.current.delete(msg)
        }
      }
      
      // Generate a truly unique ID that won't collide
      const timestamp = Date.now()
      const counter = toastCounterRef.current++
      const random = Math.floor(Math.random() * 10000)
      const processId = performance.now().toString(36).replace('.', '')
      const id = `toast-${timestamp}-${counter}-${random}-${processId}`
      
      setToasts((prev) => [...prev, { id, message }])
    }

    window.addEventListener("showToast", handleShowToast as EventListener)

    return () => {
      window.removeEventListener("showToast", handleShowToast as EventListener)
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }
    }
  }, [])

  const removeToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }

  if (!isMounted) return null

  return createPortal(
    <div className="toast-container">
      {toasts.map((toast) => (
        <CartUpdateToast key={toast.id} message={toast.message} onClose={() => removeToast(toast.id)} />
      ))}
    </div>,
    document.body,
  )
}

