"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "src/components/ui/dialog"
import { <PERSON><PERSON> } from "src/components/ui/button"
import { useCalendar } from "src/context/calendar-context"
import { useBookingForm } from "src/hooks/use-booking-form"
import { BookingFormFields } from "./booking-form-fields"
import { useBookingCart } from "src/store/booking-cart"
import { v4 as uuidv4 } from "uuid"
import type { BookingStatus } from "src/types/calendar"
import type { GhostBookingStatus } from "src/context/calendar-context"

interface CreateBookingModalProps {
  isOpen: boolean
  onClose: () => void
  startDate?: Date
  endDate?: Date
  initialRoomId?: string | null
}

export function CreateBookingModal({ isOpen, onClose, startDate, endDate, initialRoomId }: CreateBookingModalProps) {
  const { addBooking, convertGhostBookingToReal } = useCalendar()
  const { addItem } = useBookingCart()
  const [isAddingToCart, setIsAddingToCart] = useState(false)

  // Add state for status-related fields
  const [status, setStatus] = useState<BookingStatus>("Tentative")
  const [holdRank, setHoldRank] = useState<number>(1)
  const [lostReason, setLostReason] = useState<string>("")

  const {
    title,
    setTitle,
    description,
    setDescription,
    venueId,
    setVenueId,
    roomId,
    setRoomId,
    start,
    setStart,
    end,
    setEnd,
    isAllDay,
    setIsAllDay,
    createGhost,
    setCreateGhost,
    venueRooms,
    venues,
    handleVenueChange,
    handleGhostToggle,
    handleDateChange,
    handleTimeChange,
    handleSubmit,
    handleCancel,
    validateForm,
    ghostId,
  } = useBookingForm({
    booking: null,
    startDate,
    endDate,
    initialRoomId,
    isOpen,
    onClose,
    createGhostByDefault: true, // Always create ghost events by default
  })

  // Handle adding to cart
  const handleAddToCart = () => {
    if (!validateForm()) return

    setIsAddingToCart(true)

    try {
      // Find the room object
      const room = venueRooms.find((r) => r.id === roomId)

      if (!room) {
        throw new Error("Room not found")
      }

      // Generate a unique ID for the cart item
      const cartItemId = uuidv4()

      // Create the booking object with status
      const booking = {
        id: cartItemId,
        title,
        description,
        venueId,
        roomId,
        start,
        end,
        isAllDay,
        // Add status fields
        status,
        holdRank: status === "Hold" ? holdRank : undefined,
        lostReason: status === "Lost" ? lostReason : undefined,
      }

      // Add to cart
      addItem({
        id: cartItemId,
        booking,
        room,
        addedAt: new Date(),
      })

      // If we have a ghost event, delete it since the cart will create its own ghost
      if (ghostId) {
        // The ghost will be deleted in the useBookingForm cleanup
      }

      onClose()
    } catch (error) {
      console.error("Error adding to cart:", error)
    } finally {
      setIsAddingToCart(false)
    }
  }

  // Handle booking confirmation - convert ghost to real booking
  const handleConfirmBooking = () => {
    if (!validateForm()) return

    if (ghostId) {
      // Convert the ghost booking to a real booking
      convertGhostBookingToReal(ghostId)
      onClose()
    } else {
      // Fallback if no ghost exists (shouldn't happen with createGhostByDefault: true)
      const booking = {
        id: `booking-${Date.now()}`,
        title,
        description,
        venueId,
        roomId,
        start,
        end,
        isAllDay,
        // Add status fields
        status,
        holdRank: status === "Hold" ? holdRank : undefined,
        lostReason: status === "Lost" ? lostReason : undefined,
      }

      addBooking(booking)
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Booking</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            handleConfirmBooking()
          }}
        >
          <BookingFormFields
            title={title}
            setTitle={setTitle}
            description={description}
            setDescription={setDescription}
            venueId={venueId}
            venues={venues}
            handleVenueChange={handleVenueChange}
            roomId={roomId}
            setRoomId={setRoomId}
            venueRooms={venueRooms}
            start={start}
            end={end}
            handleDateChange={handleDateChange}
            handleTimeChange={handleTimeChange}
            isAllDay={isAllDay}
            setIsAllDay={setIsAllDay}
            createGhost={createGhost}
            handleGhostToggle={handleGhostToggle}
            // Add status-related props
            status={status}
            setStatus={setStatus}
            holdRank={holdRank}
            setHoldRank={setHoldRank}
            lostReason={lostReason}
            setLostReason={setLostReason}
            showStatusSelector={true}
          />
          <div className="mt-6 flex justify-between">
            <Button type="button" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={handleAddToCart} disabled={isAddingToCart}>
                {isAddingToCart ? "Adding..." : "Add to Cart"}
              </Button>
              <Button type="submit">Confirm Booking</Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
