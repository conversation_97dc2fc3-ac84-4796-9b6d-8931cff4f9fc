"use client"

import { useRef, useState, useEffect, useCallback } from "react"
import { useCalendar } from "src/context/calendar-context"
import FullCalendarLib from "@fullcalendar/react"
import dayGridPlugin from "@fullcalendar/daygrid"
import timeGridPlugin from "@fullcalendar/timegrid"
import interactionPlugin from "@fullcalendar/interaction"
import resourceTimelinePlugin from "@fullcalendar/resource-timeline"
import listPlugin from "@fullcalendar/list"
import multiMonthPlugin from "@fullcalendar/multimonth"
import type { DateSelectArg, DatesSetArg } from "@fullcalendar/core"

// Import custom components and hooks
import { BookingModal } from "src/components/booking-modal"
import { EventPopover } from "./components/EventPopover"
import { CalendarStyles } from "./components/CalendarStyles"
import { useCalendarEvents } from "./hooks/useCalendarEvents"
import { useCalendarViewConfig } from "./hooks/useCalendarViewConfig"
import { useResourceManagement } from "./hooks/useResourceManagement"
import { useEventManagement } from "./hooks/useEventManagement"
import { TimelineToolbar } from "src/components/timeline-toolbar"
import { Button } from "src/components/ui/button"
import { CalendarIcon, ChevronLeft, ChevronRight, Filter, SlidersHorizontal, ShoppingCart } from "lucide-react"
import { Switch } from "src/components/ui/switch"
import { Label } from "src/components/ui/label"
import { formatHeaderDate, formatDateRangeForTimeline } from "src/utils/calendar-utils"

/**
 * Main FullCalendar component with all functionality
 *
 * This component implements:
 * - Calendar rendering with FullCalendar
 * - Event handling (click, select, drag, etc.)
 * - Resource management (rooms, venues)
 * - View configuration (day, week, month, timeline)
 * - Modal integration for editing
 * - Popover display for event details
 */
export function FullCalendarWrapper() {
  // Get calendar context
  const {
    currentDate,
    setCurrentDate,
    view,
    setView,
    nextPeriod: contextNextPeriod,
    prevPeriod: contextPrevPeriod,
  } = useCalendar()

  // Reference to the FullCalendar instance
  const calendarRef = useRef<FullCalendarLib>(null)

  // Enhanced navigation functions that use API when available
  const nextPeriod = () => {
    if (calendarRef.current?.getApi()) {
      const api = calendarRef.current.getApi()

      // Add visual feedback before navigation using CSS classes
      // instead of direct style manipulation to avoid TS errors
      if (view === "month") {
        const calendarElement = calendarRef.current.elRef?.current as HTMLElement | undefined

        if (calendarElement) {
          const viewHarness = calendarElement.querySelector(".fc-view-harness")
          if (viewHarness) {
            // First remove any existing transition classes
            viewHarness.classList.remove("month-transition-prev", "month-transition-next")

            // Add a small delay for the DOM to process the class removal
            setTimeout(() => {
              // Apply the next transition animation
              viewHarness.classList.add("month-transition-next")

              // Perform the navigation
              api.next()

              // Remove animation class after animation completes
              setTimeout(() => {
                viewHarness.classList.remove("month-transition-next")
              }, 350)
            }, 10)
          } else {
            // Fallback if element not found
            api.next()
          }
        } else {
          // Fallback if element not found
          api.next()
        }
      } else {
        // For non-month views, just navigate
        api.next()
      }
      // The datesSet handler will update our app state
    } else {
      // Fallback to context method
      contextNextPeriod()
    }
  }

  const prevPeriod = () => {
    if (calendarRef.current?.getApi()) {
      const api = calendarRef.current.getApi()

      // Add visual feedback before navigation using CSS classes
      // instead of direct style manipulation to avoid TS errors
      if (view === "month") {
        const calendarElement = calendarRef.current.elRef?.current as HTMLElement | undefined

        if (calendarElement) {
          const viewHarness = calendarElement.querySelector(".fc-view-harness")
          if (viewHarness) {
            // First remove any existing transition classes
            viewHarness.classList.remove("month-transition-prev", "month-transition-next")

            // Add a small delay for the DOM to process the class removal
            setTimeout(() => {
              // Apply the prev transition animation
              viewHarness.classList.add("month-transition-prev")

              // Perform the navigation
              api.prev()

              // Remove animation class after animation completes
              setTimeout(() => {
                viewHarness.classList.remove("month-transition-prev")
              }, 350)
            }, 10)
          } else {
            // Fallback if element not found
            api.prev()
          }
        } else {
          // Fallback if element not found
          api.prev()
        }
      } else {
        // For non-month views, just navigate
        api.prev()
      }
      // The datesSet handler will update our app state
    } else {
      // Fallback to context method
      contextPrevPeriod()
    }
  }

  // Modal state
  const [modalOpen, setModalOpen] = useState(false)
  const [selectedBooking, setSelectedBooking] = useState<any | null>(null)
  const [selectedDates, setSelectedDates] = useState<{ start: Date; end: Date } | null>(null)
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null)
  const [selectedVenueId, setSelectedVenueId] = useState<string | null>(null)

  // Cart mode state
  const [isCartMode, setIsCartMode] = useState(false)

  // Setup calendar view configuration
  const {
    timelineDuration,
    timeSlotInterval,
    slotConfig,
    handleTimelineDurationChange,
    handleTimeSlotIntervalChange,
    handleViewChange,
    getFullCalendarViewType,
  } = useCalendarViewConfig(calendarRef, view, setView)

  // Setup resource management
  const { resources } = useResourceManagement()

  // Setup event management
  const { events } = useEventManagement()

  // State for managing drag operations with placeholder functionality
  const [dragState, setDragState] = useState<{
    isDragging: boolean;
    originalEvent: any;
    originalPosition: { start: Date; end: Date; resourceId?: string };
    placeholderElement: HTMLElement | null;
  } | null>(null);

  // Setup calendar event handlers
  const {
    handleDateSelect,
    handleEventClick,
    handleEventChange,
    eventDidMountHandler,
    eventWillUnmountHandler,
    clearPopover,
  } = useCalendarEvents(
    setModalOpen,
    setSelectedBooking,
    setSelectedDates,
    setSelectedRoomId,
    setSelectedVenueId,
    isCartMode
  )

  // Add this effect to sync the calendar when currentDate changes
  useEffect(() => {
    if (calendarRef.current) {
      const api = calendarRef.current.getApi()
      if (api) {
        const currentViewDate = api.getDate()

        // Check if dates are different by comparing year/month (for month view)
        // or exact timestamp for other views
        const shouldUpdate =
          view === "month"
            ? currentViewDate.getFullYear() !== currentDate.getFullYear() ||
              currentViewDate.getMonth() !== currentDate.getMonth()
            : currentViewDate.getTime() !== currentDate.getTime()

        if (shouldUpdate) {
          // Add a visual indication that navigation is happening using CSS classes
          // Access the DOM element directly instead of private property
          const calendarElement = calendarRef.current ? (document.querySelector(".fc") as HTMLElement) : undefined
          if (calendarElement) {
            const viewHarness = calendarElement.querySelector(".fc-view-harness")
            if (viewHarness) {
              viewHarness.classList.add("month-transition-fade")

              // Navigate to the new date
              api.gotoDate(currentDate)

              // Remove animation class after animation completes
              setTimeout(() => {
                viewHarness.classList.remove("month-transition-fade")
              }, 300)
            } else {
              // Fallback if element not available
              api.gotoDate(currentDate)
            }
          } else {
            // Fallback if element not available
            api.gotoDate(currentDate)
          }
        }
      }
    }
  }, [currentDate, view])

  // Handle date range changes
  const handleDatesSet = (info: DatesSetArg) => {
    if (!info.start || !(info.start instanceof Date) || isNaN(info.start.getTime())) {
      console.error("Invalid date in datesSet", info.start)
      return
    }

    // For month view, we need to compare year/month instead of exact timestamps
    // because the start date might be the last day of the previous month
    if (view === "month") {
      const startMonth = info.start.getMonth()
      const startYear = info.start.getFullYear()
      const currentMonth = currentDate.getMonth()
      const currentYear = currentDate.getFullYear()

      if (startMonth !== currentMonth || startYear !== currentYear) {
        // For month view, use the middle of the visible range to determine the month
        // This handles edge cases where the calendar shows days from adjacent months
        const visibleRange = calendarRef.current?.getApi()?.view.currentStart
        const midRangeDate = visibleRange ? new Date(visibleRange) : info.start
        midRangeDate.setDate(15) // Set to middle of month to avoid edge cases

        setCurrentDate(new Date(midRangeDate))
      }
    } else {
      // For other views, use the exact timestamp comparison
      if (info.start.getTime() !== currentDate.getTime()) {
        setCurrentDate(new Date(info.start))
      }
    }
  }

  // Add tooltip cleanup during drag operations (copied from v0-booking-calendar)
  useEffect(() => {
    // Function to clean up all tooltips
    const cleanupTooltips = () => {
      document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
    }

    // Clean up tooltips when component unmounts
    return () => {
      cleanupTooltips()
    }
  }, [view, resources, timeSlotInterval])

  // Add a MutationObserver to catch any tooltips that might be missed (copied from v0-booking-calendar)
  useEffect(() => {
    // Create a MutationObserver to watch for DOM changes
    const observer = new MutationObserver((mutations) => {
      // Check if we're currently dragging (look for fc-event-dragging class)
      const isDragging = document.querySelector(".fc-event-dragging")

      if (isDragging) {
        // If dragging, remove all tooltips
        document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
      }
    })

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ["class"],
    })

    // Clean up observer on component unmount
    return () => {
      observer.disconnect()
    }
  }, [])

  return (
    <>
      {/* Apply calendar styles */}
      <CalendarStyles />

      <div className="card h-full w-full">
        {/* Show timeline toolbar only when in timeline view */}
        {view === "timeline" && (
          <TimelineToolbar
            dateRange={formatDateRangeForTimeline(currentDate, timelineDuration)}
            onPrevPeriod={prevPeriod}
            onNextPeriod={nextPeriod}
            isCartMode={isCartMode}
            onCartModeChange={setIsCartMode}
            timelineDuration={timelineDuration}
            onTimelineDurationChange={handleTimelineDurationChange}
            timeSlotInterval={timeSlotInterval}
            onTimeSlotIntervalChange={handleTimeSlotIntervalChange}
          />
        )}

        {/* Show regular view toolbar when not in timeline view */}
        {view !== "timeline" && (
          <div className="mb-4 flex items-center justify-between rounded-lg border bg-background p-2 shadow-sm">
            {/* Left section - View type display */}
            <div className="flex items-center space-x-2">
              <div className="mr-2 flex items-center">
                <CalendarIcon className="mr-1 h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">View: {view.charAt(0).toUpperCase() + view.slice(1)}</span>
              </div>
            </div>

            {/* Center section - Date navigation */}
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={prevPeriod}
                className="calendar-nav-button prev-month-button h-8 w-8 rounded-full"
                aria-label="Previous month"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <div className="min-w-[180px] text-center text-base font-semibold">
                {formatHeaderDate(currentDate, view)}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={nextPeriod}
                className="calendar-nav-button next-month-button h-8 w-8 rounded-full"
                aria-label="Next month"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Right section - Controls */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Label htmlFor="cart-mode" className="flex cursor-pointer items-center gap-1 text-sm">
                  <ShoppingCart className="h-3.5 w-3.5" />
                  <span className="hidden sm:inline">Cart Mode</span>
                </Label>
                <Switch
                  id="cart-mode"
                  checked={isCartMode}
                  onCheckedChange={setIsCartMode}
                  className="data-[state=checked]:bg-primary"
                />
              </div>

              <div className="flex items-center space-x-2 sm:hidden">
                <Button variant="outline" size="sm" className="h-8 gap-1">
                  <Filter className="h-3.5 w-3.5" />
                  <span className="hidden sm:inline">Filter</span>
                </Button>

                <Button variant="outline" size="sm" className="h-8 gap-1">
                  <SlidersHorizontal className="h-3.5 w-3.5" />
                  <span className="hidden sm:inline">Options</span>
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* The FullCalendar Component */}
        <FullCalendarLib
          key={`${view}-${timelineDuration}-calendar`}
          schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
          ref={calendarRef}
          plugins={[
            dayGridPlugin,
            timeGridPlugin,
            resourceTimelinePlugin,
            interactionPlugin,
            listPlugin,
            multiMonthPlugin,
          ]}
          droppable
          editable
          allDayMaintainDuration
          selectable
          contentHeight={1300}
          initialView={getFullCalendarViewType(view, timelineDuration)}
          rerenderDelay={10}
          initialDate={currentDate}
          headerToolbar={false} // We'll use our custom header
          selectMirror={true}
          dayMaxEvents={true}
          weekends={true}
          events={events}
          resources={resources}
          resourceGroupField="venueName" // Group resources by venue name
          resourceAreaWidth="200px" // Increase width for hierarchical display
          resourceAreaHeaderContent="Venues & Rooms"
          resourcesInitiallyExpanded={true}
          // Apply view-specific slot configuration
          slotLabelFormat={slotConfig.slotLabelFormat as any}
          allDaySlot={true}
          allDayText="All Day"
          nowIndicator={true}
          height="100%"
          slotMinTime="00:00:00"
          slotMaxTime="24:00:00"
          scrollTime="08:00:00" // Scroll to 8am by default
          businessHours={{
            daysOfWeek: [1, 2, 3, 4, 5], // Monday - Friday
            startTime: "08:00",
            endTime: "18:00",
          }}
          eventTimeFormat={{
            hour: "numeric",
            minute: "2-digit",
            meridiem: "short",
          }}
          eventDisplay="block"
          // Remove custom eventContent to use FullCalendar's default rendering
          // This fixes drag-and-drop issues with both ghost and regular events
          slotEventOverlap={false}
          eventOverlap // Allows events to overlapping
          eventResizableFromStart={true}
          eventDurationEditable={true}
          // Google Calendar-style drag behavior: no mirror, original event stays in place
          dragRevertDuration={0}
          eventStartEditable={true}
          eventDragMinDistance={5}
          eventShortHeight={60}
          eventMinHeight={60}
          eventMinWidth={60}
          stickyHeaderDates={true}
          firstDay={1} // Start week on Monday
          locale="en"
          timeZone="local"
          resourceAreaColumns={[
            {
              field: "title",
              headerContent: "Rooms",
            },
          ]}
          resourceOrder="venueName,title" // Ensure resources are ordered by venue first, then by room name
          resourceGroupLabelClassNames="venue-group-label"
          resourceLaneClassNames="resource-lane"
          resourceGroupLaneClassNames="venue-group-lane"
          resourceAreaHeaderClassNames="resource-area-header"
          // Configure time slots based on the view-specific configuration
          slotDuration={slotConfig.slotDuration}
          slotLabelInterval={slotConfig.slotLabelInterval}
          snapDuration={slotConfig.snapDuration}
          // FullCalendar doesn't accept className directly, use classes for custom styling
          // Custom styling is handled via CalendarStyles component
          // Configure views with specific header formats and time slot settings
          views={{
            // Day and Week views with fixed 15-minute slots and proper time formatting
            timeGridDay: {
              slotDuration: "00:15:00",
              slotLabelInterval: "01:00:00",
              slotLabelFormat: {
                hour: "numeric",
                minute: "2-digit",
                omitZeroMinute: false,
                meridiem: "short",
              } as any,
            },
            timeGridWeek: {
              slotDuration: "00:15:00",
              slotLabelInterval: "01:00:00",
              slotLabelFormat: {
                hour: "numeric",
                minute: "2-digit",
                omitZeroMinute: false,
                meridiem: "short",
              } as any,
            },
            // Define custom timeline views with specific header formats
            resourceTimelineDay: {
              type: "resourceTimeline",
              duration: { days: 1 },
              slotLabelFormat: [
                { weekday: "long", day: "numeric", month: "short", year: "numeric" },
                { hour: "numeric", minute: "2-digit", hour12: true },
              ],
              slotDuration: slotConfig.slotDuration,
              slotLabelInterval: slotConfig.slotLabelInterval,
            },
            resourceTimelineThreeDays: {
              type: "resourceTimeline",
              duration: { days: 3 },
              slotLabelFormat: [
                { weekday: "short", day: "numeric", month: "short" },
                { hour: "numeric", minute: "2-digit", hour12: true },
              ],
              slotDuration: slotConfig.slotDuration,
              slotLabelInterval: slotConfig.slotLabelInterval,
            },
            resourceTimelineWeek: {
              type: "resourceTimeline",
              duration: { days: 7 },
              slotLabelFormat: [
                { weekday: "short", day: "numeric", month: "short" },
                { hour: "numeric", minute: "2-digit", hour12: true },
              ],
              slotDuration: slotConfig.slotDuration,
              slotLabelInterval: slotConfig.slotLabelInterval,
            },
            resourceTimelineTwoWeeks: {
              type: "resourceTimeline",
              duration: { days: 14 },
              slotLabelFormat: [
                { weekday: "short", day: "numeric", month: "short" },
                { hour: "numeric", minute: "2-digit", hour12: true },
              ],
              slotDuration: slotConfig.slotDuration,
              slotLabelInterval: slotConfig.slotLabelInterval,
            },
            resourceTimelineMonth: {
              type: "resourceTimeline",
              duration: { days: 30 },
              slotLabelFormat: [
                { weekday: "short", day: "numeric", month: "short" },
                { hour: "numeric", minute: "2-digit", hour12: true },
              ],
              slotDuration: slotConfig.slotDuration,
              slotLabelInterval: slotConfig.slotLabelInterval,
            },
          }}
          // Event handlers - simplified approach from v0-booking-calendar
          eventDidMount={eventDidMountHandler}
          eventWillUnmount={eventWillUnmountHandler}
          eventDragStart={() => {
            // Remove all tooltips when drag starts
            document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
            // Clear any existing popovers
            clearPopover()
          }}
          eventDragStop={() => {
            // Ensure tooltips are removed when drag stops
            document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
            // Clear any popovers
            clearPopover()
          }}
          eventResize={() => {
            // Remove tooltips during resize operations
            document.querySelectorAll(".fc-tooltip").forEach((el) => el.remove())
            clearPopover()
          }}
          select={handleDateSelect}
          eventClick={handleEventClick}
          eventChange={handleEventChange}
          datesSet={handleDatesSet}
          viewDidMount={handleViewChange as any}
          slotMinWidth={70}
        />
      </div>

      {/* Event popover */}
      <EventPopover />

      {/* Booking modal */}
      <BookingModal
        isOpen={modalOpen}
        onClose={() => {
          setModalOpen(false)
          setSelectedVenueId(null)
          setSelectedRoomId(null)
        }}
        booking={selectedBooking}
        startDate={selectedDates?.start}
        endDate={selectedDates?.end}
        initialRoomId={selectedRoomId}
        initialVenueId={selectedVenueId}
      />
    </>
  )
}

export default FullCalendarWrapper
