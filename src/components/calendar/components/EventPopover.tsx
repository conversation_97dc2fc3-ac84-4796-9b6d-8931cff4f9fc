"use client"

import React, { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { createPortal } from "react-dom"
import { MapPin, Clock, X } from "lucide-react"
import { format } from "date-fns"
import { Badge } from "src/components/ui/badge"
import { useCalendar } from "src/context/calendar-context"
import { useCalendarPopover } from "../stores/popover-store"
import { calculatePopoverPosition } from "../utils/position-utils"

/**
 * EventPopover component - displays a detailed popover for calendar events
 * Features:
 * - Animated appearance/disappearance using Framer Motion
 * - Smart positioning to stay within viewport
 * - Real-time updates to reflect latest booking data
 * - Status badges with appropriate styling
 */
export function EventPopover() {
  // Always call hooks at the top level before any conditional returns
  const { isOpen, eventData, closePopover, setMouseOverPopover } = useCalendarPopover()
  const { rooms, venues, ghostBookings, bookings } = useCalendar()
  const popoverTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Add state for the current event data that can be updated immediately
  const [currentEvent, setCurrentEvent] = useState(eventData?.event)
  const [popoverPosition, setPopoverPosition] = useState<any>(null)

  // Check if we're currently dragging (early check)
  const isDragging =
    typeof document !== "undefined" &&
    (document.body.classList.contains("fc-dragging") || document.querySelector(".fc-event-dragging") !== null)

  // Update the current event whenever the source event data changes
  useEffect(() => {
    // Don't update if we're dragging
    if (isDragging) return

    if (eventData?.event) {
      // Find the most up-to-date version of this event if available
      // This ensures we're showing the latest data even if fullcalendar hasn't re-rendered
      const eventId = eventData.event.id

      // console.log('Original event data:', eventData);

      const booking = bookings.find((b) => b.id === eventId)

      if (booking) {
        console.log("Found booking:", {
          id: booking.id,
          title: booking.title,
          status: booking.status,
        })

        // If we have the latest booking data, update our local state
        // with a faux FullCalendar event object
        setCurrentEvent({
          ...eventData.event,
          title: booking.title,
          start: booking.start,
          end: booking.end,
          extendedProps: {
            ...eventData.event.extendedProps,
            description: booking.description,
            status: booking.status || "Tentative", // Provide a fallback status
            holdRank: booking.holdRank,
            lostReason: booking.lostReason,
          },
        })
      } else {
        // Just use the event data as provided
        console.log("No matching booking found, using event data")

        // Ensure we have a valid status even if it's not in the event data
        setCurrentEvent({
          ...eventData.event,
          title: eventData.event.title,
          start: eventData.event.start,
          end: eventData.event.end,
          extendedProps: {
            ...eventData.event.extendedProps,
            status: eventData.event.extendedProps?.status || "Tentative",
          },
        })
      }

      // Calculate the position
      if (eventData.el) {
        const popoverWidth = 320
        const popoverHeight = 280
        setPopoverPosition(calculatePopoverPosition(eventData.el, popoverWidth, popoverHeight))
      }
    }
  }, [eventData, bookings, isDragging])

  // Clear timeout on unmount
  useEffect(() => {
    return () => {
      if (popoverTimeoutRef.current) {
        clearTimeout(popoverTimeoutRef.current)
      }
    }
  }, [])

  // Close popover immediately when dragging starts
  useEffect(() => {
    if (isDragging && isOpen) {
      closePopover()
    }
  }, [isDragging, isOpen, closePopover])

  // Return early if we're dragging (primary check)
  if (isDragging) return null

  // Return early if no event data or event
  if (!isOpen || !eventData || !currentEvent) return null

  // Don't render if we're currently dragging (secondary check)
  if (document.querySelector(".fc-event-dragging")) return null

  const { el } = eventData
  const { title, start, end, extendedProps } = currentEvent
  const { description, status, holdRank, isGhost, isCartGhost } = extendedProps

  // console.log('Current event:', currentEvent);

  // Get room and venue info
  const room = rooms.find((r) => r.id === extendedProps.roomId)
  const venue = venues.find((v) => v.id === extendedProps.venueId)

  // Validate dates to prevent formatting errors
  const isValidStart = start && start instanceof Date && !isNaN(start.getTime())
  const isValidEnd = end && end instanceof Date && !isNaN(end.getTime())

  // Status colors
  const statusColors: Record<
    string,
    { bg: string; text: string; darkModeBg: string; darkModeText: string; darkModeGlow: string }
  > = {
    Inquiry: {
      bg: "bg-purple-100",
      text: "text-purple-800",
      darkModeBg: "dark:bg-purple-900/30",
      darkModeText: "dark:text-purple-300",
      darkModeGlow: "dark:shadow-[0_0_10px_rgba(192,132,252,0.4)]",
    },
    Prospect: {
      bg: "bg-pink-100",
      text: "text-pink-800",
      darkModeBg: "dark:bg-pink-900/30",
      darkModeText: "dark:text-pink-300",
      darkModeGlow: "dark:shadow-[0_0_10px_rgba(244,114,182,0.4)]",
    },
    Tentative: {
      bg: "bg-amber-100",
      text: "text-amber-800",
      darkModeBg: "dark:bg-amber-900/30",
      darkModeText: "dark:text-amber-300",
      darkModeGlow: "dark:shadow-[0_0_10px_rgba(251,191,36,0.4)]",
    },
    Definite: {
      bg: "bg-green-100",
      text: "text-green-800",
      darkModeBg: "dark:bg-green-900/30",
      darkModeText: "dark:text-green-300",
      darkModeGlow: "dark:shadow-[0_0_10px_rgba(74,222,128,0.4)]",
    },
    Lost: {
      bg: "bg-red-100",
      text: "text-red-800",
      darkModeBg: "dark:bg-red-900/30",
      darkModeText: "dark:text-red-300",
      darkModeGlow: "dark:shadow-[0_0_10px_rgba(248,113,113,0.4)]",
    },
    Canceled: {
      bg: "bg-gray-100",
      text: "text-gray-800",
      darkModeBg: "dark:bg-gray-800/30",
      darkModeText: "dark:text-gray-300",
      darkModeGlow: "dark:shadow-[0_0_10px_rgba(156,163,175,0.3)]",
    },
    Hold: {
      bg: "bg-cyan-100",
      text: "text-cyan-800",
      darkModeBg: "dark:bg-cyan-900/30",
      darkModeText: "dark:text-cyan-300",
      darkModeGlow: "dark:shadow-[0_0_10px_rgba(103,232,249,0.4)]",
    },
  }

  const statusStyle = statusColors[status] || {
    bg: "bg-blue-100",
    text: "text-blue-800",
    darkModeBg: "dark:bg-blue-900/30",
    darkModeText: "dark:text-blue-300",
    darkModeGlow: "dark:shadow-[0_0_10px_rgba(96,165,250,0.4)]",
  }

  // Format dates for display with validation
  const formatDateDisplay = (date: Date | null | undefined) => {
    // Check if date is valid before formatting
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      console.warn("Invalid date provided to formatDateDisplay:", date)
      return "Invalid date"
    }

    try {
      return format(date, "MMM d, yyyy")
    } catch (error) {
      console.error("Error formatting date:", error)
      return "Invalid date"
    }
  }

  const formatTimeDisplay = (date: Date | null | undefined) => {
    // Check if date is valid before formatting
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      console.warn("Invalid time provided to formatTimeDisplay:", date)
      return "Invalid time"
    }

    try {
      return format(date, "h:mm a")
    } catch (error) {
      console.error("Error formatting time:", error)
      return "Invalid time"
    }
  }

  // Check if there's any ghost booking being edited with the same ID
  const isBeingEdited = ghostBookings?.some(
    (ghost) => ghost.id.startsWith("ghost-modal-") && currentEvent.id === ghost.id.replace("ghost-modal-", "")
  )

  // Handle mouse enter - clear any existing timeout
  const handleMouseEnter = () => {
    if (popoverTimeoutRef.current) {
      clearTimeout(popoverTimeoutRef.current)
      popoverTimeoutRef.current = null
    }
    setMouseOverPopover(true)
  }

  // Handle mouse leave - start timeout to close popover
  const handleMouseLeave = () => {
    setMouseOverPopover(false)

    // Start timeout to close popover after 800ms
    popoverTimeoutRef.current = setTimeout(() => {
      closePopover()
    }, 800)
  }

  return createPortal(
    <AnimatePresence>
      {isOpen && eventData && currentEvent && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{
            opacity: 1,
            scale: 1,
            transition: { type: "spring", damping: 20, stiffness: 300 },
          }}
          exit={{ opacity: 0, scale: 0.9 }}
          style={{
            position: "fixed",
            left: `${popoverPosition.left}px`,
            top: `${popoverPosition.top}px`,
            zIndex: 1000,
          }}
          className="max-h-[500px] w-80 overflow-hidden rounded-lg border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-800"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {/* Popover content */}
          <div className="relative">
            {/* Close button */}
            <button
              onClick={closePopover}
              className="absolute right-2 top-2 rounded-full p-1 transition-colors hover:bg-gray-100 dark:hover:bg-gray-700"
              aria-label="Close"
            >
              <X className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            </button>

            {/* Warning banner for ghost events */}
            {(isGhost || isCartGhost) && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                className="flex items-center justify-center bg-blue-500 px-4 py-1 text-xs text-white dark:bg-blue-700"
              >
                {isCartGhost ? "This event is in your booking cart" : "This is a draft booking"}
              </motion.div>
            )}

            <div className="p-4">
              {/* Status badge */}
              {status ? (
                <div className="mb-3">
                  <Badge
                    className={`
                      ${statusStyle.bg} 
                      ${statusStyle.text} 
                      ${statusStyle.darkModeBg} 
                      ${statusStyle.darkModeText} 
                      ${statusStyle.darkModeGlow}
                      event-status-indicator
                      capitalize
                      transition-all
                      duration-300
                      dark:animate-pulse-subtle dark:border-[0.5px]
                      dark:border-opacity-25
                      event-status-${status?.toLowerCase() || "tentative"}
                    `}
                  >
                    {status || "Tentative"}
                    {status === "Hold" && holdRank && <> #{holdRank}</>}
                  </Badge>
                </div>
              ) : (
                <div className="mb-3">
                  <Badge
                    className={`
                      event-status-indicator
                      event-status-tentative
                      bg-amber-100
                      capitalize
                      text-amber-800
                      transition-all
                      duration-300
                      dark:animate-pulse-subtle
                      dark:border-[0.5px]
                      dark:border-opacity-25 dark:bg-amber-900/30
                      dark:text-amber-300
                      dark:shadow-[0_0_10px_rgba(251,191,36,0.4)]
                    `}
                  >
                    Tentative
                  </Badge>
                </div>
              )}

              {/* Event title */}
              <h3 className="mb-1 pr-5 text-lg font-semibold text-gray-900 dark:text-gray-100">{title}</h3>

              {/* Venue and room */}
              {(venue || room) && (
                <div className="mb-2 flex items-start gap-2">
                  <MapPin className="mt-0.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
                  <div>
                    {venue && <p className="text-sm text-gray-600 dark:text-gray-300">{venue.name}</p>}
                    {room && <p className="text-sm text-gray-500 dark:text-gray-400">{room.name}</p>}
                  </div>
                </div>
              )}

              {/* Time details */}
              <div className="mb-3 flex items-start gap-2">
                <Clock className="mt-0.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {isValidStart ? formatDateDisplay(start) : "Invalid date"}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isValidStart ? formatTimeDisplay(start) : "Invalid time"} -{" "}
                    {isValidEnd ? formatTimeDisplay(end) : "Invalid time"}
                  </p>
                </div>
              </div>

              {/* Description */}
              {description && (
                <div className="mt-2 border-t border-gray-200 pt-3 dark:border-gray-700">
                  <p className="line-clamp-5 text-sm text-gray-600 dark:text-gray-300">{description}</p>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>,
    document.body
  )
}

export default EventPopover
