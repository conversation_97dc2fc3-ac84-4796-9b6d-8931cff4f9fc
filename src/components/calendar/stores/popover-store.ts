import { create } from 'zustand';

/**
 * Type definitions for the popover state
 */
type PopoverState = {
  /**
   * Whether the popover is currently open
   */
  isOpen: boolean;

  /**
   * Data for the event being displayed in the popover
   */
  eventData: { event: any; el: HTMLElement } | null;

  /**
   * Whether the mouse is currently over the popover
   */
  isMouseOverPopover: boolean;

  /**
   * Opens the popover with the given event data
   */
  openPopover: (data: { event: any; el: HTMLElement }) => void;

  /**
   * Closes the popover
   */
  closePopover: () => void;

  /**
   * Sets whether the mouse is over the popover
   */
  setMouseOverPopover: (isOver: boolean) => void;
};

/**
 * Zustand store for managing the state of the calendar event popover
 * 
 * Features:
 * - Tracks whether the popover is open and which event it's showing
 * - Manages mouse hover state for better UX with hover intents
 * - Provides methods to open and close the popover
 */
export const useCalendarPopover = create<PopoverState>((set) => ({
  // Initial state
  isOpen: false,
  eventData: null,
  isMouseOverPopover: false,

  // Actions
  openPopover: (data) => set({ isOpen: true, eventData: data }),
  closePopover: () => set({ isOpen: false, eventData: null }),
  setMouseOverPopover: (isOver) => set({ isMouseOverPopover: isOver }),
}));

/**
 * Export a cleanup function for tests and HMR
 */
export const cleanupCalendarPopover = () => {
  const { closePopover } = useCalendarPopover.getState();
  closePopover();
}; 