"use client"

import { useState, useEffect, useCallback } from "react"
import { useCalendar } from "src/context/calendar-context"
import type { ResourceItem } from "src/types/calendar"

/**
 * Custom hook for managing calendar resources
 *
 * Handles:
 * - Converting rooms and venues to FullCalendar resource format
 * - Filtering resources based on selection state
 * - Resource grouping by venue
 *
 * @returns Object containing resources and related state/handlers
 */
export const useResourceManagement = () => {
  const { venues, rooms } = useCalendar()
  const [resources, setResources] = useState<ResourceItem[]>([])

  // Create flat list of room resources with venue information for grouping
  useEffect(() => {
    // Create a flat list of room resources with venue information
    const roomResources: ResourceItem[] = []

    venues
      .filter((venue) => venue.isSelected)
      .forEach((venue) => {
        // Get all rooms for this venue
        const venueRooms = rooms.filter((room) => room.venueId === venue.id && room.isSelected)

        // Add each room as a resource with venue information
        venueRooms.forEach((room) => {
          roomResources.push({
            id: room.id,
            title: room.name,
            venueName: venue.name, // This will be used for grouping
            venueId: venue.id,
            eventColor: room.color,
            businessHours: {
              startTime: "08:00",
              endTime: "20:00",
              daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
            },
          })
        })
      })

    setResources(roomResources)
  }, [venues, rooms])

  /**
   * Finds a room by id
   */
  const findRoom = useCallback(
    (roomId: string) => {
      return rooms.find((r) => r.id === roomId)
    },
    [rooms]
  )

  /**
   * Finds a venue by id
   */
  const findVenue = useCallback(
    (venueId: string) => {
      return venues.find((v) => v.id === venueId)
    },
    [venues]
  )

  /**
   * Gets venue color by venue id
   */
  const getVenueColor = useCallback(
    (venueId: string) => {
      const venue = venues.find((v) => v.id === venueId)
      return venue?.color || "#4285F4"
    },
    [venues]
  )

  /**
   * Gets room color by room id
   */
  const getRoomColor = useCallback(
    (roomId: string) => {
      const room = rooms.find((r) => r.id === roomId)
      return room?.color || "#4285F4"
    },
    [rooms]
  )

  /**
   * Gets rooms for a specific venue
   */
  const getRoomsForVenue = useCallback(
    (venueId: string) => {
      return rooms.filter((r) => r.venueId === venueId && r.isSelected)
    },
    [rooms]
  )

  return {
    resources,
    findRoom,
    findVenue,
    getVenueColor,
    getRoomColor,
    getRoomsForVenue,
  }
}
