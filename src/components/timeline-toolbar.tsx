"use client"
import { <PERSON><PERSON> } from "src/components/ui/button"
import { CalendarDays, ChevronLeft, ChevronRight, ShoppingCart, Filter, SlidersHorizontal, Clock } from "lucide-react"
import { Switch } from "src/components/ui/switch"
import { Label } from "src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "src/components/ui/select"
import { cn } from "src/lib/utils"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "src/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "src/components/ui/dropdown-menu"
import type { TimeSlotInterval } from "src/types/calendar"

export type TimelineDuration = "day" | "threeDays" | "week" | "twoWeeks" | "month"

interface TimelineToolbarProps {
  dateRange: string
  onPrevPeriod: () => void
  onNextPeriod: () => void
  isCartMode: boolean
  onCartModeChange: (value: boolean) => void
  timelineDuration: TimelineDuration
  onTimelineDurationChange: (duration: TimelineDuration) => void
  timeSlotInterval: TimeSlotInterval
  onTimeSlotIntervalChange: (interval: TimeSlotInterval) => void
  className?: string
}

export function TimelineToolbar({
  dateRange,
  onPrevPeriod,
  onNextPeriod,
  isCartMode,
  onCartModeChange,
  timelineDuration,
  onTimelineDurationChange,
  timeSlotInterval,
  onTimeSlotIntervalChange,
  className,
}: TimelineToolbarProps) {
  // Helper function to format time slot interval for display
  const formatTimeSlotInterval = (interval: TimeSlotInterval): string => {
    switch (interval) {
      case "15min":
        return "15 Minutes"
      case "30min":
        return "30 Minutes"
      case "1hour":
        return "1 Hour"
      case "2hours":
        return "2 Hours"
      case "4hours":
        return "4 Hours"
      case "8hours":
        return "8 Hours"
      case "1day":
        return "1 Day"
      default:
        return "1 Hour"
    }
  }

  return (
    <div
      className={cn("mb-4 flex items-center justify-between rounded-lg border bg-background p-2 shadow-sm", className)}
    >
      {/* Left section - Timeline duration */}
      <div className="flex items-center space-x-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="mr-2 flex items-center">
                <CalendarDays className="mr-1 h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Duration:</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Select timeline duration</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <Select value={timelineDuration} onValueChange={(value) => onTimelineDurationChange(value as TimelineDuration)}>
          <SelectTrigger className="h-8 w-[110px]">
            <SelectValue placeholder="Select duration" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="day">Day</SelectItem>
            <SelectItem value="threeDays">3 Days</SelectItem>
            <SelectItem value="week">Week</SelectItem>
            <SelectItem value="twoWeeks">2 Weeks</SelectItem>
            <SelectItem value="month">Month</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Center section - Date navigation */}
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="icon" onClick={onPrevPeriod} className="h-8 w-8 rounded-full">
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <div className="min-w-[180px] text-center text-base font-semibold">{dateRange}</div>
        <Button variant="ghost" size="icon" onClick={onNextPeriod} className="h-8 w-8 rounded-full">
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Right section - Controls */}
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          <Label htmlFor="cart-mode-timeline" className="flex cursor-pointer items-center gap-1 text-sm">
            <ShoppingCart className="h-3.5 w-3.5" />
            <span className="hidden sm:inline">Cart Mode</span>
          </Label>
          <Switch
            id="cart-mode-timeline"
            checked={isCartMode}
            onCheckedChange={onCartModeChange}
            className="data-[state=checked]:bg-primary"
          />
        </div>

        <div className="flex items-center space-x-2">
          {/* Time button - visible on all screen sizes */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <Clock className="h-3.5 w-3.5" />
                <span className="hidden sm:inline">Time</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Time Slot Interval</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuRadioGroup
                value={timeSlotInterval}
                onValueChange={(value) => onTimeSlotIntervalChange(value as TimeSlotInterval)}
              >
                <DropdownMenuRadioItem value="15min">15 Minutes</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="30min">30 Minutes</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="1hour">1 Hour</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="2hours">2 Hours</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="4hours">4 Hours</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="8hours">8 Hours</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="1day">1 Day</DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Current: {formatTimeSlotInterval(timeSlotInterval)}</DropdownMenuLabel>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Filter and Options buttons - only visible on mobile */}
          <div className="flex items-center space-x-2 sm:hidden">
            <Button variant="outline" size="sm" className="h-8 gap-1">
              <Filter className="h-3.5 w-3.5" />
              <span className="hidden sm:inline">Filter</span>
            </Button>

            <Button variant="outline" size="sm" className="h-8 gap-1">
              <SlidersHorizontal className="h-3.5 w-3.5" />
              <span className="hidden sm:inline">Options</span>
            </Button>
          </div>
        </div>

        {/* Mobile menu for additional options */}
        <div className="sm:hidden">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                <SlidersHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Timeline Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                  <Clock className="mr-2 h-4 w-4" />
                  Time Display
                  <DropdownMenu>
                    <DropdownMenuTrigger className="ml-auto">
                      <ChevronRight className="h-4 w-4" />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuLabel>Time Slot Interval</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuRadioGroup
                        value={timeSlotInterval}
                        onValueChange={(value) => onTimeSlotIntervalChange(value as TimeSlotInterval)}
                      >
                        <DropdownMenuRadioItem value="15min">15 Minutes</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="30min">30 Minutes</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="1hour">1 Hour</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="2hours">2 Hours</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="4hours">4 Hours</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="8hours">8 Hours</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="1day">1 Day</DropdownMenuRadioItem>
                      </DropdownMenuRadioGroup>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Filter className="mr-2 h-4 w-4" />
                  Filter Resources
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <SlidersHorizontal className="mr-2 h-4 w-4" />
                  View Options
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  )
}
