export const DAYS_OF_WEEK = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
export const SHORT_DAYS = ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "Thu", "Fri", "Sat"]
export const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
]

export function formatDate(date: Date): string {
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
  })
}

export function formatTime(date: Date): string {
  return date.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  })
}

export function formatDateRange(start: Date, end: Date): string {
  const sameDay =
    start.getDate() === end.getDate() &&
    start.getMonth() === end.getMonth() &&
    start.getFullYear() === end.getFullYear()

  if (sameDay) {
    return `${formatTime(start)} - ${formatTime(end)}`
  }

  return `${formatDate(start)} ${formatTime(start)} - ${formatDate(end)} ${formatTime(end)}`
}

export function getDaysInMonth(year: number, month: number): Date[] {
  const days: Date[] = []
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  // Add days from previous month to fill the first week
  const firstDayOfWeek = firstDay.getDay()
  for (let i = firstDayOfWeek; i > 0; i--) {
    const day = new Date(year, month, 1 - i)
    days.push(day)
  }

  // Add days of current month
  for (let i = 1; i <= lastDay.getDate(); i++) {
    const day = new Date(year, month, i)
    days.push(day)
  }

  // Add days from next month to fill the last week
  const lastDayOfWeek = lastDay.getDay()
  for (let i = 1; i < 7 - lastDayOfWeek; i++) {
    const day = new Date(year, month + 1, i)
    days.push(day)
  }

  return days
}

export function getWeekDays(date: Date): Date[] {
  const days: Date[] = []
  const currentDay = date.getDay()
  const firstDayOfWeek = new Date(date)
  firstDayOfWeek.setDate(date.getDate() - currentDay)

  for (let i = 0; i < 7; i++) {
    const day = new Date(firstDayOfWeek)
    day.setDate(firstDayOfWeek.getDate() + i)
    days.push(day)
  }

  return days
}

export function getTimeSlots(startHour = 0, endHour = 23, interval = 60): Date[] {
  const slots: Date[] = []
  const now = new Date()
  now.setHours(0, 0, 0, 0)

  for (let hour = startHour; hour <= endHour; hour++) {
    for (let minute = 0; minute < 60; minute += interval) {
      const time = new Date(now)
      time.setHours(hour, minute)
      slots.push(time)
    }
  }

  return slots
}

export function isSameDay(date1: Date, date2: Date): boolean {
  return (
    date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
  )
}

export function isToday(date: Date): boolean {
  const today = new Date()
  return isSameDay(date, today)
}

export function addDays(date: Date, days: number): Date {
  const result = new Date(date)
  result.setDate(date.getDate() + days)
  return result
}

export function addMonths(date: Date, months: number): Date {
  const result = new Date(date)
  result.setMonth(date.getMonth() + months)
  return result
}

