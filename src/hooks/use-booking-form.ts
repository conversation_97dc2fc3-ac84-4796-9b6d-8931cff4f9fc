"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { useCalendar } from "src/context/calendar-context"
import type { Booking, GhostBooking } from "src/context/calendar-context"
import { v4 as uuidv4 } from "uuid"

// Helper function to ensure a date is valid
export function ensureValidDate(date: any): Date {
  if (date instanceof Date && !isNaN(date.getTime())) {
    return date
  }

  // Try to parse string dates
  if (typeof date === "string") {
    const parsed = new Date(date)
    if (!isNaN(parsed.getTime())) {
      return parsed
    }
  }

  // Default to current time if invalid
  return new Date()
}

interface UseBookingFormProps {
  booking?: Booking | GhostBooking | null
  startDate?: Date | null
  endDate?: Date | null
  initialRoomId?: string | null
  isOpen: boolean
  onClose: () => void
  createGhostByDefault?: boolean
}

export function useBookingForm({
  booking,
  startDate,
  endDate,
  initialRoomId,
  isOpen,
  onClose,
  createGhostByDefault = true,
}: UseBookingFormProps) {
  const {
    venues,
    rooms,
    addBooking,
    updateBooking,
    addGhostBooking,
    updateGhostBooking,
    deleteGhostBooking,
    convertGhostBookingToReal,
  } = useCalendar()

  // Ensure valid dates
  const validStartDate = startDate instanceof Date && !isNaN(startDate.getTime()) ? startDate : new Date()
  const validEndDate =
    endDate instanceof Date && !isNaN(endDate.getTime()) ? endDate : new Date(validStartDate.getTime() + 3600000)

  // State for form data
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [venueId, setVenueId] = useState(venues[0]?.id || "")
  const [roomId, setRoomId] = useState(initialRoomId || "")
  const [start, setStart] = useState(validStartDate)
  const [end, setEnd] = useState(validEndDate)
  const [isAllDay, setIsAllDay] = useState(false)
  const [createGhost, setCreateGhost] = useState(createGhostByDefault)

  // Track if this is editing a ghost booking
  const [isEditingGhost, setIsEditingGhost] = useState(false)
  const [ghostId, setGhostId] = useState<string | null>(null)

  // Get rooms for the selected venue
  const venueRooms = rooms.filter((room) => room.venueId === venueId && room.isSelected)

  // Refs for tracking state
  const ghostCreatedRef = useRef(false)
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const initializedRef = useRef(false)
  const bookingRef = useRef<Booking | GhostBooking | null>(null)

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      // Reset initialization flag when modal closes
      initializedRef.current = false
      ghostCreatedRef.current = false
      bookingRef.current = null
    }
  }, [isOpen])

  // Initialize form when modal opens or booking changes
  useEffect(() => {
    if (!isOpen) return

    // Skip re-initialization if we're already editing the same booking
    if (booking && bookingRef.current && booking.id === bookingRef.current.id) {
      return
    }

    // Update the booking ref
    bookingRef.current = booking

    // Reset initialization flag
    initializedRef.current = false

    if (booking) {
      // Check if this is a ghost booking
      const ghostBooking = booking as GhostBooking
      const isGhost = !!ghostBooking.status
      setIsEditingGhost(isGhost)
      setGhostId(isGhost ? booking.id : null)

      // Ensure valid dates
      const validStart = ensureValidDate(booking.start)
      const validEnd = ensureValidDate(booking.end)

      // Set form data
      setTitle(booking.title)
      setDescription(booking.description || "")
      setVenueId(booking.venueId)

      // Ensure we have a valid roomId
      if (booking.roomId) {
        setRoomId(booking.roomId)
      } else {
        // If no roomId, try to find a room in the venue
        const venueRooms = rooms.filter((room) => room.venueId === booking.venueId && room.isSelected)
        if (venueRooms.length > 0) {
          setRoomId(venueRooms[0].id)
        } else {
          // Try to find any selected room
          const anyRoom = rooms.find((r) => r.isSelected)
          if (anyRoom) {
            setRoomId(anyRoom.id)
            setVenueId(anyRoom.venueId)
          }
        }
      }

      setStart(validStart)
      setEnd(validEnd)
      setIsAllDay(booking.isAllDay || false)
      setCreateGhost(createGhostByDefault)

      // Mark as initialized
      setTimeout(() => {
        initializedRef.current = true
      }, 100)
    } else if (startDate && endDate) {
      // For new bookings, set initial values
      const initialVenueId = initialRoomId
        ? rooms.find((r) => r.id === initialRoomId)?.venueId || venues[0]?.id || ""
        : venues[0]?.id || ""

      setTitle("")
      setDescription("")
      setVenueId(initialVenueId)

      // Ensure we have a valid roomId
      if (initialRoomId) {
        setRoomId(initialRoomId)
      } else {
        // If no initialRoomId, try to find a room in the venue
        const venueRooms = rooms.filter((room) => room.venueId === initialVenueId && room.isSelected)
        if (venueRooms.length > 0) {
          setRoomId(venueRooms[0].id)
        } else {
          // Try to find any selected room
          const anyRoom = rooms.find((r) => r.isSelected)
          if (anyRoom) {
            setRoomId(anyRoom.id)
            setVenueId(anyRoom.venueId)
          }
        }
      }

      setStart(validStartDate)
      setEnd(validEndDate)
      setIsAllDay(false)
      setCreateGhost(createGhostByDefault)

      setIsEditingGhost(false)
      setGhostId(null)

      // Mark as initialized
      setTimeout(() => {
        initializedRef.current = true
      }, 100)
    }
  }, [
    booking,
    startDate,
    endDate,
    initialRoomId,
    venues,
    rooms,
    validStartDate,
    validEndDate,
    isOpen,
    createGhostByDefault,
  ])

  // Create a ghost booking when the modal opens for a new booking
  useEffect(() => {
    if (!isOpen || !initializedRef.current || ghostCreatedRef.current) return

    // Only create ghost booking if:
    // 1. We have start and end dates
    // 2. We're not editing an existing booking
    // 3. Ghost creation is enabled
    // 4. We have a valid roomId
    if (startDate && endDate && !booking && createGhost && roomId) {
      const newGhostId = `ghost-modal-${uuidv4()}`
      setGhostId(newGhostId)

      // Get room information if available
      const room = rooms.find((r) => r.id === roomId)
      const initialVenueId = room?.venueId || venueId

      const ghostBooking: GhostBooking = {
        id: newGhostId,
        title: title || (room ? `New Booking for ${room.name}` : "New Booking"),
        start: validStartDate,
        end: validEndDate,
        venueId: initialVenueId,
        roomId: roomId,
        description: description || "",
        isAllDay: isAllDay,
        status: "Tentative",
      }

      addGhostBooking(ghostBooking)
      setIsEditingGhost(true)
      ghostCreatedRef.current = true
    }
  }, [
    isOpen,
    startDate,
    endDate,
    booking,
    createGhost,
    roomId,
    rooms,
    addGhostBooking,
    venueId,
    validStartDate,
    validEndDate,
    initializedRef,
    title,
    description,
    isAllDay,
  ])

  // Update ghost booking when form data changes
  useEffect(() => {
    // Skip if modal is closed, not initialized, or ghost updates are disabled
    if (!isOpen || !initializedRef.current || window.disableGhostUpdates) return

    // Clear any existing timeout
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current)
    }

    // Only update if we're editing a ghost and ghost creation is enabled
    // AND we have a valid roomId
    if ((isEditingGhost && ghostId && createGhost && roomId) || (createGhost && roomId && ghostId)) {
      // Use a debounced timeout to batch updates
      updateTimeoutRef.current = setTimeout(() => {
        // Double-check that we still have a valid roomId before updating
        if (!roomId) {
          console.warn("Skipping ghost update due to missing roomId")
          return
        }

        const room = rooms.find((r) => r.id === roomId)
        const titleToUse = title || (room ? `New Booking for ${room.name}` : "New Booking")

        const updatedGhostBooking: GhostBooking = {
          id: ghostId,
          title: titleToUse,
          start,
          end,
          venueId,
          roomId,
          description,
          isAllDay,
          status: "Tentative",
        }

        updateGhostBooking(updatedGhostBooking)
      }, 300) // Reduced debounce time for more responsive updates
    }

    // Cleanup function
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current)
      }
    }
  }, [
    title,
    description,
    venueId,
    roomId,
    start,
    end,
    isAllDay,
    createGhost,
    isEditingGhost,
    ghostId,
    updateGhostBooking,
    rooms,
    isOpen,
    initializedRef,
  ])

  // Clean up modal's ghost booking when component unmounts
  useEffect(() => {
    return () => {
      // Clean up any pending timeouts
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current)
      }

      // Clean up ghost bookings when component unmounts
      // Only delete ghost events created by this modal (not cart-related ones)
      if (ghostId && ghostId.startsWith("ghost-modal-")) {
        deleteGhostBooking(ghostId)
      }
    }
  }, [ghostId, deleteGhostBooking])

  // Handle venue change
  const handleVenueChange = (value: string) => {
    // When venue changes, set the first room of that venue as selected
    const newVenueRooms = rooms.filter((room) => room.venueId === value && room.isSelected)
    const firstRoomId = newVenueRooms.length > 0 ? newVenueRooms[0].id : ""

    setVenueId(value)
    setRoomId(firstRoomId)
  }

  // Handle ghost toggle
  const handleGhostToggle = (checked: boolean) => {
    setCreateGhost(checked)

    // If turning off ghost events, clear them
    if (!checked && ghostId) {
      deleteGhostBooking(ghostId)
      setIsEditingGhost(false)
      setGhostId(null)
      ghostCreatedRef.current = false
    } else if (checked && !ghostId && roomId) {
      // If turning on ghost events, create a new one
      const newGhostId = `ghost-modal-${uuidv4()}`
      setGhostId(newGhostId)

      const ghostBooking: GhostBooking = {
        id: newGhostId,
        title: title || "New Booking",
        start,
        end,
        venueId,
        roomId,
        description,
        isAllDay,
        status: "Tentative",
      }

      addGhostBooking(ghostBooking)
      setIsEditingGhost(true)
      ghostCreatedRef.current = true
    }
  }

  // Handle date change
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const date = new Date(value)

    if (name === "startDate") {
      const newStart = new Date(start)
      newStart.setFullYear(date.getFullYear(), date.getMonth(), date.getDate())
      setStart(newStart)
    } else if (name === "endDate") {
      const newEnd = new Date(end)
      newEnd.setFullYear(date.getFullYear(), date.getMonth(), date.getDate())
      setEnd(newEnd)
    }
  }

  // Handle time change
  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const [hours, minutes] = value.split(":").map(Number)

    if (name === "startTime") {
      const newStart = new Date(start)
      newStart.setHours(hours, minutes)
      setStart(newStart)
    } else if (name === "endTime") {
      const newEnd = new Date(end)
      newEnd.setHours(hours, minutes)
      setEnd(newEnd)
    }
  }

  // Validate form
  const validateForm = () => {
    if (!title || !venueId || !roomId) {
      alert("Please fill in all required fields")
      return false
    }

    if (end <= start) {
      alert("End time must be after start time")
      return false
    }

    return true
  }

  // Handle form submission for creating/updating a booking
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    const bookingData: Booking = {
      id: booking?.id || `booking-${Date.now()}`,
      title,
      description,
      venueId,
      roomId,
      start,
      end,
      isAllDay,
      status: "Tentative",
    }

    if (isEditingGhost && ghostId) {
      // If editing a modal ghost booking, convert it to a real booking
      // First update the ghost booking to ensure it has the latest data
      const updatedGhostBooking: GhostBooking = {
        ...bookingData,
        id: ghostId,
        status: "Tentative",
      }
      updateGhostBooking(updatedGhostBooking)

      // Then convert it to a real booking
      convertGhostBookingToReal(ghostId)
    } else if (booking) {
      // If editing a real booking, maintain its status
      const updatedBooking = {
        ...bookingData,
        status: (booking as Booking).status || "Tentative",
      }
      updateBooking(updatedBooking)
    } else {
      // If creating a new booking, add it
      addBooking(bookingData)
    }

    // Delete the ghost event
    if (ghostId) {
      deleteGhostBooking(ghostId)
    }

    onClose()
  }

  // Handle cancel
  const handleCancel = () => {
    // Clean up any pending timeouts
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current)
    }

    // Only delete the ghost event if it's not cart-related
    if (ghostId && ghostId.startsWith("ghost-modal-")) {
      deleteGhostBooking(ghostId)
    }

    onClose()
  }

  return {
    // Form state
    title,
    setTitle,
    description,
    setDescription,
    venueId,
    setVenueId,
    roomId,
    setRoomId,
    start,
    setStart,
    end,
    setEnd,
    isAllDay,
    setIsAllDay,
    createGhost,
    setCreateGhost,

    // Derived state
    venueRooms,
    venues: venues.filter((v) => v.isSelected),

    // Ghost booking state
    isEditingGhost,
    ghostId,

    // Handlers
    handleVenueChange,
    handleGhostToggle,
    handleDateChange,
    handleTimeChange,
    handleSubmit,
    handleCancel,
    validateForm,
  }
}
