"use client"

import { useEffect, useRef, useState } from "react"
import { useBooking<PERSON>art } from "src/store/booking-cart"
import { useCalendar } from "src/context/calendar-context"
import type { GhostBooking } from "src/context/calendar-context"

// Declare the global flag
declare global {
  interface Window {
    disableGhostUpdates?: boolean
  }
}

/**
 * Hook to synchronize cart items with ghost bookings
 * This ensures cart items are visually represented on the calendar
 * even after page refresh
 */
export function useCartGhostSync() {
  const { items } = useBookingCart()
  const { ghostBookings, addGhostBooking, deleteGhostBooking } = useCalendar()
  const [initialSyncComplete, setInitialSyncComplete] = useState(false)
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Skip if ghost updates are disabled
    if (window.disableGhostUpdates) return

    // Don't run this effect again if we've already done the initial sync
    if (initialSyncComplete) return

    // Check if we have any cart items to sync
    if (items.length === 0) {
      setInitialSyncComplete(true)
      return
    }

    console.log("Performing initial ghost sync for", items.length, "cart items")

    // Clear any existing timeout
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current)
    }

    // Use a timeout to ensure the calendar is fully initialized
    syncTimeoutRef.current = setTimeout(() => {
      // Get existing cart ghost IDs
      const existingCartGhostIds = ghostBookings
        .filter((ghost) => ghost.id.startsWith("ghost-cart-"))
        .map((ghost) => ghost.id)

      // Get current cart item IDs
      const currentCartItemIds = items.map((item) => `ghost-cart-${item.id}`)

      // Find ghost bookings to remove (exist in ghostBookings but not in items)
      const ghostsToRemove = existingCartGhostIds.filter((ghostId) => !currentCartItemIds.includes(ghostId))

      // Remove ghost bookings that don't have corresponding cart items
      ghostsToRemove.forEach((ghostId) => {
        deleteGhostBooking(ghostId)
      })

      // Add or update ghost bookings for each cart item
      items.forEach((item) => {
        const ghostId = `ghost-cart-${item.id}`
        const existingGhost = ghostBookings.find((ghost) => ghost.id === ghostId)

        // Ensure we have valid date objects
        const validStart =
          item.booking.start instanceof Date && !isNaN(item.booking.start.getTime())
            ? item.booking.start
            : typeof item.booking.start === "string" && item.booking.start
              ? new Date(item.booking.start) // Parse ISO string dates
              : new Date() // Only fallback to current time if no valid date exists

        const validEnd =
          item.booking.end instanceof Date && !isNaN(item.booking.end.getTime())
            ? item.booking.end
            : typeof item.booking.end === "string" && item.booking.end
              ? new Date(item.booking.end) // Parse ISO string dates
              : new Date(validStart.getTime() + 3600000) // Default to 1 hour after start if no valid date

        // Add or update the ghost booking
        const ghostBooking: GhostBooking = {
          id: ghostId,
          title: `Cart: ${item.booking.title || `New Booking for ${item.room.name}`}`,
          start: validStart,
          end: validEnd,
          venueId: item.booking.venueId,
          roomId: item.booking.roomId,
          description: item.booking.description || "",
          isAllDay: item.booking.isAllDay || false,
          status: "pending",
        }

        // Always add the ghost booking on initial sync to ensure it appears
        addGhostBooking(ghostBooking)
      })

      // Mark initial sync as complete
      setInitialSyncComplete(true)
    }, 500) // Wait for 500ms after component mount
  }, [items, ghostBookings, addGhostBooking, deleteGhostBooking, initialSyncComplete])

  // Ongoing sync effect - runs when items or ghost bookings change
  useEffect(() => {
    // Skip if ghost updates are disabled
    if (window.disableGhostUpdates) return

    // Skip if initial sync hasn't completed yet
    if (!initialSyncComplete) return

    // Clear any existing timeout
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current)
    }

    // Use a debounced timeout to batch updates
    syncTimeoutRef.current = setTimeout(() => {
      // Sync logic for ongoing changes (same as before)
      // syncCartWithGhosts()
    }, 300) // Debounce for 300ms

    // Function to sync cart items with ghost bookings
    function syncCartWithGhosts() {
      // Get existing cart ghost IDs
      const existingCartGhostIds = ghostBookings
        .filter((ghost) => ghost.id.startsWith("ghost-cart-"))
        .map((ghost) => ghost.id)

      // Get current cart item IDs
      const currentCartItemIds = items.map((item) => `ghost-cart-${item.id}`)

      // Find ghost bookings to remove (exist in ghostBookings but not in items)
      const ghostsToRemove = existingCartGhostIds.filter((ghostId) => !currentCartItemIds.includes(ghostId))

      // Remove ghost bookings that don't have corresponding cart items
      ghostsToRemove.forEach((ghostId) => {
        deleteGhostBooking(ghostId)
      })

      // Add or update ghost bookings for each cart item
      items.forEach((item) => {
        const ghostId = `ghost-cart-${item.id}`
        const existingGhost = ghostBookings.find((ghost) => ghost.id === ghostId)

        // Ensure we have valid date objects
        const validStart =
          item.booking.start instanceof Date && !isNaN(item.booking.start.getTime()) ? item.booking.start : new Date()

        const validEnd =
          item.booking.end instanceof Date && !isNaN(item.booking.end.getTime())
            ? item.booking.end
            : new Date(validStart.getTime() + 3600000) // Default to 1 hour after start

        // Only add if the ghost doesn't exist
        if (!existingGhost) {
          const ghostBooking: GhostBooking = {
            id: ghostId,
            title: `Cart: ${item.booking.title || `New Booking for ${item.room.name}`}`,
            start: validStart,
            end: validEnd,
            venueId: item.booking.venueId,
            roomId: item.booking.roomId,
            description: item.booking.description || "",
            isAllDay: item.booking.isAllDay || false,
            status: "pending",
          }

          addGhostBooking(ghostBooking)
        }
      })
    }
  }, [items, ghostBookings, addGhostBooking, deleteGhostBooking, initialSyncComplete])

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current)
      }
    }
  }, [])
}
