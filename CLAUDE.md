# NetSuite Booking Calendar Integration Guide for Cursor

## Overview Prompt for <PERSON>urs<PERSON>

```
I'm building a Next.js booking calendar application that integrates with NetSuite as the backend. The app uses FullCalendar.io for the UI and needs to support drag-and-drop booking creation, editing, resizing, and deletion. 

The NetSuite data model consists of:
- CS Event: Parent record that can have multiple bookings
- CS Booking: Individual booking with time, duration, status, and details
- CS Space: Bookable space/room within a venue
- CS Venue: Parent location containing multiple spaces

The app uses NextAuth.js with OAuth 2.0 for authentication. All API routes should be in the app/api folder as route.ts files, calling corresponding NetSuite RESTlets.

Key requirements:
- Support bulk booking creation from cart checkout
- Cache date range queries for 15 minutes using N/cache
- Return data optimized for FullCalendar.io format
- Handle timezone data from frontend without conversion
- Support special "Hold" status with queue position management (max 10 per space)
```

## SuiteScript RESTlet Endpoints

### 1. **Bookings RESTlet** (`customscript_cs_booking_restlet.js`)

```javascript
/**
 * @NApiVersion 2.1
 * @NScriptType Restlet
 * @NModuleScope SameAccount
 */

/**
 * RESTlet for CS Booking operations
 * Handles: GET (list/search), POST (create/bulk create), PUT (update), DELETE
 * 
 * GET Parameters:
 * - startDate: ISO datetime string
 * - endDate: ISO datetime string  
 * - venueIds: comma-separated venue IDs (optional)
 * - spaceIds: comma-separated space IDs (optional)
 * - eventId: specific event ID (optional)
 * - status: booking status filter (optional)
 * 
 * POST Body:
 * - Single booking: { title, eventId, spaceId, startDate, endDate, description, status, timezone }
 * - Bulk bookings: { bookings: [...] }
 * 
 * PUT Body:
 * - { id, title?, spaceId?, startDate?, endDate?, description?, status?, timezone? }
 * 
 * DELETE Parameters:
 * - id: booking ID to delete
 */

// Cache configuration: 15 minutes for date range queries
// Use cache key pattern: 'bookings_{startDate}_{endDate}_{venueIds}_{spaceIds}'

// Transform NetSuite booking records to FullCalendar format:
// {
//   id: bookingId,
//   title: bookingTitle,
//   start: startDateISO,
//   end: endDateISO,
//   resourceId: spaceId,
//   extendedProps: {
//     eventId, venueId, description, status, holdRank
//   }
// }

// Special handling for "Hold" status:
// - Query custom hold queue record for the space
// - Assign next available position (1-10)
// - Include holdRank in response
```

### 2. **Resources RESTlet** (`customscript_cs_resources_restlet.js`)

```javascript
/**
 * @NApiVersion 2.1
 * @NScriptType Restlet
 * @NModuleScope SameAccount
 */

/**
 * RESTlet for CS Venue and CS Space resources
 * Returns hierarchical venue/space data for FullCalendar resources
 * 
 * GET Parameters:
 * - includeHolds: boolean to include hold queue information
 * 
 * Response format optimized for FullCalendar:
 * [
 *   {
 *     id: spaceId,
 *     title: spaceName,
 *     venueId: parentVenueId,
 *     venueName: venueName,
 *     eventColor: spaceColor,
 *     extendedProps: {
 *       currentHolds: number,
 *       maxHolds: 10
 *     }
 *   }
 * ]
 */
```

### 3. **Events RESTlet** (`customscript_cs_events_restlet.js`)

```javascript
/**
 * @NApiVersion 2.1
 * @NScriptType Restlet
 * @NModuleScope SameAccount
 */

/**
 * RESTlet for CS Event operations
 * Handles event-level operations and metadata
 * 
 * GET Parameters:
 * - id: specific event ID
 * - startDate: filter events by date range
 * - endDate: filter events by date range
 */
```

## Next.js API Routes

### 1. **Bookings Route** (`app/api/bookings/route.ts`)

```typescript
// GET /api/bookings
// Fetch bookings for date range with caching
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  
  // Extract parameters
  const startDate = searchParams.get('start');
  const endDate = searchParams.get('end');
  const venueIds = searchParams.get('venueIds');
  const spaceIds = searchParams.get('resourceIds'); // FullCalendar uses resourceIds
  
  // Call NetSuite RESTlet with date range
  // Transform response to FullCalendar event format
  // Return with appropriate cache headers
}

// POST /api/bookings
// Create single or bulk bookings
export async function POST(request: Request) {
  const body = await request.json();
  
  // Handle cart checkout (bulk creation)
  if (body.bookings && Array.isArray(body.bookings)) {
    // Validate all bookings
    // Call NetSuite RESTlet for bulk creation
    // Return created bookings with IDs
  } else {
    // Single booking creation
    // Validate booking data
    // Call NetSuite RESTlet
    // Return created booking
  }
}
```

### 2. **Individual Booking Route** (`app/api/bookings/[id]/route.ts`)

```typescript
// PUT /api/bookings/[id]
// Update booking (drag, resize, edit)
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  const body = await request.json();
  
  // Validate update data
  // Handle special cases (status changes, hold queue)
  // Call NetSuite RESTlet
  // Return updated booking
}

// DELETE /api/bookings/[id]
// Delete booking with confirmation
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  // Call NetSuite RESTlet to delete
  // Return success/failure
}
```

### 3. **Resources Route** (`app/api/resources/route.ts`)

```typescript
// GET /api/resources
// Fetch venues and spaces for FullCalendar
export async function GET(request: Request) {
  // Call NetSuite RESTlet
  // Transform to FullCalendar resource format
  // Include hold queue information
}
```

## Data Validation Rules

```typescript
// Booking validation schema
interface BookingValidation {
  title: string; // Required, max 200 chars
  eventId: string; // Required, valid CS Event ID
  spaceId: string; // Required, valid CS Space ID
  startDate: string; // ISO datetime with timezone
  endDate: string; // ISO datetime with timezone, must be after startDate
  description?: string; // Optional, max 4000 chars
  status: 'Inquiry' | 'Prospect' | 'Tentative' | 'Definite' | 'Lost' | 'Canceled' | 'Hold';
  timezone: string; // IANA timezone (e.g., 'America/Los_Angeles')
}

// Validation rules:
// 1. End date must be after start date
// 2. If status is 'Hold', check space hold queue isn't full (max 10)
// 3. Booking duration must be at least 15 minutes
// 4. Maximum booking duration is 30 days
```

## Error Handling Patterns

```typescript
// Standard error response format
interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

// Error codes:
// - VALIDATION_ERROR: Invalid input data
// - NETSUITE_ERROR: NetSuite RESTlet error
// - CONFLICT_ERROR: Booking conflict (frontend can override)
// - HOLD_QUEUE_FULL: Space has maximum holds
// - AUTH_ERROR: Authentication/authorization failure
// - RATE_LIMIT: Too many requests
```

## Implementation Guidelines for Cursor

1. **NetSuite RESTlet Structure:**
   - Use N/cache module for 15-minute caching on date range queries
   - Include proper error handling with try/catch blocks
   - Use N/search for efficient date range queries
   - Transform all dates to ISO format for consistency

2. **Next.js API Routes:**
   - Use NextAuth.js session validation on all routes
   - Implement request validation using Zod or similar
   - Add appropriate CORS headers for FullCalendar
   - Use React Query's expected response format

3. **Performance Optimizations:**
   - Cache key pattern: `bookings_${startDate}_${endDate}_${venueIds}_${spaceIds}`
   - Return only necessary fields for FullCalendar
   - Use NetSuite saved searches for complex queries
   - Implement request debouncing on frontend

4. **Special Considerations:**
   - Hold queue management: Atomic operations to prevent race conditions
   - Timezone handling: Store as-is from frontend, no conversion
   - Bulk operations: Use N/transaction for consistency
   - Status styling: Include status in response for frontend styling

5. **Testing Checklist:**
   - Date range edge cases (crossing months/years)
   - Hold queue limits (attempting 11th hold)
   - Bulk creation with partial failures
   - Concurrent edit scenarios
   - Cache invalidation on updates

Would you like me to elaborate on any specific aspect or create more detailed code examples for particular scenarios?